# Subscription System Documentation

This document outlines the subscription system implementation for the XCerberus platform, including tier management, dashboard access, and upgrade flows.

## Subscription Tiers

The platform offers three subscription tiers:

1. **Free**
   - Limited access to challenges and labs
   - Basic dashboard features
   - Community support

2. **Premium**
   - Unlimited challenges and labs
   - Advanced dashboard with enhanced features
   - Attack Box and Private VPN access
   - Priority support

3. **Business**
   - All Premium features
   - Team management capabilities
   - Custom labs creation
   - Dedicated support
   - Unlimited team members

## Database Schema

The subscription system uses the following tables:

### `subscription_plans`
Stores the available subscription plans and their features.

```sql
CREATE TABLE subscription_plans (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  price decimal(10,2) NOT NULL,
  interval text NOT NULL,
  features jsonb NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

### `user_subscriptions`
Tracks user subscription status and details.

```sql
CREATE TABLE user_subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id uuid REFERENCES subscription_plans(id),
  status text NOT NULL DEFAULT 'inactive',
  current_period_start timestamptz,
  current_period_end timestamptz,
  cancel_at_period_end boolean DEFAULT false,
  payment_method_id text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id)
);
```

### `subscription_changes`
Tracks subscription upgrade/downgrade history.

```sql
CREATE TABLE subscription_changes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  from_plan text NOT NULL,
  to_plan text NOT NULL,
  change_time timestamptz NOT NULL DEFAULT now(),
  created_at timestamptz DEFAULT now()
);
```

### `subscription_usage`
Tracks feature usage per user.

```sql
CREATE TABLE subscription_usage (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  feature text NOT NULL,
  usage_count integer DEFAULT 0,
  last_used_at timestamptz DEFAULT now(),
  reset_at timestamptz,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, feature)
);
```

## Dashboard Access Control

The application controls dashboard access based on the user's subscription level:

1. Free users can only access the BasicDashboard
2. Premium users can access the PremiumDashboard
3. Business users can access the BusinessDashboard

This is implemented in the `Dashboard.jsx` component:

```jsx
// Render appropriate dashboard based on subscription level
if (subscriptionLevel === 'Business') {
  return (
    <BusinessDashboard
      {...dashboardData}
      recommendations={recommendations}
    />
  );
} else if (subscriptionLevel === 'Premium') {
  return (
    <PremiumDashboard
      {...dashboardData}
      recommendations={recommendations}
    />
  );
} else {
  return (
    <BasicDashboard
      {...dashboardData}
      recommendations={recommendations}
    />
  );
}
```

## Upgrade Flow

The upgrade flow is implemented using the following components:

1. **UpgradeButton.jsx**: A reusable button component that triggers the upgrade modal
2. **UpgradeModal.jsx**: A modal that displays plan details and handles the upgrade process
3. **subscriptionUpgrade.js**: A service that manages the upgrade process and tracks changes

### Upgrade Process

1. User clicks on an upgrade button in their dashboard
2. The upgrade modal appears showing the benefits of the new plan
3. User confirms the upgrade
4. The system calls the `upgradeSubscription` function
5. The database updates the user's subscription
6. The change is tracked in the `subscription_changes` table
7. The user is redirected to their new dashboard

## Feature Access Control

Feature access is controlled through the `SubscriptionContext` which provides:

1. `hasFeature(featureName)`: Checks if the user has access to a specific feature
2. `getFeatureLimit(featureName)`: Gets the usage limit for a specific feature
3. `isFree`, `isPremium`, `isBusiness`: Convenience flags for subscription level checks

## Implementation Components

### UI Components

- **BasicDashboard.jsx**: Dashboard for Free tier users
- **PremiumDashboard.jsx**: Dashboard for Premium tier users
- **BusinessDashboard.jsx**: Dashboard for Business tier users
- **UpgradeButton.jsx**: Button to trigger upgrade flow
- **UpgradeModal.jsx**: Modal for upgrading subscription
- **SubscriptionHistory.jsx**: Component to display subscription change history

### Services

- **subscription.js**: Core subscription management functions
- **subscriptionUpgrade.js**: Functions for handling upgrades and tracking changes

### Context

- **SubscriptionContext.jsx**: React context for subscription state and feature access

## Usage Examples

### Checking Feature Access

```jsx
import { useSubscription } from '../contexts/SubscriptionContext';

function MyComponent() {
  const { hasFeature, getFeatureLimit } = useSubscription();
  
  // Check if user has access to a feature
  if (hasFeature('attackBox')) {
    // Show attack box feature
  }
  
  // Get usage limit for a feature
  const labLimit = getFeatureLimit('maxLabs');
  
  return (
    <div>
      {hasFeature('privateVPN') ? (
        <VPNAccess />
      ) : (
        <UpgradePrompt feature="privateVPN" />
      )}
    </div>
  );
}
```

### Adding Upgrade Button

```jsx
import UpgradeButton from '../components/subscription/UpgradeButton';

function MyComponent() {
  return (
    <div>
      <h2>Feature Locked</h2>
      <p>Upgrade to access this premium feature</p>
      
      <UpgradeButton 
        targetPlan="Premium"
        variant="primary"
        size="md"
      >
        Unlock Feature
      </UpgradeButton>
    </div>
  );
}
```

### Displaying Subscription History

```jsx
import SubscriptionHistory from '../components/subscription/SubscriptionHistory';

function AccountPage() {
  return (
    <div>
      <h1>Account Settings</h1>
      
      <div className="mt-8">
        <SubscriptionHistory />
      </div>
    </div>
  );
}
```

## Maintenance and Troubleshooting

### Common Issues

1. **Dashboard not updating after upgrade**
   - The page should automatically reload after upgrade
   - If not, check browser console for errors
   - Verify that the subscription was updated in the database

2. **Feature access not reflecting subscription level**
   - Check that the SubscriptionContext is properly updated
   - Verify that the feature is defined in SUBSCRIPTION_FEATURES
   - Check for any caching issues

3. **Upgrade process failing**
   - Check database logs for errors
   - Verify that the handle_subscription_upgrade function exists
   - Check that the user has proper permissions

### Monitoring

Monitor subscription changes and usage patterns through:

1. The `subscription_changes` table for upgrade/downgrade history
2. The `subscription_usage` table for feature usage
3. The `user_activity` table for a timeline of subscription-related actions
