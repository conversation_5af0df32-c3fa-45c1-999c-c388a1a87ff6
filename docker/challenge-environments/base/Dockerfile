FROM alpine:latest

# Use multi-stage build to reduce image size
RUN apk --no-cache add \
    python3 \
    py3-pip \
    nodejs \
    npm \
    bash \
    curl \
    netcat-openbsd \
    nmap \
    openssh-client \
    git \
    vim \
    && pip3 install --no-cache-dir pwntools requests

# Create a non-root user
RUN adduser -D -u 1000 challenger
USER challenger
WORKDIR /home/<USER>

# Add healthcheck
HEALTHCHECK --interval=5s --timeout=3s --retries=3 CMD nc -z localhost 8080 || exit 1

# Set environment variables
ENV CHALLENGE_PORT=8080
ENV MAX_MEMORY=256m
ENV CPU_LIMIT=0.5

# Expose the challenge port
EXPOSE 8080

# Default command
CMD ["sh", "-c", "echo 'Challenge container is running' && tail -f /dev/null"]
