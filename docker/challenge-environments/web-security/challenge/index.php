<?php
// Sample vulnerable web application for XCerberus challenges
session_start();

// Initialize database connection
$db = new SQLite3('/tmp/challenge.db');

// Create tables if they don't exist
$db->exec('
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    is_admin INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS posts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
');

// Insert sample data if not exists
$result = $db->query('SELECT COUNT(*) as count FROM users');
$row = $result->fetchArray();
if ($row['count'] == 0) {
    // Insert admin user with flag
    $flag = getenv('CHALLENGE_FLAG') ?: 'XCerberus{sample_web_security_flag}';
    $db->exec("INSERT INTO users (username, password, is_admin) VALUES ('admin', '".password_hash('super_secret_admin_password_'.$flag, PASSWORD_DEFAULT)."', 1)");
    
    // Insert regular user
    $db->exec("INSERT INTO users (username, password, is_admin) VALUES ('user', '".password_hash('user123', PASSWORD_DEFAULT)."', 0)");
    
    // Insert sample posts
    $db->exec("INSERT INTO posts (user_id, title, content) VALUES (1, 'Welcome to XCerberus', 'This is a sample vulnerable web application for practicing web security skills.')");
    $db->exec("INSERT INTO posts (user_id, title, content) VALUES (2, 'My First Post', 'Hello everyone! I am new here.')");
}

// Handle login
if (isset($_POST['login'])) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    // Vulnerable SQL injection
    $query = "SELECT * FROM users WHERE username = '$username'";
    $result = $db->query($query);
    $user = $result->fetchArray();
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['is_admin'] = $user['is_admin'];
    } else {
        $error = "Invalid username or password";
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Handle post creation
if (isset($_POST['create_post'])) {
    if (!isset($_SESSION['user_id'])) {
        $error = "You must be logged in to create a post";
    } else {
        $title = $_POST['title'];
        $content = $_POST['content'];
        $user_id = $_SESSION['user_id'];
        
        $db->exec("INSERT INTO posts (user_id, title, content) VALUES ($user_id, '$title', '$content')");
        $success = "Post created successfully";
    }
}

// Get all posts
$posts = [];
$result = $db->query('SELECT p.*, u.username FROM posts p JOIN users u ON p.user_id = u.id ORDER BY p.created_at DESC');
while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
    $posts[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XCerberus Web Security Challenge</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        h1, h2, h3 {
            color: #333;
        }
        .login-form, .post-form {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .post {
            margin-bottom: 20px;
            padding: 15px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .post-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .post-meta {
            color: #777;
            font-size: 0.9em;
        }
        .error {
            color: red;
            margin-bottom: 10px;
        }
        .success {
            color: green;
            margin-bottom: 10px;
        }
        .admin-panel {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border: 1px solid #b0c4de;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>XCerberus Web Security Challenge</h1>
            <?php if (isset($_SESSION['user_id'])): ?>
                <div>
                    Welcome, <?php echo htmlspecialchars($_SESSION['username']); ?>
                    <?php if ($_SESSION['is_admin']): ?>
                        (Admin)
                    <?php endif; ?>
                    | <a href="?logout=1">Logout</a>
                </div>
            <?php endif; ?>
        </header>
        
        <?php if (isset($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (isset($success)): ?>
            <div class="success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (!isset($_SESSION['user_id'])): ?>
            <div class="login-form">
                <h2>Login</h2>
                <form method="post">
                    <div>
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div>
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" name="login">Login</button>
                </form>
            </div>
        <?php else: ?>
            <div class="post-form">
                <h2>Create a New Post</h2>
                <form method="post">
                    <div>
                        <label for="title">Title:</label>
                        <input type="text" id="title" name="title" required>
                    </div>
                    <div>
                        <label for="content">Content:</label>
                        <textarea id="content" name="content" rows="4" required></textarea>
                    </div>
                    <button type="submit" name="create_post">Create Post</button>
                </form>
            </div>
            
            <?php if ($_SESSION['is_admin']): ?>
                <div class="admin-panel">
                    <h2>Admin Panel</h2>
                    <p>Welcome to the admin panel. Here you can manage the application.</p>
                    <p>The flag is hidden in the admin's password hash in the database.</p>
                </div>
            <?php endif; ?>
        <?php endif; ?>
        
        <h2>Recent Posts</h2>
        <?php if (empty($posts)): ?>
            <p>No posts yet.</p>
        <?php else: ?>
            <?php foreach ($posts as $post): ?>
                <div class="post">
                    <div class="post-header">
                        <h3><?php echo $post['title']; ?></h3>
                        <div class="post-meta">
                            Posted by <?php echo htmlspecialchars($post['username']); ?> on <?php echo $post['created_at']; ?>
                        </div>
                    </div>
                    <div class="post-content">
                        <?php echo $post['content']; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</body>
</html>
