FROM nginx:alpine

# Install required packages
RUN apk --no-cache add \
    php8 \
    php8-fpm \
    php8-mysqli \
    php8-json \
    php8-openssl \
    php8-curl \
    php8-pdo \
    php8-pdo_mysql \
    php8-session \
    supervisor

# Configure nginx
COPY config/nginx.conf /etc/nginx/conf.d/default.conf

# Configure PHP-FPM
COPY config/fpm-pool.conf /etc/php8/php-fpm.d/www.conf
COPY config/php.ini /etc/php8/conf.d/custom.ini

# Configure supervisord
COPY config/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Create challenge directory
RUN mkdir -p /var/www/html

# Copy challenge files
COPY challenge/ /var/www/html/

# Set proper permissions
RUN chown -R nginx:nginx /var/www/html

# Expose port
EXPOSE 8080

# Start supervisord
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
