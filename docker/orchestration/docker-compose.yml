version: '3.8'

services:
  # Challenge Manager Service
  challenge-manager:
    build:
      context: ../services/challenge-manager
    container_name: xcerberus-challenge-manager
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
    depends_on:
      - redis
    networks:
      - xcerberus-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Redis for caching and pub/sub
  redis:
    image: redis:alpine
    container_name: xcerberus-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - xcerberus-network
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Web Application Proxy
  web-proxy:
    image: nginx:alpine
    container_name: xcerberus-web-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../config/nginx:/etc/nginx/conf.d
      - ../ssl:/etc/nginx/ssl
    depends_on:
      - challenge-manager
    networks:
      - xcerberus-network
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 128M
        reservations:
          cpus: '0.1'
          memory: 64M

  # Challenge Environment - Web Security
  challenge-web-security:
    build:
      context: ../challenge-environments/web-security
    container_name: xcerberus-web-security
    restart: unless-stopped
    networks:
      - challenge-network
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Challenge Environment - Network Security
  challenge-network-security:
    build:
      context: ../challenge-environments/network-security
    container_name: xcerberus-network-security
    restart: unless-stopped
    networks:
      - challenge-network
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Challenge Environment - Cryptography
  challenge-cryptography:
    build:
      context: ../challenge-environments/cryptography
    container_name: xcerberus-cryptography
    restart: unless-stopped
    networks:
      - challenge-network
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

networks:
  xcerberus-network:
    driver: bridge
  challenge-network:
    driver: bridge
    internal: true  # This network is not accessible from outside

volumes:
  redis-data:
