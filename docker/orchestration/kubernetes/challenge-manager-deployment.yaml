apiVersion: apps/v1
kind: Deployment
metadata:
  name: challenge-manager
  namespace: xcerberus
  labels:
    app: challenge-manager
spec:
  replicas: 3  # Start with 3 replicas, will be auto-scaled
  selector:
    matchLabels:
      app: challenge-manager
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: challenge-manager
    spec:
      containers:
      - name: challenge-manager
        image: xcerberus/challenge-manager:latest
        imagePullPolicy: Always
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        ports:
        - containerPort: 8080
        env:
        - name: NODE_ENV
          value: "production"
        - name: REDIS_URL
          value: "redis://redis-master:6379"
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: xcerberus-secrets
              key: supabase-url
        - name: SUPABASE_KEY
          valueFrom:
            secretKeyRef:
              name: xcerberus-secrets
              key: supabase-key
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - challenge-manager
              topologyKey: "kubernetes.io/hostname"
---
apiVersion: v1
kind: Service
metadata:
  name: challenge-manager
  namespace: xcerberus
spec:
  selector:
    app: challenge-manager
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: challenge-manager-hpa
  namespace: xcerberus
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: challenge-manager
  minReplicas: 3
  maxReplicas: 100  # Can scale up to 100 pods
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
