{"name": "xcerberus-challenge-manager", "version": "1.0.0", "description": "Challenge container manager for XCerberus platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "dockerode": "^3.3.5", "redis": "^4.6.7", "ioredis": "^5.3.2", "winston": "^3.10.0", "uuid": "^9.0.0", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.31.0", "node-cache": "^5.1.2", "express-rate-limit": "^6.9.0", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.1", "node-schedule": "^2.1.1"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}