require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { createClient } = require('@supabase/supabase-js');
const Redis = require('ioredis');
const winston = require('winston');
const Docker = require('dockerode');
const { v4: uuidv4 } = require('uuid');
const NodeCache = require('node-cache');
const schedule = require('node-schedule');

// Initialize logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'challenge-manager' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Redis client
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
const redis = new Redis(redisUrl);

// Initialize Docker client
const docker = new Docker({
  socketPath: process.env.DOCKER_SOCKET || '/var/run/docker.sock'
});

// Initialize in-memory cache
const cache = new NodeCache({
  stdTTL: 60, // 1 minute default TTL
  checkperiod: 120 // Check for expired keys every 2 minutes
});

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 8080;

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests from this IP, please try again after 15 minutes'
});

// Apply rate limiting to all routes
app.use(apiLimiter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date() });
});

// Readiness check endpoint
app.get('/ready', async (req, res) => {
  try {
    // Check Redis connection
    await redis.ping();
    
    // Check Docker connection
    await docker.ping();
    
    // Check Supabase connection
    const { data, error } = await supabase.from('challenges').select('id').limit(1);
    if (error) throw error;
    
    res.status(200).json({ status: 'ready', timestamp: new Date() });
  } catch (error) {
    logger.error('Readiness check failed', { error });
    res.status(503).json({ status: 'not ready', error: error.message });
  }
});

// API Routes

// Get challenge details
app.get('/api/challenges/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.headers['x-user-id'];
    
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    // Check cache first
    const cacheKey = `challenge:${id}`;
    const cachedChallenge = cache.get(cacheKey);
    
    if (cachedChallenge) {
      return res.json(cachedChallenge);
    }
    
    // Fetch challenge from database
    const { data: challenge, error } = await supabase
      .from('challenges')
      .select(`
        *,
        category:challenge_categories(*),
        difficulty:challenge_difficulty_levels(*),
        type:challenge_types(*),
        content:challenge_content(*)
      `)
      .eq('id', id)
      .single();
    
    if (error) {
      logger.error('Error fetching challenge', { error, challengeId: id });
      return res.status(404).json({ error: 'Challenge not found' });
    }
    
    // Check if user has access to this challenge
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', userId)
      .single();
    
    if (challenge.is_premium && profile.subscription_tier === 'free') {
      return res.status(403).json({ error: 'Premium subscription required' });
    }
    
    if (challenge.is_business && profile.subscription_tier !== 'business') {
      return res.status(403).json({ error: 'Business subscription required' });
    }
    
    // Check if challenge requires coins
    if (challenge.requires_coins) {
      // Check if user has purchased this challenge
      const { data: inventory } = await supabase
        .from('user_inventory')
        .select('id')
        .eq('user_id', userId)
        .eq('item_id', challenge.id)
        .single();
      
      if (!inventory) {
        return res.status(402).json({ 
          error: 'Purchase required', 
          coins_required: challenge.coin_cost 
        });
      }
    }
    
    // Cache the result
    cache.set(cacheKey, challenge, 300); // Cache for 5 minutes
    
    res.json(challenge);
  } catch (error) {
    logger.error('Error in get challenge endpoint', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start a challenge container
app.post('/api/challenges/:id/start', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.headers['x-user-id'];
    const teamId = req.body.teamId;
    
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    // Fetch challenge from database
    const { data: challenge, error } = await supabase
      .from('challenges')
      .select(`
        *,
        type:challenge_types(*),
        docker_config:challenge_docker_config(*)
      `)
      .eq('id', id)
      .single();
    
    if (error) {
      logger.error('Error fetching challenge', { error, challengeId: id });
      return res.status(404).json({ error: 'Challenge not found' });
    }
    
    // Check if user has an active container for this challenge
    const { data: existingContainer } = await supabase
      .from('challenge_container_instances')
      .select('*')
      .eq('challenge_id', id)
      .eq('user_id', userId)
      .eq('status', 'running')
      .single();
    
    if (existingContainer) {
      return res.json({
        containerId: existingContainer.container_id,
        instanceIp: existingContainer.instance_ip,
        instancePort: existingContainer.instance_port,
        accessToken: existingContainer.access_token,
        expiresAt: existingContainer.expires_at
      });
    }
    
    // Check if challenge type requires a container
    if (!challenge.docker_config) {
      return res.status(400).json({ error: 'This challenge does not require a container' });
    }
    
    // Create a new container
    const containerConfig = challenge.docker_config.container_config;
    const imageName = challenge.docker_config.image_name;
    
    // Generate unique name for container
    const containerName = `xcerberus-${id.substring(0, 8)}-${userId.substring(0, 8)}-${Date.now()}`;
    
    // Create container
    const container = await docker.createContainer({
      Image: imageName,
      name: containerName,
      Env: [
        `CHALLENGE_ID=${id}`,
        `USER_ID=${userId}`,
        ...(teamId ? [`TEAM_ID=${teamId}`] : [])
      ],
      HostConfig: {
        Memory: containerConfig.memory_limit || 256 * 1024 * 1024, // Default 256MB
        NanoCpus: (containerConfig.cpu_limit || 0.5) * 1000000000, // Default 0.5 CPU
        NetworkMode: 'challenge-network',
        AutoRemove: true
      }
    });
    
    // Start container
    await container.start();
    
    // Get container info
    const containerInfo = await container.inspect();
    const containerIp = containerInfo.NetworkSettings.Networks['challenge-network'].IPAddress;
    const containerPort = challenge.docker_config.port_mappings?.container_port || 8080;
    
    // Generate access token
    const accessToken = uuidv4();
    
    // Calculate expiry time (1 hour from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 1);
    
    // Save container instance to database
    const { data: instanceData, error: instanceError } = await supabase
      .from('challenge_container_instances')
      .insert([{
        challenge_id: id,
        user_id: userId,
        team_id: teamId,
        container_id: containerInfo.Id,
        instance_ip: containerIp,
        instance_port: containerPort,
        access_token: accessToken,
        status: 'running',
        created_at: new Date(),
        expires_at: expiresAt
      }])
      .select()
      .single();
    
    if (instanceError) {
      logger.error('Error saving container instance', { error: instanceError });
      await container.stop();
      return res.status(500).json({ error: 'Failed to start challenge' });
    }
    
    // Record challenge attempt
    await supabase
      .from('challenge_attempts')
      .insert([{
        challenge_id: id,
        user_id: userId,
        container_id: containerInfo.Id,
        attempt_number: 1,
        ip_address: req.ip,
        user_agent: req.headers['user-agent']
      }]);
    
    // Schedule container cleanup
    schedule.scheduleJob(expiresAt, async () => {
      try {
        const containerInstance = docker.getContainer(containerInfo.Id);
        await containerInstance.stop();
        
        // Update container status in database
        await supabase
          .from('challenge_container_instances')
          .update({ status: 'terminated', terminated_at: new Date() })
          .eq('container_id', containerInfo.Id);
        
        logger.info('Container terminated due to expiry', { containerId: containerInfo.Id });
      } catch (error) {
        logger.error('Error terminating container', { error, containerId: containerInfo.Id });
      }
    });
    
    res.json({
      containerId: containerInfo.Id,
      instanceIp: containerIp,
      instancePort: containerPort,
      accessToken: accessToken,
      expiresAt: expiresAt
    });
  } catch (error) {
    logger.error('Error starting challenge container', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Submit challenge solution
app.post('/api/challenges/:id/submit', async (req, res) => {
  try {
    const { id } = req.params;
    const { solution } = req.body;
    const userId = req.headers['x-user-id'];
    const teamId = req.body.teamId;
    
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    if (!solution) {
      return res.status(400).json({ error: 'Solution is required' });
    }
    
    // Fetch challenge from database
    const { data: challenge, error } = await supabase
      .from('challenges')
      .select(`
        *,
        content:challenge_content(*)
      `)
      .eq('id', id)
      .single();
    
    if (error) {
      logger.error('Error fetching challenge', { error, challengeId: id });
      return res.status(404).json({ error: 'Challenge not found' });
    }
    
    // Check if solution is correct
    const correctSolution = challenge.content[0].solution;
    const isCorrect = solution.trim() === correctSolution.trim();
    
    // Record the attempt
    if (teamId) {
      // Team attempt
      await supabase
        .from('team_challenge_attempts')
        .insert([{
          challenge_id: id,
          team_id: teamId,
          initiator_id: userId,
          submission: solution,
          is_correct: isCorrect,
          points_earned: isCorrect ? challenge.points : 0,
          attempt_number: 1, // This should be incremented based on previous attempts
          ip_address: req.ip,
          user_agent: req.headers['user-agent']
        }]);
      
      // If correct, record completion
      if (isCorrect) {
        await supabase
          .from('team_challenge_completions')
          .insert([{
            challenge_id: id,
            team_id: teamId,
            score: challenge.points,
            completed_at: new Date()
          }])
          .select();
      }
    } else {
      // Individual attempt
      await supabase
        .from('challenge_attempts')
        .insert([{
          challenge_id: id,
          user_id: userId,
          submission: solution,
          is_correct: isCorrect,
          points_earned: isCorrect ? challenge.points : 0,
          attempt_number: 1, // This should be incremented based on previous attempts
          ip_address: req.ip,
          user_agent: req.headers['user-agent']
        }]);
      
      // If correct, record completion
      if (isCorrect) {
        await supabase
          .from('challenge_completions')
          .insert([{
            challenge_id: id,
            user_id: userId,
            score: challenge.points,
            completed_at: new Date()
          }])
          .select();
        
        // Update user points
        await supabase.rpc('add_user_points', { 
          user_id: userId, 
          points_to_add: challenge.points 
        });
      }
    }
    
    res.json({
      correct: isCorrect,
      points: isCorrect ? challenge.points : 0,
      message: isCorrect ? 'Congratulations! Your solution is correct.' : 'Incorrect solution. Try again!'
    });
  } catch (error) {
    logger.error('Error submitting challenge solution', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get challenge hints
app.get('/api/challenges/:id/hints', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.headers['x-user-id'];
    
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    // Fetch hints from database
    const { data: hints, error } = await supabase
      .from('challenge_hints')
      .select('*')
      .eq('challenge_id', id)
      .order('display_order', { ascending: true });
    
    if (error) {
      logger.error('Error fetching hints', { error, challengeId: id });
      return res.status(500).json({ error: 'Failed to fetch hints' });
    }
    
    // Fetch user's purchased hints
    const { data: purchasedHints } = await supabase
      .from('user_hint_purchases')
      .select('hint_id')
      .eq('user_id', userId)
      .eq('challenge_id', id);
    
    const purchasedHintIds = purchasedHints?.map(h => h.hint_id) || [];
    
    // Mark hints as purchased or not
    const processedHints = hints.map(hint => ({
      ...hint,
      purchased: purchasedHintIds.includes(hint.id),
      hint_text: purchasedHintIds.includes(hint.id) ? hint.hint_text : null
    }));
    
    res.json(processedHints);
  } catch (error) {
    logger.error('Error fetching challenge hints', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Purchase a hint
app.post('/api/challenges/:id/hints/:hintId/purchase', async (req, res) => {
  try {
    const { id, hintId } = req.params;
    const userId = req.headers['x-user-id'];
    
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    // Fetch hint from database
    const { data: hint, error } = await supabase
      .from('challenge_hints')
      .select('*')
      .eq('id', hintId)
      .single();
    
    if (error) {
      logger.error('Error fetching hint', { error, hintId });
      return res.status(404).json({ error: 'Hint not found' });
    }
    
    // Check if user already purchased this hint
    const { data: existingPurchase } = await supabase
      .from('user_hint_purchases')
      .select('id')
      .eq('user_id', userId)
      .eq('hint_id', hintId)
      .single();
    
    if (existingPurchase) {
      return res.status(400).json({ error: 'Hint already purchased' });
    }
    
    // Check if user has enough coins
    const { data: profile } = await supabase
      .from('profiles')
      .select('coins')
      .eq('id', userId)
      .single();
    
    if (profile.coins < hint.coin_cost) {
      return res.status(402).json({ error: 'Not enough coins' });
    }
    
    // Deduct coins from user
    await supabase.rpc('add_user_points', { 
      user_id: userId, 
      points_to_add: -hint.coin_cost 
    });
    
    // Record hint purchase
    await supabase
      .from('user_hint_purchases')
      .insert([{
        user_id: userId,
        challenge_id: id,
        hint_id: hintId,
        coins_spent: hint.coin_cost,
        purchased_at: new Date()
      }]);
    
    // Record coin transaction
    await supabase
      .from('coin_transactions')
      .insert([{
        user_id: userId,
        amount: -hint.coin_cost,
        transaction_type: 'spend',
        description: `Purchased hint for challenge ${id}`,
        reference_id: hintId
      }]);
    
    res.json({
      success: true,
      hint_text: hint.hint_text,
      coins_spent: hint.coin_cost
    });
  } catch (error) {
    logger.error('Error purchasing hint', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
app.listen(PORT, () => {
  logger.info(`Challenge manager service running on port ${PORT}`);
});

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  // Close Redis connection
  await redis.quit();
  
  // Exit process
  process.exit(0);
});

module.exports = app;
