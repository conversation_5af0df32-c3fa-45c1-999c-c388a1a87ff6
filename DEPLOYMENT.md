# CyberForce Deployment Guide

This guide provides instructions for deploying the CyberForce application to an Amazon EC2 instance.

## Prerequisites

- An AWS account with EC2 access
- An EC2 instance running Ubuntu (recommended: t2.micro or larger)
- SSH access to the EC2 instance
- Basic knowledge of Linux commands

## Deployment Steps

### 1. Prepare the Deployment Package

On your local machine:

```bash
# Build the application
npm run build

# Create a deployment package
zip -r cyberforce-deploy.zip dist server.js server-package.json deploy.sh
```

### 2. Transfer Files to EC2

Use SCP to transfer the deployment package to your EC2 instance:

```bash
scp -i /path/to/your-key.pem cyberforce-deploy.zip ubuntu@your-ec2-ip:~
```

### 3. Deploy on EC2

SSH into your EC2 instance:

```bash
ssh -i /path/to/your-key.pem ubuntu@your-ec2-ip
```

Extract and deploy the application:

```bash
# Extract the deployment package
unzip cyberforce-deploy.zip

# Make the deployment script executable
chmod +x deploy.sh

# Run the deployment script
./deploy.sh
```

### 4. Configure Security Group

Make sure your EC2 security group allows inbound traffic on port 80 (HTTP).

1. Go to the EC2 Dashboard in AWS Console
2. Select your instance
3. Click on the Security tab
4. Click on the Security Group
5. Add an inbound rule for HTTP (port 80)
6. Optionally, add a rule for HTTPS (port 443) if you plan to set up SSL

### 5. Access Your Application

Your application should now be accessible at:

```
http://your-ec2-ip
```

### 6. (Optional) Set Up a Domain Name

For a production environment, you should:

1. Register a domain name
2. Point it to your EC2 instance
3. Set up HTTPS using Let's Encrypt or AWS Certificate Manager
4. Configure a reverse proxy like Nginx to handle HTTPS and forward requests to your Node.js server

### 7. Monitoring and Management

You can manage your application using PM2:

```bash
# Check status
pm2 status

# View logs
pm2 logs cyberforce

# Restart the application
pm2 restart cyberforce

# Stop the application
pm2 stop cyberforce
```

## Troubleshooting

If you encounter issues:

1. Check the application logs: `pm2 logs cyberforce`
2. Verify the server is running: `pm2 status`
3. Check if the port is open: `sudo netstat -tulpn | grep 3000`
4. Verify security group settings in AWS Console

## Updating the Application

To update the application:

1. Build a new version locally
2. Create and transfer a new deployment package
3. SSH into the EC2 instance
4. Extract the new files
5. Run `pm2 restart cyberforce`
