/*
  # Fix User Tables and Policies

  1. Changes
    - Drop existing user_activity and user_settings tables
    - Recreate tables with proper structure
    - Add unique policy names
    - Add performance indexes
    - Add helper functions

  2. Security
    - Enable RLS
    - Add proper policies for data access
    - Ensure secure function execution
*/

-- Drop existing tables if they exist
DROP TABLE IF EXISTS user_activity CASCADE;
DROP TABLE IF EXISTS user_settings CASCADE;

-- Create user_activity table
CREATE TABLE IF NOT EXISTS user_activity (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type text NOT NULL,
  description text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now()
);

-- Create user_settings table
CREATE TABLE IF NOT EXISTS user_settings (
  user_id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  theme text DEFAULT 'light',
  notifications_enabled boolean DEFAULT true,
  email_preferences jsonb DEFAULT '{
    "marketing": false,
    "security": true,
    "updates": true
  }'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE user_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Create uniquely named policies
CREATE POLICY "user_activity_read_own_20250225" 
  ON user_activity
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "user_settings_read_own_20250225"
  ON user_settings
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "user_settings_update_own_20250225"
  ON user_settings
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Add performance indexes
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id_20250225 ON user_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_created_at_20250225 ON user_activity(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id_20250225 ON user_settings(user_id);

-- Function to log user activity
CREATE OR REPLACE FUNCTION log_user_activity_20250225(
  p_user_id uuid,
  p_activity_type text,
  p_description text,
  p_metadata jsonb DEFAULT '{}'::jsonb
) RETURNS void AS $$
BEGIN
  INSERT INTO user_activity (user_id, activity_type, description, metadata)
  VALUES (p_user_id, p_activity_type, p_description, p_metadata);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to ensure user settings exist
CREATE OR REPLACE FUNCTION ensure_user_settings_20250225(p_user_id uuid)
RETURNS void AS $$
BEGIN
  INSERT INTO user_settings (user_id)
  VALUES (p_user_id)
  ON CONFLICT (user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Initialize settings for existing users
DO $$
DECLARE
  user_record RECORD;
BEGIN
  FOR user_record IN SELECT id FROM auth.users
  LOOP
    PERFORM ensure_user_settings_20250225(user_record.id);
  END LOOP;
END $$;