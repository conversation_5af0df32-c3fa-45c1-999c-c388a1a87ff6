/*
  # Fix User Activity Policy Conflict

  1. Changes
    - Drop existing user_activity table and policies
    - Recreate table with proper structure
    - Add new uniquely named policies
    - Add performance indexes
    - Add helper functions

  2. Security
    - Enable RLS
    - Add proper policies for data access
    - Ensure secure function execution
*/

-- Drop existing table if it exists
DROP TABLE IF EXISTS user_activity CASCADE;

-- Create user_activity table
CREATE TABLE IF NOT EXISTS user_activity (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type text NOT NULL,
  description text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE user_activity ENABLE ROW LEVEL SECURITY;

-- Create uniquely named policy
CREATE POLICY "user_activity_read_own_v2" 
  ON user_activity
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Add performance indexes
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id_v2 ON user_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_created_at_v2 ON user_activity(created_at DESC);

-- Function to log user activity with unique name
CREATE OR REPLACE FUNCTION log_user_activity_v2(
  p_user_id uuid,
  p_activity_type text,
  p_description text,
  p_metadata jsonb DEFAULT '{}'::jsonb
) RETURNS void AS $$
BEGIN
  INSERT INTO user_activity (user_id, activity_type, description, metadata)
  VALUES (p_user_id, p_activity_type, p_description, p_metadata);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;