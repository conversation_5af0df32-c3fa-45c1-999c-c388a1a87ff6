/*
  # User Management System Enhancement

  1. New Tables
    - user_activity: Track user actions and events
    - user_settings: Store user preferences and settings

  2. Performance Improvements
    - Added indexes for frequently queried columns
    - Optimized function parameters
    - Improved error handling

  3. Security
    - Enabled RLS on all tables
    - Added proper policies for data access
    - Used SECURITY DEFINER for sensitive functions

  4. Data Integrity
    - Added proper foreign key constraints
    - Implemented safe data initialization
    - Added transaction handling for coins
*/

-- Create tables first
CREATE TABLE IF NOT EXISTS user_activity (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type text NOT NULL,
  description text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS user_settings (
  user_id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  theme text DEFAULT 'light',
  notifications_enabled boolean DEFAULT true,
  email_preferences jsonb DEFAULT '{
    "marketing": false,
    "security": true,
    "updates": true
  }'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE user_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can read own activity"
  ON user_activity
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can read own settings"
  ON user_settings
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update own settings"
  ON user_settings
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Add performance indexes
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id ON user_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_created_at ON user_activity(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_coins_user_id ON user_coins(user_id);
CREATE INDEX IF NOT EXISTS idx_leaderboard_user_id ON leaderboard(user_id);
CREATE INDEX IF NOT EXISTS idx_challenge_submissions_user_id ON challenge_submissions(user_id);

-- Function to log user activity
CREATE OR REPLACE FUNCTION log_user_activity(
  p_user_id uuid,
  p_activity_type text,
  p_description text,
  p_metadata jsonb DEFAULT '{}'::jsonb
) RETURNS void AS $$
BEGIN
  INSERT INTO user_activity (user_id, activity_type, description, metadata)
  VALUES (p_user_id, p_activity_type, p_description, p_metadata);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to initialize user data
CREATE OR REPLACE FUNCTION initialize_user_data(p_user_id uuid)
RETURNS void AS $$
DECLARE
  v_email text;
  v_max_rank integer;
BEGIN
  -- Get user email
  SELECT email INTO v_email FROM auth.users WHERE id = p_user_id;
  
  -- Get max rank
  SELECT COALESCE(MAX(rank), 0) INTO v_max_rank FROM leaderboard;

  -- Create user profile
  INSERT INTO users (id, email, username, full_name)
  VALUES (
    p_user_id,
    v_email,
    'user_' || substr(p_user_id::text, 1, 8),
    'New User'
  )
  ON CONFLICT (id) DO NOTHING;

  -- Initialize user coins
  INSERT INTO user_coins (user_id, balance, last_transaction_at)
  VALUES (p_user_id, 100, now())
  ON CONFLICT (user_id) DO NOTHING;

  -- Initialize leaderboard entry
  INSERT INTO leaderboard (user_id, total_points, challenges_completed, rank)
  VALUES (p_user_id, 0, 0, v_max_rank + 1)
  ON CONFLICT (user_id) DO NOTHING;

  -- Initialize user settings
  INSERT INTO user_settings (user_id)
  VALUES (p_user_id)
  ON CONFLICT (user_id) DO NOTHING;

  -- Create welcome transaction
  INSERT INTO coin_transactions (
    user_id,
    amount,
    transaction_type,
    description
  )
  SELECT 
    p_user_id,
    100,
    'credit',
    'Welcome bonus'
  WHERE NOT EXISTS (
    SELECT 1 FROM coin_transactions 
    WHERE user_id = p_user_id AND description = 'Welcome bonus'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user profile
CREATE OR REPLACE FUNCTION get_user_profile(p_user_id uuid)
RETURNS json AS $$
BEGIN
  -- Initialize user data if needed
  PERFORM initialize_user_data(p_user_id);

  RETURN (
    SELECT json_build_object(
      'id', u.id,
      'email', u.email,
      'username', u.username,
      'full_name', u.full_name,
      'avatar_url', u.avatar_url,
      'coins', COALESCE(uc.balance, 0),
      'rank', COALESCE(l.rank, 0),
      'total_points', COALESCE(l.total_points, 0),
      'challenges_completed', COALESCE(l.challenges_completed, 0),
      'settings', (
        SELECT row_to_json(s.*)
        FROM user_settings s
        WHERE s.user_id = u.id
      ),
      'recent_activity', (
        SELECT json_agg(row_to_json(a.*))
        FROM (
          SELECT activity_type, description, created_at
          FROM user_activity
          WHERE user_id = u.id
          ORDER BY created_at DESC
          LIMIT 5
        ) a
      )
    )
    FROM users u
    LEFT JOIN user_coins uc ON uc.user_id = u.id
    LEFT JOIN leaderboard l ON l.user_id = u.id
    WHERE u.id = p_user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Initialize user data
  PERFORM initialize_user_data(NEW.id);
  
  -- Log account creation
  PERFORM log_user_activity(
    NEW.id,
    'account_created',
    'Account created successfully',
    jsonb_build_object('email', NEW.email)
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Fix any existing users missing data
DO $$
DECLARE
  user_record RECORD;
BEGIN
  FOR user_record IN SELECT id FROM auth.users
  LOOP
    PERFORM initialize_user_data(user_record.id);
  END LOOP;
END $$;