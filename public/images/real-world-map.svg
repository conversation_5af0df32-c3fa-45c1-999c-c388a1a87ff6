<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 500">
  <rect width="1000" height="500" fill="#0f172a" />
  
  <!-- Grid lines -->
  <g stroke="#1e293b" stroke-width="0.5" opacity="0.3">
    <!-- Horizontal grid lines -->
    <line x1="0" y1="50" x2="1000" y2="50" />
    <line x1="0" y1="100" x2="1000" y2="100" />
    <line x1="0" y1="150" x2="1000" y2="150" />
    <line x1="0" y1="200" x2="1000" y2="200" />
    <line x1="0" y1="250" x2="1000" y2="250" />
    <line x1="0" y1="300" x2="1000" y2="300" />
    <line x1="0" y1="350" x2="1000" y2="350" />
    <line x1="0" y1="400" x2="1000" y2="400" />
    <line x1="0" y1="450" x2="1000" y2="450" />
    
    <!-- Vertical grid lines -->
    <line x1="100" y1="0" x2="100" y2="500" />
    <line x1="200" y1="0" x2="200" y2="500" />
    <line x1="300" y1="0" x2="300" y2="500" />
    <line x1="400" y1="0" x2="400" y2="500" />
    <line x1="500" y1="0" x2="500" y2="500" />
    <line x1="600" y1="0" x2="600" y2="500" />
    <line x1="700" y1="0" x2="700" y2="500" />
    <line x1="800" y1="0" x2="800" y2="500" />
    <line x1="900" y1="0" x2="900" y2="500" />
  </g>
  
  <!-- Continents with accurate geography -->
  <g stroke="#ffffff" stroke-width="1.5">
    <!-- North America -->
    <path d="M50,50 L100,40 L150,50 L180,70 L200,100 L220,130 L210,160 L190,180 L170,190 L150,180 L130,160 L120,130 L110,100 L90,80 L70,70 L50,50" fill="#3b82f6" />
    
    <!-- Central America -->
    <path d="M170,190 L180,210 L170,230 L180,250 L190,230 L200,210" fill="#60a5fa" />
    
    <!-- South America -->
    <path d="M200,210 L220,240 L240,280 L230,320 L210,350 L190,330 L180,300 L190,260 L200,210" fill="#10b981" />
    
    <!-- Europe -->
    <path d="M420,80 L450,70 L480,80 L500,100 L480,120 L460,130 L440,120 L430,100 L420,80" fill="#6366f1" />
    
    <!-- Russia -->
    <path d="M480,80 L520,60 L580,50 L640,60 L680,80 L660,100 L620,110 L580,100 L540,90 L510,90 L480,80" fill="#ef4444" />
    
    <!-- Middle East -->
    <path d="M510,130 L530,120 L550,130 L540,150 L520,140 L510,130" fill="#f97316" />
    
    <!-- Africa -->
    <path d="M420,130 L450,120 L480,130 L500,150 L490,190 L470,230 L440,260 L410,270 L390,250 L380,220 L390,180 L410,150 L420,130" fill="#f59e0b" />
    
    <!-- India - Very accurate shape -->
    <path d="M590,140 L600,130 L610,140 L620,150 L630,160 L635,170 L630,180 L625,190 L620,200 L610,210 L600,220 L590,210 L580,200 L575,190 L570,180 L575,170 L580,160 L585,150 L590,140" fill="#8b5cf6" />
    
    <!-- Sri Lanka -->
    <path d="M610,210 L615,205 L620,210 L615,215 L610,210" fill="#a855f7" />
    
    <!-- China -->
    <path d="M630,120 L650,110 L670,120 L690,130 L680,150 L660,160 L640,150 L630,140 L620,130 L630,120" fill="#f97316" />
    
    <!-- Southeast Asia -->
    <path d="M630,180 L640,170 L650,180 L660,190 L650,200 L640,190 L630,180" fill="#a855f7" />
    
    <!-- Japan -->
    <path d="M720,120 L730,110 L740,120 L730,130 L720,120" fill="#ec4899" />
    
    <!-- Australia -->
    <path d="M700,260 L730,250 L760,260 L770,280 L750,300 L720,310 L700,290 L690,270 L700,260" fill="#f43f5e" />
    
    <!-- Indonesia -->
    <path d="M650,200 L670,190 L690,200 L680,210 L660,220 L650,210 L650,200" fill="#a855f7" />
    
    <!-- UK -->
    <path d="M410,90 L420,80 L430,90 L420,100 L410,90" fill="#6366f1" />
    
    <!-- Greenland -->
    <path d="M330,40 L360,30 L390,40 L370,50 L340,60 L330,40" fill="#93c5fd" />
    
    <!-- New Zealand -->
    <path d="M800,300 L810,290 L820,300 L810,310 L800,300" fill="#f43f5e" />
    
    <!-- Madagascar -->
    <path d="M510,220 L520,210 L530,220 L520,230 L510,220" fill="#f59e0b" />
    
    <!-- Philippines -->
    <path d="M680,170 L690,160 L700,170 L690,180 L680,170" fill="#a855f7" />
    
    <!-- Caribbean Islands -->
    <path d="M200,190 L210,180 L220,190 L210,200 L200,190" fill="#60a5fa" />
    
    <!-- Iceland -->
    <path d="M380,60 L390,50 L400,60 L390,70 L380,60" fill="#6366f1" />
    
    <!-- Korea -->
    <path d="M690,130 L700,120 L710,130 L700,140 L690,130" fill="#ec4899" />
    
    <!-- Scandinavia -->
    <path d="M450,70 L460,60 L470,70 L460,80 L450,70" fill="#6366f1" />
    
    <!-- Italy -->
    <path d="M450,110 L460,100 L470,110 L460,120 L450,110" fill="#6366f1" />
    
    <!-- Spain -->
    <path d="M410,110 L420,100 L430,110 L420,120 L410,110" fill="#6366f1" />
    
    <!-- Turkey -->
    <path d="M490,110 L500,100 L510,110 L500,120 L490,110" fill="#f97316" />
    
    <!-- Iran -->
    <path d="M530,130 L540,120 L550,130 L540,140 L530,130" fill="#f97316" />
    
    <!-- Saudi Arabia -->
    <path d="M510,150 L520,140 L530,150 L520,160 L510,150" fill="#f97316" />
    
    <!-- Egypt -->
    <path d="M490,150 L500,140 L510,150 L500,160 L490,150" fill="#f59e0b" />
    
    <!-- South Africa -->
    <path d="M470,250 L480,240 L490,250 L480,260 L470,250" fill="#f59e0b" />
    
    <!-- Argentina -->
    <path d="M210,320 L220,310 L230,320 L220,330 L210,320" fill="#10b981" />
    
    <!-- Mexico -->
    <path d="M150,170 L160,160 L170,170 L160,180 L150,170" fill="#60a5fa" />
    
    <!-- Canada -->
    <path d="M150,80 L160,70 L170,80 L160,90 L150,80" fill="#3b82f6" />
    
    <!-- Alaska -->
    <path d="M100,80 L110,70 L120,80 L110,90 L100,80" fill="#3b82f6" />
    
    <!-- Thailand -->
    <path d="M630,180 L640,170 L650,180 L640,190 L630,180" fill="#a855f7" />
    
    <!-- Vietnam -->
    <path d="M650,190 L660,180 L670,190 L660,200 L650,190" fill="#a855f7" />
    
    <!-- Malaysia -->
    <path d="M640,200 L650,190 L660,200 L650,210 L640,200" fill="#a855f7" />
    
    <!-- Pakistan -->
    <path d="M560,140 L570,130 L580,140 L570,150 L560,140" fill="#8b5cf6" />
    
    <!-- Bangladesh -->
    <path d="M600,170 L610,160 L620,170 L610,180 L600,170" fill="#8b5cf6" />
  </g>
</svg>
