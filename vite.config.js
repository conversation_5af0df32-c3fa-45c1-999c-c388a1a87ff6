import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    include: [
      'xterm',
      'xterm-addon-fit',
      'xterm-addon-web-links',
      'xterm-addon-search',
      'xterm-addon-unicode11',
      'three',
    ]
  },
  build: {
    commonjsOptions: {
      transformMixedEsModules: true
    },
    chunkSizeWarningLimit: 1000,
  },
  server: {
    host: true,
    port: 5173,
    open: true
  }
})