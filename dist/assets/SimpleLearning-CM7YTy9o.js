import{b2 as H,r as d,s as j,j as e,au as V,a9 as Q,I as re,ae as te,k as Y,aN as z,ag as A,d as X,ba as Z,u as ae,x as ie,aC as W,az as le}from"./index-c6UceSOv.js";import{S as ne}from"./SimpleUpgradeBanner-vRfC26d4.js";const O=d.createContext();function oe({children:s}){const{user:m,profile:o}=H(),[C,L]=d.useState([]),[D,F]=d.useState([]),[S,k]=d.useState([]),[w,f]=d.useState({}),[v,i]=d.useState([]),[E,N]=d.useState(!0),[y,_]=d.useState(null);d.useEffect(()=>{(async()=>{try{N(!0);const{data:a,error:r}=await j.from("learning_module_categories").select("*").order("display_order",{ascending:!0});if(r)throw r;const{data:n,error:u}=await j.from("learning_module_difficulty_levels").select("*").order("display_order",{ascending:!0});if(u)throw u;let c=j.from("learning_modules").select(`
            id,
            title,
            slug,
            description,
            category_id,
            difficulty_id,
            estimated_time,
            is_premium,
            is_business,
            created_at,
            category:learning_module_categories(name),
            difficulty:learning_module_difficulty_levels(name)
          `).eq("is_active",!0);m?o&&(o.subscription_tier==="free"?c=c.eq("is_premium",!1).eq("is_business",!1):o.subscription_tier==="premium"&&(c=c.eq("is_business",!1))):c=c.eq("is_premium",!1).eq("is_business",!1);const{data:h,error:B}=await c;if(B)throw B;let M=j.from("learning_paths").select(`
            id,
            title,
            description,
            category_id,
            is_premium,
            is_business,
            created_at,
            category:learning_module_categories(name),
            modules:learning_path_modules(
              module_id,
              display_order
            )
          `).eq("is_active",!0);m?o&&(o.subscription_tier==="free"?M=M.eq("is_premium",!1).eq("is_business",!1):o.subscription_tier==="premium"&&(M=M.eq("is_business",!1))):M=M.eq("is_premium",!1).eq("is_business",!1);const{data:G,error:T}=await M;if(T)throw T;F(a),k(n),L(h),i(G)}catch(a){console.error("Error fetching learning modules:",a),_(a.message)}finally{N(!1)}})()},[m,o]),d.useEffect(()=>{if(!m){f({});return}(async()=>{try{N(!0);const{data:r,error:n}=await j.from("learning_module_progress").select(`
            id,
            module_id,
            progress,
            last_activity,
            completed,
            completed_at
          `).eq("user_id",m.id);if(n)throw n;const u={};r.forEach(c=>{u[c.module_id]=c}),f(u)}catch(r){console.error("Error fetching user progress:",r),_(r.message)}finally{N(!1)}})();const a=j.channel(`user-module-progress-${m.id}`).on("INSERT",r=>{f(n=>({...n,[r.new.module_id]:r.new}))}).on("UPDATE",r=>{f(n=>({...n,[r.new.module_id]:r.new}))}).subscribe();return()=>{j.removeChannel(a)}},[m]);const P=async l=>{try{N(!0);const a=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(l);let r=j.from("learning_modules").select(`
          id,
          title,
          slug,
          description,
          category_id,
          difficulty_id,
          estimated_time,
          is_premium,
          is_business,
          created_at,
          category:learning_module_categories(name),
          difficulty:learning_module_difficulty_levels(name),
          content:learning_module_content(content)
        `);a?r=r.eq("id",l):r=r.eq("slug",l);const{data:n,error:u}=await r.single();if(u)throw u;return n}catch(a){throw console.error("Error fetching module:",a),_(a.message),a}finally{N(!1)}},$=async(l,a,r=!1)=>{try{if(!m)throw new Error("You must be logged in to update progress");const n=w[l];if(n){const{data:u,error:c}=await j.from("learning_module_progress").update({progress:a,completed:r,completed_at:r?new Date:null,last_activity:new Date}).eq("id",n.id).select().single();if(c)throw c;return f(h=>({...h,[l]:u})),u}else{const{data:u,error:c}=await j.from("learning_module_progress").insert([{module_id:l,user_id:m.id,progress:a,completed:r,completed_at:r?new Date:null,last_activity:new Date}]).select().single();if(c)throw c;return f(h=>({...h,[l]:u})),u}}catch(n){throw console.error("Error updating progress:",n),_(n.message),n}},q=async(l,a,r="")=>{try{if(!m)throw new Error("You must be logged in to rate a module");const{data:n,error:u}=await j.from("learning_module_ratings").select("id").eq("user_id",m.id).eq("module_id",l).single();if(u&&u.code!=="PGRST116")throw u;if(n){const{data:c,error:h}=await j.from("learning_module_ratings").update({rating:a,feedback:r}).eq("id",n.id).select().single();if(h)throw h;return c}else{const{data:c,error:h}=await j.from("learning_module_ratings").insert([{module_id:l,user_id:m.id,rating:a,feedback:r}]).select().single();if(h)throw h;return c}}catch(n){throw console.error("Error rating module:",n),_(n.message),n}},t=l=>{var a;return((a=w[l])==null?void 0:a.completed)||!1},R={modules:C,categories:D,difficulties:S,learningPaths:v,userProgress:w,loading:E,error:y,getModuleById:P,updateModuleProgress:$,rateModule:q,isModuleCompleted:t,getModuleProgress:l=>{var a;return((a=w[l])==null?void 0:a.progress)||0},getFilteredModules:(l={})=>{let a=[...C];if(l.category&&(a=a.filter(r=>r.category.name===l.category)),l.difficulty&&(a=a.filter(r=>r.difficulty.name===l.difficulty)),l.search){const r=l.search.toLowerCase();a=a.filter(n=>n.title.toLowerCase().includes(r)||n.description.toLowerCase().includes(r))}return!o||o.subscription_tier==="free"?a=a.filter(r=>!r.is_premium&&!r.is_business):o.subscription_tier==="premium"&&(a=a.filter(r=>!r.is_business)),l.completed===!0?a=a.filter(r=>t(r.id)):l.completed===!1&&(a=a.filter(r=>!t(r.id))),a}};return e.jsx(O.Provider,{value:R,children:s})}function ee(){const s=d.useContext(O);if(s===void 0)throw new Error("useLearningModule must be used within a LearningModuleProvider");return s}const de=()=>{const{darkMode:s}=V(),{user:m,profile:o}=H(),{modules:C,categories:L,difficulties:D,loading:F,error:S,getFilteredModules:k,isModuleCompleted:w,getModuleProgress:f}=ee(),[v,i]=d.useState([]),[E,N]=d.useState(""),[y,_]=d.useState({category:"",difficulty:"",completed:""});d.useEffect(()=>{const t=k({category:y.category,difficulty:y.difficulty,completed:y.completed==="completed"?!0:y.completed==="incomplete"?!1:void 0,search:E});i(t)},[C,y,E,k]);const P=(t,b)=>{_(p=>({...p,[t]:b}))},$=()=>{_({category:"",difficulty:"",completed:""}),N("")},q=t=>{if(!t)return"N/A";const b=Math.floor(t/60),p=t%60;return b===0?`${p} min`:p===0?`${b} hr`:`${b} hr ${p} min`};return F?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(Q,{className:"animate-spin text-4xl text-[#88cc14]"})}):S?e.jsxs("div",{className:`${s?"bg-red-900/20 border-red-900/30":"bg-red-100 border-red-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold text-red-500 mb-4",children:"Error"}),e.jsx("p",{className:`${s?"text-red-300":"text-red-700"}`,children:S}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600",children:"Retry"})]}):e.jsxs("div",{children:[!o||o.subscription_tier==="free"?e.jsx(ne,{title:"Upgrade to Premium",description:"Get access to all learning modules and features with a premium subscription.",buttonText:"View Plans",buttonLink:"/pricing",className:"mb-6"}):null,e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",className:`w-full pl-10 pr-4 py-2 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,placeholder:"Search modules...",value:E,onChange:t=>N(t.target.value)}),e.jsx(re,{className:"absolute left-3 top-3 text-gray-400"})]})}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs("select",{className:`px-3 py-2 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,value:y.category,onChange:t=>P("category",t.target.value),children:[e.jsx("option",{value:"",children:"All Categories"}),L.map(t=>e.jsx("option",{value:t.name,children:t.name},t.id))]}),e.jsxs("select",{className:`px-3 py-2 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,value:y.difficulty,onChange:t=>P("difficulty",t.target.value),children:[e.jsx("option",{value:"",children:"All Difficulties"}),D.map(t=>e.jsx("option",{value:t.name,children:t.name},t.id))]}),e.jsxs("select",{className:`px-3 py-2 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,value:y.completed,onChange:t=>P("completed",t.target.value),children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"incomplete",children:"Incomplete"})]}),e.jsx("button",{onClick:$,className:`px-3 py-2 rounded-lg ${s?"bg-[#252D4A] hover:bg-[#313e6a] text-white":"bg-gray-200 hover:bg-gray-300 text-gray-800"} border border-transparent`,children:"Reset"})]})]})}),e.jsx("div",{className:"mb-4",children:e.jsxs("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:["Showing ",v.length," of ",C.length," modules"]})}),v.length===0?e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx(te,{className:"mx-auto text-4xl mb-4 text-gray-500"}),e.jsx("h2",{className:"text-xl font-bold mb-2",children:"No Modules Found"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Try adjusting your filters or search query."}),e.jsx("button",{onClick:$,className:"mt-4 px-4 py-2 theme-button-primary rounded-lg",children:"Reset Filters"})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:v.map(t=>{const b=w(t.id),p=f(t.id),R=!t.is_premium||(o==null?void 0:o.subscription_tier)==="premium"||(o==null?void 0:o.subscription_tier)==="business";return e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden transition-transform hover:transform hover:scale-[1.02]`,children:e.jsxs("div",{className:`p-4 ${b?s?"bg-[#88cc14]/10":"bg-[#88cc14]/5":""}`,children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h3",{className:"text-lg font-bold",children:t.title}),e.jsxs("div",{className:"flex items-center",children:[t.is_business&&e.jsx("span",{className:`ml-2 px-2 py-1 rounded text-xs ${s?"bg-purple-900/30 text-purple-300":"bg-purple-100 text-purple-800"}`,children:"Business"}),t.is_premium&&!t.is_business&&e.jsx("span",{className:`ml-2 px-2 py-1 rounded text-xs ${s?"bg-blue-900/30 text-blue-300":"bg-blue-100 text-blue-800"}`,children:"Premium"})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:t.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:t.difficulty.name}),t.estimated_time&&e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${s?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(Y,{className:"mr-1"})," ",q(t.estimated_time)]})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4 line-clamp-2`,children:t.description}),p>0&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between text-xs mb-1",children:[e.jsx("span",{children:b?"Completed":"In Progress"}),e.jsxs("span",{children:[Math.round(p),"%"]})]}),e.jsx("div",{className:`h-2 w-full rounded-full ${s?"bg-gray-800":"bg-gray-200"}`,children:e.jsx("div",{className:"h-2 rounded-full bg-[#88cc14]",style:{width:`${p}%`}})})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[b&&e.jsxs("span",{className:"flex items-center text-[#88cc14]",children:[e.jsx(z,{className:"mr-1"})," Completed"]}),!b&&p>0&&e.jsxs("span",{className:"flex items-center text-blue-500",children:[Math.round(p),"% Complete"]}),!b&&p===0&&e.jsx("span",{className:"invisible",children:"Placeholder"}),R?e.jsx(A,{to:`/learn/${t.slug||t.id}`,className:"px-4 py-2 theme-button-primary rounded-lg",children:p>0?"Continue":"Start Learning"}):e.jsxs(A,{to:"/pricing",className:`px-4 py-2 rounded-lg flex items-center ${s?"bg-[#252D4A] text-gray-300":"bg-gray-200 text-gray-700"}`,children:[e.jsx(X,{className:"mr-2"})," Upgrade"]})]})]})},t.id)})})]})},ce=()=>{var J,K;const{darkMode:s}=V(),{user:m,profile:o}=H(),{getModuleById:C,updateModuleProgress:L,rateModule:D,isModuleCompleted:F,getModuleProgress:S,loading:k,error:w}=ee(),{moduleId:f}=Z(),v=ae(),[i,E]=d.useState(null),[N,y]=d.useState(!0),[_,P]=d.useState(null),[$,q]=d.useState(0),[t,b]=d.useState(0),[p,R]=d.useState(""),[l,a]=d.useState(!1),[r,n]=d.useState(0),[u,c]=d.useState(0),[h,B]=d.useState(!1);d.useEffect(()=>{(async()=>{try{y(!0);const x=await C(f);E(x),c(S(x.id)),B(F(x.id))}catch(x){console.error("Error fetching module:",x),P(x.message)}finally{y(!1)}})()},[f,C,S,F]);const M=async(g,x=!1)=>{try{if(!m){v("/login",{state:{from:`/learn/${f}`}});return}await L(i.id,g,x),c(g),B(x)}catch(U){console.error("Error updating progress:",U)}},G=async()=>{try{if(!m){v("/login",{state:{from:`/learn/${f}`}});return}if($===0)return;a(!0),await D(i.id,$,p),a(!1)}catch(g){console.error("Error submitting rating:",g),a(!1)}},T=g=>{if(!g)return"N/A";const x=Math.floor(g/60),U=g%60;return x===0?`${U} min`:U===0?`${x} hr`:`${x} hr ${U} min`},se=()=>i?!!(!i.is_premium&&!i.is_business||i.is_premium&&!i.is_business&&(o==null?void 0:o.subscription_tier)==="premium"||(o==null?void 0:o.subscription_tier)==="business"):!1;if(N||k)return e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(Q,{className:"animate-spin text-4xl text-[#88cc14]"})});if(_||w)return e.jsxs("div",{className:`${s?"bg-red-900/20 border-red-900/30":"bg-red-100 border-red-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold text-red-500 mb-4",children:"Error"}),e.jsx("p",{className:`${s?"text-red-300":"text-red-700"}`,children:_||w}),e.jsx("button",{onClick:()=>v("/learn"),className:"mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600",children:"Back to Modules"})]});if(!i)return e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx(ie,{className:"mx-auto text-4xl mb-4 text-yellow-500"}),e.jsx("h2",{className:"text-xl font-bold mb-2",children:"Module Not Found"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"The module you're looking for doesn't exist or you don't have access to it."}),e.jsx(A,{to:"/learn",className:"mt-4 px-4 py-2 theme-button-primary rounded-lg inline-block",children:"Browse Modules"})]});if(!se())return e.jsxs("div",{children:[e.jsx("div",{className:"mb-6",children:e.jsxs(A,{to:"/learn",className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(W,{className:"mr-2"})," Back to Modules"]})}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:i.title}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:i.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:i.difficulty.name}),i.estimated_time&&e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${s?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(Y,{className:"mr-1"})," ",T(i.estimated_time)]}),i.is_business&&e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/30 text-purple-300":"bg-purple-100 text-purple-800"}`,children:"Business"}),i.is_premium&&!i.is_business&&e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/30 text-blue-300":"bg-blue-100 text-blue-800"}`,children:"Premium"})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-6`,children:i.description}),e.jsxs("div",{className:`${s?"bg-[#252D4A] border-gray-700":"bg-gray-100 border-gray-300"} border rounded-lg p-6 text-center mb-6`,children:[e.jsx(X,{className:"mx-auto text-4xl mb-4 text-[#88cc14]"}),e.jsx("h2",{className:"text-xl font-bold mb-2",children:"Premium Content"}),e.jsxs("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:["This module is available to ",i.is_business?"Business":"Premium"," subscribers only."]}),e.jsx(A,{to:"/pricing",className:"px-4 py-2 theme-button-primary rounded-lg inline-block",children:"Upgrade Now"})]})]})]});const I=((K=(J=i.content)==null?void 0:J[0])==null?void 0:K.content)||{};return e.jsxs("div",{children:[e.jsx("div",{className:"mb-6",children:e.jsxs(A,{to:"/learn",className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(W,{className:"mr-2"})," Back to Modules"]})}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:i.title}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:i.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:i.difficulty.name}),i.estimated_time&&e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${s?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(Y,{className:"mr-1"})," ",T(i.estimated_time)]}),i.is_business&&e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/30 text-purple-300":"bg-purple-100 text-purple-800"}`,children:"Business"}),i.is_premium&&!i.is_business&&e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/30 text-blue-300":"bg-blue-100 text-blue-800"}`,children:"Premium"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:h?"Completed":u>0?"In Progress":"Not Started"}),e.jsxs("span",{children:[Math.round(u),"%"]})]}),e.jsx("div",{className:`h-2 w-full rounded-full ${s?"bg-gray-800":"bg-gray-200"}`,children:e.jsx("div",{className:"h-2 rounded-full bg-[#88cc14]",style:{width:`${u}%`}})})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-6`,children:i.description}),e.jsxs("div",{className:`${s?"bg-[#252D4A] border-gray-700":"bg-gray-100 border-gray-300"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Module Content"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Introduction"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:I.introduction||"Introduction content will be displayed here."})]}),I.sections&&I.sections.length>0?e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Sections"}),e.jsx("ul",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg divide-y ${s?"divide-gray-800":"divide-gray-200"}`,children:I.sections.map((g,x)=>e.jsx("li",{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:`w-8 h-8 flex items-center justify-center rounded-full mr-3 ${x<r||h?"bg-[#88cc14] text-white":x===r?s?"bg-blue-600 text-white":"bg-blue-500 text-white":s?"bg-gray-800 text-gray-400":"bg-gray-200 text-gray-600"}`,children:x+1}),e.jsx("span",{className:"font-medium",children:g})]})},x))})]}):e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"No sections available for this module."})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-between items-center",children:[h?e.jsxs("div",{className:"flex items-center text-[#88cc14]",children:[e.jsx(z,{className:"mr-2"})," Module Completed"]}):e.jsxs("button",{onClick:()=>M(100,!0),className:"w-full sm:w-auto px-6 py-3 theme-button-primary rounded-lg flex items-center justify-center",children:[e.jsx(z,{className:"mr-2"})," Mark as Completed"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"mr-2",children:"Rate:"}),e.jsx("div",{className:"flex",children:[1,2,3,4,5].map(g=>e.jsx("button",{type:"button",onClick:()=>q(g),onMouseEnter:()=>b(g),onMouseLeave:()=>b(0),className:"text-2xl focus:outline-none",children:e.jsx(le,{className:`${(t||$)>=g?"text-yellow-400":s?"text-gray-700":"text-gray-300"}`})},g))})]})]}),$>0&&e.jsxs("div",{className:"mt-4",children:[e.jsx("textarea",{value:p,onChange:g=>R(g.target.value),placeholder:"Share your feedback about this module (optional)",className:`w-full p-3 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,rows:3}),e.jsx("button",{onClick:G,disabled:l,className:"mt-2 px-4 py-2 theme-button-primary rounded-lg",children:l?e.jsxs(e.Fragment,{children:[e.jsx(Q,{className:"inline animate-spin mr-2"})," Submitting..."]}):"Submit Rating"})]})]})]})},ge=()=>{const{darkMode:s}=V(),{moduleId:m}=Z();return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs(oe,{children:[e.jsx("h1",{className:"text-3xl font-bold mb-6",children:m?"Learning Module":"Learning Modules"}),m?e.jsx(ce,{}):e.jsx(de,{})]})})})};export{ge as default};
