import{r as n,j as s}from"./index-c6UceSOv.js";function v({commands:l=[],onCommandComplete:u}){const[c,x]=n.useState(""),[y,a]=n.useState([]),[o,f]=n.useState(0),[p,m]=n.useState(!1),g=n.useRef(null),d=n.useRef(null);n.useEffect(()=>{var e;(e=d.current)==null||e.scrollIntoView({behavior:"smooth"})},[y]),n.useEffect(()=>{a([{type:"system",text:"=== XCerberus OS Lab Terminal ==="},{type:"system",text:'Type "help" for available commands'},{type:"system",text:""}]),l.length>0&&a(e=>[...e,{type:"system",text:`Current task: ${l[0].description}`}])},[l]);const h=e=>{if(e.preventDefault(),!c.trim()||p)return;const i=c.trim();a(r=>[...r,{type:"input",text:`$ ${i}`}]),x(""),m(!0),setTimeout(()=>{if(i==="help")a(r=>[...r,{type:"system",text:"Available commands:"},{type:"system",text:"  help     - Show this help message"},{type:"system",text:"  clear    - Clear terminal screen"},{type:"system",text:""},{type:"system",text:"Task-specific commands:"},...l.map(t=>({type:"system",text:`  ${t.command}  - ${t.description}`}))]);else if(i==="clear")a([]);else{const r=l[o];r&&i===r.command?(a(t=>[...t,{type:"success",text:"✓ Command successful!"},{type:"output",text:r.expectedOutput||"Command executed successfully."}]),u==null||u(r),o<l.length-1?setTimeout(()=>{f(t=>t+1),a(t=>[...t,{type:"system",text:""},{type:"system",text:`Next task: ${l[o+1].description}`}])},1e3):a(t=>[...t,{type:"success",text:"🎉 All tasks completed! Congratulations!"}])):(a(t=>[...t,{type:"error",text:"Command not recognized or incorrect for current task."}]),r!=null&&r.hint&&a(t=>[...t,{type:"hint",text:`Hint: ${r.hint}`}]))}m(!1)},500)};return s.jsxs("div",{className:"bg-black rounded-lg overflow-hidden border border-gray-800",children:[s.jsx("div",{className:"p-3 border-b border-gray-800 bg-gray-900",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-3 h-3 rounded-full bg-red-500"}),s.jsx("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),s.jsx("div",{className:"w-3 h-3 rounded-full bg-green-500"}),s.jsx("span",{className:"ml-2 text-gray-400 text-sm",children:"XCerberus@terminal:~"})]})}),s.jsxs("div",{ref:g,className:"h-96 p-4 font-mono text-sm overflow-y-auto bg-black text-gray-300",children:[y.map((e,i)=>s.jsx("div",{className:`mb-2 ${e.type==="input"||e.type==="success"?"text-green-400":e.type==="error"?"text-red-400":e.type==="hint"?"text-yellow-400":e.type==="output"?"text-blue-300":"text-gray-300"}`,children:e.text},i)),p&&s.jsx("div",{className:"text-green-400 animate-pulse",children:"_"}),s.jsx("div",{ref:d}),s.jsxs("form",{onSubmit:h,className:"mt-2 flex items-center",children:[s.jsx("span",{className:"text-green-400 mr-2",children:"$"}),s.jsx("input",{type:"text",value:c,onChange:e=>x(e.target.value),className:"flex-1 bg-transparent border-none outline-none text-green-400",disabled:p,autoFocus:!0})]})]})]})}export{v as L};
