import{bb as b,H as l,bc as ne,aw as N,bd as q,k as f,x as E,be as j,bf as F,d,bg as H,bh as T,bi as ae,bj as R,y as w,bk as V,bl as re,E as p,ab as u,aV as h,ad as $,aM as K,F as ce,ai as D,ae as c,T as I,bm as Q,v as x,bn as le,G as S,bo as G,bp as _,h as Z,aK as me,bq as de,br as pe,bs as A,an as C,bt as ue,ap as J,I as k,bu as he,t as ge,bv as ye,bw as L,aa as fe,bx as ve,z as be,by as P,bz as we,ac as g,j as e,m as v,b as xe,r as M,n as X,M as Se,bA as Ce}from"./index-c6UceSOv.js";const Ne=[{id:"fundamentals",title:"Fundamentals",icon:b,modules:[{id:"os-concepts",title:"Operating System Concepts",icon:b,topics:[{id:"os-introduction",title:"Introduction to Operating Systems",icon:b},{id:"process-management",title:"Process Management",icon:l},{id:"memory-management",title:"Memory Management",icon:ne},{id:"file-systems",title:"File Systems",icon:N},{id:"io-management",title:"I/O Management",icon:q},{id:"cpu-scheduling",title:"CPU Scheduling",icon:f},{id:"deadlocks",title:"Deadlocks",icon:E},{id:"synchronization",title:"Process Synchronization",icon:j},{id:"secondary-storage",title:"Secondary Storage Management",icon:F},{id:"protection-security",title:"Protection and Security",icon:d},{id:"distributed-os",title:"Distributed Operating Systems",icon:l},{id:"real-time-os",title:"Real-Time Operating Systems",icon:f},{id:"virtualization",title:"Virtualization",icon:H},{id:"kernel-operations",title:"Kernel Operations",icon:T},{id:"system-calls",title:"System Calls",icon:ae},{id:"os-interview",title:"OS Interview Q&A",icon:R}]},{id:"networking",title:"Networking Fundamentals",icon:l,topics:[{id:"networking-basics",title:"Networking Basics",icon:V,topics:[{id:"network-models",title:"Network Models",icon:T},{id:"network-devices",title:"Network Devices",icon:w},{id:"network-addressing",title:"Network Addressing",icon:l},{id:"network-media",title:"Network Media",icon:V}]},{id:"tcp-ip",title:"TCP/IP Protocol Suite",icon:l,topics:[{id:"ip-protocol",title:"IP Protocol",icon:l},{id:"tcp-udp",title:"TCP & UDP",icon:q},{id:"dns-dhcp",title:"DNS & DHCP",icon:w},{id:"routing-protocols",title:"Routing Protocols",icon:re}]},{id:"network-security",title:"Network Security",icon:d,topics:[{id:"security-basics",title:"Security Fundamentals",icon:p},{id:"encryption-protocols",title:"Encryption Protocols",icon:d},{id:"network-threats",title:"Network Threats",icon:u},{id:"security-tools",title:"Security Tools",icon:h}]},{id:"wireless-networking",title:"Wireless Networking",icon:$,topics:[{id:"wireless-standards",title:"Wireless Standards",icon:$},{id:"wireless-security",title:"Wireless Security",icon:d},{id:"wireless-troubleshooting",title:"Troubleshooting",icon:h}]},{id:"network-services",title:"Network Services",icon:w,topics:[{id:"web-services",title:"Web Services",icon:K},{id:"email-services",title:"Email Services",icon:ce},{id:"file-services",title:"File Services",icon:N}]},{id:"network-management",title:"Network Management",icon:I,topics:[{id:"monitoring",title:"Network Monitoring",icon:D},{id:"troubleshooting",title:"Troubleshooting",icon:h},{id:"documentation",title:"Documentation",icon:c}]},{id:"network-interview",title:"Network Interview Q&A",icon:R}]},{id:"linux",title:"Linux Essentials",icon:Q,topics:[{id:"linux-basics",title:"Linux Basics",icon:x,topics:[{id:"linux-intro",title:"Introduction to Linux",icon:Q},{id:"command-line",title:"Command Line Interface",icon:x},{id:"file-system",title:"File System Hierarchy",icon:le},{id:"basic-commands",title:"Basic Commands",icon:S}]},{id:"shell-scripting",title:"Shell Scripting",icon:x,topics:[{id:"bash-basics",title:"Bash Basics",icon:S},{id:"variables-control",title:"Variables & Control Flow",icon:G},{id:"functions-arrays",title:"Functions & Arrays",icon:_},{id:"script-debugging",title:"Script Debugging",icon:u}]},{id:"system-administration",title:"System Administration",icon:de,topics:[{id:"user-management",title:"User Management",icon:Z},{id:"permissions",title:"Permissions & Access Control",icon:me},{id:"process-management",title:"Process Management",icon:G},{id:"service-management",title:"Service Management",icon:I}]},{id:"package-management",title:"Package Management",icon:_,topics:[{id:"package-basics",title:"Package Management Basics",icon:pe},{id:"apt-yum",title:"APT & YUM",icon:H},{id:"source-builds",title:"Building from Source",icon:A}]},{id:"networking-tools",title:"Networking Tools",icon:l,topics:[{id:"network-config",title:"Network Configuration",icon:I},{id:"network-tools",title:"Network Utilities",icon:h},{id:"remote-access",title:"Remote Access",icon:x}]},{id:"system-monitoring",title:"System Monitoring",icon:D,topics:[{id:"performance-tools",title:"Performance Tools",icon:C},{id:"log-management",title:"Log Management",icon:ue},{id:"monitoring-tools",title:"Monitoring Tools",icon:D}]},{id:"security-hardening",title:"Security Hardening",icon:p,topics:[{id:"security-basics",title:"Security Basics",icon:d},{id:"firewall-config",title:"Firewall Configuration",icon:p},{id:"security-tools",title:"Security Tools",icon:h}]},{id:"storage-management",title:"Storage Management",icon:F,topics:[{id:"disk-management",title:"Disk Management",icon:F},{id:"lvm",title:"Logical Volume Management",icon:T},{id:"raid",title:"RAID Configuration",icon:J}]},{id:"troubleshooting",title:"Troubleshooting",icon:he,topics:[{id:"system-recovery",title:"System Recovery",icon:j},{id:"diagnostics",title:"Diagnostic Tools",icon:k},{id:"common-issues",title:"Common Issues",icon:E}]},{id:"automation",title:"Automation & Scheduling",icon:f,topics:[{id:"cron-jobs",title:"Cron Jobs",icon:f},{id:"automation-tools",title:"Automation Tools",icon:ge},{id:"task-scheduling",title:"Task Scheduling",icon:ye}]},{id:"linux-interview",title:"Linux Interview Q&A",icon:R}]}]},{id:"offensive-security",title:"Offensive Security",icon:u,modules:[{id:"penetration-testing",title:"Penetration Testing",icon:A,topics:[{id:"recon",title:"Information Gathering",icon:k},{id:"vulnerability-assessment",title:"Vulnerability Assessment",icon:u},{id:"exploitation",title:"Exploitation",icon:h}]},{id:"web-security",title:"Web Security",icon:S,topics:[{id:"web-vulnerabilities",title:"Common Web Vulnerabilities",icon:u},{id:"web-exploitation",title:"Web Exploitation",icon:A},{id:"secure-coding",title:"Secure Coding Practices",icon:S}]},{id:"mobile-security",title:"Mobile Security",icon:L,topics:[{id:"android-security",title:"Android Security",icon:L},{id:"ios-security",title:"iOS Security",icon:L}]}]},{id:"defensive-security",title:"Defensive Security",icon:p,modules:[{id:"incident-response",title:"Incident Response",icon:ve,topics:[{id:"incident-handling",title:"Incident Handling",icon:p},{id:"digital-forensics",title:"Digital Forensics",icon:k},{id:"malware-analysis",title:"Malware Analysis",icon:fe}]},{id:"security-operations",title:"Security Operations",icon:be,topics:[{id:"siem",title:"SIEM",icon:C},{id:"threat-hunting",title:"Threat Hunting",icon:k},{id:"security-monitoring",title:"Security Monitoring",icon:b}]}]},{id:"cryptography",title:"Cryptography",icon:P,modules:[{id:"crypto-fundamentals",title:"Cryptography Fundamentals",icon:P,topics:[{id:"symmetric-crypto",title:"Symmetric Cryptography",icon:P},{id:"asymmetric-crypto",title:"Asymmetric Cryptography",icon:P},{id:"hashing",title:"Hashing and Digital Signatures",icon:we}]},{id:"crypto-applications",title:"Cryptography Applications",icon:d,topics:[{id:"tls-ssl",title:"TLS/SSL",icon:d},{id:"vpn-crypto",title:"VPN Technologies",icon:K},{id:"blockchain",title:"Blockchain Technology",icon:J}]}]},{id:"cloud-security",title:"Cloud Security",icon:g,modules:[{id:"cloud-concepts",title:"Cloud Security Concepts",icon:g,topics:[{id:"cloud-models",title:"Cloud Service Models",icon:g},{id:"cloud-threats",title:"Cloud Security Threats",icon:u},{id:"cloud-controls",title:"Security Controls",icon:p}]},{id:"cloud-platforms",title:"Cloud Platforms",icon:w,topics:[{id:"aws-security",title:"AWS Security",icon:g},{id:"azure-security",title:"Azure Security",icon:g},{id:"gcp-security",title:"GCP Security",icon:g}]}]},{id:"governance",title:"Security Governance",icon:c,modules:[{id:"risk-management",title:"Risk Management",icon:C,topics:[{id:"risk-assessment",title:"Risk Assessment",icon:C},{id:"compliance",title:"Compliance",icon:c},{id:"policies",title:"Security Policies",icon:c}]},{id:"security-frameworks",title:"Security Frameworks",icon:c,topics:[{id:"iso-27001",title:"ISO 27001",icon:c},{id:"nist",title:"NIST Framework",icon:c},{id:"pci-dss",title:"PCI DSS",icon:c}]}]}],ke={title:"Introduction to Operating Systems",description:"Learn the fundamental concepts of operating systems through interactive simulations and hands-on exercises.",sections:[{title:"What is an Operating System?",content:`An operating system (OS) is the most important software that runs on a computer. It manages the computer's memory, processes, and all of its software and hardware.

Key responsibilities include:
1. Process Management
   • Controls and allocates processes
   • Schedules tasks
   • Handles process synchronization

2. Memory Management
   • Allocates memory to programs
   • Manages virtual memory
   • Handles memory protection

3. File System Management
   • Organizes and maintains files
   • Controls access permissions
   • Manages storage devices

4. I/O Management
   • Handles input/output operations
   • Manages device drivers
   • Controls peripheral devices

5. Security and Protection
   • User authentication
   • Access control
   • Resource protection`,visualization:{type:"interactive",component:"OSLayersVisualization",data:{layers:[{name:"Applications",icon:"FaDesktop",color:"#88cc14"},{name:"User Interface",icon:"FaCode",color:"#00f3ff"},{name:"Kernel",icon:"FaMicrochip",color:"#ff4757"}]}}},{title:"Types of Operating Systems",content:`Operating systems come in various types, each designed for specific use cases:

1. Single-User, Single-Task
   • Example: MS-DOS
   • One user can run one program at a time
   • Simple and straightforward design
   • Limited multitasking capabilities

2. Single-User, Multi-Task
   • Example: Windows, macOS
   • One user can run multiple programs
   • Supports background processes
   • Full multitasking support

3. Multi-User
   • Example: Unix, Linux
   • Multiple users access simultaneously
   • Advanced security features
   • Resource sharing capabilities

4. Real-Time OS
   • Example: QNX, VxWorks
   • Used in critical systems
   • Guaranteed response times
   • Deterministic behavior

5. Distributed OS
   • Example: Amoeba
   • Manages networked computers
   • Appears as single system
   • Transparent resource sharing`,visualization:{type:"interactive",component:"OSTypesVisualization",data:{types:[{name:"Single-User",icon:"FaUser"},{name:"Multi-User",icon:"FaUsers"},{name:"Real-Time",icon:"FaClock"},{name:"Distributed",icon:"FaNetwork"}]}}}],practicalLab:{title:"OS Command Line Interface Lab",description:"Master essential Linux commands through hands-on practice in a simulated environment.",tasks:[{category:"Basic Navigation",commands:[{command:"pwd",description:"Print current working directory",hint:"Use pwd to show your current location in the filesystem",expectedOutput:`/home/<USER>
This is your home directory.`},{command:"ls -la",description:"List all files including hidden ones",hint:"The -l flag shows detailed information, -a shows hidden files",expectedOutput:`total 32
drwxr-xr-x 4 <USER> <GROUP> 4096 Mar 19 10:00 .
drwxr-xr-x 3 <USER> <GROUP> 4096 Mar 19 10:00 ..
-rw------- 1 <USER> <GROUP>  220 Mar 19 10:00 .bash_history`},{command:"cd /etc",description:"Change to system configuration directory",hint:"Use cd to change directories",expectedOutput:`Changed to /etc directory
This directory contains system configuration files.`}]},{category:"File Operations",commands:[{command:"touch test.txt",description:"Create a new empty file",hint:"touch creates a new empty file or updates timestamps of existing files",expectedOutput:"Created file: test.txt"},{command:"mkdir projects",description:"Create a new directory",hint:"mkdir is used to create new directories",expectedOutput:"Created directory: projects"},{command:"cp test.txt projects/",description:"Copy file to projects directory",hint:"cp copies files or directories",expectedOutput:"Copied test.txt to projects/"}]},{category:"System Information",commands:[{command:"uname -a",description:"Display system information",hint:"uname shows system information, -a shows all info",expectedOutput:"Linux xcerberus 5.15.0-generic #1 SMP PREEMPT_DYNAMIC Thu Mar 14 10:00:00 UTC 2024 x86_64 GNU/Linux"},{command:"free -h",description:"Show memory usage",hint:"free shows memory usage, -h shows human-readable sizes",expectedOutput:`              total        used        free      shared  buff/cache   available
Mem:           15Gi       2.1Gi       8.2Gi       1.2Gi       4.7Gi        11Gi
Swap:         8.0Gi          0B       8.0Gi`}]}]},conceptualQuiz:[{question:"What is the primary function of an operating system?",options:["To provide a user interface","To manage hardware and software resources","To run applications","To connect to the internet"],correct:1,explanation:"The primary function of an OS is to manage hardware and software resources, ensuring efficient operation of the computer system."},{question:"Which type of operating system is best suited for servers?",options:["Single-User, Single-Task","Single-User, Multi-Task","Multi-User","Real-Time OS"],correct:2,explanation:"Multi-User operating systems are ideal for servers as they allow multiple users to access resources simultaneously and provide better security features."},{question:"What is a process in an operating system?",options:["A program stored on the disk","A program in execution","A file containing code","A system configuration"],correct:1,explanation:"A process is a program in execution, which includes the program code, its current activity, and resource allocations."},{question:"Which component of an OS handles memory allocation?",options:["Process Manager","Memory Manager","File System","I/O Manager"],correct:1,explanation:"The Memory Manager is responsible for allocating and deallocating memory space to processes and managing virtual memory."},{question:"What is the purpose of a system call?",options:["To run user applications","To interface between user programs and OS services","To manage hardware directly","To create new processes"],correct:1,explanation:"System calls provide an interface between user programs and OS services, allowing programs to request services from the operating system safely."}]},Pe={title:"Process Management",description:"Learn how operating systems manage and control processes, including scheduling and synchronization.",sections:[{title:"Process Concepts",content:`A process is an instance of a program in execution. It is the basic unit of work in an operating system. Each process has its own memory space, system resources, and security context.

Key components of a process include:
- Program Counter: Indicates the address of the next instruction
- Process State: Current state of the process (new, ready, running, etc.)
- Memory Boundaries: Memory allocated to the process
- List of Open Files: Files currently being accessed
- List of Related Processes: Parent and child processes`,visualization:{type:"interactive",component:"ProcessVisualization",data:{states:[{name:"New",color:"#88cc14"},{name:"Ready",color:"#00f3ff"},{name:"Running",color:"#ff4757"},{name:"Waiting",color:"#ffd32a"},{name:"Terminated",color:"#747d8c"}]}}},{title:"Process States",content:`A process goes through various states during its lifecycle:

1. New: Process is being created
2. Ready: Process is waiting to be assigned to a processor
3. Running: Instructions are being executed
4. Waiting: Process is waiting for some event to occur
5. Terminated: Process has finished execution

State transitions occur based on:
- Scheduler decisions
- I/O operations
- Resource availability
- Process completion`},{title:"Process Control Block (PCB)",content:`The Process Control Block (PCB) is a data structure that contains all the information about a process. It's created when a process is created and is maintained throughout its lifetime.

PCB contains:
- Process ID (PID)
- Program Counter
- CPU Registers
- CPU Scheduling Information
- Memory Management Information
- Accounting Information
- I/O Status Information

The PCB is essential for:
- Context Switching
- Process Management
- Resource Tracking
- Process Synchronization`},{title:"Context Switching",content:`Context switching is the process of saving the state of a running process and restoring the state of a different process when switching between them.

Steps involved:
1. Save the context of current process in its PCB
2. Update relevant process control block pointers
3. Move PCB to appropriate queue (ready/blocked)
4. Select another process for execution
5. Update memory management structures
6. Restore context of the new process

Context switching overhead includes:
- Saving/restoring register values
- Switching memory maps
- Updating various tables and lists
- Flushing various caches`},{title:"Process Scheduling",content:`Process scheduling is the activity of selecting which process runs on the CPU at any given time. The main objectives are:

- CPU Utilization: Keep the CPU as busy as possible
- Throughput: Maximize number of processes completed per time unit
- Turnaround Time: Minimize time between submission and completion
- Waiting Time: Minimize time in ready queue
- Response Time: Minimize time until first response

Common Scheduling Algorithms:
1. First-Come, First-Served (FCFS)
2. Shortest Job First (SJF)
3. Priority Scheduling
4. Round Robin
5. Multilevel Queue
6. Multilevel Feedback Queue`},{title:"CPU Scheduling Algorithms",content:`Detailed look at major scheduling algorithms:

1. First-Come, First-Served (FCFS):
   - Simplest scheduling algorithm
   - Non-preemptive
   - Can lead to "convoy effect"

2. Shortest Job First (SJF):
   - Optimal for minimizing average waiting time
   - Can be preemptive or non-preemptive
   - Requires prediction of burst time

3. Priority Scheduling:
   - Assigns priority to each process
   - Can lead to starvation
   - Often used with aging mechanism

4. Round Robin:
   - Time quantum based
   - Fair allocation of CPU
   - Performance depends on quantum size

5. Multilevel Queue:
   - Processes assigned to different queues
   - Each queue has its own scheduling algorithm
   - No movement between queues

6. Multilevel Feedback Queue:
   - Allows processes to move between queues
   - Adapts to process behavior
   - Most sophisticated and flexible`}],practicalLab:{title:"Process Management Lab",description:"Practice process management commands and understand process states",tasks:[{category:"Basic Process Commands",commands:[{command:"ps",description:"Display current processes",expectedOutput:"List of running processes with basic information"},{command:"ps aux",description:"Show detailed process information for all users",expectedOutput:"Detailed list of all processes including CPU and memory usage"},{command:"pstree",description:"Display process tree",expectedOutput:"Tree view showing process hierarchy"},{command:"top",description:"Show real-time process information",expectedOutput:"Interactive process viewer showing system activity"}]},{category:"Process Control",commands:[{command:"kill -l",description:"List all available signals",expectedOutput:"List of signal numbers and names"},{command:"kill PID",description:"Send SIGTERM to process",expectedOutput:"Process terminated gracefully"},{command:"kill -9 PID",description:"Force kill process (SIGKILL)",expectedOutput:"Process terminated immediately"},{command:"killall process_name",description:"Kill all processes by name",expectedOutput:"All matching processes terminated"}]},{category:"Process Monitoring",commands:[{command:"nice -n 10 command",description:"Start process with custom priority",expectedOutput:"Process started with adjusted nice value"},{command:"renice +5 PID",description:"Change process priority",expectedOutput:"Process priority adjusted"},{command:"pidof process_name",description:"Find process ID by name",expectedOutput:"PID of the specified process"},{command:"pgrep process_name",description:"Search processes by name",expectedOutput:"List of matching process IDs"}]},{category:"Process Analysis",commands:[{command:"strace command",description:"Trace system calls and signals",expectedOutput:"System calls made by the process"},{command:"lsof -p PID",description:"List open files for process",expectedOutput:"Files opened by the specified process"},{command:"time command",description:"Measure process execution time",expectedOutput:"Real, user, and system time statistics"},{command:"watch command",description:"Execute command periodically",expectedOutput:"Command output updated every 2 seconds"}]},{category:"Background Processes",commands:[{command:"command &",description:"Start process in background",expectedOutput:"Process started with job number"},{command:"jobs",description:"List background jobs",expectedOutput:"List of background processes"},{command:"fg %job_number",description:"Bring process to foreground",expectedOutput:"Process brought to foreground"},{command:"bg %job_number",description:"Resume process in background",expectedOutput:"Process resumed in background"}]}]},conceptualQuiz:[{question:"What is a process control block (PCB)?",options:["A block of memory allocated to a process","A data structure containing process information","A system call to control processes","A type of process scheduling algorithm"],correct:1,explanation:"A Process Control Block (PCB) is a data structure that contains important information about a specific process, including its state, program counter, CPU registers, and memory management information."},{question:"Which state is a process in when it's currently executing?",options:["Ready","Running","Waiting","New"],correct:1,explanation:"A process is in the Running state when it is currently being executed by the CPU."},{question:"What happens during context switching?",options:["A new process is created","A process is terminated","The CPU switches from one process to another","Memory is allocated to a process"],correct:2,explanation:"Context switching occurs when the CPU switches from executing one process to another, saving the current process's state and loading the new process's state."},{question:"Which scheduling algorithm is optimal for minimizing average waiting time?",options:["First-Come, First-Served","Shortest Job First","Round Robin","Priority Scheduling"],correct:1,explanation:"Shortest Job First (SJF) is theoretically optimal for minimizing average waiting time, though it requires knowing or predicting process burst times."},{question:"What is the main disadvantage of priority scheduling?",options:["High overhead","Process starvation","Low throughput","Complex implementation"],correct:1,explanation:"Priority scheduling can lead to process starvation, where lower-priority processes may never execute if there are always higher-priority processes ready to run."}]},Me={title:"Memory Management",description:"Learn how operating systems manage memory allocation, virtual memory, paging, and memory protection.",sections:[{title:"Memory Hierarchy",content:`Memory in a computer system is organized in a hierarchical structure, with different levels offering various trade-offs between speed, cost, and capacity.

The memory hierarchy consists of:

1. Registers
   - Fastest memory
   - Located in CPU
   - Very small capacity
   - Used for immediate data processing

2. Cache Memory (L1, L2, L3)
   - Very fast access time
   - Small capacity
   - Bridges gap between registers and main memory
   - Managed automatically by hardware

3. Main Memory (RAM)
   - Moderate access time
   - Large capacity
   - Directly accessible by CPU
   - Volatile storage

4. Virtual Memory
   - Extension of RAM using disk space
   - Very large capacity
   - Slower access time
   - Managed by OS memory manager`,visualization:{type:"interactive",component:"MemoryHierarchyVisualization",data:{levels:[{name:"Registers",speed:"Fastest",size:"Bytes",accessTime:"< 1ns"},{name:"Cache",speed:"Very Fast",size:"KB-MB",accessTime:"2-10ns"},{name:"Main Memory",speed:"Fast",size:"GB",accessTime:"50-100ns"},{name:"Virtual Memory",speed:"Slow",size:"TB",accessTime:"5-10ms"}]}}},{title:"Virtual Memory",content:`Virtual memory is a memory management technique that provides an idealized abstraction of the storage resources that are actually available on a given machine.

Key concepts of virtual memory:

1. Address Translation
   - Virtual addresses mapped to physical addresses
   - Translation done by Memory Management Unit (MMU)
   - Page tables maintain mapping information

2. Benefits
   - Programs can use more memory than physically available
   - Memory isolation between processes
   - Shared memory implementation
   - More efficient memory utilization

3. Implementation
   - Demand paging
   - Page replacement algorithms
   - Memory protection
   - Shared pages`,visualization:{type:"interactive",component:"VirtualMemoryVisualization",data:{virtualAddress:"0x1234",pageTable:[{virtualPage:0,physicalPage:2,valid:!0},{virtualPage:1,physicalPage:4,valid:!0},{virtualPage:2,physicalPage:1,valid:!1}]}}},{title:"Paging",content:`Paging is a memory management scheme that eliminates the need for contiguous allocation of physical memory.

Key aspects of paging:

1. Page Structure
   - Virtual memory divided into fixed-size pages
   - Physical memory divided into frames
   - Pages mapped to frames

2. Page Table
   - Maintains mapping between pages and frames
   - Contains page table entries (PTEs)
   - Includes status bits (valid, dirty, referenced)

3. Translation Lookaside Buffer (TLB)
   - Cache for page table entries
   - Speeds up virtual-to-physical address translation
   - Managed by MMU

4. Multi-level Paging
   - Reduces page table size
   - Hierarchical page tables
   - Common in modern systems`,visualization:{type:"interactive",component:"PagingVisualization",data:{pageSize:4096,pages:[{id:0,status:"mapped",frame:2},{id:1,status:"swapped",frame:null},{id:2,status:"mapped",frame:5}]}}},{title:"Page Replacement Algorithms",content:`When memory is full and a new page needs to be loaded, the operating system must choose which page to remove from memory.

Common page replacement algorithms:

1. First-In-First-Out (FIFO)
   - Replaces oldest page in memory
   - Simple to implement
   - Not always effective

2. Least Recently Used (LRU)
   - Replaces page that hasn't been used for longest time
   - Very effective but expensive to implement
   - Requires tracking of page access times

3. Clock Algorithm
   - Approximation of LRU
   - Uses reference bit
   - Good balance of simplicity and effectiveness

4. Optimal Algorithm
   - Replaces page that won't be used for longest time
   - Theoretical benchmark
   - Impossible to implement in practice`,visualization:{type:"interactive",component:"PageReplacementVisualization",data:{algorithms:["FIFO","LRU","Clock","Optimal"],pageReferences:[1,2,3,4,1,2,5,1,2,3,4,5]}}}],practicalLab:{title:"Memory Management Lab",description:"Practice memory management commands and monitor system memory usage",tasks:[{category:"Memory Information",commands:[{command:"free -h",description:"Display memory usage in human-readable format",expectedOutput:"Memory usage statistics including total, used, and available memory"},{command:"vmstat",description:"Virtual memory statistics",expectedOutput:"Detailed virtual memory statistics"},{command:"cat /proc/meminfo",description:"Detailed memory information",expectedOutput:"Comprehensive memory information from the proc filesystem"},{command:"swapon --show",description:"Show swap space usage",expectedOutput:"Information about swap devices and their usage"}]},{category:"Process Memory",commands:[{command:"pmap PID",description:"Display process memory map",expectedOutput:"Memory map of a specific process"},{command:"top -o %MEM",description:"Show processes sorted by memory usage",expectedOutput:"Process list sorted by memory consumption"},{command:"ps aux --sort=-%mem",description:"List processes by memory usage",expectedOutput:"Detailed process information sorted by memory usage"},{command:"smem -tk",description:"Show processes' memory usage details",expectedOutput:"Detailed memory usage per process"}]},{category:"Memory Limits",commands:[{command:"ulimit -a",description:"Show all system limits",expectedOutput:"Current system resource limits"},{command:"ulimit -v",description:"Show virtual memory limit",expectedOutput:"Maximum virtual memory size"},{command:"cat /proc/sys/vm/swappiness",description:"Check swappiness value",expectedOutput:"Current swappiness parameter value"},{command:"cat /proc/sys/vm/overcommit_memory",description:"Check memory overcommit settings",expectedOutput:"Current memory overcommit policy"}]},{category:"Cache Management",commands:[{command:"sync",description:"Synchronize cached writes to disk",expectedOutput:"Flushes file system buffers"},{command:"echo 3 > /proc/sys/vm/drop_caches",description:"Clear system caches",expectedOutput:"Permission denied (needs root)"},{command:"sysctl vm.drop_caches",description:"Check cache drop settings",expectedOutput:"Current cache drop parameter value"},{command:"vmtouch -v file",description:"Show file in cache status",expectedOutput:"File's presence in page cache"}]}]},conceptualQuiz:[{question:"What is virtual memory?",options:["Physical memory installed in the computer","A memory management technique that uses disk space as an extension of RAM","A type of cache memory","The total amount of RAM available"],correct:1,explanation:"Virtual memory is a memory management technique that uses disk space to extend the available RAM, allowing programs to use more memory than physically available."},{question:"What is a page fault?",options:["A hardware memory error","When a program crashes","When a requested page is not in main memory","When memory becomes corrupted"],correct:2,explanation:"A page fault occurs when a program tries to access a page that is mapped in virtual memory but not currently loaded in physical memory."},{question:"Which page replacement algorithm is considered optimal but impossible to implement in practice?",options:["FIFO (First-In-First-Out)","LRU (Least Recently Used)","The Optimal Algorithm","Clock Algorithm"],correct:2,explanation:"The Optimal Algorithm is theoretically perfect as it replaces the page that won't be used for the longest time in the future, but it requires knowledge of future page references, making it impossible to implement in practice."},{question:"What is the purpose of the Translation Lookaside Buffer (TLB)?",options:["To store frequently used data","To cache virtual to physical address translations","To manage disk space","To compress memory pages"],correct:1,explanation:"The TLB is a cache that stores recent virtual to physical address translations to speed up memory access by reducing the need to access the page table in main memory."},{question:"What happens during thrashing?",options:["The CPU overheats","The system spends more time paging than executing","Memory becomes corrupted","The hard drive fails"],correct:1,explanation:"Thrashing occurs when the system spends more time moving pages between disk and memory (paging) than executing actual program instructions, severely degrading performance."}]},Oe={title:"File Systems",description:"Understand how operating systems manage files, directories, and storage.",sections:[{title:"File System Structure",content:`The Linux file system follows a hierarchical tree structure, with each directory having a specific purpose:

┌── / (Root Directory)
├── /bin     - Essential user binaries
├── /boot    - Boot loader files
├── /dev     - Device files
├── /etc     - System configuration
├── /home    - User home directories
├── /lib     - System libraries
├── /media   - Removable media
├── /mnt     - Mount point
├── /opt     - Optional software
├── /proc    - Process information
├── /root    - Root user home
├── /sbin    - System binaries
├── /tmp     - Temporary files
├── /usr     - User programs
└── /var     - Variable files

Each directory serves a specific purpose:

• /bin: Contains essential command binaries
• /boot: Contains bootloader files and kernels
• /dev: Contains device files for hardware
• /etc: Contains system-wide configuration
• /home: Contains user home directories
• /lib: Contains shared system libraries
• /media: Mount point for removable media
• /mnt: Mount point for temporary filesystems
• /opt: Contains optional/add-on software
• /proc: Virtual filesystem for process info
• /root: Home directory for root user
• /sbin: Contains system administration binaries
• /tmp: Contains temporary files
• /usr: Contains user programs and data
• /var: Contains variable data files`,visualization:{type:"interactive",component:"FileSystemVisualization",data:{structure:"tree",nodes:[{name:"/",type:"dir",description:"Root Directory"},{name:"/bin",type:"dir",description:"Essential user binaries"},{name:"/boot",type:"dir",description:"Boot loader files"},{name:"/dev",type:"dir",description:"Device files"},{name:"/etc",type:"dir",description:"System configuration"},{name:"/home",type:"dir",description:"User home directories"},{name:"/lib",type:"dir",description:"System libraries"},{name:"/media",type:"dir",description:"Removable media"},{name:"/mnt",type:"dir",description:"Mount point"},{name:"/opt",type:"dir",description:"Optional software"},{name:"/proc",type:"dir",description:"Process information"},{name:"/root",type:"dir",description:"Root user home"},{name:"/sbin",type:"dir",description:"System binaries"},{name:"/tmp",type:"dir",description:"Temporary files"},{name:"/usr",type:"dir",description:"User programs"},{name:"/var",type:"dir",description:"Variable files"}]}}},{title:"File Types",content:`Linux supports several types of files:

1. Regular Files (-)
   • Text files
   • Binary files
   • Data files
   • Shell scripts

2. Directories (d)
   • Containers for other files
   • Can contain subdirectories
   • Special entries: . (current) and .. (parent)

3. Links
   • Symbolic links (l): Point to another file
   • Hard links: Direct reference to file data

4. Special Files
   • Block devices (b): Storage devices
   • Character devices (c): Serial devices
   • Named pipes (p): Inter-process communication
   • Sockets (s): Network communication

File type is indicated by the first character in ls -l output:
-rw-r--r--  Regular file
drwxr-xr-x  Directory
lrwxrwxrwx  Symbolic link
brw-rw----  Block device
crw-rw----  Character device
srw-rw-rw-  Socket
prw-r--r--  Named pipe`,visualization:{type:"interactive",component:"FileTypesVisualization",data:{types:[{symbol:"-",name:"Regular File",example:"document.txt"},{symbol:"d",name:"Directory",example:"/home/<USER>"},{symbol:"l",name:"Symbolic Link",example:"link -> target"},{symbol:"b",name:"Block Device",example:"/dev/sda"},{symbol:"c",name:"Character Device",example:"/dev/tty"},{symbol:"s",name:"Socket",example:"/tmp/socket"},{symbol:"p",name:"Named Pipe",example:"/tmp/pipe"}]}}}],practicalLab:{title:"File System Operations Lab",description:"Practice file system operations and management",tasks:[{category:"Basic File Operations",commands:[{command:"ls -l",description:"List files with details",expectedOutput:"Detailed file listing with permissions"},{command:"touch file.txt",description:"Create empty file",expectedOutput:"File created"},{command:"mkdir dir",description:"Create directory",expectedOutput:"Directory created"},{command:"rm file.txt",description:"Remove file",expectedOutput:"File removed"}]},{category:"File Permissions",commands:[{command:"chmod 755 file",description:"Change file permissions",expectedOutput:"Permissions updated"},{command:"chown user:group file",description:"Change file ownership",expectedOutput:"Ownership changed"}]}]},conceptualQuiz:[{question:"What is the purpose of the /etc directory?",options:["Store temporary files","Store system configuration files","Store user home directories","Store program files"],correct:1,explanation:"The /etc directory contains system-wide configuration files and directories that are used by the operating system and installed applications."},{question:"Which directory contains essential command binaries?",options:["/sbin","/bin","/usr","/opt"],correct:1,explanation:"The /bin directory contains essential command binaries that need to be available in single user mode, including commands like ls, cp, and cat."}]},Fe={title:"I/O Management",description:"Learn how operating systems handle input/output operations and device management.",sections:[{title:"I/O Hardware",content:`I/O devices vary widely in their function and speed. Common types include:

1. Block Devices
   - Fixed size blocks
   - Random access
   - Example: disk drives

2. Character Devices
   - Stream of characters
   - Sequential access
   - Example: keyboards, mice

3. Network Devices
   - Stream of data packets
   - Example: network interfaces

Key components:
- Port: connection point for device
- Bus: data pathway
- Controller: electronics that operate port, bus, device`,visualization:{type:"interactive",component:"IOHardwareVisualization",data:{devices:[{type:"block",name:"Hard Drive",speed:"Medium"},{type:"character",name:"Keyboard",speed:"Slow"},{type:"network",name:"Network Card",speed:"Fast"}]}}},{title:"I/O Software",content:`I/O software is organized in layers:

1. User Level Software
   - Libraries
   - System calls
   - Spooling systems

2. Device Drivers
   - Device-specific code
   - Standardized interface
   - Hardware control

3. Interrupt Handlers
   - Save registers
   - Handle interrupt
   - Restore state

4. Device Controllers
   - Hardware that controls device
   - Buffers data
   - Simple instruction set`,visualization:{type:"interactive",component:"IOSoftwareVisualization",data:{layers:[{name:"User Level",color:"#88cc14"},{name:"Device Drivers",color:"#00f3ff"},{name:"Interrupt Handlers",color:"#ff4757"},{name:"Controllers",color:"#ffd32a"}]}}}],practicalLab:{title:"I/O Operations Lab",description:"Practice I/O operations and device management",tasks:[{category:"Device Information",commands:[{command:"lsblk",description:"List block devices",expectedOutput:"Block device hierarchy"},{command:"lspci",description:"List PCI devices",expectedOutput:"PCI device information"},{command:"lsusb",description:"List USB devices",expectedOutput:"USB device information"}]},{category:"I/O Monitoring",commands:[{command:"iostat",description:"Show I/O statistics",expectedOutput:"I/O device statistics"},{command:"iotop",description:"Monitor I/O usage",expectedOutput:"Process I/O usage"}]}]},conceptualQuiz:[{question:"What is the purpose of a device driver?",options:["To physically drive the device","To provide a standardized interface to the device","To store device data","To power the device"],correct:1,explanation:"A device driver provides a standardized interface between the operating system and a specific hardware device."},{question:"What is the difference between block and character devices?",options:["Block devices are faster","Character devices use more memory","Block devices transfer fixed-size blocks, character devices transfer byte streams","Character devices are more reliable"],correct:2,explanation:"Block devices transfer data in fixed-size blocks and allow random access, while character devices transfer data as streams of bytes sequentially."}]},Te={title:"CPU Scheduling",description:"Learn about CPU scheduling algorithms, process states, and scheduling criteria.",sections:[{title:"CPU Scheduling Basics",content:`CPU scheduling is the basis of multiprogrammed operating systems. By switching the CPU among processes, the operating system can make the computer more productive.

Key concepts include:

1. CPU-I/O Burst Cycle
   - Programs alternate between CPU and I/O bursts
   - CPU burst distribution is key to scheduling
   - Different patterns require different approaches

2. CPU Scheduler
   - Selects from processes in ready queue
   - Allocates CPU to selected process
   - Scheduling decisions happen when:
     * Process switches from running to waiting
     * Process terminates
     * New process arrives
     * Process switches from waiting to ready`,visualization:{type:"interactive",component:"ProcessStateVisualization",data:{states:[{name:"Running",color:"#88cc14"},{name:"Ready",color:"#00f3ff"},{name:"Waiting",color:"#ff4757"}]}}},{title:"Scheduling Criteria",content:`CPU scheduling algorithms are evaluated based on different criteria:

1. CPU Utilization
   - Keep CPU as busy as possible
   - Typically 40-90% for time-sharing systems

2. Throughput
   - Number of processes completed per time unit
   - Maximize process completion rate

3. Turnaround Time
   - Time from submission to completion
   - Includes execution and waiting time

4. Waiting Time
   - Time spent in ready queue
   - Minimize average waiting time

5. Response Time
   - Time from submission to first response
   - Critical for interactive systems`,visualization:{type:"interactive",component:"SchedulingMetricsVisualization",data:{metrics:[{name:"CPU Utilization",target:"90%"},{name:"Throughput",target:"Maximum"},{name:"Turnaround Time",target:"Minimum"},{name:"Waiting Time",target:"Minimum"},{name:"Response Time",target:"Minimum"}]}}},{title:"Scheduling Algorithms",content:`Common CPU scheduling algorithms include:

1. First-Come, First-Served (FCFS)
   - Simplest scheduling algorithm
   - Non-preemptive
   - Can lead to "convoy effect"

2. Shortest-Job-First (SJF)
   - Optimal for minimizing average waiting time
   - Can be preemptive or non-preemptive
   - Difficult to predict next CPU burst

3. Priority Scheduling
   - Each process assigned a priority
   - Higher priority processes run first
   - Can lead to starvation

4. Round Robin (RR)
   - Each process gets small unit of CPU time
   - Time quantum typically 10-100 milliseconds
   - Fair allocation but higher overhead

5. Multilevel Queue
   - Processes assigned to different queues
   - Each queue has its own scheduling algorithm
   - No movement between queues

6. Multilevel Feedback Queue
   - Similar to multilevel queue
   - Processes can move between queues
   - Most flexible but complex`}],practicalLab:{title:"CPU Scheduling Lab",description:"Practice implementing and analyzing different scheduling algorithms",tasks:[{category:"FCFS Implementation",commands:[{command:"python3 fcfs.py",description:"Run FCFS scheduling simulation",expectedOutput:`First Come First Served Scheduling
Process  Burst Time  Waiting Time  Turnaround Time
P1         24          0            24
P2         3           24           27
P3         3           27           30
Average Waiting Time: 17.0`}]},{category:"SJF Implementation",commands:[{command:"python3 sjf.py",description:"Run SJF scheduling simulation",expectedOutput:`Shortest Job First Scheduling
Process  Burst Time  Waiting Time  Turnaround Time
P2         3           0            3
P3         3           3            6
P1         24          6            30
Average Waiting Time: 3.0`}]}]},conceptualQuiz:[{question:"What is the convoy effect in CPU scheduling?",options:["When short processes wait for a long process to finish","When processes move together in a queue","When CPU utilization is maximized","When processes are scheduled randomly"],correct:0,explanation:"The convoy effect occurs in FCFS scheduling when short processes must wait for a long process to finish, leading to poor average waiting time."},{question:"Which scheduling algorithm is optimal for minimizing average waiting time?",options:["FCFS","SJF","Round Robin","Priority Scheduling"],correct:1,explanation:"Shortest Job First (SJF) is theoretically optimal for minimizing average waiting time, though it requires knowing or predicting process burst times."},{question:"What is the main advantage of Round Robin scheduling?",options:["Best average waiting time","Highest throughput","Fair CPU allocation","Lowest overhead"],correct:2,explanation:"Round Robin ensures fair allocation of CPU time by giving each process a small time quantum, making it ideal for time-sharing systems."}]},Re={title:"Deadlocks",description:"Understanding deadlocks, their conditions, prevention, avoidance, and recovery strategies.",sections:[{title:"Deadlock Fundamentals",content:`A deadlock occurs when a set of processes are blocked because each process is holding a resource and waiting to acquire a resource held by another process.

Four necessary conditions for deadlock:

1. Mutual Exclusion
   - Only one process can use a resource at a time
   - Resources cannot be shared

2. Hold and Wait
   - Process holds resources while waiting for others
   - Does not release current resources

3. No Preemption
   - Resources cannot be forcibly taken
   - Only released voluntarily

4. Circular Wait
   - Set of processes waiting for each other
   - Forms a circular chain`,visualization:{type:"interactive",component:"DeadlockVisualization",data:{processes:[{id:"P1",holding:"R1",waiting:"R2"},{id:"P2",holding:"R2",waiting:"R1"}]}}},{title:"Deadlock Prevention",content:`Deadlock prevention works by ensuring at least one of the necessary conditions cannot occur:

1. Mutual Exclusion Prevention
   - Make resources sharable
   - Not always possible (e.g., printers)

2. Hold and Wait Prevention
   - Request all resources initially
   - Release all before requesting new ones

3. No Preemption Prevention
   - Allow resource preemption
   - May not be feasible for some resources

4. Circular Wait Prevention
   - Impose total ordering of resources
   - Request in increasing order`,visualization:{type:"interactive",component:"PreventionVisualization",data:{strategies:[{name:"Resource Ordering",success:!0},{name:"Request All Initially",success:!0},{name:"Allow Preemption",success:!1},{name:"Resource Sharing",success:!1}]}}},{title:"Deadlock Avoidance",content:`Deadlock avoidance requires:

1. System State Information
   - Maximum resource needs
   - Currently allocated resources
   - Available resources

2. Safe State
   - System can allocate resources to each process
   - In some sequence without deadlock

3. Banker's Algorithm
   - Checks if request leads to safe state
   - Grants only safe requests
   - Conservative but guarantees no deadlock

4. Resource-Allocation Graph
   - Nodes represent processes and resources
   - Edges represent allocation and requests
   - Claim edges show future requests`},{title:"Deadlock Detection and Recovery",content:`When prevention and avoidance are not used:

1. Detection Methods
   - Wait-for graph
   - Resource allocation graph
   - State detection algorithms

2. Recovery Strategies
   - Process Termination
     * Terminate all deadlocked processes
     * Terminate one at a time until cycle breaks
   
   - Resource Preemption
     * Select victim process
     * Rollback to safe state
     * Avoid starvation

3. Recovery Costs
   - Termination costs
   - Rollback costs
   - Starvation costs`}],practicalLab:{title:"Deadlock Detection Lab",description:"Practice implementing deadlock detection algorithms",tasks:[{category:"Resource Allocation Graph",commands:[{command:"python3 detect_deadlock.py",description:"Run deadlock detection algorithm",expectedOutput:`Checking for deadlock...
Process P1: Holding R1, Waiting for R2
Process P2: Holding R2, Waiting for R3
Process P3: Holding R3, Waiting for R1
Deadlock detected!
Cycle: P1 -> R2 -> P2 -> R3 -> P3 -> R1 -> P1`}]},{category:"Banker's Algorithm",commands:[{command:"python3 bankers_algorithm.py",description:"Run Banker's algorithm simulation",expectedOutput:`Current State:
Available resources: A=3 B=3 C=2
Allocated resources:
P0: A=0 B=1 C=0
P1: A=2 B=0 C=0
P2: A=3 B=0 C=2
Maximum needs:
P0: A=7 B=5 C=3
P1: A=3 B=2 C=2
P2: A=9 B=0 C=2
System is in safe state.
Safe sequence: P1, P0, P2`}]}]},conceptualQuiz:[{question:"Which of the following is NOT a necessary condition for deadlock?",options:["Mutual Exclusion","Hold and Wait","Process Priority","Circular Wait"],correct:2,explanation:"Process priority is not one of the four necessary conditions for deadlock. The conditions are: mutual exclusion, hold and wait, no preemption, and circular wait."},{question:"What is the main advantage of the Banker's Algorithm?",options:["It's very fast","It prevents deadlock","It's easy to implement","It uses minimal resources"],correct:1,explanation:"The Banker's Algorithm's main advantage is that it prevents deadlocks by only granting resource requests that lead to safe states."},{question:"Which deadlock recovery method has the least overhead?",options:["Process termination","Resource preemption","Process rollback","Total system restart"],correct:0,explanation:"Process termination, while potentially wasteful of completed work, has the least overhead in terms of implementation and execution complexity."}]},De={title:"Device Management",description:"Understanding device management, device drivers, and I/O systems in operating systems.",sections:[{title:"Device Management Basics",content:`Device management is a crucial part of operating systems, handling communication between the system and various hardware devices.

Key components include:

1. Device Controllers
   - Hardware that controls device operation
   - Contains local buffer storage
   - Special-purpose registers
   - Manages device-specific details

2. Device Drivers
   - Software that communicates with controllers
   - Provides uniform interface to OS
   - Device-specific code
   - Handles interrupts and error handling

3. I/O Subsystem
   - Memory-mapped I/O vs Port-mapped I/O
   - Polling vs Interrupts
   - Direct Memory Access (DMA)
   - Device independence`,visualization:{type:"interactive",component:"DeviceManagementVisualization",data:{components:[{name:"Device Controller",type:"hardware"},{name:"Device Driver",type:"software"},{name:"I/O Subsystem",type:"system"}]}}},{title:"I/O Hardware",content:`I/O Hardware consists of various components and techniques:

1. Port-Mapped I/O
   - Special I/O instructions
   - Separate I/O and memory space
   - IN and OUT instructions

2. Memory-Mapped I/O
   - Devices mapped to memory addresses
   - Regular memory instructions
   - No special I/O instructions

3. Direct Memory Access (DMA)
   - Bypasses CPU for data transfer
   - Reduces system overhead
   - Handles bulk data transfer

4. Interrupt Handling
   - Device signals completion
   - CPU handles interrupt
   - Context switching overhead`,visualization:{type:"interactive",component:"IOHardwareVisualization",data:{techniques:[{name:"Port-Mapped I/O",type:"port"},{name:"Memory-Mapped I/O",type:"memory"},{name:"DMA",type:"direct"}]}}},{title:"Device Drivers",content:`Device drivers are essential software components:

1. Driver Functions
   - Initialize device
   - Interpret commands
   - Handle interrupts
   - Manage data transfer

2. Driver Types
   - Character device drivers
   - Block device drivers
   - Network device drivers
   - Virtual device drivers

3. Driver Architecture
   - User space vs Kernel space
   - Module loading/unloading
   - Device abstraction
   - Error handling

4. Driver Development
   - Kernel API usage
   - Hardware protocols
   - Testing and debugging
   - Documentation`},{title:"I/O Software Layers",content:`I/O software is organized in layers:

1. User Level I/O Software
   - Libraries
   - System calls
   - Device-independent operations

2. Device Independent Software
   - Naming
   - Protection
   - Blocking/Non-blocking
   - Buffering

3. Device Drivers
   - Device-specific code
   - Standard interfaces
   - Error handling

4. Interrupt Handlers
   - Save device status
   - Signal device driver
   - Handle errors

5. Hardware
   - Device controllers
   - Registers
   - Device mechanics`}],practicalLab:{title:"Device Driver Lab",description:"Practice implementing and analyzing device drivers",tasks:[{category:"Character Device Driver",commands:[{command:"cat /dev/example",description:"Read from example character device",expectedOutput:`Example character device output
Device status: OK`}]},{category:"Block Device Operations",commands:[{command:"dd if=/dev/zero of=/dev/example bs=1M count=1",description:"Write to example block device",expectedOutput:`1+0 records in
1+0 records out
1048576 bytes (1.0 MB) copied`}]}]},conceptualQuiz:[{question:"What is the main advantage of DMA over programmed I/O?",options:["Lower CPU overhead","Higher data accuracy","Better error handling","Simpler implementation"],correct:0,explanation:"DMA reduces CPU overhead by allowing devices to transfer data directly to/from memory without CPU intervention for each byte."},{question:"Which I/O mapping technique uses regular memory instructions?",options:["Port-mapped I/O","Memory-mapped I/O","Direct Memory Access","Interrupt-driven I/O"],correct:1,explanation:"Memory-mapped I/O maps device registers to memory addresses, allowing the use of regular memory instructions for I/O operations."},{question:"What is the primary role of a device driver?",options:["Manage hardware interrupts","Provide a uniform interface to the OS","Control device power management","Handle user input"],correct:1,explanation:"The primary role of a device driver is to provide a uniform interface to the operating system, hiding device-specific details."}]},o={title:"OS Interview Questions & Answers",description:"Comprehensive collection of operating system interview questions with detailed answers and examples.",sections:[{title:"Basic OS Concepts",content:`
Operating System Fundamentals:

1. What is an Operating System?
   - An operating system is system software that manages hardware and software resources
   - Provides common services for computer programs
   - Acts as an intermediary between hardware and user applications
   - Key responsibilities: Process management, Memory management, File system management, I/O management

2. What are the main functions of an Operating System?
   - Process Management: Creation, scheduling, termination
   - Memory Management: Allocation, protection, virtual memory
   - File Management: File creation, deletion, organization
   - Device Management: Device driver management, I/O operations
   - Security: Access control, user authentication
   - User Interface: Command line or GUI

3. What are the different types of Operating Systems?
   - Batch OS: Processes jobs in batches
   - Multi-programming OS: Multiple programs in memory
   - Multi-tasking OS: Multiple tasks executed concurrently
   - Multi-processing OS: Multiple processors working together
   - Real-time OS: Guaranteed response times
   - Distributed OS: Multiple computers working together

4. Explain the difference between Kernel and Shell
   - Kernel: Core component that manages hardware resources
   - Shell: Interface between user and kernel
   - Kernel handles: Memory management, Process scheduling, System calls
   - Shell handles: Command interpretation, Script execution, User interaction`},{title:"Process & Thread Management",content:`
Process and Thread Concepts:

1. What is a Process?
   - Instance of a program in execution
   - Contains: Program counter, Stack, Data section
   - Has its own address space
   - Resources allocated by OS

2. What is a Thread?
   - Lightweight unit of execution within a process
   - Shares resources with other threads in same process
   - Has own: Program counter, Stack, Registers
   - Faster context switching than processes

3. Difference between Process and Thread
   - Processes are independent, threads share resources
   - Processes have separate memory space, threads share memory
   - Process creation is more expensive than thread creation
   - Inter-process communication is more complex than inter-thread

4. What is Context Switching?
   - Saving state of current process/thread
   - Loading state of new process/thread
   - Overhead includes:
     * Saving/restoring registers
     * Switching memory maps
     * Updating various tables
     * Cache invalidation`},{title:"Memory Management",content:`
Memory Management Concepts:

1. What is Virtual Memory?
   - Technique that provides illusion of more memory than physically available
   - Uses disk space as extension of RAM
   - Benefits:
     * Larger address space
     * Memory isolation
     * More efficient memory utilization

2. What is Paging?
   - Memory management scheme
   - Divides physical memory into fixed-size frames
   - Divides logical memory into pages
   - Advantages:
     * No external fragmentation
     * Flexible memory allocation

3. What is Segmentation?
   - Memory management scheme
   - Divides memory into variable-sized segments
   - Each segment represents logical unit
   - Advantages:
     * Better for program structure
     * Easier sharing and protection

4. Explain Page Replacement Algorithms
   - FIFO (First In First Out)
   - LRU (Least Recently Used)
   - Optimal Page Replacement
   - Clock Algorithm
   Each has different trade-offs between complexity and efficiency`},{title:"Scheduling Algorithms",content:`
CPU Scheduling Concepts:

1. What is CPU Scheduling?
   - Process of determining which process runs on CPU
   - Aims to maximize CPU utilization
   - Types:
     * Preemptive
     * Non-preemptive

2. Common Scheduling Algorithms:
   - First-Come, First-Served (FCFS)
     * Simple but can lead to convoy effect
   - Shortest Job First (SJF)
     * Optimal for minimizing average waiting time
   - Round Robin (RR)
     * Fair allocation with time quantum
   - Priority Scheduling
     * Based on process priority

3. Scheduling Criteria:
   - CPU Utilization
   - Throughput
   - Turnaround Time
   - Waiting Time
   - Response Time

4. Real-time Scheduling:
   - Hard real-time systems
   - Soft real-time systems
   - Deadline scheduling
   - Rate monotonic scheduling`},{title:"Deadlock Management",content:`
Deadlock Concepts:

1. What is Deadlock?
   - Situation where processes wait indefinitely
   - Each process holds resources needed by others
   - Four necessary conditions:
     * Mutual Exclusion
     * Hold and Wait
     * No Preemption
     * Circular Wait

2. Deadlock Prevention:
   - Prevent one of four necessary conditions
   - Methods:
     * Resource ordering
     * Request all resources initially
     * Allow preemption
     * Break circular wait

3. Deadlock Avoidance:
   - Banker's Algorithm
   - Safe state checking
   - Resource allocation graph
   - Dynamic avoidance

4. Deadlock Detection and Recovery:
   - Detection algorithms
   - Recovery methods:
     * Process termination
     * Resource preemption
   - Recovery strategies`}],practicalLab:{title:"OS Interview Practice Lab",description:"Practice solving common operating system interview problems",tasks:[{category:"Process Synchronization",commands:[{command:"solve producer_consumer",description:"Implement producer-consumer solution",expectedOutput:"Producer-consumer problem solution template"},{command:"solve dining_philosophers",description:"Implement dining philosophers solution",expectedOutput:"Dining philosophers problem solution template"}]},{category:"Memory Management",commands:[{command:"implement page_replacement",description:"Implement LRU page replacement",expectedOutput:"LRU implementation template"},{command:"analyze memory_usage",description:"Analyze memory usage patterns",expectedOutput:"Memory analysis tools and techniques"}]}]},conceptualQuiz:[{question:"What are the four necessary conditions for deadlock?",options:["Mutual exclusion, hold and wait, no preemption, circular wait","Priority, scheduling, resources, waiting","Starvation, aging, priority inversion, blocking","Synchronization, mutual exclusion, semaphores, monitors"],correct:0,explanation:"The four necessary conditions for deadlock are mutual exclusion (resources cannot be shared), hold and wait (processes hold resources while waiting for others), no preemption (resources cannot be forcibly taken), and circular wait (circular chain of processes waiting for resources)."},{question:"What is the difference between preemptive and non-preemptive scheduling?",options:["Preemptive allows interrupting running processes, non-preemptive doesn't","Preemptive is faster, non-preemptive is slower","Preemptive uses more memory, non-preemptive uses less","Preemptive is for real-time systems only, non-preemptive for batch systems"],correct:0,explanation:"In preemptive scheduling, the operating system can interrupt and suspend a running process to give CPU time to another process. In non-preemptive scheduling, once a process starts running, it continues until it completes or voluntarily yields the CPU."}]},Ie={title:"Linux Basics",description:"Master the fundamentals of Linux through interactive missions and hands-on challenges.",sections:[{title:"Introduction to Linux Commands",content:`Linux commands are the foundation of working with Linux systems. Let's start with the basics:

1. Navigation Commands
   - pwd: Print working directory
   - ls: List directory contents
   - cd: Change directory
   - mkdir: Create directory
   - rmdir: Remove directory

2. File Operations
   - touch: Create empty file
   - cp: Copy files
   - mv: Move/rename files
   - rm: Remove files
   - cat: Display file contents

3. File Permissions
   - chmod: Change file permissions
   - chown: Change file ownership
   - chgrp: Change group ownership

4. System Information
   - uname: System information
   - whoami: Current user
   - date: System date/time
   - df: Disk usage
   - free: Memory usage`,visualization:{type:"interactive",component:"FileSystemVisualization",data:{structure:"tree",nodes:[{name:"/",type:"dir"},{name:"/home",type:"dir"},{name:"/home/<USER>",type:"dir"},{name:"/home/<USER>/documents",type:"dir"},{name:"/home/<USER>/documents/file.txt",type:"file"}]}}},{title:"File System Structure",content:`The Linux file system follows a hierarchical structure:

/ (Root Directory)
├── /bin - Essential user binaries
├── /boot - Boot loader files
├── /dev - Device files
├── /etc - System configuration
├── /home - User home directories
├── /lib - System libraries
├── /media - Removable media
├── /mnt - Mount point
├── /opt - Optional software
├── /proc - Process information
├── /root - Root user home
├── /sbin - System binaries
├── /tmp - Temporary files
├── /usr - User programs
└── /var - Variable files`,visualization:{type:"interactive",component:"FileSystemVisualization",data:{structure:"tree",nodes:[{name:"/",type:"dir"},{name:"/bin",type:"dir"},{name:"/etc",type:"dir"},{name:"/home",type:"dir"},{name:"/var",type:"dir"}]}}}],practicalLab:{title:"Linux Basics Lab",description:"Practice essential Linux commands in a safe environment",missions:[{id:"mission1",title:"Command Line Warrior",description:"Learn essential Linux commands through an immersive terminal experience.",difficulty:"Beginner",points:100,requirements:["None"],rewards:["Command Line Mastery Badge","100 XP","Access to Navigation Missions"],tasks:[{id:"task1",description:"Print current working directory",command:"pwd",points:10,hint:"Use pwd to show your current location"},{id:"task2",description:"List all files in current directory",command:"ls -la",points:10,hint:"The -la flags show all files with details"},{id:"task3",description:"Create a new directory named 'mission1'",command:"mkdir mission1",points:10,hint:"mkdir creates new directories"}]},{id:"mission2",title:"File System Navigator",description:"Master file system navigation and manipulation.",difficulty:"Intermediate",points:150,requirements:["Command Line Warrior"],rewards:["File System Expert Badge","150 XP","Access to Permission Missions"],tasks:[{id:"task1",description:"Navigate to home directory",command:"cd ~",points:15,hint:"~ represents your home directory"},{id:"task2",description:"Create multiple nested directories",command:"mkdir -p mission2/level1/level2",points:15,hint:"-p creates parent directories as needed"}]},{id:"mission3",title:"Permission Master",description:"Learn file permissions and access control.",difficulty:"Advanced",points:200,requirements:["File System Navigator"],rewards:["Security Expert Badge","200 XP","Access to Advanced Missions"],tasks:[{id:"task1",description:"Change file permissions",command:"chmod 755 script.sh",points:20,hint:"755 gives rwx to owner, rx to others"},{id:"task2",description:"Change file ownership",command:"chown user:group file.txt",points:20,hint:"chown changes file owner and group"}]}]},conceptualQuiz:[{question:"What command is used to list files in a directory?",options:["dir","ls","list","show"],correct:1,explanation:"The 'ls' command is used to list directory contents in Linux. It can be used with various flags like -l for long format and -a to show hidden files."},{question:"Which directory typically contains user home directories?",options:["/root","/usr","/home","/users"],correct:2,explanation:"The /home directory is where user home directories are stored in Linux. Each user gets their own subdirectory under /home."},{question:"What does the chmod command do?",options:["Changes file mode","Changes file permissions","Changes file owner","Changes file type"],correct:1,explanation:"chmod (change mode) is used to change file permissions in Linux, controlling who can read, write, or execute the file."}]},Ae={title:"Introduction to Linux",description:"Master the fundamentals of Linux operating system through interactive learning and hands-on practice.",sections:[{title:"What is Linux?",content:`Linux is a free and open-source operating system kernel that powers millions of devices worldwide.

Key aspects of Linux:
- Open Source: Free to use, modify, and distribute
- Multi-user: Supports multiple users simultaneously
- Multi-tasking: Runs multiple programs concurrently
- Security: Strong security model and permissions
- Community: Large community of developers and users

Linux is used in:
- Servers
- Desktop computers
- Mobile devices (Android)
- Embedded systems
- Cloud infrastructure`,game:{type:"quiz",questions:[{question:"What makes Linux unique among operating systems?",options:["Its open-source nature","It only runs on servers","It's only for programmers","It costs more than Windows"],correct:0}]}},{title:"Linux File System",content:`The Linux file system follows a hierarchical structure:

/ (Root Directory)
├── /bin - Essential user binaries
├── /boot - Boot loader files
├── /dev - Device files
├── /etc - System configuration
├── /home - User home directories
├── /lib - System libraries
├── /media - Removable media
├── /mnt - Mount point
├── /opt - Optional software
├── /proc - Process information
├── /root - Root user home
├── /sbin - System binaries
├── /tmp - Temporary files
├── /usr - User programs
└── /var - Variable files`,game:{type:"filesystem",tasks:[{description:"Create a directory in /home",command:"mkdir /home/<USER>",points:10},{description:"Create a file in user's directory",command:"touch /home/<USER>/file.txt",points:10}]}},{title:"Basic Commands",content:`Essential Linux commands for navigation and file management:

1. Navigation Commands
   - pwd: Print working directory
   - cd: Change directory
   - ls: List directory contents

2. File Operations
   - touch: Create empty file
   - mkdir: Create directory
   - cp: Copy files
   - mv: Move/rename files
   - rm: Remove files

3. File Viewing
   - cat: Display file contents
   - less: Page through file
   - head: Show file start
   - tail: Show file end

4. System Information
   - uname: System information
   - whoami: Current user
   - date: System date/time
   - df: Disk usage
   - free: Memory usage`,game:{type:"terminal",tasks:[{description:"Navigate to home directory",command:"cd ~",points:5},{description:"List all files including hidden ones",command:"ls -la",points:5},{description:"Create a new directory",command:"mkdir test",points:5}]}}],practicalLab:{title:"Linux Basics Lab",description:"Practice essential Linux commands in a safe environment",tasks:[{category:"File Operations",commands:[{command:"ls -la",description:"List all files with details",expectedOutput:`total 20
drwxr-xr-x 2 <USER> <GROUP> 4096 Mar 7 12:00 .`},{command:"mkdir test",description:"Create test directory",expectedOutput:""},{command:"touch file.txt",description:"Create empty file",expectedOutput:""}]},{category:"Navigation",commands:[{command:"pwd",description:"Print working directory",expectedOutput:"/home/<USER>"},{command:"cd test",description:"Change to test directory",expectedOutput:""}]}]}},Le={title:"Shell Scripting",description:"Master shell scripting in Linux through interactive examples and hands-on practice.",sections:[{title:"Introduction to Shell Scripting",content:`Shell scripting is a powerful way to automate tasks in Linux. A shell script is a file containing a sequence of commands that are executed by the shell.

Key concepts:
1. Shebang (#!)
   - Tells system which interpreter to use
   - Example: #!/bin/bash

2. Script Execution
   - Make script executable: chmod +x script.sh
   - Run script: ./script.sh

3. Variables
   - Assignment: name="value"
   - Access: $name
   - Environment variables: $PATH, $HOME

4. Comments
   - Single line: # comment
   - Multi-line: : ' multiple
                    line comment '`,game:{type:"terminal",tasks:[{description:"Create a script file",command:"touch script.sh",points:5},{description:"Make script executable",command:"chmod +x script.sh",points:5},{description:"Add shebang line",command:"echo '#!/bin/bash' > script.sh",points:10}]}},{title:"Control Flow",content:`Shell scripts support various control flow structures:

1. Conditional Statements
   if [ condition ]; then
     commands
   elif [ condition ]; then
     commands
   else
     commands
   fi

2. Loops
   - for loop:
     for i in {1..5}; do
       commands
     done

   - while loop:
     while [ condition ]; do
       commands
     done

3. Case Statements
   case $var in
     pattern1) commands;;
     pattern2) commands;;
     *) default commands;;
   esac`,game:{type:"scriptBuilder",tasks:[{description:"Create an if statement checking if a file exists",template:`if [ -f filename ]; then
  echo 'File exists'
fi`,points:15},{description:"Create a loop that counts from 1 to 5",template:`for i in {1..5}; do
  echo $i
done`,points:15}]}},{title:"Functions",content:`Functions help organize and reuse code:

1. Function Declaration
   function_name() {
     commands
     return value
   }

2. Function Parameters
   - Access parameters: $1, $2, etc.
   - Number of parameters: $#
   - All parameters: $@

3. Return Values
   - return command (0-255)
   - Use command substitution
   - Example: result=$(function_name)

4. Local Variables
   - Declare with local keyword
   - Scope limited to function
   - Example: local var="value"`,game:{type:"functionBuilder",tasks:[{description:"Create a function that takes two parameters and returns their sum",template:`add_numbers() {
  echo $(($1 + $2))
}`,points:20},{description:"Create a function that checks if a number is even",template:`is_even() {
  if [ $(($1 % 2)) -eq 0 ]; then
    return 0
  else
    return 1
  fi
}`,points:20}]}}],practicalLab:{title:"Shell Scripting Lab",description:"Practice writing and running shell scripts",tasks:[{category:"Basic Scripts",commands:[{command:`echo '#!/bin/bash
echo "Hello, World!"' > hello.sh`,description:"Create a Hello World script",expectedOutput:""},{command:"chmod +x hello.sh",description:"Make script executable",expectedOutput:""},{command:"./hello.sh",description:"Run the script",expectedOutput:"Hello, World!"}]},{category:"Variables and Input",commands:[{command:`name='John'
echo "Hello, $name!"`,description:"Use variables",expectedOutput:"Hello, John!"},{command:"read -p 'Enter your name: ' name",description:"Read user input",expectedOutput:"Enter your name: "}]},{category:"Control Flow",commands:[{command:"for i in {1..5}; do echo $i; done",description:"For loop example",expectedOutput:`1
2
3
4
5`},{command:"if [ -f file.txt ]; then echo 'File exists'; fi",description:"If statement example",expectedOutput:""}]}]},conceptualQuiz:[{question:"What is the purpose of the shebang (#!) line?",options:["To make the script executable","To specify the interpreter","To add a comment","To include another script"],correct:1,explanation:"The shebang (#!) line at the beginning of a script tells the system which interpreter should be used to execute the script."},{question:"How do you access the first parameter passed to a shell script?",options:["$0","$1","$#","$@"],correct:1,explanation:"$1 refers to the first parameter passed to a script. $0 is the script name, $# is the number of parameters, and $@ is all parameters."},{question:"What is the correct way to assign a value to a variable in bash?",options:["variable = value","variable=value","$variable = value","set variable = value"],correct:1,explanation:"In bash, variables are assigned using the format variable=value with no spaces around the equals sign."}]},Ue={"os-introduction":ke,"process-management":Pe,"memory-management":Me,"file-systems":Oe,"io-management":Fe,"cpu-scheduling":Te,deadlocks:Re,"device-management":De,"os-basics-interview":o,"process-thread-interview":o,"memory-interview":o,"scheduling-interview":o,"deadlock-interview":o,"filesystem-interview":o,"device-io-interview":o,"security-interview":o,"real-world-scenarios":o,"coding-problems":o,"linux-basics":Ie,"linux-introduction":Ae,"shell-scripting":Le},Y={"os-introduction":{title:"Introduction to Operating Systems",sections:[{title:"What is an Operating System?",content:[{type:"text",value:"An operating system (OS) is system software that manages computer hardware, software resources, and provides common services for computer programs. It acts as an intermediary between users and the computer hardware."},{type:"text",value:"The operating system is a critical component in the functioning of a computer system. Without an operating system, a user cannot run an application program on their computer."}]},{title:"Core Functions of an Operating System",content:[{type:"text",value:"Operating systems perform several key functions:"},{type:"text",value:"<ul><li><strong>Process Management:</strong> Creating, scheduling, and terminating processes</li><li><strong>Memory Management:</strong> Allocating and deallocating memory space</li><li><strong>File System Management:</strong> Creating, deleting, and organizing files</li><li><strong>Device Management:</strong> Managing device communication</li><li><strong>Security:</strong> Protecting system resources and user data</li></ul>"}]}]}},ze={title:"Content Coming Soon",description:"This content is currently being developed and will be available soon.",sections:[{title:"Under Development",content:"We are working hard to bring you high-quality content for this topic. Please check back later."}],practicalLab:{title:"Lab Under Development",description:"Our team is creating hands-on labs for this topic.",tasks:[]},conceptualQuiz:[]},qe=t=>Y[t]?Y[t]:Ue[t]||ze,Ee=({data:t})=>e.jsx("div",{className:"space-y-4 p-4 bg-gray-900 rounded-lg",children:t.layers.map((n,m)=>e.jsxs(v.div,{initial:{x:-100,opacity:0},animate:{x:0,opacity:1},transition:{delay:m*.2},className:"p-4 rounded-lg flex items-center gap-3",style:{backgroundColor:`${n.color}20`,border:`1px solid ${n.color}`},children:[e.jsx("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center",style:{backgroundColor:`${n.color}40`},children:e.jsx("span",{className:"text-white text-xl",children:n.icon})}),e.jsx("span",{className:"text-white font-medium",children:n.name})]},m))}),Be={FaUser:xe,FaUsers:Z,FaClock:f,FaNetwork:l},je=({data:t})=>e.jsx("div",{className:"grid grid-cols-2 gap-4 p-4 bg-gray-900 rounded-lg",children:t.types.map((n,m)=>{const a=Be[n.icon];return e.jsxs(v.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},transition:{delay:m*.1},className:"bg-gray-800 p-4 rounded-lg flex items-center gap-3",children:[a&&e.jsx(a,{className:"text-[#88cc14] text-xl"}),e.jsx("span",{className:"text-white font-medium",children:n.name})]},m)})}),He=({questions:t})=>{const[n,m]=M.useState(0),[a,U]=M.useState({}),[ee,z]=M.useState(!1),[O,B]=M.useState(0);if(!t||t.length===0)return null;const te=(s,i)=>{U(r=>({...r,[s]:i}))},ie=()=>{let s=0;return Object.entries(a).forEach(([i,r])=>{t[i].correct===r&&s++}),s},se=()=>{const s=ie();B(s),z(!0)},oe=()=>{U({}),z(!1),B(0),m(0)},W=Object.keys(a).length===t.length;return e.jsxs(v.div,{initial:{opacity:0},animate:{opacity:1},className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Knowledge Check"}),ee?e.jsxs(v.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-4xl font-bold mb-2",children:[O," / ",t.length]}),e.jsx("p",{className:"text-gray-600",children:O===t.length?"Perfect score! You've mastered this topic!":O>t.length/2?"Good job! Keep practicing to improve further.":"Keep studying! You'll get better with practice."})]}),e.jsx("div",{className:"space-y-6",children:t.map((s,i)=>{const r=a[i]===s.correct;return e.jsx("div",{className:`p-4 rounded-lg ${r?"bg-green-50":"bg-red-50"}`,children:e.jsxs("div",{className:"flex items-start gap-3",children:[r?e.jsx(X,{className:"text-green-500 mt-1"}):e.jsx(Se,{className:"text-red-500 mt-1"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900 mb-2",children:s.question}),e.jsx("p",{className:"text-sm text-gray-600",children:s.explanation})]})]})},i)})}),e.jsxs("button",{onClick:oe,className:"w-full flex items-center justify-center gap-2 bg-gray-900 text-white py-3 px-4 rounded-lg font-bold hover:bg-gray-800 transition-all",children:[e.jsx(Ce,{}),"Try Again"]})]}):e.jsxs("div",{className:"space-y-8",children:[t.map((s,i)=>e.jsxs(v.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:i*.1},className:"space-y-4",children:[e.jsxs("p",{className:"font-medium text-gray-900",children:[i+1,". ",s.question]}),e.jsx("div",{className:"space-y-2",children:s.options.map((r,y)=>e.jsx("button",{onClick:()=>te(i,y),className:`w-full text-left p-4 rounded-lg transition-all ${a[i]===y?"bg-[#88cc14]/10 border-[#88cc14] border":"bg-gray-50 hover:bg-[#88cc14]/5 border border-gray-200"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-5 h-5 rounded-full border ${a[i]===y?"border-[#88cc14] bg-[#88cc14]":"border-gray-300"} flex items-center justify-center`,children:a[i]===y&&e.jsx(X,{className:"text-white text-xs"})}),e.jsx("span",{className:"text-gray-700",children:r})]})},y))})]},i)),e.jsx("button",{onClick:se,disabled:!W,className:`w-full py-3 px-4 rounded-lg font-bold transition-all ${W?"bg-[#88cc14] text-black hover:bg-[#7ab811]":"bg-gray-100 text-gray-400 cursor-not-allowed"}`,children:"Submit Answers"})]})]})};export{He as K,je as O,Ee as a,qe as g,Ne as l};
