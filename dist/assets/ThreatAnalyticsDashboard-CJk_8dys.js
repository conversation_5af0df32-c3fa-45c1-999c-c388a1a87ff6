import{au as jt,r as _,j as l,x as Dt,I as vt}from"./index-c6UceSOv.js";function tt(r,t){return function(){return r.apply(t,arguments)}}const{toString:Ct}=Object.prototype,{getPrototypeOf:Ie}=Object,we=(r=>t=>{const e=Ct.call(t);return r[e]||(r[e]=e.slice(8,-1).toLowerCase())})(Object.create(null)),F=r=>(r=r.toLowerCase(),t=>we(t)===r),be=r=>t=>typeof t===r,{isArray:re}=Array,de=be("undefined");function Pt(r){return r!==null&&!de(r)&&r.constructor!==null&&!de(r.constructor)&&P(r.constructor.isBuffer)&&r.constructor.isBuffer(r)}const rt=F("ArrayBuffer");function Ot(r){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(r):t=r&&r.buffer&&rt(r.buffer),t}const _t=be("string"),P=be("function"),st=be("number"),Re=r=>r!==null&&typeof r=="object",It=r=>r===!0||r===!1,he=r=>{if(we(r)!=="object")return!1;const t=Ie(r);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in r)&&!(Symbol.iterator in r)},$t=F("Date"),Ut=F("File"),Lt=F("Blob"),Ft=F("FileList"),Bt=r=>Re(r)&&P(r.pipe),qt=r=>{let t;return r&&(typeof FormData=="function"&&r instanceof FormData||P(r.append)&&((t=we(r))==="formdata"||t==="object"&&P(r.toString)&&r.toString()==="[object FormData]"))},Mt=F("URLSearchParams"),[zt,Ht,Vt,Xt]=["ReadableStream","Request","Response","Headers"].map(F),Jt=r=>r.trim?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function pe(r,t,{allOwnKeys:e=!1}={}){if(r===null||typeof r>"u")return;let s,n;if(typeof r!="object"&&(r=[r]),re(r))for(s=0,n=r.length;s<n;s++)t.call(null,r[s],s,r);else{const o=e?Object.getOwnPropertyNames(r):Object.keys(r),a=o.length;let i;for(s=0;s<a;s++)i=o[s],t.call(null,r[i],i,r)}}function nt(r,t){t=t.toLowerCase();const e=Object.keys(r);let s=e.length,n;for(;s-- >0;)if(n=e[s],t===n.toLowerCase())return n;return null}const Y=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ot=r=>!de(r)&&r!==Y;function De(){const{caseless:r}=ot(this)&&this||{},t={},e=(s,n)=>{const o=r&&nt(t,n)||n;he(t[o])&&he(s)?t[o]=De(t[o],s):he(s)?t[o]=De({},s):re(s)?t[o]=s.slice():t[o]=s};for(let s=0,n=arguments.length;s<n;s++)arguments[s]&&pe(arguments[s],e);return t}const Gt=(r,t,e,{allOwnKeys:s}={})=>(pe(t,(n,o)=>{e&&P(n)?r[o]=tt(n,e):r[o]=n},{allOwnKeys:s}),r),Wt=r=>(r.charCodeAt(0)===65279&&(r=r.slice(1)),r),Yt=(r,t,e,s)=>{r.prototype=Object.create(t.prototype,s),r.prototype.constructor=r,Object.defineProperty(r,"super",{value:t.prototype}),e&&Object.assign(r.prototype,e)},Kt=(r,t,e,s)=>{let n,o,a;const i={};if(t=t||{},r==null)return t;do{for(n=Object.getOwnPropertyNames(r),o=n.length;o-- >0;)a=n[o],(!s||s(a,r,t))&&!i[a]&&(t[a]=r[a],i[a]=!0);r=e!==!1&&Ie(r)}while(r&&(!e||e(r,t))&&r!==Object.prototype);return t},Qt=(r,t,e)=>{r=String(r),(e===void 0||e>r.length)&&(e=r.length),e-=t.length;const s=r.indexOf(t,e);return s!==-1&&s===e},Zt=r=>{if(!r)return null;if(re(r))return r;let t=r.length;if(!st(t))return null;const e=new Array(t);for(;t-- >0;)e[t]=r[t];return e},er=(r=>t=>r&&t instanceof r)(typeof Uint8Array<"u"&&Ie(Uint8Array)),tr=(r,t)=>{const s=(r&&r[Symbol.iterator]).call(r);let n;for(;(n=s.next())&&!n.done;){const o=n.value;t.call(r,o[0],o[1])}},rr=(r,t)=>{let e;const s=[];for(;(e=r.exec(t))!==null;)s.push(e);return s},sr=F("HTMLFormElement"),nr=r=>r.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,s,n){return s.toUpperCase()+n}),qe=(({hasOwnProperty:r})=>(t,e)=>r.call(t,e))(Object.prototype),or=F("RegExp"),at=(r,t)=>{const e=Object.getOwnPropertyDescriptors(r),s={};pe(e,(n,o)=>{let a;(a=t(n,o,r))!==!1&&(s[o]=a||n)}),Object.defineProperties(r,s)},ar=r=>{at(r,(t,e)=>{if(P(r)&&["arguments","caller","callee"].indexOf(e)!==-1)return!1;const s=r[e];if(P(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+e+"'")})}})},ir=(r,t)=>{const e={},s=n=>{n.forEach(o=>{e[o]=!0})};return re(r)?s(r):s(String(r).split(t)),e},cr=()=>{},lr=(r,t)=>r!=null&&Number.isFinite(r=+r)?r:t;function ur(r){return!!(r&&P(r.append)&&r[Symbol.toStringTag]==="FormData"&&r[Symbol.iterator])}const dr=r=>{const t=new Array(10),e=(s,n)=>{if(Re(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[n]=s;const o=re(s)?[]:{};return pe(s,(a,i)=>{const u=e(a,n+1);!de(u)&&(o[i]=u)}),t[n]=void 0,o}}return s};return e(r,0)},pr=F("AsyncFunction"),fr=r=>r&&(Re(r)||P(r))&&P(r.then)&&P(r.catch),it=((r,t)=>r?setImmediate:t?((e,s)=>(Y.addEventListener("message",({source:n,data:o})=>{n===Y&&o===e&&s.length&&s.shift()()},!1),n=>{s.push(n),Y.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e))(typeof setImmediate=="function",P(Y.postMessage)),hr=typeof queueMicrotask<"u"?queueMicrotask.bind(Y):typeof process<"u"&&process.nextTick||it,c={isArray:re,isArrayBuffer:rt,isBuffer:Pt,isFormData:qt,isArrayBufferView:Ot,isString:_t,isNumber:st,isBoolean:It,isObject:Re,isPlainObject:he,isReadableStream:zt,isRequest:Ht,isResponse:Vt,isHeaders:Xt,isUndefined:de,isDate:$t,isFile:Ut,isBlob:Lt,isRegExp:or,isFunction:P,isStream:Bt,isURLSearchParams:Mt,isTypedArray:er,isFileList:Ft,forEach:pe,merge:De,extend:Gt,trim:Jt,stripBOM:Wt,inherits:Yt,toFlatObject:Kt,kindOf:we,kindOfTest:F,endsWith:Qt,toArray:Zt,forEachEntry:tr,matchAll:rr,isHTMLForm:sr,hasOwnProperty:qe,hasOwnProp:qe,reduceDescriptors:at,freezeMethods:ar,toObjectSet:ir,toCamelCase:nr,noop:cr,toFiniteNumber:lr,findKey:nt,global:Y,isContextDefined:ot,isSpecCompliantForm:ur,toJSONObject:dr,isAsyncFn:pr,isThenable:fr,setImmediate:it,asap:hr};function b(r,t,e,s,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=r,this.name="AxiosError",t&&(this.code=t),e&&(this.config=e),s&&(this.request=s),n&&(this.response=n,this.status=n.status?n.status:null)}c.inherits(b,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:c.toJSONObject(this.config),code:this.code,status:this.status}}});const ct=b.prototype,lt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(r=>{lt[r]={value:r}});Object.defineProperties(b,lt);Object.defineProperty(ct,"isAxiosError",{value:!0});b.from=(r,t,e,s,n,o)=>{const a=Object.create(ct);return c.toFlatObject(r,a,function(u){return u!==Error.prototype},i=>i!=="isAxiosError"),b.call(a,r.message,t,e,s,n),a.cause=r,a.name=r.name,o&&Object.assign(a,o),a};const yr=null;function ve(r){return c.isPlainObject(r)||c.isArray(r)}function ut(r){return c.endsWith(r,"[]")?r.slice(0,-2):r}function Me(r,t,e){return r?r.concat(t).map(function(n,o){return n=ut(n),!e&&o?"["+n+"]":n}).join(e?".":""):t}function mr(r){return c.isArray(r)&&!r.some(ve)}const gr=c.toFlatObject(c,{},null,function(t){return/^is[A-Z]/.test(t)});function Se(r,t,e){if(!c.isObject(r))throw new TypeError("target must be an object");t=t||new FormData,e=c.toFlatObject(e,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,m){return!c.isUndefined(m[w])});const s=e.metaTokens,n=e.visitor||p,o=e.dots,a=e.indexes,u=(e.Blob||typeof Blob<"u"&&Blob)&&c.isSpecCompliantForm(t);if(!c.isFunction(n))throw new TypeError("visitor must be a function");function d(y){if(y===null)return"";if(c.isDate(y))return y.toISOString();if(!u&&c.isBlob(y))throw new b("Blob is not supported. Use a Buffer instead.");return c.isArrayBuffer(y)||c.isTypedArray(y)?u&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function p(y,w,m){let T=y;if(y&&!m&&typeof y=="object"){if(c.endsWith(w,"{}"))w=s?w:w.slice(0,-2),y=JSON.stringify(y);else if(c.isArray(y)&&mr(y)||(c.isFileList(y)||c.endsWith(w,"[]"))&&(T=c.toArray(y)))return w=ut(w),T.forEach(function(A,I){!(c.isUndefined(A)||A===null)&&t.append(a===!0?Me([w],I,o):a===null?w:w+"[]",d(A))}),!1}return ve(y)?!0:(t.append(Me(m,w,o),d(y)),!1)}const h=[],E=Object.assign(gr,{defaultVisitor:p,convertValue:d,isVisitable:ve});function x(y,w){if(!c.isUndefined(y)){if(h.indexOf(y)!==-1)throw Error("Circular reference detected in "+w.join("."));h.push(y),c.forEach(y,function(T,N){(!(c.isUndefined(T)||T===null)&&n.call(t,T,c.isString(N)?N.trim():N,w,E))===!0&&x(T,w?w.concat(N):[N])}),h.pop()}}if(!c.isObject(r))throw new TypeError("data must be an object");return x(r),t}function ze(r){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(r).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function $e(r,t){this._pairs=[],r&&Se(r,this,t)}const dt=$e.prototype;dt.append=function(t,e){this._pairs.push([t,e])};dt.toString=function(t){const e=t?function(s){return t.call(this,s,ze)}:ze;return this._pairs.map(function(n){return e(n[0])+"="+e(n[1])},"").join("&")};function xr(r){return encodeURIComponent(r).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function pt(r,t,e){if(!t)return r;const s=e&&e.encode||xr;c.isFunction(e)&&(e={serialize:e});const n=e&&e.serialize;let o;if(n?o=n(t,e):o=c.isURLSearchParams(t)?t.toString():new $e(t,e).toString(s),o){const a=r.indexOf("#");a!==-1&&(r=r.slice(0,a)),r+=(r.indexOf("?")===-1?"?":"&")+o}return r}class He{constructor(){this.handlers=[]}use(t,e,s){return this.handlers.push({fulfilled:t,rejected:e,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){c.forEach(this.handlers,function(s){s!==null&&t(s)})}}const ft={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},wr=typeof URLSearchParams<"u"?URLSearchParams:$e,br=typeof FormData<"u"?FormData:null,Rr=typeof Blob<"u"?Blob:null,Sr={isBrowser:!0,classes:{URLSearchParams:wr,FormData:br,Blob:Rr},protocols:["http","https","file","blob","url","data"]},Ue=typeof window<"u"&&typeof document<"u",Ce=typeof navigator=="object"&&navigator||void 0,Er=Ue&&(!Ce||["ReactNative","NativeScript","NS"].indexOf(Ce.product)<0),Tr=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Nr=Ue&&window.location.href||"http://localhost",Ar=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ue,hasStandardBrowserEnv:Er,hasStandardBrowserWebWorkerEnv:Tr,navigator:Ce,origin:Nr},Symbol.toStringTag,{value:"Module"})),j={...Ar,...Sr};function kr(r,t){return Se(r,new j.classes.URLSearchParams,Object.assign({visitor:function(e,s,n,o){return j.isNode&&c.isBuffer(e)?(this.append(s,e.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function jr(r){return c.matchAll(/\w+|\[(\w*)]/g,r).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Dr(r){const t={},e=Object.keys(r);let s;const n=e.length;let o;for(s=0;s<n;s++)o=e[s],t[o]=r[o];return t}function ht(r){function t(e,s,n,o){let a=e[o++];if(a==="__proto__")return!0;const i=Number.isFinite(+a),u=o>=e.length;return a=!a&&c.isArray(n)?n.length:a,u?(c.hasOwnProp(n,a)?n[a]=[n[a],s]:n[a]=s,!i):((!n[a]||!c.isObject(n[a]))&&(n[a]=[]),t(e,s,n[a],o)&&c.isArray(n[a])&&(n[a]=Dr(n[a])),!i)}if(c.isFormData(r)&&c.isFunction(r.entries)){const e={};return c.forEachEntry(r,(s,n)=>{t(jr(s),n,e,0)}),e}return null}function vr(r,t,e){if(c.isString(r))try{return(t||JSON.parse)(r),c.trim(r)}catch(s){if(s.name!=="SyntaxError")throw s}return(e||JSON.stringify)(r)}const fe={transitional:ft,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){const s=e.getContentType()||"",n=s.indexOf("application/json")>-1,o=c.isObject(t);if(o&&c.isHTMLForm(t)&&(t=new FormData(t)),c.isFormData(t))return n?JSON.stringify(ht(t)):t;if(c.isArrayBuffer(t)||c.isBuffer(t)||c.isStream(t)||c.isFile(t)||c.isBlob(t)||c.isReadableStream(t))return t;if(c.isArrayBufferView(t))return t.buffer;if(c.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let i;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return kr(t,this.formSerializer).toString();if((i=c.isFileList(t))||s.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return Se(i?{"files[]":t}:t,u&&new u,this.formSerializer)}}return o||n?(e.setContentType("application/json",!1),vr(t)):t}],transformResponse:[function(t){const e=this.transitional||fe.transitional,s=e&&e.forcedJSONParsing,n=this.responseType==="json";if(c.isResponse(t)||c.isReadableStream(t))return t;if(t&&c.isString(t)&&(s&&!this.responseType||n)){const a=!(e&&e.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(i){if(a)throw i.name==="SyntaxError"?b.from(i,b.ERR_BAD_RESPONSE,this,null,this.response):i}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:j.classes.FormData,Blob:j.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};c.forEach(["delete","get","head","post","put","patch"],r=>{fe.headers[r]={}});const Cr=c.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Pr=r=>{const t={};let e,s,n;return r&&r.split(`
`).forEach(function(a){n=a.indexOf(":"),e=a.substring(0,n).trim().toLowerCase(),s=a.substring(n+1).trim(),!(!e||t[e]&&Cr[e])&&(e==="set-cookie"?t[e]?t[e].push(s):t[e]=[s]:t[e]=t[e]?t[e]+", "+s:s)}),t},Ve=Symbol("internals");function ce(r){return r&&String(r).trim().toLowerCase()}function ye(r){return r===!1||r==null?r:c.isArray(r)?r.map(ye):String(r)}function Or(r){const t=Object.create(null),e=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=e.exec(r);)t[s[1]]=s[2];return t}const _r=r=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(r.trim());function Ne(r,t,e,s,n){if(c.isFunction(s))return s.call(this,t,e);if(n&&(t=e),!!c.isString(t)){if(c.isString(s))return t.indexOf(s)!==-1;if(c.isRegExp(s))return s.test(t)}}function Ir(r){return r.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,e,s)=>e.toUpperCase()+s)}function $r(r,t){const e=c.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(r,s+e,{value:function(n,o,a){return this[s].call(this,t,n,o,a)},configurable:!0})})}let D=class{constructor(t){t&&this.set(t)}set(t,e,s){const n=this;function o(i,u,d){const p=ce(u);if(!p)throw new Error("header name must be a non-empty string");const h=c.findKey(n,p);(!h||n[h]===void 0||d===!0||d===void 0&&n[h]!==!1)&&(n[h||u]=ye(i))}const a=(i,u)=>c.forEach(i,(d,p)=>o(d,p,u));if(c.isPlainObject(t)||t instanceof this.constructor)a(t,e);else if(c.isString(t)&&(t=t.trim())&&!_r(t))a(Pr(t),e);else if(c.isHeaders(t))for(const[i,u]of t.entries())o(u,i,s);else t!=null&&o(e,t,s);return this}get(t,e){if(t=ce(t),t){const s=c.findKey(this,t);if(s){const n=this[s];if(!e)return n;if(e===!0)return Or(n);if(c.isFunction(e))return e.call(this,n,s);if(c.isRegExp(e))return e.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=ce(t),t){const s=c.findKey(this,t);return!!(s&&this[s]!==void 0&&(!e||Ne(this,this[s],s,e)))}return!1}delete(t,e){const s=this;let n=!1;function o(a){if(a=ce(a),a){const i=c.findKey(s,a);i&&(!e||Ne(s,s[i],i,e))&&(delete s[i],n=!0)}}return c.isArray(t)?t.forEach(o):o(t),n}clear(t){const e=Object.keys(this);let s=e.length,n=!1;for(;s--;){const o=e[s];(!t||Ne(this,this[o],o,t,!0))&&(delete this[o],n=!0)}return n}normalize(t){const e=this,s={};return c.forEach(this,(n,o)=>{const a=c.findKey(s,o);if(a){e[a]=ye(n),delete e[o];return}const i=t?Ir(o):String(o).trim();i!==o&&delete e[o],e[i]=ye(n),s[i]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const e=Object.create(null);return c.forEach(this,(s,n)=>{s!=null&&s!==!1&&(e[n]=t&&c.isArray(s)?s.join(", "):s)}),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,e])=>t+": "+e).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){const s=new this(t);return e.forEach(n=>s.set(n)),s}static accessor(t){const s=(this[Ve]=this[Ve]={accessors:{}}).accessors,n=this.prototype;function o(a){const i=ce(a);s[i]||($r(n,a),s[i]=!0)}return c.isArray(t)?t.forEach(o):o(t),this}};D.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);c.reduceDescriptors(D.prototype,({value:r},t)=>{let e=t[0].toUpperCase()+t.slice(1);return{get:()=>r,set(s){this[e]=s}}});c.freezeMethods(D);function Ae(r,t){const e=this||fe,s=t||e,n=D.from(s.headers);let o=s.data;return c.forEach(r,function(i){o=i.call(e,o,n.normalize(),t?t.status:void 0)}),n.normalize(),o}function yt(r){return!!(r&&r.__CANCEL__)}function se(r,t,e){b.call(this,r??"canceled",b.ERR_CANCELED,t,e),this.name="CanceledError"}c.inherits(se,b,{__CANCEL__:!0});function mt(r,t,e){const s=e.config.validateStatus;!e.status||!s||s(e.status)?r(e):t(new b("Request failed with status code "+e.status,[b.ERR_BAD_REQUEST,b.ERR_BAD_RESPONSE][Math.floor(e.status/100)-4],e.config,e.request,e))}function Ur(r){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(r);return t&&t[1]||""}function Lr(r,t){r=r||10;const e=new Array(r),s=new Array(r);let n=0,o=0,a;return t=t!==void 0?t:1e3,function(u){const d=Date.now(),p=s[o];a||(a=d),e[n]=u,s[n]=d;let h=o,E=0;for(;h!==n;)E+=e[h++],h=h%r;if(n=(n+1)%r,n===o&&(o=(o+1)%r),d-a<t)return;const x=p&&d-p;return x?Math.round(E*1e3/x):void 0}}function Fr(r,t){let e=0,s=1e3/t,n,o;const a=(d,p=Date.now())=>{e=p,n=null,o&&(clearTimeout(o),o=null),r.apply(null,d)};return[(...d)=>{const p=Date.now(),h=p-e;h>=s?a(d,p):(n=d,o||(o=setTimeout(()=>{o=null,a(n)},s-h)))},()=>n&&a(n)]}const ge=(r,t,e=3)=>{let s=0;const n=Lr(50,250);return Fr(o=>{const a=o.loaded,i=o.lengthComputable?o.total:void 0,u=a-s,d=n(u),p=a<=i;s=a;const h={loaded:a,total:i,progress:i?a/i:void 0,bytes:u,rate:d||void 0,estimated:d&&i&&p?(i-a)/d:void 0,event:o,lengthComputable:i!=null,[t?"download":"upload"]:!0};r(h)},e)},Xe=(r,t)=>{const e=r!=null;return[s=>t[0]({lengthComputable:e,total:r,loaded:s}),t[1]]},Je=r=>(...t)=>c.asap(()=>r(...t)),Br=j.hasStandardBrowserEnv?((r,t)=>e=>(e=new URL(e,j.origin),r.protocol===e.protocol&&r.host===e.host&&(t||r.port===e.port)))(new URL(j.origin),j.navigator&&/(msie|trident)/i.test(j.navigator.userAgent)):()=>!0,qr=j.hasStandardBrowserEnv?{write(r,t,e,s,n,o){const a=[r+"="+encodeURIComponent(t)];c.isNumber(e)&&a.push("expires="+new Date(e).toGMTString()),c.isString(s)&&a.push("path="+s),c.isString(n)&&a.push("domain="+n),o===!0&&a.push("secure"),document.cookie=a.join("; ")},read(r){const t=document.cookie.match(new RegExp("(^|;\\s*)("+r+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(r){this.write(r,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Mr(r){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(r)}function zr(r,t){return t?r.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):r}function gt(r,t,e){let s=!Mr(t);return r&&(s||e==!1)?zr(r,t):t}const Ge=r=>r instanceof D?{...r}:r;function Q(r,t){t=t||{};const e={};function s(d,p,h,E){return c.isPlainObject(d)&&c.isPlainObject(p)?c.merge.call({caseless:E},d,p):c.isPlainObject(p)?c.merge({},p):c.isArray(p)?p.slice():p}function n(d,p,h,E){if(c.isUndefined(p)){if(!c.isUndefined(d))return s(void 0,d,h,E)}else return s(d,p,h,E)}function o(d,p){if(!c.isUndefined(p))return s(void 0,p)}function a(d,p){if(c.isUndefined(p)){if(!c.isUndefined(d))return s(void 0,d)}else return s(void 0,p)}function i(d,p,h){if(h in t)return s(d,p);if(h in r)return s(void 0,d)}const u={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:i,headers:(d,p,h)=>n(Ge(d),Ge(p),h,!0)};return c.forEach(Object.keys(Object.assign({},r,t)),function(p){const h=u[p]||n,E=h(r[p],t[p],p);c.isUndefined(E)&&h!==i||(e[p]=E)}),e}const xt=r=>{const t=Q({},r);let{data:e,withXSRFToken:s,xsrfHeaderName:n,xsrfCookieName:o,headers:a,auth:i}=t;t.headers=a=D.from(a),t.url=pt(gt(t.baseURL,t.url,t.allowAbsoluteUrls),r.params,r.paramsSerializer),i&&a.set("Authorization","Basic "+btoa((i.username||"")+":"+(i.password?unescape(encodeURIComponent(i.password)):"")));let u;if(c.isFormData(e)){if(j.hasStandardBrowserEnv||j.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((u=a.getContentType())!==!1){const[d,...p]=u?u.split(";").map(h=>h.trim()).filter(Boolean):[];a.setContentType([d||"multipart/form-data",...p].join("; "))}}if(j.hasStandardBrowserEnv&&(s&&c.isFunction(s)&&(s=s(t)),s||s!==!1&&Br(t.url))){const d=n&&o&&qr.read(o);d&&a.set(n,d)}return t},Hr=typeof XMLHttpRequest<"u",Vr=Hr&&function(r){return new Promise(function(e,s){const n=xt(r);let o=n.data;const a=D.from(n.headers).normalize();let{responseType:i,onUploadProgress:u,onDownloadProgress:d}=n,p,h,E,x,y;function w(){x&&x(),y&&y(),n.cancelToken&&n.cancelToken.unsubscribe(p),n.signal&&n.signal.removeEventListener("abort",p)}let m=new XMLHttpRequest;m.open(n.method.toUpperCase(),n.url,!0),m.timeout=n.timeout;function T(){if(!m)return;const A=D.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),k={data:!i||i==="text"||i==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:A,config:r,request:m};mt(function(g){e(g),w()},function(g){s(g),w()},k),m=null}"onloadend"in m?m.onloadend=T:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(T)},m.onabort=function(){m&&(s(new b("Request aborted",b.ECONNABORTED,r,m)),m=null)},m.onerror=function(){s(new b("Network Error",b.ERR_NETWORK,r,m)),m=null},m.ontimeout=function(){let I=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const k=n.transitional||ft;n.timeoutErrorMessage&&(I=n.timeoutErrorMessage),s(new b(I,k.clarifyTimeoutError?b.ETIMEDOUT:b.ECONNABORTED,r,m)),m=null},o===void 0&&a.setContentType(null),"setRequestHeader"in m&&c.forEach(a.toJSON(),function(I,k){m.setRequestHeader(k,I)}),c.isUndefined(n.withCredentials)||(m.withCredentials=!!n.withCredentials),i&&i!=="json"&&(m.responseType=n.responseType),d&&([E,y]=ge(d,!0),m.addEventListener("progress",E)),u&&m.upload&&([h,x]=ge(u),m.upload.addEventListener("progress",h),m.upload.addEventListener("loadend",x)),(n.cancelToken||n.signal)&&(p=A=>{m&&(s(!A||A.type?new se(null,r,m):A),m.abort(),m=null)},n.cancelToken&&n.cancelToken.subscribe(p),n.signal&&(n.signal.aborted?p():n.signal.addEventListener("abort",p)));const N=Ur(n.url);if(N&&j.protocols.indexOf(N)===-1){s(new b("Unsupported protocol "+N+":",b.ERR_BAD_REQUEST,r));return}m.send(o||null)})},Xr=(r,t)=>{const{length:e}=r=r?r.filter(Boolean):[];if(t||e){let s=new AbortController,n;const o=function(d){if(!n){n=!0,i();const p=d instanceof Error?d:this.reason;s.abort(p instanceof b?p:new se(p instanceof Error?p.message:p))}};let a=t&&setTimeout(()=>{a=null,o(new b(`timeout ${t} of ms exceeded`,b.ETIMEDOUT))},t);const i=()=>{r&&(a&&clearTimeout(a),a=null,r.forEach(d=>{d.unsubscribe?d.unsubscribe(o):d.removeEventListener("abort",o)}),r=null)};r.forEach(d=>d.addEventListener("abort",o));const{signal:u}=s;return u.unsubscribe=()=>c.asap(i),u}},Jr=function*(r,t){let e=r.byteLength;if(e<t){yield r;return}let s=0,n;for(;s<e;)n=s+t,yield r.slice(s,n),s=n},Gr=async function*(r,t){for await(const e of Wr(r))yield*Jr(e,t)},Wr=async function*(r){if(r[Symbol.asyncIterator]){yield*r;return}const t=r.getReader();try{for(;;){const{done:e,value:s}=await t.read();if(e)break;yield s}}finally{await t.cancel()}},We=(r,t,e,s)=>{const n=Gr(r,t);let o=0,a,i=u=>{a||(a=!0,s&&s(u))};return new ReadableStream({async pull(u){try{const{done:d,value:p}=await n.next();if(d){i(),u.close();return}let h=p.byteLength;if(e){let E=o+=h;e(E)}u.enqueue(new Uint8Array(p))}catch(d){throw i(d),d}},cancel(u){return i(u),n.return()}},{highWaterMark:2})},Ee=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",wt=Ee&&typeof ReadableStream=="function",Yr=Ee&&(typeof TextEncoder=="function"?(r=>t=>r.encode(t))(new TextEncoder):async r=>new Uint8Array(await new Response(r).arrayBuffer())),bt=(r,...t)=>{try{return!!r(...t)}catch{return!1}},Kr=wt&&bt(()=>{let r=!1;const t=new Request(j.origin,{body:new ReadableStream,method:"POST",get duplex(){return r=!0,"half"}}).headers.has("Content-Type");return r&&!t}),Ye=64*1024,Pe=wt&&bt(()=>c.isReadableStream(new Response("").body)),xe={stream:Pe&&(r=>r.body)};Ee&&(r=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!xe[t]&&(xe[t]=c.isFunction(r[t])?e=>e[t]():(e,s)=>{throw new b(`Response type '${t}' is not supported`,b.ERR_NOT_SUPPORT,s)})})})(new Response);const Qr=async r=>{if(r==null)return 0;if(c.isBlob(r))return r.size;if(c.isSpecCompliantForm(r))return(await new Request(j.origin,{method:"POST",body:r}).arrayBuffer()).byteLength;if(c.isArrayBufferView(r)||c.isArrayBuffer(r))return r.byteLength;if(c.isURLSearchParams(r)&&(r=r+""),c.isString(r))return(await Yr(r)).byteLength},Zr=async(r,t)=>{const e=c.toFiniteNumber(r.getContentLength());return e??Qr(t)},es=Ee&&(async r=>{let{url:t,method:e,data:s,signal:n,cancelToken:o,timeout:a,onDownloadProgress:i,onUploadProgress:u,responseType:d,headers:p,withCredentials:h="same-origin",fetchOptions:E}=xt(r);d=d?(d+"").toLowerCase():"text";let x=Xr([n,o&&o.toAbortSignal()],a),y;const w=x&&x.unsubscribe&&(()=>{x.unsubscribe()});let m;try{if(u&&Kr&&e!=="get"&&e!=="head"&&(m=await Zr(p,s))!==0){let k=new Request(t,{method:"POST",body:s,duplex:"half"}),B;if(c.isFormData(s)&&(B=k.headers.get("content-type"))&&p.setContentType(B),k.body){const[g,f]=Xe(m,ge(Je(u)));s=We(k.body,Ye,g,f)}}c.isString(h)||(h=h?"include":"omit");const T="credentials"in Request.prototype;y=new Request(t,{...E,signal:x,method:e.toUpperCase(),headers:p.normalize().toJSON(),body:s,duplex:"half",credentials:T?h:void 0});let N=await fetch(y);const A=Pe&&(d==="stream"||d==="response");if(Pe&&(i||A&&w)){const k={};["status","statusText","headers"].forEach(z=>{k[z]=N[z]});const B=c.toFiniteNumber(N.headers.get("content-length")),[g,f]=i&&Xe(B,ge(Je(i),!0))||[];N=new Response(We(N.body,Ye,g,()=>{f&&f(),w&&w()}),k)}d=d||"text";let I=await xe[c.findKey(xe,d)||"text"](N,r);return!A&&w&&w(),await new Promise((k,B)=>{mt(k,B,{data:I,headers:D.from(N.headers),status:N.status,statusText:N.statusText,config:r,request:y})})}catch(T){throw w&&w(),T&&T.name==="TypeError"&&/fetch/i.test(T.message)?Object.assign(new b("Network Error",b.ERR_NETWORK,r,y),{cause:T.cause||T}):b.from(T,T&&T.code,r,y)}}),Oe={http:yr,xhr:Vr,fetch:es};c.forEach(Oe,(r,t)=>{if(r){try{Object.defineProperty(r,"name",{value:t})}catch{}Object.defineProperty(r,"adapterName",{value:t})}});const Ke=r=>`- ${r}`,ts=r=>c.isFunction(r)||r===null||r===!1,Rt={getAdapter:r=>{r=c.isArray(r)?r:[r];const{length:t}=r;let e,s;const n={};for(let o=0;o<t;o++){e=r[o];let a;if(s=e,!ts(e)&&(s=Oe[(a=String(e)).toLowerCase()],s===void 0))throw new b(`Unknown adapter '${a}'`);if(s)break;n[a||"#"+o]=s}if(!s){const o=Object.entries(n).map(([i,u])=>`adapter ${i} `+(u===!1?"is not supported by the environment":"is not available in the build"));let a=t?o.length>1?`since :
`+o.map(Ke).join(`
`):" "+Ke(o[0]):"as no adapter specified";throw new b("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return s},adapters:Oe};function ke(r){if(r.cancelToken&&r.cancelToken.throwIfRequested(),r.signal&&r.signal.aborted)throw new se(null,r)}function Qe(r){return ke(r),r.headers=D.from(r.headers),r.data=Ae.call(r,r.transformRequest),["post","put","patch"].indexOf(r.method)!==-1&&r.headers.setContentType("application/x-www-form-urlencoded",!1),Rt.getAdapter(r.adapter||fe.adapter)(r).then(function(s){return ke(r),s.data=Ae.call(r,r.transformResponse,s),s.headers=D.from(s.headers),s},function(s){return yt(s)||(ke(r),s&&s.response&&(s.response.data=Ae.call(r,r.transformResponse,s.response),s.response.headers=D.from(s.response.headers))),Promise.reject(s)})}const St="1.8.4",Te={};["object","boolean","number","function","string","symbol"].forEach((r,t)=>{Te[r]=function(s){return typeof s===r||"a"+(t<1?"n ":" ")+r}});const Ze={};Te.transitional=function(t,e,s){function n(o,a){return"[Axios v"+St+"] Transitional option '"+o+"'"+a+(s?". "+s:"")}return(o,a,i)=>{if(t===!1)throw new b(n(a," has been removed"+(e?" in "+e:"")),b.ERR_DEPRECATED);return e&&!Ze[a]&&(Ze[a]=!0,console.warn(n(a," has been deprecated since v"+e+" and will be removed in the near future"))),t?t(o,a,i):!0}};Te.spelling=function(t){return(e,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function rs(r,t,e){if(typeof r!="object")throw new b("options must be an object",b.ERR_BAD_OPTION_VALUE);const s=Object.keys(r);let n=s.length;for(;n-- >0;){const o=s[n],a=t[o];if(a){const i=r[o],u=i===void 0||a(i,o,r);if(u!==!0)throw new b("option "+o+" must be "+u,b.ERR_BAD_OPTION_VALUE);continue}if(e!==!0)throw new b("Unknown option "+o,b.ERR_BAD_OPTION)}}const me={assertOptions:rs,validators:Te},M=me.validators;let K=class{constructor(t){this.defaults=t,this.interceptors={request:new He,response:new He}}async request(t,e){try{return await this._request(t,e)}catch(s){if(s instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const o=n.stack?n.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,e){typeof t=="string"?(e=e||{},e.url=t):e=t||{},e=Q(this.defaults,e);const{transitional:s,paramsSerializer:n,headers:o}=e;s!==void 0&&me.assertOptions(s,{silentJSONParsing:M.transitional(M.boolean),forcedJSONParsing:M.transitional(M.boolean),clarifyTimeoutError:M.transitional(M.boolean)},!1),n!=null&&(c.isFunction(n)?e.paramsSerializer={serialize:n}:me.assertOptions(n,{encode:M.function,serialize:M.function},!0)),e.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),me.assertOptions(e,{baseUrl:M.spelling("baseURL"),withXsrfToken:M.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let a=o&&c.merge(o.common,o[e.method]);o&&c.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),e.headers=D.concat(a,o);const i=[];let u=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(e)===!1||(u=u&&w.synchronous,i.unshift(w.fulfilled,w.rejected))});const d=[];this.interceptors.response.forEach(function(w){d.push(w.fulfilled,w.rejected)});let p,h=0,E;if(!u){const y=[Qe.bind(this),void 0];for(y.unshift.apply(y,i),y.push.apply(y,d),E=y.length,p=Promise.resolve(e);h<E;)p=p.then(y[h++],y[h++]);return p}E=i.length;let x=e;for(h=0;h<E;){const y=i[h++],w=i[h++];try{x=y(x)}catch(m){w.call(this,m);break}}try{p=Qe.call(this,x)}catch(y){return Promise.reject(y)}for(h=0,E=d.length;h<E;)p=p.then(d[h++],d[h++]);return p}getUri(t){t=Q(this.defaults,t);const e=gt(t.baseURL,t.url,t.allowAbsoluteUrls);return pt(e,t.params,t.paramsSerializer)}};c.forEach(["delete","get","head","options"],function(t){K.prototype[t]=function(e,s){return this.request(Q(s||{},{method:t,url:e,data:(s||{}).data}))}});c.forEach(["post","put","patch"],function(t){function e(s){return function(o,a,i){return this.request(Q(i||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:a}))}}K.prototype[t]=e(),K.prototype[t+"Form"]=e(!0)});let ss=class Et{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let e;this.promise=new Promise(function(o){e=o});const s=this;this.promise.then(n=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](n);s._listeners=null}),this.promise.then=n=>{let o;const a=new Promise(i=>{s.subscribe(i),o=i}).then(n);return a.cancel=function(){s.unsubscribe(o)},a},t(function(o,a,i){s.reason||(s.reason=new se(o,a,i),e(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const e=this._listeners.indexOf(t);e!==-1&&this._listeners.splice(e,1)}toAbortSignal(){const t=new AbortController,e=s=>{t.abort(s)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let t;return{token:new Et(function(n){t=n}),cancel:t}}};function ns(r){return function(e){return r.apply(null,e)}}function os(r){return c.isObject(r)&&r.isAxiosError===!0}const _e={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(_e).forEach(([r,t])=>{_e[t]=r});function Tt(r){const t=new K(r),e=tt(K.prototype.request,t);return c.extend(e,K.prototype,t,{allOwnKeys:!0}),c.extend(e,t,null,{allOwnKeys:!0}),e.create=function(n){return Tt(Q(r,n))},e}const R=Tt(fe);R.Axios=K;R.CanceledError=se;R.CancelToken=ss;R.isCancel=yt;R.VERSION=St;R.toFormData=Se;R.AxiosError=b;R.Cancel=R.CanceledError;R.all=function(t){return Promise.all(t)};R.spread=ns;R.isAxiosError=os;R.mergeConfig=Q;R.AxiosHeaders=D;R.formToJSON=r=>ht(c.isHTMLForm(r)?new FormData(r):r);R.getAdapter=Rt.getAdapter;R.HttpStatusCode=_e;R.default=R;const{Axios:ws,AxiosError:bs,CanceledError:Rs,isCancel:Ss,CancelToken:Es,VERSION:Ts,all:Ns,Cancel:As,isAxiosError:ks,spread:js,toFormData:Ds,AxiosHeaders:vs,HttpStatusCode:Cs,formToJSON:Ps,getAdapter:Os,mergeConfig:_s}=R;class as{constructor(){this.proxyUrls=["https://corsproxy.io/?","https://cors-anywhere.herokuapp.com/","https://api.allorigins.win/raw?url="],this.currentProxyIndex=0}getNextProxy(){const t=this.proxyUrls[this.currentProxyIndex];return this.currentProxyIndex=(this.currentProxyIndex+1)%this.proxyUrls.length,t}async get(t,e={},s={}){try{return(await R.get(t,{headers:e,params:s})).data}catch(n){console.log("Direct request failed, trying proxy...",n.message);let o=n;for(let a=0;a<this.proxyUrls.length;a++)try{const u=this.getNextProxy()+encodeURIComponent(t);return(await R.get(u,{headers:e,params:s})).data}catch(i){o=i,console.log(`Proxy ${a+1} failed, trying next...`,i.message)}throw o}}async post(t,e={},s={}){try{return(await R.post(t,e,{headers:s})).data}catch(n){console.log("Direct request failed, trying proxy...",n.message);let o=n;for(let a=0;a<this.proxyUrls.length;a++)try{const u=this.getNextProxy()+encodeURIComponent(t);return(await R.post(u,e,{headers:s})).data}catch(i){o=i,console.log(`Proxy ${a+1} failed, trying next...`,i.message)}throw o}}}const S=new as,X="https://otx.alienvault.com/api/v1",is=r=>{const t=R.create({baseURL:X,headers:{"X-OTX-API-KEY":r,"Content-Type":"application/json"}});return{getPulses:async(e=10)=>{try{try{return(await t.get(`/pulses/subscribed?limit=${e}`)).data.results}catch{console.log("Direct OTX pulses request failed, trying proxy...");const n=`${X}/pulses/subscribed`,o={"X-OTX-API-KEY":r,"Content-Type":"application/json"},a={limit:e};return(await S.get(n,o,a)).results}}catch(s){throw console.error("Error fetching OTX pulses:",s),s}},getPulseIndicators:async e=>{try{try{return(await t.get(`/pulses/${e}/indicators`)).data}catch{console.log(`Direct OTX pulse indicators request for ${e} failed, trying proxy...`);const n=`${X}/pulses/${e}/indicators`,o={"X-OTX-API-KEY":r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching indicators for pulse ${e}:`,s),s}},getIPReputation:async e=>{try{try{return(await t.get(`/indicators/IPv4/${e}/general`)).data}catch{console.log(`Direct OTX IP reputation request for ${e} failed, trying proxy...`);const n=`${X}/indicators/IPv4/${e}/general`,o={"X-OTX-API-KEY":r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching IP reputation for ${e}:`,s),s}},getDomainReputation:async e=>{try{try{return(await t.get(`/indicators/domain/${e}/general`)).data}catch{console.log(`Direct OTX domain reputation request for ${e} failed, trying proxy...`);const n=`${X}/indicators/domain/${e}/general`,o={"X-OTX-API-KEY":r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching domain reputation for ${e}:`,s),s}},getURLReputation:async e=>{try{const s=encodeURIComponent(e);try{return(await t.get(`/indicators/url/${s}/general`)).data}catch{console.log(`Direct OTX URL reputation request for ${e} failed, trying proxy...`);const o=`${X}/indicators/url/${s}/general`,a={"X-OTX-API-KEY":r,"Content-Type":"application/json"};return await S.get(o,a)}}catch(s){throw console.error(`Error fetching URL reputation for ${e}:`,s),s}},getFileReputation:async(e,s="sha256")=>{try{try{return(await t.get(`/indicators/file/${e}/${s}`)).data}catch{console.log(`Direct OTX file reputation request for ${e} failed, trying proxy...`);const o=`${X}/indicators/file/${e}/${s}`,a={"X-OTX-API-KEY":r,"Content-Type":"application/json"};return await S.get(o,a)}}catch(n){throw console.error(`Error fetching file reputation for ${e}:`,n),n}},searchIndicators:async e=>{try{const s=encodeURIComponent(e);try{return(await t.get(`/search/indicators?q=${s}`)).data.results}catch{console.log(`Direct OTX search request for ${e} failed, trying proxy...`);const o=`${X}/search/indicators`,a={"X-OTX-API-KEY":r,"Content-Type":"application/json"},i={q:e};return(await S.get(o,a,i)).results}}catch(s){throw console.error(`Error searching indicators for ${e}:`,s),s}}}},je="https://api.abuseipdb.com/api/v2",cs=r=>{const t=R.create({baseURL:je,headers:{Key:r,Accept:"application/json"}});return{checkIP:async(e,s=90)=>{try{try{return(await t.get("/check",{params:{ipAddress:e,maxAgeInDays:s,verbose:!0}})).data.data}catch{console.log("Direct AbuseIPDB request failed, trying proxy...");const o=`${je}/check`,a={Key:r,Accept:"application/json"},i={ipAddress:e,maxAgeInDays:s,verbose:!0};return(await S.get(o,a,i)).data}}catch(n){throw console.error(`Error checking IP ${e}:`,n),n}},getBlacklist:async(e=90,s=25)=>{try{try{return(await t.get("/blacklist",{params:{confidenceMinimum:e,limit:s}})).data.data}catch{console.log("Direct AbuseIPDB blacklist request failed, trying proxy...");const o=`${je}/blacklist`,a={Key:r,Accept:"application/json"},i={confidenceMinimum:e,limit:s};return(await S.get(o,a,i)).data}}catch(n){throw console.error("Error fetching blacklist:",n),n}},reportIP:async(e,s,n)=>{try{return(await t.post("/report",{ip:e,categories:s,comment:n})).data}catch(o){throw console.error(`Error reporting IP ${e}:`,o),o}},checkBulk:async(e,s=90)=>{try{return(await t.post("/check-block",{ipAddress:e.join(","),maxAgeInDays:s})).data}catch(n){throw console.error("Error performing bulk IP check:",n),n}}}},ee="https://services.nvd.nist.gov/rest/json/cves/2.0",ls=r=>{const t=R.create({baseURL:ee,headers:{apiKey:r,"Content-Type":"application/json"}});return{searchVulnerabilities:async(e={})=>{try{try{return(await t.get("",{params:e})).data}catch{console.log("Direct NVD request failed, trying proxy...");const n=ee,o={apiKey:r,"Content-Type":"application/json"};return await S.get(n,o,e)}}catch(s){throw console.error("Error fetching vulnerability data:",s),s}},getCVEDetails:async e=>{var s,n;try{try{return((s=(await t.get("",{params:{cveId:e}})).data.vulnerabilities)==null?void 0:s[0])||null}catch{console.log(`Direct NVD request for ${e} failed, trying proxy...`);const a=ee,i={apiKey:r,"Content-Type":"application/json"},u={cveId:e};return((n=(await S.get(a,i,u)).vulnerabilities)==null?void 0:n[0])||null}}catch(o){throw console.error(`Error fetching details for ${e}:`,o),o}},searchByKeyword:async(e,s=10,n=0)=>{try{try{return(await t.get("",{params:{keywordSearch:e,resultsPerPage:s,startIndex:n}})).data}catch{console.log(`Direct NVD keyword search for "${e}" failed, trying proxy...`);const a=ee,i={apiKey:r,"Content-Type":"application/json"},u={keywordSearch:e,resultsPerPage:s,startIndex:n};return await S.get(a,i,u)}}catch(o){throw console.error(`Error searching for "${e}":`,o),o}},getRecentVulnerabilities:async(e=30,s=20)=>{try{const n=new Date;n.setDate(n.getDate()-e);const o=n.toISOString().split("T")[0]+"T00:00:00.000";try{return(await t.get("",{params:{pubStartDate:o,resultsPerPage:s}})).data}catch{console.log("Direct NVD recent vulnerabilities request failed, trying proxy...");const i=ee,u={apiKey:r,"Content-Type":"application/json"},d={pubStartDate:o,resultsPerPage:s};return await S.get(i,u,d)}}catch(n){throw console.error("Error fetching recent vulnerabilities:",n),n}},getVulnerabilitiesByScore:async(e=7,s=10,n=20)=>{try{try{return(await t.get("",{params:{cvssV3Severity:et(e),resultsPerPage:n}})).data}catch{console.log("Direct NVD score-based request failed, trying proxy...");const a=ee,i={apiKey:r,"Content-Type":"application/json"},u={cvssV3Severity:et(e),resultsPerPage:n};return await S.get(a,i,u)}}catch(o){throw console.error("Error fetching vulnerabilities by score range:",o),o}}}},et=r=>r>=9?"CRITICAL":r>=7?"HIGH":r>=4?"MEDIUM":"LOW",le="https://www.virustotal.com/api/v3",us=r=>{const t=R.create({baseURL:le,headers:{"x-apikey":r,"Content-Type":"application/json"}});return{getFileReport:async e=>{try{try{return(await t.get(`/files/${e}`)).data}catch{console.log(`Direct VirusTotal file report request for ${e} failed, trying proxy...`);const n=`${le}/files/${e}`,o={"x-apikey":r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching file report for ${e}:`,s),s}},getUrlReport:async e=>{try{const s=Buffer.from(e).toString("base64").replace(/\\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");try{return(await t.get(`/urls/${s}`)).data}catch{console.log("Direct VirusTotal URL report request failed, trying proxy...");const o=`${le}/urls/${s}`,a={"x-apikey":r,"Content-Type":"application/json"};return await S.get(o,a)}}catch(s){throw console.error("Error fetching URL report:",s),s}},getDomainReport:async e=>{try{try{return(await t.get(`/domains/${e}`)).data}catch{console.log(`Direct VirusTotal domain report request for ${e} failed, trying proxy...`);const n=`${le}/domains/${e}`,o={"x-apikey":r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching domain report for ${e}:`,s),s}},getIpReport:async e=>{try{try{return(await t.get(`/ip_addresses/${e}`)).data}catch{console.log(`Direct VirusTotal IP report request for ${e} failed, trying proxy...`);const n=`${le}/ip_addresses/${e}`,o={"x-apikey":r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching IP report for ${e}:`,s),s}},scanUrl:async e=>{try{return(await t.post("/urls",`url=${encodeURIComponent(e)}`,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data}catch(s){throw console.error("Error submitting URL for scanning:",s),s}},getComments:async(e,s)=>{try{let n;switch(s){case"file":n=`/files/${e}/comments`;break;case"url":n=`/urls/${Buffer.from(e).toString("base64").replace(/\\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}/comments`;break;case"domain":n=`/domains/${e}/comments`;break;case"ip_address":n=`/ip_addresses/${e}/comments`;break;default:throw new Error("Invalid resource type")}return(await t.get(n)).data}catch(n){throw console.error("Error fetching comments:",n),n}}}},C="https://api.shodan.io",ds=r=>({search:async(t,e=1)=>{try{try{return(await R.get(`${C}/shodan/host/search`,{params:{key:r,query:t,page:e}})).data}catch{console.log("Direct Shodan search request failed, trying proxy...");const n=`${C}/shodan/host/search`,o={key:r,query:t,page:e};return await S.get(n,{},o)}}catch(s){throw console.error("Error searching Shodan:",s),s}},getHostInfo:async t=>{try{try{return(await R.get(`${C}/shodan/host/${t}`,{params:{key:r}})).data}catch{console.log(`Direct Shodan host info request for ${t} failed, trying proxy...`);const s=`${C}/shodan/host/${t}`,n={key:r};return await S.get(s,{},n)}}catch(e){throw console.error(`Error fetching host info for ${t}:`,e),e}},getApiInfo:async()=>{try{return(await R.get(`${C}/api-info`,{params:{key:r}})).data}catch(t){throw console.error("Error fetching API info:",t),t}},getServices:async()=>{try{return(await R.get(`${C}/shodan/services`,{params:{key:r}})).data}catch(t){throw console.error("Error fetching Shodan services:",t),t}},getQuerySummary:async t=>{try{try{return(await R.get(`${C}/shodan/host/count`,{params:{key:r,query:t}})).data}catch{console.log("Direct Shodan query summary request failed, trying proxy...");const s=`${C}/shodan/host/count`,n={key:r,query:t};return await S.get(s,{},n)}}catch(e){throw console.error("Error fetching query summary:",e),e}},searchCertificates:async(t,e=1)=>{try{try{return(await R.get(`${C}/shodan/certs/search`,{params:{key:r,query:t,page:e}})).data}catch{console.log("Direct Shodan certificate search request failed, trying proxy...");const n=`${C}/shodan/certs/search`,o={key:r,query:t,page:e};return await S.get(n,{},o)}}catch(s){throw console.error("Error searching certificates:",s),s}},getPorts:async()=>{try{return(await R.get(`${C}/shodan/ports`,{params:{key:r}})).data}catch(t){throw console.error("Error fetching Shodan ports:",t),t}},getDomainInfo:async t=>{try{try{return(await R.get(`${C}/dns/domain/${t}`,{params:{key:r}})).data}catch{console.log(`Direct Shodan domain info request for ${t} failed, trying proxy...`);const s=`${C}/dns/domain/${t}`,n={key:r};return await S.get(s,{},n)}}catch(e){throw console.error(`Error fetching domain info for ${t}:`,e),e}}}),J="https://api.greynoise.io/v3",ps=r=>{const t=R.create({baseURL:J,headers:{key:r,"Content-Type":"application/json"}});return{getNoiseStatus:async e=>{try{try{return(await t.get(`/noise/quick/${e}`)).data}catch{console.log(`Direct GreyNoise noise status request for ${e} failed, trying proxy...`);const n=`${J}/noise/quick/${e}`,o={key:r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching noise status for ${e}:`,s),s}},getRiotStatus:async e=>{try{try{return(await t.get(`/riot/${e}`)).data}catch{console.log(`Direct GreyNoise RIOT status request for ${e} failed, trying proxy...`);const n=`${J}/riot/${e}`,o={key:r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching RIOT status for ${e}:`,s),s}},getCommunityContext:async e=>{try{try{return(await t.get(`/community/${e}`)).data}catch{console.log(`Direct GreyNoise community context request for ${e} failed, trying proxy...`);const n=`${J}/community/${e}`,o={key:r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching community context for ${e}:`,s),s}},getIPContext:async e=>{try{try{return(await t.get(`/ip/${e}`)).data}catch{console.log(`Direct GreyNoise IP context request for ${e} failed, trying proxy...`);const n=`${J}/ip/${e}`,o={key:r,"Content-Type":"application/json"};return await S.get(n,o)}}catch(s){throw console.error(`Error fetching IP context for ${e}:`,s),s}},queryGNQL:async(e,s=10,n=null)=>{try{const o={query:e,size:s};n&&(o.scroll=n);try{return(await t.get("/experimental/gnql",{params:o})).data}catch{console.log("Direct GreyNoise GNQL query request failed, trying proxy...");const i=`${J}/experimental/gnql`,u={key:r,"Content-Type":"application/json"};return await S.get(i,u,o)}}catch(o){throw console.error("Error executing GNQL query:",o),o}},getStats:async(e,s="classification")=>{try{try{return(await t.get("/experimental/gnql/stats",{params:{query:e,stat:s}})).data}catch{console.log("Direct GreyNoise stats request failed, trying proxy...");const o=`${J}/experimental/gnql/stats`,a={key:r,"Content-Type":"application/json"},i={query:e,stat:s};return await S.get(o,a,i)}}catch(n){throw console.error("Error fetching stats:",n),n}},getTimeline:async e=>{try{try{return(await t.get("/experimental/gnql/timeline",{params:{query:e}})).data}catch{console.log("Direct GreyNoise timeline request failed, trying proxy...");const n=`${J}/experimental/gnql/timeline`,o={key:r,"Content-Type":"application/json"},a={query:e};return await S.get(n,o,a)}}catch(s){throw console.error("Error fetching timeline:",s),s}}}};var te={};class fs{constructor(){this.services={},this.initialized=!1}initialize(){if(this.initialized)return this.services;const t=te.REACT_APP_OTX_API_KEY||"437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0",e=te.REACT_APP_ABUSEIPDB_API_KEY||"********************************************************************************",s=te.REACT_APP_NVD_API_KEY||void 0||"5e3ecbad-e117-4fad-9a19-983e63edfb67",n=te.REACT_APP_VIRUSTOTAL_API_KEY||void 0||"****************************************************************",o=te.REACT_APP_SHODAN_API_KEY||void 0||"********************************",a=te.REACT_APP_GREYNOISE_API_KEY||void 0||"dX0AK56JO7Vvh1BLbuUI3zxgK6gFIVZ21zHeHVz4wEOTXpZBvtqS8F2TzJDwNy2F";return this.services.otx=is(t),this.services.abuseIPDB=cs(e),this.services.nvd=ls(s),this.services.virusTotal=us(n),this.services.shodan=ds(o),this.services.greyNoise=ps(a),this.initialized=!0,this.services}getService(t){return this.initialized||this.initialize(),this.services[t]||null}getAllServices(){return this.initialized||this.initialize(),this.services}isServiceAvailable(t){return this.initialized||this.initialize(),!!this.services[t]}}const hs=new fs;class ys{constructor(){this.initialized=!1,this.services={}}async initialize(){this.initialized||(this.services=hs.initialize(),this.initialized=!0)}async correlateIPData(t){this.initialized||await this.initialize();const e={ip:t,sources:[],riskScore:0,riskFactors:[],geoData:null,relatedIndicators:[],analysisTimestamp:new Date().toISOString()};try{if(this.services.abuseIPDB){const s=await this.services.abuseIPDB.checkIP(t);if(s){e.sources.push("AbuseIPDB"),e.geoData={countryCode:s.countryCode,countryName:s.countryName,isp:s.isp,domain:s.domain,usageType:s.usageType};const n=s.abuseConfidenceScore||0;e.riskScore+=n*.7,n>80?e.riskFactors.push("High abuse confidence score"):n>50&&e.riskFactors.push("Medium abuse confidence score"),s.totalReports>10&&(e.riskFactors.push("Multiple abuse reports"),e.riskScore+=10)}}if(this.services.otx){const s=await this.services.otx.getIPReputation(t);if(s){if(e.sources.push("AlienVault OTX"),s.pulse_info&&s.pulse_info.count>0){const n=s.pulse_info.count;e.riskScore+=Math.min(n*5,30),n>5&&e.riskFactors.push("Associated with multiple threat intelligence reports"),s.pulse_info.pulses&&s.pulse_info.pulses.forEach(o=>{o.indicators&&o.indicators.forEach(a=>{a.type!=="IPv4"&&a.type!=="IPv6"&&e.relatedIndicators.push({type:a.type,indicator:a.indicator,title:o.name,created:o.created})})})}if(s.reputation&&s.reputation.reputation_score){const n=s.reputation.reputation_score;e.riskScore+=(100-n)*.3,n<20&&e.riskFactors.push("Poor reputation score")}}}return e.riskScore=Math.min(Math.round(e.riskScore),100),e.riskScore>=75?e.riskLevel="Critical":e.riskScore>=50?e.riskLevel="High":e.riskScore>=25?e.riskLevel="Medium":e.riskLevel="Low",e}catch(s){throw console.error(`Error correlating data for IP ${t}:`,s),s}}async analyzeThreatCluster(t){this.initialized||await this.initialize();const e={analyzedCount:t.length,highRiskCount:0,countryClusters:{},ispClusters:{},highRiskIPs:[],analysisTimestamp:new Date().toISOString()};try{const s=t.map(o=>this.correlateIPData(o));return(await Promise.all(s)).forEach(o=>{var a;if(o.riskScore>=50&&(e.highRiskCount++,e.highRiskIPs.push({ip:o.ip,riskScore:o.riskScore,riskLevel:o.riskLevel,countryName:((a=o.geoData)==null?void 0:a.countryName)||"Unknown"})),o.geoData&&o.geoData.countryName){const i=o.geoData.countryName;e.countryClusters[i]||(e.countryClusters[i]={count:0,avgRiskScore:0,ips:[]}),e.countryClusters[i].count++,e.countryClusters[i].avgRiskScore=(e.countryClusters[i].avgRiskScore*(e.countryClusters[i].count-1)+o.riskScore)/e.countryClusters[i].count,e.countryClusters[i].ips.push(o.ip)}if(o.geoData&&o.geoData.isp){const i=o.geoData.isp;e.ispClusters[i]||(e.ispClusters[i]={count:0,avgRiskScore:0,ips:[]}),e.ispClusters[i].count++,e.ispClusters[i].avgRiskScore=(e.ispClusters[i].avgRiskScore*(e.ispClusters[i].count-1)+o.riskScore)/e.ispClusters[i].count,e.ispClusters[i].ips.push(o.ip)}}),e.topCountries=Object.entries(e.countryClusters).sort((o,a)=>a[1].count-o[1].count).slice(0,5).map(([o,a])=>({country:o,count:a.count,avgRiskScore:Math.round(a.avgRiskScore)})),e.topISPs=Object.entries(e.ispClusters).sort((o,a)=>a[1].count-o[1].count).slice(0,5).map(([o,a])=>({isp:o,count:a.count,avgRiskScore:Math.round(a.avgRiskScore)})),e}catch(s){throw console.error("Error analyzing threat cluster:",s),s}}async generateThreatReport(t={}){this.initialized||await this.initialize();const e={title:t.title||"Threat Intelligence Report",generatedAt:new Date().toISOString(),summary:{totalThreats:0,criticalThreats:0,highThreats:0,mediumThreats:0,lowThreats:0},topThreats:[],geographicDistribution:{},attackVectors:{},recommendations:[]};try{if(this.services.abuseIPDB){const s=await this.services.abuseIPDB.getBlacklist(70,50);if(s&&s.length>0){const n=await this.analyzeThreatCluster(s.map(o=>o.ipAddress));e.summary.totalThreats=s.length,e.summary.criticalThreats=n.highRiskIPs.filter(o=>o.riskLevel==="Critical").length,e.summary.highThreats=n.highRiskIPs.filter(o=>o.riskLevel==="High").length,e.topThreats=n.highRiskIPs.sort((o,a)=>a.riskScore-o.riskScore).slice(0,10),e.geographicDistribution=n.topCountries.reduce((o,a)=>(o[a.country]=a.count,o),{})}}if(this.services.otx){const s=await this.services.otx.getPulses(20);if(s&&s.length>0){const n={};s.forEach(a=>{a.tags&&a.tags.forEach(i=>{let u=i.toLowerCase();if(u.includes("ransomware"))u="Ransomware";else if(u.includes("phish"))u="Phishing";else if(u.includes("malware"))u="Malware";else if(u.includes("ddos"))u="DDoS";else if(u.includes("exploit"))u="Exploit";else if(u.includes("backdoor"))u="Backdoor";else if(u.includes("trojan"))u="Trojan";else if(u.includes("botnet"))u="Botnet";else return;n[u]=(n[u]||0)+1})}),e.attackVectors=Object.entries(n).sort((a,i)=>i[1]-a[1]).reduce((a,[i,u])=>(a[i]=u,a),{});const o=Object.keys(e.attackVectors).slice(0,3);o.includes("Ransomware")&&e.recommendations.push("Implement regular backup procedures and test recovery processes","Deploy endpoint protection with anti-ransomware capabilities","Segment networks to limit lateral movement"),o.includes("Phishing")&&e.recommendations.push("Conduct regular phishing awareness training","Implement email filtering solutions","Deploy multi-factor authentication"),o.includes("Exploit")&&e.recommendations.push("Establish a robust patch management process","Conduct regular vulnerability scanning","Implement a web application firewall"),(o.includes("Malware")||o.includes("Trojan"))&&e.recommendations.push("Deploy advanced endpoint protection","Implement application whitelisting","Conduct regular system scans"),(o.includes("DDoS")||o.includes("Botnet"))&&e.recommendations.push("Implement DDoS protection services","Configure network rate limiting","Develop a DDoS response plan")}}return e}catch(s){throw console.error("Error generating threat report:",s),s}}async analyzeDomain(t){var s;this.initialized||await this.initialize();const e={domain:t,sources:[],riskScore:0,riskFactors:[],relatedIndicators:[],analysisTimestamp:new Date().toISOString()};try{if(this.services.otx){const n=await this.services.otx.getDomainReputation(t);if(n){if(e.sources.push("AlienVault OTX"),n.pulse_info&&n.pulse_info.count>0){const o=n.pulse_info.count;e.riskScore+=Math.min(o*5,30),o>5&&e.riskFactors.push("Associated with multiple threat intelligence reports"),n.pulse_info.pulses&&n.pulse_info.pulses.forEach(a=>{a.indicators&&a.indicators.forEach(i=>{i.type!=="domain"&&i.indicator!==t&&e.relatedIndicators.push({type:i.type,indicator:i.indicator,title:a.name,created:a.created})})})}if(n.passive_dns&&n.passive_dns.length>0&&(e.passiveDNS=n.passive_dns.map(a=>({address:a.address,firstSeen:a.first,lastSeen:a.last})),n.passive_dns.filter(a=>{const i=new Date(a.first);return(new Date-i)/(1e3*60*60*24)<30}).length>5&&(e.riskFactors.push("Multiple recent DNS records (potential DGA)"),e.riskScore+=15)),n.whois&&(e.whois={creationDate:n.whois.creation_date,expirationDate:n.whois.expiration_date,updatedDate:n.whois.updated_date,registrar:n.whois.registrar,nameServers:n.whois.nameservers},n.whois.creation_date)){const o=new Date(n.whois.creation_date);(new Date-o)/(1e3*60*60*24)<30&&(e.riskFactors.push("Recently registered domain"),e.riskScore+=20)}n.malware&&n.malware.count>0&&(e.riskFactors.push("Associated with malware"),e.riskScore+=25,e.malware={count:n.malware.count,samples:((s=n.malware.samples)==null?void 0:s.map(o=>({hash:o.hash,date:o.date,name:o.name})))||[]}),n.url_list&&n.url_list.url_list&&n.url_list.url_list.length>0&&(e.urls=n.url_list.url_list.map(o=>({url:o.url,date:o.date,domain:o.domain})))}}return e.riskScore=Math.min(Math.round(e.riskScore),100),e.riskScore>=75?e.riskLevel="Critical":e.riskScore>=50?e.riskLevel="High":e.riskScore>=25?e.riskLevel="Medium":e.riskLevel="Low",e}catch(n){throw console.error(`Error analyzing domain ${t}:`,n),n}}}const ue=new ys,Is=()=>{const{darkMode:r}=jt(),[t,e]=_.useState(!0),[s,n]=_.useState(null),[o,a]=_.useState(null),[i,u]=_.useState(null),[d,p]=_.useState(null),[h,E]=_.useState(""),[x,y]=_.useState(null),[w,m]=_.useState("ip"),T=_.useRef(null),N=_.useRef(null);_.useEffect(()=>{const g=async()=>{try{e(!0),n(null),await ue.initialize();const f=await ue.generateThreatReport({title:"Current Threat Landscape"});a(f)}catch(f){console.error("Error loading threat analytics data:",f),f.message&&f.message.includes("Network Error")?n("Network error connecting to threat intelligence APIs. Using proxy to retry connection..."):f.response&&f.response.status===403?n("API access forbidden. Please check your API keys and try again."):f.response&&f.response.status===429?n("API rate limit exceeded. Please wait a moment and try again."):n("Unable to connect to threat analytics API. Retrying connection..."),setTimeout(()=>{o||(n("Retrying connection to threat intelligence APIs..."),g())},5e3)}finally{e(!1)}};g()},[]),_.useEffect(()=>{o&&T.current&&A(),o&&N.current&&I()},[o,r]);const A=()=>{const g=T.current,f=g.getContext("2d");f.clearRect(0,0,g.width,g.height);const z=g.width,H=g.height,G=40,ne=20,oe=H-60,W=Object.entries(o.attackVectors).sort((q,$)=>$[1]-q[1]).slice(0,5);if(W.length===0)return;const ae=Math.max(...W.map(([q,$])=>$));W.forEach(([q,$],ie)=>{const V=60+ie*(G+ne),Z=$/ae*oe,v=H-40-Z,U=f.createLinearGradient(V,v,V,H-40);let O,L;switch(q){case"Ransomware":O="#ff0066",L="#990033";break;case"Phishing":O="#3366ff",L="#003399";break;case"Malware":O="#ff9900",L="#cc6600";break;case"DDoS":O="#00cc99",L="#006633";break;case"Exploit":O="#cc33ff",L="#660099";break;default:O="#999999",L="#666666"}U.addColorStop(0,O),U.addColorStop(1,L),f.fillStyle=U,f.beginPath(),f.roundRect(V,v,G,Z,5),f.fill(),f.fillStyle=r?"#ffffff":"#000000",f.font="12px Arial",f.textAlign="center",f.fillText($,V+G/2,v-5),f.fillStyle=r?"#cccccc":"#333333",f.font="10px Arial",f.fillText(q,V+G/2,H-20)}),f.fillStyle=r?"#ffffff":"#000000",f.font="bold 14px Arial",f.textAlign="center",f.fillText("Top Attack Vectors",z/2,20)},I=()=>{const g=N.current,f=g.getContext("2d");f.clearRect(0,0,g.width,g.height);const z=g.width,H=g.height,G=Math.min(z,H)/2-40,ne=z/2,oe=H/2,W=Object.entries(o.geographicDistribution).sort((v,U)=>U[1]-v[1]);if(W.length===0)return;const ae=W.reduce((v,[U,O])=>v+O,0),q=["#ff3366","#3366ff","#33cc33","#ff9900","#9966ff","#ff6666","#66ccff","#99cc00","#ffcc00","#cc66ff"];let $=0;const ie=[];W.forEach(([v,U],O)=>{const L=U/ae*2*Math.PI,Le=$+L,Fe=$+L/2;if(f.fillStyle=q[O%q.length],f.beginPath(),f.moveTo(ne,oe),f.arc(ne,oe,G,$,Le),f.closePath(),f.fill(),L>.2){const Nt=Math.round(U/ae*100),Be=G*.7,At=ne+Math.cos(Fe)*Be,kt=oe+Math.sin(Fe)*Be;f.fillStyle="#ffffff",f.font="bold 12px Arial",f.textAlign="center",f.textBaseline="middle",f.fillText(`${Nt}%`,At,kt)}ie.push({country:v,value:U,percent:Math.round(U/ae*100),color:q[O%q.length]}),$=Le});const V=20;let Z=H-20-ie.length*20;ie.forEach(v=>{f.fillStyle=v.color,f.fillRect(V,Z,15,15),f.fillStyle=r?"#ffffff":"#000000",f.font="12px Arial",f.textAlign="left",f.textBaseline="middle",f.fillText(`${v.country} (${v.percent}%)`,V+20,Z+7),Z+=20}),f.fillStyle=r?"#ffffff":"#000000",f.font="bold 14px Arial",f.textAlign="center",f.fillText("Geographic Distribution",z/2,20)},k=async g=>{try{u(g),e(!0),n(null);const f=await ue.correlateIPData(g);p(f)}catch(f){console.error(`Error analyzing IP ${g}:`,f),n(`Failed to analyze IP ${g}.`),p(null)}finally{e(!1)}},B=async g=>{if(g.preventDefault(),!!h.trim())try{if(e(!0),n(null),w==="ip"){const f=await ue.correlateIPData(h);y({type:"ip",data:f})}else{const f=await ue.analyzeDomain(h);y({type:"domain",data:f})}}catch(f){console.error(`Error searching for "${h}":`,f),n(`Failed to analyze ${w==="ip"?"IP":"domain"} "${h}".`),y(null)}finally{e(!1)}};return t&&!o?l.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:l.jsx("div",{className:"flex justify-center items-center h-64",children:l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})})}):s&&!o?l.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[l.jsx("h2",{className:"text-xl font-bold mb-4",children:"Advanced Threat Analytics"}),l.jsx("p",{className:"text-gray-400 mb-4",children:"In-depth analysis of threat data from multiple intelligence sources."}),l.jsxs("div",{className:"bg-blue-900 bg-opacity-20 border border-blue-800 rounded-lg p-4 mb-4 text-blue-400",children:[l.jsx(Dt,{className:"inline-block mr-2"}),s]}),l.jsx("div",{className:"flex justify-center mt-6",children:l.jsxs("div",{className:"animate-pulse flex space-x-4 items-center",children:[l.jsx("div",{className:"rounded-full bg-blue-700 h-3 w-3"}),l.jsx("div",{className:"rounded-full bg-blue-700 h-3 w-3 animate-animation-delay-200"}),l.jsx("div",{className:"rounded-full bg-blue-700 h-3 w-3 animate-animation-delay-400"})]})})]}):l.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsx("h2",{className:"text-xl font-bold",children:"Threat Analytics Dashboard"}),l.jsxs("form",{onSubmit:B,className:"flex",children:[l.jsx("div",{className:"mr-2",children:l.jsxs("select",{className:"bg-gray-700 border border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",value:w,onChange:g=>m(g.target.value),children:[l.jsx("option",{value:"ip",children:"IP"}),l.jsx("option",{value:"domain",children:"Domain"})]})}),l.jsx("input",{type:"text",placeholder:`Search ${w==="ip"?"IP address":"domain"}...`,className:"bg-gray-700 border border-gray-600 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",value:h,onChange:g=>E(g.target.value)}),l.jsxs("button",{type:"submit",className:"bg-blue-600 hover:bg-blue-700 rounded-r px-4 py-2 flex items-center",disabled:t,children:[l.jsx(vt,{className:"mr-2"}),"Analyze"]})]})]}),x&&l.jsxs("div",{className:"mb-6 bg-gray-700 rounded-lg p-4",children:[l.jsxs("h3",{className:"text-lg font-semibold mb-3",children:["Analysis Results: ",x.type==="ip"?"IP Address":"Domain"," ",h]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[l.jsxs("div",{className:"bg-gray-800 p-3 rounded",children:[l.jsx("div",{className:"text-sm text-gray-400",children:"Risk Score"}),l.jsxs("div",{className:"text-2xl font-bold",children:[x.data.riskScore,"/100"]}),l.jsxs("div",{className:`text-sm ${x.data.riskLevel==="Critical"?"text-red-400":x.data.riskLevel==="High"?"text-orange-400":x.data.riskLevel==="Medium"?"text-yellow-400":"text-green-400"}`,children:[x.data.riskLevel," Risk"]})]}),l.jsxs("div",{className:"bg-gray-800 p-3 rounded",children:[l.jsx("div",{className:"text-sm text-gray-400",children:"Data Sources"}),l.jsx("div",{className:"flex flex-wrap gap-2 mt-1",children:x.data.sources.map((g,f)=>l.jsx("span",{className:"bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded",children:g},f))})]}),l.jsxs("div",{className:"bg-gray-800 p-3 rounded",children:[l.jsx("div",{className:"text-sm text-gray-400",children:"Analysis Time"}),l.jsx("div",{children:new Date(x.data.analysisTimestamp).toLocaleString()})]})]}),x.data.riskFactors.length>0&&l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-semibold mb-2",children:"Risk Factors"}),l.jsx("ul",{className:"list-disc list-inside space-y-1 pl-2",children:x.data.riskFactors.map((g,f)=>l.jsx("li",{className:"text-yellow-300",children:g},f))})]}),x.type==="ip"&&x.data.geoData&&l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-semibold mb-2",children:"Geolocation Data"}),l.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:[l.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[l.jsx("div",{className:"text-xs text-gray-400",children:"Country"}),l.jsx("div",{children:x.data.geoData.countryName||"Unknown"})]}),l.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[l.jsx("div",{className:"text-xs text-gray-400",children:"ISP"}),l.jsx("div",{children:x.data.geoData.isp||"Unknown"})]}),l.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[l.jsx("div",{className:"text-xs text-gray-400",children:"Domain"}),l.jsx("div",{children:x.data.geoData.domain||"Unknown"})]}),l.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[l.jsx("div",{className:"text-xs text-gray-400",children:"Usage Type"}),l.jsx("div",{children:x.data.geoData.usageType||"Unknown"})]})]})]}),x.type==="domain"&&x.data.whois&&l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-semibold mb-2",children:"WHOIS Data"}),l.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:[l.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[l.jsx("div",{className:"text-xs text-gray-400",children:"Creation Date"}),l.jsx("div",{children:x.data.whois.creationDate?new Date(x.data.whois.creationDate).toLocaleDateString():"Unknown"})]}),l.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[l.jsx("div",{className:"text-xs text-gray-400",children:"Expiration Date"}),l.jsx("div",{children:x.data.whois.expirationDate?new Date(x.data.whois.expirationDate).toLocaleDateString():"Unknown"})]}),l.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[l.jsx("div",{className:"text-xs text-gray-400",children:"Registrar"}),l.jsx("div",{children:x.data.whois.registrar||"Unknown"})]})]})]}),x.data.relatedIndicators&&x.data.relatedIndicators.length>0&&l.jsxs("div",{children:[l.jsx("h4",{className:"font-semibold mb-2",children:"Related Indicators"}),l.jsx("div",{className:"max-h-40 overflow-y-auto",children:l.jsxs("table",{className:"w-full",children:[l.jsx("thead",{className:"bg-gray-800",children:l.jsxs("tr",{children:[l.jsx("th",{className:"px-2 py-1 text-left text-xs",children:"Type"}),l.jsx("th",{className:"px-2 py-1 text-left text-xs",children:"Indicator"}),l.jsx("th",{className:"px-2 py-1 text-left text-xs",children:"Source"})]})}),l.jsx("tbody",{children:x.data.relatedIndicators.slice(0,10).map((g,f)=>l.jsxs("tr",{className:"border-t border-gray-700",children:[l.jsx("td",{className:"px-2 py-1 text-xs",children:g.type}),l.jsx("td",{className:"px-2 py-1 text-xs font-mono",children:g.indicator}),l.jsx("td",{className:"px-2 py-1 text-xs",children:g.title})]},f))})]})})]})]}),o&&l.jsxs("div",{className:"mb-6",children:[l.jsx("h3",{className:"text-lg font-semibold mb-3",children:o.title}),l.jsxs("p",{className:"text-sm text-gray-400 mb-4",children:["Generated on ",new Date(o.generatedAt).toLocaleString()]}),l.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-6",children:[l.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[l.jsx("div",{className:"text-sm text-gray-400",children:"Total Threats"}),l.jsx("div",{className:"text-2xl font-bold",children:o.summary.totalThreats})]}),l.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[l.jsx("div",{className:"text-sm text-gray-400",children:"Critical"}),l.jsx("div",{className:"text-2xl font-bold text-red-400",children:o.summary.criticalThreats})]}),l.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[l.jsx("div",{className:"text-sm text-gray-400",children:"High"}),l.jsx("div",{className:"text-2xl font-bold text-orange-400",children:o.summary.highThreats})]}),l.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[l.jsx("div",{className:"text-sm text-gray-400",children:"Medium"}),l.jsx("div",{className:"text-2xl font-bold text-yellow-400",children:o.summary.mediumThreats})]}),l.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[l.jsx("div",{className:"text-sm text-gray-400",children:"Low"}),l.jsx("div",{className:"text-2xl font-bold text-green-400",children:o.summary.lowThreats})]})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[l.jsx("div",{className:"bg-gray-700 p-4 rounded-lg",children:l.jsx("canvas",{ref:T,width:"400",height:"250",className:"w-full"})}),l.jsx("div",{className:"bg-gray-700 p-4 rounded-lg",children:l.jsx("canvas",{ref:N,width:"400",height:"250",className:"w-full"})})]})]}),o&&o.topThreats&&o.topThreats.length>0&&l.jsxs("div",{className:"mb-6",children:[l.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Top Threats"}),l.jsx("div",{className:"bg-gray-700 rounded-lg overflow-hidden",children:l.jsxs("table",{className:"w-full",children:[l.jsx("thead",{className:"bg-gray-800",children:l.jsxs("tr",{children:[l.jsx("th",{className:"px-4 py-2 text-left",children:"IP Address"}),l.jsx("th",{className:"px-4 py-2 text-left",children:"Risk Score"}),l.jsx("th",{className:"px-4 py-2 text-left",children:"Risk Level"}),l.jsx("th",{className:"px-4 py-2 text-left",children:"Country"}),l.jsx("th",{className:"px-4 py-2 text-left",children:"Actions"})]})}),l.jsx("tbody",{children:o.topThreats.map((g,f)=>l.jsxs("tr",{className:`border-t border-gray-600 hover:bg-gray-600 cursor-pointer ${i===g.ip?"bg-gray-600":""}`,children:[l.jsx("td",{className:"px-4 py-2 font-mono",children:g.ip}),l.jsxs("td",{className:"px-4 py-2",children:[g.riskScore,"/100"]}),l.jsx("td",{className:"px-4 py-2",children:l.jsx("span",{className:`px-2 py-1 rounded text-xs ${g.riskLevel==="Critical"?"bg-red-900 text-red-200":g.riskLevel==="High"?"bg-orange-900 text-orange-200":g.riskLevel==="Medium"?"bg-yellow-900 text-yellow-200":"bg-green-900 text-green-200"}`,children:g.riskLevel})}),l.jsx("td",{className:"px-4 py-2",children:g.countryName}),l.jsx("td",{className:"px-4 py-2",children:l.jsx("button",{onClick:()=>k(g.ip),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs",children:"Analyze"})})]},f))})]})})]}),o&&o.recommendations&&o.recommendations.length>0&&l.jsxs("div",{className:"mb-6",children:[l.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Security Recommendations"}),l.jsx("div",{className:"bg-gray-700 p-4 rounded-lg",children:l.jsx("ul",{className:"list-disc list-inside space-y-2 pl-2",children:o.recommendations.map((g,f)=>l.jsx("li",{className:"text-blue-300",children:g},f))})})]})]})};export{Is as T,R as a,is as b,cs as c,hs as d,ue as t};
