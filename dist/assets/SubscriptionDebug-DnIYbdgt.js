import{u as p,g as h,r as l,j as e,ah as o}from"./index-c6UceSOv.js";const v=()=>{const n=p(),{subscriptionLevel:u,subscription:x}=h(),[d,b]=l.useState(u),[c,r]=l.useState(""),a=t=>{try{const s=localStorage.getItem("user_subscription");let i=s?JSON.parse(s):{};i.tier=t,localStorage.setItem("user_subscription",JSON.stringify(i)),b(t),r(`Subscription level set to ${t}. Please refresh the page or navigate to the dashboard.`)}catch(s){console.error("Error setting subscription level:",s),r(`Error: ${s.message}`)}},m=()=>{try{const t={id:"mock-user-id",email:"<EMAIL>",user_metadata:{username:"dev_user",full_name:"Development User"}},s={user_id:t.id,tier:o.PREMIUM,coins:500,start_date:new Date().toISOString(),end_date:new Date(Date.now()+30*24*60*60*1e3).toISOString(),is_active:!0};localStorage.setItem("supabase.auth.user",JSON.stringify(t)),localStorage.setItem("supabase.auth.token","mock-token"),localStorage.setItem("user_subscription",JSON.stringify(s)),r("Mock user created with Premium subscription. Please refresh the page.")}catch(t){console.error("Error creating mock user:",t),r(`Error: ${t.message}`)}},g=()=>{n("/dashboard")};return e.jsx("div",{className:"min-h-screen bg-[#0B1120] text-white flex items-center justify-center",children:e.jsxs("div",{className:"bg-[#1A1F35] border border-gray-800 rounded-lg p-8 max-w-md w-full",children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Subscription Debug"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("p",{className:"text-gray-400 mb-2",children:"Current Subscription Level:"}),e.jsx("p",{className:"text-xl font-bold text-[#88cc14]",children:d})]}),e.jsxs("div",{className:"space-y-4 mb-8",children:[e.jsx("button",{onClick:()=>a(o.FREE),className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg",children:"Set to Free"}),e.jsx("button",{onClick:()=>a(o.PREMIUM),className:"w-full px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg",children:"Set to Premium"}),e.jsx("button",{onClick:()=>a(o.BUSINESS),className:"w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg",children:"Set to Business"})]}),e.jsx("div",{className:"mb-8",children:e.jsx("button",{onClick:m,className:"w-full px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg",children:"Create Mock User (Premium)"})}),e.jsx("div",{className:"mb-8",children:e.jsx("button",{onClick:g,className:"w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg",children:"Go to Dashboard"})}),c&&e.jsx("div",{className:"p-4 bg-gray-800 rounded-lg text-center",children:c})]})})};export{v as default};
