import{j as e,m as b,g as h,ag as u,l as g,V as m,d as f}from"./index-c6UceSOv.js";function j({children:s,className:l="",hover:t=!0,animate:n=!0,glow:i=!1,accent:a=!1,accentColor:o="green",...r}){const x={green:"bg-[#88cc14]",blue:"bg-blue-500",red:"bg-red-500",yellow:"bg-yellow-500",purple:"bg-purple-500",orange:"bg-orange-500"},c=`
    bg-[#1A1F35]
    border border-gray-800
    rounded-xl overflow-hidden
    ${t?"hover:border-[#88cc14]/50 transition-colors duration-300":""}
    ${i?"shadow-lg shadow-[#88cc14]/10":""}
    ${l}
  `,d=e.jsxs(e.Fragment,{children:[a&&e.jsx("div",{className:`h-1 ${x[o]}`}),e.jsx("div",{className:"p-6",children:s})]});return n?e.jsx(b.div,{className:c,whileHover:t?{y:-5}:{},transition:{duration:.3},...r,children:d}):e.jsx("div",{className:c,...r,children:d})}const N=({title:s,description:l,image:t,type:n="subscription",feature:i,requiredTier:a="premium",difficulty:o,to:r})=>{const{requiresCoins:x,getItemCost:c}=h();return e.jsx(b.div,{whileHover:{y:-5},transition:{duration:.3},children:e.jsx(u,{to:r||"/pricing",children:e.jsxs(j,{className:"h-full relative overflow-hidden group",hover:!1,animate:!1,children:[e.jsx("div",{className:"absolute inset-0 bg-black/70 backdrop-blur-sm flex flex-col items-center justify-center z-10 p-4",children:n==="subscription"?e.jsxs(e.Fragment,{children:[e.jsx(g,{className:"text-[#88cc14] text-4xl mb-3"}),e.jsx("h3",{className:"text-xl font-bold text-white mb-1",children:s}),e.jsx("p",{className:"text-gray-300 text-sm mb-3 text-center",children:l}),e.jsxs("span",{className:"bg-[#88cc14] text-black px-3 py-1 rounded-full text-sm font-bold",children:[a.charAt(0).toUpperCase()+a.slice(1)," Required"]})]}):e.jsxs(e.Fragment,{children:[e.jsx(m,{className:"text-yellow-500 text-4xl mb-3"}),e.jsx("h3",{className:"text-xl font-bold text-white mb-1",children:s}),e.jsx("p",{className:"text-gray-300 text-sm mb-3 text-center",children:l}),e.jsxs("span",{className:"bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold flex items-center gap-1",children:[e.jsx(m,{className:"text-sm"}),c("challenge",o)," Coins"]})]})}),e.jsxs("div",{className:"relative h-40 overflow-hidden",children:[t?e.jsx("img",{src:t,alt:s,className:"w-full h-full object-cover filter blur-sm opacity-30"}):e.jsx("div",{className:"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 filter blur-sm opacity-30"}),e.jsx("div",{className:"absolute top-2 right-2 bg-black/50 p-1 rounded-full",children:e.jsx(f,{className:"text-white"})})]}),e.jsxs("div",{className:"p-4 filter blur-sm opacity-30",children:[e.jsx("h3",{className:"font-bold text-lg mb-1 text-white",children:s}),e.jsx("p",{className:"text-gray-400 text-sm line-clamp-2",children:l})]})]})})})};export{N as L};
