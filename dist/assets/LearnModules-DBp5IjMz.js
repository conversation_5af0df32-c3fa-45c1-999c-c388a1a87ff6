import{r as x,g as b,j as e,ag as r,I as j,az as w,n as v,D as N,aD as S,ae as C}from"./index-c6UceSOv.js";import{U as L}from"./UpgradeBanner-Bw_AdR7a.js";import{L as A}from"./LockedItem-BNePvFlE.js";import"./Button-jOBCH0E8.js";const T=()=>{const[i,o]=x.useState(""),[a,c]=x.useState("all"),{hasAccess:k,getAvailableCount:p,isPremium:d,isBusiness:u}=b(),m=t=>{const s=p("learnModules");return t<s||d||u},h=[{id:1,title:"Web Requests",category:"web",difficulty:"Easy",progress:100,completed:!0,description:"Learn how HTTP requests work and how to manipulate them.",duration:"2 hours",rating:4.8,tags:["HTTP","Web","Basics"]},{id:2,title:"Introduction to Web Applications",category:"web",difficulty:"Fundamental",progress:45,completed:!1,description:"Understand the architecture and components of modern web applications.",duration:"3 hours",rating:4.6,tags:["Web","Architecture","Basics"]},{id:3,title:"Using Web Proxies",category:"web",difficulty:"Easy",progress:0,completed:!1,description:"Learn how to use web proxies to intercept and modify HTTP traffic.",duration:"2.5 hours",rating:4.7,tags:["Proxy","Burp Suite","Web"]},{id:4,title:"JavaScript Deobfuscation",category:"web",difficulty:"Medium",progress:0,completed:!1,description:"Techniques to deobfuscate and analyze JavaScript code.",duration:"4 hours",rating:4.5,tags:["JavaScript","Web","Code Analysis"]},{id:5,title:"SQL Injection Fundamentals",category:"web",difficulty:"Medium",progress:0,completed:!1,description:"Learn how to identify and exploit SQL injection vulnerabilities.",duration:"5 hours",rating:4.9,tags:["SQL","Web","Injection"]},{id:6,title:"Cross-Site Scripting (XSS)",category:"web",difficulty:"Medium",progress:0,completed:!1,description:"Understanding and exploiting XSS vulnerabilities.",duration:"4 hours",rating:4.7,tags:["XSS","Web","JavaScript"]},{id:7,title:"Network Traffic Analysis",category:"network",difficulty:"Medium",progress:0,completed:!1,description:"Learn how to analyze network traffic using Wireshark and other tools.",duration:"6 hours",rating:4.6,tags:["Network","Wireshark","Analysis"]},{id:8,title:"Active Directory Basics",category:"windows",difficulty:"Medium",progress:0,completed:!1,description:"Understanding Active Directory structure and common misconfigurations.",duration:"5 hours",rating:4.8,tags:["Windows","Active Directory","Authentication"]},{id:9,title:"Linux Privilege Escalation",category:"linux",difficulty:"Hard",progress:0,completed:!1,description:"Techniques to escalate privileges on Linux systems.",duration:"7 hours",rating:4.9,tags:["Linux","Privilege Escalation","Post-Exploitation"]},{id:10,title:"Cryptography Fundamentals",category:"crypto",difficulty:"Medium",progress:0,completed:!1,description:"Learn the basics of cryptography and common encryption algorithms.",duration:"4 hours",rating:4.5,tags:["Cryptography","Encryption","Security"]}],f=[{id:"all",label:"All Modules"},{id:"web",label:"Web Security"},{id:"network",label:"Network Security"},{id:"windows",label:"Windows"},{id:"linux",label:"Linux"},{id:"crypto",label:"Cryptography"}],g=h.filter(t=>{const s=a==="all"||t.category===a,n=t.title.toLowerCase().includes(i.toLowerCase())||t.description.toLowerCase().includes(i.toLowerCase())||t.tags.some(l=>l.toLowerCase().includes(i.toLowerCase()));return s&&n}),y=t=>{switch(t){case"Fundamental":return"bg-blue-500/20 text-blue-500";case"Easy":return"bg-green-500/20 text-green-500";case"Medium":return"bg-yellow-500/20 text-yellow-500";case"Hard":return"bg-red-500/20 text-red-500";default:return"bg-gray-500/20 text-gray-500"}};return e.jsx("div",{className:"min-h-screen bg-[#0B1120] text-white",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Learning Modules"}),e.jsx("p",{className:"text-gray-400",children:"Master cybersecurity skills with our comprehensive learning modules"})]}),e.jsxs("div",{className:"mt-4 md:mt-0 flex items-center gap-2",children:[e.jsx(r,{to:"/learn/paths",className:"bg-[#1A1F35] hover:bg-[#252D4A] text-white px-4 py-2 rounded-md transition-colors",children:"View Career Paths"}),e.jsx(r,{to:"/learn/certifications",className:"bg-[#88cc14] hover:bg-[#7ab811] text-black px-4 py-2 rounded-md font-medium transition-colors",children:"Certifications"})]})]}),!d&&!u&&e.jsx(L,{targetTier:"premium",message:"Upgrade to Premium to unlock all 50 learning modules and accelerate your cybersecurity journey",showClose:!0}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-8",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(j,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"}),e.jsx("input",{type:"text",placeholder:"Search modules...",value:i,onChange:t=>o(t.target.value),className:"w-full bg-[#1A1F35] border border-gray-800 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-[#88cc14]"})]}),e.jsx("div",{className:"flex overflow-x-auto pb-2 gap-2",children:f.map(t=>e.jsx("button",{onClick:()=>c(t.id),className:`px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${a===t.id?"bg-[#88cc14] text-black font-medium":"bg-[#1A1F35] text-gray-400 hover:bg-[#252D4A] hover:text-white"}`,children:t.label},t.id))})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map((t,s)=>m(s)?e.jsxs("div",{className:"bg-[#1A1F35] rounded-lg overflow-hidden border border-gray-800 hover:border-[#88cc14]/50 transition-colors",children:[e.jsxs("div",{className:"p-4 border-b border-gray-800",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h3",{className:"text-lg font-bold",children:t.title}),e.jsx("span",{className:`px-2 py-0.5 rounded text-xs ${y(t.difficulty)}`,children:t.difficulty})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-4 line-clamp-2",children:t.description}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:t.tags.map((n,l)=>e.jsx("span",{className:"px-2 py-0.5 bg-[#0B1120] text-gray-400 rounded text-xs",children:n},l))}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-400",children:[e.jsxs("span",{children:["Duration: ",t.duration]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(w,{className:"text-yellow-500 text-xs"}),e.jsx("span",{children:t.rating})]})]})]}),e.jsx("div",{className:"p-4 bg-[#0F172A]",children:t.completed?e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center gap-2 text-green-500",children:[e.jsx(v,{}),e.jsx("span",{children:"Completed"})]}),e.jsxs(r,{to:`/learn/modules/${t.id}`,className:"text-[#88cc14] hover:underline flex items-center gap-1",children:["Review ",e.jsx(N,{className:"text-xs"})]})]}):t.progress>0?e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"text-gray-400",children:"Progress"}),e.jsxs("span",{className:"text-[#88cc14]",children:[t.progress,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-800 rounded-full h-2 mb-2",children:e.jsx("div",{className:"bg-[#88cc14] h-2 rounded-full",style:{width:`${t.progress}%`}})}),e.jsxs(r,{to:`/learn/modules/${t.id}`,className:"flex items-center justify-center gap-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium py-2 rounded-lg transition-colors w-full",children:[e.jsx(S,{className:"text-xs"}),"Continue"]})]}):e.jsxs(r,{to:`/learn/modules/${t.id}`,className:"flex items-center justify-center gap-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium py-2 rounded-lg transition-colors w-full",children:[e.jsx(C,{className:"text-xs"}),"Start Learning"]})})]},t.id):e.jsx(A,{title:t.title,description:t.description,type:"subscription",feature:"learnModules",requiredTier:"premium",to:"/pricing"},t.id))}),g.length===0&&e.jsxs("div",{className:"bg-[#1A1F35] rounded-lg p-8 text-center",children:[e.jsx("div",{className:"text-gray-400 mb-2",children:"No modules found matching your criteria"}),e.jsx("button",{onClick:()=>{o(""),c("all")},className:"text-[#88cc14] hover:underline",children:"Clear filters"})]})]})})};export{T as default};
