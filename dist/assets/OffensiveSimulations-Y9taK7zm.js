import{au as g,r as h,j as e,x as o,m as r,ag as n,z as l,am as p,Q as f,D as b,G as y,H as v,ad as w}from"./index-c6UceSOv.js";import{C as j}from"./CyberForceSEO-CGlWp3aL.js";const S=()=>{const{darkMode:t}=g(),[a,d]=h.useState(null),c=[{id:"web-app-pentest",title:"Web Application Penetration Testing",description:"Identify and exploit vulnerabilities in web applications",icon:e.jsx(y,{className:"text-red-500"}),difficulty:"Beginner to Advanced",duration:"1-3 hours",skills:["OWASP Top 10","SQL Injection","XSS","CSRF","Authentication Bypass"],color:"from-red-500/20 to-red-600/5",borderColor:"border-red-500/20",image:"https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80"},{id:"network-pentest",title:"Network Penetration Testing",description:"Discover and exploit vulnerabilities in network infrastructure",icon:e.jsx(v,{className:"text-orange-500"}),difficulty:"Intermediate",duration:"2-4 hours",skills:["Network Scanning","Service Enumeration","Vulnerability Assessment","Exploitation"],color:"from-orange-500/20 to-orange-600/5",borderColor:"border-orange-500/20",image:"https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80"},{id:"wireless-security",title:"Wireless Security Assessment",description:"Test the security of wireless networks and identify vulnerabilities",icon:e.jsx(w,{className:"text-blue-500"}),difficulty:"Intermediate",duration:"1-2 hours",skills:["WiFi Encryption","WPA/WPA2 Cracking","Evil Twin Attacks","Client-Side Attacks"],color:"from-blue-500/20 to-blue-600/5",borderColor:"border-blue-500/20",image:"https://images.unsplash.com/photo-1562408590-e32931084e23?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80"},{id:"social-engineering",title:"Social Engineering Scenarios",description:"Practice ethical social engineering techniques and defenses",icon:e.jsx(l,{className:"text-purple-500"}),difficulty:"Beginner to Advanced",duration:"1-3 hours",skills:["Phishing","Pretexting","Baiting","Psychological Manipulation"],color:"from-purple-500/20 to-purple-600/5",borderColor:"border-purple-500/20",image:"https://images.unsplash.com/photo-1563237023-b1e970526dcb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1742&q=80"}],x=[{question:"What experience level is required for offensive simulations?",answer:"Our offensive simulations are designed for various skill levels, from beginners to advanced practitioners. Each simulation clearly indicates the recommended experience level, and we provide guidance throughout the exercises."},{question:"Are these simulations legal to perform?",answer:"Yes, all our simulations are conducted in controlled, isolated environments specifically designed for training purposes. You will never be targeting real systems outside of our training environment."},{question:"What tools will I need to participate?",answer:"Most simulations can be completed using our browser-based virtual lab environment. For more advanced scenarios, we provide a pre-configured virtual machine with all necessary tools installed."},{question:"How realistic are these offensive simulations?",answer:"Our simulations are designed to closely mimic real-world scenarios and vulnerabilities found in actual organizations. We regularly update our environments to reflect current attack techniques and vulnerabilities."}],m=i=>{d(a===i?null:i)};return e.jsxs("div",{className:`min-h-screen ${t?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-20`,children:[e.jsx(j,{title:"Offensive Security Simulations",description:"Practice ethical hacking and penetration testing with CyberForce's offensive security simulations. Learn web app testing, network exploitation, and more.",keywords:["offensive security","penetration testing","ethical hacking","web app security","network security"],canonicalUrl:"https://cyberforce.om/simulations/offensive"}),e.jsxs("div",{className:"relative overflow-hidden bg-gradient-to-b from-[#0B1120] to-[#1A1F35] py-16",children:[e.jsxs("div",{className:"absolute inset-0 opacity-20",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-red-500/20 via-orange-500/20 to-yellow-500/20"}),e.jsx("div",{className:"grid grid-cols-10 grid-rows-10 h-full w-full",children:Array.from({length:100}).map((i,s)=>e.jsx("div",{className:"border-[0.5px] border-white/5"},s))})]}),e.jsx("div",{className:"container mx-auto px-4 relative z-10",children:e.jsxs("div",{className:"max-w-3xl mx-auto text-center",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 px-4 py-2 rounded-full bg-red-500/10 border border-red-500/20 mb-6",children:[e.jsx(o,{className:"text-red-500"}),e.jsx("span",{className:"text-sm font-medium text-red-400",children:"For Educational Purposes Only"})]}),e.jsxs(r.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-4xl md:text-5xl font-bold mb-6 text-white",children:["Offensive Security ",e.jsx("span",{className:"text-red-500",children:"Simulations"})]}),e.jsx(r.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"text-xl text-gray-300 mb-8",children:"Develop ethical hacking skills through realistic penetration testing scenarios"}),e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"flex flex-wrap justify-center gap-4",children:[e.jsxs(n,{to:"/pricing",className:"px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium flex items-center gap-2 transition-colors",children:[e.jsx(l,{})," Start Training"]}),e.jsx(n,{to:"/simulations",className:"px-6 py-3 bg-gray-700 hover:bg-gray-800 text-white rounded-lg font-medium flex items-center gap-2 transition-colors",children:"View All Simulations"})]})]})})]}),e.jsxs("div",{className:"container mx-auto px-4 py-16",children:[e.jsxs("div",{className:"max-w-3xl mx-auto mb-16",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center",children:e.jsx(l,{className:"text-red-500 text-xl"})}),e.jsx("h2",{className:`text-3xl font-bold ${t?"text-white":"text-gray-900"}`,children:"What Are Offensive Simulations?"})]}),e.jsx("p",{className:`text-lg mb-6 ${t?"text-gray-300":"text-gray-600"}`,children:"Offensive security simulations provide hands-on experience with ethical hacking techniques in controlled environments. These exercises help security professionals identify vulnerabilities, understand attack vectors, and develop effective defensive strategies."}),e.jsxs("div",{className:`p-4 rounded-lg border ${t?"bg-red-900/10 border-red-900/30":"bg-red-50 border-red-200"} flex items-start gap-3 mb-8`,children:[e.jsx(o,{className:"text-red-500 text-xl flex-shrink-0 mt-1"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-red-500 mb-1",children:"Important Disclaimer"}),e.jsx("p",{className:`${t?"text-gray-300":"text-gray-700"}`,children:"These techniques should only be practiced in authorized environments. Unauthorized testing against systems you don't own is illegal and unethical. All CyberForce simulations are conducted in isolated, legal training environments."})]})]})]}),e.jsxs("div",{className:"mb-16",children:[e.jsx("h2",{className:`text-3xl font-bold mb-8 text-center ${t?"text-white":"text-gray-900"}`,children:"Available Offensive Simulations"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:c.map(i=>e.jsxs("div",{className:`rounded-xl overflow-hidden border ${i.borderColor} transition-all duration-300 ${t?"bg-[#1A1F35]":"bg-white"}`,children:[e.jsxs("div",{className:"h-48 overflow-hidden relative",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10"}),e.jsx("img",{src:i.image,alt:i.title,className:"w-full h-full object-cover"}),e.jsxs("div",{className:"absolute bottom-0 left-0 p-4 z-20",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-1",children:i.title}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"px-2 py-1 bg-black/50 rounded text-xs text-white",children:i.difficulty}),e.jsx("span",{className:"px-2 py-1 bg-black/50 rounded text-xs text-white",children:i.duration})]})]})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("p",{className:`mb-4 ${t?"text-gray-300":"text-gray-600"}`,children:i.description}),e.jsx("h4",{className:"font-semibold mb-2",children:"Skills You'll Practice:"}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-6",children:i.skills.map((s,u)=>e.jsx("span",{className:`px-2 py-1 rounded text-xs ${t?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-700"}`,children:s},u))}),e.jsx("button",{className:"w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors",children:"Start Simulation"})]})]},i.id))})]}),e.jsxs("div",{className:"max-w-3xl mx-auto mb-16",children:[e.jsx("h2",{className:`text-3xl font-bold mb-8 text-center ${t?"text-white":"text-gray-900"}`,children:"Frequently Asked Questions"}),e.jsx("div",{className:"space-y-4",children:x.map((i,s)=>e.jsxs("div",{className:`rounded-lg border ${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} overflow-hidden`,children:[e.jsxs("button",{onClick:()=>m(s),className:"w-full px-6 py-4 text-left flex items-center justify-between",children:[e.jsx("span",{className:"font-semibold",children:i.question}),a===s?e.jsx(p,{}):e.jsx(f,{})]}),a===s&&e.jsx("div",{className:`px-6 py-4 border-t ${t?"border-gray-800":"border-gray-200"}`,children:e.jsx("p",{className:`${t?"text-gray-300":"text-gray-600"}`,children:i.answer})})]},s))})]}),e.jsxs("div",{className:`rounded-xl overflow-hidden relative ${t?"bg-[#1A1F35]":"bg-white"} border ${t?"border-gray-800":"border-gray-200"}`,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-red-500/10 to-orange-500/10"}),e.jsxs("div",{className:"relative z-10 p-8 md:p-12 text-center",children:[e.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Ready to Test Your Offensive Security Skills?"}),e.jsx("p",{className:`text-lg mb-8 max-w-2xl mx-auto ${t?"text-gray-300":"text-gray-600"}`,children:"Join CyberForce today and gain access to our full range of offensive security simulations and training resources."}),e.jsxs(n,{to:"/pricing",className:"px-8 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium inline-flex items-center gap-2 transition-colors",children:["Start Training ",e.jsx(b,{})]})]})]})]})]})};export{S as default};
