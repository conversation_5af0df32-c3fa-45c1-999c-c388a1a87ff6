import{u as h,au as y,r as a,j as e,ag as i,aC as p,F as j,d as f,b0 as v,b1 as N}from"./index-c6UceSOv.js";const $=()=>{const o=h(),{darkMode:s}=y(),[n,d]=a.useState("<EMAIL>"),[g,m]=a.useState(""),[r,l]=a.useState(!1),[c,u]=a.useState(null),x=t=>{if(t.preventDefault(),l(!0),u(null),n==="<EMAIL>"){const b={id:"goutham-user-id",email:"<EMAIL>",user_metadata:{full_name:"<PERSON><PERSON><PERSON>",username:"goutham"}};localStorage.setItem("supabase.auth.token","mock-token-for-goutham"),localStorage.setItem("supabase.auth.user",JSON.stringify(b)),localStorage.setItem("user_subscription",JSON.stringify({tier:"free",user_id:"goutham-user-id",active:!0,coins:50})),setTimeout(()=>{l(!1),o("/simplified-dashboard")},1e3);return}setTimeout(()=>{l(!1),o("/simplified-dashboard")},1e3)};return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120]":"bg-gray-50"} flex items-center justify-center p-4`,children:e.jsxs("div",{className:"max-w-md w-full",children:[e.jsx("div",{className:"mb-8",children:e.jsxs(i,{to:"/",className:`${s?"text-gray-400 hover:text-white":"text-gray-600 hover:text-gray-900"} flex items-center gap-2`,children:[e.jsx(p,{}),e.jsx("span",{children:"Back to Home"})]})}),e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-xl border overflow-hidden`,children:e.jsxs("div",{className:"p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:`text-3xl font-bold ${s?"text-white":"text-gray-900"} mb-2`,children:"Welcome Back"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Sign in to continue your learning journey"})]}),c&&e.jsx("div",{className:`${s?"bg-red-500/20 text-red-400":"bg-red-100 text-red-800"} px-4 py-3 rounded mb-6`,children:c}),e.jsxs("form",{onSubmit:x,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"email",className:`block ${s?"text-gray-400":"text-gray-700"} mb-2`,children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(j,{className:"text-gray-500"})}),e.jsx("input",{type:"email",id:"email",className:`${s?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"<EMAIL>",value:n,onChange:t=>d(t.target.value),required:!0})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("label",{htmlFor:"password",className:`${s?"text-gray-400":"text-gray-700"}`,children:"Password"}),e.jsx(i,{to:"/forgot-password",className:"text-[#88cc14] hover:underline text-sm",children:"Forgot Password?"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(f,{className:"text-gray-500"})}),e.jsx("input",{type:"password",id:"password",className:`${s?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"••••••••",value:g,onChange:t=>m(t.target.value),required:!0})]})]}),e.jsx("button",{type:"submit",className:`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${r?"opacity-70 cursor-not-allowed":""}`,disabled:r,children:r?"Signing In...":"Sign In"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:["Don't have an account?"," ",e.jsx(i,{to:"/signup",className:"text-[#88cc14] hover:underline",children:"Sign Up"})]})}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:`w-full border-t ${s?"border-gray-800":"border-gray-300"}`})}),e.jsx("div",{className:"relative flex justify-center",children:e.jsx("span",{className:`${s?"bg-[#1A1F35]":"bg-white"} px-4 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Or continue with"})})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsxs("button",{type:"button",className:`flex items-center justify-center gap-2 ${s?"bg-[#0B1120] hover:bg-[#151F38] text-white border-gray-800":"bg-gray-50 hover:bg-gray-100 text-gray-900 border-gray-300"} py-3 px-4 rounded-lg border transition-colors`,children:[e.jsx(v,{}),e.jsx("span",{children:"Google"})]}),e.jsxs("button",{type:"button",className:`flex items-center justify-center gap-2 ${s?"bg-[#0B1120] hover:bg-[#151F38] text-white border-gray-800":"bg-gray-50 hover:bg-gray-100 text-gray-900 border-gray-300"} py-3 px-4 rounded-lg border transition-colors`,children:[e.jsx(N,{}),e.jsx("span",{children:"GitHub"})]})]})]})]})})]})})};export{$ as default};
