import{u as ie,b2 as le,au as oe,g as de,r,s as i,j as e,ai as xe,h as I,l as R,aw as ue,b5 as ge,T as me,U as he,aX as pe,V as be}from"./index-c6UceSOv.js";import{S as fe,U as ye,a as ve,C as je,N as Ne,b as we}from"./UserSubscriptionManager-C1fkpI97.js";const Ie=()=>{const T=ie(),{user:Se,profile:_e}=le(),{darkMode:t}=oe(),{subscriptionLevel:Ue}=de(),[a,l]=r.useState("overview"),[Ce,$e]=r.useState(!1),[De,y]=r.useState(!1),[Fe,L]=r.useState(new Date),[Ae,G]=r.useState(0),[p,O]=r.useState({totalUsers:0,freeUsers:0,premiumUsers:0,businessUsers:0,totalRevenue:0,activeSubscriptions:0,totalChallenges:0,totalModules:0,totalNotifications:0,pendingApprovals:0,userGrowth:0,revenueGrowth:0,activeUsers:0}),[B,v]=r.useState(!0),[j,P]=r.useState(null),[N,q]=r.useState([]),[Me,V]=r.useState([]),b=r.useCallback(async(s=!0)=>{var c,d,x,u,g,m,h,S;try{s&&(y(!0),v(!0));const{data:o,error:_}=await i.from("profiles").select("subscription_tier, count").group("subscription_tier");if(_)throw _;const{data:H,error:U}=await i.from("subscription_tracking").select("status, count").eq("status","active").group("status");if(U)throw U;const{data:J,error:C}=await i.from("subscription_tracking").select("payment_id, amount, created_at").not("payment_id","is",null);if(C)throw C;const{data:K,error:$}=await i.from("challenges").select("count");if($)throw $;const{data:Q,error:D}=await i.from("learning_modules").select("count");if(D)throw D;const{data:W,error:F}=await i.from("subscription_tracking").select(`
          id,
          user_id,
          start_date,
          status,
          amount,
          profiles:user_id (username, avatar_url),
          subscription_plans (name)
        `).order("start_date",{ascending:!1}).limit(5);if(F)throw F;const{data:A,error:M}=await i.from("notifications").select("count").eq("is_read",!1);if(M)throw M;const{data:Y,error:k}=await i.from("user_activity").select(`
          id,
          user_id,
          activity_type,
          activity_data,
          created_at,
          profiles:user_id (username, avatar_url)
        `).order("created_at",{ascending:!1}).limit(10);if(k)throw k;const E=o.reduce((n,f)=>n+f.count,0),Z=((c=o.find(n=>n.subscription_tier==="free"))==null?void 0:c.count)||0,ee=((d=o.find(n=>n.subscription_tier==="premium"))==null?void 0:d.count)||0,te=((x=o.find(n=>n.subscription_tier==="business"))==null?void 0:x.count)||0,se=((u=H[0])==null?void 0:u.count)||0,ae=J.reduce((n,f)=>n+(f.amount||0),0),re=Math.round(Math.random()*20-5),ce=Math.round(Math.random()*30-5),ne=Math.round(E*(.3+Math.random()*.4));O({totalUsers:E,freeUsers:Z,premiumUsers:ee,businessUsers:te,totalRevenue:ae,activeSubscriptions:se,totalChallenges:((g=K[0])==null?void 0:g.count)||0,totalModules:((m=Q[0])==null?void 0:m.count)||0,totalNotifications:((h=A[0])==null?void 0:h.count)||0,pendingApprovals:Math.floor(Math.random()*10),userGrowth:re,revenueGrowth:ce,activeUsers:ne}),G(((S=A[0])==null?void 0:S.count)||0),q(W||[]),V(Y||[]),L(new Date)}catch(o){console.error("Error fetching dashboard data:",o),P("Failed to load dashboard data. Please try again later.")}finally{v(!1),s&&setTimeout(()=>y(!1),500)}},[]);r.useEffect(()=>{b(!1);const s=i.channel("dashboard-changes").on("postgres_changes",{event:"*",schema:"public"},()=>{b(!1)}).subscribe(),c=setInterval(()=>{b(!1)},5*60*1e3);return()=>{s.unsubscribe(),clearInterval(c)}},[b]);const X=()=>{localStorage.removeItem("supabase.auth.token"),localStorage.removeItem("supabase.auth.user"),localStorage.removeItem("user_subscription"),T("/login",{replace:!0})},w=s=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",maximumFractionDigits:0}).format(s),z=s=>{const c=new Date(s);return c.toLocaleDateString()+" "+c.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})};return e.jsx("div",{className:`min-h-screen ${t?"bg-[#0B1120] text-white":"bg-gray-100 text-gray-900"}`,children:e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:`w-64 fixed inset-y-0 ${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border-r`,children:[e.jsxs("div",{className:"p-6",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Admin Dashboard"}),e.jsx("p",{className:`text-sm ${t?"text-gray-400":"text-gray-500"}`,children:"Manage your platform"})]}),e.jsxs("nav",{className:"mt-6",children:[e.jsxs("button",{onClick:()=>l("overview"),className:`w-full flex items-center gap-3 px-6 py-3 ${a==="overview"?"bg-[#88cc14]/10 text-[#88cc14]":t?"text-gray-400 hover:bg-gray-800":"text-gray-600 hover:bg-gray-100"}`,children:[e.jsx(xe,{}),e.jsx("span",{children:"Overview"})]}),e.jsxs("button",{onClick:()=>l("users"),className:`w-full flex items-center gap-3 px-6 py-3 ${a==="users"?"bg-[#88cc14]/10 text-[#88cc14]":t?"text-gray-400 hover:bg-gray-800":"text-gray-600 hover:bg-gray-100"}`,children:[e.jsx(I,{}),e.jsx("span",{children:"User Management"})]}),e.jsxs("button",{onClick:()=>l("subscriptions"),className:`w-full flex items-center gap-3 px-6 py-3 ${a==="subscriptions"?"bg-[#88cc14]/10 text-[#88cc14]":t?"text-gray-400 hover:bg-gray-800":"text-gray-600 hover:bg-gray-100"}`,children:[e.jsx(R,{}),e.jsx("span",{children:"Subscription Management"})]}),e.jsxs("button",{onClick:()=>l("content"),className:`w-full flex items-center gap-3 px-6 py-3 ${a==="content"?"bg-[#88cc14]/10 text-[#88cc14]":t?"text-gray-400 hover:bg-gray-800":"text-gray-600 hover:bg-gray-100"}`,children:[e.jsx(ue,{}),e.jsx("span",{children:"Content Management"})]}),e.jsxs("button",{onClick:()=>l("notifications"),className:`w-full flex items-center gap-3 px-6 py-3 ${a==="notifications"?"bg-[#88cc14]/10 text-[#88cc14]":t?"text-gray-400 hover:bg-gray-800":"text-gray-600 hover:bg-gray-100"}`,children:[e.jsx(ge,{}),e.jsx("span",{children:"Notifications"})]}),e.jsxs("button",{onClick:()=>l("settings"),className:`w-full flex items-center gap-3 px-6 py-3 ${a==="settings"?"bg-[#88cc14]/10 text-[#88cc14]":t?"text-gray-400 hover:bg-gray-800":"text-gray-600 hover:bg-gray-100"}`,children:[e.jsx(me,{}),e.jsx("span",{children:"Settings"})]}),e.jsxs("button",{onClick:X,className:`w-full flex items-center gap-3 px-6 py-3 ${t?"text-red-400 hover:bg-red-400/10":"text-red-600 hover:bg-red-50"}`,children:[e.jsx(he,{}),e.jsx("span",{children:"Sign Out"})]})]})]}),e.jsx("div",{className:"ml-64 flex-1",children:e.jsxs("div",{className:"p-6",children:[a==="overview"&&e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Dashboard Overview"}),B?e.jsx("div",{className:"flex justify-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"})}):j?e.jsx("div",{className:`p-4 rounded-lg ${t?"bg-red-900/20 text-red-400":"bg-red-100 text-red-800"}`,children:j}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[e.jsx("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-blue-500 bg-opacity-10",children:e.jsx(I,{className:"text-blue-500"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:`text-sm ${t?"text-gray-400":"text-gray-500"}`,children:"Total Users"}),e.jsx("p",{className:"text-2xl font-bold",children:p.totalUsers})]})]})}),e.jsx("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-green-500 bg-opacity-10",children:e.jsx(R,{className:"text-green-500"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:`text-sm ${t?"text-gray-400":"text-gray-500"}`,children:"Premium Users"}),e.jsx("p",{className:"text-2xl font-bold",children:p.premiumUsers})]})]})}),e.jsx("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-purple-500 bg-opacity-10",children:e.jsx(pe,{className:"text-purple-500"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:`text-sm ${t?"text-gray-400":"text-gray-500"}`,children:"Business Users"}),e.jsx("p",{className:"text-2xl font-bold",children:p.businessUsers})]})]})}),e.jsx("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-yellow-500 bg-opacity-10",children:e.jsx(be,{className:"text-yellow-500"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:`text-sm ${t?"text-gray-400":"text-gray-500"}`,children:"Total Revenue"}),e.jsx("p",{className:"text-2xl font-bold",children:w(p.totalRevenue)})]})]})})]}),e.jsx("div",{className:"mb-6",children:e.jsx(fe,{})}),e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Recent Subscriptions"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:`${t?"bg-gray-800":"bg-gray-50"}`,children:[e.jsx("th",{className:"px-4 py-3 text-left",children:"User"}),e.jsx("th",{className:"px-4 py-3 text-left",children:"Plan"}),e.jsx("th",{className:"px-4 py-3 text-left",children:"Amount"}),e.jsx("th",{className:"px-4 py-3 text-left",children:"Date"}),e.jsx("th",{className:"px-4 py-3 text-left",children:"Status"})]})}),e.jsx("tbody",{children:N.length>0?N.map(s=>{var c,d,x,u,g,m,h;return e.jsxs("tr",{className:`border-t ${t?"border-gray-800 hover:bg-gray-800":"border-gray-200 hover:bg-gray-50"}`,children:[e.jsx("td",{className:"px-4 py-3",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-[#88cc14]/20 flex items-center justify-center",children:(c=s.profiles)!=null&&c.avatar_url?e.jsx("img",{src:s.profiles.avatar_url,alt:s.profiles.username,className:"w-full h-full rounded-full object-cover"}):e.jsx("span",{className:"text-[#88cc14]",children:((x=(d=s.profiles)==null?void 0:d.username)==null?void 0:x.charAt(0).toUpperCase())||"U"})}),e.jsx("span",{children:((u=s.profiles)==null?void 0:u.username)||"Unknown User"})]})}),e.jsx("td",{className:"px-4 py-3",children:((g=s.subscription_plans)==null?void 0:g.name)||"Unknown Plan"}),e.jsx("td",{className:"px-4 py-3",children:w(s.amount||0)}),e.jsx("td",{className:"px-4 py-3",children:z(s.start_date)}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s.status==="active"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:((m=s.status)==null?void 0:m.charAt(0).toUpperCase())+((h=s.status)==null?void 0:h.slice(1))||"Unknown"})})]},s.id)}):e.jsx("tr",{children:e.jsx("td",{colSpan:"5",className:"px-4 py-3 text-center",children:"No recent subscriptions"})})})]})})]})]})]}),a==="users"&&e.jsx(ye,{}),a==="subscriptions"&&e.jsx(ve,{}),a==="content"&&e.jsx(je,{}),a==="notifications"&&e.jsx(Ne,{}),a==="settings"&&e.jsx(we,{})]})})]})})};export{Ie as default};
