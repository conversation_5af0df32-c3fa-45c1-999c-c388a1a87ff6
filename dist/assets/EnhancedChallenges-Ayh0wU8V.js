import{b2 as ae,r as m,s as u,j as e,au as ne,a9 as O,I as be,i as W,k as re,V as J,aN as K,ag as B,d as ge,ba as he,u as fe,x as te,aC as me,w as ye,az as je}from"./index-c6UceSOv.js";import{S as Ne}from"./SimpleUpgradeBanner-vRfC26d4.js";const xe=m.createContext();function _e({children:s}){const{user:h,profile:i}=ae(),[$,I]=m.useState([]),[M,Y]=m.useState([]),[L,P]=m.useState([]),[R,G]=m.useState([]),[C,E]=m.useState({}),[S,n]=m.useState({}),[f,k]=m.useState({}),[A,N]=m.useState(!0),[V,t]=m.useState(null);m.useEffect(()=>{(async()=>{try{N(!0);const{data:a,error:r}=await u.from("challenge_categories").select("*").order("display_order",{ascending:!0});if(r)throw r;const{data:o,error:d}=await u.from("challenge_difficulty_levels").select("*").order("display_order",{ascending:!0});if(d)throw d;const{data:g,error:p}=await u.from("challenge_types").select("*");if(p)throw p;let b=u.from("challenges").select(`
            id,
            title,
            slug,
            description,
            category_id,
            difficulty_id,
            type_id,
            points,
            coin_reward,
            estimated_time,
            is_premium,
            is_business,
            created_at,
            category:challenge_categories(name),
            difficulty:challenge_difficulty_levels(name),
            type:challenge_types(name)
          `).eq("is_active",!0);h?i&&(i.subscription_tier==="free"?b=b.eq("is_premium",!1).eq("is_business",!1):i.subscription_tier==="premium"&&(b=b.eq("is_business",!1))):b=b.eq("is_premium",!1).eq("is_business",!1);const{data:w,error:j}=await b;if(j)throw j;Y(a),P(o),G(g),I(w)}catch(a){console.error("Error fetching challenges:",a),t(a.message)}finally{N(!1)}})()},[h,i]),m.useEffect(()=>{if(!h){E({}),n({}),k({});return}(async()=>{try{N(!0);const{data:d,error:g}=await u.from("challenge_completions").select(`
            id,
            challenge_id,
            points_earned,
            coins_earned,
            completion_time,
            completed_at
          `).eq("user_id",h.id);if(g)throw g;const{data:p,error:b}=await u.from("challenge_attempts").select(`
            id,
            challenge_id,
            is_correct,
            points_earned,
            coins_earned,
            attempt_time,
            created_at
          `).eq("user_id",h.id);if(b)throw b;const{data:w,error:j}=await u.from("challenge_hint_purchases").select(`
            id,
            hint_id,
            coins_spent,
            purchased_at,
            hint:challenge_hints(challenge_id)
          `).eq("user_id",h.id);if(j)throw j;const q={};d.forEach(v=>{q[v.challenge_id]=v});const T={};p.forEach(v=>{T[v.challenge_id]||(T[v.challenge_id]=[]),T[v.challenge_id].push(v)});const U={};w.forEach(v=>{const z=v.hint.challenge_id;U[z]||(U[z]=[]),U[z].push(v)}),E(q),n(T),k(U)}catch(d){console.error("Error fetching user data:",d),t(d.message)}finally{N(!1)}})();const a=u.channel(`user-challenge-completions-${h.id}`).on("INSERT",d=>{E(g=>({...g,[d.new.challenge_id]:d.new}))}).subscribe(),r=u.channel(`user-challenge-attempts-${h.id}`).on("INSERT",d=>{n(g=>{const p=g[d.new.challenge_id]||[];return{...g,[d.new.challenge_id]:[...p,d.new]}})}).subscribe(),o=u.channel(`user-hint-purchases-${h.id}`).on("INSERT",d=>{u.from("challenge_hints").select("challenge_id").eq("id",d.new.hint_id).single().then(({data:g,error:p})=>{if(p)return;const b=g.challenge_id;k(w=>{const j=w[b]||[];return{...w,[b]:[...j,{...d.new,hint:{challenge_id:b}}]}})})}).subscribe();return()=>{u.removeChannel(a),u.removeChannel(r),u.removeChannel(o)}},[h]);const y=async l=>{try{N(!0);const a=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(l);let r=u.from("challenges").select(`
          id,
          title,
          slug,
          description,
          category_id,
          difficulty_id,
          type_id,
          points,
          coin_reward,
          estimated_time,
          is_premium,
          is_business,
          created_at,
          category:challenge_categories(name),
          difficulty:challenge_difficulty_levels(name),
          type:challenge_types(name),
          content:challenge_content(content),
          hints:challenge_hints(id, hint, coin_cost, display_order)
        `);a?r=r.eq("id",l):r=r.eq("slug",l);const{data:o,error:d}=await r.single();if(d)throw d;return o.hints&&o.hints.sort((g,p)=>g.display_order-p.display_order),o}catch(a){throw console.error("Error fetching challenge:",a),t(a.message),a}finally{N(!1)}},_=async(l,a)=>{try{if(!h)throw new Error("You must be logged in to submit an attempt");const r=$.find(w=>w.id===l);if(!r)throw new Error("Challenge not found");if(C[l])return{success:!1,message:"You have already completed this challenge"};const o=Math.random()>=.5,d=o?r.points:0,g=o?r.coin_reward:0,{data:p,error:b}=await u.from("challenge_attempts").insert([{user_id:h.id,challenge_id:l,solution:a,is_correct:o,points_earned:d,coins_earned:g,attempt_time:60,created_at:new Date}]).select().single();if(b)throw b;if(o){const{data:w,error:j}=await u.from("challenge_completions").insert([{user_id:h.id,challenge_id:l,points_earned:d,coins_earned:g,completion_time:60,completed_at:new Date}]).select().single();if(j)throw j;if(g>0){const{error:q}=await u.from("profiles").update({coins:((i==null?void 0:i.coins)||0)+g}).eq("id",h.id);if(q)throw q}return{success:!0,isCorrect:o,pointsEarned:d,coinsEarned:g,message:"Congratulations! You solved the challenge!"}}return{success:!0,isCorrect:o,pointsEarned:0,coinsEarned:0,message:"Incorrect solution. Try again!"}}catch(r){throw console.error("Error submitting challenge attempt:",r),t(r.message),r}},X=async(l,a)=>{try{if(!h)throw new Error("You must be logged in to purchase a hint");if(((i==null?void 0:i.coins)||0)<a)throw new Error("Not enough coins to purchase this hint");const{data:r,error:o}=await u.from("challenge_hint_purchases").select("id").eq("user_id",h.id).eq("hint_id",l).maybeSingle();if(o)throw o;if(r)return{success:!0,message:"You have already purchased this hint"};const{data:d,error:g}=await u.from("challenge_hint_purchases").insert([{user_id:h.id,hint_id:l,coins_spent:a,purchased_at:new Date}]).select().single();if(g)throw g;const{error:p}=await u.from("profiles").update({coins:((i==null?void 0:i.coins)||0)-a}).eq("id",h.id);if(p)throw p;return{success:!0,message:"Hint purchased successfully"}}catch(r){throw console.error("Error purchasing hint:",r),t(r.message),r}},D=async(l,a,r="")=>{try{if(!h)throw new Error("You must be logged in to rate a challenge");const{data:o,error:d}=await u.from("challenge_ratings").select("id").eq("user_id",h.id).eq("challenge_id",l).single();if(d&&d.code!=="PGRST116")throw d;if(o){const{data:g,error:p}=await u.from("challenge_ratings").update({rating:a,feedback:r}).eq("id",o.id).select().single();if(p)throw p;return g}else{const{data:g,error:p}=await u.from("challenge_ratings").insert([{challenge_id:l,user_id:h.id,rating:a,feedback:r}]).select().single();if(p)throw p;return g}}catch(o){throw console.error("Error rating challenge:",o),t(o.message),o}},H=l=>!!C[l],Z={challenges:$,categories:M,difficulties:L,types:R,userCompletions:C,userAttempts:S,loading:A,error:V,getChallengeById:y,submitChallengeAttempt:_,purchaseHint:X,rateChallenge:D,isChallengeCompleted:H,getChallengeAttempts:l=>S[l]||[],hasUserPurchasedHint:l=>{for(const a in f)if(f[a].some(o=>o.hint_id===l))return!0;return!1},getFilteredChallenges:(l={})=>{let a=[...$];if(l.category&&(a=a.filter(r=>r.category.name===l.category)),l.difficulty&&(a=a.filter(r=>r.difficulty.name===l.difficulty)),l.type&&(a=a.filter(r=>r.type.name===l.type)),l.search){const r=l.search.toLowerCase();a=a.filter(o=>o.title.toLowerCase().includes(r)||o.description.toLowerCase().includes(r))}return!i||i.subscription_tier==="free"?a=a.filter(r=>!r.is_premium&&!r.is_business):i.subscription_tier==="premium"&&(a=a.filter(r=>!r.is_business)),l.completed===!0?a=a.filter(r=>H(r.id)):l.completed===!1&&(a=a.filter(r=>!H(r.id))),l.minPoints!==void 0&&(a=a.filter(r=>r.points>=l.minPoints)),l.maxPoints!==void 0&&(a=a.filter(r=>r.points<=l.maxPoints)),a}};return e.jsx(xe.Provider,{value:Z,children:s})}function ue(){const s=m.useContext(xe);if(s===void 0)throw new Error("useEnhancedChallenge must be used within an EnhancedChallengeProvider");return s}const we=()=>{const{darkMode:s}=ne(),{user:h,profile:i}=ae(),{challenges:$,categories:I,difficulties:M,types:Y,loading:L,error:P,getFilteredChallenges:R,isChallengeCompleted:G}=ue(),[C,E]=m.useState([]),[S,n]=m.useState(""),[f,k]=m.useState({category:"",difficulty:"",type:"",completed:""});m.useEffect(()=>{const t=R({category:f.category,difficulty:f.difficulty,type:f.type,completed:f.completed==="completed"?!0:f.completed==="incomplete"?!1:void 0,search:S});E(t)},[$,f,S,R]);const A=(t,y)=>{k(_=>({..._,[t]:y}))},N=()=>{k({category:"",difficulty:"",type:"",completed:""}),n("")},V=t=>{if(!t)return"N/A";const y=Math.floor(t/60),_=t%60;return y===0?`${_} min`:_===0?`${y} hr`:`${y} hr ${_} min`};return L?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(O,{className:"animate-spin text-4xl text-[#88cc14]"})}):P?e.jsxs("div",{className:`${s?"bg-red-900/20 border-red-900/30":"bg-red-100 border-red-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold text-red-500 mb-4",children:"Error"}),e.jsx("p",{className:`${s?"text-red-300":"text-red-700"}`,children:P}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600",children:"Retry"})]}):e.jsxs("div",{children:[!i||i.subscription_tier==="free"?e.jsx(Ne,{title:"Upgrade to Premium",description:"Get access to all challenges and features with a premium subscription.",buttonText:"View Plans",buttonLink:"/pricing",className:"mb-6"}):null,e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",className:`w-full pl-10 pr-4 py-2 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,placeholder:"Search challenges...",value:S,onChange:t=>n(t.target.value)}),e.jsx(be,{className:"absolute left-3 top-3 text-gray-400"})]})}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs("select",{className:`px-3 py-2 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,value:f.category,onChange:t=>A("category",t.target.value),children:[e.jsx("option",{value:"",children:"All Categories"}),I.map(t=>e.jsx("option",{value:t.name,children:t.name},t.id))]}),e.jsxs("select",{className:`px-3 py-2 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,value:f.difficulty,onChange:t=>A("difficulty",t.target.value),children:[e.jsx("option",{value:"",children:"All Difficulties"}),M.map(t=>e.jsx("option",{value:t.name,children:t.name},t.id))]}),e.jsxs("select",{className:`px-3 py-2 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,value:f.type,onChange:t=>A("type",t.target.value),children:[e.jsx("option",{value:"",children:"All Types"}),Y.map(t=>e.jsx("option",{value:t.name,children:t.name},t.id))]}),e.jsxs("select",{className:`px-3 py-2 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,value:f.completed,onChange:t=>A("completed",t.target.value),children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"incomplete",children:"Incomplete"})]}),e.jsx("button",{onClick:N,className:`px-3 py-2 rounded-lg ${s?"bg-[#252D4A] hover:bg-[#313e6a] text-white":"bg-gray-200 hover:bg-gray-300 text-gray-800"} border border-transparent`,children:"Reset"})]})]})}),e.jsx("div",{className:"mb-4",children:e.jsxs("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:["Showing ",C.length," of ",$.length," challenges"]})}),C.length===0?e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx(W,{className:"mx-auto text-4xl mb-4 text-gray-500"}),e.jsx("h2",{className:"text-xl font-bold mb-2",children:"No Challenges Found"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Try adjusting your filters or search query."}),e.jsx("button",{onClick:N,className:"mt-4 px-4 py-2 theme-button-primary rounded-lg",children:"Reset Filters"})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:C.map(t=>{const y=G(t.id),_=!t.is_premium||(i==null?void 0:i.subscription_tier)==="premium"||(i==null?void 0:i.subscription_tier)==="business";return e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden transition-transform hover:transform hover:scale-[1.02]`,children:e.jsxs("div",{className:`p-4 ${y?s?"bg-[#88cc14]/10":"bg-[#88cc14]/5":""}`,children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h3",{className:"text-lg font-bold",children:t.title}),e.jsxs("div",{className:"flex items-center",children:[t.is_business&&e.jsx("span",{className:`ml-2 px-2 py-1 rounded text-xs ${s?"bg-purple-900/30 text-purple-300":"bg-purple-100 text-purple-800"}`,children:"Business"}),t.is_premium&&!t.is_business&&e.jsx("span",{className:`ml-2 px-2 py-1 rounded text-xs ${s?"bg-blue-900/30 text-blue-300":"bg-blue-100 text-blue-800"}`,children:"Premium"})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:t.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:t.difficulty.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-green-900/20 text-green-300":"bg-green-100 text-green-800"}`,children:t.type.name}),t.estimated_time&&e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${s?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(re,{className:"mr-1"})," ",V(t.estimated_time)]})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4 line-clamp-2`,children:t.description}),e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(W,{className:"mr-1 text-yellow-500"})," ",t.points," points"]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(J,{className:"mr-1 text-yellow-500"})," ",t.coin_reward," coins"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[y&&e.jsxs("span",{className:"flex items-center text-[#88cc14]",children:[e.jsx(K,{className:"mr-1"})," Completed"]}),!y&&e.jsx("span",{className:"invisible",children:"Placeholder"}),_?e.jsx(B,{to:`/challenges/${t.slug||t.id}`,className:"px-4 py-2 theme-button-primary rounded-lg",children:y?"View Challenge":"Start Challenge"}):e.jsxs(B,{to:"/pricing",className:`px-4 py-2 rounded-lg flex items-center ${s?"bg-[#252D4A] text-gray-300":"bg-gray-200 text-gray-700"}`,children:[e.jsx(ge,{className:"mr-2"})," Upgrade"]})]})]})},t.id)})})]})},ve=()=>{var oe,de;const{darkMode:s}=ne(),{user:h,profile:i}=ae(),{getChallengeById:$,submitChallengeAttempt:I,purchaseHint:M,rateChallenge:Y,isChallengeCompleted:L,getChallengeAttempts:P,hasUserPurchasedHint:R,loading:G,error:C}=ue(),{challengeId:E}=he(),S=fe(),[n,f]=m.useState(null),[k,A]=m.useState(!0),[N,V]=m.useState(null),[t,y]=m.useState(""),[_,X]=m.useState(!1),[D,H]=m.useState(null),[Q,le]=m.useState(0),[ie,Z]=m.useState(0),[l,a]=m.useState(""),[r,o]=m.useState(!1),[d,g]=m.useState([]),[p,b]=m.useState(!1),[w,j]=m.useState(!1),[q,T]=m.useState({});m.useEffect(()=>{(async()=>{try{A(!0);const x=await $(E);f(x),b(L(x.id)),g(P(x.id));const F={};x.hints&&x.hints.forEach(ee=>{F[ee.id]=R(ee.id)}),T(F)}catch(x){console.error("Error fetching challenge:",x),V(x.message)}finally{A(!1)}})()},[E,$,L,P,R]);const U=async c=>{if(c.preventDefault(),!!t.trim())try{X(!0),H(null);const x=await I(n.id,t);H(x),x.isCorrect&&(b(!0),g(P(n.id)))}catch(x){console.error("Error submitting solution:",x),H({success:!1,message:x.message})}finally{X(!1)}},v=async(c,x)=>{try{j(!0),(await M(c,x)).success&&T(ee=>({...ee,[c]:!0}))}catch(F){console.error("Error purchasing hint:",F)}finally{j(!1)}},z=async()=>{try{if(!h){S("/login",{state:{from:`/challenges/${E}`}});return}if(Q===0)return;o(!0),await Y(n.id,Q,l),o(!1)}catch(c){console.error("Error submitting rating:",c),o(!1)}},ce=c=>{if(!c)return"N/A";const x=Math.floor(c/60),F=c%60;return x===0?`${F} min`:F===0?`${x} hr`:`${x} hr ${F} min`},pe=()=>n?!!(!n.is_premium&&!n.is_business||n.is_premium&&!n.is_business&&(i==null?void 0:i.subscription_tier)==="premium"||(i==null?void 0:i.subscription_tier)==="business"):!1;if(k||G)return e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(O,{className:"animate-spin text-4xl text-[#88cc14]"})});if(N||C)return e.jsxs("div",{className:`${s?"bg-red-900/20 border-red-900/30":"bg-red-100 border-red-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold text-red-500 mb-4",children:"Error"}),e.jsx("p",{className:`${s?"text-red-300":"text-red-700"}`,children:N||C}),e.jsx("button",{onClick:()=>S("/challenges"),className:"mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600",children:"Back to Challenges"})]});if(!n)return e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx(te,{className:"mx-auto text-4xl mb-4 text-yellow-500"}),e.jsx("h2",{className:"text-xl font-bold mb-2",children:"Challenge Not Found"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"The challenge you're looking for doesn't exist or you don't have access to it."}),e.jsx(B,{to:"/challenges",className:"mt-4 px-4 py-2 theme-button-primary rounded-lg inline-block",children:"Browse Challenges"})]});if(!pe())return e.jsxs("div",{children:[e.jsx("div",{className:"mb-6",children:e.jsxs(B,{to:"/challenges",className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(me,{className:"mr-2"})," Back to Challenges"]})}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:n.title}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:n.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:n.difficulty.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-green-900/20 text-green-300":"bg-green-100 text-green-800"}`,children:n.type.name}),n.estimated_time&&e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${s?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(re,{className:"mr-1"})," ",ce(n.estimated_time)]}),n.is_business&&e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/30 text-purple-300":"bg-purple-100 text-purple-800"}`,children:"Business"}),n.is_premium&&!n.is_business&&e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/30 text-blue-300":"bg-blue-100 text-blue-800"}`,children:"Premium"})]}),e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(W,{className:"mr-1 text-yellow-500"})," ",n.points," points"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"mr-1 text-yellow-500"})," ",n.coin_reward," coins"]})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-6`,children:n.description}),e.jsxs("div",{className:`${s?"bg-[#252D4A] border-gray-700":"bg-gray-100 border-gray-300"} border rounded-lg p-6 text-center mb-6`,children:[e.jsx(ge,{className:"mx-auto text-4xl mb-4 text-[#88cc14]"}),e.jsx("h2",{className:"text-xl font-bold mb-2",children:"Premium Content"}),e.jsxs("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:["This challenge is available to ",n.is_business?"Business":"Premium"," subscribers only."]}),e.jsx(B,{to:"/pricing",className:"px-4 py-2 theme-button-primary rounded-lg inline-block",children:"Upgrade Now"})]})]})]});const se=((de=(oe=n.content)==null?void 0:oe[0])==null?void 0:de.content)||{};return e.jsxs("div",{children:[e.jsx("div",{className:"mb-6",children:e.jsxs(B,{to:"/challenges",className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(me,{className:"mr-2"})," Back to Challenges"]})}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:n.title}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:n.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:n.difficulty.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-green-900/20 text-green-300":"bg-green-100 text-green-800"}`,children:n.type.name}),n.estimated_time&&e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${s?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(re,{className:"mr-1"})," ",ce(n.estimated_time)]}),n.is_business&&e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/30 text-purple-300":"bg-purple-100 text-purple-800"}`,children:"Business"}),n.is_premium&&!n.is_business&&e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/30 text-blue-300":"bg-blue-100 text-blue-800"}`,children:"Premium"})]}),e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(W,{className:"mr-1 text-yellow-500"})," ",n.points," points"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"mr-1 text-yellow-500"})," ",n.coin_reward," coins"]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Description"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:n.description})]}),e.jsxs("div",{className:`${s?"bg-[#252D4A] border-gray-700":"bg-gray-100 border-gray-300"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Challenge Details"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:se.description||"Challenge details will be displayed here."}),se.flag_format&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"text-lg font-medium mb-2",children:"Flag Format"}),e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-3 font-mono`,children:se.flag_format})]})]}),n.hints&&n.hints.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Hints"}),e.jsx("div",{className:"space-y-4",children:n.hints.map((c,x)=>e.jsxs("div",{className:`${s?"bg-[#252D4A] border-gray-700":"bg-gray-100 border-gray-300"} border rounded-lg p-4`,children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsxs("h3",{className:"font-medium",children:["Hint ",x+1]}),q[c.id]?e.jsxs("span",{className:"text-[#88cc14] flex items-center",children:[e.jsx(K,{className:"mr-1"})," Purchased"]}):e.jsx("button",{onClick:()=>v(c.id,c.coin_cost),disabled:w||((i==null?void 0:i.coins)||0)<c.coin_cost,className:`flex items-center px-3 py-1 rounded-lg ${((i==null?void 0:i.coins)||0)<c.coin_cost?s?"bg-gray-800 text-gray-500":"bg-gray-200 text-gray-500":"theme-button-primary"}`,children:w?e.jsxs(e.Fragment,{children:[e.jsx(O,{className:"animate-spin mr-1"})," Purchasing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(J,{className:"mr-1"})," ",c.coin_cost," coins"]})})]}),q[c.id]?e.jsx("p",{className:`${s?"text-gray-300":"text-gray-700"}`,children:c.hint}):e.jsxs("div",{className:"flex items-center justify-center py-4",children:[e.jsx(ye,{className:`text-2xl ${s?"text-gray-600":"text-gray-400"}`}),e.jsx("span",{className:"ml-2 text-gray-500",children:"Purchase this hint to reveal it"})]})]},c.id))})]}),p?e.jsx("div",{className:`mb-6 p-4 rounded-lg ${s?"bg-green-900/20 border-green-900/30":"bg-green-100 border-green-200"} border`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(K,{className:"mr-2 text-green-500"}),e.jsx("p",{className:`${s?"text-green-300":"text-green-800"}`,children:"You have successfully completed this challenge!"})]})}):e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Submit Solution"}),e.jsxs("form",{onSubmit:U,children:[e.jsx("div",{className:"mb-4",children:e.jsx("input",{type:"text",value:t,onChange:c=>y(c.target.value),placeholder:"Enter flag (e.g., flag{...})",className:`w-full p-3 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,required:!0})}),e.jsx("button",{type:"submit",disabled:_,className:"w-full px-4 py-3 theme-button-primary rounded-lg flex items-center justify-center",children:_?e.jsxs(e.Fragment,{children:[e.jsx(O,{className:"animate-spin mr-2"})," Submitting..."]}):"Submit Solution"})]}),D&&e.jsxs("div",{className:`mt-4 p-4 rounded-lg ${D.isCorrect?s?"bg-green-900/20 border-green-900/30 text-green-300":"bg-green-100 border-green-200 text-green-800":s?"bg-red-900/20 border-red-900/30 text-red-300":"bg-red-100 border-red-200 text-red-800"} border`,children:[e.jsxs("div",{className:"flex items-center",children:[D.isCorrect?e.jsx(K,{className:"mr-2 text-green-500"}):e.jsx(te,{className:"mr-2 text-red-500"}),e.jsx("p",{children:D.message})]}),D.isCorrect&&e.jsxs("div",{className:"mt-2 flex flex-col sm:flex-row gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(W,{className:"mr-1 text-yellow-500"})," ",D.pointsEarned," points earned"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"mr-1 text-yellow-500"})," ",D.coinsEarned," coins earned"]})]})]})]}),d.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Your Attempts"}),e.jsx("div",{className:`${s?"bg-[#252D4A] border-gray-700":"bg-gray-100 border-gray-300"} border rounded-lg p-4`,children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:`${s?"border-gray-700":"border-gray-300"} border-b`,children:[e.jsx("th",{className:"text-left py-2 px-4",children:"Date"}),e.jsx("th",{className:"text-left py-2 px-4",children:"Solution"}),e.jsx("th",{className:"text-left py-2 px-4",children:"Result"})]})}),e.jsx("tbody",{children:d.map((c,x)=>e.jsxs("tr",{className:`${s?"border-gray-700":"border-gray-300"} border-b last:border-b-0`,children:[e.jsx("td",{className:"py-2 px-4",children:new Date(c.created_at).toLocaleString()}),e.jsx("td",{className:"py-2 px-4 font-mono",children:c.solution}),e.jsx("td",{className:"py-2 px-4",children:c.is_correct?e.jsxs("span",{className:"text-green-500 flex items-center",children:[e.jsx(K,{className:"mr-1"})," Correct"]}):e.jsxs("span",{className:"text-red-500 flex items-center",children:[e.jsx(te,{className:"mr-1"})," Incorrect"]})})]},c.id||x))})]})})})]}),e.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-700",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Rate this Challenge"}),e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("span",{className:"mr-2",children:"Rating:"}),e.jsx("div",{className:"flex",children:[1,2,3,4,5].map(c=>e.jsx("button",{type:"button",onClick:()=>le(c),onMouseEnter:()=>Z(c),onMouseLeave:()=>Z(0),className:"text-2xl focus:outline-none",children:e.jsx(je,{className:`${(ie||Q)>=c?"text-yellow-400":s?"text-gray-700":"text-gray-300"}`})},c))})]}),Q>0&&e.jsxs("div",{children:[e.jsx("textarea",{value:l,onChange:c=>a(c.target.value),placeholder:"Share your feedback about this challenge (optional)",className:`w-full p-3 rounded-lg ${s?"bg-[#252D4A] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,rows:3}),e.jsx("button",{onClick:z,disabled:r,className:"mt-2 px-4 py-2 theme-button-primary rounded-lg",children:r?e.jsxs(e.Fragment,{children:[e.jsx(O,{className:"inline animate-spin mr-2"})," Submitting..."]}):"Submit Rating"})]})]})]})]})},Ee=()=>{const{darkMode:s}=ne(),{challengeId:h}=he();return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs(_e,{children:[e.jsx("h1",{className:"text-3xl font-bold mb-6",children:h?"Challenge":"Cybersecurity Challenges"}),h?e.jsx(ve,{}):e.jsx(we,{})]})})})};export{Ee as default};
