import{b2 as M,au as Y,r as s,j as e,bG as I,bM as G,h as P,ag as A,s as o}from"./index-c6UceSOv.js";const B=()=>{const{user:i,profile:x}=M(),{darkMode:n}=Y(),[h,w]=s.useState([]),[p,N]=s.useState([]),[d,m]=s.useState(!0),[v,l]=s.useState(null),[b,S]=s.useState(!1),[f,_]=s.useState(""),[T,C]=s.useState(""),[g,y]=s.useState(!1);s.useEffect(()=>{(async()=>{!i||!x||(x.subscription_tier==="premium"||x.subscription_tier==="business"?S(!0):l("You need a premium or business subscription to access teams"))})()},[i,x]),s.useEffect(()=>{if(!i||!b)return;(async()=>{try{m(!0);const{data:t,error:a}=await o.from("team_members").select(`
            team:teams(
              id,
              name,
              description,
              created_at,
              created_by,
              max_members
            ),
            role
          `).eq("user_id",i.id);if(a)throw a;const{data:c,error:u}=await o.from("team_invitations").select(`
            id,
            team_id,
            team:teams(name, description),
            invited_by,
            inviter:profiles!team_invitations_invited_by_fkey(username),
            status,
            created_at,
            expires_at
          `).eq("email",i.email).eq("status","pending");if(u)throw u;const F=await Promise.all(t.map(async j=>{var E;const{data:L,error:D}=await o.from("team_members").select("count").eq("team_id",j.team.id);if(D)throw D;return{...j.team,role:j.role,memberCount:((E=L[0])==null?void 0:E.count)||0}}));w(F),N(c)}catch(t){console.error("Error fetching teams:",t),l(t.message)}finally{m(!1)}})()},[i,b]);const $=async r=>{if(r.preventDefault(),!f.trim()){l("Team name is required");return}try{if(m(!0),h.length>=15){l("You have reached the maximum number of teams (15)");return}const{data:t,error:a}=await o.from("teams").insert([{name:f,description:T,created_by:i.id,max_members:8}]).select().single();if(a)throw a;const{error:c}=await o.from("team_members").insert([{team_id:t.id,user_id:i.id,role:"owner"}]);if(c)throw c;_(""),C(""),y(!1),w([...h,{...t,role:"owner",memberCount:1}])}catch(t){console.error("Error creating team:",t),l(t.message)}finally{m(!1)}},k=async r=>{try{m(!0);const{data:t,error:a}=await o.from("team_invitations").select("team_id, status").eq("id",r).single();if(a)throw a;if(t.status!=="pending"){l("This invitation is no longer valid");return}const{error:c}=await o.from("team_invitations").update({status:"accepted"}).eq("id",r);if(c)throw c;const{error:u}=await o.from("team_members").insert([{team_id:t.team_id,user_id:i.id,role:"member"}]);if(u)throw u;window.location.reload()}catch(t){console.error("Error accepting invitation:",t),l(t.message)}finally{m(!1)}},q=async r=>{try{m(!0);const{error:t}=await o.from("team_invitations").update({status:"rejected"}).eq("id",r);if(t)throw t;N(p.filter(a=>a.id!==r))}catch(t){console.error("Error declining invitation:",t),l(t.message)}finally{m(!1)}};return!d&&!b?e.jsx(I,{to:"/pricing"}):e.jsx("div",{className:`min-h-screen ${n?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Team Dashboard"}),b&&e.jsxs("button",{onClick:()=>y(!g),className:"theme-button-primary flex items-center gap-2 px-4 py-2 rounded-lg",children:[e.jsx(G,{})," ",g?"Cancel":"Create Team"]})]}),v&&e.jsx("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:v})}),g&&e.jsxs("div",{className:`${n?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Create New Team"}),e.jsxs("form",{onSubmit:$,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2",children:"Team Name"}),e.jsx("input",{type:"text",className:"theme-input w-full p-2 rounded",value:f,onChange:r=>_(r.target.value),required:!0})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2",children:"Description"}),e.jsx("textarea",{className:"theme-input w-full p-2 rounded",value:T,onChange:r=>C(r.target.value),rows:"3"})]}),e.jsx("button",{type:"submit",className:"theme-button-primary px-4 py-2 rounded-lg",disabled:d,children:d?"Creating...":"Create Team"})]})]}),p.length>0&&e.jsxs("div",{className:`${n?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Team Invitations"}),e.jsx("div",{className:"space-y-4",children:p.map(r=>e.jsxs("div",{className:`${n?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-300"} rounded-lg border p-4 flex flex-col md:flex-row md:items-center justify-between`,children:[e.jsxs("div",{className:"mb-4 md:mb-0",children:[e.jsx("h3",{className:"font-bold",children:r.team.name}),e.jsxs("p",{className:`text-sm ${n?"text-gray-400":"text-gray-600"}`,children:["Invited by ",r.inviter.username," on ",new Date(r.created_at).toLocaleDateString()]}),r.team.description&&e.jsx("p",{className:"mt-2",children:r.team.description})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>k(r.id),className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",disabled:d,children:"Accept"}),e.jsx("button",{onClick:()=>q(r.id),className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg",disabled:d,children:"Decline"})]})]},r.id))})]}),e.jsxs("div",{className:`${n?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"My Teams"}),d?e.jsx("p",{children:"Loading teams..."}):h.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(P,{className:"mx-auto text-4xl mb-4 text-gray-400"}),e.jsx("p",{className:"mb-4",children:"You are not a member of any teams yet."}),b&&!g&&e.jsx("button",{onClick:()=>y(!0),className:"theme-button-primary px-4 py-2 rounded-lg",children:"Create Your First Team"})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:h.map(r=>e.jsxs("div",{className:`${n?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-300"} rounded-lg border p-4`,children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h3",{className:"font-bold",children:r.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${r.role==="owner"?"bg-purple-100 text-purple-800":r.role==="admin"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:r.role})]}),r.description&&e.jsx("p",{className:`text-sm ${n?"text-gray-400":"text-gray-600"} mb-4`,children:r.description}),e.jsxs("div",{className:"flex justify-between items-center text-sm mb-4",children:[e.jsxs("span",{children:[r.memberCount," / ",r.max_members," members"]}),e.jsxs("span",{children:["Created ",new Date(r.created_at).toLocaleDateString()]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(A,{to:`/teams/${r.id}`,className:"theme-button-primary flex-1 text-center py-2 rounded-lg",children:"View Team"}),r.role==="owner"&&e.jsx(A,{to:`/teams/${r.id}/manage`,className:`${n?"bg-[#1A1F35] hover:bg-[#252D4A] text-white":"bg-gray-200 hover:bg-gray-300 text-gray-800"} flex-1 text-center py-2 rounded-lg`,children:"Manage"})]})]},r.id))})]})]})})};export{B as default};
