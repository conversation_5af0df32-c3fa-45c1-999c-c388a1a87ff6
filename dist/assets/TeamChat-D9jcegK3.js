import{ba as L,b2 as k,au as A,r as t,s as i,j as e,bG as F,ag as R,aC as G,aG as $,h as I}from"./index-c6UceSOv.js";const B=()=>{const{teamId:a}=L(),{user:n,profile:P}=k(),{darkMode:l}=A(),[g,M]=t.useState(null),[S,T]=t.useState([]),[h,p]=t.useState([]),[c,j]=t.useState(""),[o,y]=t.useState(!0),[v,f]=t.useState(null),[d,N]=t.useState(!1),w=t.useRef(null);t.useEffect(()=>{(async()=>{if(!(!n||!a))try{y(!0);const{data:r,error:m}=await i.from("team_members").select("id, role").eq("team_id",a).eq("user_id",n.id).single();if(m&&m.code!=="PGRST116")throw m;if(!r){N(!1),f("You are not a member of this team");return}N(!0);const{data:u,error:x}=await i.from("teams").select("*").eq("id",a).single();if(x)throw x;M(u);const{data:b,error:_}=await i.from("team_members").select(`
            id,
            role,
            user:profiles(id, username, full_name, avatar_url)
          `).eq("team_id",a);if(_)throw _;T(b);const{data:D,error:E}=await i.from("team_messages").select(`
            id,
            message,
            created_at,
            user:profiles(id, username, full_name, avatar_url)
          `).eq("team_id",a).order("created_at",{ascending:!0});if(E)throw E;p(D)}catch(r){console.error("Error fetching team data:",r),f(r.message)}finally{y(!1)}})()},[n,a]),t.useEffect(()=>{if(!a||!d)return;const s=i.channel(`team-messages-${a}`).on("postgres_changes",{event:"INSERT",schema:"public",table:"team_messages",filter:`team_id=eq.${a}`},r=>{(async()=>{const{data:u,error:x}=await i.from("team_messages").select(`
              id,
              message,
              created_at,
              user:profiles(id, username, full_name, avatar_url)
            `).eq("id",r.new.id).single();!x&&u&&p(b=>[...b,u])})()}).subscribe();return()=>{i.removeChannel(s)}},[a,d]),t.useEffect(()=>{var s;(s=w.current)==null||s.scrollIntoView({behavior:"smooth"})},[h]);const q=async s=>{if(s.preventDefault(),!(!c.trim()||!n||!a))try{const{error:r}=await i.from("team_messages").insert([{team_id:a,user_id:n.id,message:c.trim()}]);if(r)throw r;j("")}catch(r){console.error("Error sending message:",r),f(r.message)}},C=s=>new Date(s).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return!o&&!d?e.jsx(F,{to:"/teams"}):e.jsx("div",{className:`min-h-screen ${l?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(R,{to:"/teams",className:`mr-4 ${l?"text-gray-400 hover:text-white":"text-gray-600 hover:text-black"}`,children:e.jsx(G,{})}),o?e.jsx("h1",{className:"text-2xl font-bold",children:"Loading..."}):e.jsxs("h1",{className:"text-2xl font-bold",children:[g==null?void 0:g.name," - Team Chat"]})]}),v&&e.jsx("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:v})}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsxs("div",{className:`flex-1 ${l?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4 flex flex-col h-[calc(100vh-200px)]`,children:[e.jsx("div",{className:"flex-1 overflow-y-auto mb-4",children:o?e.jsx("p",{children:"Loading messages..."}):h.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full",children:[e.jsx($,{className:"text-4xl mb-4 text-gray-400"}),e.jsx("p",{className:`${l?"text-gray-400":"text-gray-600"}`,children:"No messages yet. Start the conversation!"})]}):e.jsxs("div",{className:"space-y-4",children:[h.map(s=>{const r=s.user.id===(n==null?void 0:n.id);return e.jsx("div",{className:`flex ${r?"justify-end":"justify-start"}`,children:e.jsxs("div",{className:`max-w-[80%] ${r?`${l?"bg-[#88cc14]/20 text-white":"bg-[#88cc14]/10 text-gray-900"} rounded-tl-lg rounded-tr-none`:`${l?"bg-[#252D4A] text-white":"bg-gray-100 text-gray-900"} rounded-tr-lg rounded-tl-none`} rounded-bl-lg rounded-br-lg p-3`,children:[!r&&e.jsx("div",{className:"font-bold text-sm mb-1",children:s.user.username}),e.jsx("div",{className:"break-words",children:s.message}),e.jsx("div",{className:`text-xs mt-1 text-right ${l?"text-gray-400":"text-gray-500"}`,children:C(s.created_at)})]})},s.id)}),e.jsx("div",{ref:w})]})}),e.jsxs("form",{onSubmit:q,className:"flex gap-2",children:[e.jsx("input",{type:"text",className:"theme-input flex-1 p-2 rounded-lg",placeholder:"Type your message...",value:c,onChange:s=>j(s.target.value),disabled:o||!d}),e.jsx("button",{type:"submit",className:"theme-button-primary px-4 py-2 rounded-lg",disabled:o||!c.trim()||!d,children:e.jsx($,{})})]})]}),e.jsxs("div",{className:`w-full md:w-64 ${l?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:[e.jsxs("h2",{className:"text-xl font-bold mb-4 flex items-center gap-2",children:[e.jsx(I,{})," Members"]}),o?e.jsx("p",{children:"Loading members..."}):e.jsx("div",{className:"space-y-2",children:S.map(s=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[s.user.avatar_url?e.jsx("img",{src:s.user.avatar_url,alt:s.user.username,className:"w-8 h-8 rounded-full mr-2"}):e.jsx("div",{className:`w-8 h-8 rounded-full mr-2 flex items-center justify-center ${l?"bg-gray-700":"bg-gray-200"}`,children:s.user.username.charAt(0).toUpperCase()}),e.jsx("span",{children:s.user.username})]}),e.jsx("span",{className:`text-xs px-2 py-1 rounded ${s.role==="owner"?"bg-purple-100 text-purple-800":s.role==="admin"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:s.role})]},s.id))})]})]})]})})};export{B as default};
