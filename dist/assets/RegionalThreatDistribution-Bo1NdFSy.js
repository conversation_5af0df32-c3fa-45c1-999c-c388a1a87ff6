import{au as p,r as i,j as e,aM as f,av as u,E as x,bB as y,x as g}from"./index-c6UceSOv.js";const w=()=>{const{darkMode:b}=p(),[j,N]=i.useState(null),[r,d]=i.useState(!1),c=[{name:"Asia Pacific",totalThreats:1245,attackTypes:[{type:"Ransomware",count:320,color:"#ff3b30"},{type:"Phishing",count:280,color:"#ff9500"},{type:"DDoS",count:210,color:"#ffcc00"},{type:"Data Breach",count:175,color:"#007aff"},{type:"Supply Chain",count:95,color:"#5856d6"},{type:"Zero-day Exploit",count:85,color:"#ff2d55"},{type:"Malware",count:65,color:"#34c759"},{type:"Other",count:15,color:"#8e8e93"}]},{name:"Europe",totalThreats:980,attackTypes:[{type:"Ransomware",count:250,color:"#ff3b30"},{type:"Phishing",count:220,color:"#ff9500"},{type:"DDoS",count:170,color:"#ffcc00"},{type:"Data Breach",count:140,color:"#007aff"},{type:"Supply Chain",count:75,color:"#5856d6"},{type:"Zero-day Exploit",count:65,color:"#ff2d55"},{type:"Malware",count:50,color:"#34c759"},{type:"Other",count:10,color:"#8e8e93"}]},{name:"North America",totalThreats:760,attackTypes:[{type:"Ransomware",count:210,color:"#ff3b30"},{type:"Phishing",count:180,color:"#ff9500"},{type:"DDoS",count:120,color:"#ffcc00"},{type:"Data Breach",count:95,color:"#007aff"},{type:"Supply Chain",count:65,color:"#5856d6"},{type:"Zero-day Exploit",count:45,color:"#ff2d55"},{type:"Malware",count:35,color:"#34c759"},{type:"Other",count:10,color:"#8e8e93"}]},{name:"Middle East",totalThreats:620,attackTypes:[{type:"Ransomware",count:180,color:"#ff3b30"},{type:"Phishing",count:140,color:"#ff9500"},{type:"DDoS",count:95,color:"#ffcc00"},{type:"Data Breach",count:75,color:"#007aff"},{type:"Supply Chain",count:45,color:"#5856d6"},{type:"Zero-day Exploit",count:40,color:"#ff2d55"},{type:"Malware",count:35,color:"#34c759"},{type:"Other",count:10,color:"#8e8e93"}]}],l=()=>{d(!r)},h=()=>c.map((a,o)=>e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("div",{className:"text-lg font-semibold",children:a.name}),e.jsxs("div",{className:"text-sm",children:[a.totalThreats.toLocaleString()," threats"]})]}),e.jsx("div",{className:"flex h-10 rounded-md overflow-hidden",style:{width:"100%"},children:a.attackTypes.map((t,s)=>{const n=t.count/a.totalThreats*100;return e.jsx("div",{style:{width:`${n}%`,backgroundColor:t.color,transition:"width 0.3s ease"},title:`${t.type}: ${t.count} (${Math.round(n)}%)`},s)})})]},o)),m=()=>{const a=[];return c.forEach(o=>{o.attackTypes.forEach(t=>{a.some(s=>s.type===t.type)||a.push(t)})}),e.jsx("div",{className:"flex flex-wrap gap-4 mt-4",children:a.map((o,t)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 mr-2",style:{backgroundColor:o.color}}),e.jsx("span",{className:"text-sm",children:o.type})]},t))})};return e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[e.jsx(f,{className:"mr-2 text-green-400"})," Regional Threat Distribution",e.jsx("button",{className:"ml-2 text-gray-400 hover:text-gray-300",onClick:l,title:"Information about regional threats",children:e.jsx(u,{size:14})})]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["Updated ",new Date().toLocaleString()]})]}),r&&e.jsxs("div",{className:"mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[e.jsx(x,{className:"mr-1 text-green-400"})," Understanding Regional Threats"]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:l,children:"×"})]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Different regions face unique cybersecurity threats based on geopolitical factors, economic conditions, and the presence of state-sponsored threat actors. Understanding these regional patterns can help organizations implement targeted security controls."}),e.jsx("div",{className:"flex items-center text-xs",children:e.jsxs("a",{href:"https://www.cisa.gov/topics/cyber-threats-and-advisories",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 flex items-center",children:["Learn more about regional threats ",e.jsx(y,{className:"ml-1",size:10})]})})]}),e.jsxs("div",{className:"mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm",children:[e.jsx(g,{className:"inline-block mr-2"}),"Unable to load regional threat data. Using sample data instead."]}),e.jsx("div",{className:"mb-4 bg-gray-800 rounded-lg p-4",children:h()}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Attack Types"}),m()]})]})};export{w as default};
