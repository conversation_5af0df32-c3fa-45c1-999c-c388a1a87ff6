import{u as D,r as i,s as b,j as e,a as B,m as f,F as C,b as L,c as S,d as N,e as v,f as P}from"./index-c6UceSOv.js";function A(){const h=D(),[l,r]=i.useState(null);i.useEffect(()=>{b.auth.getSession().then(({data:{session:n}})=>{r((n==null?void 0:n.user)||null)});const{data:{subscription:t}}=b.auth.onAuthStateChange((n,a)=>{r((a==null?void 0:a.user)||null)});return()=>{t.unsubscribe()}},[]);const m=(()=>{var x,o;const t=((x=l==null?void 0:l.user_metadata)==null?void 0:x.subscription)==="premium"||((o=l==null?void 0:l.user_metadata)==null?void 0:o.subscription)==="business",n=[{to:"/challenges",icon:FaCode,text:"Challenges"},{to:"/leaderboard",icon:FaTrophy,text:"Leaderboard"}],a=[{to:"/learn",icon:FaGraduationCap,text:"Learn"},{to:"/games",icon:FaGamepad,text:"Start Hack"}];return t?[...a,...n]:n})();return e.jsx("nav",{className:`fixed w-full z-50 transition-all duration-300 ${isScrolled?"py-3 bg-black shadow-lg":"py-4 md:py-5 bg-black bg-opacity-90"}`,children:e.jsx("div",{className:"container mx-auto px-5",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(Link,{to:"/",className:"flex items-center z-20 group",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 bg-[#2DD4BF]/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e.jsxs("span",{className:"font-bold text-2xl relative z-10",children:[e.jsx("span",{className:"text-[#4A5CBA]",children:"Cyber"}),e.jsx("span",{className:"text-[#F5B93F]",children:"Force"})]})]})}),e.jsx("button",{onClick:()=>setIsOpen(!isOpen),className:"md:hidden z-20 p-2 rounded-lg hover:bg-gray-800 transition-colors",children:isOpen?e.jsx(FaTimes,{className:"text-xl text-[#2DD4BF]"}):e.jsx(FaBars,{className:"text-xl text-[#2DD4BF]"})}),e.jsxs("div",{className:"hidden md:flex items-center space-x-8",children:[m.map(t=>e.jsxs(Link,{to:t.to,className:"flex items-center space-x-2 nav-link relative group",children:[e.jsx(t.icon,{className:"text-xl text-[#2DD4BF] group-hover:text-white transition-colors"}),e.jsx("span",{className:"text-[#2DD4BF] group-hover:text-white transition-colors",children:t.text}),e.jsx("span",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-[#2DD4BF] transition-all duration-300 group-hover:w-full"})]},t.to)),l?e.jsxs("div",{className:"relative dashboard-menu-container",children:[e.jsxs("button",{onClick:handleDashboardClick,className:"bg-[#2DD4BF] text-white font-bold px-6 py-2 rounded-lg hover:bg-[#2DD4BF]/80 transition-all duration-300 hover:shadow-lg hover:shadow-[#2DD4BF]/20 flex items-center gap-2 relative overflow-hidden group",children:[e.jsx(FaUser,{}),e.jsx("span",{children:"Dashboard"})]}),showDashboardMenu&&e.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50",children:[e.jsx(Link,{to:"/dashboard",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Standard Dashboard"}),e.jsx(Link,{to:"/enhanced-dashboard",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Enhanced Dashboard"}),e.jsx(Link,{to:"/simplified-dashboard",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"CyberForce Dashboard"}),e.jsx("hr",{className:"my-1"}),e.jsx("button",{onClick:async()=>{await b.auth.signOut(),h("/")},className:"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:"Sign Out"})]})]}):e.jsxs(Link,{to:"/login",className:"bg-[#2DD4BF] text-white font-bold px-6 py-2 rounded-lg hover:bg-[#2DD4BF]/80 transition-all duration-300 hover:shadow-lg hover:shadow-[#2DD4BF]/20 relative overflow-hidden group",children:[e.jsx("span",{className:"relative z-10",children:"Sign In"}),e.jsx("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-500",style:{transform:"translateX(-100%)"}})]})]}),e.jsx(motion.div,{initial:!1,animate:{opacity:isOpen?1:0,pointerEvents:isOpen?"auto":"none"},className:"fixed inset-0 bg-black/90 md:hidden backdrop-blur-sm",onClick:()=>setIsOpen(!1)}),e.jsx(motion.div,{initial:{x:"100%"},animate:{x:isOpen?0:"100%"},transition:{type:"tween"},className:"fixed top-0 right-0 bottom-0 w-3/4 bg-black shadow-xl md:hidden z-10 p-6",children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsx("div",{className:"flex-1 space-y-4 mt-16",children:m.map(t=>e.jsxs(Link,{to:t.to,onClick:()=>setIsOpen(!1),className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-800 transition-colors text-[#2DD4BF] group",children:[e.jsx(t.icon,{className:"text-[#2DD4BF] text-xl group-hover:scale-110 transition-transform"}),e.jsx("span",{className:"font-medium group-hover:translate-x-1 transition-transform",children:t.text})]},t.to))}),l?e.jsxs("button",{onClick:handleDashboardClick,className:"w-full bg-[#2DD4BF] text-white font-bold px-6 py-3 rounded-lg hover:bg-[#2DD4BF]/80 transition-all flex items-center justify-center gap-2",children:[e.jsx(FaUser,{}),e.jsx("span",{children:"Dashboard"})]}):e.jsx(Link,{to:"/login",onClick:()=>setIsOpen(!1),className:"w-full bg-[#2DD4BF] text-white font-bold px-6 py-3 rounded-lg hover:bg-[#2DD4BF]/80 transition-all text-center",children:"Sign In"})]})})]})})})}function z(){const h=D(),l=B(),[r,d]=i.useState(!0),[m,t]=i.useState(!1),[n,a]=i.useState(null),[x,o]=i.useState(null),[s,y]=i.useState({email:"",password:"",confirmPassword:"",username:"",fullName:""}),u=p=>{y({...s,[p.target.name]:p.target.value}),a(null)},w=()=>{if(!s.email||!s.password)return a("Please fill in all required fields"),!1;if(!r){if(s.password!==s.confirmPassword)return a("Passwords do not match"),!1;if(!s.username)return a("Username is required"),!1;if(s.password.length<6)return a("Password must be at least 6 characters"),!1}return!0},F=async p=>{var j;if(p.preventDefault(),!!w()){t(!0),a(null),o(null);try{if(r){console.log("Attempting login with:",s.email);const{session:c}=await v(s.email,s.password);if(c){console.log("Login successful, redirecting..."),await new Promise(k=>setTimeout(k,1e3));const g=((j=l.state)==null?void 0:j.from)||"/simplified-dashboard";h(g,{replace:!0})}}else{console.log("Attempting registration with:",s.email,s.username);const{user:c}=await P(s.email,s.password,s.username,s.fullName);if(c){console.log("Registration successful, attempting auto-login"),o("Account created successfully! Logging you in...");try{const{session:g}=await v(s.email,s.password);g&&(console.log("Auto-login successful, redirecting..."),h("/dashboard",{replace:!0}))}catch(g){console.error("Auto-login failed:",g),d(!0),y({...s,password:"",confirmPassword:""})}}}}catch(c){console.error("Auth error:",c),a(c.message||"An error occurred during authentication")}finally{t(!1)}}};return e.jsxs(e.Fragment,{children:[e.jsx(A,{}),e.jsxs("div",{className:"min-h-screen bg-black flex flex-col items-center justify-center px-4 py-20",children:[e.jsxs("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[e.jsx("div",{className:"w-full h-full opacity-[0.02]",style:{backgroundImage:`
                linear-gradient(to right, rgb(45, 212, 191) 1px, transparent 1px),
                linear-gradient(to bottom, rgb(45, 212, 191) 1px, transparent 1px)
              `,backgroundSize:"100px 100px"}}),e.jsx("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl"}),e.jsx("div",{className:"absolute bottom-1/3 right-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl"})]}),e.jsxs(f.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-md bg-black rounded-lg border border-gray-800 shadow-xl overflow-hidden relative z-10",children:[e.jsxs("div",{className:"p-6 sm:p-8",children:[e.jsx("div",{className:"text-center mb-6",children:e.jsx("div",{className:"inline-block mx-auto",children:e.jsxs("span",{className:"font-bold text-3xl",children:[e.jsx("span",{className:"text-[#2DD4BF]",children:"Cyber"}),e.jsx("span",{className:"text-amber-500",children:"XCerberus"})]})})}),e.jsx("h2",{className:"text-2xl sm:text-3xl font-bold text-center mb-8 bg-gradient-to-r from-[#2DD4BF] to-amber-500 text-transparent bg-clip-text",children:r?"Welcome Back":"Join XCerberus"}),e.jsxs("div",{className:"flex justify-center space-x-4 mb-8",children:[e.jsx("button",{onClick:()=>{d(!0),a(null),o(null)},className:`px-4 py-2 rounded-full transition-all duration-300 ${r?"bg-[#2DD4BF] text-black font-bold":"text-gray-500 hover:text-gray-300"}`,children:"Login"}),e.jsx("button",{onClick:()=>{d(!1),a(null),o(null)},className:`px-4 py-2 rounded-full transition-all duration-300 ${r?"text-gray-500 hover:text-gray-300":"bg-[#2DD4BF] text-black font-bold"}`,children:"Register"})]}),n&&e.jsx(f.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 rounded-lg bg-red-900/50 border border-red-500/50 text-red-400 text-sm text-center",children:n}),x&&e.jsx(f.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 rounded-lg bg-green-900/50 border border-green-500/50 text-green-400 text-sm text-center",children:x}),e.jsxs("form",{onSubmit:F,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-gray-300 text-sm font-medium mb-2 flex items-center",htmlFor:"email",children:[e.jsx(C,{className:"mr-2 text-[#2DD4BF]"}),"Email"]}),e.jsx("input",{type:"email",id:"email",name:"email",value:s.email,onChange:u,className:"w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors",placeholder:"<EMAIL>"})]}),!r&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-gray-300 text-sm font-medium mb-2 flex items-center",htmlFor:"username",children:[e.jsx(L,{className:"mr-2 text-[#2DD4BF]"}),"Username"]}),e.jsx("input",{type:"text",id:"username",name:"username",value:s.username,onChange:u,className:"w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors",placeholder:"Choose a username"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-gray-300 text-sm font-medium mb-2 flex items-center",htmlFor:"fullName",children:[e.jsx(S,{className:"mr-2 text-[#2DD4BF]"}),"Full Name"]}),e.jsx("input",{type:"text",id:"fullName",name:"fullName",value:s.fullName,onChange:u,className:"w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors",placeholder:"Your full name"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-gray-300 text-sm font-medium mb-2 flex items-center",htmlFor:"password",children:[e.jsx(N,{className:"mr-2 text-[#2DD4BF]"}),"Password"]}),e.jsx("input",{type:"password",id:"password",name:"password",value:s.password,onChange:u,className:"w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors",placeholder:"••••••••"})]}),!r&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-gray-300 text-sm font-medium mb-2 flex items-center",htmlFor:"confirmPassword",children:[e.jsx(N,{className:"mr-2 text-[#2DD4BF]"}),"Confirm Password"]}),e.jsx("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:s.confirmPassword,onChange:u,className:"w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors",placeholder:"••••••••"})]}),e.jsxs("button",{type:"submit",disabled:m,className:"w-full bg-[#2DD4BF] text-black font-bold py-3 px-4 rounded-lg hover:bg-[#2DD4BF]/80 transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group",children:[e.jsx("span",{className:"relative z-10",children:m?e.jsxs("span",{className:"flex items-center justify-center",children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):r?"Sign In":"Create Account"}),e.jsx("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-500",style:{transform:"translateX(-100%)"}})]})]}),r&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-6",children:[e.jsx("p",{className:"text-center text-sm text-gray-500 mb-4",children:"Test Accounts"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("button",{type:"button",onClick:()=>handleTestLogin("free"),className:"flex flex-col items-center justify-center p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors",children:[e.jsx(FaUserAlt,{className:"text-blue-400 text-xl mb-1"}),e.jsx("span",{className:"text-xs text-gray-300",children:"Free"})]}),e.jsxs("button",{type:"button",onClick:()=>handleTestLogin("premium"),className:"flex flex-col items-center justify-center p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors",children:[e.jsx(FaUserShield,{className:"text-green-400 text-xl mb-1"}),e.jsx("span",{className:"text-xs text-gray-300",children:"Premium"})]}),e.jsxs("button",{type:"button",onClick:()=>handleTestLogin("business"),className:"flex flex-col items-center justify-center p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors",children:[e.jsx(FaUserTie,{className:"text-purple-400 text-xl mb-1"}),e.jsx("span",{className:"text-xs text-gray-300",children:"Business"})]})]})]}),e.jsx("p",{className:"mt-6 text-center text-sm text-gray-500",children:e.jsx("a",{href:"#",className:"text-[#2DD4BF] hover:text-[#2DD4BF]/80 transition-colors",children:"Forgot your password?"})})]})]}),e.jsx("div",{className:"px-6 py-4 bg-gray-900 text-center text-sm border-t border-gray-800",children:e.jsxs("p",{className:"text-gray-400",children:[r?"Don't have an account? ":"Already have an account? ",e.jsx("button",{onClick:()=>{d(!r),a(null)},className:"text-[#2DD4BF] hover:text-[#2DD4BF]/80 font-medium transition-colors",children:r?"Sign up":"Sign in"})]})})]})]})]})}export{z as default};
