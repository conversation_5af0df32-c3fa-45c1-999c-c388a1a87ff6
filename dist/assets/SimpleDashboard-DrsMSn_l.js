import{u as A,au as B,b2 as F,g as L,r as h,j as e,b3 as P,b4 as M,V as z,l as U,I as T,b5 as I,b as E,d as x,U as _,n as u,D as m,i,b6 as f,ak as a,ai as w,b7 as k,h as j,ax as O,T as D,G as R}from"./index-c6UceSOv.js";const H=()=>{var v;const t=A(),{darkMode:s,toggleDarkMode:$}=B(),{user:n,profile:o}=F(),{subscriptionLevel:y,userCoins:C,isPremium:p,isBusiness:g}=L(),[c,S]=h.useState("overview");h.useEffect(()=>{n||console.log("No user found, but continuing for testing purposes")},[n]),h.useEffect(()=>{console.log("Active tab changed to:",c)},[c]);const N=f[y]||f.free,l=y==="free",d={name:(o==null?void 0:o.full_name)||(n==null?void 0:n.email)||"User",email:(n==null?void 0:n.email)||"<EMAIL>",avatar:(o==null?void 0:o.avatar_url)||null,progress:25,completedChallenges:l?2:12,completedModules:l?1:8,points:l?250:1250,coins:C||0,rank:l?"Beginner":"Advanced Beginner",recentActivity:[{id:1,type:"challenge",name:"SQL Injection Basics",date:"2 hours ago",points:100},{id:2,type:"module",name:"Web Security Fundamentals",date:"1 day ago",points:150},{id:3,type:"challenge",name:"Password Cracking Basics",date:"3 days ago",points:100}],recommendedContent:[{id:1,type:"challenge",name:"Network Traffic Analysis",difficulty:"Easy",locked:l},{id:2,type:"module",name:"Network Security Basics",difficulty:"Beginner",locked:!1},{id:3,type:"challenge",name:"OSINT Investigation",difficulty:"Easy",locked:l}]},b=[{name:"Overview",icon:e.jsx(O,{}),id:"overview",locked:!1,path:"/simplified-dashboard"},{name:"Learning Modules",icon:e.jsx(a,{}),id:"learning",locked:!1,limited:l,path:"/simplified-dashboard"},{name:"Challenges",icon:e.jsx(i,{}),id:"challenges",locked:!1,limited:l,path:"/simplified-dashboard"},{name:"Leaderboard",icon:e.jsx(j,{}),id:"leaderboard",locked:!1,path:"/simplified-dashboard"},{name:"Store",icon:e.jsx(k,{}),id:"store",locked:!1,path:"/simplified-dashboard"},{name:"Analytics",icon:e.jsx(w,{}),id:"analytics",locked:l,path:"/simplified-dashboard"},{name:"Settings",icon:e.jsx(D,{}),id:"settings",locked:!1,path:"/simplified-dashboard"}];return(p||g)&&b.splice(3,0,{name:"Start Hack",icon:e.jsx(R,{}),id:"starthack",locked:!1}),g&&b.splice(4,0,{name:"Team Management",icon:e.jsx(j,{}),id:"teams",locked:!1}),e.jsxs("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"}`,children:[e.jsx("header",{className:`fixed top-0 left-0 right-0 z-40 ${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border-b`,children:e.jsx("div",{className:"container mx-auto px-4 py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-[#88cc14] font-bold text-2xl mr-1",children:"X"}),e.jsx("span",{className:`font-bold text-2xl ${s?"text-white":"text-gray-900"}`,children:"Cerberus"})]}),e.jsx("div",{className:"ml-4",children:g?e.jsx("span",{className:"bg-purple-500 text-white text-xs px-2 py-1 rounded-full font-medium",children:"Business"}):p?e.jsx("span",{className:"bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-medium",children:"Premium"}):e.jsx("span",{className:"bg-gray-500 text-white text-xs px-2 py-1 rounded-full font-medium",children:"Free Tier"})})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("button",{onClick:$,className:`p-2 rounded-full ${s?"bg-gray-800 text-yellow-400 hover:bg-gray-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,"aria-label":"Toggle dark/light mode",children:s?e.jsx(P,{}):e.jsx(M,{})}),!g&&e.jsxs("div",{className:"hidden md:flex items-center mr-2",children:[e.jsx(z,{className:"text-yellow-500 mr-1"}),e.jsx("span",{className:`text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:d.coins})]}),l&&e.jsxs("button",{onClick:()=>t("/pricing"),className:"hidden md:flex items-center text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded-lg",children:[e.jsx(U,{className:"mr-1"}),"Upgrade"]}),e.jsxs("div",{className:`relative hidden md:block ${s?"text-gray-400":"text-gray-600"}`,children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(T,{className:"w-4 h-4"})}),e.jsx("input",{type:"text",className:`py-2 pl-10 pr-4 rounded-lg ${s?"bg-[#0B1120] border-gray-800 focus:border-gray-700":"bg-gray-100 border-gray-200 focus:border-gray-300"} border focus:outline-none focus:ring-1 focus:ring-[#88cc14]`,placeholder:"Search..."})]}),e.jsxs("button",{className:`p-2 rounded-full ${s?"hover:bg-gray-800":"hover:bg-gray-100"} relative`,children:[e.jsx(I,{className:s?"text-gray-400":"text-gray-600"}),e.jsx("span",{className:"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"})]}),e.jsx("div",{className:"relative",children:e.jsxs("button",{className:`flex items-center gap-2 ${s?"hover:bg-gray-800":"hover:bg-gray-100"} p-2 rounded-lg`,children:[e.jsx("div",{className:"w-8 h-8 bg-[#88cc14] rounded-full flex items-center justify-center text-black",children:d.avatar||e.jsx(E,{})}),e.jsx("span",{className:"hidden md:inline",children:d.name})]})})]})]})})}),e.jsxs("div",{className:"flex pt-16",children:[e.jsx("aside",{className:`fixed left-0 top-16 bottom-0 w-20 md:w-64 ${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border-r z-30 transition-all duration-300 overflow-y-auto`,children:e.jsx("div",{className:"p-4",children:e.jsxs("nav",{className:"space-y-1",children:[b.map(r=>e.jsxs("button",{onClick:()=>{r.locked||(S(r.id),window.location.pathname!=="/simplified-dashboard"&&t("/simplified-dashboard"))},disabled:r.locked,className:`flex items-center w-full p-3 rounded-lg transition-colors ${r.locked?s?"bg-gray-800/50 text-gray-500 cursor-not-allowed":"bg-gray-100/50 text-gray-400 cursor-not-allowed":c===r.id?"bg-[#88cc14] text-black":s?"text-gray-400 hover:bg-gray-800 hover:text-white":"text-gray-600 hover:bg-gray-100 hover:text-black"}`,children:[e.jsx("div",{className:"flex items-center justify-center w-6 h-6 md:mr-3",children:r.icon}),e.jsx("span",{className:"hidden md:block flex-1 text-left",children:r.name}),r.locked&&e.jsx(x,{className:"hidden md:block text-xs opacity-70 ml-2"}),r.limited&&!r.locked&&e.jsx("span",{className:"hidden md:block text-xs px-1.5 py-0.5 bg-gray-500/20 rounded text-gray-500 ml-2",children:"Limited"})]},r.id)),e.jsxs("button",{onClick:r=>{r.preventDefault(),r.stopPropagation(),localStorage.removeItem("supabase.auth.token"),localStorage.removeItem("supabase.auth.user"),localStorage.removeItem("user_subscription"),localStorage.removeItem("user_profile"),sessionStorage.clear(),window.location.replace("/login")},className:`flex items-center w-full p-3 rounded-lg transition-colors mt-8 ${s?"text-gray-400 hover:bg-gray-800 hover:text-white":"text-gray-600 hover:bg-gray-100 hover:text-black"}`,children:[e.jsx("div",{className:"flex items-center justify-center w-6 h-6 md:mr-3",children:e.jsx(_,{})}),e.jsx("span",{className:"hidden md:block flex-1 text-left",children:"Sign Out"})]})]})})}),e.jsxs("main",{className:"flex-1 ml-20 md:ml-64 p-6",children:[c==="overview"&&l&&e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6 relative overflow-hidden`,children:[e.jsx("div",{className:"absolute -top-20 -right-20 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl"}),e.jsx("div",{className:"absolute -bottom-20 -left-20 w-64 h-64 bg-yellow-500/5 rounded-full blur-3xl"}),e.jsxs("div",{className:"relative z-10 flex flex-col md:flex-row items-center justify-between gap-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:`text-2xl font-bold ${s?"text-white":"text-gray-900"} mb-2`,children:"Upgrade to Premium"}),e.jsx("p",{className:`${s?"text-gray-300":"text-gray-600"} mb-4`,children:"Unlock all features and accelerate your cybersecurity learning journey."}),e.jsxs("ul",{className:"space-y-2 mb-6",children:[e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(u,{className:"text-[#88cc14]"}),e.jsx("span",{className:`${s?"text-gray-300":"text-gray-600"}`,children:"Access all 50 learning modules"})]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(u,{className:"text-[#88cc14]"}),e.jsx("span",{className:`${s?"text-gray-300":"text-gray-600"}`,children:"Unlock 100+ challenges"})]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(u,{className:"text-[#88cc14]"}),e.jsx("span",{className:`${s?"text-gray-300":"text-gray-600"}`,children:"Start Hack simulations"})]})]})]}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("div",{className:"text-3xl font-bold text-yellow-500",children:"₹399"}),e.jsx("div",{className:`text-sm ${s?"text-gray-400":"text-gray-500"}`,children:"per month"})]}),e.jsxs("button",{onClick:()=>window.open("/pricing","_blank"),className:"py-2 px-6 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors flex items-center",children:["Upgrade Now ",e.jsx(m,{className:"ml-2"})]})]})]})]}),c==="overview"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("h1",{className:"text-2xl font-bold mb-2",children:["Welcome back, ",d.name,"!"]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:l?"Start your cybersecurity journey with our free learning resources.":"Continue your cybersecurity journey. You're making great progress!"}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700",children:e.jsx("div",{className:"bg-[#88cc14] h-2.5 rounded-full",style:{width:`${d.progress}%`}})}),e.jsxs("div",{className:"flex justify-between mt-2 text-sm",children:[e.jsxs("span",{children:[d.progress,"% complete"]}),e.jsxs("span",{children:["Level: ",d.rank]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:`font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Challenges"}),e.jsx(i,{className:"text-yellow-500"})]}),e.jsx("p",{className:"text-3xl font-bold",children:d.completedChallenges}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"completed"}),l&&e.jsxs("div",{className:"mt-2 text-xs text-[#88cc14]",children:[N.challenges.availableChallenges," available in free tier"]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:`font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Learning Modules"}),e.jsx(a,{className:"text-blue-500"})]}),e.jsx("p",{className:"text-3xl font-bold",children:d.completedModules}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"completed"}),l&&e.jsxs("div",{className:"mt-2 text-xs text-[#88cc14]",children:[N.learnModules.availableModules," available in free tier"]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:`font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Points"}),e.jsx(w,{className:"text-green-500"})]}),e.jsx("p",{className:"text-3xl font-bold",children:d.points}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"total earned"})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 relative`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:`font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Coins"}),e.jsx(k,{className:"text-purple-500"})]}),e.jsx("p",{className:"text-3xl font-bold",children:d.coins}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"available"}),l&&e.jsxs("button",{onClick:()=>t("/store"),className:"absolute bottom-2 right-2 text-xs text-[#88cc14] hover:underline flex items-center",children:["Get more ",e.jsx(m,{className:"ml-1 text-[8px]"})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-bold",children:"Recent Activity"}),l&&d.recentActivity.length>0&&e.jsx("span",{className:"text-xs px-2 py-1 bg-gray-500/20 rounded text-gray-500",children:"Limited History"})]}),d.recentActivity.length>0?e.jsx("div",{className:"space-y-4",children:d.recentActivity.map(r=>e.jsxs("div",{className:`flex items-start p-3 rounded-lg ${s?"bg-[#0B1120]":"bg-gray-50"}`,children:[e.jsx("div",{className:`p-2 rounded-lg mr-4 ${r.type==="challenge"?"bg-yellow-500/20 text-yellow-500":"bg-blue-500/20 text-blue-500"}`,children:r.type==="challenge"?e.jsx(i,{}):e.jsx(a,{})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-medium",children:r.name}),e.jsxs("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:["Completed ",r.date]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("span",{className:"font-bold text-[#88cc14]",children:["+",r.points]}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"points"})]})]},r.id))}):e.jsxs("div",{className:`p-6 text-center ${s?"text-gray-400":"text-gray-600"}`,children:[e.jsx("p",{children:"No recent activity yet."}),e.jsx("p",{className:"mt-2 text-sm",children:"Complete challenges and modules to see your activity here."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Recommended for You"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:d.recommendedContent.map(r=>e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"} relative`,children:[r.locked&&e.jsxs("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4",children:[e.jsx(x,{className:"text-2xl text-yellow-500 mb-2"}),e.jsx("p",{className:"text-white text-center text-sm mb-3",children:"Upgrade to Premium to access this content"}),e.jsx("button",{onClick:()=>t("/pricing"),className:"py-1 px-4 bg-yellow-500 hover:bg-yellow-600 text-black rounded-lg transition-colors text-sm",children:"Upgrade"})]}),e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:`p-2 rounded-lg mr-3 ${r.type==="challenge"?"bg-yellow-500/20 text-yellow-500":"bg-blue-500/20 text-blue-500"}`,children:r.type==="challenge"?e.jsx(i,{}):e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:r.name}),e.jsxs("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:[r.difficulty," • ",r.type]})]})]}),e.jsx("button",{onClick:()=>!r.locked&&window.open(r.type==="challenge"?`/challenges/${r.id}`:`/learn/${r.id}`,"_blank"),className:`w-full py-2 px-4 ${r.locked?"bg-gray-500 cursor-not-allowed":"bg-[#88cc14] hover:bg-[#7ab811]"} text-black rounded-lg transition-colors block text-center`,children:r.type==="challenge"?"Start Challenge":"Start Learning"})]},r.id))})]}),l&&e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Premium Features You're Missing"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsx("div",{className:`p-4 rounded-lg ${s?"bg-[#0B1120]":"bg-gray-50"}`,children:e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500",children:e.jsx(i,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"100+ Challenges"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Advanced cybersecurity challenges"})]})]})}),e.jsx("div",{className:`p-4 rounded-lg ${s?"bg-[#0B1120]":"bg-gray-50"}`,children:e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Full Learning Modules"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Complete learning curriculum"})]})]})}),e.jsx("div",{className:`p-4 rounded-lg ${s?"bg-[#0B1120]":"bg-gray-50"}`,children:e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-purple-500/20 text-purple-500",children:e.jsx(j,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Community Access"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Connect with other security professionals"})]})]})})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("button",{onClick:()=>t("/pricing"),className:"inline-flex items-center py-2 px-6 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors",children:["Upgrade to Premium ",e.jsx(m,{className:"ml-2"})]}),e.jsx("p",{className:`mt-2 text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Starting at just ₹399/month"})]})]})]}),c==="challenges"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Available Challenges"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-6`,children:"Start with these free challenges to test your cybersecurity skills."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500",children:e.jsx(i,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"SQL Injection Basics"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • Web Security"})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs bg-green-500/20 text-green-500 px-2 py-1 rounded",children:"Completed"}),e.jsx("button",{onClick:()=>t("/challenges/sql-injection-basics"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"View Challenge"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500",children:e.jsx(i,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Password Cracking Basics"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • Authentication"})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs bg-green-500/20 text-green-500 px-2 py-1 rounded",children:"Completed"}),e.jsx("button",{onClick:()=>t("/challenges/password-cracking-basics"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"View Challenge"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500",children:e.jsx(i,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"XSS Attack Simulation"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • Web Security"})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"}),e.jsx("button",{onClick:()=>t("/challenges/xss-attack-simulation"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Challenge"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500",children:e.jsx(i,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Network Traffic Analysis"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • Network Security"})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"}),e.jsx("button",{onClick:()=>t("/challenges/network-traffic-analysis"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Challenge"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500",children:e.jsx(i,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Basic Cryptography"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • Cryptography"})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"}),e.jsx("button",{onClick:()=>t("/challenges/basic-cryptography"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Challenge"})]})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 relative overflow-hidden`,children:[e.jsx("div",{className:"absolute -top-20 -right-20 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl"}),e.jsx("h2",{className:"text-2xl font-bold mb-4 relative z-10",children:"Premium Challenges"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-6 relative z-10`,children:"Upgrade to access 100+ advanced challenges across all difficulty levels."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 relative z-10",children:[e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"} relative`,children:[e.jsxs("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4",children:[e.jsx(x,{className:"text-2xl text-yellow-500 mb-2"}),e.jsx("p",{className:"text-white text-center text-sm",children:"Premium Challenge"})]}),e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-red-500/20 text-red-500",children:e.jsx(i,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Advanced Penetration Testing"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Advanced • Offensive Security"})]})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"} relative`,children:[e.jsxs("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4",children:[e.jsx(x,{className:"text-2xl text-yellow-500 mb-2"}),e.jsx("p",{className:"text-white text-center text-sm",children:"Premium Challenge"})]}),e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-purple-500/20 text-purple-500",children:e.jsx(i,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Reverse Engineering Malware"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Intermediate • Malware Analysis"})]})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"} relative`,children:[e.jsxs("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4",children:[e.jsx(x,{className:"text-2xl text-yellow-500 mb-2"}),e.jsx("p",{className:"text-white text-center text-sm",children:"Premium Challenge"})]}),e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(i,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Cloud Security Assessment"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Intermediate • Cloud Security"})]})]})]})]}),e.jsx("div",{className:"text-center relative z-10",children:e.jsxs("button",{onClick:()=>t("/pricing"),className:"inline-flex items-center py-2 px-6 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors",children:["Upgrade to Premium ",e.jsx(m,{className:"ml-2"})]})})]})]}),c==="learning"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Learning Modules"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:"Start your cybersecurity journey with our comprehensive learning paths."}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Your Learning Progress"}),e.jsx("span",{className:"text-sm font-medium",children:"1/3 Modules Completed"})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700",children:e.jsx("div",{className:"bg-[#88cc14] h-2.5 rounded-full",style:{width:"33%"}})})]}),e.jsxs("div",{className:"flex overflow-x-auto pb-4 space-x-4 mb-6",children:[e.jsxs("div",{className:`flex-shrink-0 w-64 p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsx("h3",{className:"font-bold text-lg mb-2",children:"Foundations"}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Master the essential concepts needed for cybersecurity"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs bg-green-500/20 text-green-500 px-2 py-1 rounded",children:"1/3 Complete"}),e.jsx("button",{className:"text-xs text-[#88cc14] hover:underline",children:"View Path"})]})]}),e.jsxs("div",{className:`flex-shrink-0 w-64 p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsx("h3",{className:"font-bold text-lg mb-2",children:"Defensive Security"}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Learn to protect systems and respond to threats"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Not Started"}),e.jsx("button",{className:"text-xs text-[#88cc14] hover:underline",children:"View Path"})]})]}),e.jsxs("div",{className:`flex-shrink-0 w-64 p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsx("h3",{className:"font-bold text-lg mb-2",children:"Offensive Security"}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Master penetration testing and ethical hacking"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Not Started"}),e.jsx("button",{className:"text-xs text-[#88cc14] hover:underline",children:"View Path"})]})]}),e.jsxs("div",{className:`flex-shrink-0 w-64 p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsx("h3",{className:"font-bold text-lg mb-2",children:"Specialized Areas"}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Explore advanced topics like malware analysis"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Not Started"}),e.jsx("button",{className:"text-xs text-[#88cc14] hover:underline",children:"View Path"})]})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-green-500/20 text-green-500",children:e.jsx(a,{className:"text-xl"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Foundations"}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Master the essential concepts needed for cybersecurity"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Introduction to Cybersecurity"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • 60 min • 4 sections"})]})]}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Learn the fundamentals of cybersecurity, key concepts, and why it matters."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:e.jsx("span",{className:"text-xs bg-green-500/20 text-green-500 px-2 py-1 rounded",children:"Completed"})}),e.jsx("button",{onClick:()=>t("/learn/intro-to-cybersecurity"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Continue Learning"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Operating System Concepts"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • 90 min • 5 sections"})]})]}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Understand how operating systems work and their security implications."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"})}),e.jsx("button",{onClick:()=>t("/learn/os-concepts"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Learning"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Network Fundamentals"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • 120 min • 6 sections"})]})]}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Learn how networks function, protocols, and network security basics."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"})}),e.jsx("button",{onClick:()=>t("/learn/network-fundamentals"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Learning"})]})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{className:"text-xl"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Defensive Security"}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Learn to protect systems and respond to threats"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Security Operations Basics"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • 90 min • 5 sections"})]})]}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Learn the fundamentals of SOC operations and threat monitoring."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"})}),e.jsx("button",{onClick:()=>window.open("/learn/soc-basics","_blank"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Learning"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Threat Hunting Fundamentals"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • 75 min • 4 sections"})]})]}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Learn the basics of proactively searching for threats in your environment."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"})}),e.jsx("button",{onClick:()=>window.open("/learn/threat-hunting-basics","_blank"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Learning"})]})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-red-500/20 text-red-500",children:e.jsx(a,{className:"text-xl"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Offensive Security"}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Master penetration testing and ethical hacking"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Ethical Hacking Fundamentals"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • 90 min • 5 sections"})]})]}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Learn the basics of ethical hacking and penetration testing methodology."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"})}),e.jsx("button",{onClick:()=>window.open("/learn/ethical-hacking-basics","_blank"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Learning"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Web Application Security"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • 120 min • 6 sections"})]})]}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Learn about common web vulnerabilities and how to exploit them ethically."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"})}),e.jsx("button",{onClick:()=>window.open("/learn/web-app-security","_blank"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Learning"})]})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-purple-500/20 text-purple-500",children:e.jsx(a,{className:"text-xl"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Specialized Areas"}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Explore advanced topics in cybersecurity"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Linux for Cybersecurity"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • 120 min • 6 sections"})]})]}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Learn essential Linux skills for cybersecurity professionals."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"})}),e.jsx("button",{onClick:()=>window.open("/learn/linux-for-cybersecurity","_blank"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Learning"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"}`,children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Scripting for Security"}),e.jsx("p",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Beginner • 90 min • 5 sections"})]})]}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"} mb-3`,children:"Learn basic scripting with Python and Bash for security automation."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:e.jsx("span",{className:"text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded",children:"Available"})}),e.jsx("button",{onClick:()=>window.open("/learn/scripting-for-security","_blank"),className:"text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded",children:"Start Learning"})]})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 relative overflow-hidden`,children:[e.jsx("div",{className:"absolute -top-20 -right-20 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl"}),e.jsx("h2",{className:"text-2xl font-bold mb-4 relative z-10",children:"Premium Learning Modules"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-6 relative z-10`,children:"Upgrade to access all 50 learning modules with in-depth content and hands-on labs."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 relative z-10",children:[e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"} relative`,children:[e.jsxs("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4",children:[e.jsx(x,{className:"text-2xl text-yellow-500 mb-2"}),e.jsx("p",{className:"text-white text-center text-sm",children:"Premium Module"})]}),e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Advanced Penetration Testing"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Advanced • 120 min"})]})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"} relative`,children:[e.jsxs("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4",children:[e.jsx(x,{className:"text-2xl text-yellow-500 mb-2"}),e.jsx("p",{className:"text-white text-center text-sm",children:"Premium Module"})]}),e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Secure Coding Practices"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Intermediate • 90 min"})]})]})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"border-gray-800 bg-[#0B1120]":"border-gray-200 bg-gray-50"} relative`,children:[e.jsxs("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4",children:[e.jsx(x,{className:"text-2xl text-yellow-500 mb-2"}),e.jsx("p",{className:"text-white text-center text-sm",children:"Premium Module"})]}),e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500",children:e.jsx(a,{})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:"Cloud Security Architecture"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Intermediate • 105 min"})]})]})]})]}),e.jsx("div",{className:"text-center relative z-10",children:e.jsxs("button",{onClick:()=>t("/pricing"),className:"inline-flex items-center py-2 px-6 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors",children:["Upgrade to Premium ",e.jsx(m,{className:"ml-2"})]})})]})]}),c!=="overview"&&c!=="challenges"&&c!=="learning"&&e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:(v=b.find(r=>r.id===c))==null?void 0:v.name}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"This section is under development. Please check back later!"})]})]})]})]})};export{H as default};
