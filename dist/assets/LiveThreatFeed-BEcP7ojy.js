import{r as c,j as e,av as z,E as f,bB as R,a9 as V,x as M,aM as $,H as K,z as W,ap as X,aa as H,ad as Y,y as G,d as J}from"./index-c6UceSOv.js";import{c as Q,b as Z}from"./ThreatAnalyticsDashboard-CJk_8dys.js";const ae=()=>{const[v,q]=c.useState([]),[i,j]=c.useState(!0),[ee,U]=c.useState("live"),[m,x]=c.useState(!0),[y,h]=c.useState(null),b="8cf85380b2f8cccfedf8d392ca780cfab0503f4a253a105a229a938a24ac0a65302ed162ee2f031a",N="437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0",w=s=>{switch(s.toLowerCase()){case"ransomware":return e.jsx(J,{className:"text-red-500"});case"ddos":return e.jsx(G,{className:"text-orange-500"});case"phishing":return e.jsx(Y,{className:"text-yellow-500"});case"malware":return e.jsx(H,{className:"text-purple-500"});case"data breach":return e.jsx(X,{className:"text-blue-500"});case"apt":case"advanced persistent threat":return e.jsx(W,{className:"text-pink-500"});case"network attack":return e.jsx(K,{className:"text-green-500"});case"unknown":return e.jsx(M,{className:"text-gray-500"});default:return e.jsx(f,{className:"text-blue-500"})}},B=s=>{switch(s.toLowerCase()){case"critical":return"bg-red-900 text-red-200";case"high":return"bg-orange-900 text-orange-200";case"medium":return"bg-yellow-900 text-yellow-200";case"low":return"bg-green-900 text-green-200";default:return"bg-blue-900 text-blue-200"}},O=s=>new Date(s).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",second:"2-digit"});c.useEffect(()=>{const s=Q(b),r=Z(N),n=async()=>{try{x(!0),h(null);const[d,p]=await Promise.allSettled([s.getBlacklist(80,15).catch(t=>(console.error("AbuseIPDB API error:",t),[])),r.getPulses(5).catch(t=>(console.error("OTX API error:",t),[]))]),C=d.status==="fulfilled"?d.value:[],D=p.status==="fulfilled"?p.value:[];if(C.length===0&&D.length===0&&d.status==="rejected"&&p.status==="rejected")throw new Error("Unable to connect to threat intelligence APIs. Using sample data instead.");const P=C.map(t=>({id:`ip-${t.ipAddress}`,type:"Malicious IP",severity:T(t.abuseConfidenceScore),source:t.countryCode||"Unknown",target:"Multiple",industry:"Various",timestamp:new Date().toISOString(),color:k(T(t.abuseConfidenceScore)),details:{ipAddress:t.ipAddress,confidenceScore:t.abuseConfidenceScore,totalReports:t.totalReports,countryCode:t.countryCode}})),E=D.map(t=>{var L,F;let l="Unknown";t.tags&&Array.isArray(t.tags)&&(t.tags.some(o=>o.toLowerCase().includes("ransomware"))?l="Ransomware":t.tags.some(o=>o.toLowerCase().includes("malware"))?l="Malware":t.tags.some(o=>o.toLowerCase().includes("phishing"))?l="Phishing":t.tags.some(o=>o.toLowerCase().includes("ddos"))&&(l="DDoS"));const u=t.adversary?"High":"Medium";return{id:`pulse-${t.id||Math.random().toString(36).substring(2,15)}`,type:l,severity:u,source:t.author_name||"Threat Actor",target:((L=t.targeted_countries)==null?void 0:L.join(", "))||"Multiple",industry:((F=t.industries)==null?void 0:F.join(", "))||"Various",timestamp:t.created||new Date().toISOString(),color:k(u),details:{name:t.name||"Unknown Threat",description:t.description||"No description available",tags:t.tags||[],references:t.references||[]}}});if(P.length===0&&E.length===0)throw new Error("No threat data available. Please try again later.");{const t=[...P,...E].sort((l,u)=>new Date(u.timestamp)-new Date(l.timestamp));q(t),U("live")}x(!1)}catch(d){console.error("Error fetching threat data:",d),h(d.message||"Failed to fetch live threat data. Please try again."),x(!1),setTimeout(()=>{i&&(h(null),x(!0),n())},1e4)}};n();let g;return i&&(g=setInterval(n,3e4)),()=>{g&&clearInterval(g)}},[i,b,N]);const T=s=>s>=90?"Critical":s>=80?"High":s>=60?"Medium":"Low",k=s=>{switch(s.toLowerCase()){case"critical":return"#ef4444";case"high":return"#f97316";case"medium":return"#eab308";case"low":return"#22c55e";default:return"#3b82f6"}},[a,S]=c.useState(null),[I,A]=c.useState(!1),_=s=>({ransomware:{tactics:["Impact","Execution"],techniques:["T1486: Data Encrypted for Impact","T1490: Inhibit System Recovery"],description:"Ransomware encrypts files and demands payment for decryption keys. It often enters systems through phishing emails or exploiting vulnerabilities.",mitigation:"Maintain offline backups, implement application allowlisting, keep systems patched, and use anti-ransomware solutions.",url:"https://attack.mitre.org/techniques/T1486/"},phishing:{tactics:["Initial Access","Credential Access"],techniques:["T1566: Phishing","T1534: Internal Spearphishing"],description:"Phishing attacks use deceptive emails, messages, or websites to steal credentials or deliver malware by tricking users into taking harmful actions.",mitigation:"Implement email filtering, user awareness training, multi-factor authentication, and disable macros in documents from the internet.",url:"https://attack.mitre.org/techniques/T1566/"},ddos:{tactics:["Impact"],techniques:["T1498: Network Denial of Service","T1499: Endpoint Denial of Service"],description:"DDoS attacks overwhelm services with excessive traffic or requests, making them unavailable to legitimate users.",mitigation:"Use DDoS protection services, implement rate limiting, and configure network infrastructure to handle traffic surges.",url:"https://attack.mitre.org/techniques/T1498/"},malware:{tactics:["Execution","Defense Evasion","Discovery"],techniques:["T1204: User Execution","T1027: Obfuscated Files or Information"],description:"Malware is malicious software designed to damage systems, steal data, or gain unauthorized access to networks.",mitigation:"Use anti-malware solutions, keep systems updated, implement application allowlisting, and practice the principle of least privilege.",url:"https://attack.mitre.org/tactics/TA0002/"},"data breach":{tactics:["Exfiltration","Collection"],techniques:["T1048: Exfiltration Over Alternative Protocol","T1114: Email Collection"],description:"Data breaches involve unauthorized access to sensitive data, often followed by data theft or exposure.",mitigation:"Encrypt sensitive data, implement data loss prevention tools, monitor for unusual data access patterns, and use network segmentation.",url:"https://attack.mitre.org/tactics/TA0010/"},apt:{tactics:["Persistence","Privilege Escalation","Defense Evasion"],techniques:["T1078: Valid Accounts","T1053: Scheduled Task/Job","T1055: Process Injection"],description:"Advanced Persistent Threats are prolonged, targeted attacks by sophisticated threat actors, often nation-states, aiming to maintain long-term access to networks.",mitigation:"Implement defense-in-depth strategies, conduct threat hunting, monitor for unusual behavior, and use advanced endpoint protection.",url:"https://attack.mitre.org/groups/"},"network attack":{tactics:["Lateral Movement","Discovery"],techniques:["T1046: Network Service Scanning","T1021: Remote Services"],description:"Network attacks target network infrastructure and services to gain access, disrupt operations, or move laterally within environments.",mitigation:"Implement network segmentation, use intrusion detection systems, monitor network traffic, and secure remote access services.",url:"https://attack.mitre.org/tactics/TA0008/"}})[s.toLowerCase()]||{tactics:["Multiple"],techniques:["Various techniques may be employed"],description:"This threat type encompasses various attack vectors and methods that may target different aspects of systems and networks.",mitigation:"Implement defense-in-depth strategies, keep systems updated, and follow security best practices appropriate to your environment.",url:"https://attack.mitre.org/"};return e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${i?"bg-green-500 animate-pulse":"bg-gray-500"} mr-2`}),e.jsxs("span",{className:"text-sm text-gray-400",children:[i?"Live Feed":"Paused"," • Real-time Threat Data"]}),e.jsx("button",{className:"ml-2 text-gray-400 hover:text-gray-300",onClick:()=>A(!I),title:"Information about the threat feed",children:e.jsx(z,{size:14})})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:`px-2 py-1 text-xs rounded ${i?"bg-red-600 hover:bg-red-700":"bg-green-600 hover:bg-green-700"}`,onClick:()=>j(!i),disabled:m,children:i?"Pause":"Resume"}),e.jsx("button",{className:"px-2 py-1 text-xs rounded bg-blue-600 hover:bg-blue-700",onClick:()=>{i||j(!0)},disabled:m||i,children:"Refresh Now"})]})]}),I&&e.jsxs("div",{className:"mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[e.jsx(f,{className:"mr-1 text-blue-400"})," Live Threat Intelligence Feed"]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>A(!1),children:"×"})]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"This feed displays real-time cyber threats detected across the globe. Data is collected from multiple threat intelligence sources including AbuseIPDB and AlienVault OTX. Click on any threat to see detailed information including MITRE ATT&CK framework references and mitigation strategies."}),e.jsx("div",{className:"flex items-center text-xs",children:e.jsxs("a",{href:"https://attack.mitre.org/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 flex items-center",children:["Learn more about MITRE ATT&CK ",e.jsx(R,{className:"ml-1",size:10})]})})]}),m&&e.jsxs("div",{className:"flex items-center justify-center py-4",children:[e.jsx(V,{className:"animate-spin text-blue-500 mr-2"}),e.jsx("span",{className:"text-gray-300",children:"Fetching live threat data..."})]}),y&&e.jsxs("div",{className:"bg-red-900 bg-opacity-20 border border-red-800 rounded-lg p-3 mb-4 text-red-400",children:[e.jsx(M,{className:"inline-block mr-2"}),y]}),e.jsxs("div",{className:"flex-1 flex flex-col md:flex-row gap-4 overflow-hidden",children:[e.jsx("div",{className:`overflow-y-auto space-y-2 pr-1 ${a?"md:w-3/5":"w-full"}`,children:!m&&v.length===0?e.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:"No threat data available"}):v.map(s=>e.jsxs("div",{className:`bg-gray-700 rounded-lg p-3 border-l-4 animate-fadeIn cursor-pointer transition-colors ${a===s?"bg-gray-600":"hover:bg-gray-650"}`,style:{borderLeftColor:s.color},onClick:()=>S(a===s?null:s),children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"mt-0.5 mr-2",children:w(s.type)}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:s.type}),e.jsxs("div",{className:"text-xs text-gray-400 flex items-center",children:[e.jsx($,{className:"mr-1",size:10}),e.jsx("span",{className:"mr-1",children:s.source})," → ",e.jsx("span",{className:"ml-1",children:s.target})]})]})]}),e.jsxs("div",{className:"flex flex-col items-end",children:[e.jsx("span",{className:`text-xs px-1.5 py-0.5 rounded-full ${B(s.severity)}`,children:s.severity}),e.jsx("span",{className:"text-xs text-gray-400",children:O(s.timestamp)})]})]}),e.jsxs("div",{className:"mt-1 text-xs grid grid-cols-1 gap-0.5",children:[s.id.startsWith("ip-")&&e.jsxs("div",{className:"flex flex-wrap",children:[e.jsx("span",{className:"text-gray-400 mr-1",children:"IP:"})," ",s.details.ipAddress," •",e.jsx("span",{className:"text-gray-400 mx-1",children:"Conf:"})," ",s.details.confidenceScore,"% •",e.jsx("span",{className:"text-gray-400 mx-1",children:"Reports:"})," ",s.details.totalReports||0]}),s.id.startsWith("pulse-")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-400 mr-1",children:"Name:"})," ",s.details.name]}),s.details.tags&&s.details.tags.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-0.5",children:[s.details.tags.slice(0,3).map((r,n)=>e.jsx("span",{className:"bg-gray-800 px-1 py-0.5 rounded text-xs",children:r},n)),s.details.tags.length>3&&e.jsxs("span",{className:"bg-gray-800 px-1 py-0.5 rounded text-xs",children:["+",s.details.tags.length-3]})]})]})]})]},s.id))}),a&&e.jsxs("div",{className:"md:w-2/5 bg-gray-800 rounded-lg p-4 overflow-y-auto",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[w(a.type),e.jsxs("span",{className:"ml-2",children:[a.type," Details"]})]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>S(null),children:"×"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Source"}),e.jsx("div",{className:"font-medium",children:a.source})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Target"}),e.jsx("div",{className:"font-medium",children:a.target})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Severity"}),e.jsx("div",{className:"font-medium",children:a.severity})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Timestamp"}),e.jsx("div",{className:"font-medium",children:new Date(a.timestamp).toLocaleString()})]})]}),a.id.startsWith("ip-")&&e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"IP Details"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"IP Address"}),e.jsx("div",{children:a.details.ipAddress})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Country"}),e.jsx("div",{children:a.details.countryCode||"Unknown"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Confidence Score"}),e.jsxs("div",{children:[a.details.confidenceScore,"%"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Total Reports"}),e.jsx("div",{children:a.details.totalReports||0})]})]})]}),a.id.startsWith("pulse-")&&e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Threat Intelligence"}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Name"}),e.jsx("div",{className:"text-sm",children:a.details.name})]}),a.details.description&&e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Description"}),e.jsx("div",{className:"text-sm",children:a.details.description})]}),a.details.tags&&a.details.tags.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:a.details.tags.map((s,r)=>e.jsx("span",{className:"bg-gray-600 px-2 py-0.5 rounded text-xs",children:s},r))})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"font-medium mb-2 flex items-center",children:[e.jsx(f,{className:"mr-1 text-blue-400"})," MITRE ATT&CK Framework"]}),(()=>{const s=_(a.type);return e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg text-sm",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Tactics"}),e.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:s.tactics.map((r,n)=>e.jsx("span",{className:"bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full text-xs",children:r},n))})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Techniques"}),e.jsx("div",{className:"flex flex-col gap-1 mt-1",children:s.techniques.map((r,n)=>e.jsx("span",{className:"text-xs",children:r},n))})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Description"}),e.jsx("p",{className:"text-xs mt-1",children:s.description})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Mitigation Strategies"}),e.jsx("p",{className:"text-xs mt-1",children:s.mitigation})]}),e.jsx("div",{className:"text-right",children:e.jsxs("a",{href:s.url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 text-xs flex items-center justify-end",children:["Learn more ",e.jsx(R,{className:"ml-1",size:10})]})})]})})()]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Recommended Actions"}),e.jsxs("ul",{className:"list-disc list-inside text-sm space-y-1 text-gray-300",children:[e.jsxs("li",{children:["Monitor for similar ",a.type.toLowerCase()," activity in your environment"]}),e.jsxs("li",{children:["Review logs for connections to ",a.source]}),e.jsx("li",{children:"Update security controls based on the MITRE ATT&CK techniques"}),e.jsx("li",{children:"Share this intelligence with your security team"})]})]})]})]}),e.jsxs("div",{className:"mt-4 text-xs text-gray-400",children:[e.jsx("strong",{children:"Data Sources:"})," AbuseIPDB API and AlienVault OTX API •",e.jsx("strong",{children:"Update Frequency:"})," ",i?"Every 30 seconds":"Paused"," •",e.jsx("strong",{children:"Last Updated:"})," ",new Date().toLocaleTimeString()]})]})};export{ae as default};
