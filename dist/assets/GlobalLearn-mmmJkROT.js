import{u as Y,b2 as q,g as G,r as l,bR as n,j as e,M as ne,N as ie,m as K,O as T,P as j,Q as _,d as P,aX as oe,l as ce,ak as V}from"./index-c6UceSOv.js";import{U as R}from"./UpgradeBanner-Bw_AdR7a.js";import{L as de}from"./LockedItem-BNePvFlE.js";import{l as W,g as xe,O as ue,a as ge,K as me}from"./KnowledgeCheck-WZsBtKyo.js";import{L as be}from"./LabTerminal-Bk3L4em0.js";import"./Button-jOBCH0E8.js";const he={OSLayersVisualization:ge,OSTypesVisualization:ue},fe=()=>{const m=Y(),{user:b}=q(),{subscriptionLevel:M,hasAccess:i,getRemainingContent:h}=G(),[d,v]=l.useState("fundamentals"),[x,C]=l.useState("os-concepts"),[u,L]=l.useState("os-introduction"),[f,k]=l.useState(["fundamentals"]),[N,I]=l.useState(["os-concepts"]),[w,X]=l.useState(!0),[S,F]=l.useState(!1),[D,A]=l.useState(!1),p=!!b,E=xe(u),g=W.find(t=>t.id===d),r=g==null?void 0:g.modules.find(t=>t.id===x),U=r==null?void 0:r.topics.find(t=>t.id===u),[H,B]=l.useState(!0),[Q,O]=l.useState(0);l.useEffect(()=>{if(r)if(!p)B(r.tier===n.FREE),O(0);else{const t=i("learnModules",r.tier||"free");B(t);const{remaining:a}=h("learnModules");O(a)}},[r,M,i,h,p]);const J=t=>{k(a=>a.includes(t)?a.filter(s=>s!==t):[...a,t])},Z=t=>{I(a=>a.includes(t)?a.filter(s=>s!==t):[...a,t])},y=(t,a,s)=>{v(t),C(a),L(s),f.includes(t)||k([...f,t]),N.includes(a)||I([...N,a]),S&&F(!1)},ee=()=>{X(!w)},se=()=>{F(!S)},te=t=>{switch(t){case n.PREMIUM:return e.jsx(ce,{className:"text-yellow-400"});case n.BUSINESS:return e.jsx(oe,{className:"text-blue-400"});default:return null}},$=t=>{if(!t||t===n.FREE)return null;const a=te(t),s=i("learnModules",t);return e.jsxs("span",{className:`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${s?t===n.PREMIUM?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"}`,children:[a,e.jsx("span",{className:"ml-1",children:t.charAt(0).toUpperCase()+t.slice(1)}),!s&&e.jsx(P,{className:"ml-1 text-xs"})]})},z=()=>e.jsx("div",{className:`bg-gray-100 dark:bg-gray-800 overflow-y-auto ${w?"w-64 flex-shrink-0":"w-0"} transition-all duration-300 ease-in-out`,children:w&&e.jsxs("div",{className:"p-4",children:[e.jsx("h2",{className:"text-xl font-bold mb-4 text-gray-800 dark:text-white",children:"Learning Paths"}),e.jsx("div",{className:"space-y-2",children:W.map(t=>e.jsxs("div",{className:"rounded-lg overflow-hidden",children:[e.jsxs("button",{className:`w-full flex items-center justify-between p-3 text-left font-medium rounded-lg ${d===t.id?"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"}`,onClick:()=>J(t.id),children:[e.jsx("span",{className:"truncate",children:t.title}),f.includes(t.id)?e.jsx(_,{className:"flex-shrink-0"}):e.jsx(j,{className:"flex-shrink-0"})]}),f.includes(t.id)&&e.jsx("div",{className:"mt-1 ml-2 pl-2 border-l-2 border-gray-300 dark:border-gray-600",children:t.modules.map(a=>e.jsxs("div",{className:"mt-1",children:[e.jsxs("button",{className:`w-full flex items-center justify-between p-2 text-left text-sm font-medium rounded-lg ${x===a.id?"bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-200":"text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700/50"}`,onClick:()=>Z(a.id),children:[e.jsxs("span",{className:"flex items-center truncate",children:[a.title,$(a.tier)]}),N.includes(a.id)?e.jsx(_,{className:"flex-shrink-0"}):e.jsx(j,{className:"flex-shrink-0"})]}),N.includes(a.id)&&e.jsx("div",{className:"mt-1 ml-2 pl-2 border-l-2 border-gray-200 dark:border-gray-700",children:a.topics.map(s=>e.jsx("button",{className:`w-full flex items-center justify-between p-2 text-left text-xs rounded-lg ${u===s.id?"bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-200":"text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-700/30"}`,onClick:()=>y(t.id,a.id,s.id),children:e.jsxs("span",{className:"flex items-center truncate",children:[s.title,$(s.tier)]})},s.id))})]},a.id))})]},t.id))})]})}),ae=()=>H?E?e.jsxs("div",{className:"prose prose-lg dark:prose-invert max-w-none",children:[e.jsx("h1",{children:E.title}),E.sections.map((t,a)=>e.jsxs("div",{className:"mb-8",children:[t.title&&e.jsx("h2",{children:t.title}),t.content.map((s,o)=>{if(s.type==="text")return e.jsx("p",{dangerouslySetInnerHTML:{__html:s.value}},o);if(s.type==="code")return e.jsx("pre",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto",children:e.jsx("code",{children:s.value})},o);if(s.type==="image")return e.jsxs("div",{className:"my-4",children:[e.jsx("img",{src:s.src,alt:s.alt||"Learning content image",className:"rounded-lg max-w-full h-auto"}),s.caption&&e.jsx("p",{className:"text-sm text-center text-gray-500 dark:text-gray-400 mt-2",children:s.caption})]},o);if(s.type==="visualization"){const c=he[s.component];return c?e.jsx("div",{className:"my-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:e.jsx(c,{...s.props})},o):null}else{if(s.type==="lab")return e.jsx("div",{className:"my-6",children:D?e.jsxs("div",{className:"border rounded-lg overflow-hidden",children:[e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 px-4 py-2 flex justify-between items-center",children:[e.jsxs("h3",{className:"text-lg font-medium",children:["Lab: ",s.title]}),e.jsx("button",{className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",onClick:()=>A(!1),children:"Close Lab"})]}),e.jsx("div",{className:"p-4",children:e.jsx(be,{commands:s.commands})})]}):e.jsx("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",onClick:()=>A(!0),children:"Start Lab Exercise"})},o);if(s.type==="quiz")return e.jsx("div",{className:"my-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:e.jsx(me,{questions:s.questions})},o)}return null})]},a)),e.jsxs("div",{className:"flex justify-between mt-8 pt-4 border-t border-gray-200 dark:border-gray-700",children:[re(),le()]})]}):e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Select a topic to start learning"})}):e.jsx(de,{title:(r==null?void 0:r.title)||"Premium Module",description:p?"This module is only available with a higher subscription tier.":"Sign in to access this premium module.",tier:(r==null?void 0:r.tier)||n.PREMIUM,actionText:p?"Upgrade to Access":"Sign In",onAction:()=>m(p?"/pricing":"/login")}),re=()=>{if(!r||!U)return e.jsx("div",{});const t=r.topics.findIndex(s=>s.id===u);if(t>0){const s=r.topics[t-1];return e.jsxs("button",{className:"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",onClick:()=>y(d,x,s.id),children:[e.jsx(T,{className:"mr-2"}),"Previous: ",s.title]})}const a=g.modules.findIndex(s=>s.id===x);if(a>0){const s=g.modules[a-1],o=s.topics[s.topics.length-1],c=i("learnModules",s.tier||"free");return e.jsxs("button",{className:`flex items-center ${c?"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300":"text-gray-400 dark:text-gray-600 cursor-not-allowed"}`,onClick:()=>{c&&y(d,s.id,o.id)},children:[e.jsx(T,{className:"mr-2"}),"Previous: ",s.title,!c&&e.jsx(P,{className:"ml-2"})]})}return e.jsx("div",{})},le=()=>{if(!r||!U)return e.jsx("div",{});const t=r.topics.findIndex(s=>s.id===u);if(t<r.topics.length-1){const s=r.topics[t+1];return e.jsxs("button",{className:"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",onClick:()=>y(d,x,s.id),children:["Next: ",s.title,e.jsx(j,{className:"ml-2"})]})}const a=g.modules.findIndex(s=>s.id===x);if(a<g.modules.length-1){const s=g.modules[a+1],o=s.topics[0],c=i("learnModules",s.tier||"free");return e.jsxs("button",{className:`flex items-center ${c?"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300":"text-gray-400 dark:text-gray-600 cursor-not-allowed"}`,onClick:()=>{c&&y(d,s.id,o.id)},children:["Next: ",s.title,e.jsx(j,{className:"ml-2"}),!c&&e.jsx(P,{className:"ml-2"})]})}return e.jsx("div",{})};return e.jsxs("div",{className:"flex flex-col h-full bg-white dark:bg-gray-900",children:[e.jsxs("div",{className:"md:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h1",{className:"text-xl font-bold text-gray-800 dark:text-white",children:"Learning"}),e.jsx("button",{className:"p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700",onClick:se,children:S?e.jsx(ne,{}):e.jsx(ie,{})})]}),S&&e.jsx(K.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},className:"mt-4 overflow-hidden",children:z()})]}),e.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[e.jsx("div",{className:"hidden md:block",children:z()}),e.jsx("button",{className:"hidden md:flex items-center justify-center w-6 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors",onClick:ee,children:w?e.jsx(T,{}):e.jsx(j,{})}),e.jsxs("div",{className:"flex-1 overflow-auto p-4 md:p-8",children:[M===n.FREE&&e.jsx("div",{className:"mb-6",children:e.jsx(R,{title:"Upgrade to Access All Learning Modules",description:`You have access to ${Q} more modules with your current plan.`,buttonText:"Upgrade Now",onButtonClick:()=>m("/pricing")})}),ae()]})]})]})},we=()=>{const m=Y(),{user:b,loading:M}=q(),{subscriptionLevel:i,loading:h,getRemainingContent:d}=G(),[v,x]=l.useState(0),[C,u]=l.useState(!1);l.useEffect(()=>{if(localStorage.getItem("has_visited_learn")||(u(!0),localStorage.setItem("has_visited_learn","true")),!h&&b){const{remaining:k}=d("learnModules");x(k)}},[h,d,b]);const L=()=>C?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs(K.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6",children:[e.jsx("div",{className:"flex items-center justify-center mb-4",children:e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900 p-3 rounded-full",children:e.jsx(V,{className:"text-blue-600 dark:text-blue-300 text-2xl"})})}),e.jsx("h2",{className:"text-2xl font-bold text-center mb-4 text-gray-800 dark:text-white",children:"Welcome to XCerberus Learning"}),e.jsxs("p",{className:"text-gray-600 dark:text-gray-300 mb-6 text-center",children:["Explore our comprehensive cybersecurity curriculum designed for all skill levels.",i===n.FREE&&e.jsxs("span",{className:"block mt-2 text-sm",children:["You have access to ",e.jsx("span",{className:"font-bold",children:v})," modules with your free account."]})]}),e.jsxs("div",{className:"flex flex-col space-y-3",children:[e.jsx("button",{className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors",onClick:()=>u(!1),children:"Start Learning"}),i===n.FREE&&e.jsx("button",{className:"w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-lg transition-colors",onClick:()=>m("/pricing"),children:"Explore Premium"})]})]})}):null;return e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[e.jsx(V,{className:"mr-2 text-blue-600 dark:text-blue-400"}),"Learning Center"]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Master cybersecurity skills with our comprehensive curriculum"})]}),i===n.FREE&&e.jsx("div",{className:"hidden sm:block",children:e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>m("/pricing"),children:"Upgrade for Full Access"})})]})})}),e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[!b&&e.jsx("div",{className:"mb-6",children:e.jsx(R,{title:"Sign In to Track Your Progress",description:"Create an account or sign in to save your progress and access more learning content.",buttonText:"Sign In",onButtonClick:()=>m("/login"),variant:"prominent"})}),b&&i===n.FREE&&e.jsx("div",{className:"mb-6",children:e.jsx(R,{title:"Unlock All Learning Modules",description:`You have access to ${v} more modules with your free account. Upgrade to unlock all content.`,buttonText:"View Plans",onButtonClick:()=>m("/pricing")})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden h-[calc(100vh-12rem)]",children:e.jsx(fe,{})})]}),L()]})};export{we as default};
