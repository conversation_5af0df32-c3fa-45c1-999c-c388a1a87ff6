import{b2 as L,au as U,r as c,j as e,bG as q,aN as n,l as T,bW as H,ag as C,bX as R,s as d}from"./index-c6UceSOv.js";const G=()=>{const{user:i,profile:m}=L(),{darkMode:t}=U(),[r,b]=c.useState(null),[h,E]=c.useState([]),[p,y]=c.useState(!0),[j,u]=c.useState(null),[f,N]=c.useState(!1),[A,w]=c.useState(!1),[_,v]=c.useState(!1);c.useEffect(()=>{(async()=>{if(i)try{y(!0);const{data:a,error:o}=await d.from("subscription_tracking").select(`
            id,
            plan:subscription_plans(id, name, price, currency, features),
            start_date,
            end_date,
            status,
            auto_renew,
            payment_id,
            payment_method
          `).eq("user_id",i.id).eq("status","active").order("created_at",{ascending:!1}).limit(1).single();if(o&&o.code!=="PGRST116")throw o;const{data:l,error:g}=await d.from("subscription_tracking").select(`
            id,
            plan:subscription_plans(id, name, price, currency),
            start_date,
            end_date,
            status,
            created_at
          `).eq("user_id",i.id).order("created_at",{ascending:!1}).limit(10);if(g)throw g;b(a),E(l)}catch(a){console.error("Error fetching subscription data:",a),u(a.message)}finally{y(!1)}})()},[i]);const k=async s=>{if(i)try{N(!0),u(null);const{data:a,error:o}=await d.from("subscription_plans").select("*").eq("name",s).single();if(o)throw o;const l=new Date;l.setDate(l.getDate()+30);const{data:g,error:D}=await d.from("subscription_tracking").insert([{user_id:i.id,plan_id:a.id,start_date:new Date,end_date:l,status:"active",auto_renew:!0,payment_id:`sim_${Date.now()}`,payment_method:"credit_card"}]).select().single();if(D)throw D;const{error:S}=await d.from("profiles").update({subscription_tier:s,subscription_start_date:new Date,subscription_end_date:l,auto_renew:!0,updated_at:new Date}).eq("id",i.id);if(S)throw S;if(r){const{error:$}=await d.from("subscription_tracking").update({status:"cancelled",updated_at:new Date}).eq("id",r.id);if($)throw $}w(!0),setTimeout(()=>{window.location.reload()},2e3)}catch(a){console.error("Error upgrading subscription:",a),u(a.message)}finally{N(!1)}},F=async()=>{if(!(!i||!r))try{v(!0),u(null);const{error:s}=await d.from("subscription_tracking").update({status:"cancelled",auto_renew:!1,updated_at:new Date}).eq("id",r.id);if(s)throw s;const{error:a}=await d.from("profiles").update({auto_renew:!1,updated_at:new Date}).eq("id",i.id);if(a)throw a;b({...r,status:"cancelled",auto_renew:!1}),w(!0),setTimeout(()=>{window.location.reload()},2e3)}catch(s){console.error("Error cancelling subscription:",s),u(s.message)}finally{v(!1)}},x=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),P=s=>{if(!s)return 0;const l=new Date(s)-new Date,g=Math.ceil(l/(1e3*60*60*24));return g>0?g:0};return!p&&!i?e.jsx(q,{to:"/login"}):e.jsx("div",{className:`min-h-screen ${t?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Subscription Management"}),j&&e.jsx("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:j})}),A&&e.jsxs("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 flex items-center",children:[e.jsx(n,{className:"mr-2"}),e.jsx("p",{children:"Your subscription has been updated successfully!"})]}),e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6 mb-6`,children:[e.jsxs("h2",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx(T,{className:"mr-2 text-[#88cc14]"})," Current Subscription"]}),p?e.jsx("p",{children:"Loading subscription data..."}):r?e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-bold",children:[r.plan.name.charAt(0).toUpperCase()+r.plan.name.slice(1)," Plan"]}),e.jsx("p",{className:`${t?"text-gray-400":"text-gray-600"}`,children:r.plan.price?`${r.plan.currency||"₹"}${r.plan.price}/month`:"Custom pricing"})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx("span",{className:`px-3 py-1 rounded-full text-sm ${r.status==="active"?"bg-green-100 text-green-800":r.status==="cancelled"?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"}`,children:r.status.charAt(0).toUpperCase()+r.status.slice(1)})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsxs("div",{className:`${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-300"} rounded-lg border p-4`,children:[e.jsx("p",{className:`text-sm ${t?"text-gray-400":"text-gray-600"} mb-1`,children:"Start Date"}),e.jsx("p",{className:"font-medium",children:x(r.start_date)})]}),e.jsxs("div",{className:`${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-300"} rounded-lg border p-4`,children:[e.jsx("p",{className:`text-sm ${t?"text-gray-400":"text-gray-600"} mb-1`,children:"End Date"}),e.jsx("p",{className:"font-medium",children:x(r.end_date)})]}),e.jsxs("div",{className:`${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-300"} rounded-lg border p-4`,children:[e.jsx("p",{className:`text-sm ${t?"text-gray-400":"text-gray-600"} mb-1`,children:"Days Remaining"}),e.jsxs("p",{className:"font-medium",children:[P(r.end_date)," days"]})]})]}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:`${t?"text-gray-400":"text-gray-600"} mb-2`,children:"Auto-Renewal"}),e.jsx("div",{className:"flex items-center",children:r.auto_renew?e.jsxs(e.Fragment,{children:[e.jsx(n,{className:"text-green-500 mr-2"}),e.jsxs("span",{children:["Enabled - Your subscription will renew on ",x(r.end_date)]})]}):e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"text-red-500 mr-2"}),e.jsxs("span",{children:["Disabled - Your subscription will expire on ",x(r.end_date)]})]})})]}),r.status==="active"&&e.jsx("button",{onClick:F,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg",disabled:_,children:_?"Processing...":"Cancel Subscription"})]})]}):e.jsxs("div",{children:[e.jsx("p",{className:"mb-4",children:"You don't have an active subscription."}),e.jsx(C,{to:"/pricing",className:"theme-button-primary px-4 py-2 rounded-lg inline-block",children:"View Subscription Plans"})]})]}),(m==null?void 0:m.subscription_tier)!=="business"&&e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Upgrade Your Subscription"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(m==null?void 0:m.subscription_tier)!=="premium"&&e.jsxs("div",{className:`${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-300"} rounded-lg border p-6`,children:[e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Premium Plan"}),e.jsxs("p",{className:"text-2xl font-bold mb-4",children:["₹399",e.jsx("span",{className:`text-sm ${t?"text-gray-400":"text-gray-600"}`,children:"/month"})]}),e.jsxs("ul",{className:"mb-6 space-y-2",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"Full access to all learning modules"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"15 premium challenges"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"Complete access to all Start Hack features"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"Community access with expert support"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"Access to real Linux boxes for practice"]})]}),e.jsx("button",{onClick:()=>k("premium"),className:"theme-button-primary w-full py-2 rounded-lg",disabled:f,children:f?"Processing...":"Upgrade to Premium"})]}),e.jsxs("div",{className:`${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-300"} rounded-lg border p-6`,children:[e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Business Plan"}),e.jsx("p",{className:"text-2xl font-bold mb-4",children:"Contact Sales"}),e.jsxs("ul",{className:"mb-6 space-y-2",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"Everything in Premium"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"Team formation capabilities"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"Group participation in challenges"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"Internal team messaging system"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(n,{className:"text-green-500 mr-2"}),"Custom learning paths for teams"]})]}),e.jsx(C,{to:"/contact",className:`${t?"bg-[#1A1F35] hover:bg-[#252D4A] text-white":"bg-gray-200 hover:bg-gray-300 text-gray-800"} w-full py-2 rounded-lg block text-center`,children:"Contact Sales"})]})]})]}),e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6`,children:[e.jsxs("h2",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx(R,{className:"mr-2"})," Subscription History"]}),p?e.jsx("p",{children:"Loading subscription history..."}):h.length===0?e.jsx("p",{children:"No subscription history found."}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:`${t?"border-gray-800":"border-gray-200"} border-b`,children:[e.jsx("th",{className:"px-4 py-2 text-left",children:"Plan"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Start Date"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"End Date"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Status"})]})}),e.jsx("tbody",{children:h.map(s=>e.jsxs("tr",{className:`${t?"border-gray-800 hover:bg-[#252D4A]":"border-gray-200 hover:bg-gray-100"} border-b`,children:[e.jsx("td",{className:"px-4 py-2",children:s.plan.name.charAt(0).toUpperCase()+s.plan.name.slice(1)}),e.jsx("td",{className:"px-4 py-2",children:x(s.start_date)}),e.jsx("td",{className:"px-4 py-2",children:x(s.end_date)}),e.jsx("td",{className:"px-4 py-2",children:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${s.status==="active"?"bg-green-100 text-green-800":s.status==="expired"?"bg-red-100 text-red-800":s.status==="cancelled"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})})]},s.id))})]})})]})]})})};export{G as default};
