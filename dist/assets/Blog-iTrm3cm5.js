import{au as j,r as o,j as e,aL as A,I as S,al as T,b as N,a3 as x}from"./index-c6UceSOv.js";const k=()=>{const{darkMode:r}=j(),[h,l]=o.useState([]),[f,m]=o.useState(!0),[c,b]=o.useState(""),[n,g]=o.useState("");o.useEffect(()=>{(async()=>{m(!0);try{const i=localStorage.getItem("xcerberus_blog_posts");let s=[];if(i)try{s=JSON.parse(i),console.log("Loaded blog posts from localStorage:",s)}catch(d){console.error("Error parsing stored blog posts:",d)}(!s||s.length===0)&&(s=p(),console.log("Using sample blog posts")),l(s)}catch(i){console.error("Error loading blog posts:",i),l(p())}finally{m(!1)}})();const a=setInterval(()=>{try{const i=localStorage.getItem("xcerberus_blog_posts");if(i){const s=JSON.parse(i);l(s)}}catch(i){console.error("Error checking for new blog posts:",i)}},6e4);return()=>clearInterval(a)},[]);const p=()=>{const t=[],a=new Date;t.push({id:"post-1",title:"Global Threat Intelligence Report: Ransomware Surge Targeting Healthcare",content:`
        <h2>Executive Summary</h2>
        <p>Our threat intelligence team has identified a significant 73% increase in ransomware attacks targeting healthcare organizations over the past 30 days. This surge appears to be part of a coordinated campaign originating primarily from threat actors in Eastern Europe.</p>

        <h2>Key Findings</h2>
        <ul>
          <li>73% increase in ransomware attacks targeting healthcare organizations</li>
          <li>New zero-day vulnerability discovered in widely-used networking equipment</li>
          <li>Coordinated DDoS campaign affecting multiple financial institutions</li>
          <li>Advanced persistent threat (APT) group activity increased by 28%</li>
        </ul>

        <h2>Attack Vector Analysis</h2>
        <p>The primary attack vectors include phishing emails with malicious attachments, exploitation of unpatched VPN vulnerabilities, and compromised third-party service providers. The attackers are using sophisticated social engineering techniques to bypass security awareness training.</p>

        <h2>Recommendations</h2>
        <p>Organizations should immediately patch systems, implement multi-factor authentication, and review incident response plans. Security teams should increase monitoring for the specific indicators of compromise detailed in the full report.</p>
      `,summary:"Our threat intelligence team has identified a significant 73% increase in ransomware attacks targeting healthcare organizations over the past 30 days.",author:"XCerberus Research Team",date:a.toISOString(),tags:["Ransomware","Healthcare","Zero-day","APT"],image:"https://images.unsplash.com/photo-**********-824ae1b704d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"});const i=new Date(a.getTime()-4*60*60*1e3);t.push({id:"post-2",title:"Financial Sector Under Attack: Coordinated DDoS Campaign Analysis",content:`
        <h2>Executive Summary</h2>
        <p>A coordinated Distributed Denial of Service (DDoS) campaign has been detected targeting major financial institutions across North America and Europe. The attacks appear to be using a botnet of compromised IoT devices to generate traffic volumes exceeding 1 Tbps.</p>

        <h2>Attack Pattern</h2>
        <p>The attacks follow a distinctive pattern, beginning with a small probing attack followed by a massive volumetric attack 15-20 minutes later. This suggests reconnaissance followed by targeted disruption.</p>

        <h2>Geographic Distribution</h2>
        <p>Attack traffic is originating primarily from compromised devices in Southeast Asia, Eastern Europe, and South America. The command and control infrastructure appears to be hosted on bulletproof hosting providers in multiple jurisdictions.</p>

        <h2>Mitigation Strategies</h2>
        <p>Financial institutions should implement rate limiting, traffic filtering at the network edge, and consider engaging with DDoS protection services. Sharing attack signatures with industry partners is recommended to improve collective defense.</p>
      `,summary:"A coordinated Distributed Denial of Service (DDoS) campaign has been detected targeting major financial institutions across North America and Europe.",author:"XCerberus Research Team",date:i.toISOString(),tags:["DDoS","Financial","Botnet","IoT"],image:"https://images.unsplash.com/photo-**********-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"});const s=new Date(a.getTime()-8*60*60*1e3);t.push({id:"post-3",title:"Critical Infrastructure Vulnerability Alert: Energy Sector at Risk",content:`
        <h2>Executive Summary</h2>
        <p>Our threat intelligence has identified a new vulnerability affecting SCADA systems commonly used in energy sector infrastructure. The vulnerability allows for remote code execution without authentication and is being actively exploited in the wild.</p>

        <h2>Technical Details</h2>
        <p>The vulnerability (CVE-2023-XXXXX) affects the authentication mechanism in SCADA control systems, allowing attackers to bypass security controls and execute arbitrary code with system privileges.</p>

        <h2>Observed Exploitation</h2>
        <p>We have detected exploitation attempts originating from multiple threat actors, including known APT groups with ties to nation-state activities. The attacks are highly targeted and focused on energy production and distribution facilities.</p>

        <h2>Immediate Actions Required</h2>
        <p>Energy sector organizations should immediately apply the vendor-provided patch or implement the recommended workarounds. Network segmentation should be verified, and enhanced monitoring should be implemented for affected systems.</p>
      `,summary:"Our threat intelligence has identified a new vulnerability affecting SCADA systems commonly used in energy sector infrastructure.",author:"XCerberus Research Team",date:s.toISOString(),tags:["SCADA","Energy","Critical Infrastructure","Vulnerability"],image:"https://images.unsplash.com/photo-1605792657660-596af9009e82?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1402&q=80"});const d=new Date(a.getTime()-12*60*60*1e3);t.push({id:"post-4",title:"Supply Chain Attack Campaign: Software Repositories Compromised",content:`
        <h2>Executive Summary</h2>
        <p>A sophisticated supply chain attack has been detected targeting popular open-source software repositories. Malicious code has been injected into several widely-used development libraries, potentially affecting thousands of downstream applications.</p>

        <h2>Attack Methodology</h2>
        <p>The attackers have compromised developer accounts and injected malicious code that exfiltrates sensitive data while maintaining the original functionality of the libraries. The code is obfuscated to avoid detection by automated scanning tools.</p>

        <h2>Affected Packages</h2>
        <p>The following packages have been confirmed as compromised: [list of package names and versions]. These packages are commonly used in web development, data processing, and cloud infrastructure management.</p>

        <h2>Recommended Actions</h2>
        <p>Organizations should immediately audit their software dependencies, verify the integrity of installed packages, and implement software composition analysis as part of their CI/CD pipeline. Multi-factor authentication should be enforced for all developer accounts.</p>
      `,summary:"A sophisticated supply chain attack has been detected targeting popular open-source software repositories.",author:"XCerberus Research Team",date:d.toISOString(),tags:["Supply Chain","Software","Open Source","Data Exfiltration"],image:"https://images.unsplash.com/photo-**********-ef010cbdcc31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80"});const w=new Date(a.getTime()-16*60*60*1e3);return t.push({id:"post-5",title:"Emerging Threat: AI-Powered Phishing Campaigns on the Rise",content:`
        <h2>Executive Summary</h2>
        <p>Our threat intelligence has identified a significant increase in sophisticated phishing campaigns leveraging AI-generated content. These campaigns are notable for their highly personalized content, grammatically correct text, and contextually relevant messaging.</p>

        <h2>AI Techniques Employed</h2>
        <p>The attackers are using large language models to generate personalized phishing emails based on information harvested from social media and data breaches. The AI-generated content is highly convincing and bypasses traditional phishing detection methods.</p>

        <h2>Target Selection</h2>
        <p>These campaigns are primarily targeting executives, finance personnel, and employees with access to sensitive systems. The attackers appear to be conducting extensive reconnaissance to identify high-value targets within organizations.</p>

        <h2>Defense Strategies</h2>
        <p>Organizations should update their security awareness training to include examples of AI-generated phishing, implement DMARC/DKIM/SPF email authentication, and consider AI-powered email security solutions that can detect subtle anomalies in communication patterns.</p>
      `,summary:"Our threat intelligence has identified a significant increase in sophisticated phishing campaigns leveraging AI-generated content.",author:"XCerberus Research Team",date:w.toISOString(),tags:["Phishing","AI","Social Engineering","Email Security"],image:"https://images.unsplash.com/photo-1496096265110-f83ad7f96608?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"}),t},u=h.filter(t=>{const a=c===""||t.title.toLowerCase().includes(c.toLowerCase())||t.summary.toLowerCase().includes(c.toLowerCase()),i=n===""||t.tags.includes(n);return a&&i}),y=[...new Set(h.flatMap(t=>t.tags))].sort(),v=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return e.jsx("div",{className:`min-h-screen ${r?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-800"}`,children:e.jsxs("div",{className:"container mx-auto px-4 py-8 pt-24",children:[e.jsx("p",{className:"text-[#88cc14] mb-2 text-sm font-medium",children:"THREAT INTELLIGENCE"}),e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:`text-2xl font-bold ${r?"text-white":"text-gray-800"}`,children:"Global Cyber Threat Analysis"}),e.jsxs("button",{className:"px-3 py-1.5 bg-[#88cc14] text-white rounded-md hover:bg-[#6ba811] transition-colors flex items-center gap-1 text-sm",children:[e.jsx(A,{className:"text-xs"})," Subscribe"]})]}),e.jsx("p",{className:`${r?"text-gray-400":"text-gray-600"} mb-8 text-sm border-b border-gray-800 pb-4`,children:"Analysis of global cyber threats, updated every 4 hours with the latest intelligence."}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search threat analysis...",value:c,onChange:t=>b(t.target.value),className:`w-full p-2 pl-8 text-sm rounded-md ${r?"bg-gray-800/50 text-white border-gray-700":"bg-white text-gray-800 border-gray-300"} border`}),e.jsx(S,{className:"absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs"})]})}),f?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#88cc14]"})}):u.length===0?e.jsx("div",{className:`text-center p-8 ${r?"bg-gray-800":"bg-gray-100"} rounded-lg`,children:e.jsx("p",{className:"text-lg",children:"No blog posts found matching your criteria."})}):e.jsx("div",{className:"space-y-6",children:u.map(t=>e.jsx("div",{className:`border-b ${r?"border-gray-800":"border-gray-200"} pb-6 mb-6 last:border-0`,children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[t.image&&e.jsx("div",{className:"md:w-1/3 h-48 overflow-hidden rounded-md",children:e.jsx("img",{src:t.image,alt:t.title,className:"w-full h-full object-cover transition-transform hover:scale-105"})}),e.jsxs("div",{className:"md:w-2/3",children:[e.jsx("h2",{className:"text-xl font-bold mb-2 hover:text-[#88cc14] transition-colors",children:t.title}),e.jsxs("div",{className:"flex flex-wrap items-center text-xs text-gray-500 mb-3 gap-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(T,{className:"mr-1 text-[10px]"}),v(t.date)]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(N,{className:"mr-1 text-[10px]"}),"XCerberus Research Team"]})]}),e.jsx("p",{className:`mb-3 text-sm ${r?"text-gray-400":"text-gray-600"}`,children:t.summary}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:t.tags.map(a=>e.jsxs("span",{className:`px-2 py-0.5 text-xs rounded-full cursor-pointer flex items-center ${n===a?"bg-[#88cc14] text-white":r?"bg-gray-800 text-gray-400 hover:bg-gray-700":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,onClick:()=>g(a===n?"":a),children:[e.jsx(x,{className:"mr-1 text-[8px]"}),a]},a))}),e.jsx("button",{className:"px-3 py-1 text-sm bg-transparent border border-[#88cc14] text-[#88cc14] rounded-md hover:bg-[#88cc14]/10 transition-colors",children:"Read Analysis"})]})]})},t.id))})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-6 bg-[#0F172A] rounded-md p-4 border border-gray-800",children:[e.jsx("h3",{className:"text-sm font-bold mb-3 text-gray-300",children:"FILTER BY TOPIC"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:y.map(t=>e.jsxs("span",{className:`px-2 py-0.5 text-xs rounded-full cursor-pointer flex items-center ${n===t?"bg-[#88cc14] text-white":"bg-gray-800 text-gray-400 hover:bg-gray-700"}`,onClick:()=>g(t===n?"":t),children:[e.jsx(x,{className:"mr-1 text-[8px]"}),t]},t))})]}),e.jsxs("div",{className:"bg-[#0F172A] rounded-md p-4 border border-gray-800",children:[e.jsx("h3",{className:"text-sm font-bold mb-3 text-gray-300",children:"ABOUT THREAT INTELLIGENCE"}),e.jsx("p",{className:"text-gray-400 text-xs mb-3 leading-relaxed",children:"The XCerberus Threat Intelligence feed provides expert analysis of global cyber threats. Our team continuously monitors threat data and publishes new insights every 4 hours."}),e.jsx("p",{className:"text-gray-400 text-xs mb-4 leading-relaxed",children:"Each report analyzes patterns in attack data, identifies emerging threats, and provides actionable security recommendations."}),e.jsxs("div",{className:"bg-[#88cc14]/5 p-3 rounded-md border border-[#88cc14]/10",children:[e.jsx("h4",{className:"font-medium text-[#88cc14] text-xs mb-2",children:"Research Methodology"}),e.jsxs("ol",{className:"text-gray-400 text-xs space-y-1 list-decimal list-inside leading-relaxed",children:[e.jsx("li",{children:"Collection of threat data from multiple sources"}),e.jsx("li",{children:"Pattern analysis and threat identification"}),e.jsx("li",{children:"Detailed technical and strategic assessment"}),e.jsx("li",{children:"Publication of findings every 4 hours"})]})]})]})]})]})]})})};export{k as default};
