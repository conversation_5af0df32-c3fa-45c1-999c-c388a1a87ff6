import{b2 as w,au as N,r as s,j as e,bG as y,ai as A,h as S,V as D}from"./index-c6UceSOv.js";const E=()=>{const{user:i}=w(),{darkMode:r}=N(),[t,c]=s.useState("overview"),[U,b]=s.useState({totalUsers:0,premiumUsers:0,totalRevenue:0,activeSubscriptions:0,totalChallenges:0,totalModules:0}),[k,v]=s.useState([]),[_,p]=s.useState([]),[u,l]=s.useState(!0),[h,d]=s.useState(null),[o,f]=s.useState(!1),[R,g]=s.useState(!1);return s.useEffect(()=>{(async()=>{var a,x;if(i)try{const n=((a=i.user_metadata)==null?void 0:a.is_admin)===!0,m=((x=i.user_metadata)==null?void 0:x.is_super_admin)===!0;f(n||m),g(m),!n&&!m&&d("You do not have permission to access this page")}catch(n){console.error("Error checking admin status:",n),d(n.message)}})()},[i]),s.useEffect(()=>{if(!o)return;(async()=>{try{l(!0),b({totalUsers:100,premiumUsers:25,totalRevenue:15e3,activeSubscriptions:30,totalChallenges:50,totalModules:20}),v([{id:1,username:"user1",subscription_tier:"premium",created_at:new Date},{id:2,username:"user2",subscription_tier:"free",created_at:new Date}]),p([{id:1,status:"active",end_date:new Date,user:{username:"user1"},plan:{name:"Premium",price:15}}]),l(!1)}catch(a){console.error("Error fetching dashboard data:",a),d(a.message),l(!1)}})()},[o]),!u&&!o?e.jsx(y,{to:"/"}):e.jsx(e.Fragment,{children:e.jsx("div",{className:`min-h-screen ${r?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row",children:[e.jsxs("div",{className:`w-full md:w-64 mb-6 md:mb-0 md:mr-6 ${r?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Admin Panel"}),e.jsx("nav",{children:e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsxs("button",{onClick:()=>c("overview"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${t==="overview"?"bg-[#88cc14] text-black":r?"hover:bg-[#252D4A]":"hover:bg-gray-100"}`,children:[e.jsx(A,{className:"mr-2"})," Overview"]})}),e.jsx("li",{children:e.jsxs("button",{onClick:()=>c("users"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${t==="users"?"bg-[#88cc14] text-black":r?"hover:bg-[#252D4A]":"hover:bg-gray-100"}`,children:[e.jsx(S,{className:"mr-2"})," Users"]})}),e.jsx("li",{children:e.jsxs("button",{onClick:()=>c("revenue"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${t==="revenue"?"bg-[#88cc14] text-black":r?"hover:bg-[#252D4A]":"hover:bg-gray-100"}`,children:[e.jsx(D,{className:"mr-2"})," Revenue"]})})]})})]}),e.jsx("div",{className:"flex-1",children:u?e.jsx("div",{className:`${r?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6`,children:e.jsx("p",{children:"Loading dashboard data..."})}):h?e.jsx("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:e.jsx("p",{children:h})}):e.jsxs("div",{children:[t==="overview"&&e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Dashboard Overview"}),e.jsx("p",{children:"Overview content here"})]}),t==="users"&&e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"User Management"}),e.jsx("p",{children:"User management interface will be implemented here."})]}),t==="revenue"&&e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Revenue Analytics"}),e.jsx("p",{children:"Revenue analytics interface will be implemented here."})]}),t==="notifications"&&e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Notifications"})})]})})]})})})})};export{E as default};
