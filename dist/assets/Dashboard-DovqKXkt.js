import{r as A,j as e,V as xe,R as Se,a7 as Pe,c as Fe,Q as G,A as me,m as D,T as Ae,U as ge,s as P,M as Te,a8 as Q,l as U,n as le,a9 as Me,g as q,B as _e,aa as Re,ab as De,v as Ee,E as V,d as Le,z as Be,G as H,H as Z,ac as $e,ad as Ue,ae as We,af as J,a6 as Ie,ag as O,N as Oe,P as ne,ah as Y,i as K,ai as W,aj as ee,ak as se,y as he,al as ue,k as pe,am as be,an as te,D as $,ao as je,ap as ye,u as ae,w as ie,x as fe,aq as ze,ar as Ye,as as qe,at as Ve}from"./index-c6UceSOv.js";function He({profile:a,subscription:n,coins:x,onSignOut:m}){var u;const[d,t]=A.useState(!1);return e.jsx("div",{className:"bg-[#1A1F35] border-b border-gray-800 sticky top-0 z-40",children:e.jsx("div",{className:"max-w-[2000px] mx-auto px-4 sm:px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2 bg-[#88cc14]/10 px-3 py-1.5 rounded-lg",children:[e.jsx(xe,{className:"text-[#88cc14] text-sm"}),e.jsxs("span",{className:"text-[#88cc14] font-bold text-sm hidden sm:inline",children:[(x==null?void 0:x.balance)||0," XC"]})]}),e.jsxs("div",{className:"hidden sm:flex items-center gap-2 bg-black/20 px-3 py-1.5 rounded-lg",children:[e.jsx(Se,{className:"text-gray-400 text-sm"}),e.jsx("span",{className:"text-gray-400 text-sm",children:"Wallet"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("button",{className:"relative p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-white/5",children:[e.jsx(Pe,{}),e.jsx("span",{className:"absolute top-1 right-1 w-2 h-2 bg-[#88cc14] rounded-full"})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("button",{onClick:()=>t(!d),className:"flex items-center gap-3 hover:bg-white/5 p-2 rounded-lg transition-colors",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-[#88cc14]/10 flex items-center justify-center",children:a!=null&&a.avatar_url?e.jsx("img",{src:a.avatar_url,alt:a.username,className:"w-full h-full rounded-full object-cover"}):e.jsx(Fe,{className:"text-[#88cc14]"})}),e.jsxs("div",{className:"text-left hidden sm:block",children:[e.jsx("p",{className:"text-sm font-medium text-white",children:(a==null?void 0:a.username)||"User"}),e.jsxs("p",{className:"text-xs text-gray-400",children:[((u=n==null?void 0:n.subscription_plan)==null?void 0:u.name)||"Free"," Plan"]})]}),e.jsx(G,{className:"text-gray-400 text-xs"})]}),e.jsx(me,{children:d&&e.jsx(D.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},className:"absolute right-0 mt-2 w-48 bg-[#1A1F35] border border-gray-800 rounded-lg shadow-xl overflow-hidden z-50",children:e.jsxs("div",{className:"p-2",children:[e.jsxs("button",{onClick:()=>{},className:"w-full flex items-center gap-3 p-2 text-gray-400 hover:text-white hover:bg-white/5 rounded-lg transition-colors text-left",children:[e.jsx(Ae,{}),e.jsx("span",{children:"Settings"})]}),e.jsxs("button",{onClick:m,className:"w-full flex items-center gap-3 p-2 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded-lg transition-colors text-left",children:[e.jsx(ge,{}),e.jsx("span",{children:"Sign Out"})]})]})})})]})]})]})})})}const Xe=async a=>{try{const{data:{session:n}}=await P.auth.getSession();if(!(n!=null&&n.user))throw new Error("Not authenticated");const{data:x,error:m}=await P.rpc("handle_subscription_upgrade",{p_user_id:n.user.id,p_new_plan_name:a});if(m)throw m;const{data:d,error:t}=await P.from("user_subscriptions").select(`
        *,
        subscription_plans (*)
      `).eq("user_id",n.user.id).single();if(t)throw t;return d}catch(n){throw console.error("Error starting subscription:",n),n}},Je=async(a,n=!0)=>{var x;try{const{data:{session:m}}=await P.auth.getSession();if(!(m!=null&&m.user))throw new Error("Not authenticated");const{data:d}=await P.from("user_subscriptions").select(`
        *,
        subscription_plans (name)
      `).eq("user_id",m.user.id).single(),t=((x=d==null?void 0:d.subscription_plans)==null?void 0:x.name)||"Free";if(t===a)return d;const u=await Xe(a);return n&&await Ge(t,a),u}catch(m){throw console.error("Error upgrading subscription:",m),m}},Ge=async(a,n)=>{try{const{data:{session:x}}=await P.auth.getSession();if(!(x!=null&&x.user))return;await P.from("subscription_changes").insert({user_id:x.user.id,from_plan:a,to_plan:n,change_time:new Date().toISOString()}),await P.from("user_activity").insert({user_id:x.user.id,activity_type:"subscription_change",description:`Upgraded from ${a} to ${n}`,created_at:new Date().toISOString()})}catch(x){console.error("Error tracking subscription change:",x)}},Qe=({isOpen:a,onClose:n,targetPlan:x="Premium",currentPlan:m="Free"})=>{const[d,t]=A.useState(!1),[u,o]=A.useState(null),[i,s]=A.useState(!1),l={Free:{icon:null,title:"Free Plan",price:"₹0",color:"gray-600",features:["Access to basic labs","Limited challenges","Community support"]},Premium:{icon:U,title:"Premium Plan",price:"₹399",color:"#88cc14",features:["Unlimited labs and challenges","Attack Box access","Private VPN","Priority support"]},Business:{icon:Q,title:"Business Plan",price:"₹999",color:"#88cc14",features:["All Premium features","Team management","Custom labs","Dedicated support","Unlimited team members"]}},b=async()=>{try{t(!0),o(null),await Je(x),s(!0),setTimeout(()=>{n(),window.location.reload()},2e3)}catch(E){console.error("Error upgrading subscription:",E),o("Failed to upgrade subscription. Please try again.")}finally{t(!1)}},f=l[x],F=l[m];return a?e.jsx(me,{children:a&&e.jsx(D.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsxs(D.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white rounded-xl shadow-xl max-w-md w-full overflow-hidden",children:[e.jsxs("div",{className:"relative bg-gradient-to-r from-[#0B1120] to-[#1A1F35] p-6 text-white",children:[e.jsx("button",{onClick:n,disabled:d,className:"absolute top-4 right-4 text-white/80 hover:text-white",children:e.jsx(Te,{})}),e.jsxs("div",{className:"flex items-center gap-3",children:[f.icon&&e.jsx(f.icon,{className:"text-[#88cc14] text-2xl"}),e.jsxs("h2",{className:"text-xl font-bold",children:["Upgrade to ",x]})]}),e.jsx("p",{className:"mt-2 text-gray-300",children:"Unlock advanced features and take your cybersecurity skills to the next level."})]}),e.jsx("div",{className:"p-6",children:i?e.jsxs("div",{className:"text-center py-6",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(le,{className:"text-green-600 text-2xl"})}),e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"Upgrade Successful!"}),e.jsxs("p",{className:"text-gray-600",children:["Your account has been upgraded to ",x,". Redirecting to your new dashboard..."]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"font-bold text-gray-900 mb-4",children:"You'll get access to:"}),e.jsx("ul",{className:"space-y-3",children:f.features.map((E,L)=>e.jsxs("li",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-5 h-5 rounded-full bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(le,{className:"text-[#88cc14] text-xs"})}),e.jsx("span",{className:"text-gray-700",children:E})]},L))})]}),e.jsx("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:"Price"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[f.price,e.jsx("span",{className:"text-sm font-normal text-gray-600",children:"/month"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-gray-600",children:"Current plan"}),e.jsx("p",{className:"font-medium text-gray-900",children:F.title})]})]})}),u&&e.jsx("div",{className:"mb-6 p-3 bg-red-50 text-red-700 rounded-lg text-sm",children:u}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("button",{onClick:n,disabled:d,className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:b,disabled:d,className:"flex-1 bg-[#88cc14] text-black font-bold px-4 py-2 rounded-lg hover:bg-[#7ab811] transition-colors flex items-center justify-center gap-2",children:d?e.jsxs(e.Fragment,{children:[e.jsx(Me,{className:"animate-spin"}),"Processing..."]}):e.jsx(e.Fragment,{children:"Upgrade Now"})})]})]})})]})})}):null},ce=({targetPlan:a="Premium",variant:n="primary",size:x="md",fullWidth:m=!1,className:d="",children:t})=>{const[u,o]=A.useState(!1),{subscriptionLevel:i}=q();if(a==="Premium"&&(i==="Premium"||i==="Business")||a==="Business"&&i==="Business")return null;const s={primary:"bg-[#88cc14] text-black hover:bg-[#7ab811]",secondary:"bg-black text-white hover:bg-gray-900",text:"text-[#88cc14] hover:underline bg-transparent"},l={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2",lg:"px-6 py-3 text-lg"},b=a==="Business"?Q:U;return e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>o(!0),className:`
          ${s[n]} 
          ${l[x]} 
          ${m?"w-full":""}
          font-bold rounded-lg transition-colors flex items-center justify-center gap-2
          ${d}
        `,children:[e.jsx(b,{}),t||`Upgrade to ${a}`]}),e.jsx(Qe,{isOpen:u,onClose:()=>o(!1),targetPlan:a,currentPlan:i})]})},Ze={learn:[{id:"learning-modules",label:"Learning Modules",icon:We,route:"/learn/modules"},{id:"certifications",label:"Certifications",icon:J,route:"/learn/certifications"},{id:"career-paths",label:"Career Paths",icon:Ie,route:"/learn/paths"}],startHack:{attack:[{id:"web-penetration",label:"Web Penetration",icon:H,route:"/hack/web"},{id:"network-penetration",label:"Network Penetration",icon:Z,route:"/hack/network"},{id:"cloud-security",label:"Cloud Security",icon:$e,route:"/hack/cloud"},{id:"wireless-security",label:"Wireless Security",icon:Ue,route:"/hack/wireless"}],defence:[{id:"siem",label:"SIEM",icon:V,route:"/hack/siem"},{id:"incident-response",label:"Incident Response",icon:Le,route:"/hack/incident"},{id:"threat-hunting",label:"Threat Hunting",icon:Be,route:"/hack/threat-hunting"}],malware:[{id:"malware-analysis",label:"Malware Analysis",icon:Re,route:"/hack/malware"},{id:"reverse-engineering",label:"Reverse Engineering",icon:De,route:"/hack/reverse"},{id:"exploit-development",label:"Exploit Development",icon:Ee,route:"/hack/exploit"}]},challenges:[{id:"all-challenges",label:"All Challenges",icon:_e,route:"/challenges"}]};function Ke({profile:a,selectedSection:n,setSelectedSection:x,onSignOut:m,subscriptionLevel:d}){const{subscriptionLevel:t}=q(),u=d||t,[o,i]=A.useState(!1),[s,l]=A.useState({learn:!0,startHack:!0,attack:!0,defence:!0,malware:!0,achievements:!0,ctf:!0,referrals:!0,help:!0}),[b,f]=A.useState(!1);A.useEffect(()=>{const c=()=>{const g=window.innerWidth<1024;f(g),g&&i(!0)};return c(),window.addEventListener("resize",c),()=>{window.removeEventListener("resize",c)}},[]);const F=()=>{i(c=>!c)},E=c=>{l(g=>({...g,[c]:!g[c]}))},L=(c,g)=>{if(!Array.isArray(c))return null;const p=g==="learn"||g==="startHack",v=u===Y.PREMIUM||u===Y.BUSINESS;return c.map(j=>{const N=p&&!v;return e.jsxs("button",{onClick:()=>{if(N){window.location.href="/pricing";return}x(j.id),b&&i(!0)},className:`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors ${n===j.id?"bg-[#88cc14]/10 text-[#88cc14]":N?"text-gray-600 hover:text-gray-500 cursor-not-allowed":"text-gray-400 hover:text-white hover:bg-white/5"}`,children:[e.jsx(j.icon,{className:`text-lg flex-shrink-0 ${N?"text-gray-600":""}`}),!o&&e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsx("span",{className:"whitespace-nowrap overflow-hidden text-ellipsis",children:j.label}),N&&e.jsx("span",{className:"text-xs bg-yellow-500/20 text-yellow-500 px-1.5 py-0.5 rounded",children:"PRO"})]})]},j.id)})},r=(c,g)=>c?Object.entries(c).map(([p,v])=>e.jsxs("div",{className:"space-y-1",children:[e.jsxs("button",{onClick:()=>E(p),className:"w-full flex items-center justify-between text-xs text-gray-400 mb-2 hover:text-gray-300 transition-colors px-2",children:[e.jsx("span",{className:"capitalize",children:p.replace(/([A-Z])/g," $1").trim()}),!o&&e.jsx(ne,{className:`text-xs transform transition-transform duration-200 ${s[p]?"rotate-90":""}`})]}),s[p]&&!o&&e.jsx("div",{className:"ml-4 space-y-1",children:L(Array.isArray(v)?v:Object.values(v).flat(),g)})]},p)):null;return e.jsxs(e.Fragment,{children:[!o&&b&&e.jsx("div",{className:"fixed inset-0 bg-black/50 z-20",onClick:F}),e.jsxs("aside",{className:`fixed lg:static inset-y-0 left-0 bg-[#1A1F35] border-r border-gray-800 transition-all duration-300 ease-in-out ${o?"w-20":"w-64"} ${b&&o?"-translate-x-full lg:translate-x-0":"translate-x-0"} z-30`,children:[e.jsx(O,{to:"/",className:"flex items-center justify-center h-16 border-b border-gray-800",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 bg-[#88cc14]/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e.jsx("span",{className:"font-bold text-2xl relative z-10",children:o?e.jsx("span",{className:"text-[#88cc14]",children:"X"}):e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-[#88cc14]",children:"X"}),e.jsx("span",{className:"text-white",children:"CERBERUS"})]})})]})}),!b&&e.jsx("button",{onClick:F,className:"absolute -right-3 top-20 bg-[#1A1F35] border border-gray-800 rounded-full p-1.5 text-gray-400 hover:text-white transition-colors z-40",children:e.jsx(Oe,{className:"text-sm"})}),e.jsx("div",{className:"h-[calc(100vh-4rem)] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-800 scrollbar-track-transparent",children:e.jsx("nav",{className:"p-4 space-y-6",children:Object.entries(Ze).map(([c,g])=>e.jsxs("div",{children:[e.jsxs("button",{onClick:()=>E(c),className:"w-full flex items-center justify-between text-xs text-gray-400 mb-2 hover:text-gray-300 transition-colors",children:[!o&&e.jsx("span",{className:"uppercase tracking-wider font-bold",children:c.replace("_"," ")}),!o&&e.jsx(ne,{className:`text-xs transform transition-transform duration-200 ${s[c]?"rotate-90":""}`})]}),e.jsx("div",{className:"space-y-1",children:typeof g=="object"&&!Array.isArray(g)?r(g,c):L(g,c)})]},c))})}),!o&&e.jsxs("div",{className:"p-4",children:[u===Y.FREE&&e.jsx(ce,{targetPlan:"Premium",variant:"primary",size:"md",fullWidth:!0,children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(U,{}),e.jsx("span",{children:"Upgrade to Premium"})]})}),u===Y.PREMIUM&&e.jsx(ce,{targetPlan:"Business",variant:"secondary",size:"md",fullWidth:!0,children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Q,{}),e.jsx("span",{children:"Upgrade to Business"})]})})]}),!o&&e.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-800 bg-[#1A1F35]",children:e.jsxs("button",{onClick:m,className:"w-full flex items-center gap-3 p-3 rounded-lg text-red-400 hover:bg-red-400/10 transition-colors",children:[e.jsx(ge,{}),e.jsx("span",{children:"Sign Out"})]})})]})]})}function de({selectedSection:a,profile:n,subscription:x,coins:m,challenges:d,completedChallenges:t,totalPoints:u}){const o=[{icon:xe,title:"XCerberus Coins",value:(m==null?void 0:m.balance)||0,suffix:" XC",color:"#88cc14"},{icon:K,title:"Challenges Completed",value:t||0,color:"#88cc14"},{icon:W,title:"Total Points",value:u||0,color:"#88cc14"}],i=[{icon:ee,title:"Start Hack",description:"Begin a new hacking mission",link:"/global-start-hack"},{icon:se,title:"Learn",description:"Access learning resources",link:"/global-learn"},{icon:H,title:"Challenges",description:"Take on security challenges",link:"/global-challenges"}];return e.jsxs(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-[2000px] mx-auto px-4 sm:px-6 py-6 sm:py-8",children:[e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8",children:o.map((s,l)=>e.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:l*.1},className:"bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(s.icon,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-400 text-sm",children:s.title}),e.jsxs("p",{className:"text-xl sm:text-2xl font-bold",style:{color:s.color},children:[s.value,s.suffix]})]})]})},l))}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8",children:i.map((s,l)=>e.jsx(O,{to:s.link,className:"bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-4 sm:p-6 hover:border-[#88cc14] transition-all duration-300 group",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(s.icon,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-bold",children:s.title}),e.jsx("p",{className:"text-gray-400 text-sm",children:s.description})]})]})},l))}),e.jsxs("div",{className:"bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-4 sm:p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Recent Activity"}),e.jsx("div",{className:"space-y-4",children:d&&d.length>0?d.slice(0,5).map((s,l)=>{var b;return e.jsxs(D.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:l*.1},className:"flex items-center gap-4 p-4 rounded-lg hover:bg-[#88cc14]/5",children:[e.jsx("div",{className:`w-10 h-10 rounded-lg ${s.status==="completed"?"bg-[#88cc14]/10 text-[#88cc14]":"bg-gray-800/50 text-gray-400"} flex items-center justify-center`,children:s.status==="completed"?e.jsx(V,{}):e.jsx(he,{})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"text-white font-medium",children:[s.status==="completed"?"Completed":"Started"," challenge: ",((b=s.challenges)==null?void 0:b.title)||"Challenge"]}),e.jsx("p",{className:"text-sm text-gray-400",children:new Date(s.submission_time).toLocaleString()})]}),s.status==="completed"&&s.points_earned>0&&e.jsxs("div",{className:"bg-[#88cc14]/10 text-[#88cc14] px-3 py-1 rounded-full text-sm",children:["+",s.points_earned," points"]})]},l)}):e.jsxs("div",{className:"text-center py-8 text-gray-400",children:[e.jsx("p",{children:"No recent activity"}),e.jsx(O,{to:"/challenges",className:"text-[#88cc14] hover:underline mt-2 inline-block",children:"Start a challenge"})]})})]})]})}const es=({learningStats:a={},timeRange:n="month",onTimeRangeChange:x,showDetails:m=!1,onToggleDetails:d})=>{const t={modulesCompleted:a.modulesCompleted||0,totalModules:a.totalModules||0,timeSpent:a.timeSpent||0,averageScore:a.averageScore||0,streak:a.streak||0,topSkills:a.topSkills||[],recentActivity:a.recentActivity||[],progressByCategory:a.progressByCategory||[],...a},u=t.totalModules>0?Math.round(t.modulesCompleted/t.totalModules*100):0,o=s=>{const l=Math.floor(s/60),b=s%60;return l>0?`${l}h ${b}m`:`${b}m`},i=()=>{switch(n){case"week":return"This Week";case"month":return"This Month";case"year":return"This Year";case"all":return"All Time";default:return"This Month"}};return e.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center",children:[e.jsx(W,{className:"mr-2 text-blue-600 dark:text-blue-400"}),"Learning Analytics"]}),e.jsx("div",{className:"relative",children:e.jsxs("select",{className:"bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5",value:n,onChange:s=>x&&x(s.target.value),children:[e.jsx("option",{value:"week",children:"This Week"}),e.jsx("option",{value:"month",children:"This Month"}),e.jsx("option",{value:"year",children:"This Year"}),e.jsx("option",{value:"all",children:"All Time"})]})})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:[u,"%"]}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Completion"})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:t.modulesCompleted}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Modules Completed"})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:o(t.timeSpent)}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Time Spent"})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-600 dark:text-yellow-400",children:t.streak}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Day Streak"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Overall Progress"}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[t.modulesCompleted,"/",t.totalModules," Modules"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:`${u}%`}})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Top Skills"}),e.jsx("div",{className:"space-y-2",children:t.topSkills.slice(0,3).map((s,l)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-2",children:e.jsx("div",{className:`h-2.5 rounded-full ${l===0?"bg-blue-600":l===1?"bg-green-600":"bg-purple-600"}`,style:{width:`${s.level}%`}})}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 min-w-[60px] text-right",children:s.name})]},l))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Recent Activity"}),e.jsx("div",{className:"space-y-2",children:t.recentActivity.slice(0,3).map((s,l)=>e.jsxs("div",{className:"flex items-start p-2 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx("div",{className:"mr-3 mt-0.5",children:s.type==="completion"?e.jsx(K,{className:"text-yellow-500"}):e.jsx(se,{className:"text-blue-500"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm font-medium text-gray-800 dark:text-gray-200",children:s.title}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 flex items-center",children:[e.jsx(ue,{className:"mr-1"}),s.date,s.duration&&e.jsxs("span",{className:"ml-2 flex items-center",children:[e.jsx(pe,{className:"mr-1"}),s.duration]})]})]})]},l))})]}),e.jsx("button",{className:"w-full flex items-center justify-center text-sm text-blue-600 dark:text-blue-400 hover:underline",onClick:d,children:m?e.jsxs(e.Fragment,{children:["Show Less ",e.jsx(be,{className:"ml-1"})]}):e.jsxs(e.Fragment,{children:["Show More ",e.jsx(G,{className:"ml-1"})]})}),m&&e.jsxs(D.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Progress by Category"}),e.jsx("div",{className:"space-y-3",children:t.progressByCategory.map((s,l)=>e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-1",children:[e.jsx("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:s.name}),e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[s.completed,"/",s.total," (",Math.round(s.completed/s.total*100),"%)"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5",children:e.jsx("div",{className:"bg-blue-600 h-1.5 rounded-full",style:{width:`${s.completed/s.total*100}%`}})})]},l))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Learning Trends"}),e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 h-40 flex items-center justify-center",children:e.jsx(te,{className:"text-gray-400 text-4xl"})}),e.jsxs("p",{className:"text-xs text-center text-gray-500 dark:text-gray-400 mt-2",children:["Learning activity for ",i().toLowerCase()]})]}),e.jsx("div",{className:"text-center",children:e.jsxs(O,{to:"/analytics/learning",className:"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center",children:["View Full Analytics",e.jsx($,{className:"ml-1 text-xs"})]})})]})]})})},ss=({challengeStats:a={},timeRange:n="month",onTimeRangeChange:x,showDetails:m=!1,onToggleDetails:d})=>{const t={challengesCompleted:a.challengesCompleted||0,totalChallenges:a.totalChallenges||0,timeSpent:a.timeSpent||0,averageScore:a.averageScore||0,successRate:a.successRate||0,pointsEarned:a.pointsEarned||0,topCategories:a.topCategories||[],recentActivity:a.recentActivity||[],difficultyBreakdown:a.difficultyBreakdown||[],...a},u=t.totalChallenges>0?Math.round(t.challengesCompleted/t.totalChallenges*100):0,o=()=>{switch(n){case"week":return"This Week";case"month":return"This Month";case"year":return"This Year";case"all":return"All Time";default:return"This Month"}},i=s=>{switch(s.toLowerCase()){case"web":return e.jsx(H,{className:"text-purple-500"});case"network":return e.jsx(Z,{className:"text-blue-500"});case"security":return e.jsx(V,{className:"text-green-500"});case"database":return e.jsx(ye,{className:"text-yellow-500"});default:return e.jsx(je,{className:"text-gray-500"})}};return e.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center",children:[e.jsx(W,{className:"mr-2 text-blue-600 dark:text-blue-400"}),"Challenge Analytics"]}),e.jsx("div",{className:"relative",children:e.jsxs("select",{className:"bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5",value:n,onChange:s=>x&&x(s.target.value),children:[e.jsx("option",{value:"week",children:"This Week"}),e.jsx("option",{value:"month",children:"This Month"}),e.jsx("option",{value:"year",children:"This Year"}),e.jsx("option",{value:"all",children:"All Time"})]})})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:[u,"%"]}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Completion"})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:t.challengesCompleted}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Challenges Completed"})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:[t.successRate,"%"]}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Success Rate"})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-600 dark:text-yellow-400",children:t.pointsEarned}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Points Earned"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Overall Progress"}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[t.challengesCompleted,"/",t.totalChallenges," Challenges"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:`${u}%`}})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Top Categories"}),e.jsx("div",{className:"space-y-2",children:t.topCategories.slice(0,3).map((s,l)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 flex justify-center mr-2",children:i(s.name)}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:`h-2.5 rounded-full ${l===0?"bg-blue-600":l===1?"bg-green-600":"bg-purple-600"}`,style:{width:`${s.percentage}%`}})})}),e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-2 min-w-[60px] text-right",children:[s.name," (",s.count,")"]})]},l))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Recent Activity"}),e.jsx("div",{className:"space-y-2",children:t.recentActivity.slice(0,3).map((s,l)=>e.jsxs("div",{className:"flex items-start p-2 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx("div",{className:"mr-3 mt-0.5",children:s.success?e.jsx(K,{className:"text-yellow-500"}):e.jsx(ee,{className:"text-blue-500"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm font-medium text-gray-800 dark:text-gray-200",children:s.title}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 flex items-center",children:[e.jsx(ue,{className:"mr-1"}),s.date,s.duration&&e.jsxs("span",{className:"ml-2 flex items-center",children:[e.jsx(pe,{className:"mr-1"}),s.duration]})]})]}),e.jsx("div",{className:"text-xs font-medium",children:s.points&&e.jsxs("span",{className:"px-2 py-0.5 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 rounded-full",children:["+",s.points," pts"]})})]},l))})]}),e.jsx("button",{className:"w-full flex items-center justify-center text-sm text-blue-600 dark:text-blue-400 hover:underline",onClick:d,children:m?e.jsxs(e.Fragment,{children:["Show Less ",e.jsx(be,{className:"ml-1"})]}):e.jsxs(e.Fragment,{children:["Show More ",e.jsx(G,{className:"ml-1"})]})}),m&&e.jsxs(D.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Difficulty Breakdown"}),e.jsx("div",{className:"space-y-3",children:t.difficultyBreakdown.map((s,l)=>e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-1",children:[e.jsx("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:s.name}),e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[s.completed,"/",s.total," (",Math.round(s.completed/s.total*100),"%)"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5",children:e.jsx("div",{className:`h-1.5 rounded-full ${s.name==="Beginner"?"bg-green-600":s.name==="Intermediate"?"bg-yellow-600":"bg-red-600"}`,style:{width:`${s.completed/s.total*100}%`}})})]},l))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Challenge Trends"}),e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 h-40 flex items-center justify-center",children:e.jsx(te,{className:"text-gray-400 text-4xl"})}),e.jsxs("p",{className:"text-xs text-center text-gray-500 dark:text-gray-400 mt-2",children:["Challenge activity for ",o().toLowerCase()]})]}),e.jsx("div",{className:"text-center",children:e.jsxs(O,{to:"/analytics/challenges",className:"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center",children:["View Full Analytics",e.jsx($,{className:"ml-1 text-xs"})]})})]})]})})},ts=({recommendations:a={},userSkills:n={},subscriptionLevel:x,onUpgrade:m})=>{const d=ae(),t={modules:a.modules||[],challenges:a.challenges||[],simulations:a.simulations||[],careerPaths:a.careerPaths||[],...a},u=i=>{switch(i==null?void 0:i.toLowerCase()){case"web":return e.jsx(H,{className:"text-purple-500"});case"network":return e.jsx(Z,{className:"text-blue-500"});case"security":return e.jsx(V,{className:"text-green-500"});case"database":return e.jsx(ye,{className:"text-yellow-500"});default:return e.jsx(je,{className:"text-gray-500"})}},o=(i,s)=>{switch(i){case"module":d(`/global-learn?module=${s}`);break;case"challenge":d(`/global-challenges?challenge=${s}`);break;case"simulation":d(`/global-start-hack?scenario=${s}`);break;case"careerPath":d(`/career-paths/${s}`);break}};return e.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:e.jsxs("div",{className:"p-5",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center",children:[e.jsx(ie,{className:"mr-2 text-yellow-500"}),"Recommended for You"]})}),e.jsx("div",{className:"mb-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(W,{className:"text-blue-600 dark:text-blue-400 mt-1 mr-3"}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Based on Your Skills"}),e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1",children:"Recommendations tailored to your current skill levels and learning goals."}),e.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:Object.entries(n).slice(0,3).map(([i,s],l)=>e.jsxs("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",children:[i,": ",s,"%"]},l))})]})]})}),t.modules.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center",children:[e.jsx(se,{className:"mr-1 text-blue-600 dark:text-blue-400"}),"Learning Modules"]}),e.jsx("div",{className:"space-y-2",children:t.modules.slice(0,2).map((i,s)=>e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer",onClick:()=>o("module",i.id),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"mr-3",children:u(i.category)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 dark:text-white",children:i.title}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:i.description})]}),e.jsx($,{className:"text-gray-400 dark:text-gray-500 ml-2"})]})},s))})]}),t.challenges.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center",children:[e.jsx(ee,{className:"mr-1 text-green-600 dark:text-green-400"}),"Challenges"]}),e.jsx("div",{className:"space-y-2",children:t.challenges.slice(0,2).map((i,s)=>e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer",onClick:()=>o("challenge",i.id),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"mr-3",children:u(i.category)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 dark:text-white",children:i.title}),e.jsxs("div",{className:"flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1",children:[e.jsx("span",{className:"px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs mr-2",children:i.difficulty}),e.jsx("span",{children:i.estimatedTime})]})]}),e.jsx($,{className:"text-gray-400 dark:text-gray-500 ml-2"})]})},s))})]}),t.simulations.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center",children:[e.jsx(he,{className:"mr-1 text-purple-600 dark:text-purple-400"}),"Simulations"]}),e.jsx("div",{className:"space-y-2",children:t.simulations.slice(0,1).map((i,s)=>e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer",onClick:()=>o("simulation",i.id),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"mr-3",children:u(i.category)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 dark:text-white",children:i.title}),e.jsxs("div",{className:"flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1",children:[e.jsx("span",{className:"px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs mr-2",children:i.difficulty}),e.jsx("span",{children:i.estimatedTime})]})]}),e.jsx($,{className:"text-gray-400 dark:text-gray-500 ml-2"})]})},s))})]}),t.careerPaths.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center",children:[e.jsx(J,{className:"mr-1 text-indigo-600 dark:text-indigo-400"}),"Career Paths"]}),e.jsx("div",{className:"space-y-2",children:t.careerPaths.slice(0,1).map((i,s)=>e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer",onClick:()=>o("careerPath",i.id),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"mr-3",children:e.jsx(J,{className:"text-indigo-500"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 dark:text-white",children:i.title}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:i.description})]}),e.jsx($,{className:"text-gray-400 dark:text-gray-500 ml-2"})]})},s))})]}),t.modules.length===0&&t.challenges.length===0&&t.simulations.length===0&&t.careerPaths.length===0&&e.jsxs("div",{className:"text-center py-6 text-gray-500 dark:text-gray-400",children:[e.jsx(ie,{className:"mx-auto text-2xl text-yellow-500 mb-2"}),e.jsx("p",{children:"Complete more modules and challenges to get personalized recommendations."}),e.jsx("button",{className:"mt-4 px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors",onClick:()=>d("/global-learn"),children:"Explore Learning Modules"})]}),(t.modules.length>0||t.challenges.length>0||t.simulations.length>0||t.careerPaths.length>0)&&e.jsxs("button",{className:"w-full mt-2 flex items-center justify-center text-sm text-blue-600 dark:text-blue-400 hover:underline",onClick:()=>d("/recommendations"),children:["View All Recommendations",e.jsx($,{className:"ml-1 text-xs"})]})]})})},oe=({type:a="line",data:n=[],labels:x=[],title:m="",height:d=200,colors:t=["#3B82F6","#10B981","#8B5CF6","#F59E0B","#EF4444"],showLegend:u=!0,showGrid:o=!0,animate:i=!0,loading:s=!1,error:l=null})=>{const b=A.useRef(null);A.useEffect(()=>{if(s||l||!n.length)return;const r=b.current;if(!r)return;const c=r.getContext("2d"),g=r.width,p=r.height;switch(c.clearRect(0,0,g,p),a){case"line":f(c,n,x,g,p,t,o);break;case"bar":F(c,n,x,g,p,t,o);break;case"pie":E(c,n,x,g,p,t);break;default:f(c,n,x,g,p,t,o)}},[a,n,x,t,o,s,l]);const f=(r,c,g,p,v,j,N)=>{const w=p-80,R=v-40*2;if(N){r.strokeStyle="#e5e7eb",r.lineWidth=.5;for(let y=0;y<=5;y++){const h=40+R/5*y;r.beginPath(),r.moveTo(40,h),r.lineTo(p-40,h),r.stroke()}for(let y=0;y<g.length;y++){const h=40+w/(g.length-1)*y;r.beginPath(),r.moveTo(h,40),r.lineTo(h,v-40),r.stroke()}}r.strokeStyle="#9ca3af",r.lineWidth=1,r.beginPath(),r.moveTo(40,40),r.lineTo(40,v-40),r.lineTo(p-40,v-40),r.stroke(),r.fillStyle="#6b7280",r.font="10px Arial",r.textAlign="center";for(let y=0;y<g.length;y++){const h=40+w/(g.length-1)*y;r.fillText(g[y],h,v-40+15)}const T=Math.max(...c.flat());for(let y=0;y<c.length;y++){const h=c[y],C=j[y%j.length];r.strokeStyle=C,r.lineWidth=2,r.beginPath();for(let k=0;k<h.length;k++){const S=40+w/(h.length-1)*k,M=v-40-h[k]/T*R;k===0?r.moveTo(S,M):r.lineTo(S,M)}r.stroke(),r.fillStyle=C;for(let k=0;k<h.length;k++){const S=40+w/(h.length-1)*k,M=v-40-h[k]/T*R;r.beginPath(),r.arc(S,M,4,0,Math.PI*2),r.fill()}}},F=(r,c,g,p,v,j,N)=>{const w=p-80,R=v-40*2;if(N){r.strokeStyle="#e5e7eb",r.lineWidth=.5;for(let h=0;h<=5;h++){const C=40+R/5*h;r.beginPath(),r.moveTo(40,C),r.lineTo(p-40,C),r.stroke()}}r.strokeStyle="#9ca3af",r.lineWidth=1,r.beginPath(),r.moveTo(40,40),r.lineTo(40,v-40),r.lineTo(p-40,v-40),r.stroke(),r.fillStyle="#6b7280",r.font="10px Arial",r.textAlign="center";const T=w/g.length/(c.length+.5);for(let h=0;h<g.length;h++){const C=40+w/g.length*(h+.5);r.fillText(g[h],C,v-40+15)}const y=Math.max(...c.flat());for(let h=0;h<c.length;h++){const C=c[h],k=j[h%j.length];r.fillStyle=k;for(let S=0;S<C.length;S++){const M=C[S]/y*R,I=40+w/g.length*(S+.5)-T*c.length/2+T*h,z=v-40-M;r.fillRect(I,z,T,M)}}},E=(r,c,g,p,v,j)=>{const N=p/2,_=v/2,w=Math.min(N,_)-40,R=c.reduce((y,h)=>y+h,0);let T=0;for(let y=0;y<c.length;y++){const h=2*Math.PI*c[y]/R,C=T+h,k=j[y%j.length];if(r.fillStyle=k,r.beginPath(),r.moveTo(N,_),r.arc(N,_,w,T,C),r.closePath(),r.fill(),g[y]){const S=T+h/2,M=N+Math.cos(S)*(w*.7),I=_+Math.sin(S)*(w*.7);r.fillStyle="#ffffff",r.font="bold 12px Arial",r.textAlign="center",r.textBaseline="middle",r.fillText(g[y],M,I)}T=C}},L=()=>{switch(a){case"line":return e.jsx(W,{className:"text-blue-500"});case"bar":return e.jsx(te,{className:"text-green-500"});case"pie":return e.jsx(ze,{className:"text-purple-500"});default:return e.jsx(W,{className:"text-blue-500"})}};return s?e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",style:{height:`${d}px`},children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Loading chart data..."})]})}):l?e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",style:{height:`${d}px`},children:e.jsxs("div",{className:"text-center text-red-500 dark:text-red-400",children:[e.jsx(fe,{className:"mx-auto text-2xl mb-2"}),e.jsx("p",{className:"text-sm",children:l})]})}):n.length?e.jsxs(D.div,{initial:i?{opacity:0,y:20}:!1,animate:i?{opacity:1,y:0}:!1,className:"bg-white dark:bg-gray-800 rounded-lg p-4",children:[m&&e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"mr-2",children:L()}),e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:m})]}),e.jsx("div",{className:"relative",children:e.jsx("canvas",{ref:b,width:500,height:d,className:"w-full h-auto"})}),u&&n.length>1&&e.jsx("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:n.map((r,c)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full mr-1",style:{backgroundColor:t[c%t.length]}}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:x[c]||`Series ${c+1}`})]},c))})]}):e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",style:{height:`${d}px`},children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-gray-400 dark:text-gray-500 mb-2",children:L()}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No data available"})]})})},as=({skills:a=[],dataSeries:n=[],labels:x=["Your Skills","Team Average"],colors:m=["rgba(59, 130, 246, 0.7)","rgba(16, 185, 129, 0.7)"],height:d=300,animate:t=!0,loading:u=!1,error:o=null})=>{const i=A.useRef(null);A.useEffect(()=>{if(u||o||!a.length||!n.length)return;const l=i.current;if(!l)return;const b=l.getContext("2d"),f=l.width,F=l.height;b.clearRect(0,0,f,F),s(b,a,n,f,F,m)},[a,n,m,u,o]);const s=(l,b,f,F,E,L)=>{const r=F/2,c=E/2,g=Math.min(r,c)-40,p=b.length,v=Math.PI*2/p;l.strokeStyle="#e5e7eb",l.lineWidth=1;for(let j=0;j<p;j++){const N=j*v-Math.PI/2,_=r+Math.cos(N)*g,w=c+Math.sin(N)*g;l.beginPath(),l.moveTo(r,c),l.lineTo(_,w),l.stroke()}for(let j=1;j<=5;j++){const N=g/5*j;l.beginPath(),l.arc(r,c,N,0,Math.PI*2),l.stroke()}l.fillStyle="#6b7280",l.font="12px Arial",l.textAlign="center",l.textBaseline="middle";for(let j=0;j<p;j++){const N=j*v-Math.PI/2,_=r+Math.cos(N)*(g+20),w=c+Math.sin(N)*(g+20);l.fillText(b[j],_,w)}for(let j=0;j<f.length;j++){const N=f[j],_=L[j%L.length];l.fillStyle=_,l.strokeStyle=_.replace("0.7","1"),l.lineWidth=2,l.beginPath();for(let h=0;h<p;h++){const C=h*v-Math.PI/2,k=N[h]/100,S=r+Math.cos(C)*g*k,M=c+Math.sin(C)*g*k;h===0?l.moveTo(S,M):l.lineTo(S,M)}const w=-Math.PI/2,R=N[0]/100,T=r+Math.cos(w)*g*R,y=c+Math.sin(w)*g*R;l.lineTo(T,y),l.fill(),l.stroke();for(let h=0;h<p;h++){const C=h*v-Math.PI/2,k=N[h]/100,S=r+Math.cos(C)*g*k,M=c+Math.sin(C)*g*k;l.beginPath(),l.arc(S,M,4,0,Math.PI*2),l.fill()}}};return u?e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",style:{height:`${d}px`},children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Loading skills data..."})]})}):o?e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",style:{height:`${d}px`},children:e.jsxs("div",{className:"text-center text-red-500 dark:text-red-400",children:[e.jsx(fe,{className:"mx-auto text-2xl mb-2"}),e.jsx("p",{className:"text-sm",children:o})]})}):!a.length||!n.length?e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",style:{height:`${d}px`},children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No skills data available"})})}):e.jsxs(D.div,{initial:t?{opacity:0,y:20}:!1,animate:t?{opacity:1,y:0}:!1,className:"bg-white dark:bg-gray-800 rounded-lg p-4",children:[e.jsx("div",{className:"relative",children:e.jsx("canvas",{ref:i,width:500,height:d,className:"w-full h-auto"})}),e.jsx("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:n.map((l,b)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full mr-1",style:{backgroundColor:m[b%m.length]}}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:x[b]||`Series ${b+1}`})]},b))})]})},rs=({title:a="Upgrade Your Experience",description:n="Unlock premium features and content with a subscription upgrade.",buttonText:x="Upgrade Now",onUpgrade:m,variant:d="default",feature:t=null})=>d==="compact"?e.jsxs("div",{className:"bg-[#1A1F35] border border-[#88cc14]/20 rounded-lg p-4 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center",children:e.jsx(U,{className:"text-[#88cc14]"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-white",children:a}),e.jsx("p",{className:"text-sm text-gray-400",children:t?`Unlock ${t} with Premium`:n})]})]}),e.jsxs("button",{onClick:m,className:"px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center gap-2",children:[x,e.jsx($,{})]})]}):d==="inline"?e.jsxs("button",{onClick:m,className:"inline-flex items-center gap-2 text-[#88cc14] hover:text-[#7ab811]",children:[e.jsx(U,{}),e.jsx("span",{children:t?`Upgrade to unlock ${t}`:"Upgrade to Premium"})]}):e.jsxs(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-[#1A1F35] border border-[#88cc14]/20 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-[#88cc14]/20 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(U,{className:"text-[#88cc14] text-2xl"})}),e.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:a}),e.jsx("p",{className:"text-gray-300 mb-6 max-w-md mx-auto",children:n}),e.jsx("button",{onClick:m,className:"px-6 py-3 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg font-medium transition-colors",children:x})]});function ls({profile:a,subscription:n,coins:x,challenges:m,completedChallenges:d,totalPoints:t}){const[u,o]=A.useState("dashboard"),i=ae(),{subscriptionLevel:s,hasAccess:l,isFree:b,isPremium:f,isBusiness:F}=q(),{learningStats:E,challengeStats:L,recommendations:r,userSkills:c,teamMembers:g,teamStats:p,timeRange:v,isLoading:j,error:N,showLearningDetails:_,showChallengeDetails:w,showTeamDetails:R,handleTimeRangeChange:T,toggleLearningDetails:y,toggleChallengeDetails:h,toggleTeamDetails:C,fetchAllData:k}=Ye(),S=["Jan","Feb","Mar","Apr","May","Jun"],M=[[30,45,60,70,75,85]],I=[[2,3,5,4,6,8]],z=["Web Security","Network Security","Cryptography","OSINT","Reverse Engineering"],Ne=[70,60,40,80,50],ve=[65,70,55,60,75],re=async()=>{try{await qe(),i("/login")}catch(B){console.error("Error signing out:",B)}},X=()=>{i("/pricing")},ke=()=>{k()},we=()=>F?"Business Dashboard":f?"Premium Dashboard":"Dashboard";return e.jsx("div",{className:"min-h-screen bg-[#0B1120] text-white",children:e.jsxs("div",{className:"flex",children:[e.jsx(Ke,{profile:a,selectedSection:u,setSelectedSection:o,onSignOut:re,subscriptionLevel:s}),e.jsxs("div",{className:"flex-1",children:[e.jsx(He,{profile:a,subscription:n,coins:x,onSignOut:re}),u==="dashboard"?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:we()}),e.jsxs("div",{className:"flex gap-2",children:[b&&e.jsx("button",{onClick:X,className:"px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center",children:"Upgrade Now"}),e.jsx("button",{onClick:ke,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center",children:"Refresh Data"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[e.jsx(es,{learningStats:E,timeRange:v,onTimeRangeChange:T,showDetails:_,onToggleDetails:y,loading:j.learning,error:N.learning,subscriptionLevel:s}),e.jsx(ss,{challengeStats:L,timeRange:v,onTimeRangeChange:T,showDetails:w,onToggleDetails:h,loading:j.challenges,error:N.challenges,subscriptionLevel:s})]}),f||F?e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[e.jsx(oe,{type:"line",data:M,labels:S,title:"Learning Progress Over Time",height:200}),e.jsx(oe,{type:"bar",data:I,labels:S,title:"Challenges Completed by Month",height:200})]}):e.jsx("div",{className:"mb-6",children:e.jsx(rs,{title:"Unlock Detailed Analytics",description:"Upgrade to Premium to access detailed learning and challenge analytics, progress tracking, and personalized recommendations.",buttonText:"Upgrade to Premium",onUpgrade:X})}),(f||F)&&z.length>0&&e.jsx("div",{className:"mb-6",children:e.jsx(as,{skills:z,dataSeries:[Ne,ve],labels:["Your Skills","Team Average"],height:300})}),F&&e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Team Performance"}),e.jsx("button",{onClick:C,className:"text-[#88cc14] hover:text-[#7ab811]",children:R?"Show Less":"Show More"})]}),j.team?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[#88cc14]"})}):N.team?e.jsx("div",{className:"text-red-400 py-4",children:N.team}):e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-300 mb-4",children:"Track your team's progress and performance across all learning modules and challenges."}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:g.slice(0,3).map((B,Ce)=>e.jsx("div",{className:"bg-[#1A1F35] p-4 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center",children:B.avatar_url?e.jsx("img",{src:B.avatar_url,alt:B.username,className:"w-full h-full rounded-full"}):e.jsx("span",{className:"text-[#88cc14]",children:B.username.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-white",children:B.username}),e.jsx("p",{className:"text-sm text-gray-400",children:B.role||"Team Member"})]})]})},Ce))}),R&&e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-white font-medium mb-2",children:"Team Statistics"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-[#1A1F35] p-3 rounded-lg",children:[e.jsx("p",{className:"text-gray-400 text-sm",children:"Completed Challenges"}),e.jsx("p",{className:"text-xl font-bold text-white",children:(p==null?void 0:p.completedChallenges)||0})]}),e.jsxs("div",{className:"bg-[#1A1F35] p-3 rounded-lg",children:[e.jsx("p",{className:"text-gray-400 text-sm",children:"Completed Modules"}),e.jsx("p",{className:"text-xl font-bold text-white",children:(p==null?void 0:p.completedModules)||0})]}),e.jsxs("div",{className:"bg-[#1A1F35] p-3 rounded-lg",children:[e.jsx("p",{className:"text-gray-400 text-sm",children:"Average Score"}),e.jsxs("p",{className:"text-xl font-bold text-white",children:[(p==null?void 0:p.averageScore)||0,"%"]})]}),e.jsxs("div",{className:"bg-[#1A1F35] p-3 rounded-lg",children:[e.jsx("p",{className:"text-gray-400 text-sm",children:"Total Points"}),e.jsx("p",{className:"text-xl font-bold text-white",children:(p==null?void 0:p.totalPoints)||0})]})]})]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(ts,{recommendations:r,userSkills:c,subscriptionLevel:s,onUpgrade:X,loading:j.recommendations,error:N.recommendations})}),e.jsx(de,{selectedSection:u,profile:a,subscription:n,coins:x,challenges:m,completedChallenges:d,totalPoints:t})]}):e.jsx(de,{selectedSection:u,profile:a,subscription:n,coins:x,challenges:m,completedChallenges:d,totalPoints:t})]})]})})}const ns=async a=>{try{const[n,x,m,d,t,u]=await Promise.all([P.from("user_profiles").select("*").eq("id",a).single(),P.from("user_subscriptions").select(`
          *,
          subscription_plans (*)
        `).eq("user_id",a).single(),P.from("user_coins").select("*").eq("user_id",a).single(),P.from("challenge_submissions").select(`
          *,
          challenges (*)
        `).eq("user_id",a).order("submission_time",{ascending:!1}),P.from("learning_progress").select("*").eq("user_id",a).order("created_at",{ascending:!1}),P.from("user_activity").select(`
          *,
          challenges (*)
        `).eq("user_id",a).order("created_at",{ascending:!1}).limit(10)]);return{profile:n.data,subscription:x.data,coins:m.data,challenges:d.data||[],learningProgress:t.data||[],recentActivity:u.data||[],completedChallenges:(d.data||[]).filter(o=>o.status==="completed").length,totalPoints:(d.data||[]).reduce((o,i)=>o+(i.points_earned||0),0)}}catch(n){throw console.error("Error fetching dashboard data:",n),n}},is=async a=>{try{const[n,x]=await Promise.all([P.from("challenge_submissions").select("*, challenges(*)").eq("user_id",a).eq("status","completed"),P.from("learning_progress").select("*").eq("user_id",a)]),m=n.data||[],d=x.data||[],t={strengths:[],weaknesses:[],recommendedPaths:[],nextChallenges:[]},u=m.reduce((o,i)=>{var s,l;return o[(s=i.challenges)==null?void 0:s.category]=(o[(l=i.challenges)==null?void 0:l.category]||0)+1,o},{});return t.strengths=Object.entries(u).filter(([o,i])=>i>=3).map(([o])=>o),t.weaknesses=Object.entries(u).filter(([o,i])=>i<2).map(([o])=>o),t.nextChallenges=[{category:"Web Security",recommendedDifficulty:"Medium"},{category:"Network Security",recommendedDifficulty:"Hard"}],t}catch(n){throw console.error("Error getting recommendations:",n),n}};function ds(){const a=ae(),{subscriptionLevel:n}=q(),[x,m]=A.useState(!0),[d,t]=A.useState(null),[u,o]=A.useState(null);return A.useEffect(()=>{(async()=>{try{const{data:{session:b}}=await P.auth.getSession();if(!b){a("/login");return}const f=await ns(b.user.id);t(f);const F=await is(b.user.id);o(F)}catch(b){console.error("Error loading dashboard:",b)}finally{m(!1)}})();const s=P.channel("challenges").on("*",b=>{t(f=>({...f,challenges:[...(f==null?void 0:f.challenges)||[],b.new]}))}).subscribe(),l=P.channel("user_activity").on("*",b=>{t(f=>({...f,recentActivity:[b.new,...((f==null?void 0:f.recentActivity)||[]).slice(0,9)]}))}).subscribe();return()=>{s.unsubscribe(),l.unsubscribe()}},[a]),x?e.jsx("div",{className:"min-h-screen bg-[#0B1120] flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"})}):e.jsx(Ve,{children:e.jsx(ls,{...d,recommendations:u})})}export{ds as default};
