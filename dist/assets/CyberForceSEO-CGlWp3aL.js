import{j as e,a_ as u}from"./index-c6UceSOv.js";const w=({title:o,description:s,keywords:i=[],ogImage:r="/images/CyberForce.png",ogType:m="website",twitterCard:y="summary_large_image",canonicalUrl:l,structuredData:p})=>{const a="CyberForce - Advanced Cybersecurity Training Platform",n=o?`${o} | CyberForce`:a,t=s||"CyberForce is a premier destination for comprehensive cyber security capacity building and training, empowering individuals and organizations to effectively navigate the complex world of cyber security.",d=[...["cybersecurity","cyber security training","security challenges","CTF","cyber defense","security training","Oman cybersecurity","CyberForce Oman","cybersecurity learning platform"],...i].join(", "),c=l||(typeof window<"u"?window.location.href:"https://cyberforce.om"),g=p||{"@context":"https://schema.org","@type":"Organization",name:"CyberForce",url:"https://cyberforce.om",logo:"https://cyberforce.om/images/CyberForce.png",sameAs:["https://www.linkedin.com/company/cyberforceoman/","https://www.instagram.com/cyberforce_om/","https://x.com/cyberforce_om"],description:t,address:{"@type":"PostalAddress",addressCountry:"Oman",addressLocality:"Muscat"},contactPoint:{"@type":"ContactPoint",telephone:"+96871104475",contactType:"customer service",email:"<EMAIL>"}};return e.jsxs(u,{children:[e.jsx("title",{children:n}),e.jsx("meta",{name:"description",content:t}),e.jsx("meta",{name:"keywords",content:d}),e.jsx("link",{rel:"canonical",href:c}),e.jsx("meta",{property:"og:title",content:n}),e.jsx("meta",{property:"og:description",content:t}),e.jsx("meta",{property:"og:image",content:r}),e.jsx("meta",{property:"og:url",content:c}),e.jsx("meta",{property:"og:type",content:m}),e.jsx("meta",{property:"og:site_name",content:a}),e.jsx("meta",{name:"twitter:card",content:y}),e.jsx("meta",{name:"twitter:site",content:"@cyberforce_om"}),e.jsx("meta",{name:"twitter:title",content:n}),e.jsx("meta",{name:"twitter:description",content:t}),e.jsx("meta",{name:"twitter:image",content:r}),e.jsx("meta",{name:"author",content:"CyberForce"}),e.jsx("meta",{name:"language",content:"English"}),e.jsx("meta",{name:"robots",content:"index, follow"}),e.jsx("meta",{name:"googlebot",content:"index, follow"}),e.jsx("meta",{name:"revisit-after",content:"7 days"}),e.jsx("meta",{name:"distribution",content:"global"}),e.jsx("meta",{name:"rating",content:"general"}),e.jsx("meta",{name:"geo.region",content:"OM"}),e.jsx("meta",{name:"geo.placename",content:"Muscat"}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify(g)})]})};export{w as C};
