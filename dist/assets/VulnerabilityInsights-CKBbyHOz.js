import{au as Q,r as o,j as e,E as V,av as G,bB as z,I as $,x as P,y as q,H as B,ap as X}from"./index-c6UceSOv.js";import{d as S}from"./ThreatAnalyticsDashboard-CJk_8dys.js";const ee=()=>{const{darkMode:_}=Q(),[I,n]=o.useState(!0),[D,x]=o.useState(null),[W,u]=o.useState([]),[t,C]=o.useState(null),[E,H]=o.useState(""),[A,M]=o.useState(!1);o.useEffect(()=>{try{S.initialize()}catch(s){console.error("Error initializing API services:",s)}},[]),o.useEffect(()=>{(async()=>{try{n(!0),x(null);const i=S.getService("nvd");if(!i){console.warn("NVD service not available, using sample data"),u(k()),n(!1);return}const l=await i.getRecentVulnerabilities(30,10);if(l&&l.vulnerabilities){const c=l.vulnerabilities.map(a=>{var v,g,f,b,y,j,N,d;const r=a.cve,p=((g=(v=r.metrics)==null?void 0:v.cvssMetricV31)==null?void 0:g[0])||((b=(f=r.metrics)==null?void 0:f.cvssMetricV30)==null?void 0:b[0])||{},h=p.cvssData||{};return{id:r.id,description:((j=(y=r.descriptions)==null?void 0:y.find(m=>m.lang==="en"))==null?void 0:j.value)||"No description available",published:r.published,lastModified:r.lastModified,severity:((N=p.cvssData)==null?void 0:N.baseSeverity)||"UNKNOWN",baseScore:h.baseScore||0,attackVector:h.attackVector||"UNKNOWN",references:r.references||[],weaknesses:((d=r.weaknesses)==null?void 0:d.map(m=>{var w,O;return(O=(w=m.description)==null?void 0:w[0])==null?void 0:O.value}))||[]}});u(c)}else u(k());n(!1)}catch(i){console.error("Error loading vulnerability data:",i),x("Unable to load vulnerability data. Using sample data instead."),u(k()),n(!1)}})()},[]);const L=async()=>{if(E.trim())try{n(!0),x(null);const s=S.getService("nvd");if(!s)throw new Error("NVD service not available");const i=await s.searchByKeyword(E);if(i&&i.vulnerabilities){const l=i.vulnerabilities.map(c=>{var h,v,g,f,b,y,j,N;const a=c.cve,r=((v=(h=a.metrics)==null?void 0:h.cvssMetricV31)==null?void 0:v[0])||((f=(g=a.metrics)==null?void 0:g.cvssMetricV30)==null?void 0:f[0])||{},p=r.cvssData||{};return{id:a.id,description:((y=(b=a.descriptions)==null?void 0:b.find(d=>d.lang==="en"))==null?void 0:y.value)||"No description available",published:a.published,lastModified:a.lastModified,severity:((j=r.cvssData)==null?void 0:j.baseSeverity)||"UNKNOWN",baseScore:p.baseScore||0,attackVector:p.attackVector||"UNKNOWN",references:a.references||[],weaknesses:((N=a.weaknesses)==null?void 0:N.map(d=>{var m,w;return(w=(m=d.description)==null?void 0:m[0])==null?void 0:w.value}))||[]}});u(l)}else x("No vulnerabilities found for your search query.");n(!1)}catch(s){console.error("Error searching vulnerabilities:",s),x("Error searching vulnerabilities. Please try again."),n(!1)}},K=async s=>{try{n(!0);const l=await S.getService("nvd").getCVEDetails(s);return n(!1),l}catch(i){return console.error(`Error fetching details for ${s}:`,i),n(!1),null}},F=async s=>{if(t&&t.id===s.id){C(null);return}const i=await K(s.id);C({...s,details:i})},T=s=>{switch(s){case"CRITICAL":return"bg-red-900/30 text-red-400";case"HIGH":return"bg-orange-900/30 text-orange-400";case"MEDIUM":return"bg-yellow-900/30 text-yellow-400";case"LOW":return"bg-green-900/30 text-green-400";default:return"bg-gray-900/30 text-gray-400"}},U=s=>{switch(s){case"CRITICAL":return"#ef4444";case"HIGH":return"#f97316";case"MEDIUM":return"#eab308";case"LOW":return"#22c55e";default:return"#6b7280"}},R=s=>{const i=new Date(s);return i.toLocaleDateString()+" "+i.toLocaleTimeString()},k=()=>[{id:"CVE-2023-12345",description:"A remote code execution vulnerability in the web server component allows attackers to execute arbitrary code via a specially crafted HTTP request.",published:"2023-06-15T14:30:00.000Z",lastModified:"2023-06-20T09:15:00.000Z",severity:"CRITICAL",baseScore:9.8,attackVector:"NETWORK",references:[{url:"https://example.com/advisory/123"},{url:"https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-12345"}],weaknesses:["CWE-78: OS Command Injection","CWE-20: Improper Input Validation"]},{id:"CVE-2023-67890",description:"An SQL injection vulnerability in the authentication module allows attackers to bypass authentication and access sensitive data.",published:"2023-06-10T10:45:00.000Z",lastModified:"2023-06-18T16:20:00.000Z",severity:"HIGH",baseScore:8.5,attackVector:"NETWORK",references:[{url:"https://example.com/advisory/456"},{url:"https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-67890"}],weaknesses:["CWE-89: SQL Injection","CWE-287: Improper Authentication"]},{id:"CVE-2023-54321",description:"A cross-site scripting (XSS) vulnerability in the user profile page allows attackers to inject malicious scripts that execute in victims' browsers.",published:"2023-06-05T08:15:00.000Z",lastModified:"2023-06-12T11:30:00.000Z",severity:"MEDIUM",baseScore:6.4,attackVector:"NETWORK",references:[{url:"https://example.com/advisory/789"},{url:"https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-54321"}],weaknesses:["CWE-79: Cross-site Scripting","CWE-116: Improper Encoding or Escaping of Output"]},{id:"CVE-2023-09876",description:"An information disclosure vulnerability in the logging component may expose sensitive configuration information in log files.",published:"2023-06-01T16:20:00.000Z",lastModified:"2023-06-08T09:45:00.000Z",severity:"LOW",baseScore:3.7,attackVector:"LOCAL",references:[{url:"https://example.com/advisory/012"},{url:"https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-09876"}],weaknesses:["CWE-532: Insertion of Sensitive Information into Log File","CWE-200: Information Exposure"]}],Z=s=>{const i={"CWE-78":["Use parameterized queries or prepared statements","Implement input validation and sanitization","Apply the principle of least privilege","Use a secure API that provides a parameterized interface"],"CWE-20":["Validate all input according to a whitelist of allowed values","Implement strict type checking","Sanitize and normalize input before processing","Use framework-provided validation mechanisms"],"CWE-89":["Use parameterized queries or prepared statements","Apply input validation and sanitization","Implement least privilege database accounts","Use ORM frameworks with built-in SQL injection protection"],"CWE-79":["Use context-sensitive encoding/escaping","Implement Content Security Policy (CSP)","Validate all input on the server side","Use modern frameworks with built-in XSS protection"],"CWE-200":["Implement proper error handling that doesn't expose sensitive information","Use secure logging practices","Apply the principle of least privilege","Encrypt sensitive data"],"CWE-287":["Implement multi-factor authentication","Use secure session management","Apply the principle of defense in depth","Implement proper access controls"]},l=s.map(a=>{const r=a.match(/CWE-(\d+)/);return r?r[0]:null}).filter(Boolean);let c=[];return l.forEach(a=>{i[a]&&(c=[...c,...i[a]])}),c.length===0?["Keep systems and applications updated with the latest security patches","Implement defense in depth with multiple layers of security controls","Apply the principle of least privilege","Conduct regular security assessments and penetration testing","Implement proper input validation and output encoding"]:[...new Set(c)]};return e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[e.jsx(V,{className:"mr-2 text-blue-400"})," Vulnerability Insights",e.jsx("button",{className:"ml-2 text-gray-400 hover:text-gray-300",onClick:()=>M(!A),title:"Information about vulnerabilities",children:e.jsx(G,{size:14})})]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["Updated ",new Date().toLocaleString()]})]}),A&&e.jsxs("div",{className:"mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[e.jsx(V,{className:"mr-1 text-blue-400"})," Understanding Vulnerabilities"]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>M(!1),children:"×"})]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Vulnerabilities are weaknesses in software, hardware, or processes that can be exploited by threat actors. This dashboard provides information about recent vulnerabilities, their severity, and recommended mitigations. Data is sourced from the National Vulnerability Database (NVD) and other threat intelligence sources."}),e.jsx("div",{className:"flex items-center text-xs",children:e.jsxs("a",{href:"https://nvd.nist.gov/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 flex items-center",children:["Learn more about NVD ",e.jsx(z,{className:"ml-1",size:10})]})})]}),e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex",children:[e.jsx("input",{type:"text",className:"flex-1 bg-gray-700 border border-gray-600 rounded-l-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search vulnerabilities (e.g., 'log4j', 'SQL injection', CVE ID)",value:E,onChange:s=>H(s.target.value),onKeyPress:s=>s.key==="Enter"&&L()}),e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white rounded-r-lg px-4 py-2 text-sm",onClick:L,children:e.jsx($,{})})]})}),I&&e.jsx("div",{className:"flex-1 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"})}),D&&e.jsxs("div",{className:"mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm",children:[e.jsx(P,{className:"inline-block mr-2"}),D]}),e.jsxs("div",{className:"flex-1 flex flex-col md:flex-row gap-4 overflow-hidden",children:[e.jsx("div",{className:`overflow-y-auto space-y-2 pr-1 ${t?"md:w-3/5":"w-full"}`,children:!I&&W.length===0?e.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:"No vulnerability data available"}):W.map(s=>e.jsx("div",{className:`bg-gray-700 rounded-lg p-3 border-l-4 cursor-pointer transition-colors ${(t==null?void 0:t.id)===s.id?"bg-gray-600":"hover:bg-gray-650"}`,style:{borderLeftColor:U(s.severity)},onClick:()=>F(s),children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:s.id}),e.jsx("div",{className:"text-sm text-gray-300 line-clamp-2 mt-1",children:s.description})]}),e.jsxs("div",{className:"flex flex-col items-end",children:[e.jsxs("span",{className:`text-xs px-1.5 py-0.5 rounded-full ${T(s.severity)}`,children:[s.severity," (",s.baseScore,")"]}),e.jsx("span",{className:"text-xs text-gray-400 mt-1",children:new Date(s.published).toLocaleDateString()})]})]})},s.id))}),t&&e.jsxs("div",{className:"md:w-2/5 bg-gray-800 rounded-lg p-4 overflow-y-auto",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[e.jsx(P,{className:"mr-2",style:{color:U(t.severity)}}),e.jsx("span",{children:t.id})]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>C(null),children:"×"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Severity"}),e.jsx("div",{className:"font-medium",children:e.jsxs("span",{className:`inline-block px-2 py-0.5 rounded-full text-xs ${T(t.severity)}`,children:[t.severity," (",t.baseScore,")"]})})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Attack Vector"}),e.jsx("div",{className:"font-medium",children:t.attackVector})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Published"}),e.jsx("div",{className:"font-medium text-sm",children:R(t.published)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Last Modified"}),e.jsx("div",{className:"font-medium text-sm",children:R(t.lastModified)})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Description"}),e.jsx("div",{className:"text-sm bg-gray-700 p-3 rounded-lg",children:t.description})]}),t.weaknesses&&t.weaknesses.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Weaknesses"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:t.weaknesses.map((s,i)=>e.jsx("span",{className:"bg-gray-700 px-2 py-1 rounded text-xs",children:s},i))})]}),t.references&&t.references.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"References"}),e.jsxs("div",{className:"space-y-1 text-sm",children:[t.references.slice(0,3).map((s,i)=>e.jsx("div",{children:e.jsxs("a",{href:s.url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 flex items-center",children:[s.url.length>50?s.url.substring(0,50)+"...":s.url,e.jsx(z,{className:"ml-1",size:10})]})},i)),t.references.length>3&&e.jsxs("div",{className:"text-xs text-gray-400",children:["+",t.references.length-3," more references"]})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"font-medium mb-2 flex items-center",children:[e.jsx(V,{className:"mr-1 text-blue-400"})," Mitigation Recommendations"]}),e.jsx("div",{className:"bg-gray-700 p-3 rounded-lg text-sm",children:e.jsx("ul",{className:"list-disc list-inside space-y-1 text-gray-300",children:Z(t.weaknesses||[]).map((s,i)=>e.jsx("li",{children:s},i))})})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Additional Information"}),e.jsxs("div",{className:"grid grid-cols-1 gap-2",children:[e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg flex items-center",children:[e.jsx(q,{className:"text-blue-400 mr-2"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:"Affected Systems"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Check vendor advisories for specific affected versions"})]})]}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg flex items-center",children:[e.jsx(B,{className:"text-green-400 mr-2"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:"Exploitation Status"}),e.jsx("div",{className:"text-xs text-gray-400",children:t.severity==="CRITICAL"||t.severity==="HIGH"?"Active exploitation likely in the wild":"No known active exploitation at this time"})]})]}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg flex items-center",children:[e.jsx(X,{className:"text-purple-400 mr-2"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:"Patch Availability"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Check vendor websites for patch information"})]})]})]})]})]})]}),e.jsxs("div",{className:"mt-4 text-xs text-gray-400",children:[e.jsx("strong",{children:"Data Sources:"})," National Vulnerability Database (NVD) •",e.jsx("strong",{children:"Analysis:"})," Based on CVSS scoring and vulnerability descriptions"]})]})};export{ee as default};
