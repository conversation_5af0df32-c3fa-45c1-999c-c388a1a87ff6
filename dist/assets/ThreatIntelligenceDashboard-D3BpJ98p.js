const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/LiveThreatFeed-BEcP7ojy.js","assets/index-c6UceSOv.js","assets/index-vnVdz15a.css","assets/ThreatAnalyticsDashboard-CJk_8dys.js","assets/CountryRiskMatrix-DqRR2Ly4.js","assets/RegionalThreatDistribution-Bo1NdFSy.js","assets/VulnerabilityInsights-CKBbyHOz.js","assets/ThreatHuntingWorkbench-B0T6OEvm.js","assets/ThreatCorrelationGraph-BsxN9bla.js"])))=>i.map(i=>d[i]);
import{au as n,r as a,j as e,av as o,x as c,aM as d,an as x,E as h,H as u,I as m,aJ as i}from"./index-c6UceSOv.js";const b=a.lazy(()=>i(()=>import("./LiveThreatFeed-BEcP7ojy.js"),__vite__mapDeps([0,1,2,3]))),j=a.lazy(()=>i(()=>import("./CountryRiskMatrix-DqRR2Ly4.js"),__vite__mapDeps([4,1,2,3]))),_=a.lazy(()=>i(()=>import("./RegionalThreatDistribution-Bo1NdFSy.js"),__vite__mapDeps([5,1,2]))),v=a.lazy(()=>i(()=>import("./VulnerabilityInsights-CKBbyHOz.js"),__vite__mapDeps([6,1,2,3]))),f=a.lazy(()=>i(()=>import("./ThreatHuntingWorkbench-B0T6OEvm.js"),__vite__mapDeps([7,1,2,3]))),y=a.lazy(()=>i(()=>import("./ThreatCorrelationGraph-BsxN9bla.js"),__vite__mapDeps([8,1,2,3]))),p=()=>e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"})}),N=()=>{const{darkMode:g}=n(),[s,t]=a.useState("live-feed"),l=[{id:"live-feed",label:"Live Threat Feed",icon:e.jsx(c,{})},{id:"country-risk",label:"Country Risk Matrix",icon:e.jsx(d,{})},{id:"regional-distribution",label:"Regional Threat Distribution",icon:e.jsx(x,{})},{id:"vulnerability-insights",label:"Vulnerability Insights",icon:e.jsx(h,{})},{id:"threat-correlation",label:"Threat Correlation",icon:e.jsx(u,{})},{id:"threat-hunting",label:"Threat Hunting",icon:e.jsx(m,{})}];return e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex border-b border-gray-700 mb-4",children:[l.map(r=>e.jsxs("button",{className:`flex items-center px-4 py-2 text-sm font-medium ${s===r.id?"border-b-2 border-blue-500 text-blue-400":"text-gray-400 hover:text-gray-300"}`,onClick:()=>t(r.id),children:[e.jsx("span",{className:"mr-2",children:r.icon}),r.label]},r.id)),e.jsxs("div",{className:"ml-auto flex items-center px-4",children:[e.jsx(o,{className:"text-gray-500 mr-2"}),e.jsx("span",{className:"text-xs text-gray-500",children:"Data refreshes automatically"})]})]}),e.jsx("div",{className:"flex-1 overflow-hidden",children:e.jsxs(a.Suspense,{fallback:e.jsx(p,{}),children:[s==="live-feed"&&e.jsx("div",{className:"h-full",children:e.jsx(b,{})}),s==="country-risk"&&e.jsx("div",{className:"h-full",children:e.jsx(j,{})}),s==="regional-distribution"&&e.jsx("div",{className:"h-full",children:e.jsx(_,{})}),s==="vulnerability-insights"&&e.jsx("div",{className:"h-full",children:e.jsx(v,{})}),s==="threat-correlation"&&e.jsx("div",{className:"h-full",children:e.jsx(y,{})}),s==="threat-hunting"&&e.jsx("div",{className:"h-full",children:e.jsx(f,{})})]})})]})};export{N as default};
