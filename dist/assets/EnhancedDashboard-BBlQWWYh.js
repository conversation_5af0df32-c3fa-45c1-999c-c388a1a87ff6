import{a as B,j as e,b as T,ax as D,ae as F,G as j,i as C,ai as L,T as M,ag as W,D as S,ay as I,r as g,az as A,I as R,E as P,y as E,aA as q,aB as $,n as O,aC as U,w as v,aD as X,al as V,aE as Q,H as z,d as G,am as K,Q as J,t as k,M as Y,a9 as _,aF as Z,aG as ee,u as se,aH as te,aI as f}from"./index-c6UceSOv.js";const ae=({children:n})=>{const c=B(),i=[{icon:D,label:"Dashboard",path:"/dashboard"},{icon:F,label:"Learning Paths",path:"/dashboard/learning-paths"},{icon:j,label:"Challenges",path:"/dashboard/challenges"},{icon:C,label:"Achievements",path:"/dashboard/achievements"},{icon:L,label:"Progress",path:"/dashboard/progress"},{icon:T,label:"Profile",path:"/dashboard/profile"},{icon:M,label:"Settings",path:"/dashboard/settings"}];return e.jsxs("div",{className:"flex h-screen bg-cyber-black text-white",children:[e.jsxs("div",{className:"w-64 bg-[#0F172A] border-r border-gray-800 flex flex-col",children:[e.jsxs("div",{className:"p-4 border-b border-gray-800",children:[e.jsx("div",{className:"flex items-center justify-center mb-6",children:e.jsxs("h1",{className:"text-xl font-bold text-primary",children:["Cyber",e.jsx("span",{className:"text-white",children:"Learning"})]})}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"w-20 h-20 rounded-full bg-gray-800 border-2 border-primary flex items-center justify-center overflow-hidden",children:e.jsx(T,{className:"text-3xl text-gray-400"})}),e.jsx("h2",{className:"mt-2 font-bold",children:"Username"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Beginner"}),e.jsx("div",{className:"mt-2 w-full bg-gray-800 rounded-full h-1.5",children:e.jsx("div",{className:"bg-primary h-1.5 rounded-full",style:{width:"25%"}})}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Level 1 • 25 XP"})]})]}),e.jsx("nav",{className:"flex-1 overflow-y-auto py-4",children:e.jsx("ul",{className:"space-y-2 px-2",children:i.map((x,s)=>e.jsx("li",{children:e.jsxs(W,{to:x.path,className:`flex items-center gap-3 px-4 py-2.5 rounded-lg transition-colors ${c.pathname===x.path?"bg-primary text-black font-medium":"text-gray-400 hover:bg-gray-800 hover:text-white"}`,children:[e.jsx(x.icon,{className:"text-lg"}),e.jsx("span",{children:x.label})]})},s))})}),e.jsx("div",{className:"p-4 border-t border-gray-800",children:e.jsxs("div",{className:"bg-gray-800 rounded-lg p-3 text-center",children:[e.jsx("p",{className:"text-xs text-gray-400",children:"Need help?"}),e.jsx("button",{className:"mt-2 text-sm text-primary hover:underline",children:"Contact Support"})]})})]}),e.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[e.jsx("header",{className:"bg-[#0F172A] border-b border-gray-800 py-4 px-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-xl font-bold",children:"Dashboard"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"text-gray-400 hover:text-white",children:e.jsx(FaBell,{})}),e.jsx("div",{className:"h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-medium",children:"JD"})})]})]})}),e.jsx("main",{className:"flex-1 overflow-y-auto p-6 bg-cyber-black",children:n})]})]})},H=()=>{const n=[{label:"Offensive",value:"25%",color:"bg-red-500"},{label:"Defensive",value:"40%",color:"bg-blue-500"},{label:"General",value:"35%",color:"bg-green-500"}],c={title:"Bug Bounty Hunter",progress:35,modules:12,completed:4},i={current:3,days:[!0,!0,!0,!1,!1,!1,!1]},x=[{name:"Web Requests",difficulty:"Easy",completed:!0},{name:"Introduction to Web Applications",difficulty:"Fundamental",completed:!1},{name:"Using Web Proxies",difficulty:"Easy",completed:!1}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Welcome back, Username!"}),e.jsx("p",{className:"text-gray-400",children:"Continue your cybersecurity journey where you left off."})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:n.map((s,d)=>e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6 flex flex-col items-center",children:[e.jsx("div",{className:`w-24 h-24 rounded-full ${s.color} bg-opacity-20 flex items-center justify-center mb-4 border-4 ${s.color} border-opacity-50`,children:e.jsx("span",{className:"text-2xl font-bold",children:s.value})}),e.jsx("h3",{className:"text-lg font-medium",children:s.label})]},d))}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Currently Enrolled Path"}),e.jsxs("button",{className:"text-primary hover:underline flex items-center gap-1 text-sm",children:["View All ",e.jsx(S,{className:"text-xs"})]})]}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-6",children:[e.jsxs("div",{className:"bg-[#1E293B] rounded-lg p-4 flex-1",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-primary bg-opacity-20 flex items-center justify-center",children:e.jsx(j,{className:"text-primary text-xl"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold",children:c.title}),e.jsxs("p",{className:"text-sm text-gray-400",children:[c.completed," of ",c.modules," modules completed"]})]})]}),e.jsx("div",{className:"w-full bg-gray-800 rounded-full h-2 mb-2",children:e.jsx("div",{className:"bg-primary h-2 rounded-full",style:{width:`${c.progress}%`}})}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-400",children:[e.jsxs("span",{children:[c.progress,"% complete"]}),e.jsx("span",{children:"Estimated time: 12 hours left"})]}),e.jsx("button",{className:"mt-4 w-full bg-primary hover:bg-primary-hover text-black font-medium py-2 rounded-lg transition-colors",children:"Continue Learning"})]}),e.jsxs("div",{className:"bg-[#1E293B] rounded-lg p-4 md:w-80",children:[e.jsxs("h4",{className:"font-bold mb-4",children:["Weekly Streak ",e.jsxs("span",{className:"text-primary",children:["🔥 ",i.current]})]}),e.jsx("div",{className:"flex justify-between mb-4",children:i.days.map((s,d)=>e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${s?"bg-primary text-black":"bg-gray-800 text-gray-600"}`,children:s?e.jsx(I,{}):d+1}),e.jsx("span",{className:"text-xs text-gray-500",children:["S","M","T","W","T","F","S"][d]})]},d))}),e.jsx("p",{className:"text-sm text-gray-400 text-center",children:"Keep your streak going! Complete at least one module today."})]})]})]}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Recent Activity"}),e.jsx("div",{className:"space-y-4",children:x.map((s,d)=>e.jsxs("div",{className:"bg-[#1E293B] rounded-lg p-4 flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:`w-10 h-10 rounded-lg ${s.completed?"bg-green-500/20":"bg-blue-500/20"} flex items-center justify-center`,children:s.completed?e.jsx(F,{className:"text-green-500"}):e.jsx(F,{className:"text-blue-500"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:s.name}),e.jsx("p",{className:"text-xs text-gray-400",children:s.difficulty})]})]}),e.jsx("div",{children:s.completed?e.jsx("span",{className:"px-3 py-1 bg-green-500/20 text-green-500 rounded-full text-xs",children:"Completed"}):e.jsx("button",{className:"px-3 py-1 bg-primary text-black rounded-full text-xs font-medium",children:"Continue"})})]},d))})]}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Recommended For You"}),e.jsxs("button",{className:"text-primary hover:underline flex items-center gap-1 text-sm",children:["View All ",e.jsx(S,{className:"text-xs"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-[#1E293B] rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-2",children:[e.jsx("div",{className:"w-10 h-10 rounded-lg bg-purple-500/20 flex items-center justify-center",children:e.jsx(j,{className:"text-purple-500"})}),e.jsx("h4",{className:"font-medium",children:"SQL Injection Fundamentals"})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-3",children:"Learn how to exploit SQL injection vulnerabilities in web applications."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"px-2 py-0.5 bg-yellow-500/20 text-yellow-500 rounded text-xs",children:"Medium"}),e.jsx("button",{className:"text-primary hover:underline text-sm",children:"Start Learning"})]})]}),e.jsxs("div",{className:"bg-[#1E293B] rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-2",children:[e.jsx("div",{className:"w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center",children:e.jsx(L,{className:"text-blue-500"})}),e.jsx("h4",{className:"font-medium",children:"Network Traffic Analysis"})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-3",children:"Master the art of analyzing network traffic to detect security threats."}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"px-2 py-0.5 bg-red-500/20 text-red-500 rounded text-xs",children:"Hard"}),e.jsx("button",{className:"text-primary hover:underline text-sm",children:"Start Learning"})]})]})]})]})]})},re=()=>{const[n,c]=g.useState("all"),[i,x]=g.useState(""),s=[{id:1,title:"Bug Bounty Hunter",description:"Learn how to find and report security vulnerabilities in web applications.",category:"offensive",difficulty:"Intermediate",modules:12,estimatedHours:24,rating:4.8,enrolled:!0,progress:35,image:"bug-bounty.jpg",tags:["Web Security","Vulnerability Assessment","Reporting"],aiMatch:95},{id:2,title:"Network Defender",description:"Master the skills needed to protect networks from cyber attacks.",category:"defensive",difficulty:"Advanced",modules:15,estimatedHours:30,rating:4.6,enrolled:!1,progress:0,image:"network-defender.jpg",tags:["Network Security","Firewall","IDS/IPS"],aiMatch:82},{id:3,title:"Web Application Pentester",description:"Learn how to conduct thorough penetration tests on web applications.",category:"offensive",difficulty:"Advanced",modules:18,estimatedHours:36,rating:4.9,enrolled:!1,progress:0,image:"web-pentester.jpg",tags:["Web Security","Penetration Testing","OWASP Top 10"],aiMatch:88},{id:4,title:"Security Operations Analyst",description:"Develop the skills to detect, analyze, and respond to security incidents.",category:"defensive",difficulty:"Intermediate",modules:14,estimatedHours:28,rating:4.7,enrolled:!1,progress:0,image:"soc-analyst.jpg",tags:["SIEM","Incident Response","Threat Hunting"],aiMatch:76}],d=[{id:"all",label:"All Paths",icon:j},{id:"offensive",label:"Offensive",icon:j},{id:"defensive",label:"Defensive",icon:P},{id:"general",label:"General",icon:E}],m=s.filter(t=>{const b=n==="all"||t.category===n,p=t.title.toLowerCase().includes(i.toLowerCase())||t.description.toLowerCase().includes(i.toLowerCase())||t.tags.some(u=>u.toLowerCase().includes(i.toLowerCase()));return b&&p}),a=t=>{switch(t){case"offensive":return e.jsx(j,{className:"text-red-500"});case"defensive":return e.jsx(P,{className:"text-blue-500"});case"general":return e.jsx(E,{className:"text-green-500"});default:return e.jsx(j,{className:"text-primary"})}},h=t=>{switch(t){case"Beginner":return"text-green-500 bg-green-500/20";case"Intermediate":return"text-yellow-500 bg-yellow-500/20";case"Advanced":return"text-red-500 bg-red-500/20";default:return"text-blue-500 bg-blue-500/20"}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Learning Paths"}),e.jsx("p",{className:"text-gray-400",children:"Discover structured learning paths to master cybersecurity skills."})]}),e.jsx("div",{className:"bg-gradient-to-r from-[#1E293B] to-[#0F172A] rounded-lg p-6 border border-primary/30",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-start md:items-center justify-between gap-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center",children:e.jsx(A,{className:"text-primary"})}),e.jsx("h3",{className:"text-lg font-bold",children:"AI-Powered Recommendations"})]}),e.jsx("p",{className:"text-gray-400",children:"Our AI has analyzed your learning style, progress, and goals to recommend the best paths for you."})]}),e.jsx("button",{className:"bg-primary hover:bg-primary-hover text-black font-medium px-4 py-2 rounded-lg transition-colors",children:"Personalize My Learning"})]})}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(R,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"}),e.jsx("input",{type:"text",placeholder:"Search learning paths...",value:i,onChange:t=>x(t.target.value),className:"w-full bg-[#1E293B] border border-gray-700 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-primary"})]})}),e.jsx("div",{className:"flex overflow-x-auto pb-2 gap-2",children:d.map(t=>e.jsxs("button",{onClick:()=>c(t.id),className:`flex items-center gap-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${n===t.id?"bg-primary text-black font-medium":"bg-[#1E293B] text-gray-400 hover:bg-gray-700 hover:text-white"}`,children:[e.jsx(t.icon,{}),e.jsx("span",{children:t.label})]},t.id))})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:m.map(t=>e.jsxs("div",{className:"bg-[#1E293B] rounded-lg overflow-hidden border border-gray-800 hover:border-primary/50 transition-colors",children:[e.jsxs("div",{className:"h-32 bg-gradient-to-r from-[#0F172A] to-[#1E293B] relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsx("div",{className:"w-16 h-16 rounded-full bg-[#0F172A] border-2 border-primary flex items-center justify-center",children:a(t.category)})}),t.aiMatch>85&&e.jsxs("div",{className:"absolute top-2 right-2 px-2 py-1 bg-primary/90 text-black rounded text-xs font-bold flex items-center gap-1",children:[e.jsx(A,{}),e.jsxs("span",{children:["AI Match: ",t.aiMatch,"%"]})]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"text-lg font-bold mb-2",children:t.title}),e.jsx("p",{className:"text-sm text-gray-400 mb-4 line-clamp-2",children:t.description}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:t.tags.map((b,p)=>e.jsx("span",{className:"px-2 py-0.5 bg-[#0F172A] text-gray-400 rounded text-xs",children:b},p))}),e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("span",{className:`px-2 py-0.5 rounded text-xs ${h(t.difficulty)}`,children:t.difficulty}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(A,{className:"text-yellow-500 text-xs"}),e.jsx("span",{className:"text-sm",children:t.rating})]})]}),e.jsxs("div",{className:"flex justify-between text-sm text-gray-400 mb-4",children:[e.jsxs("span",{children:[t.modules," Modules"]}),e.jsxs("span",{children:["~",t.estimatedHours," Hours"]})]}),t.enrolled?e.jsxs("div",{children:[e.jsx("div",{className:"w-full bg-gray-800 rounded-full h-2 mb-2",children:e.jsx("div",{className:"bg-primary h-2 rounded-full",style:{width:`${t.progress}%`}})}),e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("span",{className:"text-xs text-gray-400",children:[t.progress,"% complete"]}),e.jsxs("button",{className:"text-primary hover:underline text-sm flex items-center gap-1",children:["Continue ",e.jsx(S,{className:"text-xs"})]})]})]}):e.jsx("button",{className:"w-full bg-primary hover:bg-primary-hover text-black font-medium py-2 rounded-lg transition-colors",children:"Enroll Now"})]})]},t.id))})]})},ne=()=>{const[n,c]=g.useState(0),[i,x]=g.useState(!1),[s,d]=g.useState(!1),m={title:"Introduction to Web Applications",path:"Bug Bounty Hunter",sections:[{title:"Web Application Architecture",content:`
          <h2>Understanding Web Application Architecture</h2>
          <p>Web applications are complex systems that involve multiple components working together to deliver functionality to users. The basic architecture includes:</p>
          <ul>
            <li><strong>Client-side (Frontend):</strong> The user interface that runs in the browser</li>
            <li><strong>Server-side (Backend):</strong> The application logic and data processing</li>
            <li><strong>Database:</strong> Where application data is stored and retrieved</li>
          </ul>
          <p>Understanding how these components interact is essential for identifying security vulnerabilities.</p>
          
          <div class="bg-[#0F172A] p-4 rounded-lg my-4">
            <h3 class="font-bold mb-2">Key Concept: Client-Server Model</h3>
            <p>In the client-server model, the client (browser) sends requests to the server, which processes these requests and returns responses. This communication typically happens over HTTP or HTTPS protocols.</p>
          </div>
          
          <p>From a security perspective, each component presents different attack surfaces and potential vulnerabilities.</p>
        `,type:"text"},{title:"HTTP Protocol Basics",content:`
          <h2>HTTP Protocol: The Foundation of Web Communication</h2>
          <p>HTTP (Hypertext Transfer Protocol) is the foundation of data communication on the web. Understanding HTTP is crucial for web application security testing.</p>
          
          <h3>HTTP Request Structure</h3>
          <p>An HTTP request consists of:</p>
          <ul>
            <li><strong>Request line:</strong> Method, URL, and HTTP version</li>
            <li><strong>Headers:</strong> Additional information about the request</li>
            <li><strong>Body:</strong> Optional data sent to the server</li>
          </ul>
          
          <div class="bg-[#0F172A] p-4 rounded-lg my-4 font-mono text-sm">
            <p>GET /index.html HTTP/1.1</p>
            <p>Host: example.com</p>
            <p>User-Agent: Mozilla/5.0</p>
            <p>Accept: text/html</p>
          </div>
          
          <h3>HTTP Response Structure</h3>
          <p>An HTTP response consists of:</p>
          <ul>
            <li><strong>Status line:</strong> HTTP version, status code, and reason phrase</li>
            <li><strong>Headers:</strong> Additional information about the response</li>
            <li><strong>Body:</strong> The requested resource or error message</li>
          </ul>
          
          <div class="bg-[#0F172A] p-4 rounded-lg my-4 font-mono text-sm">
            <p>HTTP/1.1 200 OK</p>
            <p>Content-Type: text/html</p>
            <p>Content-Length: 1234</p>
            <p>Server: Apache</p>
            <p></p>
            <p>&lt;html&gt;...&lt;/html&gt;</p>
          </div>
          
          <p>Security vulnerabilities often arise from improper handling of HTTP requests and responses.</p>
        `,type:"text"},{title:"Web Technologies Overview",content:`
          <h2>Modern Web Technologies</h2>
          <p>Web applications use various technologies on both client and server sides:</p>
          
          <h3>Client-side Technologies</h3>
          <ul>
            <li><strong>HTML:</strong> Structure of web pages</li>
            <li><strong>CSS:</strong> Styling and layout</li>
            <li><strong>JavaScript:</strong> Client-side functionality and interactivity</li>
            <li><strong>Frontend Frameworks:</strong> React, Angular, Vue.js, etc.</li>
          </ul>
          
          <h3>Server-side Technologies</h3>
          <ul>
            <li><strong>Languages:</strong> PHP, Python, Ruby, Java, Node.js, etc.</li>
            <li><strong>Frameworks:</strong> Laravel, Django, Ruby on Rails, Spring, Express, etc.</li>
            <li><strong>Databases:</strong> MySQL, PostgreSQL, MongoDB, etc.</li>
          </ul>
          
          <div class="bg-yellow-500/20 p-4 rounded-lg my-4 border-l-4 border-yellow-500">
            <h3 class="font-bold text-yellow-500 mb-2">Security Consideration</h3>
            <p>Each technology stack has its own security considerations and potential vulnerabilities. Understanding the technologies used in a web application helps identify potential security issues.</p>
          </div>
          
          <p>As a bug bounty hunter, you'll need to adapt your testing approach based on the technologies used in the target application.</p>
        `,type:"text"},{title:"Interactive Demo: HTTP Requests",content:{description:`
            <h2>Interactive HTTP Request Demo</h2>
            <p>In this interactive demo, you'll see how HTTP requests and responses work in practice. You can modify the request parameters and see how the response changes.</p>
            <p>Click the "Run Demo" button to start the interactive session.</p>
          `,codeExample:`
            // Example HTTP GET request using JavaScript
            fetch('https://api.example.com/data', {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Authorization': 'Bearer token123'
              }
            })
            .then(response => response.json())
            .then(data => console.log(data))
            .catch(error => console.error('Error:', error));
          `},type:"interactive"},{title:"Knowledge Check",content:{questions:[{question:"Which component of a web application is responsible for storing data?",options:["Frontend","Backend","Database","API"],correctAnswer:2,explanation:"The database is responsible for storing and retrieving application data. The frontend displays the data, the backend processes it, and APIs facilitate communication between components."},{question:"Which HTTP method is typically used to submit form data to a server?",options:["GET","POST","PUT","DELETE"],correctAnswer:1,explanation:"POST is typically used to submit form data to a server. GET is used to request data, PUT is used to update existing resources, and DELETE is used to remove resources."},{question:"Which of the following is NOT a client-side technology?",options:["HTML","CSS","JavaScript","PHP"],correctAnswer:3,explanation:"PHP is a server-side scripting language. HTML, CSS, and JavaScript are all client-side technologies that run in the browser."}]},type:"quiz"}],estimatedTime:25},a=m.sections[n],h=()=>{n<m.sections.length-1&&(c(n+1),d(!1))},t=()=>{n>0&&(c(n-1),d(!1))},b=()=>{switch(a.type){case"text":return e.jsx("div",{className:"prose prose-invert max-w-none",dangerouslySetInnerHTML:{__html:a.content}});case"interactive":return e.jsxs("div",{children:[e.jsx("div",{className:"prose prose-invert max-w-none mb-6",dangerouslySetInnerHTML:{__html:a.content.description}}),e.jsx("div",{className:"bg-[#0F172A] p-4 rounded-lg mb-6 font-mono text-sm overflow-x-auto",children:e.jsx("pre",{children:a.content.codeExample})}),e.jsxs("button",{className:"bg-primary hover:bg-primary-hover text-black font-medium px-4 py-2 rounded-lg transition-colors flex items-center gap-2",children:[e.jsx(X,{}),"Run Demo"]})]});case"quiz":return e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Knowledge Check"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Test your understanding of the concepts covered in this module."}),e.jsx("div",{className:"space-y-8",children:a.content.questions.map((p,u)=>e.jsxs("div",{className:"bg-[#0F172A] p-6 rounded-lg",children:[e.jsxs("h3",{className:"font-bold mb-4",children:[u+1,". ",p.question]}),e.jsx("div",{className:"space-y-3 mb-4",children:p.options.map((N,w)=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-6 h-6 rounded-full border border-gray-600 flex items-center justify-center",children:w===p.correctAnswer?e.jsx("div",{className:"w-3 h-3 rounded-full bg-primary"}):null}),e.jsx("span",{children:N})]},w))}),s&&e.jsxs("div",{className:"bg-primary/10 border border-primary/30 p-4 rounded-lg mt-4",children:[e.jsxs("div",{className:"flex items-center gap-2 text-primary font-bold mb-2",children:[e.jsx(v,{}),e.jsx("span",{children:"Explanation"})]}),e.jsx("p",{children:p.explanation})]})]},u))}),e.jsx("div",{className:"flex justify-center mt-6",children:e.jsxs("button",{onClick:()=>d(!s),className:"text-primary hover:underline flex items-center gap-2",children:[e.jsx(v,{}),s?"Hide Explanation":"Show Explanation"]})})]});default:return e.jsx("p",{children:"Content not available"})}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center gap-2 text-gray-400 text-sm mb-2",children:[e.jsx("span",{children:m.path}),e.jsx("span",{children:"•"}),e.jsxs("span",{children:["Module ",n+1," of ",m.sections.length]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-2xl font-bold",children:m.title}),e.jsx("button",{onClick:()=>x(!i),className:"text-gray-400 hover:text-primary transition-colors",children:i?e.jsx(q,{className:"text-primary"}):e.jsx($,{})})]})]}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-400 mb-2",children:[e.jsx("span",{children:"Progress"}),e.jsxs("span",{children:[Math.round(n/(m.sections.length-1)*100),"%"]})]}),e.jsx("div",{className:"w-full bg-gray-800 rounded-full h-2",children:e.jsx("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${n/(m.sections.length-1)*100}%`}})}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-2",children:[e.jsxs("span",{children:["Estimated time: ",m.estimatedTime," minutes"]}),e.jsxs("span",{children:["Section ",n+1," of ",m.sections.length]})]})]}),e.jsx("div",{className:"bg-[#0F172A] rounded-lg p-4",children:e.jsx("div",{className:"flex overflow-x-auto gap-2 pb-2",children:m.sections.map((p,u)=>e.jsxs("button",{onClick:()=>c(u),className:`px-3 py-1.5 rounded-lg text-sm whitespace-nowrap transition-colors ${n===u?"bg-primary text-black font-medium":u<n?"bg-green-500/20 text-green-500":"bg-[#1E293B] text-gray-400 hover:bg-gray-700 hover:text-white"}`,children:[u<n&&e.jsx(O,{className:"inline mr-1 text-xs"}),p.title]},u))})}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsx("h3",{className:"text-xl font-bold mb-6",children:a.title}),b()]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("button",{onClick:t,disabled:n===0,className:`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${n===0?"bg-gray-800 text-gray-600 cursor-not-allowed":"bg-[#1E293B] text-white hover:bg-gray-700"}`,children:[e.jsx(U,{}),"Previous"]}),e.jsxs("button",{onClick:h,disabled:n===m.sections.length-1,className:`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${n===m.sections.length-1?"bg-gray-800 text-gray-600 cursor-not-allowed":"bg-primary hover:bg-primary-hover text-black font-medium"}`,children:["Next",e.jsx(S,{})]})]}),e.jsx("div",{className:"fixed bottom-6 right-6",children:e.jsx("button",{className:"w-12 h-12 rounded-full bg-primary text-black flex items-center justify-center shadow-lg hover:bg-primary-hover transition-colors",children:e.jsx(v,{className:"text-xl"})})})]})},le=()=>{const[n,c]=g.useState("month"),[i,x]=g.useState(null),s={overview:{totalXP:2450,level:8,nextLevelXP:3e3,streak:12,completedModules:24,completedPaths:1,hoursSpent:42},skills:[{name:"Web Security",icon:j,color:"#ec4899",level:7,progress:70,recentActivity:[{date:"2023-06-01",xp:120,modules:["XSS Fundamentals","CSRF Protection"]},{date:"2023-06-03",xp:80,modules:["SQL Injection Advanced"]},{date:"2023-06-07",xp:150,modules:["Web Cache Poisoning","JWT Attacks"]}]},{name:"Network Security",icon:z,color:"#3b82f6",level:5,progress:50,recentActivity:[{date:"2023-06-02",xp:100,modules:["Wireshark Basics","Network Protocols"]},{date:"2023-06-05",xp:75,modules:["Firewall Configuration"]}]},{name:"Cryptography",icon:G,color:"#f59e0b",level:3,progress:30,recentActivity:[{date:"2023-06-04",xp:60,modules:["Encryption Basics"]}]},{name:"System Security",icon:E,color:"#10b981",level:4,progress:40,recentActivity:[{date:"2023-06-06",xp:90,modules:["Linux Hardening","Privilege Escalation"]}]},{name:"Defensive Security",icon:P,color:"#8b5cf6",level:6,progress:60,recentActivity:[{date:"2023-06-08",xp:110,modules:["SIEM Basics","Log Analysis"]}]}],achievements:[{name:"First Blood",description:"Complete your first module",date:"2023-05-15",icon:C},{name:"Web Warrior",description:"Complete 10 web security modules",date:"2023-05-28",icon:j},{name:"Consistent Learner",description:"Maintain a 7-day streak",date:"2023-06-01",icon:I}],certificates:[{name:"Web Application Security Fundamentals",issueDate:"2023-06-01",id:"CERT-WAS-123"}]},d=[{id:"week",label:"This Week"},{id:"month",label:"This Month"},{id:"year",label:"This Year"},{id:"all",label:"All Time"}],m=a=>{x(i===a?null:a)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Your Progress"}),e.jsx("p",{className:"text-gray-400",children:"Track your learning journey and skill development."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-primary/20 flex items-center justify-center",children:e.jsx(L,{className:"text-primary text-xl"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-bold",children:[s.overview.totalXP," XP"]}),e.jsx("p",{className:"text-sm text-gray-400",children:"Total Experience"})]})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsxs("span",{children:["Level ",s.overview.level]}),e.jsxs("span",{children:["Level ",s.overview.level+1]})]}),e.jsx("div",{className:"w-full bg-gray-800 rounded-full h-2",children:e.jsx("div",{className:"bg-primary h-2 rounded-full",style:{width:`${s.overview.totalXP/s.overview.nextLevelXP*100}%`}})}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1 text-right",children:[s.overview.nextLevelXP-s.overview.totalXP," XP to next level"]})]})]}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-blue-500/20 flex items-center justify-center",children:e.jsx(V,{className:"text-blue-500 text-xl"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-bold",children:[s.overview.hoursSpent," hours"]}),e.jsx("p",{className:"text-sm text-gray-400",children:"Learning Time"})]})]}),e.jsxs("div",{className:"mt-4 flex justify-between",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold",children:s.overview.completedModules}),e.jsx("p",{className:"text-xs text-gray-400",children:"Modules"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold",children:s.overview.completedPaths}),e.jsx("p",{className:"text-xs text-gray-400",children:"Paths"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold text-primary",children:s.overview.streak}),e.jsx("p",{className:"text-xs text-gray-400",children:"Day Streak"})]})]})]}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-purple-500/20 flex items-center justify-center",children:e.jsx(C,{className:"text-purple-500 text-xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold",children:s.achievements.length}),e.jsx("p",{className:"text-sm text-gray-400",children:"Achievements"})]})]}),e.jsxs("div",{className:"mt-4 space-y-2",children:[s.achievements.slice(0,2).map((a,h)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(a.icon,{className:"text-yellow-500 text-sm"}),e.jsx("p",{className:"text-sm truncate",children:a.name})]},h)),s.achievements.length>2&&e.jsxs("p",{className:"text-xs text-primary hover:underline cursor-pointer",children:["+",s.achievements.length-2," more achievements"]})]})]}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-green-500/20 flex items-center justify-center",children:e.jsx(Q,{className:"text-green-500 text-xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold",children:s.certificates.length}),e.jsx("p",{className:"text-sm text-gray-400",children:"Certificates"})]})]}),e.jsxs("div",{className:"mt-4 space-y-2",children:[s.certificates.map((a,h)=>e.jsxs("div",{className:"bg-[#1E293B] p-2 rounded text-sm",children:[e.jsx("p",{className:"font-medium truncate",children:a.name}),e.jsxs("p",{className:"text-xs text-gray-400",children:["Issued: ",new Date(a.issueDate).toLocaleDateString()]})]},h)),s.certificates.length===0&&e.jsx("p",{className:"text-sm text-gray-400",children:"Complete learning paths to earn certificates"})]})]})]}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Skills Progress"}),e.jsx("div",{className:"flex bg-[#1E293B] rounded-lg overflow-hidden",children:d.map(a=>e.jsx("button",{onClick:()=>c(a.id),className:`px-3 py-1.5 text-sm transition-colors ${n===a.id?"bg-primary text-black font-medium":"text-gray-400 hover:text-white"}`,children:a.label},a.id))})]}),e.jsx("div",{className:"space-y-4",children:s.skills.map((a,h)=>e.jsxs("div",{className:"bg-[#1E293B] rounded-lg overflow-hidden",children:[e.jsxs("div",{className:"p-4 flex justify-between items-center cursor-pointer",onClick:()=>m(h),children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center",style:{backgroundColor:`${a.color}20`},children:e.jsx(a.icon,{style:{color:a.color}})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold",children:a.name}),e.jsxs("p",{className:"text-xs text-gray-400",children:["Level ",a.level]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"w-32",children:[e.jsx("div",{className:"w-full bg-gray-800 rounded-full h-2",children:e.jsx("div",{className:"h-2 rounded-full",style:{width:`${a.progress}%`,backgroundColor:a.color}})}),e.jsxs("p",{className:"text-xs text-right mt-1",children:[a.progress,"%"]})]}),i===h?e.jsx(K,{className:"text-gray-400"}):e.jsx(J,{className:"text-gray-400"})]})]}),i===h&&e.jsxs("div",{className:"p-4 border-t border-gray-800",children:[e.jsx("h5",{className:"font-medium text-sm mb-3",children:"Recent Activity"}),a.recentActivity.length>0?e.jsx("div",{className:"space-y-3",children:a.recentActivity.map((t,b)=>e.jsxs("div",{className:"bg-[#0F172A] p-3 rounded-lg",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm",children:new Date(t.date).toLocaleDateString()}),e.jsxs("span",{className:"text-sm font-bold text-primary",children:["+",t.xp," XP"]})]}),e.jsx("div",{className:"space-y-1",children:t.modules.map((p,u)=>e.jsxs("div",{className:"text-sm flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-green-500"}),e.jsx("span",{children:p})]},u))})]},b))}):e.jsx("p",{className:"text-sm text-gray-400",children:"No recent activity for this skill"}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx("button",{className:"text-primary hover:underline text-sm",children:"View Skill Details"})})]})]},h))})]}),e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Recommended Next Steps"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-[#1E293B] p-4 rounded-lg border-l-4 border-primary",children:[e.jsx("h4",{className:"font-bold mb-1",children:"Continue Your Current Path"}),e.jsx("p",{className:"text-sm text-gray-400 mb-3",children:"You're making great progress in the Bug Bounty Hunter path. Keep going!"}),e.jsx("button",{className:"bg-primary hover:bg-primary-hover text-black font-medium px-4 py-2 rounded-lg transition-colors text-sm",children:"Continue Learning"})]}),e.jsxs("div",{className:"bg-[#1E293B] p-4 rounded-lg border-l-4 border-blue-500",children:[e.jsx("h4",{className:"font-bold mb-1",children:"Improve Your Network Security Skills"}),e.jsx("p",{className:"text-sm text-gray-400 mb-3",children:"Based on your progress, we recommend focusing on network security next."}),e.jsx("button",{className:"bg-[#0F172A] hover:bg-gray-800 text-white px-4 py-2 rounded-lg transition-colors text-sm",children:"Explore Network Security Modules"})]}),e.jsxs("div",{className:"bg-[#1E293B] p-4 rounded-lg border-l-4 border-purple-500",children:[e.jsx("h4",{className:"font-bold mb-1",children:"Join the Weekly Challenge"}),e.jsx("p",{className:"text-sm text-gray-400 mb-3",children:"Test your skills and earn bonus XP by participating in this week's challenge."}),e.jsx("button",{className:"bg-[#0F172A] hover:bg-gray-800 text-white px-4 py-2 rounded-lg transition-colors text-sm",children:"View Challenge"})]})]})]})]})},ie=({isOpen:n,onClose:c})=>{const[i,x]=g.useState([{id:1,role:"assistant",content:"Hi there! I'm your AI learning assistant. How can I help you with your cybersecurity learning journey today?",timestamp:new Date}]),[s,d]=g.useState(""),[m,a]=g.useState(!1),h=g.useRef(null),t=["Explain SQL injection in simple terms","What's the difference between XSS and CSRF?","How do I set up a practice environment for web hacking?","What should I learn next after web application security?"];g.useEffect(()=>{h.current&&h.current.scrollIntoView({behavior:"smooth"})},[i]);const b=()=>{if(s.trim()==="")return;const r={id:i.length+1,role:"user",content:s,timestamp:new Date};x(l=>[...l,r]),d(""),a(!0),setTimeout(()=>{const l=N(s);x(o=>[...o,l]),a(!1)},1500)},p=r=>{r.key==="Enter"&&!r.shiftKey&&(r.preventDefault(),b())},u=r=>{d(r);const l={id:i.length+1,role:"user",content:r,timestamp:new Date};x(o=>[...o,l]),a(!0),setTimeout(()=>{const o=N(r);x(y=>[...y,o]),a(!1)},1500)},N=r=>{const l=r.toLowerCase();let o="";return l.includes("sql injection")?o={type:"text",content:"SQL Injection is a code injection technique where an attacker inserts malicious SQL code into input fields that are later passed to a database query. This can allow attackers to view, modify, or delete data they shouldn't have access to."}:l.includes("xss")&&l.includes("csrf")?o={type:"comparison",content:{title:"XSS vs CSRF: Key Differences",items:[{term:"Cross-Site Scripting (XSS)",definition:"Attacker injects malicious scripts into websites viewed by other users. The script runs in the victim's browser with their privileges."},{term:"Cross-Site Request Forgery (CSRF)",definition:"Tricks the victim into submitting a malicious request to a website where they're authenticated. The attack leverages the victim's authenticated session."}]}}:l.includes("practice environment")||l.includes("set up")?o={type:"steps",content:{title:"Setting Up a Web Hacking Practice Environment",steps:["Install a virtual machine software like VirtualBox or VMware","Set up OWASP WebGoat or DVWA (Damn Vulnerable Web Application)","Consider using Kali Linux as your attack platform","Use Burp Suite Community Edition as your proxy tool","Join platforms like HackTheBox or TryHackMe for structured practice"]}}:l.includes("learn next")||l.includes("after web")?o={type:"recommendations",content:{title:"After Web Application Security",paths:[{name:"Network Penetration Testing",description:"Learn how to test network infrastructure for vulnerabilities"},{name:"Mobile Application Security",description:"Explore security issues in Android and iOS applications"},{name:"API Security",description:"Focus on securing and testing APIs"},{name:"Cloud Security",description:"Learn about securing AWS, Azure, or GCP environments"}]}}:o={type:"text",content:"I understand you're asking about "+r+". This is an interesting topic in cybersecurity. Would you like me to provide more specific information or resources about this? Feel free to ask more detailed questions so I can better assist you with your learning journey."},{id:i.length+2,role:"assistant",content:o,timestamp:new Date}},w=r=>{if(r.role==="user"||typeof r.content=="string")return e.jsx("p",{className:"whitespace-pre-wrap",children:r.content});const l=r.content;switch(l.type){case"text":return e.jsx("p",{className:"whitespace-pre-wrap",children:l.content});case"comparison":return e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-2",children:l.content.title}),e.jsx("div",{className:"space-y-3",children:l.content.items.map((o,y)=>e.jsxs("div",{className:"bg-[#0F172A] p-3 rounded-lg",children:[e.jsx("div",{className:"font-bold text-primary",children:o.term}),e.jsx("div",{className:"text-sm mt-1",children:o.definition})]},y))})]});case"steps":return e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-2",children:l.content.title}),e.jsx("ol",{className:"list-decimal list-inside space-y-2",children:l.content.steps.map((o,y)=>e.jsx("li",{className:"pl-2",children:o},y))})]});case"recommendations":return e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-2",children:l.content.title}),e.jsx("div",{className:"space-y-2",children:l.content.paths.map((o,y)=>e.jsxs("div",{className:"bg-[#0F172A] p-3 rounded-lg",children:[e.jsx("div",{className:"font-bold",children:o.name}),e.jsx("div",{className:"text-sm text-gray-400 mt-1",children:o.description})]},y))})]});default:return e.jsx("p",{children:"Unsupported message type"})}};return n?e.jsx("div",{className:"fixed inset-0 flex items-center justify-center bg-black/50 z-50",children:e.jsxs("div",{className:"bg-[#1E293B] w-full max-w-2xl rounded-lg shadow-xl flex flex-col h-[80vh] mx-4",children:[e.jsxs("div",{className:"p-4 border-b border-gray-800 flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center",children:e.jsx(k,{className:"text-primary"})}),e.jsx("h3",{className:"font-bold",children:"AI Learning Assistant"})]}),e.jsx("button",{onClick:c,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Y,{})})]}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[i.map(r=>e.jsx("div",{className:`flex ${r.role==="user"?"justify-end":"justify-start"}`,children:e.jsxs("div",{className:`max-w-[80%] rounded-lg p-3 ${r.role==="user"?"bg-primary text-black":"bg-[#0F172A]"}`,children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("div",{className:"w-6 h-6 rounded-full bg-gray-800 flex items-center justify-center",children:r.role==="user"?e.jsx(T,{className:"text-xs"}):e.jsx(k,{className:"text-xs text-primary"})}),e.jsx("span",{className:"text-xs opacity-75",children:r.role==="user"?"You":"AI Assistant"})]}),e.jsx("div",{className:`${r.role==="user"?"":"text-gray-200"}`,children:typeof r.content=="string"?e.jsx("p",{className:"whitespace-pre-wrap",children:r.content}):w(r)}),e.jsx("div",{className:"text-right text-xs opacity-50 mt-1",children:r.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})},r.id)),m&&e.jsx("div",{className:"flex justify-start",children:e.jsxs("div",{className:"max-w-[80%] rounded-lg p-3 bg-[#0F172A]",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("div",{className:"w-6 h-6 rounded-full bg-gray-800 flex items-center justify-center",children:e.jsx(k,{className:"text-xs text-primary"})}),e.jsx("span",{className:"text-xs opacity-75",children:"AI Assistant"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(_,{className:"animate-spin text-primary"}),e.jsx("span",{children:"Thinking..."})]})]})}),e.jsx("div",{ref:h})]}),i.length<3&&e.jsxs("div",{className:"p-4 border-t border-gray-800",children:[e.jsxs("div",{className:"text-sm text-gray-400 mb-2 flex items-center gap-2",children:[e.jsx(v,{className:"text-primary"}),e.jsx("span",{children:"Suggested questions:"})]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:t.map((r,l)=>e.jsx("button",{onClick:()=>u(r),className:"bg-[#0F172A] hover:bg-gray-800 text-sm px-3 py-1.5 rounded-full transition-colors",children:r},l))})]}),e.jsxs("div",{className:"p-4 border-t border-gray-800",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("textarea",{value:s,onChange:r=>d(r.target.value),onKeyDown:p,placeholder:"Ask me anything about cybersecurity...",className:"w-full bg-[#0F172A] border border-gray-700 rounded-lg px-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-primary resize-none h-12 max-h-32",rows:1}),e.jsxs("div",{className:"absolute right-2 bottom-2 flex gap-2",children:[e.jsx("button",{className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(j,{})}),e.jsx("button",{className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Z,{})})]})]}),e.jsx("button",{onClick:b,disabled:s.trim()==="",className:`w-10 h-10 rounded-full flex items-center justify-center ${s.trim()===""?"bg-gray-700 text-gray-500 cursor-not-allowed":"bg-primary text-black hover:bg-primary-hover"} transition-colors`,children:e.jsx(ee,{className:"text-sm"})})]}),e.jsx("div",{className:"text-xs text-gray-500 mt-2 text-center",children:"AI responses are generated based on your learning progress and may not always be accurate."})]})]})}):null},oe=()=>{const[n,c]=g.useState(!1);return se(),e.jsxs(e.Fragment,{children:[e.jsxs(ae,{children:[e.jsxs(te,{children:[e.jsx(f,{path:"/",element:e.jsx(H,{})}),e.jsx(f,{path:"/learning-paths",element:e.jsx(re,{})}),e.jsx(f,{path:"/learning-paths/:pathId/modules/:moduleId",element:e.jsx(ne,{})}),e.jsx(f,{path:"/progress",element:e.jsx(le,{})}),e.jsx(f,{path:"*",element:e.jsx(H,{})})]}),e.jsx("button",{onClick:()=>c(!0),className:"fixed bottom-6 right-6 w-12 h-12 rounded-full bg-primary text-black flex items-center justify-center shadow-lg hover:bg-primary-hover transition-colors",children:e.jsx(v,{className:"text-xl"})})]}),e.jsx(ie,{isOpen:n,onClose:()=>c(!1)})]})};export{oe as default};
