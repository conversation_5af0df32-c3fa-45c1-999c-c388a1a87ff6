import{au as u,r as h,j as e,E as r,m as l,ag as n,av as b,am as p,Q as g,D as f,ai as y,x as v,I as j,aw as w}from"./index-c6UceSOv.js";import{C as N}from"./CyberForceSEO-CGlWp3aL.js";const A=()=>{const{darkMode:t}=u(),[a,o]=h.useState(null),c=[{id:"soc-analyst",title:"SOC Analyst Simulation",description:"Experience a day in the life of a Security Operations Center analyst",icon:e.jsx(y,{className:"text-blue-500"}),difficulty:"Beginner to Intermediate",duration:"2-4 hours",skills:["Alert Triage","Log Analysis","Incident Classification","Threat Detection"],color:"from-blue-500/20 to-blue-600/5",borderColor:"border-blue-500/20",image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80"},{id:"incident-response",title:"Incident Response Scenario",description:"Respond to and mitigate an active security incident",icon:e.jsx(v,{className:"text-orange-500"}),difficulty:"Intermediate",duration:"3-5 hours",skills:["Incident Handling","Containment Strategies","Evidence Collection","Root Cause Analysis"],color:"from-orange-500/20 to-orange-600/5",borderColor:"border-orange-500/20",image:"https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80"},{id:"threat-hunting",title:"Threat Hunting Exercise",description:"Proactively search for threats that have evaded existing security controls",icon:e.jsx(j,{className:"text-purple-500"}),difficulty:"Intermediate to Advanced",duration:"2-4 hours",skills:["IOC Analysis","Behavioral Analysis","SIEM Query Building","Threat Intelligence"],color:"from-purple-500/20 to-purple-600/5",borderColor:"border-purple-500/20",image:"https://images.unsplash.com/photo-1614064641938-3bbee52942c7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80"},{id:"forensic-investigation",title:"Digital Forensics Investigation",description:"Analyze digital evidence to reconstruct a security incident",icon:e.jsx(w,{className:"text-green-500"}),difficulty:"Intermediate to Advanced",duration:"3-6 hours",skills:["Disk Forensics","Memory Analysis","Timeline Creation","Evidence Handling"],color:"from-green-500/20 to-green-600/5",borderColor:"border-green-500/20",image:"https://images.unsplash.com/photo-1633265486064-086b219458ec?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80"}],d=[{question:"What experience level is required for defensive simulations?",answer:"Our defensive simulations are designed for various skill levels, from beginners to advanced practitioners. Each simulation clearly indicates the recommended experience level, and we provide guidance throughout the exercises."},{question:"What tools will I use in these simulations?",answer:"You'll work with industry-standard security tools including SIEM platforms (like Splunk and ELK Stack), EDR solutions, network monitoring tools, and forensic analysis software. All tools are provided in our browser-based lab environment."},{question:"Are the scenarios based on real-world incidents?",answer:"Yes, our defensive simulations are modeled after real-world cyber attacks and incidents, incorporating current threat actor tactics, techniques, and procedures (TTPs). We regularly update our scenarios to reflect emerging threats."},{question:"Will I receive feedback on my performance?",answer:"Absolutely. Each simulation includes detailed performance metrics and feedback on your actions. You'll receive recommendations for improvement and can compare your approach to expert solutions."}],m=s=>{o(a===s?null:s)};return e.jsxs("div",{className:`min-h-screen ${t?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-20`,children:[e.jsx(N,{title:"Defensive Security Simulations",description:"Develop blue team skills with CyberForce's defensive security simulations. Practice SOC operations, incident response, threat hunting, and digital forensics.",keywords:["defensive security","blue team","SOC training","incident response","threat hunting","digital forensics"],canonicalUrl:"https://cyberforce.om/simulations/defensive"}),e.jsxs("div",{className:"relative overflow-hidden bg-gradient-to-b from-[#0B1120] to-[#1A1F35] py-16",children:[e.jsxs("div",{className:"absolute inset-0 opacity-20",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20"}),e.jsx("div",{className:"grid grid-cols-10 grid-rows-10 h-full w-full",children:Array.from({length:100}).map((s,i)=>e.jsx("div",{className:"border-[0.5px] border-white/5"},i))})]}),e.jsx("div",{className:"container mx-auto px-4 relative z-10",children:e.jsxs("div",{className:"max-w-3xl mx-auto text-center",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6",children:[e.jsx(r,{className:"text-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-blue-400",children:"Blue Team Training"})]}),e.jsxs(l.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-4xl md:text-5xl font-bold mb-6 text-white",children:["Defensive Security ",e.jsx("span",{className:"text-blue-500",children:"Simulations"})]}),e.jsx(l.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"text-xl text-gray-300 mb-8",children:"Develop blue team skills through realistic incident detection and response scenarios"}),e.jsxs(l.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"flex flex-wrap justify-center gap-4",children:[e.jsxs(n,{to:"/pricing",className:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium flex items-center gap-2 transition-colors",children:[e.jsx(r,{})," Start Training"]}),e.jsx(n,{to:"/simulations",className:"px-6 py-3 bg-gray-700 hover:bg-gray-800 text-white rounded-lg font-medium flex items-center gap-2 transition-colors",children:"View All Simulations"})]})]})})]}),e.jsxs("div",{className:"container mx-auto px-4 py-16",children:[e.jsxs("div",{className:"max-w-3xl mx-auto mb-16",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center",children:e.jsx(r,{className:"text-blue-500 text-xl"})}),e.jsx("h2",{className:`text-3xl font-bold ${t?"text-white":"text-gray-900"}`,children:"What Are Defensive Simulations?"})]}),e.jsx("p",{className:`text-lg mb-6 ${t?"text-gray-300":"text-gray-600"}`,children:"Defensive security simulations provide hands-on experience with blue team techniques in realistic environments. These exercises help security professionals develop skills in threat detection, incident response, and security operations to effectively protect organizations from cyber threats."}),e.jsxs("div",{className:`p-4 rounded-lg border ${t?"bg-blue-900/10 border-blue-900/30":"bg-blue-50 border-blue-200"} flex items-start gap-3 mb-8`,children:[e.jsx(b,{className:"text-blue-500 text-xl flex-shrink-0 mt-1"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-blue-500 mb-1",children:"Why Defensive Skills Matter"}),e.jsx("p",{className:`${t?"text-gray-300":"text-gray-700"}`,children:"While offensive security identifies vulnerabilities, defensive security ensures organizations can detect, respond to, and recover from attacks. Both skill sets are essential for a comprehensive security program, but most real-world security roles focus on defensive capabilities."})]})]})]}),e.jsxs("div",{className:"mb-16",children:[e.jsx("h2",{className:`text-3xl font-bold mb-8 text-center ${t?"text-white":"text-gray-900"}`,children:"Available Defensive Simulations"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:c.map(s=>e.jsxs("div",{className:`rounded-xl overflow-hidden border ${s.borderColor} transition-all duration-300 ${t?"bg-[#1A1F35]":"bg-white"}`,children:[e.jsxs("div",{className:"h-48 overflow-hidden relative",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10"}),e.jsx("img",{src:s.image,alt:s.title,className:"w-full h-full object-cover"}),e.jsxs("div",{className:"absolute bottom-0 left-0 p-4 z-20",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-1",children:s.title}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"px-2 py-1 bg-black/50 rounded text-xs text-white",children:s.difficulty}),e.jsx("span",{className:"px-2 py-1 bg-black/50 rounded text-xs text-white",children:s.duration})]})]})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("p",{className:`mb-4 ${t?"text-gray-300":"text-gray-600"}`,children:s.description}),e.jsx("h4",{className:"font-semibold mb-2",children:"Skills You'll Practice:"}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-6",children:s.skills.map((i,x)=>e.jsx("span",{className:`px-2 py-1 rounded text-xs ${t?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-700"}`,children:i},x))}),e.jsx("button",{className:"w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors",children:"Start Simulation"})]})]},s.id))})]}),e.jsxs("div",{className:"max-w-3xl mx-auto mb-16",children:[e.jsx("h2",{className:`text-3xl font-bold mb-8 text-center ${t?"text-white":"text-gray-900"}`,children:"Frequently Asked Questions"}),e.jsx("div",{className:"space-y-4",children:d.map((s,i)=>e.jsxs("div",{className:`rounded-lg border ${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} overflow-hidden`,children:[e.jsxs("button",{onClick:()=>m(i),className:"w-full px-6 py-4 text-left flex items-center justify-between",children:[e.jsx("span",{className:"font-semibold",children:s.question}),a===i?e.jsx(p,{}):e.jsx(g,{})]}),a===i&&e.jsx("div",{className:`px-6 py-4 border-t ${t?"border-gray-800":"border-gray-200"}`,children:e.jsx("p",{className:`${t?"text-gray-300":"text-gray-600"}`,children:s.answer})})]},i))})]}),e.jsxs("div",{className:`rounded-xl overflow-hidden relative ${t?"bg-[#1A1F35]":"bg-white"} border ${t?"border-gray-800":"border-gray-200"}`,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10"}),e.jsxs("div",{className:"relative z-10 p-8 md:p-12 text-center",children:[e.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Ready to Test Your Defensive Security Skills?"}),e.jsx("p",{className:`text-lg mb-8 max-w-2xl mx-auto ${t?"text-gray-300":"text-gray-600"}`,children:"Join CyberForce today and gain access to our full range of defensive security simulations and training resources."}),e.jsxs(n,{to:"/pricing",className:"px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium inline-flex items-center gap-2 transition-colors",children:["Start Training ",e.jsx(f,{})]})]})]})]})]})};export{A as default};
