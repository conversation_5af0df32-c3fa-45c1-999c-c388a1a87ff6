import{au as se,r as i,j as e,I as d,av as ae,ak as x,aD as te,x as ie,aY as Y}from"./index-c6UceSOv.js";import{d as r}from"./ThreatAnalyticsDashboard-CJk_8dys.js";const oe=()=>{var w,S,T,k,I,C,P,R,H,A,F;const{darkMode:re}=se(),[o,u]=i.useState("learn"),[g,l]=i.useState(!1),[b,c]=i.useState(null),[j,p]=i.useState(!1),[n,v]=i.useState(""),[y,f]=i.useState("ip"),[a,N]=i.useState(null),[le,X]=i.useState(!1),[m,ne]=i.useState({averageSalary:"$110,140",jobGrowth:"35%",openPositions:"3,500+",requiredSkills:["Threat Intelligence","SIEM Tools","Network Analysis","MITRE ATT&CK"]});i.useEffect(()=>{(async()=>{try{l(!0),await r.initialize(),X(!0),l(!1)}catch(t){console.error("Failed to initialize APIs:",t),c("Failed to initialize threat intelligence APIs. Some features may be limited."),l(!1)}})()},[]);const Z=[{id:"learn",label:"Learn Threat Hunting",icon:e.jsx(x,{className:"mr-2"})},{id:"hunt",label:"Hunt Threats",icon:e.jsx(d,{className:"mr-2"})},{id:"career",label:"Career Insights",icon:e.jsx(Y,{className:"mr-2"})}],ee=async()=>{if(!n.trim()){c("Please enter a search query");return}try{l(!0),c(null),N(null);let s={};switch(y){case"ip":if(r.services.virusTotal){const t=await r.services.virusTotal.getIPReport(n);s.virusTotal=t}if(r.services.shodan){const t=await r.services.shodan.getHostInfo(n);s.shodan=t}break;case"domain":if(r.services.virusTotal){const t=await r.services.virusTotal.getDomainReport(n);s.virusTotal=t}break;case"cve":if(r.services.nvd){const t=await r.services.nvd.searchCVE(n);s.nvd=t}break;default:c("Invalid search type")}N(s),l(!1)}catch(s){console.error("Search error:",s),c(`Error performing search: ${s.message||"Unknown error"}`),l(!1)}};return e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[e.jsx(d,{className:"mr-2 text-blue-400"})," Threat Hunting Workbench",e.jsx("button",{className:"ml-2 text-gray-400 hover:text-gray-300",onClick:()=>p(!j),title:"Information about threat hunting",children:e.jsx(ae,{size:14})})]})}),j&&e.jsxs("div",{className:"mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[e.jsx(d,{className:"mr-1 text-blue-400"})," About Threat Hunting"]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>p(!1),children:"×"})]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Threat hunting is a proactive cybersecurity approach that focuses on searching for malicious actors and activities in your network that may have evaded existing security solutions. This workbench provides tools to learn about threat hunting, practice with real threat intelligence, and explore career opportunities."})]}),e.jsx("div",{className:"flex border-b border-gray-700 mb-4",children:Z.map(s=>e.jsxs("button",{className:`px-4 py-2 text-sm font-medium flex items-center ${o===s.id?"border-b-2 border-blue-500 text-blue-400":"text-gray-400 hover:text-gray-300"}`,onClick:()=>u(s.id),children:[s.icon,s.label]},s.id))}),e.jsxs("div",{className:"flex-1 overflow-auto",children:[o==="learn"&&e.jsxs("div",{className:"h-full",children:[e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-4",children:[e.jsxs("h4",{className:"text-lg font-semibold mb-4 flex items-center",children:[e.jsx(x,{className:"mr-2 text-green-400"})," Introduction to Threat Hunting"]}),e.jsx("p",{className:"mb-4",children:"Threat hunting is a proactive cybersecurity approach where security professionals actively search for threats that have evaded existing security controls. Unlike traditional security monitoring that relies on alerts, threat hunting assumes that threats may already be present in the environment."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Why Hunt?"}),e.jsxs("ul",{className:"list-disc pl-5 text-sm space-y-1",children:[e.jsx("li",{children:"Reduce dwell time of attackers"}),e.jsx("li",{children:"Find threats that evaded detection"}),e.jsx("li",{children:"Validate security controls"}),e.jsx("li",{children:"Improve detection capabilities"})]})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Key Skills"}),e.jsxs("ul",{className:"list-disc pl-5 text-sm space-y-1",children:[e.jsx("li",{children:"Data analysis"}),e.jsx("li",{children:"Threat intelligence"}),e.jsx("li",{children:"System & network knowledge"}),e.jsx("li",{children:"Investigative mindset"})]})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Hunting Process"}),e.jsxs("ul",{className:"list-disc pl-5 text-sm space-y-1",children:[e.jsx("li",{children:"Create hypothesis"}),e.jsx("li",{children:"Gather & analyze data"}),e.jsx("li",{children:"Identify patterns"}),e.jsx("li",{children:"Investigate & document"})]})]})]})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Guided Hunt: Finding Suspicious IPs"}),e.jsx("p",{className:"mb-4",children:"In this guided hunt, we'll learn how to identify potentially malicious IP addresses using threat intelligence. Follow these steps to conduct your first threat hunt."}),e.jsxs("div",{className:"mb-6 bg-gray-700 p-4 rounded-lg",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Step 1: Form a Hypothesis"}),e.jsx("p",{className:"text-sm mb-2",children:"Every hunt starts with a hypothesis. For this hunt, our hypothesis is:"}),e.jsx("div",{className:"bg-gray-600 p-3 rounded text-sm italic mb-2",children:'"There may be suspicious IP addresses in our environment that have been flagged by threat intelligence sources."'}),e.jsx("p",{className:"text-sm",children:"This hypothesis is testable by checking IP addresses against reputation databases like VirusTotal and Shodan."})]}),e.jsxs("div",{className:"mb-6 bg-gray-700 p-4 rounded-lg",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Step 2: Try the Hunt"}),e.jsx("p",{className:"text-sm mb-3",children:"Let's try hunting for a known malicious IP address. Enter one of these example IPs in the Hunt tab:"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 mb-3",children:[e.jsx("div",{className:"bg-gray-600 p-2 rounded text-sm font-mono",children:"**************"}),e.jsx("div",{className:"bg-gray-600 p-2 rounded text-sm font-mono",children:"**************"}),e.jsx("div",{className:"bg-gray-600 p-2 rounded text-sm font-mono",children:"**************"}),e.jsx("div",{className:"bg-gray-600 p-2 rounded text-sm font-mono",children:"**************"})]}),e.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2 text-sm flex items-center",onClick:()=>{u("hunt"),f("ip"),v("**************")},children:[e.jsx(te,{className:"mr-2"})," Try This Hunt"]})]}),e.jsxs("div",{className:"mb-6 bg-gray-700 p-4 rounded-lg",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Step 3: Analyze the Results"}),e.jsx("p",{className:"text-sm mb-3",children:"When analyzing the results, look for these indicators of suspicious activity:"}),e.jsxs("ul",{className:"list-disc pl-5 text-sm space-y-1 mb-3",children:[e.jsx("li",{children:"High detection rates in VirusTotal"}),e.jsx("li",{children:"Open suspicious ports in Shodan"}),e.jsx("li",{children:"Association with known malicious infrastructure"}),e.jsx("li",{children:"Unusual geographic locations"})]}),e.jsx("p",{className:"text-sm",children:"Document your findings and determine if further investigation is needed. In a real environment, you would check if these IPs appear in your logs or network traffic."})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Next Steps"}),e.jsx("p",{className:"text-sm mb-3",children:"After completing this guided hunt, you can:"}),e.jsxs("ul",{className:"list-disc pl-5 text-sm space-y-1",children:[e.jsx("li",{children:"Try hunting for suspicious domains"}),e.jsx("li",{children:"Search for known vulnerabilities (CVEs)"}),e.jsx("li",{children:"Create your own hunting hypotheses"}),e.jsx("li",{children:"Learn about the MITRE ATT&CK framework for more structured hunting"})]})]})]})]}),o==="hunt"&&e.jsx("div",{className:"h-full",children:e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-4",children:[e.jsxs("h4",{className:"text-lg font-semibold mb-4 flex items-center",children:[e.jsx(d,{className:"mr-2 text-blue-400"})," Threat Intelligence Search"]}),e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"md:col-span-3",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Search Query"}),e.jsx("input",{type:"text",className:"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Enter IP, domain, or CVE ID...",value:n,onChange:s=>v(s.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Search Type"}),e.jsxs("select",{className:"w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500",value:y,onChange:s=>f(s.target.value),children:[e.jsx("option",{value:"ip",children:"IP Address"}),e.jsx("option",{value:"domain",children:"Domain"}),e.jsx("option",{value:"cve",children:"CVE ID"})]})]})]})}),e.jsx("div",{className:"mb-4",children:e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2 text-sm flex items-center",onClick:ee,disabled:g,children:g?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"animate-pulse mr-2",children:"⏳"})," Searching..."]}):e.jsxs(e.Fragment,{children:[e.jsx(d,{className:"mr-2"})," Search"]})})}),b&&e.jsxs("div",{className:"mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm",children:[e.jsx(ie,{className:"inline-block mr-2"}),b]}),a&&e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 overflow-auto",children:[e.jsx("h5",{className:"font-medium mb-3",children:"Search Results"}),a.virusTotal&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("h6",{className:"text-sm font-medium mb-2 flex items-center",children:[e.jsx("img",{src:"https://www.virustotal.com/gui/images/favicon.png",alt:"VirusTotal",className:"w-4 h-4 mr-2"}),"VirusTotal Results"]}),e.jsxs("div",{className:"bg-gray-800 p-3 rounded mb-2",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Malicious Detections"}),e.jsxs("span",{className:"text-sm bg-red-900/30 text-red-400 px-2 py-1 rounded",children:[((T=(S=(w=a.virusTotal.data)==null?void 0:w.attributes)==null?void 0:S.last_analysis_stats)==null?void 0:T.malicious)||0," /",(((C=(I=(k=a.virusTotal.data)==null?void 0:k.attributes)==null?void 0:I.last_analysis_stats)==null?void 0:C.malicious)||0)+(((H=(R=(P=a.virusTotal.data)==null?void 0:P.attributes)==null?void 0:R.last_analysis_stats)==null?void 0:H.harmless)||0)]})]}),((F=(A=a.virusTotal.data)==null?void 0:A.attributes)==null?void 0:F.last_analysis_results)&&e.jsx("div",{className:"text-xs",children:e.jsx("div",{className:"grid grid-cols-2 gap-2",children:Object.entries(a.virusTotal.data.attributes.last_analysis_results).filter(([s,t])=>t.category==="malicious").slice(0,6).map(([s,t],h)=>e.jsxs("div",{className:"bg-gray-700 p-2 rounded",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"text-red-400",children:t.result})]},h))})})]})]}),a.shodan&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("h6",{className:"text-sm font-medium mb-2 flex items-center",children:[e.jsx("img",{src:"https://www.shodan.io/static/img/favicon.png",alt:"Shodan",className:"w-4 h-4 mr-2"}),"Shodan Results"]}),e.jsxs("div",{className:"bg-gray-800 p-3 rounded mb-2",children:[a.shodan.country_name&&e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm",children:"Location"}),e.jsxs("span",{className:"text-sm",children:[a.shodan.country_name,", ",a.shodan.city||"Unknown City"]})]}),a.shodan.ports&&a.shodan.ports.length>0&&e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm",children:"Open Ports"}),e.jsxs("span",{className:"text-sm",children:[a.shodan.ports.slice(0,10).join(", "),a.shodan.ports.length>10?"...":""]})]}),a.shodan.tags&&a.shodan.tags.length>0&&e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm",children:"Tags"}),e.jsx("span",{className:"text-sm",children:a.shodan.tags.join(", ")})]})]})]}),a.nvd&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("h6",{className:"text-sm font-medium mb-2 flex items-center",children:[e.jsx("img",{src:"https://nvd.nist.gov/favicon.ico",alt:"NVD",className:"w-4 h-4 mr-2"}),"NVD Results"]}),a.nvd.vulnerabilities&&a.nvd.vulnerabilities.map((s,t)=>{var h,D,E,V,z,_,M,G,L,O,q,U,$,W,K,Q,B,J;return e.jsxs("div",{className:"bg-gray-800 p-3 rounded mb-2",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm font-medium",children:s.cve.id}),e.jsxs("span",{className:`text-sm px-2 py-1 rounded ${((V=(E=(D=(h=s.cve.metrics)==null?void 0:h.cvssMetricV31)==null?void 0:D[0])==null?void 0:E.cvssData)==null?void 0:V.baseScore)>=9?"bg-red-900/30 text-red-400":((G=(M=(_=(z=s.cve.metrics)==null?void 0:z.cvssMetricV31)==null?void 0:_[0])==null?void 0:M.cvssData)==null?void 0:G.baseScore)>=7?"bg-orange-900/30 text-orange-400":((U=(q=(O=(L=s.cve.metrics)==null?void 0:L.cvssMetricV31)==null?void 0:O[0])==null?void 0:q.cvssData)==null?void 0:U.baseScore)>=4?"bg-yellow-900/30 text-yellow-400":"bg-green-900/30 text-green-400"}`,children:["CVSS: ",((Q=(K=(W=($=s.cve.metrics)==null?void 0:$.cvssMetricV31)==null?void 0:W[0])==null?void 0:K.cvssData)==null?void 0:Q.baseScore)||"N/A"]})]}),e.jsx("div",{className:"text-sm mb-2",children:(J=(B=s.cve.descriptions)==null?void 0:B[0])==null?void 0:J.value}),e.jsxs("div",{className:"text-xs text-gray-400",children:["Published: ",new Date(s.cve.published).toLocaleDateString()]})]},t)})]})]})]})}),o==="career"&&e.jsx("div",{className:"h-full",children:e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-4",children:[e.jsxs("h4",{className:"text-lg font-semibold mb-4 flex items-center",children:[e.jsx(Y,{className:"mr-2 text-green-400"})," Cybersecurity Career Insights"]}),e.jsx("p",{className:"mb-4",children:"Threat hunting is one of the most in-demand skills in cybersecurity today. Organizations are increasingly recognizing the value of proactive threat detection and are willing to pay a premium for skilled threat hunters."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsxs("h5",{className:"font-medium mb-2 flex items-center",children:[e.jsx("span",{className:"text-green-400 mr-2",children:"$"})," Average Salary"]}),e.jsx("div",{className:"text-2xl font-bold mb-1",children:m.averageSalary}),e.jsx("p",{className:"text-sm text-gray-400",children:"For Threat Hunting Specialists in the US"})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsxs("h5",{className:"font-medium mb-2 flex items-center",children:[e.jsx("span",{className:"text-green-400 mr-2",children:"↗"})," Job Growth"]}),e.jsx("div",{className:"text-2xl font-bold mb-1",children:m.jobGrowth}),e.jsx("p",{className:"text-sm text-gray-400",children:"Projected growth over the next 5 years"})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsxs("h5",{className:"font-medium mb-2 flex items-center",children:[e.jsx("span",{className:"text-green-400 mr-2",children:"🔍"})," Open Positions"]}),e.jsx("div",{className:"text-2xl font-bold mb-1",children:m.openPositions}),e.jsx("p",{className:"text-sm text-gray-400",children:"Current job openings nationwide"})]})]}),e.jsx("h5",{className:"font-medium mb-3",children:"Required Skills for Threat Hunters"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mb-6",children:m.requiredSkills.map((s,t)=>e.jsx("div",{className:"bg-gray-700 p-2 rounded-lg text-center",children:s},t))}),e.jsxs("div",{className:"bg-blue-900/20 border border-blue-800 rounded-lg p-4",children:[e.jsxs("h5",{className:"font-medium mb-2 flex items-center",children:[e.jsx(x,{className:"mr-2"})," Learning Path"]}),e.jsx("p",{className:"text-sm mb-3",children:"To become a threat hunter, follow this learning path:"}),e.jsxs("ol",{className:"list-decimal pl-5 text-sm space-y-1",children:[e.jsx("li",{children:"Learn network and system fundamentals"}),e.jsx("li",{children:"Master security monitoring tools and techniques"}),e.jsx("li",{children:"Study threat intelligence and IOC identification"}),e.jsx("li",{children:"Practice with real-world hunting scenarios"}),e.jsx("li",{children:"Get certified (SANS FOR508, Certified Threat Hunter)"})]})]})]})})]})]})};export{oe as default};
