import{r as n,au as x,j as e,ag as c,b as p,I as g,ai as u,bE as y,L as d,i as b}from"./index-c6UceSOv.js";const j=[{id:1,username:"CyberNinja",points:12500,challenges_completed:78,rank:1,country:"India",badge:"Master Hacker"},{id:2,username:"HackMaster",points:11200,challenges_completed:65,rank:2,country:"India",badge:"Elite Hacker"},{id:3,username:"SecurityPro",points:10800,challenges_completed:62,rank:3,country:"India",badge:"Elite Hacker"},{id:4,username:"CodeBreaker",points:9500,challenges_completed:58,rank:4,country:"USA",badge:"Advanced Hacker"},{id:5,username:"ByteDefender",points:9200,challenges_completed:55,rank:5,country:"UK",badge:"Advanced Hacker"},{id:6,username:"Cip<PERSON><PERSON><PERSON><PERSON>",points:8900,challenges_completed:52,rank:6,country:"Canada",badge:"Advanced Hacker"},{id:7,username:"NetRunner",points:8600,challenges_completed:50,rank:7,country:"Germany",badge:"Advanced Hacker"},{id:8,username:"BinaryWizard",points:8300,challenges_completed:48,rank:8,country:"Australia",badge:"Advanced Hacker"},{id:9,username:"FirewallBreacher",points:8e3,challenges_completed:46,rank:9,country:"Japan",badge:"Intermediate Hacker"},{id:10,username:"PacketSniffer",points:7800,challenges_completed:45,rank:10,country:"India",badge:"Intermediate Hacker"},{id:11,username:"RootAccess",points:7500,challenges_completed:43,rank:11,country:"Brazil",badge:"Intermediate Hacker"},{id:12,username:"ShellShock",points:7200,challenges_completed:41,rank:12,country:"France",badge:"Intermediate Hacker"},{id:13,username:"ZeroDayHunter",points:7e3,challenges_completed:40,rank:13,country:"Russia",badge:"Intermediate Hacker"},{id:14,username:"ExploitMaster",points:6800,challenges_completed:38,rank:14,country:"China",badge:"Intermediate Hacker"},{id:15,username:"VulnScanner",points:6500,challenges_completed:36,rank:15,country:"South Korea",badge:"Intermediate Hacker"}],f=()=>{const[s,i]=n.useState(""),[o,m]=n.useState("all-time"),{darkMode:h}=x(),t=j.filter(a=>a.username.toLowerCase().includes(s.toLowerCase())||a.country.toLowerCase().includes(s.toLowerCase())||a.badge.toLowerCase().includes(s.toLowerCase())),l=a=>{switch(a){case 1:return e.jsx(b,{className:"text-yellow-500 text-xl"});case 2:return e.jsx(d,{className:"text-gray-400 text-xl"});case 3:return e.jsx(d,{className:"text-amber-700 text-xl"});default:return e.jsx(y,{className:"text-blue-500 text-xl"})}};return e.jsx("div",{className:`min-h-screen ${h?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Leaderboard"}),e.jsx("p",{className:"text-gray-400",children:"See how you rank against other cybersecurity enthusiasts"})]}),e.jsxs("div",{className:"mt-4 md:mt-0 flex items-center gap-2",children:[e.jsx(c,{to:"/challenges",className:"bg-[#1A1F35] hover:bg-[#252D4A] text-white px-4 py-2 rounded-md transition-colors",children:"Challenges"}),e.jsxs(c,{to:"/profile",className:"bg-[#88cc14] hover:bg-[#7ab811] text-black px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2",children:[e.jsx(p,{}),"My Profile"]})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 mt-8",children:t.slice(0,3).map((a,r)=>e.jsxs("div",{className:`theme-card rounded-xl p-6 ${r===0?"border-yellow-500 shadow-lg shadow-yellow-500/10":r===1?"border-gray-400 shadow-lg shadow-gray-400/10":"border-amber-700 shadow-lg shadow-amber-700/10"}`,children:[e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-[#0B1120] rounded-full flex items-center justify-center",children:l(a.rank)}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-bold",children:a.username}),e.jsx("p",{className:"text-gray-400",children:a.country})]})]}),e.jsxs("div",{className:"flex justify-between items-center mb-3",children:[e.jsx("span",{className:"text-gray-400",children:"Points:"}),e.jsx("span",{className:"font-bold text-[#88cc14]",children:a.points.toLocaleString()})]}),e.jsxs("div",{className:"flex justify-between items-center mb-3",children:[e.jsx("span",{className:"text-gray-400",children:"Challenges:"}),e.jsx("span",{className:"font-bold",children:a.challenges_completed})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400",children:"Badge:"}),e.jsx("span",{className:"bg-[#0B1120] text-[#88cc14] px-2 py-1 rounded text-sm",children:a.badge})]})]},a.id))}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsxs("div",{className:"relative flex-grow",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(g,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"theme-input rounded-lg block w-full pl-10 p-2.5 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]",placeholder:"Search users...",value:s,onChange:a=>i(a.target.value)})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("div",{className:"theme-card rounded-lg p-2.5 flex items-center gap-2",children:[e.jsx(u,{className:"text-gray-500"}),e.jsxs("select",{className:"bg-transparent theme-text-primary focus:outline-none",value:o,onChange:a=>m(a.target.value),children:[e.jsx("option",{value:"all-time",className:"theme-bg-secondary theme-text-primary",children:"All Time"}),e.jsx("option",{value:"monthly",className:"theme-bg-secondary theme-text-primary",children:"This Month"}),e.jsx("option",{value:"weekly",className:"theme-bg-secondary theme-text-primary",children:"This Week"}),e.jsx("option",{value:"daily",className:"theme-bg-secondary theme-text-primary",children:"Today"})]})]})})]})}),e.jsx("div",{className:"theme-card rounded-xl overflow-hidden shadow-lg",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"theme-table w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"theme-border border-b",children:[e.jsx("th",{className:"px-6 py-4 text-left",children:"Rank"}),e.jsx("th",{className:"px-6 py-4 text-left",children:"User"}),e.jsx("th",{className:"px-6 py-4 text-left hidden md:table-cell",children:"Country"}),e.jsx("th",{className:"px-6 py-4 text-right hidden sm:table-cell",children:"Challenges"}),e.jsx("th",{className:"px-6 py-4 text-right",children:"Points"}),e.jsx("th",{className:"px-6 py-4 text-right hidden lg:table-cell",children:"Badge"})]})}),e.jsx("tbody",{children:t.map(a=>e.jsxs("tr",{className:"theme-border border-b transition-colors hover:theme-bg-tertiary",children:[e.jsx("td",{className:"px-6 py-4",children:e.jsx("div",{className:"flex items-center gap-2",children:a.rank<=3?l(a.rank):e.jsx("span",{className:"font-bold",children:a.rank})})}),e.jsx("td",{className:"px-6 py-4 font-medium theme-text-primary",children:a.username}),e.jsx("td",{className:"px-6 py-4 hidden md:table-cell theme-text-primary",children:a.country}),e.jsx("td",{className:"px-6 py-4 text-right hidden sm:table-cell theme-text-primary",children:a.challenges_completed}),e.jsx("td",{className:"px-6 py-4 text-right font-bold text-[#88cc14]",children:a.points.toLocaleString()}),e.jsx("td",{className:"px-6 py-4 text-right hidden lg:table-cell",children:e.jsx("span",{className:"theme-bg-primary text-[#88cc14] px-2 py-1 rounded text-sm",children:a.badge})})]},a.id))})]})})})]})})};export{f as default};
