import{au as D,r as y,j as e,E as Q,aK as de,x as S,an as ne,aL as me,aM as ie,ad as he,d as re,y as le,a9 as q,aN as ce,ae as ue,G as V,ao as z,D as H,I as E,aO as Z,i as U,s as W,t as Y,b as ge,aG as xe,av as pe,aq as fe,aP as be,ak as ye,af as ve}from"./index-c6UceSOv.js";import je from"./LiveThreatFeed-BEcP7ojy.js";import{a as oe,T as Ne}from"./ThreatAnalyticsDashboard-CJk_8dys.js";const we=[{name:"Russia",lat:55.7558,lng:37.6173,weight:25},{name:"China",lat:39.9042,lng:116.4074,weight:25},{name:"North Korea",lat:39.0392,lng:125.7625,weight:15},{name:"Iran",lat:35.6892,lng:51.389,weight:15},{name:"United States",lat:37.0902,lng:-95.7129,weight:10},{name:"Brazil",lat:-14.235,lng:-51.9253,weight:5},{name:"Ukraine",lat:48.3794,lng:31.1656,weight:5}],ke=[{name:"United States",lat:37.0902,lng:-95.7129,weight:30},{name:"United Kingdom",lat:51.5074,lng:-.1278,weight:10},{name:"Germany",lat:51.1657,lng:10.4515,weight:10},{name:"France",lat:46.2276,lng:2.2137,weight:8},{name:"Japan",lat:36.2048,lng:138.2529,weight:8},{name:"South Korea",lat:35.9078,lng:127.7669,weight:7},{name:"Australia",lat:-25.2744,lng:133.7751,weight:7},{name:"India",lat:20.5937,lng:78.9629,weight:7},{name:"Canada",lat:56.1304,lng:-106.3468,weight:5},{name:"Italy",lat:41.8719,lng:12.5674,weight:4},{name:"Spain",lat:40.4637,lng:-3.7492,weight:4}],Se=[{name:"Ransomware",weight:25,color:"#ff0000"},{name:"DDoS",weight:20,color:"#ff3300"},{name:"Phishing",weight:20,color:"#ff6600"},{name:"Malware",weight:15,color:"#ff9900"},{name:"SQL Injection",weight:10,color:"#ffcc00"},{name:"XSS",weight:5,color:"#ffff00"},{name:"Zero-day Exploit",weight:5,color:"#ff00ff"}],Te=[{level:"Critical",weight:15,color:"#ff0000"},{level:"High",weight:25,color:"#ff3300"},{level:"Medium",weight:35,color:"#ffaa00"},{level:"Low",weight:25,color:"#ffff00"}],Ce=[{name:"Financial Services",weight:25},{name:"Healthcare",weight:20},{name:"Government",weight:15},{name:"Energy",weight:10},{name:"Technology",weight:10},{name:"Manufacturing",weight:8},{name:"Retail",weight:7},{name:"Education",weight:5}],P=u=>{const s=u.reduce((a,t)=>a+t.weight,0);let i=Math.random()*s;for(const a of u)if(i-=a.weight,i<=0)return a;return u[0]},$=()=>(Math.random()-.5)*2;class L{constructor(){this.attacks=[],this.statistics={totalAttacks:0,attacksByType:{},attacksBySeverity:{},attacksBySource:{},attacksByTarget:{},attacksByIndustry:{}},this.listeners=[]}initialize(){return this.generateAttacks(30),this.updateStatistics(),this}generateAttacks(s=10){const i=[];for(let a=0;a<s;a++){const t=P(we),l=P(ke);if(t.name===l.name)continue;const r=P(Se),n=P(Te),c=P(Ce),d={id:`attack-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,startLat:t.lat+$()*.5,startLng:t.lng+$()*.5,endLat:l.lat+$()*.5,endLng:l.lng+$()*.5,color:n.color,type:r.name,typeColor:r.color,source:t.name,target:l.name,severity:n.level,industry:c.name,timestamp:new Date().toISOString(),active:!0};i.push(d)}return this.attacks=[...this.attacks,...i],this.statistics.totalAttacks+=i.length,this.notifyListeners(),i}updateStatistics(){return this.statistics.attacksByType={},this.statistics.attacksBySeverity={},this.statistics.attacksBySource={},this.statistics.attacksByTarget={},this.statistics.attacksByIndustry={},this.attacks.forEach(s=>{this.statistics.attacksByType[s.type]=(this.statistics.attacksByType[s.type]||0)+1,this.statistics.attacksBySeverity[s.severity]=(this.statistics.attacksBySeverity[s.severity]||0)+1,this.statistics.attacksBySource[s.source]=(this.statistics.attacksBySource[s.source]||0)+1,this.statistics.attacksByTarget[s.target]=(this.statistics.attacksByTarget[s.target]||0)+1,this.statistics.attacksByIndustry[s.industry]=(this.statistics.attacksByIndustry[s.industry]||0)+1}),this.statistics}getActiveAttacks(){return this.attacks.filter(s=>s.active)}getStatistics(){return this.statistics}startRealTimeUpdates(s=5e3){return this.updateInterval&&clearInterval(this.updateInterval),this.updateInterval=setInterval(()=>{this.attacks=this.attacks.filter((i,a)=>a>=Math.floor(Math.random()*5)||a>=this.attacks.length-30),this.generateAttacks(Math.floor(Math.random()*5)+3),this.updateStatistics(),this.notifyListeners()},s),this}stopRealTimeUpdates(){return this.updateInterval&&(clearInterval(this.updateInterval),this.updateInterval=null),this}addListener(s){return typeof s=="function"&&this.listeners.push(s),this}removeListener(s){return this.listeners=this.listeners.filter(i=>i!==s),this}notifyListeners(){this.listeners.forEach(s=>{try{s({attacks:this.getActiveAttacks(),statistics:this.getStatistics()})}catch(i){console.error("Error in threat intelligence listener:",i)}})}static getInstance(){return L.instance||(L.instance=new L),L.instance}}const R={Ransomware:{color:"#ef4444",icon:S},DDoS:{color:"#f97316",icon:le},Malware:{color:"#8b5cf6",icon:re},Phishing:{color:"#3b82f6",icon:he},Infrastructure:{color:"#10b981",icon:ie}},M=[{name:"United States",code:"US",region:"North America",risk:"High"},{name:"Russia",code:"RU",region:"Europe",risk:"Critical"},{name:"China",code:"CN",region:"Asia",risk:"Critical"},{name:"North Korea",code:"KP",region:"Asia",risk:"Critical"},{name:"Iran",code:"IR",region:"Middle East",risk:"High"},{name:"United Kingdom",code:"GB",region:"Europe",risk:"Medium"},{name:"Germany",code:"DE",region:"Europe",risk:"Medium"},{name:"Ukraine",code:"UA",region:"Europe",risk:"High"},{name:"Japan",code:"JP",region:"Asia",risk:"Medium"},{name:"India",code:"IN",region:"Asia",risk:"Medium"},{name:"Brazil",code:"BR",region:"South America",risk:"Medium"},{name:"Australia",code:"AU",region:"Oceania",risk:"Low"},{name:"Canada",code:"CA",region:"North America",risk:"Medium"},{name:"France",code:"FR",region:"Europe",risk:"Medium"},{name:"Italy",code:"IT",region:"Europe",risk:"Medium"},{name:"Spain",code:"ES",region:"Europe",risk:"Low"},{name:"South Korea",code:"KR",region:"Asia",risk:"Medium"},{name:"Mexico",code:"MX",region:"North America",risk:"Medium"},{name:"Indonesia",code:"ID",region:"Asia",risk:"Low"},{name:"Turkey",code:"TR",region:"Middle East",risk:"Medium"},{name:"Saudi Arabia",code:"SA",region:"Middle East",risk:"Medium"},{name:"South Africa",code:"ZA",region:"Africa",risk:"Low"},{name:"Egypt",code:"EG",region:"Africa",risk:"Medium"},{name:"Pakistan",code:"PK",region:"Asia",risk:"High"},{name:"Bangladesh",code:"BD",region:"Asia",risk:"Low"}],O={"North America":"#0077b6","South America":"#00b4d8",Europe:"#90e0ef",Asia:"#0096c7",Africa:"#48cae4","Middle East":"#ade8f4",Oceania:"#caf0f8"},C={Critical:"#ef4444",High:"#f97316",Medium:"#facc15",Low:"#4ade80"},G=(u,s=2e3,i=0)=>{const[a,t]=y.useState(i);return y.useEffect(()=>{let l=null;const r=n=>{l||(l=n);const c=Math.min((n-l)/s,1);t(Math.floor(c*(u-i)+i)),c<1&&window.requestAnimationFrame(r)};window.requestAnimationFrame(r)},[u,s,i]),a},Ae=({data:u})=>{const{darkMode:s}=D(),i=Object.values(u).reduce((t,l)=>t+l,0),a=Object.entries(u).sort(([,t],[,l])=>l-t);return e.jsxs("div",{className:"space-y-4",children:[a.map(([t,l])=>{const r=Math.round(l/i*100);return e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:O[t]||"#cbd5e1"}}),e.jsx("span",{className:"font-medium",children:t})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsxs("span",{className:"text-lg font-bold mr-1",children:[r,"%"]}),e.jsxs("span",{className:"text-sm text-gray-400",children:["(",l," attacks)"]})]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2.5 overflow-hidden shadow-inner",children:e.jsx("div",{className:"h-full rounded-full transition-all duration-500 ease-out",style:{width:`${r}%`,backgroundColor:O[t]||"#cbd5e1",boxShadow:`0 0 8px ${O[t]||"#cbd5e1"}`}})})]},t)}),e.jsx("div",{className:"mt-6 grid grid-cols-2 gap-3",children:a.slice(0,2).map(([t,l])=>{const r=Math.round(l/i*100);return e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg text-center",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Highest Threat Region"}),e.jsx("div",{className:"text-xl font-bold",children:t}),e.jsxs("div",{className:"text-sm font-medium",style:{color:O[t]||"#cbd5e1"},children:[r,"% of all attacks"]})]},`stat-${t}`)})})]})},Ie=({data:u})=>{const{darkMode:s}=D(),i=y.useRef(null);return y.useEffect(()=>{if(!i.current||!u)return;const a=i.current,t=a.getContext("2d"),l=500,r=500;a.width=l,a.height=r,t.clearRect(0,0,l,r);const n=Math.min(l,r)/2.5,c=l/2,d=r/2,v=Object.values(u).reduce((x,j)=>x+j,0);let o=-Math.PI/2;Object.entries(u).forEach(([x,j])=>{var b;const m=j/v*2*Math.PI,p=o+m,N=((b=R[x])==null?void 0:b.color)||"#cbd5e1";t.fillStyle=N,t.beginPath(),t.moveTo(c,d),t.arc(c,d,n,o,p),t.closePath(),t.fill(),t.strokeStyle=s?"#1f2937":"#ffffff",t.lineWidth=1,t.beginPath(),t.moveTo(c,d),t.arc(c,d,n,o,p),t.closePath(),t.stroke();const g=Math.round(j/v*100),f=o+m/2,A=n*.7,B=c+Math.cos(f)*A,h=d+Math.sin(f)*A;if(m>.1){const w=`${g}%`;t.fillStyle="rgba(0, 0, 0, 0.7)",t.beginPath(),t.arc(B,h,15,0,2*Math.PI),t.fill(),t.fillStyle="#ffffff",t.font="bold 14px Arial",t.textAlign="center",t.textBaseline="middle",t.fillText(w,B,h)}o=p}),t.fillStyle=s?"#1f2937":"#f1f5f9",t.beginPath(),t.arc(c,d,n*.3,0,2*Math.PI),t.fill(),t.strokeStyle=s?"#374151":"#e2e8f0",t.lineWidth=2,t.beginPath(),t.arc(c,d,n*.3,0,2*Math.PI),t.stroke()},[u,s]),e.jsxs("div",{className:"relative w-full h-full",children:[e.jsx("canvas",{ref:i,width:"500",height:"500",className:"w-full h-full",style:{display:"block"}}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center pointer-events-none",children:e.jsxs("div",{className:"text-center bg-gray-900 bg-opacity-90 rounded-full p-6 shadow-xl border-2 border-gray-700",children:[e.jsx("div",{className:"text-sm text-gray-300 uppercase tracking-wider font-semibold",children:"TOTAL"}),e.jsx("div",{className:"text-4xl font-bold",children:Object.values(u).reduce((a,t)=>a+t,0)})]})})]})},Me=({threats:u})=>e.jsx("div",{className:"space-y-2 max-h-[300px] overflow-y-auto pr-2",children:u.map((s,i)=>{var t,l,r;const a=((t=R[s.type])==null?void 0:t.icon)||S;return e.jsxs("div",{className:"bg-gray-800 bg-opacity-50 p-2 rounded border border-gray-700 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3",style:{backgroundColor:`${(l=R[s.type])==null?void 0:l.color}20`},children:e.jsx(a,{style:{color:(r=R[s.type])==null?void 0:r.color}})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("div",{className:"font-medium",children:s.type}),e.jsx("div",{className:"text-xs text-gray-400",children:s.time})]}),e.jsxs("div",{className:"text-sm text-gray-400 flex items-center",children:[e.jsx("span",{children:s.source}),e.jsx("span",{className:"mx-2",children:"→"}),e.jsx("span",{children:s.target})]})]})]},i)})}),Ee=({countries:u})=>{const s=u.reduce((t,l)=>(t[l.risk]||(t[l.risk]=[]),t[l.risk].push(l),t),{}),i={Critical:0,High:1,Medium:2,Low:3},a={Critical:"Immediate action required",High:"Significant threat activity",Medium:"Moderate threat activity",Low:"Minimal threat activity"};return e.jsx("div",{className:"space-y-6",children:Object.entries(s).sort(([t],[l])=>i[t]-i[l]).map(([t,l])=>e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-5 h-5 rounded-full mr-3 flex items-center justify-center",style:{backgroundColor:C[t]},children:e.jsx("div",{className:"w-2 h-2 rounded-full bg-white"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-bold text-lg",children:[t," Risk"]}),e.jsx("p",{className:"text-sm text-gray-400",children:a[t]})]}),e.jsxs("div",{className:"ml-auto bg-gray-700 px-3 py-1 rounded-full text-sm font-medium",children:[l.length," countries"]})]}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:l.map(r=>e.jsxs("div",{className:"px-3 py-2 rounded-lg flex items-center",style:{backgroundColor:`${C[t]}15`,border:`1px solid ${C[t]}40`},children:[e.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:C[t]}}),e.jsx("span",{className:"font-medium",style:{color:C[t]},children:r.name})]},r.code))}),t==="Critical"&&e.jsxs("div",{className:"p-3 rounded-lg text-sm",style:{backgroundColor:`${C[t]}15`,border:`1px solid ${C[t]}40`},children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(S,{className:"mr-2",style:{color:C[t]}}),e.jsx("span",{className:"font-bold",style:{color:C[t]},children:"Critical Alert"})]}),e.jsx("p",{className:"text-gray-300",children:"These countries are currently experiencing significant cyber attack activity and require immediate attention."})]})]},t))})},De=()=>{const{darkMode:u}=D(),[s,i]=y.useState({totalAttacks:0,blockedAttacks:0,criticalVulnerabilities:0,securityScore:0,attacksByType:{},attacksBySource:{},attacksBySeverity:{},attacksByRegion:{},recentThreats:[]}),a=G(s.totalAttacks),t=G(s.blockedAttacks),l=G(s.securityScore);return y.useEffect(()=>{const r=L.getInstance().initialize(),n=r.getStatistics(),c={};Object.entries(n.attacksBySource||{}).forEach(([o,x])=>{const j=M.find(m=>m.name===o);if(j){const m=j.region;c[m]||(c[m]=0),c[m]+=x}});const d=[];for(let o=0;o<10;o++){const x=Math.floor(Math.random()*M.length);let j=Math.floor(Math.random()*M.length);for(;j===x;)j=Math.floor(Math.random()*M.length);const m=["Ransomware","DDoS","Malware","Phishing","Infrastructure"],p=Math.floor(Math.random()*m.length),N=Math.floor(Math.random()*24),g=Math.floor(Math.random()*60),f=`${N.toString().padStart(2,"0")}:${g.toString().padStart(2,"0")}`;d.push({source:M[x].name,target:M[j].name,type:m[p],time:f})}const v={"00:00":Math.floor(Math.random()*50)+20,"04:00":Math.floor(Math.random()*50)+20,"08:00":Math.floor(Math.random()*50)+20,"12:00":Math.floor(Math.random()*50)+20,"16:00":Math.floor(Math.random()*50)+20,"20:00":Math.floor(Math.random()*50)+20,"24:00":Math.floor(Math.random()*50)+20};return i({totalAttacks:n.totalAttacks||32320,blockedAttacks:Math.floor((n.totalAttacks||32320)*.76),criticalVulnerabilities:Math.floor(Math.random()*5)+1,securityScore:78,attacksByType:n.attacksByType||{Ransomware:35,DDoS:25,Malware:20,Phishing:15,Infrastructure:5},attacksBySource:n.attacksBySource||{},attacksBySeverity:n.attacksBySeverity||{},attacksByRegion:c||{"North America":25,Europe:30,Asia:35,"Middle East":5,"South America":3,Africa:1,Oceania:1},recentThreats:d,threatActivity:v}),r.addListener(o=>{i(x=>({...x,totalAttacks:o.statistics.totalAttacks||32320,blockedAttacks:Math.floor((o.statistics.totalAttacks||32320)*.76),attacksByType:o.statistics.attacksByType||x.attacksByType,attacksBySource:o.statistics.attacksBySource||x.attacksBySource,attacksBySeverity:o.statistics.attacksBySeverity||x.attacksBySeverity}))}),r.startRealTimeUpdates(),()=>{r.removeListener(i),r.stopRealTimeUpdates()}},[]),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-blue-500/20 flex items-center justify-center mr-5",children:e.jsx(Q,{className:"text-blue-500 text-2xl"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-400 uppercase tracking-wider",children:"Total Attacks"}),e.jsx("div",{className:"text-3xl font-bold",children:a.toLocaleString()})]})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-green-500/20 flex items-center justify-center mr-5",children:e.jsx(de,{className:"text-green-500 text-2xl"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-400 uppercase tracking-wider",children:"Blocked Attacks"}),e.jsx("div",{className:"text-3xl font-bold",children:t.toLocaleString()})]})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-red-500/20 flex items-center justify-center mr-5",children:e.jsx(S,{className:"text-red-500 text-2xl"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-400 uppercase tracking-wider",children:"Critical Vulnerabilities"}),e.jsx("div",{className:"text-3xl font-bold",children:s.criticalVulnerabilities})]})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-yellow-500/20 flex items-center justify-center mr-5",children:e.jsx(ne,{className:"text-yellow-500 text-2xl"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-400 uppercase tracking-wider",children:"Security Score"}),e.jsxs("div",{className:"text-3xl font-bold",children:[l,"%"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center mr-3",children:e.jsx(me,{className:"text-blue-500"})}),"Live Threat Feed"]}),e.jsx("div",{className:"h-[350px]",children:e.jsx(je,{})})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center mr-3",children:e.jsx(S,{className:"text-purple-500"})}),"Attack Type Distribution"]}),e.jsx("div",{className:"h-[550px] w-full flex items-center justify-center",children:e.jsx("div",{className:"w-[500px] h-[500px]",children:e.jsx(Ie,{data:s.attacksByType})})}),e.jsx("div",{className:"mt-6 grid grid-cols-2 md:grid-cols-3 gap-3",children:Object.entries(s.attacksByType||{}).map(([r,n])=>{var c;return e.jsxs("div",{className:"flex items-center bg-gray-700 p-2 rounded-lg",children:[e.jsx("div",{className:"w-4 h-4 rounded-full mr-2",style:{backgroundColor:((c=R[r])==null?void 0:c.color)||"#cbd5e1"}}),e.jsx("span",{className:"text-sm font-medium",children:r})]},r)})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-3",children:e.jsx(ie,{className:"text-green-500"})}),"Regional Threat Distribution"]}),e.jsx(Ae,{data:s.attacksByRegion})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center mr-3",children:e.jsx(S,{className:"text-red-500"})}),"Country Risk Matrix"]}),e.jsx(Ee,{countries:M})]})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center mr-3",children:e.jsx(le,{className:"text-yellow-500"})}),"Live Threat Feed"]}),e.jsx(Me,{threats:s.recentThreats})]})]})},_={abuseIPDB:{baseUrl:"https://api.abuseipdb.com/api/v2",apiKey:"********************************************************************************"},otx:{baseUrl:"https://otx.alienvault.com/api/v1",apiKey:"****************************************************************"}},ee=oe.create({baseURL:_.abuseIPDB.baseUrl,headers:{Key:_.abuseIPDB.apiKey,Accept:"application/json"}}),F=oe.create({baseURL:_.otx.baseUrl,headers:{"X-OTX-API-KEY":_.otx.apiKey,"Content-Type":"application/json"}}),T={data:{},timestamp:{},maxAge:15*60*1e3,get(u){const s=Date.now();return this.data[u]&&s-this.timestamp[u]<this.maxAge?this.data[u]:null},set(u,s){this.data[u]=s,this.timestamp[u]=Date.now()}};class Le{async getBlacklistedIPs(s=90,i=25){const a=`blacklist_${s}_${i}`,t=T.get(a);if(t)return t;try{const r=(await ee.get("/blacklist",{params:{confidenceMinimum:s,limit:i}})).data.data;return T.set(a,r),r}catch(l){throw console.error("Error fetching blacklisted IPs:",l),l}}async checkIP(s){const i=`ip_${s}`,a=T.get(i);if(a)return a;try{const l=(await ee.get("/check",{params:{ipAddress:s,maxAgeInDays:90,verbose:!0}})).data.data;return T.set(i,l),l}catch(t){throw console.error(`Error checking IP ${s}:`,t),t}}async getPulses(s=10){const i=`pulses_${s}`,a=T.get(i);if(a)return a;try{const l=(await F.get(`/pulses/subscribed?limit=${s}`)).data.results;return T.set(i,l),l}catch(t){throw console.error("Error fetching OTX pulses:",t),t}}async getPulseIndicators(s){const i=`pulse_indicators_${s}`,a=T.get(i);if(a)return a;try{const l=(await F.get(`/pulses/${s}/indicators`)).data;return T.set(i,l),l}catch(t){throw console.error(`Error fetching indicators for pulse ${s}:`,t),t}}async getDomainInfo(s){const i=`domain_${s}`,a=T.get(i);if(a)return a;try{const l=(await F.get(`/indicators/domain/${s}/general`)).data;return T.set(i,l),l}catch(t){throw console.error(`Error fetching domain info for ${s}:`,t),t}}async searchIndicators(s){const i=`search_${s}`,a=T.get(i);if(a)return a;try{const l=(await F.get(`/search/pulses?q=${encodeURIComponent(s)}`)).data.results;return T.set(i,l),l}catch(t){throw console.error(`Error searching indicators for ${s}:`,t),t}}async getThreatOfTheWeek(){try{const s=await this.getPulses(20);if(!s||s.length===0)throw new Error("No pulses found");const i=s.filter(o=>o.indicator_count>3).sort((o,x)=>{var p,N;const j=(o.indicator_count||0)+(((p=o.references)==null?void 0:p.length)||0)*2;return(x.indicator_count||0)+(((N=x.references)==null?void 0:N.length)||0)*2-j});if(i.length===0)throw new Error("No significant pulses found");const a=i[0],l=(await this.getPulseIndicators(a.id)).reduce((o,x)=>(o[x.type]||(o[x.type]=[]),o[x.type].push(x),o),{}),r=a.tags.filter(o=>o.toLowerCase().includes("malware")||o.toLowerCase().includes("ransomware")||o.toLowerCase().includes("exploit")||o.toLowerCase().includes("phishing")||o.toLowerCase().includes("backdoor")||o.toLowerCase().includes("trojan")),n=[{date:new Date(a.created).toLocaleDateString(),event:"Initial discovery"}];a.modified!==a.created&&n.push({date:new Date(a.modified).toLocaleDateString(),event:"Latest update with new indicators"}),a.references&&a.references.length>0&&n.push({date:new Date(a.created).toLocaleDateString(),event:`Referenced in ${a.references.length} external sources`});const c=[];(l.IPv4||l.IPv6)&&c.push("Monitor for connections to malicious IP addresses"),(l.domain||l.hostname)&&c.push("Implement DNS monitoring for suspicious domains"),(l["FileHash-MD5"]||l["FileHash-SHA1"]||l["FileHash-SHA256"])&&c.push("Deploy hash-based detection for malicious files"),l.URL&&c.push("Filter web traffic for malicious URLs");const d=[];return r.some(o=>o.toLowerCase().includes("ransomware"))&&(d.push("Implement regular backup procedures and test recovery processes"),d.push("Deploy endpoint protection with anti-ransomware capabilities")),r.some(o=>o.toLowerCase().includes("phishing"))&&(d.push("Conduct regular phishing awareness training"),d.push("Implement email filtering solutions")),r.some(o=>o.toLowerCase().includes("exploit"))&&(d.push("Establish a robust patch management process"),d.push("Conduct regular vulnerability scanning")),{title:a.name,summary:a.description,technicalDetails:{attackVectors:r.length>0?r:["Unknown attack vector"],indicators:Object.entries(l).flatMap(([o,x])=>x.slice(0,5).map(j=>({type:o,value:j.indicator}))).slice(0,10),techniques:a.tags.filter(o=>o.match(/^T\d{4}(\.\d{3})?$/)).map(o=>{const x={T1566:{name:"Phishing",description:"Adversaries may send phishing messages to gain access to victim systems."},"T1566.001":{name:"Phishing: Spearphishing Attachment",description:"Adversaries may send spearphishing emails with a malicious attachment in an attempt to gain access to victim systems."},"T1566.002":{name:"Phishing: Spearphishing Link",description:"Adversaries may send spearphishing emails with a malicious link in an attempt to gain access to victim systems."},T1027:{name:"Obfuscated Files or Information",description:"Adversaries may attempt to make an executable or file difficult to discover or analyze by encrypting, encoding, or otherwise obfuscating its contents on the system."},"T1027.002":{name:"Software Packing",description:"Adversaries may use software packing to conceal their code."},"T1027.005":{name:"Indicator Removal from Tools",description:"Adversaries may remove indicators from tools if they believe their malicious tool was detected."},T1059:{name:"Command and Scripting Interpreter",description:"Adversaries may abuse command and script interpreters to execute commands, scripts, or binaries."},"T1059.001":{name:"PowerShell",description:"Adversaries may abuse PowerShell commands and scripts for execution."},"T1059.003":{name:"Windows Command Shell",description:"Adversaries may abuse the Windows command shell for execution."},T1204:{name:"User Execution",description:"Adversaries may rely upon specific actions by a user in order to gain execution."},"T1204.001":{name:"Malicious Link",description:"Adversaries may rely upon a user clicking a malicious link in order to gain execution."},"T1204.002":{name:"Malicious File",description:"Adversaries may rely upon a user opening a malicious file in order to gain execution."}},j=o.split(".")[0],m=x[o]||x[j]||{name:`Unknown Technique (${o})`,description:"No description available for this technique."};return{id:o,name:m.name,description:m.description}})},timeline:n,detectionMethods:c.length>0?c:["No specific detection methods available"],mitigationSteps:d.length>0?d:["No specific mitigation steps available"],learningObjectives:[`Understand ${a.name} attack techniques`,"Identify indicators of compromise associated with this threat","Learn effective detection strategies","Practice incident response for this type of attack"],challengeScenario:{description:`You are a security analyst who has detected potential indicators related to ${a.name}. Your task is to analyze the provided evidence, confirm the infection, and recommend containment steps.`,evidenceFiles:["network_capture.pcap","suspicious_process_logs.txt","email_headers.txt"],questions:["Identify the initial infection vector based on the provided evidence","Determine which systems have been compromised","Recommend immediate containment actions","Create a timeline of the attack based on log evidence"]}}}catch(s){throw console.error("Error generating threat of the week:",s),s}}async getHuntingChallenges(){try{const s=await this.getPulses(30);if(!s||s.length===0)throw new Error("No pulses found");const i=await this.getBlacklistedIPs(80,20),a=[],t=s.filter(n=>n.tags.some(c=>c.toLowerCase().includes("ransomware"))),l=s.filter(n=>n.tags.some(c=>c.toLowerCase().includes("apt"))),r=s.filter(n=>n.tags.some(c=>c.toLowerCase().includes("malware"))&&!n.tags.some(c=>c.toLowerCase().includes("ransomware")));if(t.length>0){const n=t[0];a.push({id:`hunt-ransomware-${Date.now()}`,title:`${n.name} Ransomware Detection`,difficulty:"Beginner",category:"Endpoint",description:`Learn to identify ${n.name} ransomware activity by analyzing endpoint logs and system behavior.`,objectives:["Identify suspicious process execution patterns","Detect file encryption activities","Recognize ransomware persistence mechanisms"],points:100,estimatedTime:"30 minutes",prerequisites:[],unlocked:!0,realData:{pulseId:n.id,tags:n.tags,references:n.references}})}if(i.length>0&&a.push({id:`hunt-c2-${Date.now()}`,title:"C2 Communication Hunt",difficulty:"Intermediate",category:"Network",description:"Hunt for command and control (C2) communications by analyzing network traffic patterns.",objectives:["Identify beaconing patterns in network traffic","Detect domain generation algorithms (DGA)","Recognize encrypted C2 channels"],points:200,estimatedTime:"45 minutes",prerequisites:a.length>0?[a[0].id]:[],unlocked:!0,realData:{ipAddresses:i.slice(0,5).map(n=>n.ipAddress),countries:[...new Set(i.slice(0,5).map(n=>n.countryCode))],confidenceScores:i.slice(0,5).map(n=>n.abuseConfidenceScore)}}),l.length>0){const n=l[0];a.push({id:`hunt-apt-${Date.now()}`,title:`${n.name} APT Hunt`,difficulty:"Advanced",category:"Multi-source",description:`Learn to track sophisticated ${n.name} APT activities across multiple data sources.`,objectives:["Correlate indicators across different log sources","Identify lateral movement techniques","Detect data exfiltration attempts"],points:350,estimatedTime:"60 minutes",prerequisites:a.length>0?[a[0].id]:[],unlocked:!1,realData:{pulseId:n.id,tags:n.tags,references:n.references}})}if(r.length>0){const n=r[0];a.push({id:`hunt-malware-${Date.now()}`,title:`${n.name} Analysis`,difficulty:"Intermediate",category:"Malware",description:`Analyze ${n.name} malware behavior and identify its capabilities.`,objectives:["Identify malware infection vectors","Analyze malware behavior and capabilities","Develop detection strategies"],points:250,estimatedTime:"50 minutes",prerequisites:a.length>0?[a[0].id]:[],unlocked:!1,realData:{pulseId:n.id,tags:n.tags,references:n.references}})}return a}catch(s){throw console.error("Error generating hunting challenges:",s),s}}async getAIAssistantData(){try{const s=await this.getPulses(10);if(!s||s.length===0)throw new Error("No pulses found");const i=[];if(s.length>0){const n=s[0];i.push(`What is ${n.name}?`)}const a=s.flatMap(n=>n.tags.filter(c=>c.match(/^T\d{4}(\.\d{3})?$/)));if(a.length>0){const n=[...new Set(a)];n.length>0&&i.push(`Explain the ${n[0]} technique`)}i.push("What is a command and control (C2) server?","Explain the MITRE ATT&CK framework","How do I detect lateral movement in a network?","What are common indicators of a ransomware attack?","Explain the difference between signature and behavior-based detection");const t=[],l=s.flatMap(n=>n.tags),r=[...new Set(l)];return r.some(n=>n.toLowerCase().includes("ransomware"))&&t.push({title:"Ransomware Defense",description:"Strategies for preventing and responding to ransomware attacks",module:"ransomware-defense"}),r.some(n=>n.toLowerCase().includes("apt"))&&t.push({title:"APT Detection Techniques",description:"Methods for identifying advanced persistent threats",module:"apt-detection"}),r.some(n=>n.toLowerCase().includes("phishing"))&&t.push({title:"Phishing Analysis",description:"Techniques for analyzing and detecting phishing attempts",module:"phishing-analysis"}),t.push({title:"MITRE ATT&CK Framework",description:"Comprehensive threat model and knowledge base",module:"mitre-attack"},{title:"Threat Hunting Fundamentals",description:"Learn proactive threat detection techniques",module:"threat-hunting"}),{suggestedQuestions:i.slice(0,5),relatedLearning:t.slice(0,3),recentThreats:s.slice(0,3).map(n=>({name:n.name,description:n.description,created:n.created,tags:n.tags}))}}catch(s){throw console.error("Error generating AI assistant data:",s),s}}}const J=new Le,Pe={title:"Emotet Banking Trojan Resurgence",summary:"Emotet has reemerged with enhanced evasion techniques and is targeting financial institutions worldwide. This modular banking trojan now uses advanced polymorphic code to evade detection and has added new modules for credential theft and lateral movement.",technicalDetails:{attackVectors:["Phishing emails with malicious Office documents","Malicious JavaScript downloaders","Exploitation of SMB vulnerabilities for lateral movement"],indicators:[{type:"File Hash (SHA-256)",value:"a2d31d36f5d68a85a7a7d35f6c8964c9a6061c1f46e9a1fcc6586edc49f28920"},{type:"Domain",value:"cdn-storage-service-secure.com"},{type:"IP Address",value:"**************"},{type:"User-Agent",value:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 Edg/92.0.902.55"}],techniques:[{id:"T1566.001",name:"Phishing: Spearphishing Attachment",description:"Emotet is primarily distributed through phishing emails containing malicious Word or Excel documents with macros."},{id:"T1027",name:"Obfuscated Files or Information",description:"Uses heavily obfuscated code and encrypted payloads to evade detection."},{id:"T1210",name:"Exploitation of Remote Services",description:"Leverages EternalBlue and other SMB exploits for lateral movement within networks."}]},timeline:[{date:"2014",event:"Initial discovery as a banking trojan"},{date:"2020 January",event:"Takedown by international law enforcement"},{date:"2021 November",event:"Reemergence with new infrastructure"},{date:"2023 March",event:"Addition of new evasion techniques"},{date:"2023 October",event:"Current campaign targeting financial sector"}],detectionMethods:["Monitor for suspicious PowerShell commands with encoded parameters","Implement email filtering for documents with macros","Deploy network monitoring for unusual SMB traffic","Use memory-based detection for Emotet's in-memory operations"],mitigationSteps:["Disable Office macros in documents from external sources","Implement application allowlisting to prevent unauthorized code execution","Segment networks to limit lateral movement capabilities","Deploy advanced endpoint protection with behavioral detection"],learningObjectives:["Understand polymorphic malware evasion techniques","Identify Emotet's infection chain and persistence mechanisms","Learn effective detection strategies for banking trojans","Practice incident response for Emotet infections"],challengeScenario:{description:"You are a security analyst at a financial institution that has detected potential Emotet indicators. Your task is to analyze the provided evidence, confirm the infection, and recommend containment steps.",evidenceFiles:["network_capture.pcap","suspicious_process_logs.txt","email_headers.txt"],questions:["Identify the initial infection vector based on the provided evidence","Determine which systems have been compromised","Recommend immediate containment actions","Create a timeline of the attack based on log evidence"]}},Re=()=>{const{darkMode:u}=D(),[s,i]=y.useState("overview"),[a,t]=y.useState([]),[l,r]=y.useState(!1),[n,c]=y.useState(!0),[d,v]=y.useState(null),[o,x]=y.useState(null);y.useEffect(()=>{(async()=>{try{c(!0),x(null);const N=await J.getThreatOfTheWeek();v(N)}catch(N){console.error("Error fetching threat of the week:",N),x("Failed to load threat data. Using fallback data."),v(Pe)}finally{c(!1)}})()},[]);const j=p=>{if(!a.includes(p)){const N=[...a,p];t(N),["overview","technical","detection","challenge"].every(f=>N.includes(f))&&r(!0)}},m=()=>{var p,N;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center mr-3",children:e.jsx(S,{className:"text-red-500 text-xl"})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-xl font-bold",children:["Threat of the Week: ",d.title]}),e.jsxs("p",{className:"text-gray-400",children:["Updated ",new Date().toLocaleDateString()]})]})]}),e.jsx("p",{className:"text-gray-300 mt-3",children:d.summary})]}),e.jsxs("div",{className:"mb-6 bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Your Learning Progress"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:["overview","technical","detection","challenge"].map(g=>e.jsxs("div",{className:`px-3 py-1 rounded-full text-sm flex items-center ${a.includes(g)?"bg-green-900 text-green-200":"bg-gray-600 text-gray-300"}`,children:[a.includes(g)&&e.jsx(ce,{className:"mr-1"}),g.charAt(0).toUpperCase()+g.slice(1)]},g))}),e.jsxs("div",{className:"mt-3",children:[e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:`${a.length/4*100}%`}})}),e.jsxs("div",{className:"text-right text-sm text-gray-400 mt-1",children:[a.length,"/4 completed"]})]})]}),e.jsxs("div",{className:"flex border-b border-gray-700 mb-6",children:[e.jsxs("button",{className:`flex items-center px-4 py-2 ${s==="overview"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>i("overview"),children:[e.jsx(ue,{className:"mr-2"}),"Overview"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${s==="technical"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>i("technical"),children:[e.jsx(V,{className:"mr-2"}),"Technical Details"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${s==="detection"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>i("detection"),children:[e.jsx(z,{className:"mr-2"}),"Detection & Mitigation"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${s==="challenge"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>i("challenge"),children:[e.jsx(S,{className:"mr-2"}),"Challenge"]})]}),e.jsxs("div",{className:"mb-6",children:[s==="overview"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Threat Overview"}),e.jsx("p",{className:"text-gray-300 mb-4",children:d.summary}),e.jsx("h4",{className:"font-semibold mb-2 mt-6",children:"Historical Timeline"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute left-3 top-0 bottom-0 w-0.5 bg-gray-600"}),e.jsx("div",{className:"space-y-4 ml-10",children:d.timeline.map((g,f)=>e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute -left-10 mt-1 w-6 h-6 rounded-full bg-blue-900 border-2 border-blue-500 flex items-center justify-center",children:e.jsx("div",{className:"w-2 h-2 rounded-full bg-blue-300"})}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded",children:[e.jsx("div",{className:"font-medium",children:g.date}),e.jsx("div",{className:"text-sm text-gray-300",children:g.event})]})]},f))})]}),e.jsx("h4",{className:"font-semibold mb-2 mt-6",children:"Learning Objectives"}),e.jsx("ul",{className:"list-disc list-inside space-y-2 text-gray-300",children:d.learningObjectives.map((g,f)=>e.jsx("li",{children:g},f))}),e.jsx("div",{className:"mt-6 flex justify-end",children:e.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center",onClick:()=>{j("overview"),i("technical")},children:["Continue to Technical Details ",e.jsx(H,{className:"ml-2"})]})})]}),s==="technical"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Technical Analysis"}),e.jsx("h4",{className:"font-semibold mb-2",children:"Attack Vectors"}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-gray-300 mb-4",children:d.technicalDetails.attackVectors.map((g,f)=>e.jsx("li",{children:g},f))}),e.jsx("h4",{className:"font-semibold mb-2 mt-6",children:"MITRE ATT&CK Techniques"}),e.jsx("div",{className:"space-y-4 mb-6",children:d.technicalDetails.techniques.map((g,f)=>e.jsxs("div",{className:"bg-gray-700 p-3 rounded",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("div",{className:"font-medium",children:g.name}),e.jsx("div",{className:"text-sm bg-blue-900 text-blue-200 px-2 py-0.5 rounded",children:g.id})]}),e.jsx("div",{className:"text-sm text-gray-300 mt-1",children:g.description})]},f))}),e.jsx("h4",{className:"font-semibold mb-2",children:"Indicators of Compromise (IoCs)"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"bg-gray-700",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left",children:"Type"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Value"})]})}),e.jsx("tbody",{children:d.technicalDetails.indicators.map((g,f)=>e.jsxs("tr",{className:f%2===0?"bg-gray-700 bg-opacity-50":"",children:[e.jsx("td",{className:"px-4 py-2",children:g.type}),e.jsx("td",{className:"px-4 py-2 font-mono",children:g.value})]},f))})]})}),e.jsxs("div",{className:"mt-6 flex justify-between",children:[e.jsx("button",{className:"bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded flex items-center",onClick:()=>i("overview"),children:"Back to Overview"}),e.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center",onClick:()=>{j("technical"),i("detection")},children:["Continue to Detection & Mitigation ",e.jsx(H,{className:"ml-2"})]})]})]}),s==="detection"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Detection & Mitigation Strategies"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsxs("h4",{className:"font-semibold mb-3 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-900 flex items-center justify-center mr-2",children:e.jsx(E,{className:"text-blue-300"})}),"Detection Methods"]}),e.jsx("ul",{className:"list-disc list-inside space-y-2 text-gray-300",children:d.detectionMethods.map((g,f)=>e.jsx("li",{children:g},f))})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsxs("h4",{className:"font-semibold mb-3 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-green-900 flex items-center justify-center mr-2",children:e.jsx(Q,{className:"text-green-300"})}),"Mitigation Steps"]}),e.jsx("ul",{className:"list-disc list-inside space-y-2 text-gray-300",children:d.mitigationSteps.map((g,f)=>e.jsx("li",{children:g},f))})]})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg mb-6",children:[e.jsx("h4",{className:"font-semibold mb-3",children:"YARA Rule Example"}),e.jsx("pre",{className:"bg-gray-800 p-3 rounded font-mono text-sm overflow-x-auto",children:`rule ${d.title.replace(/\s+/g,"_")} {
    meta:
        description = "Detects ${d.title}"
        author = "XCerberus Threat Intelligence"
        date = "${new Date().toISOString().split("T")[0]}"
        hash = "${((p=d.technicalDetails.indicators.find(g=>g.type.includes("Hash")))==null?void 0:p.value)||"unknown"}"
    
    strings:
        $s1 = "${((N=d.technicalDetails.indicators.find(g=>g.type.includes("domain")))==null?void 0:N.value)||d.title}" ascii wide
        
        $code1 = { 83 EC 20 53 55 56 57 8B 7C 24 34 }
        $code2 = { 68 ?? ?? ?? ?? 68 ?? ?? ?? ?? E8 ?? ?? ?? ?? }
        
    condition:
        uint16(0) == 0x5A4D and
        filesize < 2MB and
        (all of ($s*) or all of ($code*))
}`})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg mb-6",children:[e.jsx("h4",{className:"font-semibold mb-3",children:"Sigma Rule Example"}),e.jsx("pre",{className:"bg-gray-800 p-3 rounded font-mono text-sm overflow-x-auto",children:`title: ${d.title} Detection
id: ${Math.random().toString(36).substring(2,15)}
status: experimental
description: Detects ${d.title} activity
references:
    - https://xcerberus.com/threat-intelligence/${d.title.toLowerCase().replace(/\s+/g,"-")}
author: XCerberus Threat Intelligence
date: ${new Date().toISOString().split("T")[0]}
tags:
    - attack.execution
    - attack.t1204
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        CommandLine|contains:
            - 'regsvr32.exe /s /u /i:'
            - 'cmd.exe /c powershell -w hidden -ep bypass -enc'
            - 'wscript.exe //E:'
    condition: selection
falsepositives:
    - Legitimate administrative scripts
level: high`})]}),e.jsxs("div",{className:"mt-6 flex justify-between",children:[e.jsx("button",{className:"bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded flex items-center",onClick:()=>i("technical"),children:"Back to Technical Details"}),e.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center",onClick:()=>{j("detection"),i("challenge")},children:["Continue to Challenge ",e.jsx(H,{className:"ml-2"})]})]})]}),s==="challenge"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Hands-on Challenge"}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg mb-6",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Scenario"}),e.jsx("p",{className:"text-gray-300 mb-4",children:d.challengeScenario.description}),e.jsx("h4",{className:"font-semibold mb-2",children:"Available Evidence"}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:d.challengeScenario.evidenceFiles.map((g,f)=>e.jsxs("div",{className:"bg-gray-800 px-3 py-2 rounded flex items-center",children:[e.jsx(Z,{className:"mr-2 text-blue-400"}),e.jsx("span",{className:"font-mono text-sm",children:g})]},f))}),e.jsx("h4",{className:"font-semibold mb-2",children:"Challenge Questions"}),e.jsx("div",{className:"space-y-4",children:d.challengeScenario.questions.map((g,f)=>e.jsxs("div",{className:"bg-gray-800 p-3 rounded",children:[e.jsx("p",{className:"mb-2",children:g}),e.jsx("textarea",{className:"w-full bg-gray-700 border border-gray-600 rounded p-2 text-sm",rows:"2",placeholder:"Enter your answer here..."})]},f))}),e.jsx("div",{className:"mt-6",children:e.jsx("button",{className:"bg-green-600 hover:bg-green-700 px-4 py-2 rounded",onClick:()=>j("challenge"),children:"Submit Answers"})})]}),e.jsx("div",{className:"mt-6 flex justify-between",children:e.jsx("button",{className:"bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded flex items-center",onClick:()=>i("detection"),children:"Back to Detection & Mitigation"})})]})]}),l&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white text-black max-w-2xl w-full p-8 rounded-lg",children:[e.jsxs("div",{className:"border-8 border-double border-gray-300 p-6 text-center",children:[e.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Certificate of Completion"}),e.jsx("p",{className:"text-xl mb-6",children:"This certifies that"}),e.jsx("p",{className:"text-2xl font-bold mb-6",children:"Cybersecurity Student"}),e.jsx("p",{className:"text-xl mb-6",children:"has successfully completed the threat analysis module"}),e.jsx("p",{className:"text-2xl font-bold mb-8",children:d.title}),e.jsxs("div",{className:"flex justify-between items-center mt-12",children:[e.jsxs("div",{className:"text-left",children:[e.jsx("p",{className:"font-bold",children:"XCerberus Academy"}),e.jsx("p",{children:"Threat Intelligence Division"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{children:new Date().toLocaleDateString()}),e.jsxs("p",{children:["Certificate ID: TOW-",new Date().toISOString().split("T")[0]]})]})]})]}),e.jsxs("div",{className:"mt-6 flex justify-center",children:[e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded mr-4",onClick:()=>r(!1),children:"Close"}),e.jsxs("button",{className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center",children:[e.jsx(Z,{className:"mr-2"})," Download Certificate"]})]})]})})]})};return n?e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:e.jsxs("div",{className:"flex flex-col justify-center items-center h-64",children:[e.jsx(q,{className:"animate-spin text-blue-500 text-4xl mb-4"}),e.jsx("p",{className:"text-gray-300",children:"Fetching real-time threat intelligence data..."})]})}):o?e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsxs("div",{className:"bg-red-900 bg-opacity-20 border border-red-800 rounded-lg p-4 text-red-400 mb-6",children:[e.jsx(S,{className:"inline-block mr-2"}),o]}),d&&m()]}):e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:m()})},te=[{id:"hunt-1",title:"Ransomware Detection Challenge",difficulty:"Beginner",category:"Endpoint",description:"Learn to identify ransomware activity by analyzing endpoint logs and system behavior.",objectives:["Identify suspicious process execution patterns","Detect file encryption activities","Recognize ransomware persistence mechanisms"],points:100,estimatedTime:"30 minutes",prerequisites:[],unlocked:!0},{id:"hunt-2",title:"C2 Communication Hunt",difficulty:"Intermediate",category:"Network",description:"Hunt for command and control (C2) communications by analyzing network traffic patterns.",objectives:["Identify beaconing patterns in network traffic","Detect domain generation algorithms (DGA)","Recognize encrypted C2 channels"],points:200,estimatedTime:"45 minutes",prerequisites:["hunt-1"],unlocked:!0},{id:"hunt-3",title:"Advanced Persistent Threat (APT) Hunt",difficulty:"Advanced",category:"Multi-source",description:"Learn to track sophisticated APT activities across multiple data sources.",objectives:["Correlate indicators across different log sources","Identify lateral movement techniques","Detect data exfiltration attempts"],points:350,estimatedTime:"60 minutes",prerequisites:["hunt-1","hunt-2"],unlocked:!1},{id:"hunt-4",title:"Supply Chain Compromise Detection",difficulty:"Advanced",category:"Multi-source",description:"Hunt for indicators of supply chain compromises in your environment.",objectives:["Identify suspicious software updates","Detect unusual certificate usage","Recognize compromised vendor access"],points:400,estimatedTime:"75 minutes",prerequisites:["hunt-2","hunt-3"],unlocked:!1}],se={completedChallenges:["hunt-1"],currentChallenge:"hunt-2",totalPoints:100,badges:[{id:"beginner-hunter",name:"Beginner Threat Hunter",icon:"FaSearch"}],skills:{"Log Analysis":75,"Network Traffic Analysis":40,"MITRE ATT&CK Knowledge":60,"Threat Intelligence":30}},Be=({userLevel:u="beginner"})=>{const{darkMode:s}=D(),[i,a]=y.useState([]),[t,l]=y.useState(null),[r,n]=y.useState(null),[c,d]=y.useState(!0),[v,o]=y.useState("challenges");y.useEffect(()=>{(async()=>{try{d(!0);const p=await J.getHuntingChallenges();p&&p.length>0?a(p):(console.log("Using fallback challenge data"),a(te)),l(se)}catch(p){console.error("Error fetching hunting challenges:",p),a(te),l(se)}finally{d(!1)}})()},[]);const x=m=>{const p=i.find(N=>N.id===m);n(p)},j=m=>{var g;const p={...t,completedChallenges:[...t.completedChallenges,m],totalPoints:t.totalPoints+(((g=i.find(f=>f.id===m))==null?void 0:g.points)||0)};l(p),n(null);const N=i.map(f=>!f.unlocked&&f.prerequisites.every(A=>p.completedChallenges.includes(A))?{...f,unlocked:!0}:f);a(N)};return c?e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:e.jsxs("div",{className:"flex flex-col justify-center items-center h-64",children:[e.jsx(q,{className:"animate-spin text-blue-500 text-4xl mb-4"}),e.jsx("p",{className:"text-gray-300",children:"Loading threat hunting challenges based on real-time intelligence..."})]})}):r?e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("div",{className:"mb-6",children:e.jsx("button",{className:"bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded flex items-center text-sm",onClick:()=>n(null),children:"Back to Challenges"})}),e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center mr-3 ${r.difficulty==="Beginner"?"bg-blue-500/20":r.difficulty==="Intermediate"?"bg-yellow-500/20":"bg-red-500/20"}`,children:e.jsx(E,{className:r.difficulty==="Beginner"?"text-blue-500":r.difficulty==="Intermediate"?"text-yellow-500":"text-red-500"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:r.title}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx("span",{className:`px-2 py-0.5 rounded mr-2 ${r.difficulty==="Beginner"?"bg-blue-900 text-blue-200":r.difficulty==="Intermediate"?"bg-yellow-900 text-yellow-200":"bg-red-900 text-red-200"}`,children:r.difficulty}),e.jsx("span",{className:"text-gray-400 mr-2",children:r.category}),e.jsx("span",{className:"text-gray-400",children:r.estimatedTime})]})]})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Challenge Description"}),e.jsx("p",{className:"text-gray-300 mb-4",children:r.description}),e.jsx("h3",{className:"font-semibold mb-2",children:"Objectives"}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-gray-300 mb-4",children:r.objectives.map((m,p)=>e.jsx("li",{children:m},p))}),r.realData&&e.jsxs("div",{className:"bg-blue-900 bg-opacity-30 border border-blue-800 rounded p-3 mb-4",children:[e.jsx("h4",{className:"font-semibold text-blue-300 mb-2",children:"Based on Real Threat Intelligence"}),r.realData.pulseId&&e.jsxs("div",{className:"text-sm text-gray-300 mb-2",children:[e.jsx("span",{className:"text-gray-400",children:"Intelligence Source:"})," AlienVault OTX"]}),r.realData.ipAddresses&&e.jsxs("div",{className:"text-sm text-gray-300 mb-2",children:[e.jsx("span",{className:"text-gray-400",children:"Sample Malicious IPs:"})," ",r.realData.ipAddresses.slice(0,2).join(", ")]}),r.realData.tags&&r.realData.tags.length>0&&e.jsxs("div",{className:"text-sm text-gray-300 mb-2",children:[e.jsx("span",{className:"text-gray-400",children:"Associated Tags:"})," ",e.jsx("div",{className:"inline-flex flex-wrap gap-1 mt-1",children:r.realData.tags.slice(0,5).map((m,p)=>e.jsx("span",{className:"bg-gray-700 px-2 py-0.5 rounded text-xs",children:m},p))})]}),r.realData.references&&r.realData.references.length>0&&e.jsxs("div",{className:"text-sm text-gray-300",children:[e.jsx("span",{className:"text-gray-400",children:"References:"})," ",r.realData.references.length," available"]})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-400",children:[e.jsxs("div",{children:["Points: ",r.points]}),e.jsxs("div",{children:["Estimated Time: ",r.estimatedTime]})]})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Challenge Environment"}),e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg mb-4 font-mono text-sm",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("div",{children:"Threat Hunting Console"}),e.jsx("div",{className:"text-green-400",children:"● Connected"})]}),e.jsxs("div",{className:"border-t border-gray-700 pt-2",children:[e.jsx("p",{className:"text-gray-400 mb-2",children:"// This is a simulated environment. In a real implementation, this would be an interactive console."}),e.jsxs("p",{className:"mb-1",children:["$ ",e.jsx("span",{className:"text-green-400",children:"hunt"})," --init"]}),e.jsx("p",{className:"text-gray-300 mb-1",children:"Initializing hunting environment..."}),e.jsx("p",{className:"text-gray-300 mb-1",children:"Loading data sources..."}),e.jsx("p",{className:"text-green-400 mb-1",children:"Environment ready!"}),e.jsxs("p",{className:"mb-1",children:["$ ",e.jsx("span",{className:"text-green-400",children:"hunt"})," --list-data-sources"]}),e.jsx("p",{className:"text-gray-300 mb-1",children:"Available data sources:"}),e.jsx("p",{className:"text-gray-300 mb-1",children:"- Windows Event Logs (last 7 days)"}),e.jsx("p",{className:"text-gray-300 mb-1",children:"- Network Traffic Captures (last 24 hours)"}),e.jsx("p",{className:"text-gray-300 mb-1",children:"- Endpoint Telemetry (last 7 days)"}),e.jsx("p",{className:"mb-1",children:"$ _"})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{className:"bg-green-600 hover:bg-green-700 px-4 py-2 rounded",onClick:()=>j(r.id),children:"Complete Challenge"})})]})]}):e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Threat Hunting Academy"}),e.jsx("p",{className:"text-gray-400",children:"Learn and practice threat hunting skills with real-world scenarios based on actual threat intelligence."})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-2 flex items-center",children:[e.jsx(U,{className:"text-yellow-400 mr-2"}),e.jsxs("span",{className:"font-bold",children:[t.totalPoints," XP"]})]})]}),e.jsxs("div",{className:"flex border-b border-gray-700 mb-6",children:[e.jsxs("button",{className:`flex items-center px-4 py-2 ${v==="challenges"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>o("challenges"),children:[e.jsx(z,{className:"mr-2"}),"Challenges"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${v==="skills"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>o("skills"),children:[e.jsx(E,{className:"mr-2"}),"Skills"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${v==="badges"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>o("badges"),children:[e.jsx(U,{className:"mr-2"}),"Badges"]})]}),v==="challenges"&&e.jsx("div",{children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:i.map(m=>e.jsxs("div",{className:`bg-gray-700 p-4 rounded-lg border-l-4 ${t.completedChallenges.includes(m.id)?"border-green-500":m.unlocked?"border-blue-500":"border-gray-600"}`,children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h3",{className:"font-semibold",children:m.title}),t.completedChallenges.includes(m.id)&&e.jsx(ce,{className:"text-green-500"})]}),e.jsxs("div",{className:"flex items-center text-xs mb-2",children:[e.jsx("span",{className:`px-2 py-0.5 rounded mr-2 ${m.difficulty==="Beginner"?"bg-blue-900 text-blue-200":m.difficulty==="Intermediate"?"bg-yellow-900 text-yellow-200":"bg-red-900 text-red-200"}`,children:m.difficulty}),e.jsx("span",{className:"text-gray-400",children:m.category})]}),e.jsx("p",{className:"text-sm text-gray-300 mb-3 line-clamp-2",children:m.description}),e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsxs("div",{className:"text-gray-400",children:[m.points," XP"]}),m.unlocked?e.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded flex items-center text-xs",onClick:()=>x(m.id),children:[t.completedChallenges.includes(m.id)?"Replay":"Start"," ",e.jsx(H,{className:"ml-1"})]}):e.jsxs("div",{className:"flex items-center text-gray-500",children:[e.jsx(re,{className:"mr-1"})," Locked"]})]})]},m.id))})}),v==="skills"&&e.jsxs("div",{children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Your Hunting Skills"}),e.jsx("div",{className:"space-y-4",children:Object.entries(t.skills).map(([m,p])=>e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-1",children:[e.jsx("div",{children:m}),e.jsxs("div",{className:"text-sm text-gray-400",children:[p,"/100"]})]}),e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-2.5",children:e.jsx("div",{className:`h-2.5 rounded-full ${p>=75?"bg-green-600":p>=50?"bg-blue-600":p>=25?"bg-yellow-600":"bg-red-600"}`,style:{width:`${p}%`}})})]},m))})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Recommended Skill Development"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"border-l-4 border-yellow-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Network Traffic Analysis"}),e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Current Level: 40/100"}),e.jsx("p",{className:"text-sm text-gray-300",children:"Focus on learning to identify C2 traffic patterns and unusual protocol behaviors."}),e.jsx("button",{className:"mt-2 bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs",children:"Start Learning Path"})]}),e.jsxs("div",{className:"border-l-4 border-red-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Threat Intelligence"}),e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Current Level: 30/100"}),e.jsx("p",{className:"text-sm text-gray-300",children:"Improve your ability to leverage threat intelligence in hunting activities."}),e.jsx("button",{className:"mt-2 bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs",children:"Start Learning Path"})]})]})]})]}),v==="badges"&&e.jsxs("div",{children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Earned Badges"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg text-center",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-blue-900 flex items-center justify-center mx-auto mb-2",children:e.jsx(E,{className:"text-blue-300 text-2xl"})}),e.jsx("div",{className:"font-medium",children:"Beginner Threat Hunter"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Completed first hunt"})]})})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Available Badges"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg text-center opacity-50",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2",children:e.jsx(z,{className:"text-gray-500 text-2xl"})}),e.jsx("div",{className:"font-medium",children:"Network Hunter"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Complete C2 hunt"})]}),e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg text-center opacity-50",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2",children:e.jsx(S,{className:"text-gray-500 text-2xl"})}),e.jsx("div",{className:"font-medium",children:"APT Hunter"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Complete APT hunt"})]}),e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg text-center opacity-50",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2",children:e.jsx(U,{className:"text-gray-500 text-2xl"})}),e.jsx("div",{className:"font-medium",children:"Master Hunter"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Complete all hunts"})]})]})]})]})]})},$e="AIzaSyCjrRX2vRmhFk1OQxHLleXMq0pGJC6xXrM",Oe="https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent",Fe=50,He=60*1e3;let X=0,ae=Date.now();const Ue=24*60*60*1e3,qe=new Map,ze=()=>{const u=Date.now();return u-ae>He&&(X=0,ae=u),X<Fe},_e=u=>{const s=qe.get(u);return s&&Date.now()-s.timestamp<Ue?s.response:null},Ke=async u=>{var s,i,a,t,l;try{const r=_e(u);if(r)return r;if(!ze())throw new Error("Rate limit exceeded. Please try again later.");try{const{data:n,error:c}=await W.from("ai_responses").select("*").eq("keyword",u.toLowerCase()).limit(1).single();if(!c&&n)return n}catch{console.log("No exact match found, trying fuzzy search")}try{const{data:n,error:c}=await W.from("ai_responses").select("*").ilike("keyword",`%${u.toLowerCase()}%`).limit(5);if(!c&&n&&n.length>0)return n[0]}catch{console.log("No fuzzy matches found, generating new response")}X++;try{const n=await fetch(`${Oe}?key=${$e}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:`You are a cybersecurity expert assistant. Provide a detailed, educational response to the following question about cybersecurity: "${u}"`}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!n.ok)throw new Error(`Gemini API error: ${n.status} ${n.statusText}`);return{content:((l=(t=(a=(i=(s=(await n.json()).candidates)==null?void 0:s[0])==null?void 0:i.content)==null?void 0:a.parts)==null?void 0:t[0])==null?void 0:l.text)||"I apologize, but I was unable to generate a response. Please try again with a different question.",category:"cybersecurity"}}catch(n){console.error("Error calling Gemini API:",n);try{const{data:c,error:d}=await W.from("ai_responses").select("*").ilike("keyword",`%${u.toLowerCase()}%`).limit(1);if(!d&&c&&c.length>0)return c[0]}catch(c){console.error("Error searching database for fallback response:",c)}throw new Error("Unable to connect to AI service. Please try again later.")}}catch(r){return console.error("Error getting AI response:",r),{content:`I understand you're asking about "${u}". Could you please provide more details or rephrase your question? I want to give you the most accurate and helpful response.`,category:"general"}}},We=[{role:"assistant",content:"Hello! I'm your AI Threat Intelligence Assistant. I can help you understand cybersecurity concepts, analyze threat data, and learn about attack techniques. What would you like to know about?",timestamp:new Date().toISOString()}],Ge=["What is a command and control (C2) server?","Explain the MITRE ATT&CK framework","How do I detect lateral movement in a network?","What are common indicators of a ransomware attack?","Explain the difference between signature and behavior-based detection"],Ve={"What is a command and control (C2) server?":`A Command and Control (C2) server is a computer controlled by an attacker or cybercriminal which is used to send commands to systems compromised by malware and receive stolen data from a target network.

Key characteristics of C2 servers include:

1. **Central Control Point**: They serve as the centralized management point for compromised systems (often called a botnet).

2. **Communication Channels**: They use various protocols for communication, including HTTP/HTTPS, DNS, and social media platforms to blend in with normal traffic.

3. **Functions**:
   - Send commands to compromised systems
   - Receive stolen data
   - Distribute malware updates
   - Control lateral movement within networks

4. **Detection Challenges**: Modern C2 infrastructure often uses encryption, legitimate services, and domain fronting to evade detection.

Identifying C2 communication is a critical part of threat hunting and incident response. Common detection methods include looking for unusual outbound connections, beaconing patterns, and anomalous DNS queries.`,"Explain the MITRE ATT&CK framework":`The MITRE ATT&CK (Adversarial Tactics, Techniques, and Common Knowledge) framework is a globally-accessible knowledge base of adversary tactics and techniques based on real-world observations of cyber attacks.

**Key Components:**

1. **Tactics**: The tactical goals that attackers try to achieve during an operation (the "why" of an attack technique).
   - Examples: Initial Access, Execution, Persistence, Privilege Escalation, Defense Evasion

2. **Techniques**: The specific methods used by attackers to achieve tactical goals (the "how").
   - Examples: Phishing (for Initial Access), PowerShell (for Execution)

3. **Sub-techniques**: More specific methods under a technique.
   - Example: Spearphishing Attachment (sub-technique of Phishing)

4. **Procedures**: Specific implementations of techniques used by threat actors.

**Practical Applications:**

- **Threat Intelligence**: Mapping observed attacker behavior to known patterns
- **Detection & Analytics**: Building detection rules based on technique behaviors
- **Red Teaming**: Simulating realistic attack scenarios
- **Gap Analysis**: Assessing security coverage against known attack methods

The framework is organized into different matrices for Enterprise (Windows, macOS, Linux), Mobile, and ICS (Industrial Control Systems) environments.

ATT&CK has become the de facto standard for describing and categorizing adversary behavior and is widely used by security teams for improving defenses based on real attack patterns.`,"How do I detect lateral movement in a network?":`Detecting lateral movement in a network is crucial for identifying attackers who have already gained initial access and are attempting to expand their foothold. Here are effective detection strategies:

**1. Network Traffic Analysis:**
- Monitor for unusual SMB, RDP, WMI, or PowerShell Remoting traffic between endpoints that don't typically communicate
- Look for authentication attempts from unexpected source systems
- Detect unusual port/protocol usage between internal systems

**2. Authentication Monitoring:**
- Track authentication events across systems (Windows Event IDs 4624, 4625, 4648, 4672)
- Look for accounts authenticating to multiple systems in short timeframes
- Monitor for credential dumping activities (Event ID 10 for Mimikatz)

**3. Endpoint Detection:**
- Monitor for new service creation on remote systems
- Track process creation with command-line arguments indicating remote execution
- Look for unusual scheduled task creation

**4. Behavioral Analytics:**
- Establish baselines of normal user and system behavior
- Alert on deviations from typical access patterns
- Use machine learning to identify anomalous authentication sequences

**5. Key Tools and Techniques:**
- Deploy honeypots/honeytoken accounts to detect unauthorized access attempts
- Implement network segmentation to limit lateral movement opportunities
- Use EDR solutions with specific lateral movement detection capabilities

**Common Lateral Movement Techniques to Monitor:**
- Pass-the-Hash/Pass-the-Ticket attacks
- Use of PsExec, WMI, PowerShell remoting
- Remote service creation and exploitation
- Internal port scanning and discovery activities

Effective lateral movement detection requires visibility across multiple data sources and correlation between network and endpoint telemetry.`,"What are common indicators of a ransomware attack?":`**Common Indicators of a Ransomware Attack**

Ransomware attacks typically exhibit several observable indicators across different phases of the attack lifecycle:

**Pre-encryption Indicators:**

1. **Unusual Authentication Activities:**
   - Multiple failed login attempts
   - Authentication from unusual locations/times
   - Sudden privileged account creation

2. **Suspicious Network Traffic:**
   - Communication with known C2 servers
   - Unusual SMB traffic (used for file discovery)
   - Unexpected data transfer patterns

3. **System/Admin Tool Misuse:**
   - Unexpected use of legitimate tools like PsExec, WMI, PowerShell
   - Volume Shadow Copy deletion commands
   - Suspicious registry modifications
   - Disabling of security tools

**Active Encryption Indicators:**

1. **File System Activities:**
   - High CPU/disk usage across multiple systems
   - Rapid file modifications (extensions changing)
   - Access to unusually large numbers of files
   - Creation of ransom notes in multiple directories

2. **Process Behavior:**
   - Unusual process lineage (e.g., Office app spawning cmd.exe)
   - Processes accessing large numbers of files
   - Known ransomware process names or patterns

**Post-encryption Indicators:**

1. **System Evidence:**
   - Changed file extensions (e.g., .encrypted, .locked, .crypted)
   - Ransom notes (often as text files or desktop wallpaper)
   - Inability to open common files
   - Applications failing to start

2. **Communication Evidence:**
   - Tor installation for dark web payment sites
   - Cryptocurrency wallet addresses in ransom notes
   - Communication attempts to payment verification servers

**Detection Best Practices:**
- Deploy file integrity monitoring
- Monitor for mass file type changes
- Implement behavioral analytics for process activity
- Create alerts for known ransomware file operations
- Monitor for encryption command-line parameters

Early detection of these indicators can help organizations contain ransomware before widespread encryption occurs.`,"Explain the difference between signature and behavior-based detection":`**Signature-Based vs. Behavior-Based Detection**

**Signature-Based Detection:**

Signature-based detection identifies threats by matching patterns against known malicious code, file hashes, or specific byte sequences.

**Characteristics:**
- Relies on a database of known threat signatures
- Very effective against known, previously analyzed threats
- Low false positive rate for identified threats
- Fast processing and minimal system impact

**Limitations:**
- Cannot detect zero-day or previously unseen threats
- Easily evaded by polymorphic or obfuscated malware
- Requires constant signature updates
- Ineffective against fileless malware

**Behavior-Based Detection:**

Behavior-based detection identifies threats by monitoring and analyzing the actions and behaviors of programs, processes, or users to identify suspicious or malicious activity patterns.

**Characteristics:**
- Analyzes actions rather than code patterns
- Can detect novel and zero-day threats
- Effective against polymorphic and obfuscated malware
- Provides protection against fileless attacks

**Limitations:**
- Higher false positive potential
- More resource-intensive
- Requires baseline establishment and tuning
- More complex to implement and manage

**Key Differences:**

| Aspect | Signature-Based | Behavior-Based |
|--------|----------------|----------------|
| Detection Method | Pattern matching | Activity analysis |
| Zero-day Detection | Poor | Good |
| Resource Usage | Lower | Higher |
| False Positives | Lower | Higher |
| Evasion Difficulty | Easier to evade | Harder to evade |
| Implementation | Simpler | More complex |

**Modern Approach:**
Today's most effective security solutions combine both approaches:
- Signature-based detection for efficient identification of known threats
- Behavior-based detection for identifying novel attacks and evasive malware
- Machine learning to improve both methods over time

This hybrid approach provides comprehensive protection against both known and emerging threats while balancing performance and accuracy.`},Ye=()=>{const{darkMode:u}=D(),[s,i]=y.useState(We),[a,t]=y.useState(""),[l,r]=y.useState(!1),n=y.useRef(null),[c,d]=y.useState(!1),[v,o]=y.useState(Ge),[x,j]=y.useState(null),[m,p]=y.useState(!0),[N,g]=y.useState(null);y.useEffect(()=>{(async()=>{try{p(!0),g(null);const b=await J.getAIAssistantData();b&&(b.suggestedQuestions&&b.suggestedQuestions.length>0&&o(b.suggestedQuestions),b.relatedLearning&&b.relatedLearning.length>0&&j({relatedLearning:b.relatedLearning,recentThreats:b.recentThreats||[]}))}catch(b){console.error("Error fetching AI assistant data:",b),g("Failed to load threat intelligence data.")}finally{p(!1)}})()},[]),y.useEffect(()=>{var h;(h=n.current)==null||h.scrollIntoView({behavior:"smooth"})},[s]);const f=async(h=a)=>{if(!h.trim())return;const b={role:"user",content:h,timestamp:new Date().toISOString()};i(w=>[...w,b]),t(""),r(!0),d(!0);try{let w=null;for(const[k,K]of Object.entries(Ve))if(h.toLowerCase().includes(k.toLowerCase())||k.toLowerCase().includes(h.toLowerCase())){w=K;break}if(!w)try{w=(await Ke(h)).content}catch(k){throw console.error("Error getting AI response:",k),new Error("I'm having trouble connecting to my knowledge base right now. Please try again later.")}const I={role:"assistant",content:w,timestamp:new Date().toISOString()};i(k=>[...k,I])}catch(w){console.error("Error processing message:",w);const I={role:"assistant",content:w.message||"I'm sorry, I encountered an error while processing your request. Please try again later.",timestamp:new Date().toISOString(),isError:!0};i(k=>[...k,I]),setTimeout(()=>{const k={role:"system",content:"Would you like to try again?",timestamp:new Date().toISOString(),isRetryPrompt:!0,originalMessage:h};i(K=>[...K,k])},1e3)}finally{r(!1),d(!1)}},A=h=>{f(h)},B=h=>h.split(`
`).map((b,w)=>{if(b=b.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),b.match(/^\d+\.\s/))return e.jsx("li",{className:"ml-5 list-decimal",children:b.replace(/^\d+\.\s/,"")},w);if(b.match(/^-\s/))return e.jsx("li",{className:"ml-5 list-disc",children:b.replace(/^-\s/,"")},w);if(b.match(/^\s*$/))return e.jsx("br",{},w);if(b.match(/^#{1,6}\s/)){const I=b.match(/^(#{1,6})\s/)[1].length,k=b.replace(/^#{1,6}\s/,"");switch(I){case 1:return e.jsx("h1",{className:"text-xl font-bold my-2",children:k},w);case 2:return e.jsx("h2",{className:"text-lg font-bold my-2",children:k},w);case 3:return e.jsx("h3",{className:"text-md font-bold my-2",children:k},w);default:return e.jsx("h4",{className:"font-bold my-1",children:k},w)}}else return e.jsx("p",{dangerouslySetInnerHTML:{__html:b}},w)});return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mr-3",children:e.jsx(Y,{className:"text-blue-500"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"AI Threat Intelligence Assistant"}),e.jsx("p",{className:"text-gray-400",children:"Ask questions about cybersecurity concepts and threats"})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 h-[500px] flex flex-col",children:[e.jsxs("div",{className:"flex-1 overflow-y-auto mb-4 space-y-4",children:[s.map((h,b)=>e.jsx("div",{className:`flex ${h.role==="user"?"justify-end":"justify-start"}`,children:h.isRetryPrompt?e.jsxs("div",{className:"w-full bg-gray-900/50 rounded-lg p-3 text-center",children:[e.jsx("p",{className:"text-gray-400 mb-2",children:h.content}),e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 rounded text-sm",onClick:()=>f(h.originalMessage),children:"Retry"})]}):e.jsxs("div",{className:`max-w-[80%] rounded-lg p-3 ${h.role==="user"?"bg-blue-600 text-white":h.isError?"bg-red-900/30 border border-red-800 text-gray-200":"bg-gray-800 text-gray-200"}`,children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx("div",{className:`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${h.role==="user"?"bg-blue-700":h.isError?"bg-red-800":"bg-gray-700"}`,children:h.role==="user"?e.jsx(ge,{className:"text-xs"}):h.isError?e.jsx(S,{className:"text-xs"}):e.jsx(Y,{className:"text-xs"})}),e.jsxs("div",{className:"text-xs opacity-75",children:[h.role==="user"?"You":"AI Assistant"," • ",new Date(h.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]}),e.jsx("div",{className:"text-sm space-y-1",children:B(h.content)})]})},b)),l&&e.jsx("div",{className:"flex justify-start",children:e.jsx("div",{className:"bg-gray-800 text-gray-200 rounded-lg p-3 max-w-[80%]",children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-gray-400 animate-bounce",style:{animationDelay:"0ms"}}),e.jsx("div",{className:"w-2 h-2 rounded-full bg-gray-400 animate-bounce",style:{animationDelay:"150ms"}}),e.jsx("div",{className:"w-2 h-2 rounded-full bg-gray-400 animate-bounce",style:{animationDelay:"300ms"}})]})})}),e.jsx("div",{ref:n})]}),e.jsx("div",{className:"mt-auto",children:e.jsxs("form",{onSubmit:h=>{h.preventDefault(),f()},className:"flex items-center",children:[e.jsx("input",{type:"text",value:a,onChange:h=>t(h.target.value),placeholder:"Ask about cybersecurity threats, techniques, or concepts...",className:"flex-1 bg-gray-800 border border-gray-600 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:c}),e.jsx("button",{type:"submit",className:`bg-blue-600 hover:bg-blue-700 rounded-r px-4 py-2 flex items-center ${c?"opacity-50 cursor-not-allowed":""}`,disabled:c,children:e.jsx(xe,{})})]})})]})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center",children:[e.jsx(pe,{className:"mr-2 text-blue-400"}),"Suggested Questions"]}),m?e.jsx("div",{className:"flex justify-center py-4",children:e.jsx(q,{className:"animate-spin text-blue-500"})}):e.jsx("div",{className:"space-y-2",children:v.map((h,b)=>e.jsx("button",{className:"w-full text-left bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm transition-colors",onClick:()=>A(h),disabled:c,children:h},b))}),N&&e.jsxs("div",{className:"text-xs text-red-400 mt-2",children:[e.jsx(S,{className:"inline-block mr-1"}),N]})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center",children:[e.jsx(E,{className:"mr-2 text-green-400"}),"Related Learning"]}),m?e.jsx("div",{className:"flex justify-center py-4",children:e.jsx(q,{className:"animate-spin text-green-500"})}):x&&x.relatedLearning?e.jsx("div",{className:"space-y-2",children:x.relatedLearning.map((h,b)=>e.jsxs("div",{className:"bg-gray-800 rounded p-3",children:[e.jsx("h4",{className:"font-medium text-sm",children:h.title}),e.jsx("p",{className:"text-xs text-gray-400 mb-2",children:h.description}),e.jsx("button",{className:"text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1",children:"View Module"})]},b))}):e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"bg-gray-800 rounded p-3",children:[e.jsx("h4",{className:"font-medium text-sm",children:"MITRE ATT&CK Framework"}),e.jsx("p",{className:"text-xs text-gray-400 mb-2",children:"Comprehensive threat model and knowledge base"}),e.jsx("button",{className:"text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1",children:"View Module"})]}),e.jsxs("div",{className:"bg-gray-800 rounded p-3",children:[e.jsx("h4",{className:"font-medium text-sm",children:"Threat Hunting Fundamentals"}),e.jsx("p",{className:"text-xs text-gray-400 mb-2",children:"Learn proactive threat detection techniques"}),e.jsx("button",{className:"text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1",children:"View Module"})]})]})]}),x&&x.recentThreats&&x.recentThreats.length>0&&e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center",children:[e.jsx(S,{className:"mr-2 text-red-400"}),"Recent Threats"]}),e.jsx("div",{className:"space-y-3",children:x.recentThreats.map((h,b)=>e.jsxs("div",{className:"bg-gray-800 rounded p-3",children:[e.jsx("h4",{className:"font-medium text-sm",children:h.name}),e.jsx("p",{className:"text-xs text-gray-400 mb-1",children:new Date(h.created).toLocaleDateString()}),e.jsxs("div",{className:"flex flex-wrap gap-1 mb-2",children:[h.tags.slice(0,3).map((w,I)=>e.jsx("span",{className:"bg-gray-700 px-2 py-0.5 rounded text-xs",children:w},I)),h.tags.length>3&&e.jsxs("span",{className:"bg-gray-700 px-2 py-0.5 rounded text-xs",children:["+",h.tags.length-3," more"]})]}),e.jsx("button",{className:"text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1",onClick:()=>A(`What is ${h.name}?`),children:"Ask About This Threat"})]},b))})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center",children:[e.jsx(V,{className:"mr-2 text-yellow-400"}),"Threat Analysis Tools"]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("button",{className:"w-full bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm flex items-center justify-between",children:[e.jsx("span",{children:"IOC Analyzer"}),e.jsx(S,{className:"text-yellow-400"})]}),e.jsxs("button",{className:"w-full bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm flex items-center justify-between",children:[e.jsx("span",{children:"YARA Rule Generator"}),e.jsx(V,{className:"text-blue-400"})]}),e.jsxs("button",{className:"w-full bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm flex items-center justify-between",children:[e.jsx("span",{children:"Threat Intelligence Search"}),e.jsx(E,{className:"text-green-400"})]})]})]})]})]})]})},Ze=()=>{const{darkMode:u}=D(),[s,i]=y.useState("dashboard"),[a,t]=y.useState("beginner"),[l,r]=y.useState(!0);y.useEffect(()=>{const v=setTimeout(()=>{r(!1)},1500);return()=>clearTimeout(v)},[]);const c=[{id:"dashboard",label:"Threat Dashboard",icon:fe,level:"all"},{id:"threat-of-week",label:"Threat of the Week",icon:be,level:"all"},{id:"hunting-academy",label:"Hunting Academy",icon:U,level:"all"},{id:"ai-assistant",label:"AI Threat Assistant",icon:Y,level:"all"},{id:"analytics",label:"Advanced Analytics",icon:ne,level:"intermediate"},{id:"learning-path",label:"Learning Path",icon:ye,level:"all"},{id:"practice-lab",label:"Practice Lab",icon:z,level:"intermediate"}].filter(v=>v.level==="all"||v.level==="intermediate"&&(a==="intermediate"||a==="advanced")||v.level==="advanced"&&a==="advanced"),d=()=>{if(l)return e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})});switch(s){case"dashboard":return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-6",children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Cyber Threat Intelligence Dashboard"}),e.jsxs("p",{className:"text-gray-400",children:["Comprehensive visualization of global cyber threat landscape and attack patterns.",e.jsx("span",{className:"ml-2 text-blue-400",children:"Learn as you explore real-world threat data."})]})]})}),e.jsx(De,{})]});case"threat-of-week":return e.jsx(Re,{});case"hunting-academy":return e.jsx(Be,{userLevel:a});case"ai-assistant":return e.jsx(Ye,{});case"analytics":return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-6",children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Advanced Threat Analytics"}),e.jsxs("p",{className:"text-gray-400",children:["In-depth analysis of threat data from multiple intelligence sources.",e.jsx("span",{className:"ml-2 text-blue-400",children:"For intermediate and advanced cybersecurity students."})]})]})}),e.jsx(Ne,{})]});case"learning-path":return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Personalized Learning Path"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Customized cybersecurity learning recommendations based on current threat landscape and your progress."}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsxs("h3",{className:"text-lg font-semibold mb-3",children:["Your Current Level: ",a.charAt(0).toUpperCase()+a.slice(1)]}),e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:a==="beginner"?"25%":a==="intermediate"?"65%":"90%"}})}),e.jsx("span",{className:"ml-2 text-sm text-gray-400",children:a==="beginner"?"25%":a==="intermediate"?"65%":"90%"})]}),e.jsxs("div",{className:"flex space-x-2 mb-6",children:[e.jsx("button",{className:`px-3 py-1 rounded ${a==="beginner"?"bg-blue-600":"bg-gray-600"}`,onClick:()=>t("beginner"),children:"Beginner"}),e.jsx("button",{className:`px-3 py-1 rounded ${a==="intermediate"?"bg-blue-600":"bg-gray-600"}`,onClick:()=>t("intermediate"),children:"Intermediate"}),e.jsx("button",{className:`px-3 py-1 rounded ${a==="advanced"?"bg-blue-600":"bg-gray-600"}`,onClick:()=>t("advanced"),children:"Advanced"})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-4",children:"Based on current threat trends and your progress, we recommend focusing on these skills:"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"border-l-4 border-blue-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Malware Analysis Fundamentals"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Related to 43% increase in polymorphic malware attacks"})]}),e.jsxs("div",{className:"border-l-4 border-yellow-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Network Traffic Analysis"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Critical for detecting C2 communications in recent attacks"})]}),e.jsxs("div",{className:"border-l-4 border-green-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"OSINT Techniques"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Valuable for early threat detection and reconnaissance"})]})]})]})]});case"practice-lab":return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Hands-on Practice Lab"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Apply your knowledge in realistic scenarios based on actual threat intelligence."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Available Scenarios"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"border-l-4 border-blue-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Ransomware Incident Response"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Difficulty: Intermediate"}),e.jsx("button",{className:"mt-2 px-3 py-1 bg-blue-600 rounded text-sm",children:"Start Scenario"})]}),e.jsxs("div",{className:"border-l-4 border-yellow-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"APT Detection Challenge"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Difficulty: Advanced"}),e.jsx("button",{className:"mt-2 px-3 py-1 bg-blue-600 rounded text-sm",children:"Start Scenario"})]}),e.jsxs("div",{className:"border-l-4 border-red-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Supply Chain Attack Analysis"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Difficulty: Advanced"}),e.jsx("button",{className:"mt-2 px-3 py-1 bg-blue-600 rounded text-sm",children:"Start Scenario"})]})]})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Your Progress"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:"Completed Scenarios"}),e.jsx("div",{className:"font-semibold",children:"3/12"})]}),e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:"25%"}})}),e.jsxs("div",{className:"flex justify-between items-center mt-4",children:[e.jsx("div",{children:"Earned Badges"}),e.jsx("div",{className:"font-semibold",children:"2"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center",children:e.jsx(Q,{className:"text-blue-500"})}),e.jsx("div",{className:"w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center",children:e.jsx(ve,{className:"text-green-500"})})]})]})]})]})]});default:return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Coming Soon"}),e.jsx("p",{className:"text-gray-400",children:"This feature is currently under development. Check back soon!"})]})}};return e.jsxs("div",{className:`p-6 ${u?"text-white":"text-gray-900"}`,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Security Insights & Learning Hub"}),e.jsx("p",{className:"text-gray-400",children:"Explore real-world threat intelligence and enhance your cybersecurity skills through interactive learning experiences."})]}),e.jsxs("div",{className:"mb-6 bg-gray-800 rounded-lg p-4 flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-400 mr-2",children:"Experience Level:"}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${a==="beginner"?"bg-blue-900 text-blue-200":a==="intermediate"?"bg-yellow-900 text-yellow-200":"bg-green-900 text-green-200"}`,children:a.charAt(0).toUpperCase()+a.slice(1)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-sm text-gray-400 mr-2",children:"Quick Search:"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search threats, techniques...",className:"bg-gray-700 border border-gray-600 rounded pl-8 pr-4 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"}),e.jsx(E,{className:"absolute left-2 top-2 text-gray-400"})]})]})]}),e.jsx("div",{className:"flex flex-wrap border-b border-gray-700 mb-6",children:c.map(v=>e.jsxs("button",{className:`flex items-center px-4 py-2 ${s===v.id?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>i(v.id),children:[e.jsx(v.icon,{className:"mr-2"}),v.label]},v.id))}),d()]})};export{Ze as default};
