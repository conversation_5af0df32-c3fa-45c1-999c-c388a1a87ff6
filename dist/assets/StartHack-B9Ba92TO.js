import{r as i,j as e,m as l,t as k,v as A,w as V,x as T,y as C,A as Q,z as q,B as N,C as Y,d as $,n as K,D as X,E as J,G as Z,H as ee}from"./index-c6UceSOv.js";import{L as se}from"./LabTerminal-Bk3L4em0.js";const te=({phase:r,command:s,onHint:u})=>{const[n,m]=i.useState(""),[j,p]=i.useState(!1),d={"nmap -sV":{analysis:["I've detected several open ports and services:","• SSH (22) - OpenSSH 8.2p1 with non-standard configuration","• Web servers (80, 443) - nginx 1.18.0","• MySQL (3306) - Version 8.0.28","• Custom service (8080) - Possibly related to target system",`
The SSH misconfiguration could be our entry point.`],hint:"Try scanning for all ports with 'nmap -p-' to find any hidden services."},"nmap -p-":{analysis:["Full port scan reveals additional services:","• All previously discovered ports confirmed","• New port discovered: 9001 (unknown service)",`
The unknown service on port 9001 could be significant.`],hint:"Use 'nmap -A' for deeper analysis of the unknown service."},"nmap -A":{analysis:["Aggressive scan results show:","• Linux 5.4.0-generic OS detected","• SSH authentication methods exposed","• SSL certificate reveals domain: cybercorp.local","• Critical: SSH vulnerability CVE-2020-14145 detected",`
We can potentially exploit the SSH vulnerability.`],hint:"Research CVE-2020-14145 for potential exploitation methods."},"ssh admin":{analysis:["SSH connection attempt failed, but revealed important information:","• Server allows password authentication despite config","• This suggests weak security practices","• Default credentials might work",`
We should try brute forcing with common credentials.`],hint:"Use Hydra to attempt brute forcing with common credentials."},hydra:{analysis:["Brute force attack successful!","• Credentials found: admin:admin123","• This is a common default credential pair","• The server should have had these credentials changed",`
We now have initial access to the system.`],hint:"Now that we have access, check what privileges we have with 'sudo -l'."},"sudo -l":{analysis:["Privilege check reveals dangerous permissions:","• User can run several commands with sudo WITHOUT password","• Critical: /usr/bin/find has known privilege escalation vectors","• This is a serious misconfiguration",`
We can use these permissions to escalate to root.`],hint:"The 'find' command can be used to execute arbitrary commands with '-exec'."},"sudo find":{analysis:["Privilege escalation successful!","• We now have root access","• This gives us complete control of the system","• We can now hunt for the rogue AI process",`
Next step is to locate the AI process.`],hint:"Use 'ps aux | grep AI' to search for suspicious processes."},"ps aux":{analysis:["Process analysis reveals:","• Suspicious process: [ROGUE_AI]","• Extremely high CPU usage (99.9%)","• Running as root user","• Process ID: 1234",`
This is likely our target process.`],hint:"Check network connections with 'netstat -tuln' to see what ports this process might be using."},netstat:{analysis:["Network analysis shows:","• Port 9001 is listening for connections","• This matches the hidden port we found earlier","• This is likely the AI control port",`
We can now terminate the AI process.`],hint:"Use 'kill -9 1234' to forcefully terminate the process."},kill:{analysis:["AI termination successful!","• Process has been killed","• System control restored","• Mission accomplished",`
All objectives completed successfully.`],hint:"Mission complete! Return to base."}};i.useEffect(()=>{s&&(p(!0),setTimeout(()=>{const o=Object.keys(d).find(h=>s.includes(h));m(o?d[o].analysis.join(`
`):"I don't have specific insights for this command. Try one of the suggested commands."),p(!1)},1e3))},[s]);const x=()=>{const o=Object.keys(d).find(h=>s==null?void 0:s.includes(h));return o?d[o].hint:null};return e.jsxs(l.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black rounded-lg p-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(k,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-bold",children:"AI Analysis"}),e.jsxs("p",{className:"text-gray-400 text-sm",children:["Phase: ",r]})]})]}),j?e.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[e.jsx("div",{className:"animate-spin",children:e.jsx(A,{})}),e.jsx("span",{children:"Analyzing command output..."})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"font-mono text-sm whitespace-pre-wrap text-gray-300",children:n}),x()&&e.jsxs("div",{className:"mt-4 bg-[#88cc14]/10 border border-[#88cc14]/20 rounded-lg p-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-[#88cc14] mb-2",children:[e.jsx(V,{}),e.jsx("span",{className:"font-bold",children:"Suggestion"})]}),e.jsx("p",{className:"text-gray-300",children:x()})]}),e.jsxs("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-red-400 mb-2",children:[e.jsx(T,{}),e.jsx("span",{className:"font-bold",children:"Security Alert"})]}),e.jsx("p",{className:"text-gray-300",children:"Remember to document all findings. Each discovered service and vulnerability could be crucial for later phases."})]})]})]})},ie=({onBack:r})=>{const[s,u]=i.useState(0),[n,m]=i.useState(!0),[j,p]=i.useState([]),[d,x]=i.useState(!0),[o,h]=i.useState(null),[g,I]=i.useState([]),[f,E]=i.useState(!1),[L,b]=i.useState(!1),[H,w]=i.useState(!1),[F,P]=i.useState(!1),[M,O]=i.useState(!1);i.useEffect(()=>{const t=()=>{O(window.innerWidth<768)};return t(),window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},[]);const c=[{id:"recon",name:"Reconnaissance",description:"Gather intelligence about the target system",flag:"FLAG{SSH_MISCONFIGURED}",hint:"Look for unusual service configurations and hidden ports. The flag is related to SSH.",commands:[{command:"nmap -sV *************",description:"Scan target for open ports and services",expectedOutput:`Starting Nmap 7.94 ( https://nmap.org )
Scanning *************
PORT     STATE  SERVICE        VERSION
22/tcp   open   ssh           OpenSSH 8.2p1
80/tcp   open   http          nginx 1.18.0
443/tcp  open   https         nginx 1.18.0
3306/tcp open   mysql         MySQL 8.0.28
8080/tcp open   http-proxy    [FILTERED]

Service detection performed. Please wait...
[!] Interesting: Non-standard SSH configuration detected
[!] Port 8080 appears to be running custom software`,hint:`Try these Nmap commands:
• nmap -sV *************    (Service version detection)
• nmap -p- *************    (All ports scan)
• nmap -A *************     (Aggressive scan)

The SSH service on port 22 looks misconfigured - this could be our way in.
The custom software on port 8080 might be related to the AI system.`},{command:"nmap -p- *************",description:"Scan all ports on target",expectedOutput:`Starting Nmap 7.94 ( https://nmap.org )
Scanning *************
PORT     STATE  SERVICE
22/tcp   open   ssh
80/tcp   open   http
443/tcp  open   https
3306/tcp open   mysql
8080/tcp open   http-proxy
9001/tcp open   unknown

[!] Hidden service detected on port 9001
[!] Found flag: FLAG{SSH_MISCONFIGURED}`,hint:'The "-p-" flag scans all 65535 ports. Look for unusual or non-standard ports that might be running custom services.'}]},{id:"initial-access",name:"Initial Access",description:"Exploit identified vulnerabilities to gain access",flag:"FLAG{WEAK_SSH_CREDS}",hint:"The SSH service has a known vulnerability. Try common credentials.",commands:[{command:"ssh admin@*************",description:"Attempt SSH connection as admin",expectedOutput:`SSH connection attempt...
Permission denied (publickey,password).
[!] Note: Server allows password authentication despite config
[!] Default credentials might work`,hint:"The SSH server is misconfigured to allow password authentication. Try common credentials."},{command:"hydra -l admin -P /usr/share/wordlists/common.txt ssh://*************",description:"Brute force SSH password",expectedOutput:`Hydra v9.1 (c) 2020 by van Hauser/THC & David Maciejak
Hydra (https://github.com/vanhauser-thc/thc-hydra)
[DATA] max 16 tasks per 1 server
[DATA] attacking ssh://*************:22/
[22][ssh] host: *************   login: admin   password: admin123
[STATUS] attack finished for ************* (valid pair found)

[!] Found flag: FLAG{WEAK_SSH_CREDS}`,hint:"Hydra is a fast network logon cracker that supports many protocols. The -l flag specifies the username and -P specifies the password list."}]},{id:"privilege-escalation",name:"Privilege Escalation",description:"Escalate privileges to gain administrative access",flag:"FLAG{SUDO_MISCONFIGURED}",hint:"Check for misconfigured sudo permissions and SUID binaries.",commands:[{command:"sudo -l",description:"List sudo privileges",expectedOutput:`Matching Defaults entries for admin on cybercorp:
    env_reset, mail_badpass,
    secure_path=/usr/local/sbin\\:/usr/local/bin\\:/usr/sbin\\:/usr/bin\\:/sbin\\:/bin\\:/snap/bin

User admin may run the following commands on cybercorp:
    (ALL : ALL) NOPASSWD: /usr/bin/find
    (ALL : ALL) NOPASSWD: /usr/bin/python3
    (ALL : ALL) NOPASSWD: /usr/bin/env

[!] Unusual sudo permissions detected`,hint:"The sudo -l command shows which commands you can run with sudo. Look for commands that can be used for privilege escalation."},{command:"sudo find . -exec /bin/sh \\;",description:"Exploit sudo find permission",expectedOutput:`[+] Shell spawned with elevated privileges
[+] Current user: root
[+] Current privileges: ALL

[!] Found flag: FLAG{SUDO_MISCONFIGURED}`,hint:"The find command's -exec option can be used to execute commands. Since we can run find with sudo, we can use it to spawn a root shell."}]},{id:"ai-shutdown",name:"AI Shutdown",description:"Locate and terminate the rogue AI process",flag:"FLAG{AI_TERMINATED}",hint:"The AI process is running with a specific signature. Look for unusual system processes.",commands:[{command:"ps aux | grep AI",description:"Search for AI processes",expectedOutput:`root      1234  99.9  5.0 1234567 89012 ?   R    10:00   0:00 [ROGUE_AI]
root      1235   0.0  0.0   4567   890 pts/0    S+   10:00   0:00 grep AI

[!] High CPU usage detected from ROGUE_AI process`,hint:"The ps command shows running processes. Look for suspicious processes with high CPU usage."},{command:"netstat -tuln",description:"Check network connections",expectedOutput:`Active Internet connections (only servers)
Proto Recv-Q Send-Q Local Address           Foreign Address         State
tcp        0      0 0.0.0.0:22              0.0.0.0:*               LISTEN
tcp        0      0 0.0.0.0:80              0.0.0.0:*               LISTEN
tcp        0      0 0.0.0.0:9001            0.0.0.0:*               LISTEN

[!] AI control port detected on 9001`,hint:"The netstat command shows network connections. Port 9001 was discovered earlier during reconnaissance."},{command:"kill -9 1234",description:"Terminate the AI process",expectedOutput:`[!] WARNING: AI defensive measures activated
[*] Attempting to terminate process...
[+] Process successfully terminated
[+] AI system shutting down...
[+] Control restored to authorized personnel

[!] Found flag: FLAG{AI_TERMINATED}

Mission Complete! All systems secured.`,hint:"The kill -9 command forcefully terminates a process. Use the PID found from the ps command."}]}],R=t=>{h(t);const a=t.expectedOutput.match(/FLAG{[^}]+}/);if(a&&!g.includes(a[0])){const y=a[0];I(v=>[...v,y]),b(!0),p(v=>[...v,t]);const S=c[s].commands,B=S.indexOf(t)===S.length-1;setTimeout(B?()=>{b(!1),w(!0),setTimeout(()=>{w(!1),s<c.length-1?(u(s+1),p([]),h(null)):P(!0)},3e3)}:()=>{b(!1)},2e3)}else p(y=>[...y,t])},U=()=>e.jsx("div",{className:"bg-black/50 backdrop-blur-sm sticky top-0 z-10 p-4",children:e.jsx("div",{className:"flex items-center gap-4 max-w-4xl mx-auto",children:c.map((t,a)=>e.jsx("div",{className:`flex-1 ${a===s?"text-[#88cc14]":a<s?"text-gray-400":"text-gray-600"}`,children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${a===s?"bg-[#88cc14]/20 text-[#88cc14]":a<s?"bg-gray-800 text-gray-400":"bg-gray-800/50 text-gray-600"}`,children:a+1}),e.jsx("span",{className:"font-medium hidden sm:inline",children:t.name})]})},t.id))})}),D=()=>e.jsxs("div",{className:"bg-black rounded-lg p-4 mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"text-[#88cc14]"}),e.jsx("h3",{className:"text-white font-bold",children:"Captured Flags"})]}),e.jsx("button",{onClick:()=>E(!f),className:"text-gray-400 hover:text-[#88cc14] transition-colors",children:f?e.jsx(Y,{}):e.jsx($,{})})]}),g.length>0?e.jsx("div",{className:"space-y-2",children:g.map((t,a)=>e.jsx(l.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"bg-[#88cc14]/10 text-[#88cc14] p-2 rounded font-mono",children:t},a))}):e.jsx("p",{className:"text-gray-500",children:"No flags captured yet"}),f&&c[s].hint&&e.jsxs(l.div,{initial:{opacity:0},animate:{opacity:1},className:"mt-4 bg-yellow-500/10 border border-yellow-500/20 rounded p-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-yellow-500 mb-2",children:[e.jsx(T,{}),e.jsx("span",{className:"font-bold",children:"Flag Hint"})]}),e.jsx("p",{className:"text-gray-300",children:c[s].hint})]})]}),G=()=>e.jsxs(l.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(q,{className:"text-[#88cc14] text-2xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Operation: Shadow Breach"}),e.jsx("p",{className:"text-gray-400",children:"Top Secret Clearance Required"})]})]}),e.jsx("button",{onClick:()=>m(!1),className:"bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors",children:"Accept Mission"})]}),e.jsx("div",{className:"prose prose-invert max-w-none",children:e.jsx("pre",{className:"text-gray-300 font-mono text-sm whitespace-pre-wrap",children:`Mission Briefing: A rogue AI has taken control of CyberCorp's main server infrastructure. Your mission is to infiltrate the system, bypass security measures, and regain control before critical data is compromised.

Target: CyberCorp Main Server (*************)
Security Level: High
Mission Status: Critical

Objectives:
1. Perform reconnaissance
2. Identify vulnerabilities
3. Gain initial access
4. Escalate privileges
5. Locate and stop the rogue AI

Each phase has a hidden flag to capture. Use the available tools and watch for flag patterns: FLAG{SOME_TEXT}

Remember: Stealth is crucial. Excessive failed attempts will trigger enhanced security measures.`})})]}),W=()=>e.jsx(l.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:-50},className:"fixed inset-0 flex items-center justify-center z-50 pointer-events-none",children:e.jsxs("div",{className:"bg-[#88cc14]/90 text-black p-6 rounded-lg shadow-lg flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-white flex items-center justify-center",children:e.jsx(N,{className:"text-[#88cc14] text-2xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-bold",children:"Flag Captured!"}),e.jsx("p",{children:"Great work! You've found a hidden flag."})]})]})}),z=()=>e.jsx(l.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:-50},className:"fixed inset-0 flex items-center justify-center z-50 pointer-events-none",children:e.jsx("div",{className:"bg-black/90 text-white p-6 rounded-lg shadow-lg",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-[#88cc14] mx-auto flex items-center justify-center mb-4",children:e.jsx(K,{className:"text-black text-3xl"})}),e.jsx("h3",{className:"text-2xl font-bold mb-2",children:"Phase Complete!"}),s<c.length-1?e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"text-gray-400 mb-4",children:"Moving to next phase..."}),e.jsxs("div",{className:"flex items-center justify-center gap-2 text-[#88cc14]",children:[e.jsx("span",{children:c[s].name}),e.jsx(X,{}),e.jsx("span",{children:c[s+1].name})]})]}):e.jsx("p",{className:"text-[#88cc14]",children:"Mission Accomplished!"})]})})}),_=()=>e.jsx(l.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"fixed inset-0 flex items-center justify-center z-50 bg-black/90",children:e.jsx("div",{className:"bg-black p-8 rounded-lg border border-[#88cc14] max-w-2xl w-full mx-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-20 h-20 rounded-full bg-[#88cc14] mx-auto flex items-center justify-center mb-6",children:e.jsx(J,{className:"text-black text-4xl"})}),e.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"Mission Accomplished!"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"You've successfully infiltrated the system, gained control, and terminated the rogue AI. All systems have been restored to normal operation."}),e.jsxs("div",{className:"bg-gray-900 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"text-[#88cc14] font-bold mb-2",children:"Captured Flags:"}),e.jsx("div",{className:"space-y-2",children:g.map((t,a)=>e.jsx("div",{className:"font-mono text-white bg-[#88cc14]/10 p-2 rounded",children:t},a))})]}),e.jsx("button",{onClick:r,className:"bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors",children:"Return to Missions"})]})})});return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("button",{onClick:r,className:"text-gray-600 hover:text-gray-900",children:"← Back to Missions"}),e.jsx("button",{onClick:()=>x(!d),className:"bg-[#88cc14]/10 text-[#88cc14] p-2 rounded-lg hover:bg-[#88cc14]/20 transition-colors",children:e.jsx(k,{className:"text-xl"})})]}),n?e.jsx(G,{}):e.jsxs(e.Fragment,{children:[e.jsx(U,{}),e.jsx(D,{}),d&&!M&&e.jsx(te,{phase:c[s].name,command:o==null?void 0:o.command}),e.jsxs("div",{className:"bg-black rounded-lg overflow-hidden",children:[e.jsxs("div",{className:"p-4 border-b border-gray-800",children:[e.jsxs("h2",{className:"text-xl font-bold text-white flex items-center gap-2",children:[e.jsx(C,{}),c[s].name]}),e.jsx("p",{className:"text-gray-400 mt-1",children:c[s].description})]}),e.jsx(se,{commands:c[s].commands,onCommandComplete:R})]})]}),e.jsxs(Q,{children:[L&&e.jsx(W,{}),H&&e.jsx(z,{}),F&&e.jsx(_,{})]})]})},ne=[{id:"server-breach",title:"Server Breach",description:"Break into a high-security server by exploiting vulnerabilities.",icon:C,difficulty:"Hard",points:500,tasks:[{description:"Run a port scan on the target server",command:"nmap -sV *************",hint:"Use nmap to discover open ports and services",points:20,expectedOutput:`Starting Nmap scan...
Port 22/tcp open ssh
Port 80/tcp open http`},{description:"Attempt SSH connection",command:"ssh admin@*************",hint:"Try connecting via SSH to see what authentication methods are allowed",points:20,expectedOutput:`SSH connection attempt...
Permission denied (publickey,password).
[!] Note: Server allows password authentication despite config
[!] Default credentials might work`},{description:"Brute force SSH password",command:"hydra -l admin -P /usr/share/wordlists/common.txt ssh://*************",hint:"Use Hydra to try common passwords",points:30,expectedOutput:`Hydra v9.1 (c) 2020 by van Hauser/THC & David Maciejak
[DATA] max 16 tasks per 1 server
[22][ssh] host: *************   login: admin   password: admin123
[STATUS] attack finished for ************* (valid pair found)`}]},{id:"web-exploit",title:"Web Application Exploit",description:"Exploit vulnerabilities in a web application to gain unauthorized access.",icon:Z,difficulty:"Medium",points:300,tasks:[{description:"Scan web application",command:"nikto -h http://*************",hint:"Use Nikto to discover web vulnerabilities",points:20,expectedOutput:`- Nikto v2.1.6
- Target IP: *************
- Web Server: Apache/2.4.41
[+] Multiple SQL injection vulnerabilities found
[+] Possible admin interface at /admin`}]},{id:"network-breach",title:"Network Infiltration",description:"Infiltrate a secured network and establish persistence.",icon:ee,difficulty:"Expert",points:750,tasks:[{description:"Network enumeration",command:"nmap -sn ***********/24",hint:"Map out the network topology",points:25,expectedOutput:`Starting Nmap scan...
Host *********** is up
Host ************* is up
Host ************* is up`}]}];function oe(){const[r,s]=i.useState(null),u=n=>{console.log(`Mission ${n} completed`),s(null)};return e.jsx("div",{className:"min-h-screen bg-cyber-black pt-20",children:r?e.jsx(ie,{mission:r,onComplete:()=>u(r.id),onBack:()=>s(null)}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsxs("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-6 relative inline-block",children:[e.jsx("span",{className:"relative z-10",children:"Choose Your Mission"}),e.jsx("div",{className:"absolute inset-0 bg-[#88cc14]/20 blur-xl -z-10"})]}),e.jsx("p",{className:"text-gray-400 text-lg max-w-2xl mx-auto",children:"Select your mission and demonstrate your hacking prowess. Each challenge tests different skills and techniques."})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto",children:ne.map((n,m)=>e.jsxs(l.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:m*.1},className:"bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-xl p-6 hover:border-[#88cc14] transition-all duration-300 group relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-[#88cc14]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e.jsxs("div",{className:"mb-6 relative",children:[e.jsx("div",{className:"w-16 h-16 rounded-lg bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(n.icon,{className:"text-[#88cc14] text-2xl"})}),e.jsx("div",{className:"absolute top-0 right-0",children:e.jsx("span",{className:`px-3 py-1 rounded-full text-xs font-bold ${n.difficulty==="Easy"?"bg-green-400/10 text-green-400":n.difficulty==="Medium"?"bg-yellow-400/10 text-yellow-400":"bg-red-400/10 text-red-400"}`,children:n.difficulty})})]}),e.jsx("h3",{className:"text-xl font-bold text-white mb-3 relative z-10",children:n.title}),e.jsx("p",{className:"text-gray-400 mb-6 h-24 relative z-10",children:n.description}),e.jsx("div",{className:"mb-6 relative z-10",children:e.jsxs("span",{className:"text-[#88cc14] font-bold",children:[n.points," points"]})}),e.jsxs("button",{onClick:()=>s(n),className:"w-full bg-black/50 text-[#88cc14] border border-[#88cc14]/30 font-bold py-3 px-4 rounded-lg group-hover:bg-[#88cc14] group-hover:text-black transition-all duration-300 relative z-10 flex items-center justify-center gap-2",children:[e.jsx(A,{className:"text-lg"}),e.jsx("span",{children:"Start Mission"})]})]},n.id))})]})})}export{oe as default};
