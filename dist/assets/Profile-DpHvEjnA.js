import{u as F,r as c,j as e,b as j,R as O,S as p,T as _,U as P,V as b,i as N,K as D,W as U,X as T,p as V,Y as W}from"./index-c6UceSOv.js";function B(){const f=F(),[t,y]=c.useState(null),[n,v]=c.useState(null),[x,w]=c.useState([]),[m,C]=c.useState([]),[S,E]=c.useState(!0),[h,g]=c.useState(null),[d,k]=c.useState("overview");c.useEffect(()=>{(async()=>{try{const[r,a,i,o]=await Promise.all([D().catch(l=>(console.error("Error fetching profile:",l),null)),U().catch(l=>(console.error("Error fetching coins:",l),{balance:0})),T().catch(l=>(console.error("Error fetching orders:",l),[])),V().catch(l=>(console.error("Error fetching challenges:",l),[]))]);y(r),v(a),w(i||[]),C(o||[])}catch(r){console.error("Error fetching user data:",r),g("Failed to load profile data. Please try again.")}finally{E(!1)}})()},[]);const u=async()=>{try{await W(),f("/login")}catch(s){console.error("Error signing out:",s),g("Failed to sign out. Please try again.")}};return S?e.jsx("div",{className:"min-h-screen bg-gray-50 pt-20 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"})}):h?e.jsx("div",{className:"min-h-screen bg-gray-50 pt-20 flex items-center justify-center",children:e.jsxs("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative max-w-md",children:[e.jsx("strong",{className:"font-bold",children:"Error: "}),e.jsx("span",{className:"block sm:inline",children:h}),e.jsxs("p",{className:"mt-2",children:["Please try ",e.jsx("button",{onClick:()=>window.location.reload(),className:"underline",children:"refreshing the page"}),"."]})]})}):t?e.jsx("div",{className:"min-h-screen bg-gray-50 pt-20",children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsx("div",{className:"md:col-span-1",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("div",{className:"w-24 h-24 rounded-full bg-[#88cc14]/10 flex items-center justify-center mx-auto mb-4",children:t!=null&&t.avatar_url?e.jsx("img",{src:t.avatar_url,alt:t.username,className:"w-full h-full rounded-full object-cover"}):e.jsx(j,{className:"text-[#88cc14] text-3xl"})}),e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:(t==null?void 0:t.username)||"User"}),e.jsx("p",{className:"text-gray-500",children:(t==null?void 0:t.email)||"<EMAIL>"})]}),e.jsx("nav",{className:"space-y-2",children:[{id:"overview",label:"Overview",icon:j},{id:"wallet",label:"Wallet",icon:O},{id:"orders",label:"Orders",icon:p},{id:"settings",label:"Settings",icon:_}].map(s=>e.jsxs("button",{onClick:()=>k(s.id),className:`w-full flex items-center gap-3 p-3 rounded-lg transition-colors ${d===s.id?"bg-[#88cc14]/10 text-[#88cc14]":"text-gray-600 hover:bg-gray-100"}`,children:[e.jsx(s.icon,{}),e.jsx("span",{children:s.label})]},s.id))}),e.jsxs("button",{onClick:u,className:"w-full mt-6 flex items-center gap-3 p-3 rounded-lg text-red-500 hover:bg-red-50 transition-colors",children:[e.jsx(P,{}),e.jsx("span",{children:"Sign Out"})]})]})}),e.jsxs("div",{className:"md:col-span-3",children:[d==="overview"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(b,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-gray-500",children:"Balance"}),e.jsxs("p",{className:"text-2xl font-bold text-[#88cc14]",children:[(n==null?void 0:n.balance)||0," XC"]})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(p,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-gray-500",children:"Orders"}),e.jsx("p",{className:"text-2xl font-bold text-[#88cc14]",children:x.length})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(N,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-gray-500",children:"Challenges"}),e.jsx("p",{className:"text-2xl font-bold text-[#88cc14]",children:m.filter(s=>s.status==="completed").length})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Recent Activity"}),m.length>0?e.jsx("div",{className:"space-y-4",children:m.slice(0,5).map((s,r)=>{var a;return e.jsxs("div",{className:"flex items-center gap-4 p-4 rounded-lg hover:bg-gray-50",children:[e.jsx(N,{className:s.status==="completed"?"text-[#88cc14]":"text-gray-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"font-medium text-gray-900",children:[s.status==="completed"?"Completed":"Started"," challenge: ",((a=s.challenges)==null?void 0:a.title)||"Challenge"]}),e.jsx("p",{className:"text-sm text-gray-500",children:new Date(s.submission_time).toLocaleString()})]}),s.status==="completed"&&s.points_earned>0&&e.jsxs("div",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-medium",children:["+",s.points_earned," points"]})]},r)})}):e.jsx("p",{className:"text-gray-500 text-center py-4",children:"No recent activity"})]})]}),d==="wallet"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Wallet"}),e.jsxs("div",{className:"flex items-center gap-4 p-6 bg-[#88cc14]/10 rounded-lg mb-6",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-[#88cc14]/20 flex items-center justify-center",children:e.jsx(b,{className:"text-[#88cc14] text-2xl"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:"Current Balance"}),e.jsxs("p",{className:"text-3xl font-bold text-[#88cc14]",children:[(n==null?void 0:n.balance)||0," XC"]})]})]}),e.jsx("p",{className:"text-gray-500 text-center",children:"Transaction history will appear here"})]}),d==="orders"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Orders"}),x.length===0?e.jsx("p",{className:"text-gray-500 text-center py-4",children:"No orders yet"}):e.jsx("div",{className:"space-y-4",children:x.map(s=>{var r;return e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium text-gray-900",children:["Order #",s.id.slice(0,8)]}),e.jsx("p",{className:"text-sm text-gray-500",children:new Date(s.created_at).toLocaleDateString()})]}),e.jsx("div",{className:`px-3 py-1 rounded-full text-sm ${s.status==="completed"?"bg-green-100 text-green-800":s.status==="pending"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:s.status})]}),e.jsx("div",{className:"space-y-2",children:s.order_items&&s.order_items.map(a=>{var i,o,l;return e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:((i=a.products)==null?void 0:i.image_url)||"https://via.placeholder.com/64",alt:((o=a.products)==null?void 0:o.name)||"Product",className:"w-16 h-16 object-cover rounded"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"font-medium text-gray-900",children:((l=a.products)==null?void 0:l.name)||"Product"}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Quantity: ",a.quantity]})]}),e.jsxs("p",{className:"font-medium text-gray-900",children:["₹",(a.price*a.quantity).toFixed(2)]})]},a.id)})}),e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"font-medium text-gray-900",children:"Total"}),e.jsxs("span",{className:"font-bold text-[#88cc14]",children:["₹",((r=s.total_amount)==null?void 0:r.toFixed(2))||"0.00"]})]})})]},s.id)})})]}),d==="settings"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Settings"}),e.jsxs("form",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),e.jsx("input",{type:"text",defaultValue:(t==null?void 0:t.username)||"",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),e.jsx("input",{type:"text",defaultValue:(t==null?void 0:t.full_name)||"",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",defaultValue:(t==null?void 0:t.email)||"",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]",disabled:!0}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Email cannot be changed"})]}),e.jsx("button",{type:"button",className:"w-full bg-[#88cc14] text-black font-bold py-3 px-4 rounded-lg hover:bg-[#7ab811] transition-colors",children:"Save Changes"})]})]})]})]})})}):e.jsx("div",{className:"min-h-screen bg-gray-50 pt-20 flex items-center justify-center",children:e.jsxs("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative max-w-md",children:[e.jsx("strong",{className:"font-bold",children:"Error: "}),e.jsx("span",{className:"block sm:inline",children:"Could not load profile data."}),e.jsxs("p",{className:"mt-2",children:["Please try ",e.jsx("button",{onClick:()=>window.location.reload(),className:"underline",children:"refreshing the page"})," or ",e.jsx("button",{onClick:u,className:"underline",children:"signing out"})," and back in."]})]})})}export{B as default};
