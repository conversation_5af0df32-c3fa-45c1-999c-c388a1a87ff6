import{au as w,r,j as e,aM as j,av as M,E as A,bB as p,x as P,bK as R,d as E}from"./index-c6UceSOv.js";import{t as k}from"./ThreatAnalyticsDashboard-CJk_8dys.js";const K=()=>{const{darkMode:o}=w(),[d,n]=r.useState(!0),[m,h]=r.useState(null),[x,c]=r.useState([]),[l,y]=r.useState(null),[u,g]=r.useState(!1),T=r.useRef(null),v={Critical:"#ef4444",High:"#f97316",Medium:"#eab308",Low:"#22c55e",Unknown:"#6b7280"};r.useEffect(()=>{(async()=>{try{n(!0),h(null),await k.initialize();const s=await k.generateThreatReport({title:"Country Risk Assessment"});if(s&&s.geoAnalysis&&s.geoAnalysis.topCountries){const a=s.geoAnalysis.topCountries.map(i=>({name:i.country,count:i.count,riskScore:Math.round(i.avgRiskScore),riskLevel:b(Math.round(i.avgRiskScore)),threatActors:N(i.country),commonTactics:C(),recentActivity:Math.random()>.5?"Increasing":"Stable"})).sort((i,L)=>L.riskScore-i.riskScore);c(a)}else{const a=f();c(a)}n(!1)}catch(s){console.error("Error loading country risk data:",s),h("Unable to load country risk data. Using sample data instead.");const a=f();c(a),n(!1)}})()},[]);const b=t=>t>=75?"Critical":t>=50?"High":t>=25?"Medium":"Low",N=t=>({Russia:["APT28","APT29","Sandworm"],China:["APT1","APT10","APT41"],"North Korea":["Lazarus Group","APT38","Kimsuky"],Iran:["APT33","APT35","MuddyWater"],"United States":["Equation Group","TAO"],Israel:["Unit 8200","ISNU"],"United Kingdom":["GCHQ","JTRIG"],India:["Sidewinder","Confucius"],Pakistan:["Transparent Tribe","APT36"],Vietnam:["APT32","OceanLotus"],Turkey:["Sea Turtle","StrongPity"],Brazil:["Bahamut"],Germany:["Snake"],France:["Animal Farm"],Australia:["Vixen Panda"]})[t]||["Unknown Actor","Mercenary Group","Hacktivist"].slice(0,Math.floor(Math.random()*2)+1),C=()=>["Initial Access","Execution","Persistence","Privilege Escalation","Defense Evasion","Credential Access","Discovery","Lateral Movement","Collection","Command and Control","Exfiltration","Impact"].sort(()=>.5-Math.random()).slice(0,Math.floor(Math.random()*3)+2),f=()=>[{name:"Russia",count:245,riskScore:87,riskLevel:"Critical",threatActors:["APT28","APT29","Sandworm"],commonTactics:["Initial Access","Defense Evasion","Impact"],recentActivity:"Increasing"},{name:"China",count:198,riskScore:82,riskLevel:"Critical",threatActors:["APT1","APT10","APT41"],commonTactics:["Reconnaissance","Data Collection","Persistence"],recentActivity:"Stable"},{name:"North Korea",count:112,riskScore:79,riskLevel:"Critical",threatActors:["Lazarus Group","APT38"],commonTactics:["Initial Access","Credential Access","Exfiltration"],recentActivity:"Increasing"},{name:"Iran",count:156,riskScore:76,riskLevel:"Critical",threatActors:["APT33","APT35"],commonTactics:["Initial Access","Execution","Command and Control"],recentActivity:"Increasing"},{name:"United States",count:134,riskScore:65,riskLevel:"High",threatActors:["Equation Group"],commonTactics:["Defense Evasion","Privilege Escalation"],recentActivity:"Stable"},{name:"Vietnam",count:87,riskScore:58,riskLevel:"High",threatActors:["APT32","OceanLotus"],commonTactics:["Phishing","Lateral Movement"],recentActivity:"Stable"},{name:"Brazil",count:76,riskScore:42,riskLevel:"Medium",threatActors:["Bahamut"],commonTactics:["Initial Access","Persistence"],recentActivity:"Decreasing"},{name:"India",count:65,riskScore:38,riskLevel:"Medium",threatActors:["Sidewinder"],commonTactics:["Phishing","Data Collection"],recentActivity:"Stable"},{name:"Germany",count:54,riskScore:32,riskLevel:"Medium",threatActors:["Unknown Actor"],commonTactics:["Reconnaissance","Initial Access"],recentActivity:"Stable"},{name:"France",count:48,riskScore:28,riskLevel:"Medium",threatActors:["Animal Farm"],commonTactics:["Phishing","Credential Access"],recentActivity:"Decreasing"},{name:"United Kingdom",count:42,riskScore:25,riskLevel:"Medium",threatActors:["JTRIG"],commonTactics:["Defense Evasion","Command and Control"],recentActivity:"Stable"},{name:"Australia",count:36,riskScore:18,riskLevel:"Low",threatActors:["Vixen Panda"],commonTactics:["Reconnaissance"],recentActivity:"Stable"}],S=t=>{switch(t){case"Increasing":return e.jsx("span",{className:"text-red-500",children:"↑"});case"Decreasing":return e.jsx("span",{className:"text-green-500",children:"↓"});default:return e.jsx("span",{className:"text-yellow-500",children:"→"})}},I=t=>{y(t===l?null:t)};return e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[e.jsx(j,{className:"mr-2 text-blue-400"})," Country Risk Matrix",e.jsx("button",{className:"ml-2 text-gray-400 hover:text-gray-300",onClick:()=>g(!u),title:"MITRE ATT&CK Information",children:e.jsx(M,{size:14})})]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["Updated ",new Date().toLocaleString()]})]}),u&&e.jsxs("div",{className:"mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[e.jsx(A,{className:"mr-1 text-blue-400"})," MITRE ATT&CK Framework Integration"]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>g(!1),children:"×"})]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"This matrix correlates country-based threat actors with common tactics from the MITRE ATT&CK framework, helping you understand and defend against specific threats."}),e.jsx("div",{className:"flex items-center text-xs",children:e.jsxs("a",{href:"https://attack.mitre.org/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 flex items-center",children:["Learn more about MITRE ATT&CK ",e.jsx(p,{className:"ml-1",size:10})]})})]}),d&&e.jsx("div",{className:"flex-1 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"})}),m&&e.jsxs("div",{className:"mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm",children:[e.jsx(P,{className:"inline-block mr-2"}),m]}),!d&&x.length>0&&e.jsx("div",{className:"flex-1 overflow-auto",ref:T,children:e.jsxs("div",{className:"min-w-full divide-y divide-gray-700",children:[e.jsx("div",{className:"bg-gray-800 sticky top-0 z-10",children:e.jsxs("div",{className:"grid grid-cols-12 gap-2 py-2 px-3 text-xs font-medium text-gray-300 uppercase tracking-wider",children:[e.jsx("div",{className:"col-span-3",children:"Country"}),e.jsx("div",{className:"col-span-2 text-center",children:"Risk Score"}),e.jsx("div",{className:"col-span-2 text-center",children:"Risk Level"}),e.jsx("div",{className:"col-span-2 text-center",children:"Threat Count"}),e.jsx("div",{className:"col-span-3 text-center",children:"Trend"})]})}),e.jsx("div",{className:"divide-y divide-gray-700",children:x.map(t=>e.jsxs(R.Fragment,{children:[e.jsxs("div",{className:`grid grid-cols-12 gap-2 py-3 px-3 text-sm cursor-pointer transition-colors ${l===t?o?"bg-gray-700":"bg-gray-600":o?"hover:bg-gray-700":"hover:bg-gray-600"}`,onClick:()=>I(t),children:[e.jsxs("div",{className:"col-span-3 flex items-center",children:[e.jsx(j,{className:"mr-2 text-gray-400"})," ",t.name]}),e.jsxs("div",{className:"col-span-2 text-center",children:[t.riskScore,"/100"]}),e.jsx("div",{className:"col-span-2 text-center",children:e.jsx("span",{className:"px-2 py-1 rounded-full text-xs font-medium",style:{backgroundColor:`${v[t.riskLevel]}20`,color:v[t.riskLevel]},children:t.riskLevel})}),e.jsx("div",{className:"col-span-2 text-center",children:t.count}),e.jsxs("div",{className:"col-span-3 text-center flex items-center justify-center",children:[S(t.recentActivity),e.jsx("span",{className:"ml-1",children:t.recentActivity})]})]}),l===t&&e.jsxs("div",{className:"py-4 px-6 bg-gray-800 text-sm",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Known Threat Actors"}),e.jsx("div",{className:"space-y-1",children:t.threatActors.map((s,a)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx(E,{className:"mr-2 text-red-400"})," ",s]},a))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Common MITRE ATT&CK Tactics"}),e.jsx("div",{className:"space-y-1",children:t.commonTactics.map((s,a)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx(A,{className:"mr-2 text-blue-400"})," ",s]},a))})]})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Recommended Security Controls"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 text-gray-300",children:[t.riskLevel==="Critical"&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Implement geo-blocking for non-essential traffic from this region"}),e.jsx("li",{children:"Enable enhanced logging and monitoring for traffic from this region"}),e.jsx("li",{children:"Deploy advanced threat protection solutions focused on tactics used by these actors"})]}),t.riskLevel==="High"&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Consider selective geo-filtering for sensitive systems"}),e.jsx("li",{children:"Implement additional authentication for traffic from this region"}),e.jsx("li",{children:"Monitor for specific IOCs associated with threat actors from this region"})]}),t.riskLevel==="Medium"&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Implement standard security controls with region-specific monitoring"}),e.jsx("li",{children:"Review logs for suspicious activity from this region"}),e.jsx("li",{children:"Stay updated on threat intelligence related to this region"})]}),t.riskLevel==="Low"&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Apply standard security controls"}),e.jsx("li",{children:"Include in regular security monitoring"}),e.jsx("li",{children:"No special measures required at this time"})]})]})]}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsxs("button",{className:"text-blue-400 hover:text-blue-300 text-xs flex items-center",onClick:()=>window.open("https://attack.mitre.org/","_blank"),children:["Learn more about these threats ",e.jsx(p,{className:"ml-1",size:10})]})})]})]},t.name))})]})}),e.jsxs("div",{className:"mt-4 text-xs text-gray-400",children:[e.jsx("strong",{children:"Data Sources:"})," Threat intelligence APIs and MITRE ATT&CK framework •",e.jsx("strong",{children:"Analysis:"})," Based on observed attack patterns and actor attribution"]})]})};export{K as default};
