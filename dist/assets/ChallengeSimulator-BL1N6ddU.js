import{au as $,r as n,j as e,C as D,d as B,w as N,n as M,x as O,av as T,H as P,I as R,bC as G,bD as W,ba as Y,u as U,aC as I,i as F}from"./index-c6UceSOv.js";const z=({onComplete:a})=>{const{darkMode:t}=$(),[l,i]=n.useState(""),[m,h]=n.useState(""),[x,g]=n.useState(0),[o,j]=n.useState(null),[c,v]=n.useState(!1),[k,H]=n.useState(!1),[b,L]=n.useState(0),[A]=n.useState(new Date),[S,E]=n.useState([{type:"info",text:"SQL Injection Challenge initialized..."},{type:"info",text:"Target: BankSecure Login Portal"},{type:"info",text:"Objective: Bypass authentication without valid credentials"},{type:"system",text:"Type your SQL injection payload in the username field."}]),C=(s,u)=>{const r=`SELECT * FROM users WHERE username='${s}' AND password='${u}'`;return p("query",`Executing: ${r}`),s.includes("'")||s.includes('"')?s.toLowerCase().includes("' or '1'='1")||s.toLowerCase().includes("' or 1=1")||s.toLowerCase().includes('" or "1"="1')||s.toLowerCase().includes('" or 1=1')||s.toLowerCase().includes("' or '1'='1'--")||s.toLowerCase().includes("' or 1=1--")||s.toLowerCase().includes("admin'--")?{success:!0,query:r}:{success:!1,error:"SQL syntax error",query:r}:{success:!1,error:"Invalid credentials",query:r}},p=(s,u)=>{E(r=>[...r,{type:s,text:u}]),setTimeout(()=>{const r=document.getElementById("sql-console");r&&(r.scrollTop=r.scrollHeight)},100)},d=s=>{s.preventDefault(),g(u=>u+1),p("system","Sending login request..."),setTimeout(()=>{const u=C(l,m);if(u.success){v(!0),j({type:"success",text:"Authentication bypassed successfully! You have exploited the SQL injection vulnerability."}),p("success","Authentication bypassed! Access granted to admin account."),p("system","Flag captured: flag{sql_injection_master_2023}");const y=Math.floor((new Date-A)/1e3),f=Math.floor(y/60),_=y%60,q=`${f.toString().padStart(2,"0")}:${_.toString().padStart(2,"0")}`;a&&a({success:!0,flag:"flag{sql_injection_master_2023}",attempts:x+1,timeSpent:q})}else u.error==="SQL syntax error"?(j({type:"warning",text:"SQL syntax error in your injection attempt. You're on the right track!"}),p("error",'Database returned: SQL syntax error near "'+l+'"')):(j({type:"error",text:"Login failed: Invalid credentials"}),p("error","Authentication failed: Invalid username or password"))},800)},w=()=>{L(u=>u+1),H(!0),p("system","Hint requested...");const s=["Try entering special characters like a single quote (') in the username field to see how the application responds.","The login query might look like: SELECT * FROM users WHERE username='[input]' AND password='[input]'","Using a statement like ' OR '1'='1 might help you bypass the authentication by making the WHERE clause always true.","Try this payload in the username field: ' OR '1'='1'-- (The -- comments out the rest of the query)"];b<s.length&&p("hint",s[b])};return n.useEffect(()=>{const s=document.getElementById("sql-console");s&&(s.scrollTop=s.scrollHeight)},[S]),e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${t?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[c?e.jsx(D,{className:"text-green-500 mr-2"}):e.jsx(B,{className:"text-red-500 mr-2"}),"SQL Injection Challenge"]}),e.jsx("div",{children:e.jsxs("button",{onClick:w,className:`px-3 py-1 rounded-lg flex items-center ${t?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,children:[e.jsx(N,{className:"mr-1 text-yellow-500"})," Get Hint"]})})]}),e.jsxs("div",{className:"p-6",children:[o&&e.jsx("div",{className:`mb-4 p-3 rounded-lg ${o.type==="success"?t?"bg-green-900/20 text-green-300 border border-green-900/30":"bg-green-100 text-green-800 border border-green-200":o.type==="warning"?t?"bg-yellow-900/20 text-yellow-300 border border-yellow-900/30":"bg-yellow-100 text-yellow-800 border border-yellow-200":t?"bg-red-900/20 text-red-300 border border-red-900/30":"bg-red-100 text-red-800 border border-red-200"}`,children:e.jsxs("div",{className:"flex items-start",children:[o.type==="success"?e.jsx(M,{className:"mt-1 mr-2"}):o.type==="warning"?e.jsx(O,{className:"mt-1 mr-2"}):e.jsx(T,{className:"mt-1 mr-2"}),e.jsx("div",{children:o.text})]})}),e.jsxs("div",{className:`mb-6 p-4 rounded-lg ${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsx("h3",{className:"font-bold mb-2",children:"Challenge Scenario:"}),e.jsx("p",{className:`${t?"text-gray-300":"text-gray-700"} mb-4`,children:"BankSecure is a financial institution that prides itself on security. However, their web developers have made a critical mistake in their login form. Your goal is to exploit this vulnerability to gain unauthorized access."}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(T,{className:`mr-2 ${t?"text-blue-400":"text-blue-600"}`}),e.jsx("p",{className:`${t?"text-gray-400":"text-gray-600"} text-sm`,children:"SQL injection is one of the most common web application vulnerabilities. It occurs when user input is incorrectly filtered and directly included in SQL queries."})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:`p-4 rounded-lg ${t?"bg-[#0F172A] border-gray-800":"bg-white border-gray-300"} border`,children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h3",{className:"text-xl font-bold",children:"BankSecure Login Portal"}),e.jsx("p",{className:`text-sm ${t?"text-gray-400":"text-gray-600"}`,children:"Secure Banking for Everyone"})]}),e.jsxs("form",{onSubmit:d,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:`block mb-2 text-sm font-medium ${t?"text-gray-300":"text-gray-700"}`,children:"Username"}),e.jsx("input",{type:"text",value:l,onChange:s=>i(s.target.value),className:`w-full p-2.5 rounded-lg ${t?"bg-gray-800 border-gray-700 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border`,placeholder:"Enter username",disabled:c})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:`block mb-2 text-sm font-medium ${t?"text-gray-300":"text-gray-700"}`,children:"Password"}),e.jsx("input",{type:"password",value:m,onChange:s=>h(s.target.value),className:`w-full p-2.5 rounded-lg ${t?"bg-gray-800 border-gray-700 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border`,placeholder:"Enter password",disabled:c})]}),e.jsx("button",{type:"submit",className:`w-full py-2.5 px-5 rounded-lg ${c?"bg-green-600 hover:bg-green-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,disabled:c,children:c?"Access Granted":"Login"})]})]})}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:`text-lg font-medium mb-2 ${t?"text-gray-300":"text-gray-700"}`,children:"Console Output:"}),e.jsx("div",{id:"sql-console",className:`h-64 overflow-y-auto p-3 rounded-lg font-mono text-sm ${t?"bg-black text-green-400":"bg-gray-900 text-green-400"}`,children:S.map((s,u)=>e.jsxs("div",{className:`mb-1 ${s.type==="error"?"text-red-400":s.type==="success"?"text-green-400":s.type==="query"?"text-yellow-400":s.type==="hint"?"text-blue-400":s.type==="system"?"text-purple-400":"text-green-400"}`,children:[s.type==="error"&&"[-] ",s.type==="success"&&"[+] ",s.type==="query"&&"[>] ",s.type==="hint"&&"[?] ",s.type==="system"&&"[*] ",s.type==="info"&&"[i] ",s.text]},u))})]}),k&&e.jsx("div",{className:`mt-4 p-4 rounded-lg ${t?"bg-yellow-900/20 border-yellow-900/30":"bg-yellow-50 border-yellow-200"} border`,children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(N,{className:`mt-1 mr-2 ${t?"text-yellow-400":"text-yellow-600"}`}),e.jsxs("div",{children:[e.jsxs("h4",{className:`font-medium ${t?"text-yellow-300":"text-yellow-800"}`,children:["Hint ",b,":"]}),b===1&&e.jsx("p",{className:t?"text-yellow-200":"text-yellow-700",children:"Try entering special characters like a single quote (') in the username field to see how the application responds."}),b===2&&e.jsx("p",{className:t?"text-yellow-200":"text-yellow-700",children:"The login query might look like: SELECT * FROM users WHERE username='[input]' AND password='[input]'"}),b===3&&e.jsx("p",{className:t?"text-yellow-200":"text-yellow-700",children:"Using a statement like ' OR '1'='1 might help you bypass the authentication by making the WHERE clause always true."}),b===4&&e.jsx("p",{className:t?"text-yellow-200":"text-yellow-700",children:"Try this payload in the username field: ' OR '1'='1'-- (The -- comments out the rest of the query)"})]})]})}),c&&e.jsxs("div",{className:`mt-6 p-4 rounded-lg ${t?"bg-green-900/20 border-green-900/30":"bg-green-100 border-green-200"} border`,children:[e.jsx("h3",{className:`text-lg font-bold mb-2 ${t?"text-green-300":"text-green-800"}`,children:"Challenge Completed!"}),e.jsx("p",{className:`mb-3 ${t?"text-green-200":"text-green-700"}`,children:"Congratulations! You've successfully exploited the SQL injection vulnerability in the login form."}),e.jsxs("div",{className:`p-3 rounded ${t?"bg-black":"bg-gray-900"} font-mono text-green-400 mb-3`,children:["Flag: flag","{sql_injection_master_2023}"]}),e.jsx("h4",{className:`font-medium mb-2 ${t?"text-green-300":"text-green-800"}`,children:"What you learned:"}),e.jsxs("ul",{className:`list-disc pl-5 ${t?"text-green-200":"text-green-700"}`,children:[e.jsx("li",{children:"How SQL injection vulnerabilities occur in web applications"}),e.jsx("li",{children:"How to identify and exploit vulnerable input fields"}),e.jsx("li",{children:"The importance of parameterized queries to prevent SQL injection"}),e.jsx("li",{children:"How to bypass authentication using SQL injection techniques"})]})]})]})]})},V=({onComplete:a})=>{const{darkMode:t}=$(),[l,i]=n.useState(0),[m,h]=n.useState(""),[x,g]=n.useState(0),[o,j]=n.useState(null),[c,v]=n.useState(!1),[k,H]=n.useState(!1),[b,L]=n.useState(0),[A]=n.useState(new Date),[S,E]=n.useState([]),[C,p]=n.useState([{type:"info",text:"Password Cracking Challenge initialized..."},{type:"info",text:"Target: Password Hash Database"},{type:"info",text:"Objective: Crack all three password hashes"},{type:"system",text:"Loading hash #1: 5f4dcc3b5aa765d61d8327deb882cf99 (MD5)"}]),d=[{hash:"5f4dcc3b5aa765d61d8327deb882cf99",type:"MD5",password:"password",hint1:"This is one of the most common passwords used.",hint2:`It's literally the word "password".`},{hash:"5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8",type:"SHA-1",password:"password1",hint1:"This password is similar to the first one, but with a number added.",hint2:'Try adding a "1" to the end of the first password.'},{hash:"$2y$10$F9hXnLl7IVhMJ.Zyj0jGAO38BXCxV/N4NLnLVVEx7hHQmWkr0qcp.",type:"bcrypt",password:"security42",hint1:"This is a common word related to this field of study, followed by a two-digit number.",hint2:`The word is "security" and the number is the "Answer to the Ultimate Question of Life, the Universe, and Everything" in The Hitchhiker's Guide to the Galaxy.`}],w=(r,y)=>{p(f=>[...f,{type:r,text:y}]),setTimeout(()=>{const f=document.getElementById("password-console");f&&(f.scrollTop=f.scrollHeight)},100)},s=r=>{r.preventDefault(),g(y=>y+1),w("system",`Attempting to crack ${d[l].type} hash with password: ${m}`),setTimeout(()=>{if(m.toLowerCase()===d[l].password.toLowerCase())if(w("success",`Hash cracked! Password: ${m}`),E(y=>[...y,m]),l<d.length-1)i(y=>y+1),h(""),w("system",`Loading hash #${l+2}: ${d[l+1].hash} (${d[l+1].type})`),j({type:"success",text:`Great job! You've cracked hash #${l+1}. Now try the next one.`});else{v(!0),j({type:"success",text:"Congratulations! You've cracked all the password hashes!"});const f=Math.floor((new Date-A)/1e3),_=Math.floor(f/60),q=f%60,Q=`${_.toString().padStart(2,"0")}:${q.toString().padStart(2,"0")}`;a&&a({success:!0,flag:`flag{${S.join("_")}_master_cracker}`,attempts:x+1,timeSpent:Q})}else w("error","Incorrect password. Try again."),j({type:"error",text:"Incorrect password. Try again."})},800)},u=()=>{L(r=>r+1),H(!0),w("system","Hint requested..."),b%2===0?w("hint",d[l].hint1):w("hint",d[l].hint2)};return n.useEffect(()=>{const r=document.getElementById("password-console");r&&(r.scrollTop=r.scrollHeight)},[C]),e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${t?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[c?e.jsx(D,{className:"text-green-500 mr-2"}):e.jsx(B,{className:"text-red-500 mr-2"}),"Password Cracking Challenge"]}),e.jsx("div",{children:e.jsxs("button",{onClick:u,className:`px-3 py-1 rounded-lg flex items-center ${t?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,children:[e.jsx(N,{className:"mr-1 text-yellow-500"})," Get Hint"]})})]}),e.jsxs("div",{className:"p-6",children:[o&&e.jsx("div",{className:`mb-4 p-3 rounded-lg ${o.type==="success"?t?"bg-green-900/20 text-green-300 border border-green-900/30":"bg-green-100 text-green-800 border border-green-200":o.type==="warning"?t?"bg-yellow-900/20 text-yellow-300 border border-yellow-900/30":"bg-yellow-100 text-yellow-800 border border-yellow-200":t?"bg-red-900/20 text-red-300 border border-red-900/30":"bg-red-100 text-red-800 border border-red-200"}`,children:e.jsxs("div",{className:"flex items-start",children:[o.type==="success"?e.jsx(M,{className:"mt-1 mr-2"}):o.type==="warning"?e.jsx(O,{className:"mt-1 mr-2"}):e.jsx(T,{className:"mt-1 mr-2"}),e.jsx("div",{children:o.text})]})}),e.jsxs("div",{className:`mb-6 p-4 rounded-lg ${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsx("h3",{className:"font-bold mb-2",children:"Challenge Scenario:"}),e.jsx("p",{className:`${t?"text-gray-300":"text-gray-700"} mb-4`,children:"You have obtained a file containing password hashes from a security breach. Your task is to crack these hashes to reveal the original passwords."}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(T,{className:`mr-2 ${t?"text-blue-400":"text-blue-600"}`}),e.jsx("p",{className:`${t?"text-gray-400":"text-gray-600"} text-sm`,children:"Password cracking is the process of recovering passwords from data that has been stored in or transmitted in a hashed form."})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:`p-4 rounded-lg ${t?"bg-[#0F172A] border-gray-800":"bg-white border-gray-300"} border`,children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h3",{className:"text-xl font-bold",children:"Password Hash Cracker"}),e.jsxs("p",{className:`text-sm ${t?"text-gray-400":"text-gray-600"}`,children:["Hash ",l+1," of ",d.length,": ",d[l].type]})]}),e.jsx("div",{className:`p-3 mb-4 rounded ${t?"bg-black":"bg-gray-900"} font-mono text-green-400 overflow-x-auto`,children:d[l].hash}),e.jsxs("form",{onSubmit:s,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:`block mb-2 text-sm font-medium ${t?"text-gray-300":"text-gray-700"}`,children:"Password Guess"}),e.jsx("input",{type:"text",value:m,onChange:r=>h(r.target.value),className:`w-full p-2.5 rounded-lg ${t?"bg-gray-800 border-gray-700 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border`,placeholder:"Enter password guess",disabled:c})]}),e.jsx("button",{type:"submit",className:`w-full py-2.5 px-5 rounded-lg ${c?"bg-green-600 hover:bg-green-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,disabled:c,children:c?"All Hashes Cracked!":"Crack Hash"})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:"Progress"}),e.jsxs("span",{children:[Math.round(S.length/d.length*100),"%"]})]}),e.jsx("div",{className:`h-2 w-full rounded-full ${t?"bg-gray-800":"bg-gray-200"}`,children:e.jsx("div",{className:"h-2 rounded-full bg-green-500",style:{width:`${S.length/d.length*100}%`}})})]})]})}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:`text-lg font-medium mb-2 ${t?"text-gray-300":"text-gray-700"}`,children:"Console Output:"}),e.jsx("div",{id:"password-console",className:`h-64 overflow-y-auto p-3 rounded-lg font-mono text-sm ${t?"bg-black text-green-400":"bg-gray-900 text-green-400"}`,children:C.map((r,y)=>e.jsxs("div",{className:`mb-1 ${r.type==="error"?"text-red-400":r.type==="success"?"text-green-400":r.type==="query"?"text-yellow-400":r.type==="hint"?"text-blue-400":r.type==="system"?"text-purple-400":"text-green-400"}`,children:[r.type==="error"&&"[-] ",r.type==="success"&&"[+] ",r.type==="query"&&"[>] ",r.type==="hint"&&"[?] ",r.type==="system"&&"[*] ",r.type==="info"&&"[i] ",r.text]},y))})]}),k&&e.jsx("div",{className:`mt-4 p-4 rounded-lg ${t?"bg-yellow-900/20 border-yellow-900/30":"bg-yellow-50 border-yellow-200"} border`,children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(N,{className:`mt-1 mr-2 ${t?"text-yellow-400":"text-yellow-600"}`}),e.jsxs("div",{children:[e.jsxs("h4",{className:`font-medium ${t?"text-yellow-300":"text-yellow-800"}`,children:["Hint ",b,":"]}),e.jsx("p",{className:t?"text-yellow-200":"text-yellow-700",children:b%2===1?d[l].hint1:d[l].hint2})]})]})}),c&&e.jsxs("div",{className:`mt-6 p-4 rounded-lg ${t?"bg-green-900/20 border-green-900/30":"bg-green-100 border-green-200"} border`,children:[e.jsx("h3",{className:`text-lg font-bold mb-2 ${t?"text-green-300":"text-green-800"}`,children:"Challenge Completed!"}),e.jsx("p",{className:`mb-3 ${t?"text-green-200":"text-green-700"}`,children:"Congratulations! You've successfully cracked all the password hashes."}),e.jsxs("div",{className:`p-3 rounded ${t?"bg-black":"bg-gray-900"} font-mono text-green-400 mb-3`,children:["Flag: flag","{"+S.join("_")+"_master_cracker}"]}),e.jsx("h4",{className:`font-medium mb-2 ${t?"text-green-300":"text-green-800"}`,children:"What you learned:"}),e.jsxs("ul",{className:`list-disc pl-5 ${t?"text-green-200":"text-green-700"}`,children:[e.jsx("li",{children:"How to identify different types of password hashes"}),e.jsx("li",{children:"Techniques for cracking password hashes of varying complexity"}),e.jsx("li",{children:"The importance of strong, unique passwords"}),e.jsx("li",{children:"Why modern hashing algorithms like bcrypt are more secure than older ones like MD5"})]})]})]})]})},X=({onComplete:a})=>{const{darkMode:t}=$(),[l,i]=n.useState(!1),[m,h]=n.useState(0),x=()=>{h(g=>g+1),i(!0)};return e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${t?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(P,{className:"text-blue-500 mr-2"}),"Network Traffic Analysis Challenge"]}),e.jsx("div",{children:e.jsxs("button",{onClick:x,className:`px-3 py-1 rounded-lg flex items-center ${t?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,children:[e.jsx(N,{className:"mr-1 text-yellow-500"})," Get Hint"]})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:`p-8 rounded-lg ${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border text-center`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"Network Traffic Analysis Simulator"}),e.jsx("p",{className:"mb-6",children:"This challenge is coming soon! Check back later for a fully interactive network traffic analysis simulation."}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{onClick:()=>{a&&a({success:!0,flag:"flag{network_analysis_expert}",attempts:1,timeSpent:"10:00"})},className:"px-6 py-3 theme-button-primary rounded-lg",children:"Simulate Completion"})})]})})]})},J=({onComplete:a})=>{const{darkMode:t}=$(),[l,i]=n.useState(!1),[m,h]=n.useState(0),x=()=>{h(g=>g+1),i(!0)};return e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${t?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(R,{className:"text-purple-500 mr-2"}),"OSINT Investigation Challenge"]}),e.jsx("div",{children:e.jsxs("button",{onClick:x,className:`px-3 py-1 rounded-lg flex items-center ${t?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,children:[e.jsx(N,{className:"mr-1 text-yellow-500"})," Get Hint"]})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:`p-8 rounded-lg ${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border text-center`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"OSINT Investigation Simulator"}),e.jsx("p",{className:"mb-6",children:"This challenge is coming soon! Check back later for a fully interactive OSINT investigation simulation."}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{onClick:()=>{a&&a({success:!0,flag:"flag{osint_investigation_complete}",attempts:1,timeSpent:"15:30"})},className:"px-6 py-3 theme-button-primary rounded-lg",children:"Simulate Completion"})})]})})]})},Z=({onComplete:a})=>{const{darkMode:t}=$(),[l,i]=n.useState(!1),[m,h]=n.useState(0),x=()=>{h(g=>g+1),i(!0)};return e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${t?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(G,{className:"text-green-500 mr-2"}),"Steganography Challenge"]}),e.jsx("div",{children:e.jsxs("button",{onClick:x,className:`px-3 py-1 rounded-lg flex items-center ${t?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,children:[e.jsx(N,{className:"mr-1 text-yellow-500"})," Get Hint"]})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:`p-8 rounded-lg ${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border text-center`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"Steganography Simulator"}),e.jsx("p",{className:"mb-6",children:"This challenge is coming soon! Check back later for a fully interactive steganography simulation."}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{onClick:()=>{a&&a({success:!0,flag:"flag{hidden_message_found}",attempts:1,timeSpent:"12:45"})},className:"px-6 py-3 theme-button-primary rounded-lg",children:"Simulate Completion"})})]})})]})},K=({onComplete:a})=>{const{darkMode:t}=$(),[l,i]=n.useState(!1),[m,h]=n.useState(0),x=()=>{h(g=>g+1),i(!0)};return e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${t?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(W,{className:"text-red-500 mr-2"}),"Binary Analysis Challenge"]}),e.jsx("div",{children:e.jsxs("button",{onClick:x,className:`px-3 py-1 rounded-lg flex items-center ${t?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,children:[e.jsx(N,{className:"mr-1 text-yellow-500"})," Get Hint"]})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:`p-8 rounded-lg ${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border text-center`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"Binary Analysis Simulator"}),e.jsx("p",{className:"mb-6",children:"This challenge is coming soon! Check back later for a fully interactive binary analysis simulation."}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{onClick:()=>{a&&a({success:!0,flag:"flag{binary_analysis_complete}",attempts:1,timeSpent:"18:20"})},className:"px-6 py-3 theme-button-primary rounded-lg",children:"Simulate Completion"})})]})})]})},ee=[{id:"c1",slug:"sql-injection-basics",title:"SQL Injection Basics",description:"Learn the fundamentals of SQL injection by exploiting a vulnerable login form. This challenge introduces you to one of the most common web application vulnerabilities.",category:{name:"Web Security"},difficulty:{name:"Beginner"},type:{name:"Exploitation"},points:100,coin_reward:10,estimated_time:20,simulator:z},{id:"c2",slug:"password-cracking-basics",title:"Password Cracking Basics",description:"Learn the fundamentals of password cracking by breaking a series of increasingly complex password hashes. This challenge introduces you to common password cracking tools and techniques.",category:{name:"Cryptography"},difficulty:{name:"Beginner"},type:{name:"Analysis"},points:100,coin_reward:10,estimated_time:25,simulator:V},{id:"c3",slug:"network-traffic-analysis",title:"Network Traffic Analysis",description:"Analyze captured network traffic to identify suspicious activities and extract hidden information. This challenge introduces you to packet analysis and network forensics.",category:{name:"Network Security"},difficulty:{name:"Easy"},type:{name:"Analysis"},points:150,coin_reward:15,estimated_time:30,simulator:X},{id:"c4",slug:"osint-investigation",title:"OSINT Investigation",description:"Use Open Source Intelligence techniques to gather information about a fictional target. This challenge introduces you to the power of publicly available information.",category:{name:"OSINT"},difficulty:{name:"Easy"},type:{name:"Reconnaissance"},points:150,coin_reward:15,estimated_time:35,simulator:J},{id:"c5",slug:"basic-steganography",title:"Basic Steganography",description:"Discover hidden messages concealed within digital images. This challenge introduces you to steganography techniques and tools.",category:{name:"Forensics"},difficulty:{name:"Easy"},type:{name:"Analysis"},points:150,coin_reward:15,estimated_time:25,simulator:Z},{id:"c6",slug:"simple-binary-analysis",title:"Simple Binary Analysis",description:"Analyze a simple executable file to understand its behavior and find hidden functionality. This challenge introduces you to basic reverse engineering concepts.",category:{name:"Reverse Engineering"},difficulty:{name:"Medium"},type:{name:"Analysis"},points:200,coin_reward:20,estimated_time:40,simulator:K}],se=()=>{const{darkMode:a}=$(),{challengeId:t}=Y(),l=U(),[i,m]=n.useState(null),[h,x]=n.useState(!1),[g,o]=n.useState(null);n.useEffect(()=>{const v=ee.find(k=>k.slug===t||k.id===t);m(v)},[t]);const j=v=>{x(!0),o(v)};if(!i)return e.jsx("div",{className:`min-h-screen ${a?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("button",{onClick:()=>l("/challenges"),className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(I,{className:"mr-2"})," Back to Challenges"]})}),e.jsxs("div",{className:`${a?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Challenge Not Found"}),e.jsx("p",{className:`${a?"text-gray-400":"text-gray-600"}`,children:"The challenge you're looking for doesn't exist or has been removed."}),e.jsx("button",{onClick:()=>l("/challenges"),className:"mt-4 px-6 py-2 theme-button-primary rounded-lg",children:"Browse Challenges"})]})]})});const c=i.simulator;return e.jsx("div",{className:`min-h-screen ${a?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("button",{onClick:()=>l("/challenges"),className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(I,{className:"mr-2"})," Back to Challenges"]})}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-3xl font-bold",children:i.title}),e.jsxs("div",{className:"flex flex-wrap gap-2 mt-2",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${a?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:i.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${a?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:i.difficulty.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${a?"bg-green-900/20 text-green-300":"bg-green-100 text-green-800"}`,children:i.type.name}),e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${a?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(F,{className:"mr-1 text-yellow-500"})," ",i.points," points"]})]})]}),e.jsx(c,{onComplete:j}),h&&g&&e.jsxs("div",{className:`mt-8 ${a?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("h2",{className:"text-2xl font-bold mb-4 flex items-center",children:[e.jsx(F,{className:"text-yellow-500 mr-2"})," Challenge Completed!"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[e.jsxs("div",{className:`p-4 rounded-lg ${a?"bg-[#252D4A]":"bg-gray-100"}`,children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Points Earned"}),e.jsx("div",{className:"text-2xl font-bold",children:i.points})]}),e.jsxs("div",{className:`p-4 rounded-lg ${a?"bg-[#252D4A]":"bg-gray-100"}`,children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Coins Rewarded"}),e.jsx("div",{className:"text-2xl font-bold",children:i.coin_reward})]}),e.jsxs("div",{className:`p-4 rounded-lg ${a?"bg-[#252D4A]":"bg-gray-100"}`,children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Time Spent"}),e.jsx("div",{className:"text-2xl font-bold",children:g.timeSpent||"15:32"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg ${a?"bg-[#252D4A]":"bg-gray-100"} mb-6`,children:[e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Your Flag"}),e.jsx("div",{className:`p-3 rounded ${a?"bg-black":"bg-gray-900"} font-mono text-green-400`,children:g.flag})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("button",{onClick:()=>l("/challenges"),className:`px-6 py-2 rounded-lg ${a?"bg-gray-800 hover:bg-gray-700 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-800"}`,children:"Back to Challenges"}),e.jsx("button",{onClick:()=>{x(!1),o(null)},className:"px-6 py-2 theme-button-primary rounded-lg",children:"Next Challenge"})]})]})]})})};export{se as default};
