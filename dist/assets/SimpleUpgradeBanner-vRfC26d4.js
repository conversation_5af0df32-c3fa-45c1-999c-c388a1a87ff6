import{j as e,l as c,ag as r}from"./index-c6UceSOv.js";const n=({targetTier:s="premium",price:l=399,currency:t="₹",message:a="Upgrade to Premium for full access to all features"})=>e.jsx("div",{className:"bg-[#1A1F35] border border-[#88cc14]/30 rounded-lg p-4 mb-6 relative overflow-hidden",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center",children:e.jsx(c,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-bold text-white",children:[s.charAt(0).toUpperCase()+s.slice(1)," Subscription"]}),e.jsx("p",{className:"text-gray-300 text-sm",children:a})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[s==="business"?e.jsx("span",{className:"text-white font-bold",children:"Contact Sales"}):e.jsxs("span",{className:"text-white font-bold",children:[t,l]}),e.jsx(r,{to:"/pricing",className:"bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium px-4 py-2 rounded-lg transition-colors",children:"Upgrade Now"})]})]})});export{n as S};
