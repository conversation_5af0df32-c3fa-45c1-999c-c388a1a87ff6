import{r,j as e,m as E,s as k,u as G,g as W,h as A,i as H,d as R,k as Y,l as z,n as Q,o as X,p as O,q as K}from"./index-c6UceSOv.js";const $=({onComplete:u})=>{const[i,p]=r.useState(""),[x,c]=r.useState([]),[a,y]=r.useState(0),[d,f]=r.useState(!1),[g,w]=r.useState(""),n=[{instruction:"SQL Injection Challenge - Login Bypass",context:`
Database: users
Table structure:
  - username (varchar)
  - password (varchar)
  - role (varchar)

Login query:
SELECT * FROM users WHERE username = '[INPUT]' AND password = 'password'

Goal: Bypass login without knowing the password.
      `,validation:t=>["' OR '1'='1","' OR 1=1 --","' OR 'x'='x","admin'--"].some(m=>t.toLowerCase().includes(m.toLowerCase())),success:"SQL injection successful! Query returned all users, bypassing authentication.",hint:"Try making the WHERE clause always true with OR condition"},{instruction:"XSS Challenge - Steal Cookies",context:`
Vulnerable page code:
<div>Welcome, [INPUT]!</div>

The input is directly inserted into HTML without sanitization.
Goal: Create a payload that would steal user cookies.
      `,validation:t=>t.includes("<script>")&&(t.includes("document.cookie")||t.includes("fetch"))&&(t.includes("<\/script>")||t.includes("/>")),success:"XSS payload successful! This would steal user cookies when executed.",hint:"Use <script> tag to access document.cookie and send it to an attacker's server"},{instruction:"CSRF Challenge - Unauthorized Transfer",context:`
Vulnerable transfer endpoint:
POST /api/transfer
Content-Type: application/x-www-form-urlencoded

amount=[AMOUNT]&to=[ACCOUNT]

Goal: Create a form that automatically submits a transfer when loaded.
      `,validation:t=>t.toLowerCase().includes("<form")&&t.toLowerCase().includes("method='post'")&&t.toLowerCase().includes("action='/api/transfer'")&&t.toLowerCase().includes("onload")&&t.toLowerCase().includes("submit()"),success:"CSRF attack crafted! This form would automatically submit when a user visits the page.",hint:"Create a form that submits automatically using onload event"}];r.useEffect(()=>{w(n[a].context),c([{type:"system",text:"Challenge environment initialized..."}])},[a]);const C=t=>{if(t.preventDefault(),!i.trim())return;const l=n[a],m=l.validation(i);c(o=>[...o,{type:"input",text:`$ ${i}`}]),setTimeout(()=>{m?(c(o=>[...o,{type:"success",text:l.success},{type:"system",text:"Moving to next challenge..."}]),a<n.length-1?setTimeout(()=>y(a+1),1500):(f(!0),u&&u())):c(o=>[...o,{type:"error",text:"Attempt failed."},{type:"hint",text:`Hint: ${l.hint}`}]),p("")},500)};return e.jsxs("div",{className:"bg-black text-green-400 p-6 rounded-lg font-mono",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"text-xs text-gray-500 mb-2",children:["Challenge Progress: ",a+1,"/",n.length]}),e.jsx("div",{className:"h-2 bg-gray-800 rounded-full",children:e.jsx("div",{className:"h-full bg-green-500 rounded-full transition-all duration-300",style:{width:`${(a+1)/n.length*100}%`}})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:n[a].instruction}),e.jsx("div",{className:"bg-black/30 rounded-lg p-4 border border-gray-800",children:e.jsx("pre",{className:"text-gray-400 text-sm whitespace-pre-wrap font-mono",children:g})})]}),e.jsx("div",{className:"h-48 mb-6 bg-black/50 rounded-lg border border-gray-800 overflow-y-auto",children:e.jsx("div",{className:"p-4 space-y-2",children:x.map((t,l)=>e.jsx("div",{className:`${t.type==="input"?"text-blue-400":t.type==="success"?"text-green-400":t.type==="error"?"text-red-400":t.type==="hint"?"text-yellow-400":t.type==="system"?"text-purple-400":"text-gray-400"}`,children:t.text},l))})}),e.jsxs("form",{onSubmit:C,className:"flex gap-2",children:[e.jsx("input",{type:"text",value:i,onChange:t=>p(t.target.value),className:"flex-1 bg-gray-900 text-green-400 px-4 py-2 rounded-lg border border-gray-800 focus:outline-none focus:border-green-500",placeholder:"Enter your payload...",disabled:d}),e.jsx("button",{type:"submit",className:"bg-green-600 text-black px-6 py-2 rounded-lg font-bold hover:bg-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",disabled:d,children:"Execute"})]}),d&&e.jsxs(E.div,{initial:{opacity:0},animate:{opacity:1},className:"mt-6 p-4 bg-green-500/20 text-green-400 rounded-lg border border-green-500/20",children:[e.jsx("h4",{className:"text-lg font-bold mb-2",children:"🎉 Challenge Complete!"}),e.jsx("p",{children:"You've successfully demonstrated understanding of:"}),e.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[e.jsx("li",{children:"SQL Injection techniques"}),e.jsx("li",{children:"Cross-Site Scripting (XSS) attacks"}),e.jsx("li",{children:"Cross-Site Request Forgery (CSRF)"})]})]})]})},V=({onComplete:u})=>{const[i,p]=r.useState(""),[x,c]=r.useState([]),[a,y]=r.useState(0),[d,f]=r.useState(!1),[g,w]=r.useState(""),n=[{instruction:"Buffer Overflow - Stack Smashing",context:`
Vulnerable C program:
void vulnerable_function(char *input) {
    char buffer[64];
    strcpy(buffer, input);  // No bounds checking!
}

Memory layout:
[buffer(64 bytes)][saved EBP(4 bytes)][return address(4 bytes)]

Goal: Overflow the buffer to crash the program.
      `,validation:t=>t.length>64&&/A{64,}/.test(t),success:"Buffer overflow successful! Program crashed due to stack smashing.",hint:"Fill the buffer with 'A's and overflow into saved EBP and return address"},{instruction:"Shellcode Injection",context:`
x86 Assembly shellcode template:
\\x31\\xc0       ; xor eax, eax
\\x50           ; push eax
\\x68//sh      ; push "//sh"
\\x68/bin      ; push "/bin"
\\x89\\xe3       ; mov ebx, esp
\\x50           ; push eax
\\x53           ; push ebx
\\x89\\xe1       ; mov ecx, esp
\\xb0\\x0b       ; mov al, 11
\\xcd\\x80       ; int 0x80

Goal: Create working shellcode with NOP sled.
      `,validation:t=>t.startsWith("\\x90")&&t.includes("\\x31\\xc0")&&t.includes("\\xcd\\x80"),success:"Shellcode successfully crafted! This would spawn a shell when executed.",hint:"Start with NOP sled (\\x90) followed by the shellcode template"},{instruction:"Return-to-libc Attack",context:`
Memory layout with ASLR disabled:
system() address: 0xb7e63190
"/bin/sh" string: 0xb7f83a24
exit() address: 0xb7e55180

Goal: Craft payload to call system("/bin/sh") without shellcode.
      `,validation:t=>t.includes("0xb7e63190")&&t.includes("0xb7f83a24")&&t.includes("0xb7e55180"),success:"Return-to-libc attack successful! This would execute shell command without shellcode.",hint:'Chain addresses: system() + exit() + "/bin/sh"'}];r.useEffect(()=>{w(n[a].context),c([{type:"system",text:"Binary loaded into memory..."}])},[a]);const C=t=>{if(t.preventDefault(),!i.trim())return;const l=n[a],m=l.validation(i);c(o=>[...o,{type:"input",text:`$ ${i}`}]),setTimeout(()=>{m?(c(o=>[...o,{type:"success",text:l.success},{type:"system",text:"Preparing next challenge..."}]),a<n.length-1?setTimeout(()=>y(a+1),1500):(f(!0),u&&u())):c(o=>[...o,{type:"error",text:"Exploitation failed."},{type:"hint",text:`Hint: ${l.hint}`}]),p("")},500)};return e.jsxs("div",{className:"bg-black text-green-400 p-6 rounded-lg font-mono",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"text-xs text-gray-500 mb-2",children:["Challenge Progress: ",a+1,"/",n.length]}),e.jsx("div",{className:"h-2 bg-gray-800 rounded-full",children:e.jsx("div",{className:"h-full bg-green-500 rounded-full transition-all duration-300",style:{width:`${(a+1)/n.length*100}%`}})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:n[a].instruction}),e.jsx("div",{className:"bg-black/30 rounded-lg p-4 border border-gray-800",children:e.jsx("pre",{className:"text-gray-400 text-sm whitespace-pre-wrap font-mono",children:g})})]}),e.jsx("div",{className:"h-48 mb-6 bg-black/50 rounded-lg border border-gray-800 overflow-y-auto",children:e.jsx("div",{className:"p-4 space-y-2",children:x.map((t,l)=>e.jsx("div",{className:`${t.type==="input"?"text-blue-400":t.type==="success"?"text-green-400":t.type==="error"?"text-red-400":t.type==="hint"?"text-yellow-400":t.type==="system"?"text-purple-400":"text-gray-400"}`,children:t.text},l))})}),e.jsxs("form",{onSubmit:C,className:"flex gap-2",children:[e.jsx("input",{type:"text",value:i,onChange:t=>p(t.target.value),className:"flex-1 bg-gray-900 text-green-400 px-4 py-2 rounded-lg border border-gray-800 focus:outline-none focus:border-green-500",placeholder:"Enter your exploit...",disabled:d}),e.jsx("button",{type:"submit",className:"bg-green-600 text-black px-6 py-2 rounded-lg font-bold hover:bg-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",disabled:d,children:"Execute"})]}),d&&e.jsxs(E.div,{initial:{opacity:0},animate:{opacity:1},className:"mt-6 p-4 bg-green-500/20 text-green-400 rounded-lg border border-green-500/20",children:[e.jsx("h4",{className:"text-lg font-bold mb-2",children:"🎉 Challenge Complete!"}),e.jsx("p",{children:"You've successfully demonstrated understanding of:"}),e.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[e.jsx("li",{children:"Buffer Overflow exploitation"}),e.jsx("li",{children:"Shellcode development"}),e.jsx("li",{children:"Return-to-libc attacks"})]})]})]})},J=({onComplete:u})=>{const[i,p]=r.useState(""),[x,c]=r.useState([]),[a,y]=r.useState(0),[d,f]=r.useState(!1),[g,w]=r.useState(""),n=[{instruction:"Classical Cryptography - Caesar Cipher",context:`
Encrypted message:
"PBQRPENPXVAT"

Known information:
- Message is encrypted using Caesar cipher
- Shift value is between 1-25
- Message is related to this challenge

Goal: Decrypt the message.
      `,validation:t=>t.toLowerCase()==="codecracking",success:"Successfully decrypted! The shift value was 13 (ROT13).",hint:"Try each possible shift value systematically"},{instruction:"Public Key Cryptography - Weak Prime",context:`
RSA parameters:
n = 77 (public modulus)
e = 7 (public exponent)
c = 42 (encrypted message)

Known information:
- n is product of two primes
- One prime is less than 10
- Message m is a single digit

Goal: Factor n and decrypt the message.
      `,validation:t=>t.toLowerCase().replace(/\s/g,"")==="m=4".toLowerCase(),success:"Correct! You've broken RSA with weak primes (7 and 11).",hint:"Try small prime numbers as factors of 77"},{instruction:"Protocol Attack - Diffie-Hellman",context:`
Diffie-Hellman parameters:
p = 23 (prime modulus)
g = 5 (generator)
A = 8 (Alice's public key)
B = 19 (Bob's public key)

Intercepted messages:
[A -> B]: g^a mod p = 8
[B -> A]: g^b mod p = 19

Goal: Find the shared secret key.
      `,validation:t=>t.toLowerCase().replace(/\s/g,"")==="key=2".toLowerCase(),success:"Man-in-the-middle attack successful! You've found the shared secret.",hint:"Use discrete logarithm for small numbers"}];r.useEffect(()=>{w(n[a].context),c([{type:"system",text:"Cryptographic challenge initialized..."}])},[a]);const C=t=>{if(t.preventDefault(),!i.trim())return;const l=n[a],m=l.validation(i);c(o=>[...o,{type:"input",text:`$ ${i}`}]),setTimeout(()=>{m?(c(o=>[...o,{type:"success",text:l.success},{type:"system",text:"Preparing next challenge..."}]),a<n.length-1?setTimeout(()=>y(a+1),1500):(f(!0),u&&u())):c(o=>[...o,{type:"error",text:"Decryption failed."},{type:"hint",text:`Hint: ${l.hint}`}]),p("")},500)};return e.jsxs("div",{className:"bg-black text-green-400 p-6 rounded-lg font-mono",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"text-xs text-gray-500 mb-2",children:["Challenge Progress: ",a+1,"/",n.length]}),e.jsx("div",{className:"h-2 bg-gray-800 rounded-full",children:e.jsx("div",{className:"h-full bg-green-500 rounded-full transition-all duration-300",style:{width:`${(a+1)/n.length*100}%`}})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:n[a].instruction}),e.jsx("div",{className:"bg-black/30 rounded-lg p-4 border border-gray-800",children:e.jsx("pre",{className:"text-gray-400 text-sm whitespace-pre-wrap font-mono",children:g})})]}),e.jsx("div",{className:"h-48 mb-6 bg-black/50 rounded-lg border border-gray-800 overflow-y-auto",children:e.jsx("div",{className:"p-4 space-y-2",children:x.map((t,l)=>e.jsx("div",{className:`${t.type==="input"?"text-blue-400":t.type==="success"?"text-green-400":t.type==="error"?"text-red-400":t.type==="hint"?"text-yellow-400":t.type==="system"?"text-purple-400":"text-gray-400"}`,children:t.text},l))})}),e.jsxs("form",{onSubmit:C,className:"flex gap-2",children:[e.jsx("input",{type:"text",value:i,onChange:t=>p(t.target.value),className:"flex-1 bg-gray-900 text-green-400 px-4 py-2 rounded-lg border border-gray-800 focus:outline-none focus:border-green-500",placeholder:"Enter your solution...",disabled:d}),e.jsx("button",{type:"submit",className:"bg-green-600 text-black px-6 py-2 rounded-lg font-bold hover:bg-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",disabled:d,children:"Execute"})]}),d&&e.jsxs(E.div,{initial:{opacity:0},animate:{opacity:1},className:"mt-6 p-4 bg-green-500/20 text-green-400 rounded-lg border border-green-500/20",children:[e.jsx("h4",{className:"text-lg font-bold mb-2",children:"🎉 Challenge Complete!"}),e.jsx("p",{children:"You've successfully demonstrated understanding of:"}),e.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[e.jsx("li",{children:"Classical cryptography"}),e.jsx("li",{children:"Public key cryptography"}),e.jsx("li",{children:"Cryptographic protocol analysis"})]})]})]})},F=async u=>{var i,p;try{const[x,c,a,y]=await Promise.all([k.from("challenge_attempts").select("*",{count:"exact"}).eq("challenge_id",u),k.from("challenge_attempts").select("*",{count:"exact"}).eq("challenge_id",u).eq("status","success"),k.from("challenge_attempts").select("user_id",{count:"exact",distinct:!0}).eq("challenge_id",u),k.from("challenge_attempts").select("execution_time").eq("challenge_id",u).eq("status","success")]),d=((i=y.data)==null?void 0:i.reduce((f,g)=>f+g.execution_time,0))/(((p=y.data)==null?void 0:p.length)||1);return{attempts:x.count||0,completions:c.count||0,uniqueUsers:a.count||0,successRate:x.count?Math.round(c.count/x.count*100):0,averageTime:d?Math.round(d/1e3/60):0}}catch(x){return console.error("Error getting challenge summary:",x),{attempts:0,completions:0,uniqueUsers:0,successRate:0,averageTime:0}}};function te(){const u=G(),[i,p]=r.useState([]),[x,c]=r.useState([]),[a,y]=r.useState({}),[d,f]=r.useState(!0),[g,w]=r.useState("all"),[n,C]=r.useState(null),[t,l]=r.useState(!1),[m,o]=r.useState(null),{subscriptionLevel:Z,getFeatureLimit:M}=W();r.useEffect(()=>{(async()=>{try{const h=await X(),v=h.map(j=>F(j.id)),T=await Promise.all(v),S=h.map((j,I)=>({...j,stats:T[I]}));p(S);const{data:{session:N}}=await k.auth.getSession();if(o((N==null?void 0:N.user)||null),N!=null&&N.user){const j=await O();c(j||[])}}catch(h){console.error("Error fetching challenges:",h)}finally{f(!1)}})();const b=k.channel("challenges").on("*",async h=>{if(h.eventType==="INSERT"||h.eventType==="UPDATE"){const v=await F(h.new.id);y(T=>({...T,[h.new.id]:v}))}}).subscribe();return()=>{b.unsubscribe()}},[]);const B=[{id:"all",name:"All Challenges"},{id:"web",name:"Web Security"},{id:"crypto",name:"Cryptography"},{id:"binary",name:"Binary Exploitation"}],D=g==="all"?i:i.filter(s=>s.category.toLowerCase().includes(g)),P=M("maxChallenges"),U=P<0,L=s=>x.some(b=>b.challenge_id===s&&b.status==="completed"),q=s=>{if(!m){u("/login",{state:{from:"/challenges"}});return}C(s),l(!0)},_=({challenge:s})=>{const[b,h]=r.useState(!1),[v,T]=r.useState(!1);if(!s)return null;const S=async()=>{T(!0);try{await K(s.id,"completed",s.points);const j=await O();c(j||[])}catch(j){console.error("Error submitting challenge:",j)}},N=()=>{switch(s.category.toLowerCase()){case"web":return e.jsx($,{onComplete:S});case"binary":return e.jsx(V,{onComplete:S});case"crypto":return e.jsx(J,{onComplete:S});default:return e.jsx($,{onComplete:S})}};return e.jsx("div",{className:"fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4 overflow-y-auto",children:e.jsxs(E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"bg-gray-900 rounded-lg w-full max-w-4xl my-8 relative",children:[e.jsx("button",{onClick:()=>l(!1),className:"absolute top-4 right-4 text-gray-400 hover:text-white bg-gray-800 hover:bg-gray-700 rounded-full p-2 transition-colors","aria-label":"Close challenge",children:e.jsx("svg",{className:"w-6 h-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center mr-4 bg-primary/10",children:e.jsx(R,{className:"text-2xl text-primary"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-2xl font-bold text-white",children:s.title}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("span",{className:"text-sm text-gray-400",children:s.difficulty}),e.jsx("span",{className:"mx-2 text-gray-600",children:"•"}),e.jsxs("span",{className:"text-sm text-primary",children:[s.points," pts"]})]})]})]}),b?e.jsxs("div",{className:"challenge-content",children:[N(),v&&e.jsx("div",{className:"mt-6 flex justify-end",children:e.jsx("button",{onClick:()=>l(!1),className:"bg-primary text-black font-bold py-2 px-6 rounded-lg hover:bg-primary-hover transition-all duration-300",children:"Close Challenge"})})]}):e.jsxs("div",{className:"space-y-6 text-gray-300",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-white mb-2",children:"Objective"}),e.jsx("p",{children:s.description})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-white mb-2",children:"Tasks"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx("span",{className:"w-5 h-5 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 text-sm",children:"1"}),"Find and exploit vulnerabilities"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("span",{className:"w-5 h-5 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 text-sm",children:"2"}),"Bypass security measures"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("span",{className:"w-5 h-5 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 text-sm",children:"3"}),"Capture the flag"]})]})]}),e.jsx("button",{onClick:()=>h(!0),className:"w-full bg-primary text-black font-bold py-3 px-4 rounded-lg hover:bg-primary-hover transition-all duration-300",children:"Start Challenge"})]})]})]})})};return e.jsxs("div",{className:"min-h-screen bg-gray-50 pt-20",children:[e.jsx("div",{className:"bg-black text-white py-16",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Open Challenges"}),e.jsx("p",{className:"text-gray-400 text-lg mb-8",children:"Test your skills with real-world cybersecurity challenges. Complete challenges to earn XCerberus coins and climb the leaderboard."}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[{icon:A,value:"1000+",label:"Participants"},{icon:H,value:"50+",label:"Active Challenges"},{icon:R,value:"₹10K+",label:"In Rewards"},{icon:Y,value:"24/7",label:"Support"}].map((s,b)=>e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4",children:[e.jsx(s.icon,{className:"text-primary text-2xl mx-auto mb-2"}),e.jsx("div",{className:"text-xl font-bold",children:s.value}),e.jsx("div",{className:"text-gray-400 text-sm",children:s.label})]},b))})]})})}),m&&!U&&e.jsx("div",{className:"container mx-auto px-4 py-4",children:e.jsxs("div",{className:"bg-primary/10 border border-primary/20 rounded-lg p-4 flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsxs("p",{className:"text-gray-800",children:[e.jsx("span",{className:"font-bold",children:"Your Challenge Access: "}),x.length," of ",P," challenges used"]}),e.jsx("div",{className:"w-64 h-2 bg-gray-200 rounded-full mt-2",children:e.jsx("div",{className:"h-full bg-primary rounded-full",style:{width:`${x.length/P*100}%`}})})]}),e.jsxs("a",{href:"/pricing",className:"bg-primary text-black px-4 py-2 rounded-lg font-bold hover:bg-primary-hover transition-colors flex items-center gap-2",children:[e.jsx(z,{}),e.jsx("span",{children:"Upgrade"})]})]})}),e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"flex flex-wrap gap-4 mb-8",children:B.map(s=>e.jsx("button",{onClick:()=>w(s.id),className:`px-4 py-2 rounded-full transition-all ${g===s.id?"bg-primary text-white":"bg-white text-gray-600 hover:bg-gray-100"}`,children:s.name},s.id))}),d?e.jsx("div",{className:"text-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"})}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:D.map((s,b)=>{var h,v;return e.jsxs(E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:b*.1},className:"bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:s.title}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${s.difficulty==="Easy"?"bg-green-100 text-green-800":s.difficulty==="Medium"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:s.difficulty}),e.jsxs("span",{className:"text-primary",children:[s.points," pts"]})]})]}),e.jsx("div",{className:`p-2 rounded-lg ${m&&L(s.id)?"bg-green-100":"bg-primary/10"}`,children:m&&L(s.id)?e.jsx(Q,{className:"text-green-600"}):e.jsx(R,{className:"text-primary"})})]}),e.jsx("p",{className:"text-gray-600 mb-6 line-clamp-2",children:s.description}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[e.jsx(A,{}),e.jsxs("span",{children:[((h=s.stats)==null?void 0:h.attempts)||0," attempts"]}),((v=s.stats)==null?void 0:v.completions)>0&&e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"mx-2",children:"•"}),e.jsxs("span",{className:"text-green-500",children:[s.stats.successRate,"% success rate"]})]})]}),e.jsx("button",{onClick:()=>q(s),className:`bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors ${m&&L(s.id)?"bg-green-600 hover:bg-green-700":""}`,children:m&&L(s.id)?"Completed":"Start Challenge"})]})]},s.id)})})]}),t&&e.jsx(_,{challenge:n})]})}export{te as default};
