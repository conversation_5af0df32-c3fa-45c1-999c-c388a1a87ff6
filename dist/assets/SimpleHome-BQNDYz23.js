import{j as e,ag as s}from"./index-c6UceSOv.js";function l(){return e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-900 text-white",children:e.jsxs("div",{className:"text-center max-w-4xl mx-auto px-4",children:[e.jsx("h1",{className:"text-5xl font-bold mb-6 text-green-400",children:"XCerberus"}),e.jsx("p",{className:"text-2xl mb-8",children:"The Ultimate Cybersecurity Learning Platform"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-12",children:[e.jsxs("div",{className:"bg-gray-800 p-6 rounded-lg",children:[e.jsx("h2",{className:"text-xl font-bold mb-3 text-green-400",children:"Learn Cybersecurity"}),e.jsx("p",{className:"mb-4",children:"Master offensive and defensive security skills through interactive modules."}),e.jsx(s,{to:"/challenges",className:"inline-block bg-green-500 hover:bg-green-600 text-black font-bold py-2 px-6 rounded-lg transition-colors",children:"View Challenges"})]}),e.jsxs("div",{className:"bg-gray-800 p-6 rounded-lg",children:[e.jsx("h2",{className:"text-xl font-bold mb-3 text-green-400",children:"Premium Features"}),e.jsx("p",{className:"mb-4",children:"Unlock advanced learning paths and hands-on hacking labs."}),e.jsx(s,{to:"/pricing",className:"inline-block bg-green-500 hover:bg-green-600 text-black font-bold py-2 px-6 rounded-lg transition-colors",children:"View Plans"})]})]}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-4",children:[e.jsx(s,{to:"/simplified-dashboard",className:"text-green-400 hover:underline",children:"Dashboard"}),e.jsx(s,{to:"/learn/modules",className:"text-green-400 hover:underline",children:"Learning Modules"}),e.jsx(s,{to:"/challenges",className:"text-green-400 hover:underline",children:"Challenges"}),e.jsx(s,{to:"/pricing",className:"text-green-400 hover:underline",children:"Pricing"}),e.jsx(s,{to:"/about",className:"text-green-400 hover:underline",children:"About"})]})]})})}export{l as default};
