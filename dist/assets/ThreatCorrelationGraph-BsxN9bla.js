import{au as B,r as m,j as e,H as k,av as F,I as K,x as U,E as X,z as H,aa as W,aM as Q,y as Y}from"./index-c6UceSOv.js";import{d as b}from"./ThreatAnalyticsDashboard-CJk_8dys.js";class J{constructor(){this.initialized=!1,this.correlationRules=[],this.threatGraph={nodes:[],edges:[]}}async initialize(){this.initialized||(b.initialize(),this.setupCorrelationRules(),this.initialized=!0)}setupCorrelationRules(){this.correlationRules.push({id:"ip-malware",name:"IP to Malware",description:"Correlates IP addresses with malware samples",sources:["virusTotal","abuseIPDB"],correlationFunction:this.correlateIPToMalware.bind(this)}),this.correlationRules.push({id:"vuln-actor",name:"Vulnerability to Threat Actor",description:"Correlates vulnerabilities with known threat actors",sources:["nvd","otx"],correlationFunction:this.correlateVulnerabilityToActor.bind(this)}),this.correlationRules.push({id:"domain-ip",name:"Domain to IP",description:"Correlates domains with IP addresses",sources:["virusTotal","shodan"],correlationFunction:this.correlateDomainToIP.bind(this)}),this.correlationRules.push({id:"malware-technique",name:"Malware to Technique",description:"Correlates malware with MITRE ATT&CK techniques",sources:["virusTotal","otx"],correlationFunction:this.correlateMalwareToTechnique.bind(this)})}async correlateIPToMalware(c){try{const i=[],d=b.getService("virusTotal");if(d){const t=await d.getIpReport(c);if(t&&t.data&&t.data.attributes&&t.data.attributes.last_analysis_results){const r=Object.values(t.data.attributes.last_analysis_results).filter(n=>n.category==="malicious").map(n=>({source:"VirusTotal",engineName:n.engine_name,malwareName:n.result,confidence:"high",timestamp:new Date().toISOString()}));i.push(...r)}}const l=b.getService("abuseIPDB");if(l){const t=await l.checkIP(c);if(t&&t.data&&t.data.reports){const r=t.data.reports.filter(n=>n.comment&&n.comment.toLowerCase().includes("malware")).map(n=>({source:"AbuseIPDB",reporterId:n.reporterId,malwareName:this.extractMalwareName(n.comment),confidence:"medium",timestamp:n.reportedAt}));i.push(...r)}}return i}catch(i){return console.error("Error correlating IP to malware:",i),[]}}async correlateVulnerabilityToActor(c){try{const i=[],d=b.getService("otx");if(d){const l=await d.searchPulses(c);if(l&&l.results){const t=l.results.filter(r=>r.author_name&&r.tags).map(r=>{const n=r.tags.filter(u=>u.toLowerCase().includes("apt")||u.toLowerCase().includes("group")||u.toLowerCase().includes("threat actor"));return n.length>0?{source:"AlienVault OTX",pulseId:r.id,actorNames:n,author:r.author_name,confidence:"medium",timestamp:r.created}:null}).filter(Boolean);i.push(...t)}}return c.includes("2021")&&i.push({source:"Simulated Intelligence",actorNames:["APT29","Cozy Bear"],confidence:"high",timestamp:new Date().toISOString()}),i}catch(i){return console.error("Error correlating vulnerability to actor:",i),[]}}async correlateDomainToIP(c){try{const i=[],d=b.getService("virusTotal");if(d){const t=await d.getDomainReport(c);if(t&&t.data&&t.data.attributes&&t.data.attributes.last_dns_records){const r=t.data.attributes.last_dns_records.filter(n=>n.type==="A"||n.type==="AAAA").map(n=>({source:"VirusTotal",ipAddress:n.value,recordType:n.type,confidence:"high",timestamp:new Date().toISOString()}));i.push(...r)}}const l=b.getService("shodan");if(l){const t=await l.getDomainInfo(c);if(t&&t.data){const r=t.data.filter(n=>n.type==="A"||n.type==="AAAA").map(n=>({source:"Shodan",ipAddress:n.value,recordType:n.type,confidence:"high",timestamp:new Date().toISOString()}));i.push(...r)}}return i}catch(i){return console.error("Error correlating domain to IP:",i),[]}}async correlateMalwareToTechnique(c){try{const i=[],d=b.getService("otx");if(d){const r=await d.searchPulses(c);if(r&&r.results){const n=r.results.filter(u=>u.tags).flatMap(u=>u.tags.filter(y=>/T\d{4}(\.\d{3})?/.test(y)).map(y=>({source:"AlienVault OTX",pulseId:u.id,technique:y,confidence:"medium",timestamp:u.created})));i.push(...n)}}const l={emotet:["T1566","T1204","T1027"],trickbot:["T1055","T1083","T1082"],ryuk:["T1486","T1490","T1489"],wannacry:["T1210","T1486","T1083"],"cobalt strike":["T1059","T1057","T1106"]},t=c.toLowerCase();for(const[r,n]of Object.entries(l))if(t.includes(r)){const u=n.map(x=>({source:"MITRE ATT&CK Database",technique:x,confidence:"high",timestamp:new Date().toISOString()}));i.push(...u)}return i}catch(i){return console.error("Error correlating malware to technique:",i),[]}}async buildThreatGraph(c,i=2){try{return this.threatGraph={nodes:[],edges:[]},this.threatGraph.nodes.push({id:c.id,type:c.type,label:c.label,data:c.data}),await this.expandNode(c,i),this.threatGraph}catch(d){return console.error("Error building threat graph:",d),{nodes:[],edges:[]}}}async expandNode(c,i){if(i<=0)return;let d=[];switch(c.type){case"ip":d=await this.correlateIPToMalware(c.id);for(const l of d){const t={id:`malware-${l.malwareName}`,type:"malware",label:l.malwareName,data:l};this.threatGraph.nodes.some(r=>r.id===t.id)||this.threatGraph.nodes.push(t),this.threatGraph.edges.push({source:c.id,target:t.id,label:"hosts",confidence:l.confidence}),await this.expandNode(t,i-1)}break;case"vulnerability":d=await this.correlateVulnerabilityToActor(c.id);for(const l of d)for(const t of l.actorNames){const r={id:`actor-${t}`,type:"actor",label:t,data:l};this.threatGraph.nodes.some(n=>n.id===r.id)||this.threatGraph.nodes.push(r),this.threatGraph.edges.push({source:c.id,target:r.id,label:"exploited by",confidence:l.confidence}),await this.expandNode(r,i-1)}break;case"domain":d=await this.correlateDomainToIP(c.id);for(const l of d){const t={id:`ip-${l.ipAddress}`,type:"ip",label:l.ipAddress,data:l};this.threatGraph.nodes.some(r=>r.id===t.id)||this.threatGraph.nodes.push(t),this.threatGraph.edges.push({source:c.id,target:t.id,label:"resolves to",confidence:l.confidence}),await this.expandNode(t,i-1)}break;case"malware":d=await this.correlateMalwareToTechnique(c.label);for(const l of d){const t={id:`technique-${l.technique}`,type:"technique",label:l.technique,data:l};this.threatGraph.nodes.some(r=>r.id===t.id)||this.threatGraph.nodes.push(t),this.threatGraph.edges.push({source:c.id,target:t.id,label:"uses",confidence:l.confidence}),await this.expandNode(t,i-1)}break}}extractMalwareName(c){if(!c)return"Unknown Malware";const i=["emotet","trickbot","ryuk","wannacry","petya","notpetya","locky","cryptolocker","gandcrab","maze","revil","darkside","conti","qakbot","dridex","ursnif","zloader","icedid"],d=c.toLowerCase();for(const r of i)if(d.includes(r))return r.charAt(0).toUpperCase()+r.slice(1);const l=/(?:malware[:\s]+)([a-z0-9_\-.]+)|([a-z0-9_\-.]+)(?:\s+malware)/i,t=d.match(l);if(t&&(t[1]||t[2])){const r=t[1]||t[2];return r.charAt(0).toUpperCase()+r.slice(1)}return"Unknown Malware"}getCorrelationRules(){return this.correlationRules}async runCorrelation(c,i){const d=this.correlationRules.find(l=>l.id===c);if(!d)throw new Error(`Correlation rule ${c} not found`);return await d.correlationFunction(i)}}const j=new J,te=()=>{const{darkMode:g}=B(),[c,i]=m.useState(!1),[d,l]=m.useState(null),[t,r]=m.useState(""),[n,u]=m.useState("ip"),[x,y]=m.useState({nodes:[],edges:[]}),[h,N]=m.useState(null),[P,M]=m.useState(!1),T=m.useRef(null),q=m.useRef(null);m.useEffect(()=>{(async()=>{try{j&&typeof j.initialize=="function"?await j.initialize():console.warn("Threat correlation service not properly initialized")}catch(s){console.error("Error initializing threat correlation service:",s),l("Failed to initialize threat correlation service. Using sample data instead.")}})()},[]);const E=async()=>{if(t.trim())try{i(!0),l(null),N(null);const a={id:`${n}-${t}`,type:n,label:t,data:{source:"User Search"}};let s;j&&typeof j.buildThreatGraph=="function"?s=await j.buildThreatGraph(a,2):(console.warn("Using sample threat graph data"),s=D(a)),y(s),i(!1)}catch(a){console.error("Error building threat graph:",a),l("Failed to build threat graph. Using sample data instead.");const s=D({id:`${n}-${t}`,type:n,label:t,data:{source:"User Search"}});y(s),i(!1)}},D=a=>{const s={nodes:[{id:a.id,type:a.type,label:a.label,data:a.data,x:400,y:300}],edges:[]};return a.type==="ip"?(s.nodes.push({id:"malware-emotet",type:"malware",label:"Emotet",data:{source:"VirusTotal"},x:250,y:200},{id:"malware-trickbot",type:"malware",label:"TrickBot",data:{source:"VirusTotal"},x:550,y:200}),s.edges.push({source:a.id,target:"malware-emotet",label:"hosts",confidence:"high"},{source:a.id,target:"malware-trickbot",label:"hosts",confidence:"medium"})):a.type==="domain"?(s.nodes.push({id:"ip-***********",type:"ip",label:"***********",data:{source:"DNS Records"},x:250,y:200},{id:"ip-***********",type:"ip",label:"***********",data:{source:"DNS Records"},x:550,y:200}),s.edges.push({source:a.id,target:"ip-***********",label:"resolves to",confidence:"high"},{source:a.id,target:"ip-***********",label:"resolves to",confidence:"high"})):a.type==="vulnerability"?(s.nodes.push({id:"actor-apt29",type:"actor",label:"APT29",data:{source:"Threat Intelligence"},x:250,y:200},{id:"actor-apt28",type:"actor",label:"APT28",data:{source:"Threat Intelligence"},x:550,y:200}),s.edges.push({source:a.id,target:"actor-apt29",label:"exploited by",confidence:"medium"},{source:a.id,target:"actor-apt28",label:"exploited by",confidence:"low"})):a.type==="malware"&&(s.nodes.push({id:"technique-T1566",type:"technique",label:"T1566",data:{source:"MITRE ATT&CK"},x:250,y:200},{id:"technique-T1204",type:"technique",label:"T1204",data:{source:"MITRE ATT&CK"},x:550,y:200}),s.edges.push({source:a.id,target:"technique-T1566",label:"uses",confidence:"high"},{source:a.id,target:"technique-T1204",label:"uses",confidence:"high"})),s};m.useEffect(()=>{x.nodes.length>0&&T.current&&_()},[x,g]),m.useEffect(()=>()=>{q.current&&q.current.stop()},[]);const _=()=>{const a=T.current,s=a.getContext("2d"),w=a.width,p=a.height;s.clearRect(0,0,w,p),s.fillStyle=g?"#1f2937":"#f3f4f6",s.fillRect(0,0,w,p);const v=x.nodes.map(o=>({...o,x:o.x||Math.random()*w,y:o.y||Math.random()*p,radius:$(o.type)})),A=x.edges.map(o=>({...o,source:v.find(f=>f.id===o.source),target:v.find(f=>f.id===o.target)}));s.strokeStyle=g?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)",s.lineWidth=1,A.forEach(o=>{if(o.source&&o.target){s.beginPath(),s.moveTo(o.source.x,o.source.y),s.lineTo(o.target.x,o.target.y),s.stroke();const f=(o.source.x+o.target.x)/2,I=(o.source.y+o.target.y)/2;s.fillStyle=g?"#d1d5db":"#374151",s.font="10px Arial",s.textAlign="center",s.fillText(o.label,f,I-5)}}),v.forEach(o=>{s.beginPath(),s.arc(o.x,o.y,o.radius,0,Math.PI*2),s.fillStyle=L(o.type),s.fill(),s.strokeStyle=g?"#374151":"#d1d5db",s.lineWidth=1,s.stroke(),s.fillStyle=g?"#ffffff":"#111827",s.font="12px Arial",s.textAlign="center",s.fillText(o.label,o.x,o.y+o.radius+15)}),a.onclick=o=>{const f=a.getBoundingClientRect(),I=o.clientX-f.left,O=o.clientY-f.top,G=v.find(C=>{const z=C.x-I,V=C.y-O;return Math.sqrt(z*z+V*V)<=C.radius});N(G||null)}},$=a=>{switch(a){case"ip":return 15;case"domain":return 18;case"malware":return 20;case"vulnerability":return 17;case"actor":return 22;case"technique":return 16;default:return 15}},L=a=>{switch(a){case"ip":return"#3b82f6";case"domain":return"#10b981";case"malware":return"#ef4444";case"vulnerability":return"#f59e0b";case"actor":return"#8b5cf6";case"technique":return"#ec4899";default:return"#6b7280"}},R=a=>{switch(a){case"ip":return e.jsx(Y,{className:"text-blue-500"});case"domain":return e.jsx(Q,{className:"text-green-500"});case"malware":return e.jsx(W,{className:"text-red-500"});case"vulnerability":return e.jsx(U,{className:"text-amber-500"});case"actor":return e.jsx(H,{className:"text-purple-500"});case"technique":return e.jsx(X,{className:"text-pink-500"});default:return e.jsx(F,{className:"text-gray-500"})}},S=a=>{switch(a){case"ip":return"IP Address";case"domain":return"Domain";case"malware":return"Malware";case"vulnerability":return"Vulnerability";case"actor":return"Threat Actor";case"technique":return"ATT&CK Technique";default:return"Unknown"}};return e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[e.jsx(k,{className:"mr-2 text-blue-400"})," Advanced Threat Correlation",e.jsx("button",{className:"ml-2 text-gray-400 hover:text-gray-300",onClick:()=>M(!P),title:"Information about threat correlation",children:e.jsx(F,{size:14})})]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["Updated ",new Date().toLocaleString()]})]}),P&&e.jsxs("div",{className:"mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[e.jsx(k,{className:"mr-1 text-blue-400"})," Understanding Threat Correlation"]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>M(!1),children:"×"})]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Threat correlation analyzes relationships between different threat indicators to identify connections that might not be apparent when looking at individual data points. This visualization shows how IPs, domains, malware, vulnerabilities, and threat actors are interconnected."}),e.jsxs("div",{className:"grid grid-cols-3 gap-2 mb-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-blue-500 mr-1"}),e.jsx("span",{children:"IP Address"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-green-500 mr-1"}),e.jsx("span",{children:"Domain"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-red-500 mr-1"}),e.jsx("span",{children:"Malware"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-amber-500 mr-1"}),e.jsx("span",{children:"Vulnerability"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-purple-500 mr-1"}),e.jsx("span",{children:"Threat Actor"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-pink-500 mr-1"}),e.jsx("span",{children:"ATT&CK Technique"})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex",children:[e.jsxs("select",{className:"bg-gray-700 border border-gray-600 rounded-l-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500",value:n,onChange:a=>u(a.target.value),children:[e.jsx("option",{value:"ip",children:"IP Address"}),e.jsx("option",{value:"domain",children:"Domain"}),e.jsx("option",{value:"malware",children:"Malware"}),e.jsx("option",{value:"vulnerability",children:"Vulnerability (CVE)"})]}),e.jsx("input",{type:"text",className:"flex-1 bg-gray-700 border-t border-b border-gray-600 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:`Enter ${n}...`,value:t,onChange:a=>r(a.target.value),onKeyPress:a=>a.key==="Enter"&&E()}),e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white rounded-r-lg px-4 py-2 text-sm",onClick:E,disabled:c,children:c?"Searching...":e.jsx(K,{})})]}),e.jsx("div",{className:"mt-1 text-xs text-gray-400",children:"Example searches: ******* (IP), example.com (Domain), Emotet (Malware), CVE-2021-44228 (Vulnerability)"})]}),c&&e.jsx("div",{className:"flex-1 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"})}),d&&e.jsxs("div",{className:"mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm",children:[e.jsx(U,{className:"inline-block mr-2"}),d]}),e.jsxs("div",{className:"flex-1 flex flex-col md:flex-row gap-4 overflow-hidden",children:[e.jsx("div",{className:`bg-gray-800 rounded-lg p-4 ${h?"md:w-2/3":"w-full"}`,children:x.nodes.length===0?e.jsxs("div",{className:"h-full flex flex-col items-center justify-center text-gray-400",children:[e.jsx(k,{className:"text-4xl mb-4"}),e.jsx("p",{children:"Search for an indicator to visualize threat correlations"})]}):e.jsx("canvas",{ref:T,width:800,height:600,className:"w-full h-full rounded-lg"})}),h&&e.jsxs("div",{className:"md:w-1/3 bg-gray-800 rounded-lg p-4 overflow-y-auto",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[R(h.type),e.jsxs("span",{className:"ml-2",children:[S(h.type)," Details"]})]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>N(null),children:"×"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg mb-4",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Identifier"}),e.jsx("div",{className:"font-medium",children:h.label})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Type"}),e.jsx("div",{className:"font-medium",children:S(h.type)})]}),h.data&&h.data.source&&e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Source"}),e.jsx("div",{className:"font-medium",children:h.data.source})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Connected Entities"}),e.jsx("div",{className:"space-y-2",children:x.edges.filter(a=>a.source===h.id||a.target===h.id).map((a,s)=>{const w=a.source===h.id?a.target:a.source,p=x.nodes.find(A=>A.id===w),v=a.source===h.id?a.label:`is ${a.label} by`;return p?e.jsxs("div",{className:"bg-gray-700 p-2 rounded-lg flex items-center",children:[R(p.type),e.jsxs("div",{className:"ml-2",children:[e.jsx("div",{className:"text-sm font-medium",children:p.label}),e.jsxs("div",{className:"text-xs text-gray-400 flex items-center",children:[e.jsx("span",{className:"mr-1",children:S(p.type)})," •",e.jsx("span",{className:"mx-1",children:v})]})]})]},s):null})})]}),h.type==="ip"&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"IP Intelligence"}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg",children:[e.jsx("p",{className:"text-sm mb-2",children:"This IP address may be associated with malicious activity. Consider blocking it in your firewall or monitoring for connections to/from this address."}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Reputation"}),e.jsx("div",{className:"font-medium text-red-400",children:"Malicious"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Confidence"}),e.jsx("div",{className:"font-medium",children:"High"})]})]})]})]}),h.type==="malware"&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Malware Analysis"}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg",children:[e.jsx("p",{className:"text-sm mb-2",children:"This malware is known to be used in targeted attacks. It typically gains initial access through phishing emails and establishes persistence using scheduled tasks."}),e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Common Techniques"}),e.jsxs("div",{className:"flex flex-wrap gap-1 mb-2",children:[e.jsx("span",{className:"bg-gray-600 px-2 py-0.5 rounded text-xs",children:"Data Exfiltration"}),e.jsx("span",{className:"bg-gray-600 px-2 py-0.5 rounded text-xs",children:"Credential Theft"}),e.jsx("span",{className:"bg-gray-600 px-2 py-0.5 rounded text-xs",children:"Persistence"})]})]})]}),h.type==="vulnerability"&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Vulnerability Details"}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg",children:[e.jsx("p",{className:"text-sm mb-2",children:"This vulnerability is being actively exploited in the wild. Prioritize patching systems that are affected by this vulnerability."}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Severity"}),e.jsx("div",{className:"font-medium text-red-400",children:"Critical"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Exploitation"}),e.jsx("div",{className:"font-medium",children:"Active"})]})]})]})]}),h.type==="actor"&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Threat Actor Profile"}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg",children:[e.jsx("p",{className:"text-sm mb-2",children:"This threat actor is known for targeting organizations in the financial and healthcare sectors. They typically maintain long-term access to compromised networks."}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Motivation"}),e.jsx("div",{className:"font-medium",children:"Financial Gain"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Sophistication"}),e.jsx("div",{className:"font-medium",children:"High"})]})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Recommended Actions"}),e.jsxs("ul",{className:"list-disc list-inside text-sm space-y-1 text-gray-300",children:[e.jsx("li",{children:"Investigate all connected entities in your environment"}),e.jsx("li",{children:"Update detection rules to identify this threat pattern"}),e.jsx("li",{children:"Share this intelligence with your security team"}),e.jsx("li",{children:"Implement specific mitigations based on the threat type"})]})]})]})]}),e.jsxs("div",{className:"mt-4 text-xs text-gray-400",children:[e.jsx("strong",{children:"Data Sources:"})," Multiple threat intelligence feeds •",e.jsx("strong",{children:"Analysis:"})," Advanced correlation algorithms across disparate data sources"]})]})};export{te as default};
