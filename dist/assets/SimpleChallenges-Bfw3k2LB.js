import{au as n,b2 as c,ba as i,j as e}from"./index-c6UceSOv.js";import{S as o}from"./SimpleUpgradeBanner-vRfC26d4.js";const b=()=>{const{darkMode:s}=n(),{user:d,profile:r}=c(),{challengeId:a}=i(),t=()=>e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Challenges Coming Soon"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"We're working on exciting cybersecurity challenges for you. Check back soon!"})]}),l=()=>e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsxs("h2",{className:"text-2xl font-bold mb-4",children:["Challenge ",a]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"This challenge is being prepared. Please check back soon!"})]});return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[!r||r.subscription_tier==="free"?e.jsx(o,{title:"Upgrade to Premium",description:"Get access to all challenges and features with a premium subscription.",buttonText:"View Plans",buttonLink:"/pricing",className:"mb-6"}):null,e.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Cybersecurity Challenges"}),a?e.jsx(l,{}):e.jsx(t,{})]})})};export{b as default};
