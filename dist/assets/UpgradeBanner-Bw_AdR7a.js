import{g as o,j as e,m as n,l as d,ag as x,D as m}from"./index-c6UceSOv.js";import{B as u}from"./Button-jOBCH0E8.js";const f=({targetTier:s="premium",message:a,showClose:l=!1,onClose:i})=>{const{tierFeatures:r}=o(),t=r[s];if(!t)return null;const c=`Upgrade to ${s.charAt(0).toUpperCase()+s.slice(1)} for full access to all features`;return e.jsxs(n.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-[#1A1F35] border border-[#88cc14]/30 rounded-lg p-4 mb-6 relative overflow-hidden",children:[e.jsx("div",{className:"absolute -top-10 -right-10 w-40 h-40 bg-[#88cc14]/10 rounded-full blur-xl"}),e.jsx("div",{className:"absolute -bottom-10 -left-10 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl"}),e.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4 relative z-10",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center",children:e.jsx(d,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-bold text-white",children:[s.charAt(0).toUpperCase()+s.slice(1)," Subscription"]}),e.jsx("p",{className:"text-gray-300 text-sm",children:a||c})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("span",{className:"text-white font-bold",children:["$",t.price]}),e.jsx(x,{to:"/pricing",children:e.jsx(u,{variant:"primary",size:"sm",icon:e.jsx(m,{}),iconPosition:"right",children:"Upgrade Now"})})]})]}),l&&e.jsx("button",{onClick:i,className:"absolute top-2 right-2 text-gray-400 hover:text-white","aria-label":"Close",children:"×"})]})};export{f as U};
