import{au as i,j as e,ag as t,E as r,a6 as l,D as n,i as a,ak as c,h as o,ai as d,ao as x}from"./index-c6UceSOv.js";const h=()=>{const{darkMode:s}=i();return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"}`,children:e.jsxs("div",{className:"pt-20",children:[e.jsx("section",{className:`py-16 md:py-24 border-b ${s?"border-gray-800":"border-gray-200"}`,children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-4xl md:text-5xl font-bold mb-6 leading-tight",children:["Master ",e.jsx("span",{className:"text-[#88cc14]",children:"Cybersecurity"})," Skills Through Real-World Challenges"]}),e.jsx("p",{className:`text-xl ${s?"text-gray-300":"text-gray-600"} mb-8`,children:"Join the platform where security professionals learn, practice, and compete in realistic cybersecurity scenarios."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx(t,{to:"/signup",className:"theme-button-primary font-bold px-6 py-3 rounded-lg transition-colors text-center",children:"Get Started"}),e.jsx(t,{to:"/challenges",className:`${s?"bg-[#1A1F35] hover:bg-[#252D4A] text-white":"bg-gray-100 hover:bg-gray-200 text-gray-800"} font-medium px-6 py-3 rounded-lg transition-colors text-center`,children:"Explore Challenges"})]}),e.jsxs("div",{className:`mt-8 flex items-center ${s?"text-gray-400":"text-gray-500"}`,children:[e.jsx("span",{className:"mr-2",children:"Join"}),e.jsx("span",{className:`font-bold ${s?"text-white":"text-gray-900"}`,children:"10,000+"}),e.jsx("span",{className:"ml-2",children:"security professionals"})]})]}),e.jsxs("div",{className:"relative flex justify-center py-12",children:[e.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[e.jsx("div",{className:`absolute inset-0 ${s?"opacity-10":"opacity-5"}`,children:e.jsxs("svg",{width:"100%",height:"100%",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("pattern",{id:"hexagons",width:"50",height:"43.4",patternUnits:"userSpaceOnUse",patternTransform:"scale(2)",children:e.jsx("polygon",{points:"25,0 50,14.4 50,43.4 25,57.8 0,43.4 0,14.4",fill:"none",stroke:s?"#3A5E8C":"#88cc14",strokeWidth:"1"})}),e.jsx("rect",{width:"100%",height:"100%",fill:"url(#hexagons)"})]})}),e.jsx("div",{className:`absolute top-1/4 left-1/4 w-32 h-32 rounded-full ${s?"bg-[#88cc14]/5":"bg-[#88cc14]/10"} filter blur-xl`}),e.jsx("div",{className:`absolute bottom-1/4 right-1/4 w-40 h-40 rounded-full ${s?"bg-[#4A90E2]/5":"bg-[#88cc14]/10"} filter blur-xl`})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden w-full max-w-xl hover:border-[#88cc14]/50 transition-colors shadow-xl relative z-10`,children:[e.jsxs("div",{className:`${s?"bg-[#252D4A] border-gray-800":"bg-gray-50 border-gray-200"} p-4 border-b flex items-center`,children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center mr-3",children:e.jsx(r,{className:"text-[#88cc14]"})}),e.jsxs("div",{children:[e.jsx("h3",{className:`font-bold ${s?"text-white":"text-gray-800"}`,children:"Web Application Security"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("span",{className:`text-xs ${s?"bg-blue-900/30 text-blue-300":"bg-blue-100 text-blue-800"} px-2 py-0.5 rounded`,children:"Beginner"}),e.jsx("span",{className:`text-xs ${s?"text-gray-400":"text-gray-500"} ml-2`,children:"30 min"}),e.jsx("span",{className:"text-xs text-[#88cc14] ml-2",children:"500 pts"})]})]})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-6`,children:"Learn to identify and exploit common web vulnerabilities in a safe environment. This challenge will test your knowledge of:"}),e.jsxs("ul",{className:`list-disc list-inside ${s?"text-gray-400":"text-gray-600"} mb-6 space-y-2`,children:[e.jsx("li",{children:"Cross-Site Scripting (XSS)"}),e.jsx("li",{children:"SQL Injection"}),e.jsx("li",{children:"Cross-Site Request Forgery (CSRF)"}),e.jsx("li",{children:"Authentication Bypasses"})]}),e.jsxs("div",{className:`${s?"bg-[#0B1120] border-gray-800":"bg-gray-50 border-gray-200"} border rounded-lg p-4 mb-6`,children:[e.jsx("h4",{className:`text-sm font-medium ${s?"text-gray-300":"text-gray-700"} mb-2`,children:"Flag Format"}),e.jsxs("div",{className:"font-mono text-sm text-[#88cc14]",children:["ZilalLinux","{found_the_vulnerability}"]})]}),e.jsx("div",{className:"flex justify-center",children:e.jsxs(t,{to:"/signup",className:"bg-[#88cc14] hover:bg-[#88cc14]/90 text-black px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center",children:[e.jsx(l,{className:"mr-2"})," Try This Challenge"]})})]}),e.jsxs("div",{className:`${s?"bg-black/50 border-gray-800":"bg-gray-50 border-gray-200"} border-t p-4 flex justify-between items-center`,children:[e.jsx("div",{className:`${s?"text-gray-400":"text-gray-600"} text-sm`,children:"1,245 completions"}),e.jsxs(t,{to:"/challenges",className:"text-[#88cc14] text-sm font-medium hover:underline flex items-center",children:["View All Challenges ",e.jsx(n,{className:"ml-1"})]})]})]}),e.jsx("div",{className:"absolute -top-4 -left-4 w-24 h-24 bg-[#88cc14]/10 rounded-full blur-xl"}),e.jsx("div",{className:"absolute -bottom-8 -right-8 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl"})]})]})})}),e.jsx("section",{className:`py-16 border-b ${s?"border-gray-800":"border-gray-200"}`,children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:[e.jsxs("div",{className:"text-center bg-gradient-to-b from-transparent to-[#88cc14]/5 rounded-xl p-6",children:[e.jsx("div",{className:"w-12 h-12 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(a,{className:"text-[#88cc14] text-xl"})}),e.jsx("div",{className:"text-4xl font-bold text-[#88cc14] mb-2",children:"150+"}),e.jsx("div",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Challenges"})]}),e.jsxs("div",{className:"text-center bg-gradient-to-b from-transparent to-[#88cc14]/5 rounded-xl p-6",children:[e.jsx("div",{className:"w-12 h-12 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(c,{className:"text-[#88cc14] text-xl"})}),e.jsx("div",{className:"text-4xl font-bold text-[#88cc14] mb-2",children:"50+"}),e.jsx("div",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Learning Modules"})]}),e.jsxs("div",{className:"text-center bg-gradient-to-b from-transparent to-[#88cc14]/5 rounded-xl p-6",children:[e.jsx("div",{className:"w-12 h-12 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(o,{className:"text-[#88cc14] text-xl"})}),e.jsx("div",{className:"text-4xl font-bold text-[#88cc14] mb-2",children:"10K+"}),e.jsx("div",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Users"})]}),e.jsxs("div",{className:"text-center bg-gradient-to-b from-transparent to-[#88cc14]/5 rounded-xl p-6",children:[e.jsx("div",{className:"w-12 h-12 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(l,{className:"text-[#88cc14] text-xl"})}),e.jsx("div",{className:"text-4xl font-bold text-[#88cc14] mb-2",children:"24/7"}),e.jsx("div",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Support"})]})]})})}),e.jsx("section",{className:`py-16 border-b ${s?"border-gray-800":"border-gray-200"}`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(d,{className:"text-[#88cc14] text-2xl"})}),e.jsx("h2",{className:`text-3xl font-bold mb-4 ${s?"text-white":"text-gray-900"}`,children:"Why Learn Cybersecurity?"}),e.jsx("p",{className:`text-xl ${s?"text-gray-400":"text-gray-600"} max-w-3xl mx-auto`,children:"In today's digital world, cybersecurity skills are more critical than ever before."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:[e.jsxs("div",{className:"bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-6 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden",children:[e.jsx("div",{className:"absolute -bottom-6 -right-6 w-24 h-24 bg-red-500/10 rounded-full blur-xl"}),e.jsx("div",{className:"text-red-500 text-4xl font-bold mb-2",children:"$4.35M"}),e.jsx("h3",{className:"text-lg font-semibold mb-2 text-white",children:"Average Cost of a Data Breach"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Organizations face increasing financial risks from cyber attacks, with costs rising 13% since 2020."})]}),e.jsxs("div",{className:"bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-6 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden",children:[e.jsx("div",{className:"absolute -bottom-6 -right-6 w-24 h-24 bg-yellow-500/10 rounded-full blur-xl"}),e.jsx("div",{className:"text-yellow-500 text-4xl font-bold mb-2",children:"3.5M"}),e.jsx("h3",{className:"text-lg font-semibold mb-2 text-white",children:"Unfilled Security Positions"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"The cybersecurity talent gap continues to grow, creating tremendous career opportunities."})]}),e.jsxs("div",{className:"bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-6 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden",children:[e.jsx("div",{className:"absolute -bottom-6 -right-6 w-24 h-24 bg-blue-500/10 rounded-full blur-xl"}),e.jsx("div",{className:"text-blue-500 text-4xl font-bold mb-2",children:"+15%"}),e.jsx("h3",{className:"text-lg font-semibold mb-2 text-white",children:"Annual Salary Premium"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Cybersecurity professionals earn significantly more than their IT counterparts with similar experience."})]}),e.jsxs("div",{className:"bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-6 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden",children:[e.jsx("div",{className:"absolute -bottom-6 -right-6 w-24 h-24 bg-green-500/10 rounded-full blur-xl"}),e.jsx("div",{className:"text-green-500 text-4xl font-bold mb-2",children:"300%"}),e.jsx("h3",{className:"text-lg font-semibold mb-2 text-white",children:"Increase in Attacks"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Cyber attacks have tripled since 2019, making security skills essential for all organizations."})]})]})]})}),e.jsx("section",{className:`py-16 border-b ${s?"border-gray-800":"border-gray-200"}`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center",children:e.jsx(r,{className:"text-[#88cc14] text-2xl"})}),e.jsx("h2",{className:`text-3xl font-bold mb-4 ${s?"text-white":"text-gray-900"}`,children:"Why Choose Zilal Linux?"}),e.jsx("p",{className:`text-xl ${s?"text-gray-400":"text-gray-600"} max-w-3xl mx-auto`,children:"Our platform offers a unique approach to cybersecurity education through practical, hands-on learning."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-8 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden group hover:border-[#88cc14]/50 transition-colors",children:[e.jsx("div",{className:"absolute -bottom-10 -right-10 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl group-hover:bg-[#88cc14]/10 transition-colors"}),e.jsx("div",{className:"w-16 h-16 bg-[#88cc14]/10 rounded-full flex items-center justify-center mb-6 group-hover:bg-[#88cc14]/20 transition-colors",children:e.jsx(x,{className:"text-[#88cc14] text-2xl"})}),e.jsx("h3",{className:"text-xl font-bold mb-3 text-white",children:"Hands-on Learning"}),e.jsx("p",{className:"text-gray-400",children:"Learn by doing with interactive challenges that simulate real-world scenarios and vulnerabilities."})]}),e.jsxs("div",{className:"bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-8 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden group hover:border-[#88cc14]/50 transition-colors",children:[e.jsx("div",{className:"absolute -bottom-10 -right-10 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl group-hover:bg-[#88cc14]/10 transition-colors"}),e.jsx("div",{className:"w-16 h-16 bg-[#88cc14]/10 rounded-full flex items-center justify-center mb-6 group-hover:bg-[#88cc14]/20 transition-colors",children:e.jsx(r,{className:"text-[#88cc14] text-2xl"})}),e.jsx("h3",{className:"text-xl font-bold mb-3 text-white",children:"Comprehensive Curriculum"}),e.jsx("p",{className:"text-gray-400",children:"Access a wide range of topics from web security to network penetration testing and malware analysis."})]}),e.jsxs("div",{className:"bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-8 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden group hover:border-[#88cc14]/50 transition-colors",children:[e.jsx("div",{className:"absolute -bottom-10 -right-10 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl group-hover:bg-[#88cc14]/10 transition-colors"}),e.jsx("div",{className:"w-16 h-16 bg-[#88cc14]/10 rounded-full flex items-center justify-center mb-6 group-hover:bg-[#88cc14]/20 transition-colors",children:e.jsx(a,{className:"text-[#88cc14] text-2xl"})}),e.jsx("h3",{className:"text-xl font-bold mb-3 text-white",children:"Competitive Learning"}),e.jsx("p",{className:"text-gray-400",children:"Compete with peers on our leaderboard and track your progress as you master new cybersecurity skills."})]})]})]})}),e.jsx("section",{className:"py-16",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"bg-gradient-to-br from-[#1A1F35] to-[#0B1120] rounded-xl border border-gray-800 p-8 md:p-12 text-center relative overflow-hidden",children:[e.jsxs("div",{className:"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none",children:[e.jsx("div",{className:"absolute -top-20 -right-20 w-64 h-64 bg-[#88cc14]/5 rounded-full blur-3xl"}),e.jsx("div",{className:"absolute -bottom-20 -left-20 w-64 h-64 bg-[#88cc14]/5 rounded-full blur-3xl"}),e.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full opacity-10",children:e.jsxs("svg",{width:"100%",height:"100%",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("pattern",{id:"hexagons-cta",width:"50",height:"43.4",patternUnits:"userSpaceOnUse",patternTransform:"scale(2)",children:e.jsx("polygon",{points:"25,0 50,14.4 50,43.4 25,57.8 0,43.4 0,14.4",fill:"none",stroke:"#3A5E8C",strokeWidth:"1"})}),e.jsx("rect",{width:"100%",height:"100%",fill:"url(#hexagons-cta)"})]})})]}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"w-20 h-20 mx-auto mb-6 relative",children:[e.jsx("div",{className:"absolute inset-0 rounded-full bg-[#88cc14]/20 animate-ping opacity-50"}),e.jsx("div",{className:"absolute inset-0 rounded-full bg-[#88cc14]/10"}),e.jsx("div",{className:"absolute inset-3 rounded-full bg-[#0B1120] flex items-center justify-center",children:e.jsx(l,{className:"text-[#88cc14] text-3xl"})})]}),e.jsx("h2",{className:"text-3xl font-bold mb-4 text-white",children:"Ready to Start Your Cybersecurity Journey?"}),e.jsx("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto mb-8",children:"Join thousands of security professionals who are leveling up their skills on Zilal Linux."}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-4",children:[e.jsx(t,{to:"/signup",className:"bg-[#88cc14] hover:bg-[#88cc14]/90 text-black font-bold px-8 py-3 rounded-lg transition-colors text-center",children:"Sign Up Now"}),e.jsx(t,{to:"/pricing",className:"bg-[#1A1F35]/50 hover:bg-[#252D4A] text-white border border-gray-700 font-medium px-8 py-3 rounded-lg transition-colors text-center backdrop-blur-sm",children:"View Pricing"})]}),e.jsxs("div",{className:"mt-8 flex flex-wrap justify-center gap-8",children:[e.jsxs(t,{to:"/challenges",className:"flex items-center text-[#88cc14] hover:text-[#88cc14]/80 transition-colors group",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-[#88cc14]/10 flex items-center justify-center mr-3 group-hover:bg-[#88cc14]/20 transition-colors",children:e.jsx(a,{className:"text-[#88cc14]"})}),e.jsx("span",{children:"Explore Challenges"})]}),e.jsxs(t,{to:"/learn",className:"flex items-center text-[#88cc14] hover:text-[#88cc14]/80 transition-colors group",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-[#88cc14]/10 flex items-center justify-center mr-3 group-hover:bg-[#88cc14]/20 transition-colors",children:e.jsx(c,{className:"text-[#88cc14]"})}),e.jsx("span",{children:"Start Learning"})]})]})]})]})})})]})})};export{h as default};
