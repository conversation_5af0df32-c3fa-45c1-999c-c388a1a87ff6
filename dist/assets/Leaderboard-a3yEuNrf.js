import{r as l,j as e,I as L,b as k,m as U,J as D,s as E,K as R,L as j,l as T}from"./index-c6UceSOv.js";function F(){var h,p;const[c,f]=l.useState([]),[s,N]=l.useState(null),[b,v]=l.useState(!0),[d,w]=l.useState(null),[i,_]=l.useState(""),[x,S]=l.useState("all");l.useEffect(()=>{(async()=>{try{const t=await D();f(t||[]);const{data:{session:r}}=await E.auth.getSession();if(r!=null&&r.user){const n=await R().catch(()=>null);N(n)}}catch(t){console.error("Error fetching leaderboard:",t),w(t.message)}finally{v(!1)}})()},[x]);const o=c.filter(a=>{var t,r;return(r=(t=a==null?void 0:a.users)==null?void 0:t.username)==null?void 0:r.toLowerCase().includes(i.toLowerCase())}),C=a=>{switch(a){case 1:return e.jsx(T,{className:"text-yellow-400 text-2xl"});case 2:return e.jsx(j,{className:"text-gray-400 text-2xl"});case 3:return e.jsx(j,{className:"text-amber-600 text-2xl"});default:return e.jsxs("span",{className:"text-gray-500 font-mono text-lg",children:["#",a]})}},m=s?c.findIndex(a=>a.user_id===(s==null?void 0:s.id))+1:0;return d?e.jsx("div",{className:"min-h-screen bg-gray-50 pt-20 flex items-center justify-center",children:e.jsxs("div",{className:"bg-red-50 text-red-500 p-4 rounded-lg",children:["Error loading leaderboard: ",d]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50 pt-20",children:[e.jsx("div",{className:"bg-black text-white py-16",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Global Leaderboard"}),e.jsx("p",{className:"text-gray-400 text-lg",children:"Top cybersecurity experts competing for glory and rewards. Complete challenges to earn points and climb the ranks."})]})})}),e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-8",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(L,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",value:i,onChange:a=>_(a.target.value),className:"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-primary"})]}),e.jsx("div",{className:"flex gap-2",children:["all","month","week"].map(a=>e.jsxs("button",{onClick:()=>S(a),className:`px-4 py-2 rounded-lg transition-all ${x===a?"bg-primary text-white":"bg-white text-gray-600 hover:bg-gray-100"}`,children:[a.charAt(0).toUpperCase()+a.slice(1)," Time"]},a))})]}),s&&e.jsx("div",{className:"bg-primary/10 border border-primary/20 rounded-lg p-4 mb-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center",children:s.avatar_url?e.jsx("img",{src:s.avatar_url,alt:s.username,className:"w-full h-full rounded-full object-cover"}):e.jsx(k,{className:"text-primary text-xl"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"font-bold text-gray-900",children:["Your Rank: ",m>0?m:"Not Ranked"]}),e.jsx("div",{className:"text-gray-600",children:s.username})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-2xl font-bold text-primary",children:[((h=c.find(a=>a.user_id===s.id))==null?void 0:h.total_points)||0," pts"]}),e.jsxs("div",{className:"text-gray-600",children:[((p=c.find(a=>a.user_id===s.id))==null?void 0:p.challenges_completed)||0," challenges completed"]})]})]})}),b?e.jsx("div",{className:"text-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"})}):o.length===0?e.jsx("div",{className:"bg-white rounded-lg shadow-sm p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No users found"})}):e.jsx("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-50 text-left",children:[e.jsx("th",{className:"px-6 py-4 text-gray-600",children:"Rank"}),e.jsx("th",{className:"px-6 py-4 text-gray-600",children:"User"}),e.jsx("th",{className:"px-6 py-4 text-gray-600",children:"Points"}),e.jsx("th",{className:"px-6 py-4 text-gray-600",children:"Challenges"}),e.jsx("th",{className:"px-6 py-4 text-gray-600",children:"Last Active"})]})}),e.jsx("tbody",{children:o.map((a,t)=>{var r,n,u,g,y;return e.jsxs(U.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.05},className:`border-t border-gray-100 ${s&&a.user_id===s.id?"bg-primary/5 border-l-4 border-l-primary":""}`,children:[e.jsx("td",{className:"px-6 py-4",children:e.jsx("div",{className:"flex items-center gap-2",children:C(a.rank)})}),e.jsx("td",{className:"px-6 py-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center",children:(r=a.users)!=null&&r.avatar_url?e.jsx("img",{src:a.users.avatar_url,alt:a.users.username,className:"w-full h-full rounded-full"}):e.jsx("span",{className:"text-primary font-bold",children:((u=(n=a.users)==null?void 0:n.username)==null?void 0:u.charAt(0).toUpperCase())||"?"})}),e.jsx("div",{children:e.jsx("div",{className:"font-medium text-gray-900",children:((g=a.users)==null?void 0:g.username)||"Anonymous User"})})]})}),e.jsx("td",{className:"px-6 py-4",children:e.jsxs("div",{className:"text-primary font-bold",children:[((y=a.total_points)==null?void 0:y.toLocaleString())||0," pts"]})}),e.jsx("td",{className:"px-6 py-4",children:e.jsx("div",{className:"text-gray-600",children:a.challenges_completed||0})}),e.jsx("td",{className:"px-6 py-4",children:e.jsx("div",{className:"text-gray-600",children:a.updated_at?new Date(a.updated_at).toLocaleDateString():"Never"})})]},a.id)})})]})})})]})]})}export{F as default};
