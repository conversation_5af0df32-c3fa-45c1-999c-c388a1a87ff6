import{u as y,g as N,r as b,j as e,ag as a,ae as h,af as f,a6 as u,Q as d,P as x,G as g,H as w,ac as k,ad as F,E as C,d as A,z as S,aa as B,ab as $,v as E,B as p,i as j}from"./index-c6UceSOv.js";const L=({userData:s})=>{var m;const i=y(),{subscriptionLevel:o,hasFeature:P}=N(),[c,v]=b.useState({learn:!0,attack:!1,defence:!1,malware:!1}),r=l=>{v(n=>({...n,[l]:!n[l]}))},t=o==="Premium"||o==="Business";return e.jsxs("div",{className:"flex h-screen bg-[#0B1120]",children:[e.jsxs("div",{className:"w-64 bg-[#0B1120] border-r border-gray-800 overflow-y-auto",children:[e.jsx("div",{className:"p-4 border-b border-gray-800",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-[#88cc14] font-bold text-xl",children:"X"}),e.jsx("span",{className:"text-white font-bold text-xl",children:"CERBERUS"})]})}),e.jsxs("nav",{className:"p-4",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"text-gray-400 uppercase text-xs font-bold mb-2",children:"LEARN"}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs(a,{to:"/learn/modules",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(h,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Learning Modules"})]}),e.jsxs(a,{to:"/learn/certifications",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(f,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Certifications"})]}),e.jsxs(a,{to:"/learn/paths",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(u,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Career Paths"})]})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"text-gray-400 uppercase text-xs font-bold mb-2",children:"STARTHACK"}),e.jsxs("div",{className:"mb-2",children:[e.jsxs("button",{onClick:()=>r("attack"),className:"flex items-center justify-between w-full px-3 py-2 text-gray-300 hover:bg-gray-800 hover:text-white rounded-md",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:"Attack"})}),c.attack?e.jsx(d,{size:12}):e.jsx(x,{size:12})]}),c.attack&&e.jsxs("div",{className:"ml-4 mt-1 space-y-1",children:[e.jsxs(a,{to:"/hack/web",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(g,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Web Penetration"})]}),e.jsxs(a,{to:"/hack/network",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(w,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Network Penetration"})]}),e.jsxs(a,{to:"/hack/cloud",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(k,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Cloud Security"})]}),e.jsxs(a,{to:"/hack/wireless",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(F,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Wireless Security"})]})]})]}),e.jsxs("div",{className:"mb-2",children:[e.jsxs("button",{onClick:()=>r("defence"),className:"flex items-center justify-between w-full px-3 py-2 text-gray-300 hover:bg-gray-800 hover:text-white rounded-md",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:"Defence"})}),c.defence?e.jsx(d,{size:12}):e.jsx(x,{size:12})]}),c.defence&&e.jsxs("div",{className:"ml-4 mt-1 space-y-1",children:[e.jsxs(a,{to:"/hack/siem",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(C,{className:"text-[#88cc14]"}),e.jsx("span",{children:"SIEM"})]}),e.jsxs(a,{to:"/hack/incident",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(A,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Incident Response"})]}),e.jsxs(a,{to:"/hack/threat-hunting",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(S,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Threat Hunting"})]})]})]}),e.jsxs("div",{children:[e.jsxs("button",{onClick:()=>r("malware"),className:"flex items-center justify-between w-full px-3 py-2 text-gray-300 hover:bg-gray-800 hover:text-white rounded-md",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:"Malware"})}),c.malware?e.jsx(d,{size:12}):e.jsx(x,{size:12})]}),c.malware&&e.jsxs("div",{className:"ml-4 mt-1 space-y-1",children:[e.jsxs(a,{to:"/hack/malware",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(B,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Malware Analysis"})]}),e.jsxs(a,{to:"/hack/reverse",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx($,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Reverse Engineering"})]}),e.jsxs(a,{to:"/hack/exploit",className:`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${t?"":"opacity-50 pointer-events-none"}`,children:[e.jsx(E,{className:"text-[#88cc14]"}),e.jsx("span",{children:"Exploit Development"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-400 uppercase text-xs font-bold mb-2",children:"CHALLENGES"}),e.jsxs(a,{to:"/challenges",className:"flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white",children:[e.jsx(p,{className:"text-[#88cc14]"}),e.jsx("span",{children:"All Challenges"})]})]})]})]}),e.jsxs("div",{className:"flex-1 flex flex-col",children:[e.jsxs("header",{className:"bg-[#0B1120] border-b border-gray-800 p-4 flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2 bg-[#1A1F35] px-3 py-1.5 rounded-md",children:[e.jsx("span",{className:"text-[#88cc14] font-bold",children:(s==null?void 0:s.coins)||100}),e.jsx("span",{className:"text-[#88cc14]",children:"XC"})]}),e.jsx("div",{className:"bg-[#1A1F35] px-3 py-1.5 rounded-md text-gray-300",children:"Wallet"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-8 h-8 bg-[#1A1F35] rounded-full flex items-center justify-center text-gray-300",children:e.jsx("span",{children:"🔔"})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 bg-[#88cc14] rounded-full flex items-center justify-center",children:(s==null?void 0:s.avatar)||((m=s==null?void 0:s.username)==null?void 0:m.charAt(0))||"U"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white text-sm",children:(s==null?void 0:s.username)||"chitti.gouthamkumar"}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Free Tier"})]})]})]})]}),e.jsxs("div",{className:"flex-1 p-4 bg-[#0B1120] overflow-y-auto",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-[#1A1F35] rounded-lg p-4",children:[e.jsx("div",{className:"flex items-center justify-between mb-2",children:e.jsx("div",{className:"text-gray-400 text-sm",children:"XCerberus Coins"})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-[#88cc14] text-2xl font-bold",children:(s==null?void 0:s.coins)||100}),e.jsx("span",{className:"text-[#88cc14]",children:"XC"})]})]}),e.jsxs("div",{className:"bg-[#1A1F35] rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"text-gray-400 text-sm",children:"Challenges Completed"}),e.jsx(j,{className:"text-[#88cc14]"})]}),e.jsx("div",{className:"text-white text-2xl font-bold",children:(s==null?void 0:s.completedChallenges)||0})]}),e.jsxs("div",{className:"bg-[#1A1F35] rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"text-gray-400 text-sm",children:"Total Points"}),e.jsx(j,{className:"text-[#88cc14]"})]}),e.jsx("div",{className:"text-white text-2xl font-bold",children:(s==null?void 0:s.totalPoints)||0})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[e.jsxs("div",{className:"bg-[#1A1F35] rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-[#0B1120] rounded-full flex items-center justify-center",children:e.jsx(g,{className:"text-[#88cc14]"})}),e.jsx("div",{className:"text-white font-bold",children:"Start Hack"})]}),e.jsx("div",{className:"text-gray-400 text-sm mb-4",children:"Begin a new hacking mission"}),e.jsx("button",{onClick:()=>i("/hack/start"),className:`bg-[#0B1120] text-white px-4 py-2 rounded-md w-full hover:bg-gray-800 ${t?"":"opacity-50 cursor-not-allowed"}`,disabled:!t,children:t?"Start Hacking":"Premium Feature"})]}),e.jsxs("div",{className:"bg-[#1A1F35] rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-[#0B1120] rounded-full flex items-center justify-center",children:e.jsx(h,{className:"text-[#88cc14]"})}),e.jsx("div",{className:"text-white font-bold",children:"Learn"})]}),e.jsx("div",{className:"text-gray-400 text-sm mb-4",children:"Access learning resources"}),e.jsx("button",{onClick:()=>i("/learn/modules"),className:`bg-[#0B1120] text-white px-4 py-2 rounded-md w-full hover:bg-gray-800 ${t?"":"opacity-50 cursor-not-allowed"}`,disabled:!t,children:t?"Start Learning":"Premium Feature"})]}),e.jsxs("div",{className:"bg-[#1A1F35] rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-[#0B1120] rounded-full flex items-center justify-center",children:e.jsx(p,{className:"text-[#88cc14]"})}),e.jsx("div",{className:"text-white font-bold",children:"Challenges"})]}),e.jsx("div",{className:"text-gray-400 text-sm mb-4",children:"Take on security challenges"}),e.jsx("button",{onClick:()=>i("/challenges"),className:"bg-[#0B1120] text-white px-4 py-2 rounded-md w-full hover:bg-gray-800",children:"View Challenges"})]})]}),e.jsxs("div",{className:"bg-[#1A1F35] rounded-lg p-4",children:[e.jsx("h2",{className:"text-white font-bold text-lg mb-4",children:"Recent Activity"}),s!=null&&s.recentActivity&&s.recentActivity.length>0?e.jsx("div",{className:"space-y-3",children:s.recentActivity.map((l,n)=>e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-800 pb-3",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-white",children:l.title}),e.jsx("div",{className:"text-gray-400 text-sm",children:l.timestamp})]}),e.jsxs("div",{className:"text-[#88cc14]",children:["+",l.points," XP"]})]},n))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-400 mb-2",children:"No recent activity"}),e.jsx(a,{to:"/challenges",className:"text-[#88cc14] hover:underline",children:"Start a challenge"})]})]})]})]})]})};export{L as default};
