import{j as t,m as x,ag as b}from"./index-c6UceSOv.js";const m={primary:"bg-[#88cc14] hover:bg-[#7ab811] text-black",secondary:"bg-[#1A1F35] hover:bg-[#252D4A] text-white",outline:"bg-transparent border border-[#88cc14] text-[#88cc14] hover:bg-[#88cc14]/10",ghost:"bg-transparent hover:bg-gray-800/50 text-[#88cc14]",danger:"bg-red-500 hover:bg-red-600 text-white",dark:"bg-[#0B1120] hover:bg-gray-900 text-white border border-gray-700"},u={sm:"py-1 px-3 text-sm",md:"py-2 px-4",lg:"py-3 px-6 text-lg"};function y({children:c,variant:e="primary",size:l="md",to:r,className:g="",icon:a,disabled:s=!1,onClick:p,type:d="button",...n}){const o=`font-bold rounded-lg transition-all duration-300 flex items-center justify-center gap-2 relative overflow-hidden ${m[e]} ${u[l]} ${g}`,i=t.jsxs(t.Fragment,{children:[a&&t.jsx("span",{className:"text-lg",children:a}),t.jsx("span",{className:"relative z-10",children:c}),e==="primary"||e==="secondary"?t.jsx(x.span,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-500",initial:{x:"-100%"},whileHover:{x:"100%"},transition:{duration:.8,ease:"easeInOut"}}):null]});return r?t.jsx(b,{to:r,className:`${o} group`,...n,children:i}):t.jsx("button",{type:d,className:`${o} group ${s?"opacity-50 cursor-not-allowed":""}`,onClick:p,disabled:s,...n,children:i})}export{y as B};
