import{au as O,ba as W,r as b,j as e,ag as h,aC as C,aN as l,d as g,ak as F,w as I,E as D,y as $,ap as q,bb as M,z as R,G as Q,bB as H}from"./index-c6UceSOv.js";import{MODULES as T}from"./StaticLearning-GZ7EfMLP.js";const X=()=>{var v,w,k,S,A;const{darkMode:r}=O(),{moduleSlug:i}=W(),[m,j]=b.useState("content"),[a,u]=b.useState({}),[o,P]=b.useState(!1),[f,E]=b.useState(""),[d,L]=b.useState(null),[y,z]=b.useState(()=>{const s=localStorage.getItem("moduleProgress");return s?JSON.parse(s):{viewed:[],completed:[]}}),n=T.find(s=>s.slug===i),B=s=>{if(!s)return null;let t=0;return s.length>=8&&(t+=1),s.length>=12&&(t+=1),/[A-Z]/.test(s)&&(t+=1),/[a-z]/.test(s)&&(t+=1),/[0-9]/.test(s)&&(t+=1),/[^A-Za-z0-9]/.test(s)&&(t+=1),t<=2?{strength:"weak",color:"red",text:"Very Weak"}:t<=4?{strength:"moderate",color:"yellow",text:"Moderate"}:{strength:"strong",color:"green",text:"Strong"}},N=()=>{const s=B(f);L(s)};return b.useEffect(()=>{if(n&&!y.viewed.includes(i)){const s={...y,viewed:[...y.viewed,i]};z(s),localStorage.setItem("moduleProgress",JSON.stringify(s))}},[n,i,y]),n?e.jsx("div",{className:`min-h-screen ${r?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("div",{className:"mb-6",children:e.jsxs(h,{to:"/learn",className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(C,{className:"mr-2"})," Back to Learning Modules"]})}),e.jsx("div",{className:`${r?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-4 sm:p-6 mb-6`,children:e.jsxs("div",{className:"flex flex-col sm:flex-row items-center sm:items-start sm:justify-between",children:[e.jsxs("div",{className:"text-center sm:text-left mb-4 sm:mb-0",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold mb-2",children:n.title}),e.jsxs("div",{className:"flex flex-wrap justify-center sm:justify-start gap-2 mb-4",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${r?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:n.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${r?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:n.difficulty.name})]}),e.jsx("p",{className:`${r?"text-gray-400":"text-gray-600"} max-w-3xl text-sm sm:text-base`,children:n.description})]}),e.jsx("div",{className:"w-16 h-16 bg-[#88cc14]/20 rounded-full flex items-center justify-center flex-shrink-0",children:n.icon})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"lg:col-span-1 order-2 lg:order-1",children:[e.jsx("div",{className:"block lg:hidden mb-4",children:e.jsxs("button",{onClick:()=>document.getElementById("mobile-progress").classList.toggle("hidden"),className:"w-full flex items-center justify-between p-3 bg-[#1A1F35] border border-gray-700 rounded-lg",children:[e.jsx("span",{className:"font-medium",children:"Show Module Progress"}),e.jsx(l,{className:"text-[#88cc14]"})]})}),e.jsx("div",{id:"mobile-progress",className:"hidden lg:block sticky top-24 h-[calc(100vh-120px)] overflow-y-auto pb-6 pr-2 -mr-2",children:e.jsxs("div",{className:`${r?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:[e.jsx("h3",{className:"font-bold mb-4",children:"Module Progress"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"font-medium",children:"Introduction"}),e.jsxs("span",{className:"flex items-center text-green-500",children:[e.jsx(l,{className:"mr-1"})," Free"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden",children:e.jsx("div",{className:"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:bg-green-400",style:{width:"100%"},children:e.jsx("div",{className:"absolute inset-0 bg-white/20 animate-pulse"})})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"font-medium",children:"Core Concepts"}),e.jsxs("span",{className:"flex items-center text-gray-500",children:[e.jsx(g,{className:"mr-1"})," Premium"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden",children:e.jsx("div",{className:"bg-gray-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:w-[10%]",style:{width:"0%"}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"font-medium",children:"Practical Applications"}),e.jsxs("span",{className:"flex items-center text-gray-500",children:[e.jsx(g,{className:"mr-1"})," Premium"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden",children:e.jsx("div",{className:"bg-gray-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:w-[10%]",style:{width:"0%"}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"font-medium",children:"Hands-on Lab"}),e.jsxs("span",{className:"flex items-center text-gray-500",children:[e.jsx(g,{className:"mr-1"})," Premium"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden",children:e.jsx("div",{className:"bg-gray-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:w-[10%]",style:{width:"0%"}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"font-medium",children:"Assessment Quiz"}),e.jsxs("span",{className:"flex items-center text-gray-500",children:[e.jsx(g,{className:"mr-1"})," Premium"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden",children:e.jsx("div",{className:"bg-gray-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:w-[10%]",style:{width:"0%"}})})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(h,{to:"/signup",className:"block w-full text-center px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors",children:"Unlock Full Module"})}),e.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-700",children:[e.jsx("h3",{className:"font-bold mb-4",children:"Preview Content"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center text-green-500",children:[e.jsx(l,{className:"mr-2"})," Introduction to ",n.title]}),(w=(v=n.content)==null?void 0:v.sections)==null?void 0:w.slice(0,2).map((s,t)=>{var c,p;return((p=(c=n.content)==null?void 0:c.section_details)==null?void 0:p[s])?e.jsxs("div",{className:"flex items-center text-green-500",children:[e.jsx(l,{className:"mr-2"})," Core concepts and terminology"]},t):null}),e.jsxs("div",{className:"flex items-center text-green-500",children:[e.jsx(l,{className:"mr-2"})," Hands-on practice exercises"]})]})]}),e.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-700",children:[e.jsx("h3",{className:"font-bold mb-4",children:"Related Modules"}),e.jsx("div",{className:"space-y-3",children:T.filter(s=>s.category.name===n.category.name&&s.id!==n.id).slice(0,2).map(s=>e.jsx("div",{className:`p-3 rounded-lg ${r?"bg-[#252D4A] hover:bg-[#2A3356]":"bg-gray-50 hover:bg-gray-100"} transition-colors`,children:e.jsxs(h,{to:`/learn/preview/${s.slug}`,className:"flex items-start",children:[e.jsx("div",{className:"w-8 h-8 bg-[#88cc14]/20 rounded-full flex items-center justify-center mr-3",children:s.icon}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:s.title}),e.jsx("p",{className:`text-xs ${r?"text-gray-400":"text-gray-600"}`,children:s.difficulty.name})]})]})},s.id))})]})]})})]}),e.jsx("div",{className:"lg:col-span-3 order-1 lg:order-2",children:e.jsxs("div",{className:`${r?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-4 sm:p-6 mb-6`,children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center gap-3 mb-6 text-center sm:text-left",children:[e.jsx("div",{className:"w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center mb-2 sm:mb-0",children:e.jsx(F,{className:"text-[#88cc14]"})}),e.jsx("h2",{className:"text-xl sm:text-2xl font-bold",children:n.title})]}),e.jsxs("div",{className:`${r?"text-gray-300":"text-gray-700"} leading-relaxed text-sm sm:text-base`,children:[e.jsx("p",{className:"text-base sm:text-lg mb-6",children:((k=n.content)==null?void 0:k.introduction)||`Welcome to this introductory lesson on ${n.title}. This free preview will give you a taste of what the full module offers.`}),e.jsxs("div",{className:"flex flex-wrap border-b border-gray-700 mb-6",children:[e.jsx("button",{onClick:()=>j("content"),className:`px-3 sm:px-4 py-2 text-sm sm:text-base font-medium ${m==="content"?"border-b-2 border-[#88cc14] text-[#88cc14]":"text-gray-400 hover:text-gray-300"}`,children:"Content"}),e.jsx("button",{onClick:()=>j("exercises"),className:`px-3 sm:px-4 py-2 text-sm sm:text-base font-medium ${m==="exercises"?"border-b-2 border-[#88cc14] text-[#88cc14]":"text-gray-400 hover:text-gray-300"}`,children:"Exercises"}),e.jsx("button",{onClick:()=>j("quiz"),className:`px-3 sm:px-4 py-2 text-sm sm:text-base font-medium ${m==="quiz"?"border-b-2 border-[#88cc14] text-[#88cc14]":"text-gray-400 hover:text-gray-300"}`,children:"Quiz"}),e.jsxs("div",{className:"ml-auto flex items-center text-xs text-gray-400 px-2",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-green-500 mr-1"}),e.jsx("span",{children:"Progress saved locally"})]})]}),m==="content"&&e.jsxs(e.Fragment,{children:[i==="intro-to-cybersecurity"&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"Learning Objectives"}),e.jsx("ul",{className:"space-y-2 list-disc pl-5",children:((A=(S=n.content)==null?void 0:S.sections)==null?void 0:A.map((s,t)=>{var c,p;const x=(p=(c=n.content)==null?void 0:c.section_details)==null?void 0:p[s];return x?e.jsxs("li",{children:["Understand ",x.title.toLowerCase()]},t):null}))||[e.jsx("li",{children:"Understand the core principles of cybersecurity"},"default1"),e.jsx("li",{children:"Learn about common threats and vulnerabilities"},"default2"),e.jsx("li",{children:"Explore basic security practices and controls"},"default3")]})]})}),i==="web-security-fundamentals"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"The Vulnerable Storefront: A Web Security Tale"}),e.jsxs("div",{className:"p-4 bg-[#1A1F35] rounded-lg border border-gray-700 mb-4",children:[e.jsx("p",{className:"mb-3",children:"Alex had just launched his dream e-commerce site selling custom artwork. Business was booming until one day, customers started reporting unauthorized charges on their credit cards after shopping on his site."}),e.jsx("p",{className:"mb-3",children:"An investigation revealed that hackers had exploited a Cross-Site Scripting (XSS) vulnerability in his website's comment section. The attackers injected malicious JavaScript code that captured payment information as customers entered it."}),e.jsx("p",{className:"mb-3",children:"Additionally, they discovered an SQL injection vulnerability in the site's search function that allowed attackers to access the customer database directly. The breach not only cost Alex financially but damaged his reputation with customers."}),e.jsx("p",{children:"Working with a security consultant, Alex implemented proper input validation, parameterized queries, content security policies, and HTTPS encryption. He also set up regular security scans and penetration testing. The site was now secure, but Alex learned that web security should have been a priority from day one, not an afterthought."})]}),e.jsx("p",{className:"text-sm text-gray-400 italic",children:"This story demonstrates how common web vulnerabilities can impact real businesses and why implementing proper security measures is essential for any web application."})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"Web Security Fundamentals"}),e.jsx("p",{className:"mb-4",children:"Web security focuses on protecting websites, web applications, and web services from security threats that could compromise data, disrupt service, or damage reputation."}),e.jsxs("div",{className:"bg-[#2A3356] p-4 rounded-lg mb-6 border border-[#3D4976]",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center",children:e.jsx(I,{className:"text-yellow-500"})}),e.jsx("h4",{className:"font-bold text-yellow-400",children:"Think of it like..."})]}),e.jsx("p",{children:"Web security is like securing a storefront. You need locks on the doors (authentication), security cameras (logging), alarm systems (intrusion detection), and trained staff (secure coding practices) to protect your valuable merchandise (data)."})]}),e.jsx("h4",{className:"font-bold text-lg mb-3",children:"Common Web Security Threats"}),e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center mt-1 mr-3",children:e.jsx("span",{className:"text-red-500 font-bold",children:"1"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-bold",children:"Cross-Site Scripting (XSS)"}),e.jsx("p",{children:"Attackers inject malicious scripts into trusted websites that execute in users' browsers, potentially stealing cookies, session tokens, or other sensitive information."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center mt-1 mr-3",children:e.jsx("span",{className:"text-red-500 font-bold",children:"2"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-bold",children:"SQL Injection"}),e.jsx("p",{children:"Attackers insert malicious SQL code into database queries through web application inputs, potentially allowing them to access, modify, or delete data."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center mt-1 mr-3",children:e.jsx("span",{className:"text-red-500 font-bold",children:"3"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-bold",children:"Cross-Site Request Forgery (CSRF)"}),e.jsx("p",{children:"Attackers trick authenticated users into executing unwanted actions on websites where they're logged in, by exploiting the trust a site has in a user's browser."})]})]})]}),e.jsx("h4",{className:"font-bold text-lg mb-3",children:"Key Protection Measures"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h5",{className:"font-bold mb-2",children:"HTTPS & TLS"}),e.jsx("p",{children:"Encrypting data in transit between clients and servers to prevent eavesdropping and man-in-the-middle attacks."})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h5",{className:"font-bold mb-2",children:"Content Security Policy"}),e.jsx("p",{children:"Restricting which resources can be loaded and executed on your website to mitigate XSS attacks."})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h5",{className:"font-bold mb-2",children:"Input Validation"}),e.jsx("p",{children:"Sanitizing and validating all user inputs to prevent injection attacks like SQL injection and XSS."})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h5",{className:"font-bold mb-2",children:"Authentication & Authorization"}),e.jsx("p",{children:"Implementing strong user authentication and proper access controls to protect sensitive functionality and data."})]})]})]})]}),i==="network-security-basics"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"The Hospital Breach: A Network Security Case Study"}),e.jsxs("div",{className:"p-4 bg-[#1A1F35] rounded-lg border border-gray-700 mb-4",children:[e.jsx("p",{className:"mb-3",children:"Memorial Hospital prided itself on its modern medical facilities, but its network security hadn't been updated in years. One winter morning, the hospital's systems began behaving strangely—medical records were inaccessible, and equipment was going offline."}),e.jsx("p",{className:"mb-3",children:"The IT team discovered they were experiencing a massive Distributed Denial of Service (DDoS) attack. While the staff was distracted by this obvious attack, hackers had quietly exploited an unpatched vulnerability in the hospital's network perimeter to gain access to internal systems."}),e.jsx("p",{className:"mb-3",children:"The attackers had been inside the network for weeks, moving laterally between systems through unsegmented networks. They had accessed sensitive patient data and were preparing to deploy ransomware across the entire hospital infrastructure."}),e.jsx("p",{children:"Fortunately, an alert security analyst noticed unusual traffic patterns and isolated critical systems before the ransomware could be fully deployed. In the aftermath, the hospital implemented a defense-in-depth strategy: properly configured firewalls, network segmentation, intrusion detection systems, regular patching, and 24/7 monitoring. The incident became a powerful reminder that in network security, a single vulnerability can put an entire organization at risk."})]}),e.jsx("p",{className:"text-sm text-gray-400 italic",children:"This story illustrates the importance of comprehensive network security measures and how multiple layers of protection work together to safeguard critical infrastructure."})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"Network Security Basics"}),e.jsx("p",{className:"mb-4",children:"Network security involves protecting the integrity, confidentiality, and accessibility of computer networks and data using both hardware and software technologies."}),e.jsxs("div",{className:"bg-[#2A3356] p-4 rounded-lg mb-6 border border-[#3D4976]",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center",children:e.jsx(I,{className:"text-yellow-500"})}),e.jsx("h4",{className:"font-bold text-yellow-400",children:"Think of it like..."})]}),e.jsx("p",{children:"Network security is like securing a castle. You have walls (firewalls), guards at the gates (access controls), moats (network segmentation), watchtowers (intrusion detection systems), and secure communication channels (encryption) to protect everything inside."})]}),e.jsx("h4",{className:"font-bold text-lg mb-3",children:"Network Security Components"}),e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mt-1 mr-3",children:e.jsx("span",{className:"text-blue-500 font-bold",children:"1"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-bold",children:"Firewalls"}),e.jsx("p",{children:"Hardware or software barriers that monitor and control incoming and outgoing network traffic based on predetermined security rules."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mt-1 mr-3",children:e.jsx("span",{className:"text-blue-500 font-bold",children:"2"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-bold",children:"Intrusion Detection/Prevention Systems"}),e.jsx("p",{children:"Systems that monitor network traffic for suspicious activity and policy violations, alerting administrators or taking automated actions when threats are detected."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mt-1 mr-3",children:e.jsx("span",{className:"text-blue-500 font-bold",children:"3"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-bold",children:"Virtual Private Networks (VPNs)"}),e.jsx("p",{children:"Encrypted connections that provide secure access to a private network over public infrastructure, protecting data in transit."})]})]})]}),e.jsx("h4",{className:"font-bold text-lg mb-3",children:"Common Network Attacks"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h5",{className:"font-bold mb-2",children:"Denial of Service (DoS)"}),e.jsx("p",{children:"Overwhelming a system with traffic or requests to make it unavailable to legitimate users."})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h5",{className:"font-bold mb-2",children:"Man-in-the-Middle"}),e.jsx("p",{children:"Intercepting and potentially altering communications between two parties without their knowledge."})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h5",{className:"font-bold mb-2",children:"ARP Spoofing"}),e.jsx("p",{children:"Linking an attacker's MAC address with a legitimate IP address, allowing them to intercept network traffic."})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h5",{className:"font-bold mb-2",children:"Port Scanning"}),e.jsx("p",{children:"Probing a network to identify open ports and potential vulnerabilities that could be exploited."})]})]})]})]})]}),m==="content"&&i==="intro-to-cybersecurity"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"The Digital Fortress: A Cybersecurity Story"}),e.jsxs("div",{className:"p-4 bg-[#1A1F35] rounded-lg border border-gray-700 mb-4",children:[e.jsx("p",{className:"mb-3",children:"Sarah, a small business owner, never thought much about cybersecurity until one morning when she couldn't access her company's customer database. A message appeared on her screen demanding $10,000 in Bitcoin to restore her data."}),e.jsx("p",{className:"mb-3",children:"Her business had fallen victim to ransomware—malicious software that encrypted all her important files. The attack had come through a seemingly innocent email attachment opened by one of her employees."}),e.jsx("p",{className:"mb-3",children:"After a costly recovery process and nearly a week of downtime, Sarah implemented proper security measures: regular backups, employee training on recognizing phishing attempts, strong password policies, and updated software on all devices."}),e.jsx("p",{children:"Six months later, when a similar attack targeted businesses in her area, Sarah's digital fortress remained secure. The same security principles that protected Sarah's business—confidentiality, integrity, and availability—form the foundation of all effective cybersecurity strategies."})]}),e.jsx("p",{className:"text-sm text-gray-400 italic",children:"This story illustrates how basic cybersecurity principles can protect against common threats and why they matter to everyone, not just large organizations."})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"Interactive Security Diagram"}),e.jsx("p",{className:"mb-4",children:"Explore the different components of a secure network by hovering over each element:"}),e.jsx("div",{className:"relative w-full h-[300px] md:h-[400px] bg-[#1A1F35] rounded-lg border border-gray-700 p-4 mb-6 overflow-hidden",children:e.jsxs("div",{className:"absolute inset-0 flex items-center justify-center",children:[e.jsxs("svg",{className:"absolute inset-0 w-full h-full",style:{zIndex:0},children:[e.jsx("line",{x1:"28%",y1:"50%",x2:"42%",y2:"30%",stroke:"#4B5563",strokeWidth:"2",strokeDasharray:"5,5"}),e.jsx("line",{x1:"28%",y1:"50%",x2:"42%",y2:"70%",stroke:"#4B5563",strokeWidth:"2",strokeDasharray:"5,5"}),e.jsx("line",{x1:"50%",y1:"38%",x2:"50%",y2:"62%",stroke:"#4B5563",strokeWidth:"2"}),e.jsx("line",{x1:"58%",y1:"30%",x2:"72%",y2:"40%",stroke:"#4B5563",strokeWidth:"2"}),e.jsx("line",{x1:"58%",y1:"70%",x2:"72%",y2:"70%",stroke:"#4B5563",strokeWidth:"2",strokeDasharray:"5,5"})]}),e.jsxs("div",{className:"absolute left-[20%] top-[50%] transform -translate-y-1/2 group cursor-pointer",children:[e.jsx("div",{className:"w-16 h-16 bg-red-900/30 rounded-full flex items-center justify-center border-2 border-red-500/50 group-hover:bg-red-900/50 transition-all duration-300",children:e.jsx(D,{className:"text-red-400 text-2xl group-hover:text-red-300"})}),e.jsxs("div",{className:"opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none",children:[e.jsx("div",{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"}),e.jsx("p",{className:"font-bold mb-1",children:"Firewall"}),e.jsx("p",{children:"Monitors and filters incoming and outgoing network traffic based on security rules."})]})]}),e.jsxs("div",{className:"absolute left-[50%] top-[30%] transform -translate-x-1/2 -translate-y-1/2 group cursor-pointer",children:[e.jsx("div",{className:"w-16 h-16 bg-blue-900/30 rounded-full flex items-center justify-center border-2 border-blue-500/50 group-hover:bg-blue-900/50 transition-all duration-300",children:e.jsx($,{className:"text-blue-400 text-2xl group-hover:text-blue-300"})}),e.jsxs("div",{className:"opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none",children:[e.jsx("div",{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"}),e.jsx("p",{className:"font-bold mb-1",children:"Secure Server"}),e.jsx("p",{children:"Hosts applications and services with security patches and hardened configurations."})]})]}),e.jsxs("div",{className:"absolute left-[50%] top-[70%] transform -translate-x-1/2 -translate-y-1/2 group cursor-pointer",children:[e.jsx("div",{className:"w-16 h-16 bg-green-900/30 rounded-full flex items-center justify-center border-2 border-green-500/50 group-hover:bg-green-900/50 transition-all duration-300",children:e.jsx(q,{className:"text-green-400 text-2xl group-hover:text-green-300"})}),e.jsxs("div",{className:"opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none",children:[e.jsx("div",{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"}),e.jsx("p",{className:"font-bold mb-1",children:"Encrypted Database"}),e.jsx("p",{children:"Stores sensitive data with encryption at rest and in transit."})]})]}),e.jsxs("div",{className:"absolute right-[20%] top-[40%] transform -translate-y-1/2 group cursor-pointer",children:[e.jsx("div",{className:"w-16 h-16 bg-purple-900/30 rounded-full flex items-center justify-center border-2 border-purple-500/50 group-hover:bg-purple-900/50 transition-all duration-300",children:e.jsx(M,{className:"text-purple-400 text-2xl group-hover:text-purple-300"})}),e.jsxs("div",{className:"opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none",children:[e.jsx("div",{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"}),e.jsx("p",{className:"font-bold mb-1",children:"Client Computer"}),e.jsx("p",{children:"End-user device with antivirus, firewall, and security updates."})]})]}),e.jsxs("div",{className:"absolute right-[20%] top-[70%] transform -translate-y-1/2 group cursor-pointer",children:[e.jsx("div",{className:"w-16 h-16 bg-yellow-900/30 rounded-full flex items-center justify-center border-2 border-yellow-500/50 group-hover:bg-yellow-900/50 transition-all duration-300",children:e.jsx(R,{className:"text-yellow-400 text-2xl group-hover:text-yellow-300"})}),e.jsxs("div",{className:"opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none",children:[e.jsx("div",{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"}),e.jsx("p",{className:"font-bold mb-1",children:"Threat Actor"}),e.jsx("p",{children:"Malicious entity attempting to gain unauthorized access to systems and data."})]})]})]})}),e.jsx("p",{className:"text-sm text-gray-400 italic",children:"Hover or tap on each component to learn more about its role in cybersecurity."}),e.jsxs("div",{className:"md:hidden mt-4 space-y-2",children:[e.jsxs("div",{className:"flex items-center p-2 bg-[#252D4A] rounded border border-gray-700",children:[e.jsx("div",{className:"w-8 h-8 bg-red-900/30 rounded-full flex items-center justify-center border-2 border-red-500/50 mr-3",children:e.jsx(D,{className:"text-red-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-bold text-sm",children:"Firewall"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Monitors and filters network traffic"})]})]}),e.jsxs("div",{className:"flex items-center p-2 bg-[#252D4A] rounded border border-gray-700",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-900/30 rounded-full flex items-center justify-center border-2 border-blue-500/50 mr-3",children:e.jsx($,{className:"text-blue-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-bold text-sm",children:"Secure Server"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Hosts applications and services"})]})]}),e.jsxs("div",{className:"flex items-center p-2 bg-[#252D4A] rounded border border-gray-700",children:[e.jsx("div",{className:"w-8 h-8 bg-green-900/30 rounded-full flex items-center justify-center border-2 border-green-500/50 mr-3",children:e.jsx(q,{className:"text-green-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-bold text-sm",children:"Encrypted Database"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Stores sensitive data securely"})]})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"Code Example"}),e.jsxs("div",{className:`${r?"bg-[#1A1F35]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"} mb-4`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"mr-2 text-[#88cc14]"}),e.jsx("span",{className:"font-mono font-medium",children:"Simple Encryption Example"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"px-2 py-1 text-xs bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors",children:"Copy"}),e.jsx("button",{className:"px-2 py-1 text-xs bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors",children:"Run"})]})]}),e.jsx("pre",{className:"font-mono text-sm overflow-x-auto p-2 bg-[#0B1120] rounded",children:e.jsx("code",{className:"text-gray-300",children:`# Simple Caesar Cipher implementation
def encrypt(text, shift):
    result = ""
    # traverse the plain text
    for char in text:
        if char.isalpha():
            # Determine if uppercase or lowercase
            ascii_offset = ord('A') if char.isupper() else ord('a')
            # shift the current character
            result += chr((ord(char) - ascii_offset + shift) % 26 + ascii_offset)
        else:
            # If not a letter, keep it as is
            result += char
    return result

# Example usage
message = "Hello World"
shift = 3
encrypted = encrypt(message, shift)
print(f"Original: {message}")
print(f"Encrypted: {encrypted}")`})})]})]})]}),m==="exercises"&&e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"Interactive Exercises"}),i==="intro-to-cybersecurity"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-5 rounded-lg border ${r?"border-gray-700":"border-gray-200"} mb-6`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"font-bold",children:"Security Audit Challenge"}),e.jsx("span",{className:"px-2 py-1 bg-blue-900/30 text-blue-400 rounded text-xs font-medium",children:"Interactive Demo"})]}),e.jsx("p",{className:"mb-4",children:"Identify security vulnerabilities in this virtual office environment. Click on areas that might pose security risks."}),e.jsx("div",{className:"relative w-full h-[300px] bg-[#1A1F35] rounded-lg border border-gray-700 mb-4 overflow-hidden",children:e.jsx("div",{className:"absolute inset-0 p-4 flex flex-col",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 h-full",children:[e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{className:"relative bg-[#252D4A] rounded p-3 flex-1 cursor-pointer hover:bg-[#2A3356] transition-colors",onClick:()=>{const s=JSON.parse(localStorage.getItem("securityIssuesFound")||"[]");s.includes("password-postit")||(s.push("password-postit"),localStorage.setItem("securityIssuesFound",JSON.stringify(s)),alert("Issue found: Password written on a post-it note! This is a security risk as anyone can see it."))},children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Workstation 1"}),e.jsx("span",{className:"text-xs text-gray-400",children:"Marketing Dept"})]}),e.jsxs("div",{className:"mt-2 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-700 rounded-full mr-2"}),e.jsx("div",{className:"text-xs",children:"John\\'s Computer"})]}),e.jsx("div",{className:"absolute bottom-2 right-2 w-6 h-6 bg-yellow-200 rotate-3 flex items-center justify-center",children:e.jsx("span",{className:"text-[8px] text-black",children:"PWD: admin123"})})]}),e.jsxs("div",{className:"relative bg-[#252D4A] rounded p-3 flex-1 cursor-pointer hover:bg-[#2A3356] transition-colors",onClick:()=>{const s=JSON.parse(localStorage.getItem("securityIssuesFound")||"[]");s.includes("unlocked-computer")||(s.push("unlocked-computer"),localStorage.setItem("securityIssuesFound",JSON.stringify(s)),alert("Issue found: Unlocked computer with no screen timeout! This allows unauthorized access when the user is away."))},children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Workstation 2"}),e.jsx("span",{className:"text-xs text-gray-400",children:"Sales Dept"})]}),e.jsxs("div",{className:"mt-2 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-700 rounded-full mr-2"}),e.jsx("div",{className:"text-xs",children:"Sarah's Computer"})]}),e.jsx("div",{className:"absolute top-2 right-2 text-xs text-green-400",children:"Unlocked"})]})]}),e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{className:"relative bg-[#252D4A] rounded p-3 flex-1 cursor-pointer hover:bg-[#2A3356] transition-colors",onClick:()=>{const s=JSON.parse(localStorage.getItem("securityIssuesFound")||"[]");s.includes("outdated-software")||(s.push("outdated-software"),localStorage.setItem("securityIssuesFound",JSON.stringify(s)),alert("Issue found: Server running outdated software with known vulnerabilities! This needs to be updated immediately."))},children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"File Server"}),e.jsx("span",{className:"text-xs text-gray-400",children:"Main Office"})]}),e.jsxs("div",{className:"mt-2 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-700 rounded mr-2 flex items-center justify-center",children:e.jsx("div",{className:"w-4 h-4 bg-blue-500 rounded"})}),e.jsx("div",{className:"text-xs",children:"FileShare-01"})]}),e.jsx("div",{className:"absolute bottom-2 right-2 text-xs text-red-400",children:"OS v2.1 (2018)"})]}),e.jsxs("div",{className:"relative bg-[#252D4A] rounded p-3 flex-1 cursor-pointer hover:bg-[#2A3356] transition-colors",onClick:()=>{const s=JSON.parse(localStorage.getItem("securityIssuesFound")||"[]");s.includes("open-wifi")||(s.push("open-wifi"),localStorage.setItem("securityIssuesFound",JSON.stringify(s)),alert("Issue found: Open WiFi network with no password! This allows anyone to connect and potentially access internal resources."))},children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Office WiFi"}),e.jsx("span",{className:"text-xs text-gray-400",children:"All Floors"})]}),e.jsxs("div",{className:"mt-2 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-700 rounded-full mr-2 flex items-center justify-center",children:e.jsx("div",{className:"text-white text-xs",children:"WiFi"})}),e.jsx("div",{className:"text-xs",children:"CompanyNet"})]}),e.jsx("div",{className:"absolute top-2 right-2 text-xs text-red-400",children:"Open Network"})]})]})]})})}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm font-medium",children:"Security Issues Found"}),e.jsx("span",{className:"text-sm",id:"security-issues-count",children:"0/4"})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden",children:e.jsx("div",{className:"bg-[#88cc14] h-2.5 rounded-full transition-all duration-500 ease-out",id:"security-issues-progress",style:{width:"0%"}})})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>{localStorage.setItem("securityIssuesFound","[]"),document.getElementById("security-issues-count").textContent="0/4",document.getElementById("security-issues-progress").style.width="0%"},className:"px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors text-sm",children:"Reset Challenge"}),e.jsx("p",{className:"text-sm text-gray-400 italic",children:"Click on areas that look insecure"})]}),e.jsx("script",{dangerouslySetInnerHTML:{__html:`
                            function updateSecurityProgress() {
                              const foundIssues = JSON.parse(localStorage.getItem('securityIssuesFound') || '[]');
                              const count = foundIssues.length;
                              const progressElement = document.getElementById('security-issues-progress');
                              const countElement = document.getElementById('security-issues-count');

                              if (progressElement && countElement) {
                                progressElement.style.width = (count / 4 * 100) + '%';
                                countElement.textContent = count + '/4';

                                if (count === 4) {
                                  setTimeout(() => {
                                    alert('Congratulations! You found all the security issues. Sign up to access more advanced security audit challenges!');
                                  }, 500);
                                }
                              }
                            }

                            // Run on page load
                            updateSecurityProgress();

                            // Set up a mutation observer to watch for DOM changes
                            const observer = new MutationObserver(updateSecurityProgress);
                            observer.observe(document.body, { subtree: true, childList: true });
                          `}})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-5 rounded-lg border ${r?"border-gray-700":"border-gray-200"} mb-4`,children:[e.jsx("h4",{className:"font-bold mb-3",children:"Password Strength Checker"}),e.jsx("p",{className:"mb-4",children:"Test your understanding of password security by evaluating the strength of these passwords:"}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2",children:"password123"}),e.jsx("span",{className:"px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium",children:"Very Weak"})]}),e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2",children:"P@ssw0rd!"}),e.jsx("span",{className:"px-2 py-1 bg-yellow-900/30 text-yellow-400 rounded text-xs font-medium",children:"Moderate"})]}),e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2",children:"uR5%9Lq*zX@2vB"}),e.jsx("span",{className:"px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium",children:"Strong"})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row items-center gap-2 mt-4",children:[e.jsx("input",{type:"text",placeholder:"Try your own password",className:"w-full p-2 bg-[#1A1F35] border border-gray-700 rounded",value:f,onChange:s=>E(s.target.value),onKeyPress:s=>s.key==="Enter"&&N()}),e.jsx("button",{onClick:N,className:"w-full sm:w-auto px-4 py-2 bg-[#88cc14] text-black rounded hover:bg-[#7ab811] transition-colors mt-2 sm:mt-0",children:"Check"})]}),d&&e.jsxs("div",{className:"mt-3 p-2 rounded border flex items-center",style:{backgroundColor:`${d.color==="red"?"rgba(220, 38, 38, 0.1)":d.color==="yellow"?"rgba(234, 179, 8, 0.1)":"rgba(34, 197, 94, 0.1)"}`,borderColor:`${d.color==="red"?"rgba(220, 38, 38, 0.3)":d.color==="yellow"?"rgba(234, 179, 8, 0.3)":"rgba(34, 197, 94, 0.3)"}`},children:[e.jsx("div",{className:"w-2 h-2 rounded-full mr-2",style:{backgroundColor:`${d.color==="red"?"rgb(220, 38, 38)":d.color==="yellow"?"rgb(234, 179, 8)":"rgb(34, 197, 94)"}`}}),e.jsx("span",{style:{color:`${d.color==="red"?"rgb(248, 113, 113)":d.color==="yellow"?"rgb(250, 204, 21)":"rgb(74, 222, 128)"}`},children:d.text})]}),e.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Note: Your password is not stored or transmitted"})]})]}),i==="web-security-fundamentals"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-5 rounded-lg border ${r?"border-gray-700":"border-gray-200"} mb-6`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"font-bold",children:"SQL Injection Challenge"}),e.jsx("span",{className:"px-2 py-1 bg-blue-900/30 text-blue-400 rounded text-xs font-medium",children:"Interactive Demo"})]}),e.jsx("p",{className:"mb-4",children:"Fix the vulnerable code to prevent SQL injection attacks. Replace the unsafe query with a parameterized query."}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between bg-[#1A1F35] rounded-t border border-gray-700 p-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-red-500 mr-1.5"}),e.jsx("div",{className:"w-3 h-3 rounded-full bg-yellow-500 mr-1.5"}),e.jsx("div",{className:"w-3 h-3 rounded-full bg-green-500"})]}),e.jsx("span",{className:"text-xs text-gray-400",children:"login.php"})]}),e.jsx("div",{className:"bg-[#0B1120] p-4 rounded-b border-x border-b border-gray-700 font-mono text-sm overflow-x-auto",children:e.jsx("pre",{className:"text-gray-300",children:e.jsx("code",{children:`<?php
// Get user input
$username = $_POST['username'];
$password = $_POST['password'];

// VULNERABLE CODE - SQL Injection possible!
$query = "SELECT * FROM users WHERE username = '$username' AND password = '$password'";
$result = mysqli_query($connection, $query);

// SECURE THIS CODE BELOW:
// ...
// ...
// ...
`})})})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Your Solution:"}),e.jsx("textarea",{id:"sql-solution",rows:"4",className:"w-full p-3 bg-[#1A1F35] border border-gray-700 rounded font-mono text-sm",placeholder:"Write your secure code here..."})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>{const s=document.getElementById("sql-solution").value.toLowerCase();s.includes("prepare")||s.includes("?")&&s.includes("bind_param")||s.includes("placeholder")&&s.includes("bindvalue")?(alert("Correct! You've used parameterized queries to prevent SQL injection. This is the right approach!"),localStorage.setItem("sqlChallengeCompleted","true"),document.getElementById("sql-challenge-status").textContent="Completed",document.getElementById("sql-challenge-status").className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium"):s.trim()===""?alert("Please enter your solution first."):alert("Not quite right. Your solution might still be vulnerable to SQL injection. Try using prepared statements with placeholders.")},className:"px-4 py-2 bg-[#88cc14] text-black rounded hover:bg-[#7ab811] transition-colors",children:"Check Solution"}),e.jsx("span",{id:"sql-challenge-status",className:"px-2 py-1 bg-yellow-900/30 text-yellow-400 rounded text-xs font-medium",children:localStorage.getItem("sqlChallengeCompleted")==="true"?"Completed":"Pending"})]}),e.jsxs("div",{className:"mt-4 p-3 bg-[#1A1F35] border border-gray-700 rounded",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Hint:"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Use prepared statements with placeholders (?) and bind the parameters separately from the SQL query."})]})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-5 rounded-lg border ${r?"border-gray-700":"border-gray-200"} mb-4`,children:[e.jsx("h4",{className:"font-bold mb-3",children:"SQL Injection Detector"}),e.jsx("p",{className:"mb-4",children:"Identify which of these inputs contain potential SQL injection attacks:"}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2 font-mono text-sm",children:"John Smith"}),e.jsx("span",{className:"px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium",children:"Safe"})]}),e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2 font-mono text-sm",children:"admin' OR 1=1 --"}),e.jsx("span",{className:"px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium",children:"SQL Injection"})]}),e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2 font-mono text-sm",children:"<EMAIL>"}),e.jsx("span",{className:"px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium",children:"Safe"})]}),e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2 font-mono text-sm",children:"'; DROP TABLE users; --"}),e.jsx("span",{className:"px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium",children:"SQL Injection"})]})]}),e.jsxs("div",{className:"mt-4 p-3 bg-[#1A1F35] border border-gray-700 rounded",children:[e.jsx("h5",{className:"font-medium mb-2",children:"How to Prevent SQL Injection:"}),e.jsxs("ul",{className:"list-disc pl-5 space-y-1 text-sm",children:[e.jsx("li",{children:"Use parameterized queries or prepared statements"}),e.jsx("li",{children:"Implement input validation and sanitization"}),e.jsx("li",{children:"Apply the principle of least privilege for database accounts"}),e.jsx("li",{children:"Use ORM (Object-Relational Mapping) libraries"})]})]})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-5 rounded-lg border ${r?"border-gray-700":"border-gray-200"} mb-4`,children:[e.jsx("h4",{className:"font-bold mb-3",children:"XSS Vulnerability Checker"}),e.jsx("p",{className:"mb-4",children:"Identify which of these inputs contain potential Cross-Site Scripting (XSS) attacks:"}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2 font-mono text-sm",children:"Hello, world!"}),e.jsx("span",{className:"px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium",children:"Safe"})]}),e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2 font-mono text-sm",children:"<script>alert('XSS')<\/script>"}),e.jsx("span",{className:"px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium",children:"XSS Attack"})]}),e.jsxs("div",{className:"flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700",children:[e.jsx("span",{className:"mr-2 font-mono text-sm",children:`img src="x" onerror="alert('XSS')"`}),e.jsx("span",{className:"px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium",children:"XSS Attack"})]})]})]})]}),i==="network-security-basics"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-5 rounded-lg border ${r?"border-gray-700":"border-gray-200"} mb-6`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"font-bold",children:"Network Traffic Analysis Challenge"}),e.jsx("span",{className:"px-2 py-1 bg-blue-900/30 text-blue-400 rounded text-xs font-medium",children:"Interactive Demo"})]}),e.jsx("p",{className:"mb-4",children:"Analyze this network traffic capture and identify suspicious activities. Select all packets that indicate potential security threats."}),e.jsx("div",{className:"mb-4 overflow-x-auto",children:e.jsxs("table",{className:"w-full min-w-full text-xs border border-gray-700 rounded",children:[e.jsx("thead",{className:"bg-[#1A1F35]",children:e.jsxs("tr",{children:[e.jsx("th",{className:"p-2 border-b border-r border-gray-700 text-left",children:"Select"}),e.jsx("th",{className:"p-2 border-b border-r border-gray-700 text-left",children:"Time"}),e.jsx("th",{className:"p-2 border-b border-r border-gray-700 text-left",children:"Source"}),e.jsx("th",{className:"p-2 border-b border-r border-gray-700 text-left",children:"Destination"}),e.jsx("th",{className:"p-2 border-b border-r border-gray-700 text-left",children:"Protocol"}),e.jsx("th",{className:"p-2 border-b border-gray-700 text-left",children:"Info"})]})}),e.jsxs("tbody",{className:"bg-[#0B1120]",children:[e.jsxs("tr",{className:"packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors",children:[e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:e.jsx("input",{type:"checkbox",className:"packet-checkbox","data-threat":"false"})}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"10:15:23"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"***********"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"8.8.8.8"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"DNS"}),e.jsx("td",{className:"p-2 border-b border-gray-700",children:"Standard DNS query for google.com"})]}),e.jsxs("tr",{className:"packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors",children:[e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:e.jsx("input",{type:"checkbox",className:"packet-checkbox","data-threat":"true"})}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"10:15:45"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"***********"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"***********"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"ARP"}),e.jsx("td",{className:"p-2 border-b border-gray-700 text-yellow-400",children:"ARP: Duplicate IP address detected"})]}),e.jsxs("tr",{className:"packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors",children:[e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:e.jsx("input",{type:"checkbox",className:"packet-checkbox","data-threat":"false"})}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"10:16:02"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"***********0"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"***********00"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"HTTPS"}),e.jsx("td",{className:"p-2 border-b border-gray-700",children:"TLSv1.2 Client Hello"})]}),e.jsxs("tr",{className:"packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors",children:[e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:e.jsx("input",{type:"checkbox",className:"packet-checkbox","data-threat":"true"})}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"10:16:30"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"************"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"***********"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"TCP"}),e.jsx("td",{className:"p-2 border-b border-gray-700 text-red-400",children:"Port scan detected (multiple ports)"})]}),e.jsxs("tr",{className:"packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors",children:[e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:e.jsx("input",{type:"checkbox",className:"packet-checkbox","data-threat":"false"})}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"10:17:05"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"***********5"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"***********"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"ICMP"}),e.jsx("td",{className:"p-2 border-b border-gray-700",children:"Echo (ping) request"})]}),e.jsxs("tr",{className:"packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors",children:[e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:e.jsx("input",{type:"checkbox",className:"packet-checkbox","data-threat":"true"})}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"10:17:45"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"***********0"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"192.168.1.255"}),e.jsx("td",{className:"p-2 border-b border-r border-gray-700",children:"UDP"}),e.jsx("td",{className:"p-2 border-b border-gray-700 text-red-400",children:"Excessive broadcast traffic (possible DoS)"})]})]})]})}),e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("button",{onClick:()=>{const s=document.querySelectorAll(".packet-checkbox");let t=0,x=0;s.forEach(c=>{c.getAttribute("data-threat")==="true"?(x++,c.checked&&t++):c.checked&&t--}),t===x?(alert("Great job! You correctly identified all the suspicious network traffic patterns."),localStorage.setItem("networkAnalysisCompleted","true"),document.getElementById("network-analysis-status").textContent="Completed",document.getElementById("network-analysis-status").className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium"):t<=0?alert("Try again. You missed some suspicious packets or selected normal traffic."):alert(`You found ${t} out of ${x} suspicious packets. Keep looking!`)},className:"px-4 py-2 bg-[#88cc14] text-black rounded hover:bg-[#7ab811] transition-colors",children:"Analyze Selected Packets"}),e.jsx("span",{id:"network-analysis-status",className:"px-2 py-1 bg-yellow-900/30 text-yellow-400 rounded text-xs font-medium",children:localStorage.getItem("networkAnalysisCompleted")==="true"?"Completed":"Pending"})]}),e.jsxs("div",{className:"p-3 bg-[#1A1F35] border border-gray-700 rounded",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Hint:"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Look for unusual patterns like port scans, duplicate IP addresses, excessive broadcast traffic, and other anomalies that could indicate an attack."})]})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-5 rounded-lg border ${r?"border-gray-700":"border-gray-200"} mb-4`,children:[e.jsx("h4",{className:"font-bold mb-3",children:"Firewall Rule Configuration"}),e.jsx("p",{className:"mb-4",children:"Determine which network traffic should be allowed or blocked based on security best practices:"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full min-w-full text-sm",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-700",children:[e.jsx("th",{className:"text-left py-2 px-3",children:"Source"}),e.jsx("th",{className:"text-left py-2 px-3",children:"Destination"}),e.jsx("th",{className:"text-left py-2 px-3",children:"Port"}),e.jsx("th",{className:"text-left py-2 px-3",children:"Protocol"}),e.jsx("th",{className:"text-left py-2 px-3",children:"Decision"})]})}),e.jsxs("tbody",{children:[e.jsxs("tr",{className:"border-b border-gray-700",children:[e.jsx("td",{className:"py-2 px-3",children:"Internet"}),e.jsx("td",{className:"py-2 px-3",children:"Web Server"}),e.jsx("td",{className:"py-2 px-3",children:"443"}),e.jsx("td",{className:"py-2 px-3",children:"HTTPS"}),e.jsx("td",{className:"py-2 px-3",children:e.jsx("span",{className:"px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs",children:"Allow"})})]}),e.jsxs("tr",{className:"border-b border-gray-700",children:[e.jsx("td",{className:"py-2 px-3",children:"Internet"}),e.jsx("td",{className:"py-2 px-3",children:"Database Server"}),e.jsx("td",{className:"py-2 px-3",children:"3306"}),e.jsx("td",{className:"py-2 px-3",children:"MySQL"}),e.jsx("td",{className:"py-2 px-3",children:e.jsx("span",{className:"px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs",children:"Block"})})]}),e.jsxs("tr",{className:"border-b border-gray-700",children:[e.jsx("td",{className:"py-2 px-3",children:"Admin IP"}),e.jsx("td",{className:"py-2 px-3",children:"All Servers"}),e.jsx("td",{className:"py-2 px-3",children:"22"}),e.jsx("td",{className:"py-2 px-3",children:"SSH"}),e.jsx("td",{className:"py-2 px-3",children:e.jsx("span",{className:"px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs",children:"Allow"})})]}),e.jsxs("tr",{children:[e.jsx("td",{className:"py-2 px-3",children:"Internet"}),e.jsx("td",{className:"py-2 px-3",children:"Internal Network"}),e.jsx("td",{className:"py-2 px-3",children:"Any"}),e.jsx("td",{className:"py-2 px-3",children:"Any"}),e.jsx("td",{className:"py-2 px-3",children:e.jsx("span",{className:"px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs",children:"Block"})})]})]})]})}),e.jsxs("div",{className:"mt-4 p-3 bg-[#1A1F35] border border-gray-700 rounded",children:[e.jsx("h5",{className:"font-medium mb-2",children:"Firewall Best Practices:"}),e.jsxs("ul",{className:"list-disc pl-5 space-y-1 text-sm",children:[e.jsx("li",{children:"Default deny policy: Block all traffic by default, then allow only what's necessary"}),e.jsx("li",{children:"Least privilege: Only open ports required for essential services"}),e.jsx("li",{children:"Segment networks: Use zones to isolate sensitive systems"}),e.jsx("li",{children:"Regular audits: Review and update firewall rules periodically"})]})]})]})]}),e.jsxs("div",{className:"mt-6 p-4 border border-dashed border-gray-700 rounded-lg",children:[e.jsxs("h4",{className:"font-bold mb-2 flex items-center",children:[e.jsx(g,{className:"mr-2 text-[#88cc14]"})," Premium Exercises"]}),e.jsx("p",{className:"mb-4",children:"Sign up to access more interactive exercises:"}),e.jsxs("ul",{className:"space-y-2",children:[i==="intro-to-cybersecurity"&&e.jsxs(e.Fragment,{children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx(l,{className:"text-gray-500 mr-2"})," Encryption Challenge"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(l,{className:"text-gray-500 mr-2"})," Security Risk Assessment"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(l,{className:"text-gray-500 mr-2"})," Social Engineering Simulator"]})]}),i==="web-security-fundamentals"&&e.jsxs(e.Fragment,{children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx(l,{className:"text-gray-500 mr-2"})," CSRF Attack Simulator"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(l,{className:"text-gray-500 mr-2"})," Secure Coding Practice Lab"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(l,{className:"text-gray-500 mr-2"})," Web Application Firewall Configuration"]})]}),i==="network-security-basics"&&e.jsxs(e.Fragment,{children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx(l,{className:"text-gray-500 mr-2"})," Network Traffic Analysis Lab"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(l,{className:"text-gray-500 mr-2"})," IDS/IPS Configuration Exercise"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(l,{className:"text-gray-500 mr-2"})," VPN Setup and Testing"]})]})]})]})]}),m==="quiz"&&e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 border-b border-gray-700 pb-2",children:"Knowledge Check Quiz"}),i==="intro-to-cybersecurity"&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"mb-4",children:"Test your understanding of cybersecurity basics with this quick quiz:"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h4",{className:"font-bold mb-2",children:"1. What is the primary goal of cybersecurity?"}),e.jsx("div",{className:"space-y-2 mt-3",children:["To make computers faster","To protect systems and data from attacks","To develop new software","To connect networks together"].map((s,t)=>e.jsx("div",{onClick:()=>u({...a,q1:t}),className:`p-3 rounded-lg border cursor-pointer transition-colors ${a.q1===t?"bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]":r?"bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]":"bg-white border-gray-200 hover:bg-gray-50"}`,children:s},t))}),o&&a.q1===1&&e.jsx("div",{className:"mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400",children:"Correct! Cybersecurity is all about protecting systems, networks, and data from digital attacks."}),o&&a.q1!==void 0&&a.q1!==1&&e.jsx("div",{className:"mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400",children:"Not quite. The primary goal of cybersecurity is to protect systems and data from attacks."})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h4",{className:"font-bold mb-2",children:"2. Which of the following is NOT one of the three main principles of information security?"}),e.jsx("div",{className:"space-y-2 mt-3",children:["Confidentiality","Integrity","Availability","Profitability"].map((s,t)=>e.jsx("div",{onClick:()=>u({...a,q2:t}),className:`p-3 rounded-lg border cursor-pointer transition-colors ${a.q2===t?"bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]":r?"bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]":"bg-white border-gray-200 hover:bg-gray-50"}`,children:s},t))}),o&&a.q2===3&&e.jsx("div",{className:"mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400",children:"Correct! The three main principles are Confidentiality, Integrity, and Availability (CIA triad)."}),o&&a.q2!==void 0&&a.q2!==3&&e.jsx("div",{className:"mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400",children:"Not quite. Profitability is not one of the three main principles of information security."})]})]})]}),i==="web-security-fundamentals"&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"mb-4",children:"Test your understanding of web security fundamentals with this quick quiz:"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h4",{className:"font-bold mb-2",children:"1. Which of the following is a common web application vulnerability?"}),e.jsx("div",{className:"space-y-2 mt-3",children:["Memory leaks","Cross-Site Scripting (XSS)","CPU throttling","Bandwidth limitation"].map((s,t)=>e.jsx("div",{onClick:()=>u({...a,q1:t}),className:`p-3 rounded-lg border cursor-pointer transition-colors ${a.q1===t?"bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]":r?"bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]":"bg-white border-gray-200 hover:bg-gray-50"}`,children:s},t))}),o&&a.q1===1&&e.jsx("div",{className:"mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400",children:"Correct! Cross-Site Scripting (XSS) is a common web application vulnerability where attackers inject malicious scripts into trusted websites."}),o&&a.q1!==void 0&&a.q1!==1&&e.jsx("div",{className:"mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400",children:"Not quite. Cross-Site Scripting (XSS) is a common web application vulnerability where attackers inject malicious scripts into trusted websites."})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h4",{className:"font-bold mb-2",children:"2. Which of the following is the best defense against SQL injection attacks?"}),e.jsx("div",{className:"space-y-2 mt-3",children:["Using more complex SQL queries","Parameterized queries","Disabling database access","Removing all forms from websites"].map((s,t)=>e.jsx("div",{onClick:()=>u({...a,q2:t}),className:`p-3 rounded-lg border cursor-pointer transition-colors ${a.q2===t?"bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]":r?"bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]":"bg-white border-gray-200 hover:bg-gray-50"}`,children:s},t))}),o&&a.q2===1&&e.jsx("div",{className:"mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400",children:"Correct! Parameterized queries (prepared statements) prevent SQL injection by separating SQL code from user input data."}),o&&a.q2!==void 0&&a.q2!==1&&e.jsx("div",{className:"mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400",children:"Not quite. Parameterized queries (prepared statements) are the best defense as they separate SQL code from user input data."})]})]})]}),i==="network-security-basics"&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"mb-4",children:"Test your understanding of network security basics with this quick quiz:"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h4",{className:"font-bold mb-2",children:"1. What is the primary purpose of a firewall in network security?"}),e.jsx("div",{className:"space-y-2 mt-3",children:["To speed up network traffic","To monitor and filter network traffic based on security rules","To encrypt all network communications","To compress data for faster transmission"].map((s,t)=>e.jsx("div",{onClick:()=>u({...a,q1:t}),className:`p-3 rounded-lg border cursor-pointer transition-colors ${a.q1===t?"bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]":r?"bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]":"bg-white border-gray-200 hover:bg-gray-50"}`,children:s},t))}),o&&a.q1===1&&e.jsx("div",{className:"mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400",children:"Correct! Firewalls monitor and filter network traffic based on predetermined security rules to protect networks from unauthorized access."}),o&&a.q1!==void 0&&a.q1!==1&&e.jsx("div",{className:"mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400",children:"Not quite. The primary purpose of a firewall is to monitor and filter network traffic based on security rules."})]}),e.jsxs("div",{className:`${r?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border ${r?"border-gray-700":"border-gray-200"}`,children:[e.jsx("h4",{className:"font-bold mb-2",children:"2. Which of the following is NOT a common network attack?"}),e.jsx("div",{className:"space-y-2 mt-3",children:["Denial of Service (DoS)","Man-in-the-Middle","ARP Spoofing","Database Normalization"].map((s,t)=>e.jsx("div",{onClick:()=>u({...a,q2:t}),className:`p-3 rounded-lg border cursor-pointer transition-colors ${a.q2===t?"bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]":r?"bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]":"bg-white border-gray-200 hover:bg-gray-50"}`,children:s},t))}),o&&a.q2===3&&e.jsx("div",{className:"mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400",children:"Correct! Database Normalization is a database design technique, not a network attack. The others are all common network attacks."}),o&&a.q2!==void 0&&a.q2!==3&&e.jsx("div",{className:"mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400",children:"Not quite. Database Normalization is a database design technique, not a network attack."})]})]})]}),e.jsx("div",{className:"flex justify-center mt-6",children:e.jsx("button",{onClick:()=>P(!0),className:"px-6 py-3 bg-[#88cc14] text-black font-medium rounded-lg hover:bg-[#7ab811] transition-colors",children:"Check Answers"})}),o&&e.jsxs("div",{className:"mt-4 p-4 border border-dashed border-gray-700 rounded-lg text-center",children:[e.jsx("h4",{className:"font-bold mb-2",children:"Want to continue learning?"}),e.jsx("p",{className:"mb-4",children:"Sign up to access the full module with more quizzes and interactive content."}),e.jsx(h,{to:"/signup",className:"inline-block px-6 py-2 bg-[#88cc14] text-black font-medium rounded-lg hover:bg-[#7ab811] transition-colors",children:"Create Free Account"})]})]}),e.jsxs("div",{className:`${r?"bg-yellow-900/20 border-yellow-900/30":"bg-yellow-50 border-yellow-200"} border p-5 rounded-lg my-8`,children:[e.jsxs("h3",{className:"text-xl font-bold mb-3 flex items-center",children:[e.jsx(g,{className:"mr-2 text-yellow-500"})," Premium Content Preview"]}),e.jsx("p",{className:"mb-4",children:"The full module includes comprehensive materials to deepen your understanding of cybersecurity:"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(l,{className:"text-green-500 mt-1 mr-2 flex-shrink-0"}),e.jsx("span",{children:"Interactive labs with real-world scenarios"})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(l,{className:"text-green-500 mt-1 mr-2 flex-shrink-0"}),e.jsx("span",{children:"Detailed explanations of advanced concepts"})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(l,{className:"text-green-500 mt-1 mr-2 flex-shrink-0"}),e.jsx("span",{children:"Practical exercises to apply your knowledge"})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(l,{className:"text-green-500 mt-1 mr-2 flex-shrink-0"}),e.jsx("span",{children:"Downloadable resources and cheat sheets"})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(l,{className:"text-green-500 mt-1 mr-2 flex-shrink-0"}),e.jsx("span",{children:"Assessment quizzes to test your understanding"})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(l,{className:"text-green-500 mt-1 mr-2 flex-shrink-0"}),e.jsx("span",{children:"Certificate of completion"})]})]})]})]}),e.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-700",children:[e.jsx("h3",{className:"text-xl font-bold mb-4 text-center sm:text-left",children:"Ready to continue learning?"}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsxs(h,{to:"/login",className:"w-full sm:w-auto px-6 py-3 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors flex items-center justify-center",children:[e.jsx(F,{className:"mr-2"})," Sign In to Continue Learning"]}),e.jsxs(h,{to:"/signup",className:"w-full sm:w-auto px-6 py-3 border-2 border-[#88cc14] text-[#88cc14] hover:bg-[#88cc14]/10 font-medium rounded-lg transition-colors flex items-center justify-center",children:[e.jsx(H,{className:"mr-2"})," Create Free Account"]})]})]})]})})]})]})}):e.jsx("div",{className:`min-h-screen ${r?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("div",{className:"mb-6",children:e.jsxs(h,{to:"/learn",className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(C,{className:"mr-2"})," ",e.jsx("span",{className:"hidden sm:inline",children:"Back to Learning Modules"}),e.jsx("span",{className:"sm:hidden",children:"Back"})]})}),e.jsxs("div",{className:`${r?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Module Not Found"}),e.jsx("p",{className:`${r?"text-gray-400":"text-gray-600"}`,children:"The learning module you're looking for doesn't exist or has been removed."})]})]})})};export{X as default};
