<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Page</title>
  <style>
    body {
      background-color: #0B1120;
      color: white;
      font-family: 'Arial', sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      text-align: center;
    }
    .container {
      max-width: 600px;
      padding: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      border: 1px solid #88cc14;
    }
    h1 {
      color: #88cc14;
    }
    p {
      margin-bottom: 20px;
    }
    .button {
      display: inline-block;
      background-color: #88cc14;
      color: black;
      padding: 10px 20px;
      border-radius: 5px;
      text-decoration: none;
      font-weight: bold;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Test Page</h1>
    <p>If you can see this page, basic HTML and CSS are working correctly.</p>
    <p>This is a test to determine if the issue is with the React application or with the HTML/CSS.</p>
    <a href="/" class="button">Back to Home</a>
  </div>
</body>
</html>
