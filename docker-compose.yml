version: '3.8'

services:
  # Frontend
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    volumes:
      - ./src:/app/src
      - ./public:/app/public
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:3001
    depends_on:
      - auth-service
      - learning-service
      - challenges-service
      - threats-service

  # Auth Service
  auth-service:
    build:
      context: .
      dockerfile: Dockerfile.service
    ports:
      - "3001:3001"
    volumes:
      - ./src/services/auth:/app/service
    environment:
      - NODE_ENV=development
      - PORT=3001
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
    command: node server.js

  # Learning Service
  learning-service:
    build:
      context: .
      dockerfile: Dockerfile.service
    ports:
      - "3002:3002"
    volumes:
      - ./src/services/learning:/app/service
    environment:
      - NODE_ENV=development
      - PORT=3002
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
    command: node server.js

  # Challenges Service
  challenges-service:
    build:
      context: .
      dockerfile: Dockerfile.service
    ports:
      - "3003:3003"
    volumes:
      - ./src/services/challenges:/app/service
    environment:
      - NODE_ENV=development
      - PORT=3003
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
    command: node server.js

  # AI Service
  ai-service:
    build:
      context: .
      dockerfile: Dockerfile.service
    ports:
      - "3004:3004"
    volumes:
      - ./src/services/ai:/app/service
    environment:
      - NODE_ENV=development
      - PORT=3004
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    command: node server.js

  # Threats Service
  threats-service:
    build:
      context: .
      dockerfile: Dockerfile.service
    ports:
      - "3005:3005"
    volumes:
      - ./src/services/threats:/app/service
    environment:
      - NODE_ENV=development
      - PORT=3005
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - ABUSEIPDB_API_KEY=********************************************************************************
    command: node server.js
