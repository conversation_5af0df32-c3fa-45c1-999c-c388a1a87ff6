import { useState, useEffect } from 'react';
import { colors } from '../config/colors';

export const useTheme = () => {
  const [theme, setTheme] = useState({
    primary: colors.primary.DEFAULT,
    primaryHover: colors.primary.hover,
    text: colors.text.primary,
    background: colors.background.primary,
  });

  const updateTheme = (newTheme) => {
    setTheme(newTheme);
    // Update CSS variables
    Object.entries(newTheme).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--color-${key}`, value);
    });
  };

  useEffect(() => {
    // Initialize CSS variables
    Object.entries(theme).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--color-${key}`, value);
    });
  }, []);

  return { theme, updateTheme };
};