[{"id": "c1", "slug": "sql-injection-basics", "title": "SQL Injection Basics", "description": "Learn the fundamentals of SQL injection by exploiting a vulnerable login form. This challenge introduces you to one of the most common web application vulnerabilities.", "category": {"id": "web-security", "name": "Web Security"}, "difficulty": {"id": "beginner", "name": "<PERSON><PERSON><PERSON>"}, "type": {"id": "exploitation", "name": "Exploitation"}, "points": 100, "coin_reward": 10, "estimated_time": 20, "is_free": true, "is_preview": true, "content": {"description": "In this challenge, you will learn about SQL injection, a common web application vulnerability. You are presented with a vulnerable login form. Your task is to bypass the authentication mechanism using SQL injection techniques.", "scenario": "You are testing the security of a web application. The application has a login form that is vulnerable to SQL injection. Your goal is to bypass the authentication and gain access to the admin panel.", "objectives": ["Understand how SQL injection works", "Identify the vulnerable input field", "Craft a SQL injection payload to bypass authentication", "Access the admin panel and capture the flag"], "challenge_files": [], "challenge_data": {"Target URL": "https://example.com/login (simulated in this challenge)"}, "flag_format": "flag{sql_injection_master}", "hints": [{"id": "h1", "hint": "Try entering a single quote (') in the username field to see if it causes an error.", "coin_cost": 5}, {"id": "h2", "hint": "The login query might look like: SELECT * FROM users WHERE username='[input]' AND password='[input]'", "coin_cost": 10}, {"id": "h3", "hint": "You can use OR 1=1 to make the WHERE clause always true.", "coin_cost": 15}]}}, {"id": "c2", "slug": "password-cracking-basics", "title": "Password Cracking Basics", "description": "Learn how to crack weak passwords using various techniques and tools. This challenge will teach you why strong passwords are important.", "category": {"id": "cryptography", "name": "Cryptography"}, "difficulty": {"id": "beginner", "name": "<PERSON><PERSON><PERSON>"}, "type": {"id": "analysis", "name": "Analysis"}, "points": 100, "coin_reward": 10, "estimated_time": 25, "is_free": true, "is_preview": true, "content": {"description": "In this challenge, you will learn about password cracking techniques. You are given a set of password hashes, and your task is to crack them using various methods.", "scenario": "You are a security analyst investigating a data breach. You have recovered a database of password hashes and need to determine how strong the passwords were.", "objectives": ["Understand different password hashing algorithms", "Learn how to use password cracking tools", "Crack the provided password hashes", "Analyze the strength of the cracked passwords"], "challenge_files": [{"name": "password_hashes.txt", "url": "#"}], "challenge_data": {"Hash 1 (MD5)": "5f4dcc3b5aa765d61d8327deb882cf99", "Hash 2 (SHA-1)": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "Hash 3 (bcrypt)": "$2a$10$N9qo8uLOickgx2ZMRZoMyeIjZAgcfl7p92ldGxad68LJZdL17lhWy"}, "flag_format": "flag{cracked_passwords_concatenated}", "hints": [{"id": "h4", "hint": "Try using online hash crackers for the MD5 and SHA-1 hashes.", "coin_cost": 5}, {"id": "h5", "hint": "The passwords are common words found in password dictionaries.", "coin_cost": 10}, {"id": "h6", "hint": "The first hash is for the word 'password'.", "coin_cost": 15}]}}, {"id": "c3", "slug": "network-traffic-analysis", "title": "Network Traffic Analysis", "description": "Analyze network traffic to identify suspicious activities and potential security threats. This challenge will teach you how to use packet analysis tools.", "category": {"id": "network-security", "name": "Network Security"}, "difficulty": {"id": "beginner", "name": "<PERSON><PERSON><PERSON>"}, "type": {"id": "analysis", "name": "Analysis"}, "points": 150, "coin_reward": 15, "estimated_time": 30, "is_free": true, "is_preview": true, "content": {"description": "In this challenge, you will analyze network traffic to identify suspicious activities. You are given a packet capture file (PCAP) containing network traffic from a compromised network.", "scenario": "You are a network security analyst investigating a potential data breach. You have been provided with a packet capture file containing network traffic from the time of the suspected breach.", "objectives": ["Analyze the packet capture file using Wireshark or similar tools", "Identify suspicious network connections", "Detect potential data exfiltration", "Find the flag hidden in the network traffic"], "challenge_files": [{"name": "capture.pcap", "url": "#"}], "challenge_data": {"Capture Time": "2023-05-15 14:30:00 - 15:00:00", "Network": "***********/24", "Suspicious IP": "Look for connections to unusual external IPs"}, "flag_format": "flag{exfiltrated_data}", "hints": [{"id": "h7", "hint": "Look for unusual HTTP or DNS traffic in the packet capture.", "coin_cost": 5}, {"id": "h8", "hint": "Data exfiltration often involves large amounts of data being sent to external servers.", "coin_cost": 10}, {"id": "h9", "hint": "Check for encoded data in DNS queries or HTTP requests.", "coin_cost": 15}]}}, {"id": "c4", "slug": "xss-attack-simulation", "title": "XSS Attack Simulation", "description": "Learn about Cross-Site Scripting (XSS) vulnerabilities by exploiting a vulnerable web application. This challenge will teach you how to identify and exploit XSS vulnerabilities.", "category": {"id": "web-security", "name": "Web Security"}, "difficulty": {"id": "intermediate", "name": "Intermediate"}, "type": {"id": "exploitation", "name": "Exploitation"}, "points": 200, "coin_reward": 20, "estimated_time": 35, "is_free": false, "is_preview": true, "content": {"description": "In this challenge, you will learn about Cross-Site Scripting (XSS), a common web application vulnerability. You are presented with a vulnerable web application. Your task is to identify and exploit XSS vulnerabilities to steal user cookies.", "scenario": "You are testing the security of a social media platform called 'FriendZone'. The platform allows users to create profiles and post comments. Your goal is to find and exploit XSS vulnerabilities in the comment system.", "objectives": ["Identify XSS vulnerabilities in the web application", "Craft a payload to steal user cookies", "Execute the payload and capture the flag"], "challenge_files": [], "challenge_data": {"Target URL": "https://friendzone.example.com (simulated in this challenge)"}, "flag_format": "flag{xss_master_2023}", "hints": [{"id": "h10", "hint": "Try entering special characters like < > \" '  in the comment field to see how the application responds.", "coin_cost": 5}, {"id": "h11", "hint": "The application might be filtering some characters or scripts. Try different encoding techniques to bypass filters.", "coin_cost": 10}, {"id": "h12", "hint": "A simple payload like <script>alert(1)</script> can help you test for XSS vulnerabilities.", "coin_cost": 15}]}}, {"id": "c5", "slug": "basic-cryptography", "title": "Basic Cryptography Challenge", "description": "Test your knowledge of basic cryptographic techniques including ciphers, encoding, and hashing. This challenge will introduce you to fundamental cryptographic concepts.", "category": {"id": "cryptography", "name": "Cryptography"}, "difficulty": {"id": "intermediate", "name": "Intermediate"}, "type": {"id": "puzzle", "name": "Puzzle"}, "points": 200, "coin_reward": 20, "estimated_time": 40, "is_free": false, "is_preview": true, "content": {"description": "In this challenge, you will solve a series of cryptographic puzzles involving different encryption and hashing algorithms. Each puzzle will give you a piece of the final flag.", "scenario": "A secret organization has encrypted their communications using various cryptographic techniques. Your task is to decrypt their messages and uncover their plans.", "objectives": ["Decrypt messages encrypted with different algorithms", "Solve cryptographic puzzles to reveal parts of the flag", "Combine the pieces to form the complete flag"], "challenge_files": [{"name": "encrypted_messages.txt", "url": "#"}], "challenge_data": {"Puzzle 1": "Caesar cipher: Wkh iluvw sduw ri wkh iodj lv 'fubswr'", "Puzzle 2": "Base64: VGhlIHNlY29uZCBwYXJ0IG9mIHRoZSBmbGFnIGlzICdfcHV6emxlJw==", "Puzzle 3": "MD5 hash: 5f4dcc3b5aa765d61d8327deb882cf99 (This is a common password)"}, "flag_format": "flag{part1_part2_part3}", "hints": [{"id": "h13", "hint": "The first puzzle is a Caesar cipher with a shift of 3.", "coin_cost": 5}, {"id": "h14", "hint": "The second puzzle is encoded in Base64. Try using an online Base64 decoder.", "coin_cost": 10}, {"id": "h15", "hint": "The third puzzle is an MD5 hash of a very common password.", "coin_cost": 15}]}}, {"id": "c6", "slug": "forensic-investigation", "title": "Digital Forensics Investigation", "description": "Analyze a disk image to find evidence of a security breach. This challenge will introduce you to digital forensics techniques and tools.", "category": {"id": "forensics", "name": "Digital Forensics"}, "difficulty": {"id": "advanced", "name": "Advanced"}, "type": {"id": "analysis", "name": "Analysis"}, "points": 300, "coin_reward": 30, "estimated_time": 60, "is_free": false, "is_preview": false, "content": {"description": "In this challenge, you will analyze a disk image to find evidence of a security breach. You will need to use digital forensics techniques and tools to recover deleted files, analyze logs, and identify suspicious activities.", "scenario": "A company suspects that one of their employees has been exfiltrating sensitive data. They have provided you with a disk image of the employee's computer. Your task is to analyze the disk image and find evidence of data exfiltration.", "objectives": ["Recover deleted files from the disk image", "Analyze system logs to identify suspicious activities", "Find evidence of data exfiltration", "Identify the exfiltrated data and how it was sent"], "challenge_files": [{"name": "disk_image.img (simulated in this challenge)", "url": "#"}], "flag_format": "flag{filename_date_exfiltration_method}", "hints": [{"id": "h16", "hint": "Look for recently deleted files in the trash or recycle bin.", "coin_cost": 10}, {"id": "h17", "hint": "Check browser history and download logs for suspicious activities.", "coin_cost": 15}, {"id": "h18", "hint": "Email attachments or cloud storage services are common methods for data exfiltration.", "coin_cost": 20}]}}]