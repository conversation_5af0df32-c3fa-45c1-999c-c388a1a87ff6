export const osIntroductionQuiz = {
  title: "Operating Systems Fundamentals",
  questions: [
    {
      question: "What is the primary function of an operating system?",
      options: [
        "To provide a user interface",
        "To manage hardware and software resources",
        "To run applications",
        "To connect to the internet"
      ],
      correct: 1,
      explanation: "The primary function of an OS is to manage hardware and software resources, ensuring efficient operation of the computer system."
    },
    {
      question: "Which type of operating system is best suited for servers?",
      options: [
        "Single-User, Single-Task",
        "Single-User, Multi-Task",
        "Multi-User",
        "Real-Time OS"
      ],
      correct: 2,
      explanation: "Multi-User operating systems are ideal for servers as they allow multiple users to access resources simultaneously and provide better security features."
    },
    {
      question: "What is a process in an operating system?",
      options: [
        "A program stored on the disk",
        "A program in execution",
        "A file containing code",
        "A system configuration"
      ],
      correct: 1,
      explanation: "A process is a program in execution, which includes the program code, its current activity, and resource allocations."
    },
    {
      question: "Which component of an OS handles memory allocation?",
      options: [
        "Process Manager",
        "Memory Manager",
        "File System",
        "I/O Manager"
      ],
      correct: 1,
      explanation: "The Memory Manager is responsible for allocating and deallocating memory space to processes and managing virtual memory."
    },
    {
      question: "What is the purpose of a system call?",
      options: [
        "To run user applications",
        "To interface between user programs and OS services",
        "To manage hardware directly",
        "To create new processes"
      ],
      correct: 1,
      explanation: "System calls provide an interface between user programs and OS services, allowing programs to request services from the operating system safely."
    }
  ]
};