export const processManagementQuiz = {
  title: "Process Management",
  questions: [
    {
      question: "What is a process control block (PCB)?",
      options: [
        "A block of memory allocated to a process",
        "A data structure containing process information",
        "A system call to control processes",
        "A type of process scheduling algorithm"
      ],
      correct: 1,
      explanation: "A Process Control Block (PCB) is a data structure that contains important information about a specific process, including its state, program counter, CPU registers, and memory management information."
    },
    {
      question: "Which state is a process in when it's currently executing?",
      options: [
        "Ready",
        "Running",
        "Waiting",
        "New"
      ],
      correct: 1,
      explanation: "A process is in the Running state when it is currently being executed by the CPU."
    },
    {
      question: "What happens during context switching?",
      options: [
        "A new process is created",
        "A process is terminated",
        "The CPU switches from one process to another",
        "Memory is allocated to a process"
      ],
      correct: 2,
      explanation: "Context switching occurs when the CPU switches from executing one process to another, saving the current process's state and loading the new process's state."
    },
    {
      question: "What is the purpose of process scheduling?",
      options: [
        "To create new processes",
        "To allocate CPU time to processes",
        "To manage process memory",
        "To terminate processes"
      ],
      correct: 1,
      explanation: "Process scheduling determines which process runs at a certain time and for how long, managing CPU time allocation among multiple processes."
    },
    {
      question: "What is a zombie process?",
      options: [
        "A process that consumes too much CPU",
        "A terminated process whose parent hasn't collected its exit status",
        "A process that never terminates",
        "A process with no memory allocated"
      ],
      correct: 1,
      explanation: "A zombie process is a process that has completed execution but still has an entry in the process table because its parent hasn't collected its exit status."
    }
  ]
};