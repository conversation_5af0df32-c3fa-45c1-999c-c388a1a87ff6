/**
 * Test accounts for different subscription tiers
 * These accounts can be used for testing the application without needing to sign up
 */

export const TEST_ACCOUNTS = {
  // Free tier account
  free: {
    email: '<EMAIL>',
    password: 'FreeUser123!',
    user_metadata: {
      username: 'free_user',
      full_name: 'Free User',
      subscription_tier: 'free',
      coins: 100
    }
  },
  
  // Premium tier account
  premium: {
    email: '<EMAIL>',
    password: 'PremiumUser123!',
    user_metadata: {
      username: 'premium_user',
      full_name: 'Premium User',
      subscription_tier: 'premium',
      coins: 500
    }
  },
  
  // Business tier account
  business: {
    email: '<EMAIL>',
    password: 'BusinessUser123!',
    user_metadata: {
      username: 'business_user',
      full_name: 'Business User',
      subscription_tier: 'business',
      coins: 1000
    }
  },
  
  // Admin account
  admin: {
    email: '<EMAIL>',
    password: 'AdminUser123!',
    user_metadata: {
      username: 'admin_user',
      full_name: 'Admin User',
      subscription_tier: 'business',
      coins: 9999,
      is_admin: true
    }
  },
  
  // <PERSON><PERSON>'s account (as requested)
  chitti: {
    email: 'chitti.gouth<PERSON><PERSON>@gmail.com',
    password: 'Chitti123!',
    user_metadata: {
      username: 'chitti',
      full_name: 'Goutham <PERSON> Chitti',
      subscription_tier: 'free',
      coins: 100
    }
  }
};

/**
 * Helper function to get a test account by tier
 * @param {string} tier - Subscription tier (free, premium, business, admin)
 * @returns {Object} - Test account object
 */
export const getTestAccount = (tier) => {
  return TEST_ACCOUNTS[tier] || TEST_ACCOUNTS.free;
};

/**
 * Helper function to authenticate with a test account
 * @param {string} tier - Subscription tier (free, premium, business, admin)
 * @returns {Promise<Object>} - Authentication result
 */
export const loginWithTestAccount = async (tier) => {
  const account = getTestAccount(tier);
  
  // Store mock authentication data in localStorage
  localStorage.setItem('supabase.auth.token', JSON.stringify({
    access_token: `mock_token_${tier}_${Date.now()}`,
    refresh_token: `mock_refresh_${tier}_${Date.now()}`,
    expires_at: Date.now() + 3600 * 1000 // 1 hour from now
  }));
  
  // Store mock user data in localStorage
  localStorage.setItem('supabase.auth.user', JSON.stringify({
    id: `user_${tier}_${Date.now()}`,
    email: account.email,
    user_metadata: account.user_metadata,
    created_at: new Date().toISOString()
  }));
  
  // Store mock subscription data in localStorage
  localStorage.setItem('user_subscription', JSON.stringify({
    tier: account.user_metadata.subscription_tier,
    coins: account.user_metadata.coins,
    start_date: new Date().toISOString(),
    end_date: account.user_metadata.subscription_tier === 'free' 
      ? null 
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
  }));
  
  return {
    user: {
      id: `user_${tier}_${Date.now()}`,
      email: account.email,
      user_metadata: account.user_metadata
    },
    session: {
      access_token: `mock_token_${tier}_${Date.now()}`
    }
  };
};
