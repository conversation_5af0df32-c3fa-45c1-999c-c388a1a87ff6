export const memoryManagementContent = {
  title: 'Memory Management',
  description: 'Learn how operating systems manage memory allocation, virtual memory, paging, and memory protection.',
  sections: [
    {
      title: 'Memory Hierarchy',
      content: `Memory in a computer system is organized in a hierarchical structure, with different levels offering various trade-offs between speed, cost, and capacity.

The memory hierarchy consists of:

1. Registers
   - Fastest memory
   - Located in CPU
   - Very small capacity
   - Used for immediate data processing

2. Cache Memory (L1, L2, L3)
   - Very fast access time
   - Small capacity
   - Bridges gap between registers and main memory
   - Managed automatically by hardware

3. Main Memory (RAM)
   - Moderate access time
   - Large capacity
   - Directly accessible by CPU
   - Volatile storage

4. Virtual Memory
   - Extension of RAM using disk space
   - Very large capacity
   - Slower access time
   - Managed by OS memory manager`,
      visualization: {
        type: 'interactive',
        component: 'MemoryHierarchyVisualization',
        data: {
          levels: [
            { name: 'Registers', speed: 'Fastest', size: 'Bytes', accessTime: '< 1ns' },
            { name: 'Cache', speed: 'Very Fast', size: 'KB-MB', accessTime: '2-10ns' },
            { name: 'Main Memory', speed: 'Fast', size: 'GB', accessTime: '50-100ns' },
            { name: 'Virtual Memory', speed: 'Slow', size: 'TB', accessTime: '5-10ms' }
          ]
        }
      }
    },
    {
      title: 'Virtual Memory',
      content: `Virtual memory is a memory management technique that provides an idealized abstraction of the storage resources that are actually available on a given machine.

Key concepts of virtual memory:

1. Address Translation
   - Virtual addresses mapped to physical addresses
   - Translation done by Memory Management Unit (MMU)
   - Page tables maintain mapping information

2. Benefits
   - Programs can use more memory than physically available
   - Memory isolation between processes
   - Shared memory implementation
   - More efficient memory utilization

3. Implementation
   - Demand paging
   - Page replacement algorithms
   - Memory protection
   - Shared pages`,
      visualization: {
        type: 'interactive',
        component: 'VirtualMemoryVisualization',
        data: {
          virtualAddress: '0x1234',
          pageTable: [
            { virtualPage: 0, physicalPage: 2, valid: true },
            { virtualPage: 1, physicalPage: 4, valid: true },
            { virtualPage: 2, physicalPage: 1, valid: false }
          ]
        }
      }
    },
    {
      title: 'Paging',
      content: `Paging is a memory management scheme that eliminates the need for contiguous allocation of physical memory.

Key aspects of paging:

1. Page Structure
   - Virtual memory divided into fixed-size pages
   - Physical memory divided into frames
   - Pages mapped to frames

2. Page Table
   - Maintains mapping between pages and frames
   - Contains page table entries (PTEs)
   - Includes status bits (valid, dirty, referenced)

3. Translation Lookaside Buffer (TLB)
   - Cache for page table entries
   - Speeds up virtual-to-physical address translation
   - Managed by MMU

4. Multi-level Paging
   - Reduces page table size
   - Hierarchical page tables
   - Common in modern systems`,
      visualization: {
        type: 'interactive',
        component: 'PagingVisualization',
        data: {
          pageSize: 4096,
          pages: [
            { id: 0, status: 'mapped', frame: 2 },
            { id: 1, status: 'swapped', frame: null },
            { id: 2, status: 'mapped', frame: 5 }
          ]
        }
      }
    },
    {
      title: 'Page Replacement Algorithms',
      content: `When memory is full and a new page needs to be loaded, the operating system must choose which page to remove from memory.

Common page replacement algorithms:

1. First-In-First-Out (FIFO)
   - Replaces oldest page in memory
   - Simple to implement
   - Not always effective

2. Least Recently Used (LRU)
   - Replaces page that hasn't been used for longest time
   - Very effective but expensive to implement
   - Requires tracking of page access times

3. Clock Algorithm
   - Approximation of LRU
   - Uses reference bit
   - Good balance of simplicity and effectiveness

4. Optimal Algorithm
   - Replaces page that won't be used for longest time
   - Theoretical benchmark
   - Impossible to implement in practice`,
      visualization: {
        type: 'interactive',
        component: 'PageReplacementVisualization',
        data: {
          algorithms: ['FIFO', 'LRU', 'Clock', 'Optimal'],
          pageReferences: [1, 2, 3, 4, 1, 2, 5, 1, 2, 3, 4, 5]
        }
      }
    }
  ],
  practicalLab: {
    title: "Memory Management Lab",
    description: "Practice memory management commands and monitor system memory usage",
    tasks: [
      {
        category: "Memory Information",
        commands: [
          {
            command: "free -h",
            description: "Display memory usage in human-readable format",
            expectedOutput: "Memory usage statistics including total, used, and available memory"
          },
          {
            command: "vmstat",
            description: "Virtual memory statistics",
            expectedOutput: "Detailed virtual memory statistics"
          },
          {
            command: "cat /proc/meminfo",
            description: "Detailed memory information",
            expectedOutput: "Comprehensive memory information from the proc filesystem"
          },
          {
            command: "swapon --show",
            description: "Show swap space usage",
            expectedOutput: "Information about swap devices and their usage"
          }
        ]
      },
      {
        category: "Process Memory",
        commands: [
          {
            command: "pmap PID",
            description: "Display process memory map",
            expectedOutput: "Memory map of a specific process"
          },
          {
            command: "top -o %MEM",
            description: "Show processes sorted by memory usage",
            expectedOutput: "Process list sorted by memory consumption"
          },
          {
            command: "ps aux --sort=-%mem",
            description: "List processes by memory usage",
            expectedOutput: "Detailed process information sorted by memory usage"
          },
          {
            command: "smem -tk",
            description: "Show processes' memory usage details",
            expectedOutput: "Detailed memory usage per process"
          }
        ]
      },
      {
        category: "Memory Limits",
        commands: [
          {
            command: "ulimit -a",
            description: "Show all system limits",
            expectedOutput: "Current system resource limits"
          },
          {
            command: "ulimit -v",
            description: "Show virtual memory limit",
            expectedOutput: "Maximum virtual memory size"
          },
          {
            command: "cat /proc/sys/vm/swappiness",
            description: "Check swappiness value",
            expectedOutput: "Current swappiness parameter value"
          },
          {
            command: "cat /proc/sys/vm/overcommit_memory",
            description: "Check memory overcommit settings",
            expectedOutput: "Current memory overcommit policy"
          }
        ]
      },
      {
        category: "Cache Management",
        commands: [
          {
            command: "sync",
            description: "Synchronize cached writes to disk",
            expectedOutput: "Flushes file system buffers"
          },
          {
            command: "echo 3 > /proc/sys/vm/drop_caches",
            description: "Clear system caches",
            expectedOutput: "Permission denied (needs root)"
          },
          {
            command: "sysctl vm.drop_caches",
            description: "Check cache drop settings",
            expectedOutput: "Current cache drop parameter value"
          },
          {
            command: "vmtouch -v file",
            description: "Show file in cache status",
            expectedOutput: "File's presence in page cache"
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What is virtual memory?",
      options: [
        "Physical memory installed in the computer",
        "A memory management technique that uses disk space as an extension of RAM",
        "A type of cache memory",
        "The total amount of RAM available"
      ],
      correct: 1,
      explanation: "Virtual memory is a memory management technique that uses disk space to extend the available RAM, allowing programs to use more memory than physically available."
    },
    {
      question: "What is a page fault?",
      options: [
        "A hardware memory error",
        "When a program crashes",
        "When a requested page is not in main memory",
        "When memory becomes corrupted"
      ],
      correct: 2,
      explanation: "A page fault occurs when a program tries to access a page that is mapped in virtual memory but not currently loaded in physical memory."
    },
    {
      question: "Which page replacement algorithm is considered optimal but impossible to implement in practice?",
      options: [
        "FIFO (First-In-First-Out)",
        "LRU (Least Recently Used)",
        "The Optimal Algorithm",
        "Clock Algorithm"
      ],
      correct: 2,
      explanation: "The Optimal Algorithm is theoretically perfect as it replaces the page that won't be used for the longest time in the future, but it requires knowledge of future page references, making it impossible to implement in practice."
    },
    {
      question: "What is the purpose of the Translation Lookaside Buffer (TLB)?",
      options: [
        "To store frequently used data",
        "To cache virtual to physical address translations",
        "To manage disk space",
        "To compress memory pages"
      ],
      correct: 1,
      explanation: "The TLB is a cache that stores recent virtual to physical address translations to speed up memory access by reducing the need to access the page table in main memory."
    },
    {
      question: "What happens during thrashing?",
      options: [
        "The CPU overheats",
        "The system spends more time paging than executing",
        "Memory becomes corrupted",
        "The hard drive fails"
      ],
      correct: 1,
      explanation: "Thrashing occurs when the system spends more time moving pages between disk and memory (paging) than executing actual program instructions, severely degrading performance."
    }
  ]
};