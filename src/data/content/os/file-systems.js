export const fileSystemsContent = {
  title: 'File Systems',
  description: 'Understand how operating systems manage files, directories, and storage.',
  sections: [
    {
      title: 'File System Structure',
      content: `The Linux file system follows a hierarchical tree structure, with each directory having a specific purpose:

┌── / (Root Directory)
├── /bin     - Essential user binaries
├── /boot    - Boot loader files
├── /dev     - Device files
├── /etc     - System configuration
├── /home    - User home directories
├── /lib     - System libraries
├── /media   - Removable media
├── /mnt     - Mount point
├── /opt     - Optional software
├── /proc    - Process information
├── /root    - Root user home
├── /sbin    - System binaries
├── /tmp     - Temporary files
├── /usr     - User programs
└── /var     - Variable files

Each directory serves a specific purpose:

• /bin: Contains essential command binaries
• /boot: Contains bootloader files and kernels
• /dev: Contains device files for hardware
• /etc: Contains system-wide configuration
• /home: Contains user home directories
• /lib: Contains shared system libraries
• /media: Mount point for removable media
• /mnt: Mount point for temporary filesystems
• /opt: Contains optional/add-on software
• /proc: Virtual filesystem for process info
• /root: Home directory for root user
• /sbin: Contains system administration binaries
• /tmp: Contains temporary files
• /usr: Contains user programs and data
• /var: Contains variable data files`,
      visualization: {
        type: 'interactive',
        component: 'FileSystemVisualization',
        data: {
          structure: 'tree',
          nodes: [
            { name: '/', type: 'dir', description: 'Root Directory' },
            { name: '/bin', type: 'dir', description: 'Essential user binaries' },
            { name: '/boot', type: 'dir', description: 'Boot loader files' },
            { name: '/dev', type: 'dir', description: 'Device files' },
            { name: '/etc', type: 'dir', description: 'System configuration' },
            { name: '/home', type: 'dir', description: 'User home directories' },
            { name: '/lib', type: 'dir', description: 'System libraries' },
            { name: '/media', type: 'dir', description: 'Removable media' },
            { name: '/mnt', type: 'dir', description: 'Mount point' },
            { name: '/opt', type: 'dir', description: 'Optional software' },
            { name: '/proc', type: 'dir', description: 'Process information' },
            { name: '/root', type: 'dir', description: 'Root user home' },
            { name: '/sbin', type: 'dir', description: 'System binaries' },
            { name: '/tmp', type: 'dir', description: 'Temporary files' },
            { name: '/usr', type: 'dir', description: 'User programs' },
            { name: '/var', type: 'dir', description: 'Variable files' }
          ]
        }
      }
    },
    {
      title: 'File Types',
      content: `Linux supports several types of files:

1. Regular Files (-)
   • Text files
   • Binary files
   • Data files
   • Shell scripts

2. Directories (d)
   • Containers for other files
   • Can contain subdirectories
   • Special entries: . (current) and .. (parent)

3. Links
   • Symbolic links (l): Point to another file
   • Hard links: Direct reference to file data

4. Special Files
   • Block devices (b): Storage devices
   • Character devices (c): Serial devices
   • Named pipes (p): Inter-process communication
   • Sockets (s): Network communication

File type is indicated by the first character in ls -l output:
-rw-r--r--  Regular file
drwxr-xr-x  Directory
lrwxrwxrwx  Symbolic link
brw-rw----  Block device
crw-rw----  Character device
srw-rw-rw-  Socket
prw-r--r--  Named pipe`,
      visualization: {
        type: 'interactive',
        component: 'FileTypesVisualization',
        data: {
          types: [
            { symbol: '-', name: 'Regular File', example: 'document.txt' },
            { symbol: 'd', name: 'Directory', example: '/home/<USER>' },
            { symbol: 'l', name: 'Symbolic Link', example: 'link -> target' },
            { symbol: 'b', name: 'Block Device', example: '/dev/sda' },
            { symbol: 'c', name: 'Character Device', example: '/dev/tty' },
            { symbol: 's', name: 'Socket', example: '/tmp/socket' },
            { symbol: 'p', name: 'Named Pipe', example: '/tmp/pipe' }
          ]
        }
      }
    }
  ],
  practicalLab: {
    title: "File System Operations Lab",
    description: "Practice file system operations and management",
    tasks: [
      {
        category: "Basic File Operations",
        commands: [
          {
            command: "ls -l",
            description: "List files with details",
            expectedOutput: "Detailed file listing with permissions"
          },
          {
            command: "touch file.txt",
            description: "Create empty file",
            expectedOutput: "File created"
          },
          {
            command: "mkdir dir",
            description: "Create directory",
            expectedOutput: "Directory created"
          },
          {
            command: "rm file.txt",
            description: "Remove file",
            expectedOutput: "File removed"
          }
        ]
      },
      {
        category: "File Permissions",
        commands: [
          {
            command: "chmod 755 file",
            description: "Change file permissions",
            expectedOutput: "Permissions updated"
          },
          {
            command: "chown user:group file",
            description: "Change file ownership",
            expectedOutput: "Ownership changed"
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What is the purpose of the /etc directory?",
      options: [
        "Store temporary files",
        "Store system configuration files",
        "Store user home directories",
        "Store program files"
      ],
      correct: 1,
      explanation: "The /etc directory contains system-wide configuration files and directories that are used by the operating system and installed applications."
    },
    {
      question: "Which directory contains essential command binaries?",
      options: [
        "/sbin",
        "/bin",
        "/usr",
        "/opt"
      ],
      correct: 1,
      explanation: "The /bin directory contains essential command binaries that need to be available in single user mode, including commands like ls, cp, and cat."
    }
  ]
};