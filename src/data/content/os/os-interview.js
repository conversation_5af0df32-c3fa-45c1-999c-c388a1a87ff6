export const osInterviewContent = {
  title: 'OS Interview Questions & Answers',
  description: 'Comprehensive collection of operating system interview questions with detailed answers and examples.',
  sections: [
    {
      title: 'Basic OS Concepts',
      content: `
Operating System Fundamentals:

1. What is an Operating System?
   - An operating system is system software that manages hardware and software resources
   - Provides common services for computer programs
   - Acts as an intermediary between hardware and user applications
   - Key responsibilities: Process management, Memory management, File system management, I/O management

2. What are the main functions of an Operating System?
   - Process Management: Creation, scheduling, termination
   - Memory Management: Allocation, protection, virtual memory
   - File Management: File creation, deletion, organization
   - Device Management: Device driver management, I/O operations
   - Security: Access control, user authentication
   - User Interface: Command line or GUI

3. What are the different types of Operating Systems?
   - Batch OS: Processes jobs in batches
   - Multi-programming OS: Multiple programs in memory
   - Multi-tasking OS: Multiple tasks executed concurrently
   - Multi-processing OS: Multiple processors working together
   - Real-time OS: Guaranteed response times
   - Distributed OS: Multiple computers working together

4. Explain the difference between Kernel and Shell
   - Kernel: Core component that manages hardware resources
   - Shell: Interface between user and kernel
   - Kernel handles: Memory management, Process scheduling, System calls
   - Shell handles: Command interpretation, Script execution, User interaction`
    },
    {
      title: 'Process & Thread Management',
      content: `
Process and Thread Concepts:

1. What is a Process?
   - Instance of a program in execution
   - Contains: Program counter, Stack, Data section
   - Has its own address space
   - Resources allocated by OS

2. What is a Thread?
   - Lightweight unit of execution within a process
   - Shares resources with other threads in same process
   - Has own: Program counter, Stack, Registers
   - Faster context switching than processes

3. Difference between Process and Thread
   - Processes are independent, threads share resources
   - Processes have separate memory space, threads share memory
   - Process creation is more expensive than thread creation
   - Inter-process communication is more complex than inter-thread

4. What is Context Switching?
   - Saving state of current process/thread
   - Loading state of new process/thread
   - Overhead includes:
     * Saving/restoring registers
     * Switching memory maps
     * Updating various tables
     * Cache invalidation`
    },
    {
      title: 'Memory Management',
      content: `
Memory Management Concepts:

1. What is Virtual Memory?
   - Technique that provides illusion of more memory than physically available
   - Uses disk space as extension of RAM
   - Benefits:
     * Larger address space
     * Memory isolation
     * More efficient memory utilization

2. What is Paging?
   - Memory management scheme
   - Divides physical memory into fixed-size frames
   - Divides logical memory into pages
   - Advantages:
     * No external fragmentation
     * Flexible memory allocation

3. What is Segmentation?
   - Memory management scheme
   - Divides memory into variable-sized segments
   - Each segment represents logical unit
   - Advantages:
     * Better for program structure
     * Easier sharing and protection

4. Explain Page Replacement Algorithms
   - FIFO (First In First Out)
   - LRU (Least Recently Used)
   - Optimal Page Replacement
   - Clock Algorithm
   Each has different trade-offs between complexity and efficiency`
    },
    {
      title: 'Scheduling Algorithms',
      content: `
CPU Scheduling Concepts:

1. What is CPU Scheduling?
   - Process of determining which process runs on CPU
   - Aims to maximize CPU utilization
   - Types:
     * Preemptive
     * Non-preemptive

2. Common Scheduling Algorithms:
   - First-Come, First-Served (FCFS)
     * Simple but can lead to convoy effect
   - Shortest Job First (SJF)
     * Optimal for minimizing average waiting time
   - Round Robin (RR)
     * Fair allocation with time quantum
   - Priority Scheduling
     * Based on process priority

3. Scheduling Criteria:
   - CPU Utilization
   - Throughput
   - Turnaround Time
   - Waiting Time
   - Response Time

4. Real-time Scheduling:
   - Hard real-time systems
   - Soft real-time systems
   - Deadline scheduling
   - Rate monotonic scheduling`
    },
    {
      title: 'Deadlock Management',
      content: `
Deadlock Concepts:

1. What is Deadlock?
   - Situation where processes wait indefinitely
   - Each process holds resources needed by others
   - Four necessary conditions:
     * Mutual Exclusion
     * Hold and Wait
     * No Preemption
     * Circular Wait

2. Deadlock Prevention:
   - Prevent one of four necessary conditions
   - Methods:
     * Resource ordering
     * Request all resources initially
     * Allow preemption
     * Break circular wait

3. Deadlock Avoidance:
   - Banker's Algorithm
   - Safe state checking
   - Resource allocation graph
   - Dynamic avoidance

4. Deadlock Detection and Recovery:
   - Detection algorithms
   - Recovery methods:
     * Process termination
     * Resource preemption
   - Recovery strategies`
    }
  ],
  practicalLab: {
    title: "OS Interview Practice Lab",
    description: "Practice solving common operating system interview problems",
    tasks: [
      {
        category: "Process Synchronization",
        commands: [
          {
            command: "solve producer_consumer",
            description: "Implement producer-consumer solution",
            expectedOutput: "Producer-consumer problem solution template"
          },
          {
            command: "solve dining_philosophers",
            description: "Implement dining philosophers solution",
            expectedOutput: "Dining philosophers problem solution template"
          }
        ]
      },
      {
        category: "Memory Management",
        commands: [
          {
            command: "implement page_replacement",
            description: "Implement LRU page replacement",
            expectedOutput: "LRU implementation template"
          },
          {
            command: "analyze memory_usage",
            description: "Analyze memory usage patterns",
            expectedOutput: "Memory analysis tools and techniques"
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What are the four necessary conditions for deadlock?",
      options: [
        "Mutual exclusion, hold and wait, no preemption, circular wait",
        "Priority, scheduling, resources, waiting",
        "Starvation, aging, priority inversion, blocking",
        "Synchronization, mutual exclusion, semaphores, monitors"
      ],
      correct: 0,
      explanation: "The four necessary conditions for deadlock are mutual exclusion (resources cannot be shared), hold and wait (processes hold resources while waiting for others), no preemption (resources cannot be forcibly taken), and circular wait (circular chain of processes waiting for resources)."
    },
    {
      question: "What is the difference between preemptive and non-preemptive scheduling?",
      options: [
        "Preemptive allows interrupting running processes, non-preemptive doesn't",
        "Preemptive is faster, non-preemptive is slower",
        "Preemptive uses more memory, non-preemptive uses less",
        "Preemptive is for real-time systems only, non-preemptive for batch systems"
      ],
      correct: 0,
      explanation: "In preemptive scheduling, the operating system can interrupt and suspend a running process to give CPU time to another process. In non-preemptive scheduling, once a process starts running, it continues until it completes or voluntarily yields the CPU."
    }
  ]
};