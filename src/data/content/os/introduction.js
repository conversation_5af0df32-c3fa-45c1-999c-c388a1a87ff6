export const osIntroductionContent = {
  title: 'Introduction to Operating Systems',
  description: 'Learn the fundamental concepts of operating systems through interactive simulations and hands-on exercises.',
  sections: [
    {
      title: 'What is an Operating System?',
      content: `An operating system (OS) is the most important software that runs on a computer. It manages the computer's memory, processes, and all of its software and hardware.

Key responsibilities include:
1. Process Management
   • Controls and allocates processes
   • Schedules tasks
   • Handles process synchronization

2. Memory Management
   • Allocates memory to programs
   • Manages virtual memory
   • Handles memory protection

3. File System Management
   • Organizes and maintains files
   • Controls access permissions
   • Manages storage devices

4. I/O Management
   • Handles input/output operations
   • Manages device drivers
   • Controls peripheral devices

5. Security and Protection
   • User authentication
   • Access control
   • Resource protection`,
      visualization: {
        type: 'interactive',
        component: 'OSLayersVisualization',
        data: {
          layers: [
            { name: 'Applications', icon: 'FaDesktop', color: '#88cc14' },
            { name: 'User Interface', icon: 'FaCode', color: '#00f3ff' },
            { name: '<PERSON><PERSON>', icon: 'FaMicrochip', color: '#ff4757' }
          ]
        }
      }
    },
    {
      title: 'Types of Operating Systems',
      content: `Operating systems come in various types, each designed for specific use cases:

1. Single-User, Single-Task
   • Example: MS-DOS
   • One user can run one program at a time
   • Simple and straightforward design
   • Limited multitasking capabilities

2. Single-User, Multi-Task
   • Example: Windows, macOS
   • One user can run multiple programs
   • Supports background processes
   • Full multitasking support

3. Multi-User
   • Example: Unix, Linux
   • Multiple users access simultaneously
   • Advanced security features
   • Resource sharing capabilities

4. Real-Time OS
   • Example: QNX, VxWorks
   • Used in critical systems
   • Guaranteed response times
   • Deterministic behavior

5. Distributed OS
   • Example: Amoeba
   • Manages networked computers
   • Appears as single system
   • Transparent resource sharing`,
      visualization: {
        type: 'interactive',
        component: 'OSTypesVisualization',
        data: {
          types: [
            { name: 'Single-User', icon: 'FaUser' },
            { name: 'Multi-User', icon: 'FaUsers' },
            { name: 'Real-Time', icon: 'FaClock' },
            { name: 'Distributed', icon: 'FaNetwork' }
          ]
        }
      }
    }
  ],
  practicalLab: {
    title: "OS Command Line Interface Lab",
    description: "Master essential Linux commands through hands-on practice in a simulated environment.",
    tasks: [
      {
        category: "Basic Navigation",
        commands: [
          {
            command: "pwd",
            description: "Print current working directory",
            hint: "Use pwd to show your current location in the filesystem",
            expectedOutput: "/home/<USER>"
          },
          {
            command: "ls -la",
            description: "List all files including hidden ones",
            hint: "The -l flag shows detailed information, -a shows hidden files",
            expectedOutput: "total 32\ndrwxr-xr-x 4 <USER> <GROUP> 4096 Mar 19 10:00 .\ndrwxr-xr-x 3 <USER> <GROUP> 4096 Mar 19 10:00 ..\n-rw------- 1 <USER> <GROUP>  220 Mar 19 10:00 .bash_history"
          },
          {
            command: "cd /etc",
            description: "Change to system configuration directory",
            hint: "Use cd to change directories",
            expectedOutput: "Changed to /etc directory\nThis directory contains system configuration files."
          }
        ]
      },
      {
        category: "File Operations",
        commands: [
          {
            command: "touch test.txt",
            description: "Create a new empty file",
            hint: "touch creates a new empty file or updates timestamps of existing files",
            expectedOutput: "Created file: test.txt"
          },
          {
            command: "mkdir projects",
            description: "Create a new directory",
            hint: "mkdir is used to create new directories",
            expectedOutput: "Created directory: projects"
          },
          {
            command: "cp test.txt projects/",
            description: "Copy file to projects directory",
            hint: "cp copies files or directories",
            expectedOutput: "Copied test.txt to projects/"
          }
        ]
      },
      {
        category: "System Information",
        commands: [
          {
            command: "uname -a",
            description: "Display system information",
            hint: "uname shows system information, -a shows all info",
            expectedOutput: "Linux xcerberus 5.15.0-generic #1 SMP PREEMPT_DYNAMIC Thu Mar 14 10:00:00 UTC 2024 x86_64 GNU/Linux"
          },
          {
            command: "free -h",
            description: "Show memory usage",
            hint: "free shows memory usage, -h shows human-readable sizes",
            expectedOutput: "              total        used        free      shared  buff/cache   available\nMem:           15Gi       2.1Gi       8.2Gi       1.2Gi       4.7Gi        11Gi\nSwap:         8.0Gi          0B       8.0Gi"
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What is the primary function of an operating system?",
      options: [
        "To provide a user interface",
        "To manage hardware and software resources",
        "To run applications",
        "To connect to the internet"
      ],
      correct: 1,
      explanation: "The primary function of an OS is to manage hardware and software resources, ensuring efficient operation of the computer system."
    },
    {
      question: "Which type of operating system is best suited for servers?",
      options: [
        "Single-User, Single-Task",
        "Single-User, Multi-Task",
        "Multi-User",
        "Real-Time OS"
      ],
      correct: 2,
      explanation: "Multi-User operating systems are ideal for servers as they allow multiple users to access resources simultaneously and provide better security features."
    },
    {
      question: "What is a process in an operating system?",
      options: [
        "A program stored on the disk",
        "A program in execution",
        "A file containing code",
        "A system configuration"
      ],
      correct: 1,
      explanation: "A process is a program in execution, which includes the program code, its current activity, and resource allocations."
    },
    {
      question: "Which component of an OS handles memory allocation?",
      options: [
        "Process Manager",
        "Memory Manager",
        "File System",
        "I/O Manager"
      ],
      correct: 1,
      explanation: "The Memory Manager is responsible for allocating and deallocating memory space to processes and managing virtual memory."
    },
    {
      question: "What is the purpose of a system call?",
      options: [
        "To run user applications",
        "To interface between user programs and OS services",
        "To manage hardware directly",
        "To create new processes"
      ],
      correct: 1,
      explanation: "System calls provide an interface between user programs and OS services, allowing programs to request services from the operating system safely."
    }
  ]
};