export const processManagementContent = {
  title: 'Process Management',
  description: 'Learn how operating systems manage and control processes, including scheduling and synchronization.',
  sections: [
    {
      title: 'Process Concepts',
      content: `A process is an instance of a program in execution. It is the basic unit of work in an operating system. Each process has its own memory space, system resources, and security context.

Key components of a process include:
- Program Counter: Indicates the address of the next instruction
- Process State: Current state of the process (new, ready, running, etc.)
- Memory Boundaries: Memory allocated to the process
- List of Open Files: Files currently being accessed
- List of Related Processes: Parent and child processes`,
      visualization: {
        type: 'interactive',
        component: 'ProcessVisualization',
        data: {
          states: [
            { name: 'New', color: '#88cc14' },
            { name: 'Ready', color: '#00f3ff' },
            { name: 'Running', color: '#ff4757' },
            { name: 'Waiting', color: '#ffd32a' },
            { name: 'Terminated', color: '#747d8c' }
          ]
        }
      }
    },
    {
      title: 'Process States',
      content: `A process goes through various states during its lifecycle:

1. New: Process is being created
2. Ready: Process is waiting to be assigned to a processor
3. Running: Instructions are being executed
4. Waiting: Process is waiting for some event to occur
5. Terminated: Process has finished execution

State transitions occur based on:
- Scheduler decisions
- I/O operations
- Resource availability
- Process completion`
    },
    {
      title: 'Process Control Block (PCB)',
      content: `The Process Control Block (PCB) is a data structure that contains all the information about a process. It's created when a process is created and is maintained throughout its lifetime.

PCB contains:
- Process ID (PID)
- Program Counter
- CPU Registers
- CPU Scheduling Information
- Memory Management Information
- Accounting Information
- I/O Status Information

The PCB is essential for:
- Context Switching
- Process Management
- Resource Tracking
- Process Synchronization`
    },
    {
      title: 'Context Switching',
      content: `Context switching is the process of saving the state of a running process and restoring the state of a different process when switching between them.

Steps involved:
1. Save the context of current process in its PCB
2. Update relevant process control block pointers
3. Move PCB to appropriate queue (ready/blocked)
4. Select another process for execution
5. Update memory management structures
6. Restore context of the new process

Context switching overhead includes:
- Saving/restoring register values
- Switching memory maps
- Updating various tables and lists
- Flushing various caches`
    },
    {
      title: 'Process Scheduling',
      content: `Process scheduling is the activity of selecting which process runs on the CPU at any given time. The main objectives are:

- CPU Utilization: Keep the CPU as busy as possible
- Throughput: Maximize number of processes completed per time unit
- Turnaround Time: Minimize time between submission and completion
- Waiting Time: Minimize time in ready queue
- Response Time: Minimize time until first response

Common Scheduling Algorithms:
1. First-Come, First-Served (FCFS)
2. Shortest Job First (SJF)
3. Priority Scheduling
4. Round Robin
5. Multilevel Queue
6. Multilevel Feedback Queue`
    },
    {
      title: 'CPU Scheduling Algorithms',
      content: `Detailed look at major scheduling algorithms:

1. First-Come, First-Served (FCFS):
   - Simplest scheduling algorithm
   - Non-preemptive
   - Can lead to "convoy effect"

2. Shortest Job First (SJF):
   - Optimal for minimizing average waiting time
   - Can be preemptive or non-preemptive
   - Requires prediction of burst time

3. Priority Scheduling:
   - Assigns priority to each process
   - Can lead to starvation
   - Often used with aging mechanism

4. Round Robin:
   - Time quantum based
   - Fair allocation of CPU
   - Performance depends on quantum size

5. Multilevel Queue:
   - Processes assigned to different queues
   - Each queue has its own scheduling algorithm
   - No movement between queues

6. Multilevel Feedback Queue:
   - Allows processes to move between queues
   - Adapts to process behavior
   - Most sophisticated and flexible`
    }
  ],
  practicalLab: {
    title: "Process Management Lab",
    description: "Practice process management commands and understand process states",
    tasks: [
      {
        category: "Basic Process Commands",
        commands: [
          {
            command: "ps",
            description: "Display current processes",
            expectedOutput: "List of running processes with basic information"
          },
          {
            command: "ps aux",
            description: "Show detailed process information for all users",
            expectedOutput: "Detailed list of all processes including CPU and memory usage"
          },
          {
            command: "pstree",
            description: "Display process tree",
            expectedOutput: "Tree view showing process hierarchy"
          },
          {
            command: "top",
            description: "Show real-time process information",
            expectedOutput: "Interactive process viewer showing system activity"
          }
        ]
      },
      {
        category: "Process Control",
        commands: [
          {
            command: "kill -l",
            description: "List all available signals",
            expectedOutput: "List of signal numbers and names"
          },
          {
            command: "kill PID",
            description: "Send SIGTERM to process",
            expectedOutput: "Process terminated gracefully"
          },
          {
            command: "kill -9 PID",
            description: "Force kill process (SIGKILL)",
            expectedOutput: "Process terminated immediately"
          },
          {
            command: "killall process_name",
            description: "Kill all processes by name",
            expectedOutput: "All matching processes terminated"
          }
        ]
      },
      {
        category: "Process Monitoring",
        commands: [
          {
            command: "nice -n 10 command",
            description: "Start process with custom priority",
            expectedOutput: "Process started with adjusted nice value"
          },
          {
            command: "renice +5 PID",
            description: "Change process priority",
            expectedOutput: "Process priority adjusted"
          },
          {
            command: "pidof process_name",
            description: "Find process ID by name",
            expectedOutput: "PID of the specified process"
          },
          {
            command: "pgrep process_name",
            description: "Search processes by name",
            expectedOutput: "List of matching process IDs"
          }
        ]
      },
      {
        category: "Process Analysis",
        commands: [
          {
            command: "strace command",
            description: "Trace system calls and signals",
            expectedOutput: "System calls made by the process"
          },
          {
            command: "lsof -p PID",
            description: "List open files for process",
            expectedOutput: "Files opened by the specified process"
          },
          {
            command: "time command",
            description: "Measure process execution time",
            expectedOutput: "Real, user, and system time statistics"
          },
          {
            command: "watch command",
            description: "Execute command periodically",
            expectedOutput: "Command output updated every 2 seconds"
          }
        ]
      },
      {
        category: "Background Processes",
        commands: [
          {
            command: "command &",
            description: "Start process in background",
            expectedOutput: "Process started with job number"
          },
          {
            command: "jobs",
            description: "List background jobs",
            expectedOutput: "List of background processes"
          },
          {
            command: "fg %job_number",
            description: "Bring process to foreground",
            expectedOutput: "Process brought to foreground"
          },
          {
            command: "bg %job_number",
            description: "Resume process in background",
            expectedOutput: "Process resumed in background"
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What is a process control block (PCB)?",
      options: [
        "A block of memory allocated to a process",
        "A data structure containing process information",
        "A system call to control processes",
        "A type of process scheduling algorithm"
      ],
      correct: 1,
      explanation: "A Process Control Block (PCB) is a data structure that contains important information about a specific process, including its state, program counter, CPU registers, and memory management information."
    },
    {
      question: "Which state is a process in when it's currently executing?",
      options: [
        "Ready",
        "Running",
        "Waiting",
        "New"
      ],
      correct: 1,
      explanation: "A process is in the Running state when it is currently being executed by the CPU."
    },
    {
      question: "What happens during context switching?",
      options: [
        "A new process is created",
        "A process is terminated",
        "The CPU switches from one process to another",
        "Memory is allocated to a process"
      ],
      correct: 2,
      explanation: "Context switching occurs when the CPU switches from executing one process to another, saving the current process's state and loading the new process's state."
    },
    {
      question: "Which scheduling algorithm is optimal for minimizing average waiting time?",
      options: [
        "First-Come, First-Served",
        "Shortest Job First",
        "Round Robin",
        "Priority Scheduling"
      ],
      correct: 1,
      explanation: "Shortest Job First (SJF) is theoretically optimal for minimizing average waiting time, though it requires knowing or predicting process burst times."
    },
    {
      question: "What is the main disadvantage of priority scheduling?",
      options: [
        "High overhead",
        "Process starvation",
        "Low throughput",
        "Complex implementation"
      ],
      correct: 1,
      explanation: "Priority scheduling can lead to process starvation, where lower-priority processes may never execute if there are always higher-priority processes ready to run."
    }
  ]
};