export const cpuSchedulingContent = {
  title: 'CPU Scheduling',
  description: 'Learn about CPU scheduling algorithms, process states, and scheduling criteria.',
  sections: [
    {
      title: 'CPU Scheduling Basics',
      content: `CPU scheduling is the basis of multiprogrammed operating systems. By switching the CPU among processes, the operating system can make the computer more productive.

Key concepts include:

1. CPU-I/O Burst Cycle
   - Programs alternate between CPU and I/O bursts
   - CPU burst distribution is key to scheduling
   - Different patterns require different approaches

2. CPU Scheduler
   - Selects from processes in ready queue
   - Allocates CPU to selected process
   - Scheduling decisions happen when:
     * Process switches from running to waiting
     * Process terminates
     * New process arrives
     * Process switches from waiting to ready`,
      visualization: {
        type: 'interactive',
        component: 'ProcessStateVisualization',
        data: {
          states: [
            { name: 'Running', color: '#88cc14' },
            { name: 'Ready', color: '#00f3ff' },
            { name: 'Waiting', color: '#ff4757' }
          ]
        }
      }
    },
    {
      title: 'Scheduling Criteria',
      content: `CPU scheduling algorithms are evaluated based on different criteria:

1. CPU Utilization
   - Keep CPU as busy as possible
   - Typically 40-90% for time-sharing systems

2. Throughput
   - Number of processes completed per time unit
   - Maximize process completion rate

3. Turnaround Time
   - Time from submission to completion
   - Includes execution and waiting time

4. Waiting Time
   - Time spent in ready queue
   - Minimize average waiting time

5. Response Time
   - Time from submission to first response
   - Critical for interactive systems`,
      visualization: {
        type: 'interactive',
        component: 'SchedulingMetricsVisualization',
        data: {
          metrics: [
            { name: 'CPU Utilization', target: '90%' },
            { name: 'Throughput', target: 'Maximum' },
            { name: 'Turnaround Time', target: 'Minimum' },
            { name: 'Waiting Time', target: 'Minimum' },
            { name: 'Response Time', target: 'Minimum' }
          ]
        }
      }
    },
    {
      title: 'Scheduling Algorithms',
      content: `Common CPU scheduling algorithms include:

1. First-Come, First-Served (FCFS)
   - Simplest scheduling algorithm
   - Non-preemptive
   - Can lead to "convoy effect"

2. Shortest-Job-First (SJF)
   - Optimal for minimizing average waiting time
   - Can be preemptive or non-preemptive
   - Difficult to predict next CPU burst

3. Priority Scheduling
   - Each process assigned a priority
   - Higher priority processes run first
   - Can lead to starvation

4. Round Robin (RR)
   - Each process gets small unit of CPU time
   - Time quantum typically 10-100 milliseconds
   - Fair allocation but higher overhead

5. Multilevel Queue
   - Processes assigned to different queues
   - Each queue has its own scheduling algorithm
   - No movement between queues

6. Multilevel Feedback Queue
   - Similar to multilevel queue
   - Processes can move between queues
   - Most flexible but complex`
    }
  ],
  practicalLab: {
    title: "CPU Scheduling Lab",
    description: "Practice implementing and analyzing different scheduling algorithms",
    tasks: [
      {
        category: "FCFS Implementation",
        commands: [
          {
            command: "python3 fcfs.py",
            description: "Run FCFS scheduling simulation",
            expectedOutput: `First Come First Served Scheduling
Process  Burst Time  Waiting Time  Turnaround Time
P1         24          0            24
P2         3           24           27
P3         3           27           30
Average Waiting Time: 17.0`
          }
        ]
      },
      {
        category: "SJF Implementation",
        commands: [
          {
            command: "python3 sjf.py",
            description: "Run SJF scheduling simulation",
            expectedOutput: `Shortest Job First Scheduling
Process  Burst Time  Waiting Time  Turnaround Time
P2         3           0            3
P3         3           3            6
P1         24          6            30
Average Waiting Time: 3.0`
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What is the convoy effect in CPU scheduling?",
      options: [
        "When short processes wait for a long process to finish",
        "When processes move together in a queue",
        "When CPU utilization is maximized",
        "When processes are scheduled randomly"
      ],
      correct: 0,
      explanation: "The convoy effect occurs in FCFS scheduling when short processes must wait for a long process to finish, leading to poor average waiting time."
    },
    {
      question: "Which scheduling algorithm is optimal for minimizing average waiting time?",
      options: [
        "FCFS",
        "SJF",
        "Round Robin",
        "Priority Scheduling"
      ],
      correct: 1,
      explanation: "Shortest Job First (SJF) is theoretically optimal for minimizing average waiting time, though it requires knowing or predicting process burst times."
    },
    {
      question: "What is the main advantage of Round Robin scheduling?",
      options: [
        "Best average waiting time",
        "Highest throughput",
        "Fair CPU allocation",
        "Lowest overhead"
      ],
      correct: 2,
      explanation: "Round Robin ensures fair allocation of CPU time by giving each process a small time quantum, making it ideal for time-sharing systems."
    }
  ]
};