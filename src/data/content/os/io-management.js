export const ioManagementContent = {
  title: 'I/O Management',
  description: 'Learn how operating systems handle input/output operations and device management.',
  sections: [
    {
      title: 'I/O Hardware',
      content: `I/O devices vary widely in their function and speed. Common types include:

1. Block Devices
   - Fixed size blocks
   - Random access
   - Example: disk drives

2. Character Devices
   - Stream of characters
   - Sequential access
   - Example: keyboards, mice

3. Network Devices
   - Stream of data packets
   - Example: network interfaces

Key components:
- Port: connection point for device
- Bus: data pathway
- Controller: electronics that operate port, bus, device`,
      visualization: {
        type: 'interactive',
        component: 'IOHardwareVisualization',
        data: {
          devices: [
            { type: 'block', name: 'Hard Drive', speed: 'Medium' },
            { type: 'character', name: 'Keyboard', speed: 'Slow' },
            { type: 'network', name: 'Network Card', speed: 'Fast' }
          ]
        }
      }
    },
    {
      title: 'I/O Software',
      content: `I/O software is organized in layers:

1. User Level Software
   - Libraries
   - System calls
   - Spooling systems

2. Device Drivers
   - Device-specific code
   - Standardized interface
   - Hardware control

3. Interrupt Handlers
   - Save registers
   - Handle interrupt
   - Restore state

4. Device Controllers
   - Hardware that controls device
   - Buffers data
   - Simple instruction set`,
      visualization: {
        type: 'interactive',
        component: 'IOSoftwareVisualization',
        data: {
          layers: [
            { name: 'User Level', color: '#88cc14' },
            { name: 'Device Drivers', color: '#00f3ff' },
            { name: 'Interrupt Handlers', color: '#ff4757' },
            { name: 'Controllers', color: '#ffd32a' }
          ]
        }
      }
    }
  ],
  practicalLab: {
    title: "I/O Operations Lab",
    description: "Practice I/O operations and device management",
    tasks: [
      {
        category: "Device Information",
        commands: [
          {
            command: "lsblk",
            description: "List block devices",
            expectedOutput: "Block device hierarchy"
          },
          {
            command: "lspci",
            description: "List PCI devices",
            expectedOutput: "PCI device information"
          },
          {
            command: "lsusb",
            description: "List USB devices",
            expectedOutput: "USB device information"
          }
        ]
      },
      {
        category: "I/O Monitoring",
        commands: [
          {
            command: "iostat",
            description: "Show I/O statistics",
            expectedOutput: "I/O device statistics"
          },
          {
            command: "iotop",
            description: "Monitor I/O usage",
            expectedOutput: "Process I/O usage"
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What is the purpose of a device driver?",
      options: [
        "To physically drive the device",
        "To provide a standardized interface to the device",
        "To store device data",
        "To power the device"
      ],
      correct: 1,
      explanation: "A device driver provides a standardized interface between the operating system and a specific hardware device."
    },
    {
      question: "What is the difference between block and character devices?",
      options: [
        "Block devices are faster",
        "Character devices use more memory",
        "Block devices transfer fixed-size blocks, character devices transfer byte streams",
        "Character devices are more reliable"
      ],
      correct: 2,
      explanation: "Block devices transfer data in fixed-size blocks and allow random access, while character devices transfer data as streams of bytes sequentially."
    }
  ]
};