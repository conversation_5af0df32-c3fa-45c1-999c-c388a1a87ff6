export const deviceManagementContent = {
  title: 'Device Management',
  description: 'Understanding device management, device drivers, and I/O systems in operating systems.',
  sections: [
    {
      title: 'Device Management Basics',
      content: `Device management is a crucial part of operating systems, handling communication between the system and various hardware devices.

Key components include:

1. Device Controllers
   - Hardware that controls device operation
   - Contains local buffer storage
   - Special-purpose registers
   - Manages device-specific details

2. Device Drivers
   - Software that communicates with controllers
   - Provides uniform interface to OS
   - Device-specific code
   - Handles interrupts and error handling

3. I/O Subsystem
   - Memory-mapped I/O vs Port-mapped I/O
   - Polling vs Interrupts
   - Direct Memory Access (DMA)
   - Device independence`,
      visualization: {
        type: 'interactive',
        component: 'DeviceManagementVisualization',
        data: {
          components: [
            { name: 'Device Controller', type: 'hardware' },
            { name: 'Device Driver', type: 'software' },
            { name: 'I/O Subsystem', type: 'system' }
          ]
        }
      }
    },
    {
      title: 'I/O Hardware',
      content: `I/O Hardware consists of various components and techniques:

1. Port-Mapped I/O
   - Special I/O instructions
   - Separate I/O and memory space
   - IN and OUT instructions

2. Memory-Mapped I/O
   - Devices mapped to memory addresses
   - Regular memory instructions
   - No special I/O instructions

3. Direct Memory Access (DMA)
   - Bypasses CPU for data transfer
   - Reduces system overhead
   - Handles bulk data transfer

4. Interrupt Handling
   - Device signals completion
   - CPU handles interrupt
   - Context switching overhead`,
      visualization: {
        type: 'interactive',
        component: 'IOHardwareVisualization',
        data: {
          techniques: [
            { name: 'Port-Mapped I/O', type: 'port' },
            { name: 'Memory-Mapped I/O', type: 'memory' },
            { name: 'DMA', type: 'direct' }
          ]
        }
      }
    },
    {
      title: 'Device Drivers',
      content: `Device drivers are essential software components:

1. Driver Functions
   - Initialize device
   - Interpret commands
   - Handle interrupts
   - Manage data transfer

2. Driver Types
   - Character device drivers
   - Block device drivers
   - Network device drivers
   - Virtual device drivers

3. Driver Architecture
   - User space vs Kernel space
   - Module loading/unloading
   - Device abstraction
   - Error handling

4. Driver Development
   - Kernel API usage
   - Hardware protocols
   - Testing and debugging
   - Documentation`
    },
    {
      title: 'I/O Software Layers',
      content: `I/O software is organized in layers:

1. User Level I/O Software
   - Libraries
   - System calls
   - Device-independent operations

2. Device Independent Software
   - Naming
   - Protection
   - Blocking/Non-blocking
   - Buffering

3. Device Drivers
   - Device-specific code
   - Standard interfaces
   - Error handling

4. Interrupt Handlers
   - Save device status
   - Signal device driver
   - Handle errors

5. Hardware
   - Device controllers
   - Registers
   - Device mechanics`
    }
  ],
  practicalLab: {
    title: "Device Driver Lab",
    description: "Practice implementing and analyzing device drivers",
    tasks: [
      {
        category: "Character Device Driver",
        commands: [
          {
            command: "cat /dev/example",
            description: "Read from example character device",
            expectedOutput: "Example character device output\nDevice status: OK"
          }
        ]
      },
      {
        category: "Block Device Operations",
        commands: [
          {
            command: "dd if=/dev/zero of=/dev/example bs=1M count=1",
            description: "Write to example block device",
            expectedOutput: "1+0 records in\n1+0 records out\n1048576 bytes (1.0 MB) copied"
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What is the main advantage of DMA over programmed I/O?",
      options: [
        "Lower CPU overhead",
        "Higher data accuracy",
        "Better error handling",
        "Simpler implementation"
      ],
      correct: 0,
      explanation: "DMA reduces CPU overhead by allowing devices to transfer data directly to/from memory without CPU intervention for each byte."
    },
    {
      question: "Which I/O mapping technique uses regular memory instructions?",
      options: [
        "Port-mapped I/O",
        "Memory-mapped I/O",
        "Direct Memory Access",
        "Interrupt-driven I/O"
      ],
      correct: 1,
      explanation: "Memory-mapped I/O maps device registers to memory addresses, allowing the use of regular memory instructions for I/O operations."
    },
    {
      question: "What is the primary role of a device driver?",
      options: [
        "Manage hardware interrupts",
        "Provide a uniform interface to the OS",
        "Control device power management",
        "Handle user input"
      ],
      correct: 1,
      explanation: "The primary role of a device driver is to provide a uniform interface to the operating system, hiding device-specific details."
    }
  ]
};