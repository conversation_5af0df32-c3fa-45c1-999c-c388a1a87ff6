export const deadlocksContent = {
  title: 'Deadlocks',
  description: 'Understanding deadlocks, their conditions, prevention, avoidance, and recovery strategies.',
  sections: [
    {
      title: 'Deadlock Fundamentals',
      content: `A deadlock occurs when a set of processes are blocked because each process is holding a resource and waiting to acquire a resource held by another process.

Four necessary conditions for deadlock:

1. Mutual Exclusion
   - Only one process can use a resource at a time
   - Resources cannot be shared

2. Hold and Wait
   - Process holds resources while waiting for others
   - Does not release current resources

3. No Preemption
   - Resources cannot be forcibly taken
   - Only released voluntarily

4. Circular Wait
   - Set of processes waiting for each other
   - Forms a circular chain`,
      visualization: {
        type: 'interactive',
        component: 'DeadlockVisualization',
        data: {
          processes: [
            { id: 'P1', holding: 'R1', waiting: 'R2' },
            { id: 'P2', holding: 'R2', waiting: 'R1' }
          ]
        }
      }
    },
    {
      title: 'Deadlock Prevention',
      content: `Deadlock prevention works by ensuring at least one of the necessary conditions cannot occur:

1. Mutual Exclusion Prevention
   - Make resources sharable
   - Not always possible (e.g., printers)

2. Hold and Wait Prevention
   - Request all resources initially
   - Release all before requesting new ones

3. No Preemption Prevention
   - Allow resource preemption
   - May not be feasible for some resources

4. Circular Wait Prevention
   - Impose total ordering of resources
   - Request in increasing order`,
      visualization: {
        type: 'interactive',
        component: 'PreventionVisualization',
        data: {
          strategies: [
            { name: 'Resource Ordering', success: true },
            { name: 'Request All Initially', success: true },
            { name: 'Allow Preemption', success: false },
            { name: 'Resource Sharing', success: false }
          ]
        }
      }
    },
    {
      title: 'Deadlock Avoidance',
      content: `Deadlock avoidance requires:

1. System State Information
   - Maximum resource needs
   - Currently allocated resources
   - Available resources

2. Safe State
   - System can allocate resources to each process
   - In some sequence without deadlock

3. Banker's Algorithm
   - Checks if request leads to safe state
   - Grants only safe requests
   - Conservative but guarantees no deadlock

4. Resource-Allocation Graph
   - Nodes represent processes and resources
   - Edges represent allocation and requests
   - Claim edges show future requests`
    },
    {
      title: 'Deadlock Detection and Recovery',
      content: `When prevention and avoidance are not used:

1. Detection Methods
   - Wait-for graph
   - Resource allocation graph
   - State detection algorithms

2. Recovery Strategies
   - Process Termination
     * Terminate all deadlocked processes
     * Terminate one at a time until cycle breaks
   
   - Resource Preemption
     * Select victim process
     * Rollback to safe state
     * Avoid starvation

3. Recovery Costs
   - Termination costs
   - Rollback costs
   - Starvation costs`
    }
  ],
  practicalLab: {
    title: "Deadlock Detection Lab",
    description: "Practice implementing deadlock detection algorithms",
    tasks: [
      {
        category: "Resource Allocation Graph",
        commands: [
          {
            command: "python3 detect_deadlock.py",
            description: "Run deadlock detection algorithm",
            expectedOutput: `Checking for deadlock...
Process P1: Holding R1, Waiting for R2
Process P2: Holding R2, Waiting for R3
Process P3: Holding R3, Waiting for R1
Deadlock detected!
Cycle: P1 -> R2 -> P2 -> R3 -> P3 -> R1 -> P1`
          }
        ]
      },
      {
        category: "Banker's Algorithm",
        commands: [
          {
            command: "python3 bankers_algorithm.py",
            description: "Run Banker's algorithm simulation",
            expectedOutput: `Current State:
Available resources: A=3 B=3 C=2
Allocated resources:
P0: A=0 B=1 C=0
P1: A=2 B=0 C=0
P2: A=3 B=0 C=2
Maximum needs:
P0: A=7 B=5 C=3
P1: A=3 B=2 C=2
P2: A=9 B=0 C=2
System is in safe state.
Safe sequence: P1, P0, P2`
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "Which of the following is NOT a necessary condition for deadlock?",
      options: [
        "Mutual Exclusion",
        "Hold and Wait",
        "Process Priority",
        "Circular Wait"
      ],
      correct: 2,
      explanation: "Process priority is not one of the four necessary conditions for deadlock. The conditions are: mutual exclusion, hold and wait, no preemption, and circular wait."
    },
    {
      question: "What is the main advantage of the Banker's Algorithm?",
      options: [
        "It's very fast",
        "It prevents deadlock",
        "It's easy to implement",
        "It uses minimal resources"
      ],
      correct: 1,
      explanation: "The Banker's Algorithm's main advantage is that it prevents deadlocks by only granting resource requests that lead to safe states."
    },
    {
      question: "Which deadlock recovery method has the least overhead?",
      options: [
        "Process termination",
        "Resource preemption",
        "Process rollback",
        "Total system restart"
      ],
      correct: 0,
      explanation: "Process termination, while potentially wasteful of completed work, has the least overhead in terms of implementation and execution complexity."
    }
  ]
};