export const shellScriptingContent = {
  title: 'Shell Scripting',
  description: 'Master shell scripting in Linux through interactive examples and hands-on practice.',
  sections: [
    {
      title: 'Introduction to Shell Scripting',
      content: `Shell scripting is a powerful way to automate tasks in Linux. A shell script is a file containing a sequence of commands that are executed by the shell.

Key concepts:
1. Shebang (#!)
   - Tells system which interpreter to use
   - Example: #!/bin/bash

2. Script Execution
   - Make script executable: chmod +x script.sh
   - Run script: ./script.sh

3. Variables
   - Assignment: name="value"
   - Access: $name
   - Environment variables: $PATH, $HOME

4. Comments
   - Single line: # comment
   - Multi-line: : ' multiple
                    line comment '`,
      game: {
        type: 'terminal',
        tasks: [
          {
            description: "Create a script file",
            command: "touch script.sh",
            points: 5
          },
          {
            description: "Make script executable",
            command: "chmod +x script.sh",
            points: 5
          },
          {
            description: "Add shebang line",
            command: "echo '#!/bin/bash' > script.sh",
            points: 10
          }
        ]
      }
    },
    {
      title: 'Control Flow',
      content: `Shell scripts support various control flow structures:

1. Conditional Statements
   if [ condition ]; then
     commands
   elif [ condition ]; then
     commands
   else
     commands
   fi

2. Loops
   - for loop:
     for i in {1..5}; do
       commands
     done

   - while loop:
     while [ condition ]; do
       commands
     done

3. Case Statements
   case $var in
     pattern1) commands;;
     pattern2) commands;;
     *) default commands;;
   esac`,
      game: {
        type: 'scriptBuilder',
        tasks: [
          {
            description: "Create an if statement checking if a file exists",
            template: "if [ -f filename ]; then\n  echo 'File exists'\nfi",
            points: 15
          },
          {
            description: "Create a loop that counts from 1 to 5",
            template: "for i in {1..5}; do\n  echo $i\ndone",
            points: 15
          }
        ]
      }
    },
    {
      title: 'Functions',
      content: `Functions help organize and reuse code:

1. Function Declaration
   function_name() {
     commands
     return value
   }

2. Function Parameters
   - Access parameters: $1, $2, etc.
   - Number of parameters: $#
   - All parameters: $@

3. Return Values
   - return command (0-255)
   - Use command substitution
   - Example: result=$(function_name)

4. Local Variables
   - Declare with local keyword
   - Scope limited to function
   - Example: local var="value"`,
      game: {
        type: 'functionBuilder',
        tasks: [
          {
            description: "Create a function that takes two parameters and returns their sum",
            template: "add_numbers() {\n  echo $(($1 + $2))\n}",
            points: 20
          },
          {
            description: "Create a function that checks if a number is even",
            template: "is_even() {\n  if [ $(($1 % 2)) -eq 0 ]; then\n    return 0\n  else\n    return 1\n  fi\n}",
            points: 20
          }
        ]
      }
    }
  ],
  practicalLab: {
    title: "Shell Scripting Lab",
    description: "Practice writing and running shell scripts",
    tasks: [
      {
        category: "Basic Scripts",
        commands: [
          {
            command: "echo '#!/bin/bash\necho \"Hello, World!\"' > hello.sh",
            description: "Create a Hello World script",
            expectedOutput: ""
          },
          {
            command: "chmod +x hello.sh",
            description: "Make script executable",
            expectedOutput: ""
          },
          {
            command: "./hello.sh",
            description: "Run the script",
            expectedOutput: "Hello, World!"
          }
        ]
      },
      {
        category: "Variables and Input",
        commands: [
          {
            command: "name='John'\necho \"Hello, $name!\"",
            description: "Use variables",
            expectedOutput: "Hello, John!"
          },
          {
            command: "read -p 'Enter your name: ' name",
            description: "Read user input",
            expectedOutput: "Enter your name: "
          }
        ]
      },
      {
        category: "Control Flow",
        commands: [
          {
            command: "for i in {1..5}; do echo $i; done",
            description: "For loop example",
            expectedOutput: "1\n2\n3\n4\n5"
          },
          {
            command: "if [ -f file.txt ]; then echo 'File exists'; fi",
            description: "If statement example",
            expectedOutput: ""
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What is the purpose of the shebang (#!) line?",
      options: [
        "To make the script executable",
        "To specify the interpreter",
        "To add a comment",
        "To include another script"
      ],
      correct: 1,
      explanation: "The shebang (#!) line at the beginning of a script tells the system which interpreter should be used to execute the script."
    },
    {
      question: "How do you access the first parameter passed to a shell script?",
      options: [
        "$0",
        "$1",
        "$#",
        "$@"
      ],
      correct: 1,
      explanation: "$1 refers to the first parameter passed to a script. $0 is the script name, $# is the number of parameters, and $@ is all parameters."
    },
    {
      question: "What is the correct way to assign a value to a variable in bash?",
      options: [
        "variable = value",
        "variable=value",
        "$variable = value",
        "set variable = value"
      ],
      correct: 1,
      explanation: "In bash, variables are assigned using the format variable=value with no spaces around the equals sign."
    }
  ]
};