export const linuxBasicsContent = {
  title: "Linux Basics",
  description: "Master the fundamentals of Linux through interactive missions and hands-on challenges.",
  sections: [
    {
      title: "Introduction to Linux Commands",
      content: `Linux commands are the foundation of working with Linux systems. Let's start with the basics:

1. Navigation Commands
   - pwd: Print working directory
   - ls: List directory contents
   - cd: Change directory
   - mkdir: Create directory
   - rmdir: Remove directory

2. File Operations
   - touch: Create empty file
   - cp: Copy files
   - mv: Move/rename files
   - rm: Remove files
   - cat: Display file contents

3. File Permissions
   - chmod: Change file permissions
   - chown: Change file ownership
   - chgrp: Change group ownership

4. System Information
   - uname: System information
   - whoami: Current user
   - date: System date/time
   - df: Disk usage
   - free: Memory usage`,
      visualization: {
        type: "interactive",
        component: "FileSystemVisualization",
        data: {
          structure: "tree",
          nodes: [
            { name: "/", type: "dir" },
            { name: "/home", type: "dir" },
            { name: "/home/<USER>", type: "dir" },
            { name: "/home/<USER>/documents", type: "dir" },
            { name: "/home/<USER>/documents/file.txt", type: "file" }
          ]
        }
      }
    },
    {
      title: "File System Structure",
      content: `The Linux file system follows a hierarchical structure:

/ (Root Directory)
├── /bin - Essential user binaries
├── /boot - Boot loader files
├── /dev - Device files
├── /etc - System configuration
├── /home - User home directories
├── /lib - System libraries
├── /media - Removable media
├── /mnt - Mount point
├── /opt - Optional software
├── /proc - Process information
├── /root - Root user home
├── /sbin - System binaries
├── /tmp - Temporary files
├── /usr - User programs
└── /var - Variable files`,
      visualization: {
        type: "interactive",
        component: "FileSystemVisualization",
        data: {
          structure: "tree",
          nodes: [
            { name: "/", type: "dir" },
            { name: "/bin", type: "dir" },
            { name: "/etc", type: "dir" },
            { name: "/home", type: "dir" },
            { name: "/var", type: "dir" }
          ]
        }
      }
    }
  ],
  practicalLab: {
    title: "Linux Basics Lab",
    description: "Practice essential Linux commands in a safe environment",
    missions: [
      {
        id: "mission1",
        title: "Command Line Warrior",
        description: "Learn essential Linux commands through an immersive terminal experience.",
        difficulty: "Beginner",
        points: 100,
        requirements: ["None"],
        rewards: [
          "Command Line Mastery Badge",
          "100 XP",
          "Access to Navigation Missions"
        ],
        tasks: [
          {
            id: "task1",
            description: "Print current working directory",
            command: "pwd",
            points: 10,
            hint: "Use pwd to show your current location"
          },
          {
            id: "task2",
            description: "List all files in current directory",
            command: "ls -la",
            points: 10,
            hint: "The -la flags show all files with details"
          },
          {
            id: "task3",
            description: "Create a new directory named 'mission1'",
            command: "mkdir mission1",
            points: 10,
            hint: "mkdir creates new directories"
          }
        ]
      },
      {
        id: "mission2",
        title: "File System Navigator",
        description: "Master file system navigation and manipulation.",
        difficulty: "Intermediate",
        points: 150,
        requirements: ["Command Line Warrior"],
        rewards: [
          "File System Expert Badge",
          "150 XP",
          "Access to Permission Missions"
        ],
        tasks: [
          {
            id: "task1",
            description: "Navigate to home directory",
            command: "cd ~",
            points: 15,
            hint: "~ represents your home directory"
          },
          {
            id: "task2",
            description: "Create multiple nested directories",
            command: "mkdir -p mission2/level1/level2",
            points: 15,
            hint: "-p creates parent directories as needed"
          }
        ]
      },
      {
        id: "mission3",
        title: "Permission Master",
        description: "Learn file permissions and access control.",
        difficulty: "Advanced",
        points: 200,
        requirements: ["File System Navigator"],
        rewards: [
          "Security Expert Badge",
          "200 XP",
          "Access to Advanced Missions"
        ],
        tasks: [
          {
            id: "task1",
            description: "Change file permissions",
            command: "chmod 755 script.sh",
            points: 20,
            hint: "755 gives rwx to owner, rx to others"
          },
          {
            id: "task2",
            description: "Change file ownership",
            command: "chown user:group file.txt",
            points: 20,
            hint: "chown changes file owner and group"
          }
        ]
      }
    ]
  },
  conceptualQuiz: [
    {
      question: "What command is used to list files in a directory?",
      options: [
        "dir",
        "ls",
        "list",
        "show"
      ],
      correct: 1,
      explanation: "The 'ls' command is used to list directory contents in Linux. It can be used with various flags like -l for long format and -a to show hidden files."
    },
    {
      question: "Which directory typically contains user home directories?",
      options: [
        "/root",
        "/usr",
        "/home",
        "/users"
      ],
      correct: 2,
      explanation: "The /home directory is where user home directories are stored in Linux. Each user gets their own subdirectory under /home."
    },
    {
      question: "What does the chmod command do?",
      options: [
        "Changes file mode",
        "Changes file permissions",
        "Changes file owner",
        "Changes file type"
      ],
      correct: 1,
      explanation: "chmod (change mode) is used to change file permissions in Linux, controlling who can read, write, or execute the file."
    }
  ]
};