export const linuxIntroductionContent = {
  title: 'Introduction to Linux',
  description: 'Master the fundamentals of Linux operating system through interactive learning and hands-on practice.',
  sections: [
    {
      title: 'What is Linux?',
      content: `Linux is a free and open-source operating system kernel that powers millions of devices worldwide.

Key aspects of Linux:
- Open Source: Free to use, modify, and distribute
- Multi-user: Supports multiple users simultaneously
- Multi-tasking: Runs multiple programs concurrently
- Security: Strong security model and permissions
- Community: Large community of developers and users

Linux is used in:
- Servers
- Desktop computers
- Mobile devices (Android)
- Embedded systems
- Cloud infrastructure`,
      game: {
        type: 'quiz',
        questions: [
          {
            question: "What makes Linux unique among operating systems?",
            options: [
              "Its open-source nature",
              "It only runs on servers",
              "It's only for programmers",
              "It costs more than Windows"
            ],
            correct: 0
          }
        ]
      }
    },
    {
      title: 'Linux File System',
      content: `The Linux file system follows a hierarchical structure:

/ (Root Directory)
├── /bin - Essential user binaries
├── /boot - Boot loader files
├── /dev - Device files
├── /etc - System configuration
├── /home - User home directories
├── /lib - System libraries
├── /media - Removable media
├── /mnt - Mount point
├── /opt - Optional software
├── /proc - Process information
├── /root - Root user home
├── /sbin - System binaries
├── /tmp - Temporary files
├── /usr - User programs
└── /var - Variable files`,
      game: {
        type: 'filesystem',
        tasks: [
          {
            description: "Create a directory in /home",
            command: "mkdir /home/<USER>",
            points: 10
          },
          {
            description: "Create a file in user's directory",
            command: "touch /home/<USER>/file.txt",
            points: 10
          }
        ]
      }
    },
    {
      title: 'Basic Commands',
      content: `Essential Linux commands for navigation and file management:

1. Navigation Commands
   - pwd: Print working directory
   - cd: Change directory
   - ls: List directory contents

2. File Operations
   - touch: Create empty file
   - mkdir: Create directory
   - cp: Copy files
   - mv: Move/rename files
   - rm: Remove files

3. File Viewing
   - cat: Display file contents
   - less: Page through file
   - head: Show file start
   - tail: Show file end

4. System Information
   - uname: System information
   - whoami: Current user
   - date: System date/time
   - df: Disk usage
   - free: Memory usage`,
      game: {
        type: 'terminal',
        tasks: [
          {
            description: "Navigate to home directory",
            command: "cd ~",
            points: 5
          },
          {
            description: "List all files including hidden ones",
            command: "ls -la",
            points: 5
          },
          {
            description: "Create a new directory",
            command: "mkdir test",
            points: 5
          }
        ]
      }
    }
  ],
  practicalLab: {
    title: "Linux Basics Lab",
    description: "Practice essential Linux commands in a safe environment",
    tasks: [
      {
        category: "File Operations",
        commands: [
          {
            command: "ls -la",
            description: "List all files with details",
            expectedOutput: "total 20\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Mar 7 12:00 ."
          },
          {
            command: "mkdir test",
            description: "Create test directory",
            expectedOutput: ""
          },
          {
            command: "touch file.txt",
            description: "Create empty file",
            expectedOutput: ""
          }
        ]
      },
      {
        category: "Navigation",
        commands: [
          {
            command: "pwd",
            description: "Print working directory",
            expectedOutput: "/home/<USER>"
          },
          {
            command: "cd test",
            description: "Change to test directory",
            expectedOutput: ""
          }
        ]
      }
    ]
  }
};