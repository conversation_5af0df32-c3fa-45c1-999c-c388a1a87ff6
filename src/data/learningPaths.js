import { 
  FaLinux, FaNetworkWired, FaCode, FaShieldAlt, FaCloud, 
  FaMobile, FaBug, FaServer, FaUserSecret, FaSearch,
  FaVirus, FaEnvelope, FaBrain, FaLaptopCode, FaDesktop,
  FaMemory, FaMicrochip, FaHdd, FaNetworkWired as FaProcess,
  FaLock, FaFileAlt, FaTerminal, FaUserCog, FaRandom,
  FaExchangeAlt, FaDatabase, FaShieldVirus, FaKey,
  FaFingerprint, FaEthernet, FaWifi, FaGlobe, FaCode as FaWeb,
  FaTools, FaHammer, FaChartBar, FaBook, FaClock, FaSync,
  FaBoxOpen, FaLayerGroup, FaLockOpen, FaExclamationTriangle,
  FaQuestion, FaGraduationCap, FaBriefcase, FaComments,
  FaCog, FaChartLine, FaUsers, FaFolder, FaEdit,
  FaStream, FaNetworkWired as FaNetwork, FaUserShield as FaPermissions,
  FaArchive, FaScroll, FaBoxes, FaWrench, FaClipboardList, FaRobot
} from 'react-icons/fa';

export const learningPaths = [
  {
    id: 'fundamentals',
    title: 'Fundamentals',
    icon: FaDesktop,
    modules: [
      {
        id: 'os-concepts',
        title: 'Operating System Concepts',
        icon: FaDesktop,
        topics: [
          {
            id: 'os-introduction',
            title: 'Introduction to Operating Systems',
            icon: FaDesktop
          },
          {
            id: 'process-management',
            title: 'Process Management',
            icon: FaProcess
          },
          {
            id: 'memory-management',
            title: 'Memory Management',
            icon: FaMemory
          },
          {
            id: 'file-systems',
            title: 'File Systems',
            icon: FaFileAlt
          },
          {
            id: 'io-management',
            title: 'I/O Management',
            icon: FaExchangeAlt
          },
          {
            id: 'cpu-scheduling',
            title: 'CPU Scheduling',
            icon: FaClock
          },
          {
            id: 'deadlocks',
            title: 'Deadlocks',
            icon: FaExclamationTriangle
          },
          {
            id: 'synchronization',
            title: 'Process Synchronization',
            icon: FaSync
          },
          {
            id: 'secondary-storage',
            title: 'Secondary Storage Management',
            icon: FaHdd
          },
          {
            id: 'protection-security',
            title: 'Protection and Security',
            icon: FaLock
          },
          {
            id: 'distributed-os',
            title: 'Distributed Operating Systems',
            icon: FaNetworkWired
          },
          {
            id: 'real-time-os',
            title: 'Real-Time Operating Systems',
            icon: FaClock
          },
          {
            id: 'virtualization',
            title: 'Virtualization',
            icon: FaBoxOpen
          },
          {
            id: 'kernel-operations',
            title: 'Kernel Operations',
            icon: FaLayerGroup
          },
          {
            id: 'system-calls',
            title: 'System Calls',
            icon: FaLockOpen
          },
          {
            id: 'os-interview',
            title: 'OS Interview Q&A',
            icon: FaQuestion
          }
        ]
      },
      {
        id: 'networking',
        title: 'Networking Fundamentals',
        icon: FaNetworkWired,
        topics: [
          {
            id: 'networking-basics',
            title: 'Networking Basics',
            icon: FaEthernet,
            topics: [
              {
                id: 'network-models',
                title: 'Network Models',
                icon: FaLayerGroup
              },
              {
                id: 'network-devices',
                title: 'Network Devices',
                icon: FaServer
              },
              {
                id: 'network-addressing',
                title: 'Network Addressing',
                icon: FaNetworkWired
              },
              {
                id: 'network-media',
                title: 'Network Media',
                icon: FaEthernet
              }
            ]
          },
          {
            id: 'tcp-ip',
            title: 'TCP/IP Protocol Suite',
            icon: FaNetworkWired,
            topics: [
              {
                id: 'ip-protocol',
                title: 'IP Protocol',
                icon: FaNetworkWired
              },
              {
                id: 'tcp-udp',
                title: 'TCP & UDP',
                icon: FaExchangeAlt
              },
              {
                id: 'dns-dhcp',
                title: 'DNS & DHCP',
                icon: FaServer
              },
              {
                id: 'routing-protocols',
                title: 'Routing Protocols',
                icon: FaRandom
              }
            ]
          },
          {
            id: 'network-security',
            title: 'Network Security',
            icon: FaLock,
            topics: [
              {
                id: 'security-basics',
                title: 'Security Fundamentals',
                icon: FaShieldAlt
              },
              {
                id: 'encryption-protocols',
                title: 'Encryption Protocols',
                icon: FaLock
              },
              {
                id: 'network-threats',
                title: 'Network Threats',
                icon: FaBug
              },
              {
                id: 'security-tools',
                title: 'Security Tools',
                icon: FaTools
              }
            ]
          },
          {
            id: 'wireless-networking',
            title: 'Wireless Networking',
            icon: FaWifi,
            topics: [
              {
                id: 'wireless-standards',
                title: 'Wireless Standards',
                icon: FaWifi
              },
              {
                id: 'wireless-security',
                title: 'Wireless Security',
                icon: FaLock
              },
              {
                id: 'wireless-troubleshooting',
                title: 'Troubleshooting',
                icon: FaTools
              }
            ]
          },
          {
            id: 'network-services',
            title: 'Network Services',
            icon: FaServer,
            topics: [
              {
                id: 'web-services',
                title: 'Web Services',
                icon: FaGlobe
              },
              {
                id: 'email-services',
                title: 'Email Services',
                icon: FaEnvelope
              },
              {
                id: 'file-services',
                title: 'File Services',
                icon: FaFileAlt
              }
            ]
          },
          {
            id: 'network-management',
            title: 'Network Management',
            icon: FaCog,
            topics: [
              {
                id: 'monitoring',
                title: 'Network Monitoring',
                icon: FaChartLine
              },
              {
                id: 'troubleshooting',
                title: 'Troubleshooting',
                icon: FaTools
              },
              {
                id: 'documentation',
                title: 'Documentation',
                icon: FaBook
              }
            ]
          },
          {
            id: 'network-interview',
            title: 'Network Interview Q&A',
            icon: FaQuestion
          }
        ]
      },
      {
        id: 'linux',
        title: 'Linux Essentials',
        icon: FaLinux,
        topics: [
          {
            id: 'linux-basics',
            title: 'Linux Basics',
            icon: FaTerminal,
            topics: [
              {
                id: 'linux-intro',
                title: 'Introduction to Linux',
                icon: FaLinux
              },
              {
                id: 'command-line',
                title: 'Command Line Interface',
                icon: FaTerminal
              },
              {
                id: 'file-system',
                title: 'File System Hierarchy',
                icon: FaFolder
              },
              {
                id: 'basic-commands',
                title: 'Basic Commands',
                icon: FaCode
              }
            ]
          },
          {
            id: 'shell-scripting',
            title: 'Shell Scripting',
            icon: FaTerminal,
            topics: [
              {
                id: 'bash-basics',
                title: 'Bash Basics',
                icon: FaCode
              },
              {
                id: 'variables-control',
                title: 'Variables & Control Flow',
                icon: FaStream
              },
              {
                id: 'functions-arrays',
                title: 'Functions & Arrays',
                icon: FaBoxes
              },
              {
                id: 'script-debugging',
                title: 'Script Debugging',
                icon: FaBug
              }
            ]
          },
          {
            id: 'system-administration',
            title: 'System Administration',
            icon: FaUserCog,
            topics: [
              {
                id: 'user-management',
                title: 'User Management',
                icon: FaUsers
              },
              {
                id: 'permissions',
                title: 'Permissions & Access Control',
                icon: FaPermissions
              },
              {
                id: 'process-management',
                title: 'Process Management',
                icon: FaStream
              },
              {
                id: 'service-management',
                title: 'Service Management',
                icon: FaCog
              }
            ]
          },
          {
            id: 'package-management',
            title: 'Package Management',
            icon: FaBoxes,
            topics: [
              {
                id: 'package-basics',
                title: 'Package Management Basics',
                icon: FaArchive
              },
              {
                id: 'apt-yum',
                title: 'APT & YUM',
                icon: FaBoxOpen
              },
              {
                id: 'source-builds',
                title: 'Building from Source',
                icon: FaHammer
              }
            ]
          },
          {
            id: 'networking-tools',
            title: 'Networking Tools',
            icon: FaNetwork,
            topics: [
              {
                id: 'network-config',
                title: 'Network Configuration',
                icon: FaCog
              },
              {
                id: 'network-tools',
                title: 'Network Utilities',
                icon: FaTools
              },
              {
                id: 'remote-access',
                title: 'Remote Access',
                icon: FaTerminal
              }
            ]
          },
          {
            id: 'system-monitoring',
            title: 'System Monitoring',
            icon: FaChartLine,
            topics: [
              {
                id: 'performance-tools',
                title: 'Performance Tools',
                icon: FaChartBar
              },
              {
                id: 'log-management',
                title: 'Log Management',
                icon: FaScroll
              },
              {
                id: 'monitoring-tools',
                title: 'Monitoring Tools',
                icon: FaChartLine
              }
            ]
          },
          {
            id: 'security-hardening',
            title: 'Security Hardening',
            icon: FaShieldAlt,
            topics: [
              {
                id: 'security-basics',
                title: 'Security Basics',
                icon: FaLock
              },
              {
                id: 'firewall-config',
                title: 'Firewall Configuration',
                icon: FaShieldAlt
              },
              {
                id: 'security-tools',
                title: 'Security Tools',
                icon: FaTools
              }
            ]
          },
          {
            id: 'storage-management',
            title: 'Storage Management',
            icon: FaHdd,
            topics: [
              {
                id: 'disk-management',
                title: 'Disk Management',
                icon: FaHdd
              },
              {
                id: 'lvm',
                title: 'Logical Volume Management',
                icon: FaLayerGroup
              },
              {
                id: 'raid',
                title: 'RAID Configuration',
                icon: FaDatabase
              }
            ]
          },
          {
            id: 'troubleshooting',
            title: 'Troubleshooting',
            icon: FaWrench,
            topics: [
              {
                id: 'system-recovery',
                title: 'System Recovery',
                icon: FaSync
              },
              {
                id: 'diagnostics',
                title: 'Diagnostic Tools',
                icon: FaSearch
              },
              {
                id: 'common-issues',
                title: 'Common Issues',
                icon: FaExclamationTriangle
              }
            ]
          },
          {
            id: 'automation',
            title: 'Automation & Scheduling',
            icon: FaClock,
            topics: [
              {
                id: 'cron-jobs',
                title: 'Cron Jobs',
                icon: FaClock
              },
              {
                id: 'automation-tools',
                title: 'Automation Tools',
                icon: FaRobot
              },
              {
                id: 'task-scheduling',
                title: 'Task Scheduling',
                icon: FaClipboardList
              }
            ]
          },
          {
            id: 'linux-interview',
            title: 'Linux Interview Q&A',
            icon: FaQuestion
          }
        ]
      }
    ]
  },
  {
    id: 'offensive-security',
    title: 'Offensive Security',
    icon: FaBug,
    modules: [
      {
        id: 'penetration-testing',
        title: 'Penetration Testing',
        icon: FaHammer,
        topics: [
          {
            id: 'recon',
            title: 'Information Gathering',
            icon: FaSearch
          },
          {
            id: 'vulnerability-assessment',
            title: 'Vulnerability Assessment',
            icon: FaBug
          },
          {
            id: 'exploitation',
            title: 'Exploitation',
            icon: FaTools
          }
        ]
      },
      {
        id: 'web-security',
        title: 'Web Security',
        icon: FaWeb,
        topics: [
          {
            id: 'web-vulnerabilities',
            title: 'Common Web Vulnerabilities',
            icon: FaBug
          },
          {
            id: 'web-exploitation',
            title: 'Web Exploitation',
            icon: FaHammer
          },
          {
            id: 'secure-coding',
            title: 'Secure Coding Practices',
            icon: FaCode
          }
        ]
      },
      {
        id: 'mobile-security',
        title: 'Mobile Security',
        icon: FaMobile,
        topics: [
          {
            id: 'android-security',
            title: 'Android Security',
            icon: FaMobile
          },
          {
            id: 'ios-security',
            title: 'iOS Security',
            icon: FaMobile
          }
        ]
      }
    ]
  },
  {
    id: 'defensive-security',
    title: 'Defensive Security',
    icon: FaShieldAlt,
    modules: [
      {
        id: 'incident-response',
        title: 'Incident Response',
        icon: FaShieldVirus,
        topics: [
          {
            id: 'incident-handling',
            title: 'Incident Handling',
            icon: FaShieldAlt
          },
          {
            id: 'digital-forensics',
            title: 'Digital Forensics',
            icon: FaSearch
          },
          {
            id: 'malware-analysis',
            title: 'Malware Analysis',
            icon: FaVirus
          }
        ]
      },
      {
        id: 'security-operations',
        title: 'Security Operations',
        icon: FaUserSecret,
        topics: [
          {
            id: 'siem',
            title: 'SIEM',
            icon: FaChartBar
          },
          {
            id: 'threat-hunting',
            title: 'Threat Hunting',
            icon: FaSearch
          },
          {
            id: 'security-monitoring',
            title: 'Security Monitoring',
            icon: FaDesktop
          }
        ]
      }
    ]
  },
  {
    id: 'cryptography',
    title: 'Cryptography',
    icon: FaKey,
    modules: [
      {
        id: 'crypto-fundamentals',
        title: 'Cryptography Fundamentals',
        icon: FaKey,
        topics: [
          {
            id: 'symmetric-crypto',
            title: 'Symmetric Cryptography',
            icon: FaKey
          },
          {
            id: 'asymmetric-crypto',
            title: 'Asymmetric Cryptography',
            icon: FaKey
          },
          {
            id: 'hashing',
            title: 'Hashing and Digital Signatures',
            icon: FaFingerprint
          }
        ]
      },
      {
        id: 'crypto-applications',
        title: 'Cryptography Applications',
        icon: FaLock,
        topics: [
          {
            id: 'tls-ssl',
            title: 'TLS/SSL',
            icon: FaLock
          },
          {
            id: 'vpn-crypto',
            title: 'VPN Technologies',
            icon: FaGlobe
          },
          {
            id: 'blockchain',
            title: 'Blockchain Technology',
            icon: FaDatabase
          }
        ]
      }
    ]
  },
  {
    id: 'cloud-security',
    title: 'Cloud Security',
    icon: FaCloud,
    modules: [
      {
        id: 'cloud-concepts',
        title: 'Cloud Security Concepts',
        icon: FaCloud,
        topics: [
          {
            id: 'cloud-models',
            title: 'Cloud Service Models',
            icon: FaCloud
          },
          {
            id: 'cloud-threats',
            title: 'Cloud Security Threats',
            icon: FaBug
          },
          {
            id: 'cloud-controls',
            title: 'Security Controls',
            icon: FaShieldAlt
          }
        ]
      },
      {
        id: 'cloud-platforms',
        title: 'Cloud Platforms',
        icon: FaServer,
        topics: [
          {
            id: 'aws-security',
            title: 'AWS Security',
            icon: FaCloud
          },
          {
            id: 'azure-security',
            title: 'Azure Security',
            icon: FaCloud
          },
          {
            id: 'gcp-security',
            title: 'GCP Security',
            icon: FaCloud
          }
        ]
      }
    ]
  },
  {
    id: 'governance',
    title: 'Security Governance',
    icon: FaBook,
    modules: [
      {
        id: 'risk-management',
        title: 'Risk Management',
        icon: FaChartBar,
        topics: [
          {
            id: 'risk-assessment',
            title: 'Risk Assessment',
            icon: FaChartBar
          },
          {
            id: 'compliance',
            title: 'Compliance',
            icon: FaBook
          },
          {
            id: 'policies',
            title: 'Security Policies',
            icon: FaBook
          }
        ]
      },
      {
        id: 'security-frameworks',
        title: 'Security Frameworks',
        icon: FaBook,
        topics: [
          {
            id: 'iso-27001',
            title: 'ISO 27001',
            icon: FaBook
          },
          {
            id: 'nist',
            title: 'NIST Framework',
            icon: FaBook
          },
          {
            id: 'pci-dss',
            title: 'PCI DSS',
            icon: FaBook
          }
        ]
      }
    ]
  }
];