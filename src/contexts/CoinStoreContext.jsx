import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const CoinStoreContext = createContext();

export function CoinStoreProvider({ children }) {
  const { user, profile } = useAuth();
  const [storeItems, setStoreItems] = useState([]);
  const [categories, setCategories] = useState([]);
  const [coinPackages, setCoinPackages] = useState([]);
  const [userInventory, setUserInventory] = useState([]);
  const [userWishlist, setUserWishlist] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch store data
  useEffect(() => {
    const fetchStoreData = async () => {
      try {
        setLoading(true);
        
        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('store_categories')
          .select('*')
          .order('display_order', { ascending: true });
          
        if (categoriesError) throw categoriesError;
        
        // Fetch store items
        let query = supabase
          .from('store_items')
          .select(`
            id,
            name,
            description,
            category_id,
            image_url,
            price,
            discount_price,
            discount_start,
            discount_end,
            stock,
            is_featured,
            is_premium,
            is_business,
            content,
            item_type,
            created_at,
            category:store_categories(name)
          `)
          .eq('is_active', true);
        
        const { data: itemsData, error: itemsError } = await query;
        
        if (itemsError) throw itemsError;
        
        // Fetch coin packages
        const { data: packagesData, error: packagesError } = await supabase
          .from('coin_packages')
          .select('*')
          .eq('is_active', true)
          .order('coins', { ascending: true });
          
        if (packagesError) throw packagesError;
        
        setCategories(categoriesData);
        setStoreItems(itemsData);
        setCoinPackages(packagesData);
      } catch (error) {
        console.error('Error fetching store data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchStoreData();
  }, []);
  
  // Fetch user's inventory and wishlist
  useEffect(() => {
    if (!user) {
      setUserInventory([]);
      setUserWishlist([]);
      setTransactions([]);
      return;
    }
    
    const fetchUserData = async () => {
      try {
        setLoading(true);
        
        // Fetch user's inventory
        const { data: inventoryData, error: inventoryError } = await supabase.rpc('get_user_inventory', {
          user_id: user.id
        });
        
        if (inventoryError) throw inventoryError;
        
        // Fetch user's wishlist
        const { data: wishlistData, error: wishlistError } = await supabase
          .from('user_wishlist')
          .select(`
            id,
            item_id,
            added_at,
            item:store_items(
              id,
              name,
              description,
              image_url,
              price,
              discount_price,
              item_type,
              category:store_categories(name)
            )
          `)
          .eq('user_id', user.id);
          
        if (wishlistError) throw wishlistError;
        
        // Fetch user's transactions
        const { data: transactionsData, error: transactionsError } = await supabase.rpc('get_user_transaction_history', {
          user_id: user.id,
          limit_count: 50
        });
        
        if (transactionsError) throw transactionsError;
        
        setUserInventory(inventoryData || []);
        setUserWishlist(wishlistData || []);
        setTransactions(transactionsData || []);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserData();
    
    // Set up real-time subscription for user's inventory
    const inventorySubscription = supabase
      .channel(`user-inventory-${user.id}`)
      .on('INSERT', (payload) => {
        // Fetch the complete inventory item with related data
        supabase.rpc('get_user_inventory', {
          user_id: user.id
        }).then(({ data }) => {
          if (data) {
            setUserInventory(data);
          }
        });
      })
      .on('UPDATE', (payload) => {
        // Fetch the complete inventory item with related data
        supabase.rpc('get_user_inventory', {
          user_id: user.id
        }).then(({ data }) => {
          if (data) {
            setUserInventory(data);
          }
        });
      })
      .subscribe();
      
    // Set up real-time subscription for user's transactions
    const transactionSubscription = supabase
      .channel(`user-transactions-${user.id}`)
      .on('INSERT', (payload) => {
        // Fetch the complete transaction history
        supabase.rpc('get_user_transaction_history', {
          user_id: user.id,
          limit_count: 50
        }).then(({ data }) => {
          if (data) {
            setTransactions(data);
          }
        });
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(inventorySubscription);
      supabase.removeChannel(transactionSubscription);
    };
  }, [user]);
  
  // Purchase a store item
  const purchaseItem = async (itemId, quantity = 1) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to make a purchase');
      
      // Check if user has enough coins
      const item = storeItems.find(item => item.id === itemId);
      if (!item) throw new Error('Item not found');
      
      const totalCost = item.price * quantity;
      
      if ((profile?.coins || 0) < totalCost) {
        throw new Error('Not enough coins');
      }
      
      // Purchase item
      const { data, error } = await supabase.rpc('purchase_store_item', {
        user_id: user.id,
        item_id: itemId,
        quantity
      });
      
      if (error) throw error;
      
      // Update user's coins in profile
      const newCoins = (profile?.coins || 0) - totalCost;
      
      return {
        success: true,
        newCoins,
        item
      };
    } catch (error) {
      console.error('Error purchasing item:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Add item to wishlist
  const addToWishlist = async (itemId) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to add to wishlist');
      
      // Check if item is already in wishlist
      const existingItem = userWishlist.find(item => item.item_id === itemId);
      if (existingItem) {
        throw new Error('Item is already in your wishlist');
      }
      
      // Add to wishlist
      const { data, error } = await supabase
        .from('user_wishlist')
        .insert([{
          user_id: user.id,
          item_id: itemId
        }])
        .select()
        .single();
        
      if (error) throw error;
      
      // Fetch the complete wishlist item with related data
      const { data: itemData, error: itemError } = await supabase
        .from('store_items')
        .select(`
          id,
          name,
          description,
          image_url,
          price,
          discount_price,
          item_type,
          category:store_categories(name)
        `)
        .eq('id', itemId)
        .single();
        
      if (itemError) throw itemError;
      
      // Add to state
      setUserWishlist([...userWishlist, {
        id: data.id,
        item_id: itemId,
        added_at: data.added_at,
        item: itemData
      }]);
      
      return {
        success: true
      };
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Remove item from wishlist
  const removeFromWishlist = async (wishlistId) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to remove from wishlist');
      
      // Remove from wishlist
      const { error } = await supabase
        .from('user_wishlist')
        .delete()
        .eq('id', wishlistId)
        .eq('user_id', user.id);
        
      if (error) throw error;
      
      // Remove from state
      setUserWishlist(userWishlist.filter(item => item.id !== wishlistId));
      
      return {
        success: true
      };
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Purchase coins
  const purchaseCoins = async (packageId) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to purchase coins');
      
      // Get package details
      const coinPackage = coinPackages.find(pkg => pkg.id === packageId);
      if (!coinPackage) throw new Error('Package not found');
      
      // In a real app, this would integrate with a payment processor
      // For now, we'll just simulate a successful purchase
      
      // Create coin purchase record
      const { data: purchaseData, error: purchaseError } = await supabase
        .from('coin_purchases')
        .insert([{
          user_id: user.id,
          package_id: packageId,
          coins: coinPackage.coins,
          amount: coinPackage.price,
          currency: coinPackage.currency,
          payment_method: 'credit_card',
          payment_id: `sim_${Date.now()}`,
          status: 'completed',
          completed_at: new Date()
        }])
        .select()
        .single();
        
      if (purchaseError) throw purchaseError;
      
      // Add coins to user
      const { data: coinData, error: coinError } = await supabase.rpc('add_user_coins', {
        user_id: user.id,
        coins_to_add: coinPackage.coins,
        transaction_type: 'purchase',
        description: `Purchase of ${coinPackage.name}`,
        reference_id: purchaseData.id,
        reference_type: 'coin_package'
      });
      
      if (coinError) throw coinError;
      
      return {
        success: true,
        newCoins: coinData,
        package: coinPackage
      };
    } catch (error) {
      console.error('Error purchasing coins:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Get user's coin balance
  const getUserCoinBalance = async () => {
    try {
      if (!user) return 0;
      
      const { data, error } = await supabase.rpc('get_user_coin_balance', {
        user_id: user.id
      });
      
      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Error getting coin balance:', error);
      setError(error.message);
      return 0;
    }
  };
  
  // Check if user owns an item
  const userOwnsItem = (itemId) => {
    return userInventory.some(item => item.item_id === itemId && !item.is_consumed);
  };
  
  // Get user's inventory item
  const getUserInventoryItem = (itemId) => {
    return userInventory.find(item => item.item_id === itemId);
  };
  
  // Consume an inventory item
  const consumeInventoryItem = async (inventoryId) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to consume an item');
      
      // Get inventory item
      const inventoryItem = userInventory.find(item => item.id === inventoryId);
      if (!inventoryItem) throw new Error('Item not found in your inventory');
      
      if (inventoryItem.is_consumed) {
        throw new Error('This item has already been consumed');
      }
      
      // Update inventory item
      const { error } = await supabase
        .from('user_inventory')
        .update({
          is_consumed: true,
          consumed_at: new Date()
        })
        .eq('id', inventoryId)
        .eq('user_id', user.id);
        
      if (error) throw error;
      
      // Update state
      setUserInventory(userInventory.map(item => 
        item.id === inventoryId 
          ? { ...item, is_consumed: true, consumed_at: new Date() } 
          : item
      ));
      
      return {
        success: true,
        item: inventoryItem
      };
    } catch (error) {
      console.error('Error consuming item:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Get filtered store items
  const getFilteredStoreItems = (filters = {}) => {
    let filteredItems = [...storeItems];
    
    // Filter by category
    if (filters.category) {
      filteredItems = filteredItems.filter(
        item => item.category.name === filters.category
      );
    }
    
    // Filter by price range
    if (filters.minPrice !== undefined) {
      filteredItems = filteredItems.filter(
        item => item.price >= filters.minPrice
      );
    }
    
    if (filters.maxPrice !== undefined) {
      filteredItems = filteredItems.filter(
        item => item.price <= filters.maxPrice
      );
    }
    
    // Filter by item type
    if (filters.itemType) {
      filteredItems = filteredItems.filter(
        item => item.item_type === filters.itemType
      );
    }
    
    // Filter by search query
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredItems = filteredItems.filter(
        item => 
          item.name.toLowerCase().includes(searchLower) ||
          item.description.toLowerCase().includes(searchLower)
      );
    }
    
    // Filter by featured
    if (filters.featured) {
      filteredItems = filteredItems.filter(
        item => item.is_featured
      );
    }
    
    // Filter by owned/not owned
    if (filters.owned === true) {
      filteredItems = filteredItems.filter(
        item => userOwnsItem(item.id)
      );
    } else if (filters.owned === false) {
      filteredItems = filteredItems.filter(
        item => !userOwnsItem(item.id)
      );
    }
    
    // Filter by subscription tier
    if (!profile || profile.subscription_tier === 'free') {
      filteredItems = filteredItems.filter(
        item => !item.is_premium && !item.is_business
      );
    } else if (profile.subscription_tier === 'premium') {
      filteredItems = filteredItems.filter(
        item => !item.is_business
      );
    }
    
    // Sort items
    if (filters.sort) {
      switch (filters.sort) {
        case 'price_asc':
          filteredItems.sort((a, b) => a.price - b.price);
          break;
        case 'price_desc':
          filteredItems.sort((a, b) => b.price - a.price);
          break;
        case 'newest':
          filteredItems.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
          break;
        case 'oldest':
          filteredItems.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
          break;
        default:
          break;
      }
    }
    
    return filteredItems;
  };

  // Context value
  const value = {
    storeItems,
    categories,
    coinPackages,
    userInventory,
    userWishlist,
    transactions,
    loading,
    error,
    purchaseItem,
    addToWishlist,
    removeFromWishlist,
    purchaseCoins,
    getUserCoinBalance,
    userOwnsItem,
    getUserInventoryItem,
    consumeInventoryItem,
    getFilteredStoreItems
  };

  return <CoinStoreContext.Provider value={value}>{children}</CoinStoreContext.Provider>;
}

// Custom hook to use the coin store context
export function useCoinStore() {
  const context = useContext(CoinStoreContext);
  if (context === undefined) {
    throw new Error('useCoinStore must be used within a CoinStoreProvider');
  }
  return context;
}
