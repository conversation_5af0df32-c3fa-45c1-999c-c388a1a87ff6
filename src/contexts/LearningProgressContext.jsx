import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const LearningProgressContext = createContext();

export function LearningProgressProvider({ children }) {
  const { user, profile } = useAuth();
  const [progress, setProgress] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [recommendations, setRecommendations] = useState([]);
  const [userSkills, setUserSkills] = useState(null);
  const [userInterests, setUserInterests] = useState(null);

  // Load progress data on component mount and when user changes
  useEffect(() => {
    loadProgressData();
  }, [user]);

  // Load user skills and interests
  useEffect(() => {
    loadUserPreferences();
  }, [user]);

  // Generate recommendations when progress or user preferences change
  useEffect(() => {
    if (!loading) {
      generateRecommendations();
    }
  }, [progress, userSkills, userInterests, loading]);

  // Load progress data from localStorage and database
  const loadProgressData = async () => {
    try {
      setLoading(true);
      
      // First, load from localStorage
      const localProgress = loadLocalProgress();
      
      // If user is logged in, fetch from database and merge
      if (user) {
        const { data, error } = await supabase
          .from('module_progress')
          .select('module_id, progress, last_activity, completed')
          .eq('user_id', user.id);
        
        if (error) throw error;
        
        // Convert array to object for easier merging
        const dbProgress = {};
        data.forEach(item => {
          dbProgress[item.module_id] = item.progress;
        });
        
        // Merge local and database progress, preferring the higher value
        const mergedProgress = { ...localProgress };
        
        Object.keys(dbProgress).forEach(moduleId => {
          if (!mergedProgress[moduleId] || dbProgress[moduleId] > mergedProgress[moduleId]) {
            mergedProgress[moduleId] = dbProgress[moduleId];
          }
        });
        
        // Update localStorage with merged progress
        localStorage.setItem('learning_progress', JSON.stringify(mergedProgress));
        
        // Sync local progress to database if local has higher values
        Object.keys(localProgress).forEach(moduleId => {
          if (!dbProgress[moduleId] || localProgress[moduleId] > dbProgress[moduleId]) {
            syncProgressToDatabase(moduleId, localProgress[moduleId]);
          }
        });
        
        setProgress(mergedProgress);
      } else {
        // If not logged in, just use localStorage
        setProgress(localProgress);
      }
    } catch (error) {
      console.error('Error loading progress data:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Load progress from localStorage
  const loadLocalProgress = () => {
    try {
      const progressData = localStorage.getItem('learning_progress');
      return progressData ? JSON.parse(progressData) : {};
    } catch (error) {
      console.error('Error loading local progress:', error);
      return {};
    }
  };

  // Load user preferences (skills and interests)
  const loadUserPreferences = async () => {
    try {
      // First check localStorage
      const localSkills = localStorage.getItem('user_skills');
      const localInterests = localStorage.getItem('user_interests');
      
      if (localSkills) {
        setUserSkills(JSON.parse(localSkills));
      }
      
      if (localInterests) {
        setUserInterests(JSON.parse(localInterests));
      }
      
      // If user is logged in, fetch from database
      if (user) {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('skills, interests')
          .eq('user_id', user.id)
          .single();
        
        if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
          throw error;
        }
        
        if (data) {
          // Update state and localStorage
          setUserSkills(data.skills);
          setUserInterests(data.interests);
          
          localStorage.setItem('user_skills', JSON.stringify(data.skills));
          localStorage.setItem('user_interests', JSON.stringify(data.interests));
        } else if (localSkills || localInterests) {
          // Sync local preferences to database
          syncPreferencesToDatabase(
            localSkills ? JSON.parse(localSkills) : null,
            localInterests ? JSON.parse(localInterests) : null
          );
        }
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  // Update module progress
  const updateProgress = async (moduleId, newProgress, contentDetails = {}) => {
    try {
      // Ensure progress is between 0 and 100
      newProgress = Math.max(0, Math.min(100, newProgress));
      
      // Update local state
      setProgress(prev => ({
        ...prev,
        [moduleId]: newProgress
      }));
      
      // Update localStorage
      const localProgress = loadLocalProgress();
      localProgress[moduleId] = newProgress;
      localStorage.setItem('learning_progress', JSON.stringify(localProgress));
      
      // If user is logged in, sync to database
      if (user) {
        await syncProgressToDatabase(moduleId, newProgress, contentDetails);
      }
      
      // Generate new recommendations after progress update
      generateRecommendations();
      
      return true;
    } catch (error) {
      console.error('Error updating progress:', error);
      setError(error.message);
      return false;
    }
  };

  // Sync progress to database
  const syncProgressToDatabase = async (moduleId, progress, contentDetails = {}) => {
    if (!user) return;
    
    try {
      const isCompleted = progress >= 100;
      const timestamp = new Date().toISOString();
      
      // Check if record exists
      const { data: existingData, error: fetchError } = await supabase
        .from('module_progress')
        .select('id')
        .eq('user_id', user.id)
        .eq('module_id', moduleId)
        .single();
      
      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }
      
      if (existingData) {
        // Update existing record
        const { error: updateError } = await supabase
          .from('module_progress')
          .update({
            progress,
            last_activity: timestamp,
            completed: isCompleted,
            completed_at: isCompleted ? timestamp : null,
            content_details: contentDetails
          })
          .eq('id', existingData.id);
        
        if (updateError) throw updateError;
      } else {
        // Insert new record
        const { error: insertError } = await supabase
          .from('module_progress')
          .insert({
            user_id: user.id,
            module_id: moduleId,
            progress,
            last_activity: timestamp,
            completed: isCompleted,
            completed_at: isCompleted ? timestamp : null,
            content_details: contentDetails
          });
        
        if (insertError) throw insertError;
      }
    } catch (error) {
      console.error('Error syncing progress to database:', error);
      throw error;
    }
  };

  // Sync user preferences to database
  const syncPreferencesToDatabase = async (skills, interests) => {
    if (!user) return;
    
    try {
      // Check if record exists
      const { data: existingData, error: fetchError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', user.id)
        .single();
      
      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }
      
      const preferencesData = {
        skills: skills || {},
        interests: interests || {}
      };
      
      if (existingData) {
        // Update existing record
        const { error: updateError } = await supabase
          .from('user_preferences')
          .update(preferencesData)
          .eq('id', existingData.id);
        
        if (updateError) throw updateError;
      } else {
        // Insert new record
        const { error: insertError } = await supabase
          .from('user_preferences')
          .insert({
            user_id: user.id,
            ...preferencesData
          });
        
        if (insertError) throw insertError;
      }
    } catch (error) {
      console.error('Error syncing preferences to database:', error);
    }
  };

  // Update user skills
  const updateUserSkills = async (skills) => {
    try {
      setUserSkills(skills);
      localStorage.setItem('user_skills', JSON.stringify(skills));
      
      if (user) {
        await syncPreferencesToDatabase(skills, userInterests);
      }
      
      return true;
    } catch (error) {
      console.error('Error updating user skills:', error);
      return false;
    }
  };

  // Update user interests
  const updateUserInterests = async (interests) => {
    try {
      setUserInterests(interests);
      localStorage.setItem('user_interests', JSON.stringify(interests));
      
      if (user) {
        await syncPreferencesToDatabase(userSkills, interests);
      }
      
      return true;
    } catch (error) {
      console.error('Error updating user interests:', error);
      return false;
    }
  };

  // Generate personalized recommendations
  const generateRecommendations = async () => {
    try {
      // Fetch all modules
      const { data: modules, error } = await supabase
        .from('learning_modules')
        .select(`
          id,
          title,
          slug,
          description,
          category_id,
          difficulty_id,
          estimated_time,
          is_premium,
          is_business,
          category:learning_module_categories(name),
          difficulty:learning_module_difficulty_levels(name)
        `)
        .eq('is_active', true);
      
      if (error) throw error;
      
      // Filter out completed modules
      const completedModuleIds = Object.entries(progress)
        .filter(([_, value]) => value >= 100)
        .map(([key, _]) => key);
      
      const availableModules = modules.filter(module => 
        !completedModuleIds.includes(module.id) &&
        (module.is_premium === false || profile?.subscription_tier === 'premium' || profile?.subscription_tier === 'business') &&
        (module.is_business === false || profile?.subscription_tier === 'business')
      );
      
      // Calculate scores for each module based on user preferences and progress
      const scoredModules = availableModules.map(module => {
        let score = 0;
        
        // Base score
        score += 1;
        
        // Boost score for modules in user's preferred categories
        if (userInterests && userInterests.categories && 
            userInterests.categories.includes(module.category.name)) {
          score += 3;
        }
        
        // Boost score for modules matching user's skill level
        if (userSkills && userSkills.experience) {
          const difficultyMap = {
            'beginner': ['Beginner'],
            'intermediate': ['Beginner', 'Intermediate'],
            'advanced': ['Intermediate', 'Advanced'],
            'expert': ['Advanced', 'Expert']
          };
          
          if (difficultyMap[userSkills.experience].includes(module.difficulty.name)) {
            score += 2;
          }
        }
        
        // Boost score for modules in the same category as modules in progress
        const inProgressModuleIds = Object.entries(progress)
          .filter(([_, value]) => value > 0 && value < 100)
          .map(([key, _]) => key);
        
        const inProgressModules = modules.filter(m => inProgressModuleIds.includes(m.id));
        
        if (inProgressModules.some(m => m.category_id === module.category_id)) {
          score += 2;
        }
        
        // Boost score for next modules in a sequence
        // This is a simplified approach - in a real system, you'd have explicit prerequisites
        const completedModulesInSameCategory = modules
          .filter(m => completedModuleIds.includes(m.id) && m.category_id === module.category_id);
        
        if (completedModulesInSameCategory.length > 0) {
          score += 2;
        }
        
        return {
          ...module,
          score
        };
      });
      
      // Sort by score and take top 5
      const topRecommendations = scoredModules
        .sort((a, b) => b.score - a.score)
        .slice(0, 5);
      
      setRecommendations(topRecommendations);
    } catch (error) {
      console.error('Error generating recommendations:', error);
    }
  };

  // Get module progress
  const getModuleProgress = (moduleId) => {
    return progress[moduleId] || 0;
  };

  // Check if module is completed
  const isModuleCompleted = (moduleId) => {
    return (progress[moduleId] || 0) >= 100;
  };

  // Get all completed modules
  const getCompletedModules = () => {
    return Object.entries(progress)
      .filter(([_, value]) => value >= 100)
      .map(([key, _]) => key);
  };

  // Get all in-progress modules
  const getInProgressModules = () => {
    return Object.entries(progress)
      .filter(([_, value]) => value > 0 && value < 100)
      .map(([key, _]) => key);
  };

  // Get next recommended module
  const getNextRecommendedModule = () => {
    return recommendations.length > 0 ? recommendations[0] : null;
  };

  // Track content consumption
  const trackContentConsumption = async (moduleId, sectionId, contentType, timeSpent, completed = false) => {
    try {
      // Calculate progress based on content consumption
      const currentProgress = progress[moduleId] || 0;
      
      // Only update if not already completed
      if (currentProgress < 100) {
        let newProgress = currentProgress;
        
        // Different content types contribute differently to progress
        const contentDetails = {
          sectionId,
          contentType,
          timeSpent,
          timestamp: new Date().toISOString(),
          completed
        };
        
        // If section is completed, increase progress
        if (completed) {
          // Get module details to calculate appropriate progress increment
          const { data: moduleData, error } = await supabase
            .from('learning_modules')
            .select('content')
            .eq('id', moduleId)
            .single();
          
          if (error) throw error;
          
          if (moduleData && moduleData.content) {
            const totalSections = moduleData.content.sections?.length || 1;
            const sectionProgress = Math.floor(100 / totalSections);
            newProgress = Math.min(currentProgress + sectionProgress, 100);
          } else {
            // Fallback if module data not available
            newProgress = Math.min(currentProgress + 20, 100);
          }
        } else {
          // Small increment for partial consumption
          newProgress = Math.min(currentProgress + 5, 99); // Cap at 99% until explicitly completed
        }
        
        // Update progress
        await updateProgress(moduleId, newProgress, contentDetails);
      }
      
      return true;
    } catch (error) {
      console.error('Error tracking content consumption:', error);
      return false;
    }
  };

  // Context value
  const value = {
    progress,
    recommendations,
    userSkills,
    userInterests,
    loading,
    error,
    updateProgress,
    getModuleProgress,
    isModuleCompleted,
    getCompletedModules,
    getInProgressModules,
    getNextRecommendedModule,
    updateUserSkills,
    updateUserInterests,
    trackContentConsumption,
    loadProgressData
  };

  return <LearningProgressContext.Provider value={value}>{children}</LearningProgressContext.Provider>;
}

// Custom hook to use the learning progress context
export function useLearningProgress() {
  const context = useContext(LearningProgressContext);
  if (context === undefined) {
    throw new Error('useLearningProgress must be used within a LearningProgressProvider');
  }
  return context;
}
