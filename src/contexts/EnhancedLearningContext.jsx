import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const EnhancedLearningContext = createContext();

export function EnhancedLearningProvider({ children }) {
  const { user, profile } = useAuth();
  const [modules, setModules] = useState([]);
  const [categories, setCategories] = useState([]);
  const [difficulties, setDifficulties] = useState([]);
  const [userProgress, setUserProgress] = useState({});
  const [learningPaths, setLearningPaths] = useState([]);
  const [userPathProgress, setUserPathProgress] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch all learning modules based on user's subscription tier
  useEffect(() => {
    const fetchModules = async () => {
      try {
        setLoading(true);
        
        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('learning_module_categories')
          .select('*')
          .order('display_order', { ascending: true });
          
        if (categoriesError) throw categoriesError;
        
        // Fetch difficulties
        const { data: difficultiesData, error: difficultiesError } = await supabase
          .from('learning_module_difficulty_levels')
          .select('*')
          .order('display_order', { ascending: true });
          
        if (difficultiesError) throw difficultiesError;
        
        // Fetch modules
        let query = supabase
          .from('learning_modules')
          .select(`
            id,
            title,
            description,
            category_id,
            difficulty_id,
            type_id,
            estimated_time,
            is_premium,
            is_business,
            created_at,
            category:learning_module_categories(name),
            difficulty:learning_module_difficulty_levels(name),
            type:learning_module_types(name),
            author:profiles(username)
          `)
          .eq('is_active', true);
        
        const { data: modulesData, error: modulesError } = await query;
        
        if (modulesError) throw modulesError;
        
        // Fetch learning paths
        const { data: pathsData, error: pathsError } = await supabase
          .from('learning_paths')
          .select(`
            id,
            title,
            description,
            category_id,
            is_premium,
            is_business,
            created_at,
            category:learning_module_categories(name),
            modules:learning_path_modules(
              module_id,
              display_order
            )
          `)
          .eq('is_active', true);
          
        if (pathsError) throw pathsError;
        
        setCategories(categoriesData);
        setDifficulties(difficultiesData);
        setModules(modulesData);
        setLearningPaths(pathsData);
      } catch (error) {
        console.error('Error fetching learning modules:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchModules();
  }, []);
  
  // Fetch user's module progress
  useEffect(() => {
    if (!user) {
      setUserProgress({});
      setUserPathProgress({});
      return;
    }
    
    const fetchUserProgress = async () => {
      try {
        setLoading(true);
        
        // Fetch module progress
        const { data: progressData, error: progressError } = await supabase
          .from('learning_module_progress')
          .select(`
            id,
            module_id,
            progress,
            last_activity,
            completed,
            completed_at
          `)
          .eq('user_id', user.id);
          
        if (progressError) throw progressError;
        
        // Fetch path progress
        const { data: pathProgressData, error: pathProgressError } = await supabase
          .from('learning_path_progress')
          .select(`
            id,
            path_id,
            progress,
            last_activity,
            completed,
            completed_at
          `)
          .eq('user_id', user.id);
          
        if (pathProgressError) throw pathProgressError;
        
        // Convert to object with module_id/path_id as key
        const progressObj = {};
        progressData.forEach(item => {
          progressObj[item.module_id] = item;
        });
        
        const pathProgressObj = {};
        pathProgressData.forEach(item => {
          pathProgressObj[item.path_id] = item;
        });
        
        setUserProgress(progressObj);
        setUserPathProgress(pathProgressObj);
      } catch (error) {
        console.error('Error fetching user progress:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserProgress();
    
    // Set up real-time subscription for user's module progress
    const progressSubscription = supabase
      .channel(`user-module-progress-${user.id}`)
      .on('INSERT', (payload) => {
        setUserProgress(prev => ({
          ...prev,
          [payload.new.module_id]: payload.new
        }));
      })
      .on('UPDATE', (payload) => {
        setUserProgress(prev => ({
          ...prev,
          [payload.new.module_id]: payload.new
        }));
      })
      .subscribe();
      
    // Set up real-time subscription for user's path progress
    const pathProgressSubscription = supabase
      .channel(`user-path-progress-${user.id}`)
      .on('INSERT', (payload) => {
        setUserPathProgress(prev => ({
          ...prev,
          [payload.new.path_id]: payload.new
        }));
      })
      .on('UPDATE', (payload) => {
        setUserPathProgress(prev => ({
          ...prev,
          [payload.new.path_id]: payload.new
        }));
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(progressSubscription);
      supabase.removeChannel(pathProgressSubscription);
    };
  }, [user]);
  
  // Get a single module by ID
  const getModuleById = async (id) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('learning_modules')
        .select(`
          id,
          title,
          description,
          category_id,
          difficulty_id,
          type_id,
          estimated_time,
          is_premium,
          is_business,
          created_at,
          category:learning_module_categories(name),
          difficulty:learning_module_difficulty_levels(name),
          type:learning_module_types(name),
          author:profiles(username),
          content:learning_module_content(*),
          sections:learning_module_sections(
            id,
            title,
            description,
            display_order,
            estimated_time,
            units:learning_module_units(
              id,
              title,
              type,
              content,
              display_order,
              estimated_time
            )
          ),
          resources:learning_module_resources(*)
        `)
        .eq('id', id)
        .single();
        
      if (error) throw error;
      
      // Sort sections by display_order
      data.sections.sort((a, b) => a.display_order - b.display_order);
      
      // Sort units by display_order
      data.sections.forEach(section => {
        section.units.sort((a, b) => a.display_order - b.display_order);
      });
      
      return data;
    } catch (error) {
      console.error('Error fetching module:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Get a single learning path by ID
  const getLearningPathById = async (id) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('learning_paths')
        .select(`
          id,
          title,
          description,
          category_id,
          is_premium,
          is_business,
          created_at,
          category:learning_module_categories(name),
          author:profiles(username),
          modules:learning_path_modules!inner(
            module_id,
            display_order,
            module:learning_modules(
              id,
              title,
              description,
              category_id,
              difficulty_id,
              estimated_time,
              is_premium,
              is_business,
              category:learning_module_categories(name),
              difficulty:learning_module_difficulty_levels(name)
            )
          )
        `)
        .eq('id', id)
        .single();
        
      if (error) throw error;
      
      // Sort modules by display_order
      data.modules.sort((a, b) => a.display_order - b.display_order);
      
      return data;
    } catch (error) {
      console.error('Error fetching learning path:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Update user's progress on a module
  const updateModuleProgress = async (moduleId, progress, completed = false) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to update progress');
      
      // Check if progress record exists
      const existingProgress = userProgress[moduleId];
      
      if (existingProgress) {
        // Update existing progress
        const { data, error } = await supabase
          .from('learning_module_progress')
          .update({
            progress,
            completed,
            completed_at: completed ? new Date() : null,
            last_activity: new Date()
          })
          .eq('id', existingProgress.id)
          .select()
          .single();
          
        if (error) throw error;
        
        setUserProgress(prev => ({
          ...prev,
          [moduleId]: data
        }));
        
        return data;
      } else {
        // Create new progress record
        const { data, error } = await supabase
          .from('learning_module_progress')
          .insert([{
            module_id: moduleId,
            user_id: user.id,
            progress,
            completed,
            completed_at: completed ? new Date() : null,
            last_activity: new Date()
          }])
          .select()
          .single();
          
        if (error) throw error;
        
        setUserProgress(prev => ({
          ...prev,
          [moduleId]: data
        }));
        
        return data;
      }
    } catch (error) {
      console.error('Error updating progress:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Update user's progress on a unit
  const updateUnitProgress = async (unitId, status, progress, completed = false) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to update progress');
      
      // Check if progress record exists
      const { data: existingProgress, error: existingError } = await supabase
        .from('learning_module_unit_progress')
        .select('id')
        .eq('user_id', user.id)
        .eq('unit_id', unitId)
        .single();
      
      if (existingError && existingError.code !== 'PGRST116') throw existingError;
      
      if (existingProgress) {
        // Update existing progress
        const { data, error } = await supabase
          .from('learning_module_unit_progress')
          .update({
            status,
            progress,
            completed,
            completed_at: completed ? new Date() : null,
            last_activity: new Date()
          })
          .eq('id', existingProgress.id)
          .select()
          .single();
          
        if (error) throw error;
        
        return data;
      } else {
        // Create new progress record
        const { data, error } = await supabase
          .from('learning_module_unit_progress')
          .insert([{
            unit_id: unitId,
            user_id: user.id,
            status,
            progress,
            completed,
            completed_at: completed ? new Date() : null,
            last_activity: new Date()
          }])
          .select()
          .single();
          
        if (error) throw error;
        
        return data;
      }
    } catch (error) {
      console.error('Error updating unit progress:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Submit a quiz attempt
  const submitQuizAttempt = async (unitId, score, maxScore, answers) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to submit a quiz');
      
      // Record quiz attempt
      const { data, error } = await supabase
        .from('learning_module_quiz_attempts')
        .insert([{
          unit_id: unitId,
          user_id: user.id,
          score,
          max_score: maxScore,
          answers,
          completed_at: new Date()
        }])
        .select()
        .single();
        
      if (error) throw error;
      
      // Update unit progress
      const isCompleted = score >= (maxScore * 0.7); // 70% to pass
      await updateUnitProgress(unitId, 'completed', 100, isCompleted);
      
      return data;
    } catch (error) {
      console.error('Error submitting quiz:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Submit an exercise
  const submitExercise = async (unitId, submission) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to submit an exercise');
      
      // Get unit details to validate submission
      const { data: unit, error: unitError } = await supabase
        .from('learning_module_units')
        .select('content')
        .eq('id', unitId)
        .single();
        
      if (unitError) throw unitError;
      
      // Validate submission (simple string comparison for now)
      const isCorrect = unit.content.solution === submission;
      
      // Record exercise submission
      const { data, error } = await supabase
        .from('learning_module_exercise_submissions')
        .insert([{
          unit_id: unitId,
          user_id: user.id,
          submission,
          is_correct: isCorrect,
          feedback: isCorrect ? 'Correct!' : 'Incorrect. Try again.'
        }])
        .select()
        .single();
        
      if (error) throw error;
      
      // Update unit progress if correct
      if (isCorrect) {
        await updateUnitProgress(unitId, 'completed', 100, true);
      } else {
        await updateUnitProgress(unitId, 'in_progress', 50, false);
      }
      
      return {
        ...data,
        isCorrect
      };
    } catch (error) {
      console.error('Error submitting exercise:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Rate a module
  const rateModule = async (moduleId, rating, feedback = '') => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to rate a module');
      
      // Check if user has already rated this module
      const { data: existingRating, error: existingError } = await supabase
        .from('learning_module_ratings')
        .select('id')
        .eq('user_id', user.id)
        .eq('module_id', moduleId)
        .single();
      
      if (existingError && existingError.code !== 'PGRST116') throw existingError;
      
      if (existingRating) {
        // Update existing rating
        const { data, error } = await supabase
          .from('learning_module_ratings')
          .update({
            rating,
            feedback
          })
          .eq('id', existingRating.id)
          .select()
          .single();
          
        if (error) throw error;
        
        return data;
      } else {
        // Create new rating
        const { data, error } = await supabase
          .from('learning_module_ratings')
          .insert([{
            module_id: moduleId,
            user_id: user.id,
            rating,
            feedback
          }])
          .select()
          .single();
          
        if (error) throw error;
        
        return data;
      }
    } catch (error) {
      console.error('Error rating module:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Add a comment to a module
  const addModuleComment = async (moduleId, comment, parentId = null) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to comment');
      
      const { data, error } = await supabase
        .from('learning_module_comments')
        .insert([{
          module_id: moduleId,
          user_id: user.id,
          parent_id: parentId,
          comment
        }])
        .select()
        .single();
        
      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Error adding comment:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Get comments for a module
  const getModuleComments = async (moduleId) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('learning_module_comments')
        .select(`
          id,
          comment,
          parent_id,
          created_at,
          user:profiles(id, username, avatar_url)
        `)
        .eq('module_id', moduleId)
        .eq('is_approved', true)
        .order('created_at', { ascending: false });
        
      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Error fetching comments:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Check if a module is completed by the user
  const isModuleCompleted = (moduleId) => {
    return userProgress[moduleId]?.completed || false;
  };
  
  // Get user's progress on a module
  const getModuleProgress = (moduleId) => {
    return userProgress[moduleId]?.progress || 0;
  };
  
  // Check if a learning path is completed by the user
  const isPathCompleted = (pathId) => {
    return userPathProgress[pathId]?.completed || false;
  };
  
  // Get user's progress on a learning path
  const getPathProgress = (pathId) => {
    return userPathProgress[pathId]?.progress || 0;
  };
  
  // Get filtered modules
  const getFilteredModules = (filters = {}) => {
    let filteredModules = [...modules];
    
    // Filter by category
    if (filters.category) {
      filteredModules = filteredModules.filter(
        module => module.category.name === filters.category
      );
    }
    
    // Filter by difficulty
    if (filters.difficulty) {
      filteredModules = filteredModules.filter(
        module => module.difficulty.name === filters.difficulty
      );
    }
    
    // Filter by search query
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredModules = filteredModules.filter(
        module => 
          module.title.toLowerCase().includes(searchLower) ||
          module.description.toLowerCase().includes(searchLower)
      );
    }
    
    // Filter by subscription tier
    if (!profile || profile.subscription_tier === 'free') {
      filteredModules = filteredModules.filter(
        module => !module.is_premium && !module.is_business
      );
    } else if (profile.subscription_tier === 'premium') {
      filteredModules = filteredModules.filter(
        module => !module.is_business
      );
    }
    
    // Filter by completion status
    if (filters.completed === true) {
      filteredModules = filteredModules.filter(
        module => isModuleCompleted(module.id)
      );
    } else if (filters.completed === false) {
      filteredModules = filteredModules.filter(
        module => !isModuleCompleted(module.id)
      );
    }
    
    // Filter by type
    if (filters.type) {
      filteredModules = filteredModules.filter(
        module => module.type.name === filters.type
      );
    }
    
    return filteredModules;
  };
  
  // Get filtered learning paths
  const getFilteredLearningPaths = (filters = {}) => {
    let filteredPaths = [...learningPaths];
    
    // Filter by category
    if (filters.category) {
      filteredPaths = filteredPaths.filter(
        path => path.category.name === filters.category
      );
    }
    
    // Filter by search query
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredPaths = filteredPaths.filter(
        path => 
          path.title.toLowerCase().includes(searchLower) ||
          path.description.toLowerCase().includes(searchLower)
      );
    }
    
    // Filter by subscription tier
    if (!profile || profile.subscription_tier === 'free') {
      filteredPaths = filteredPaths.filter(
        path => !path.is_premium && !path.is_business
      );
    } else if (profile.subscription_tier === 'premium') {
      filteredPaths = filteredPaths.filter(
        path => !path.is_business
      );
    }
    
    // Filter by completion status
    if (filters.completed === true) {
      filteredPaths = filteredPaths.filter(
        path => isPathCompleted(path.id)
      );
    } else if (filters.completed === false) {
      filteredPaths = filteredPaths.filter(
        path => !isPathCompleted(path.id)
      );
    }
    
    return filteredPaths;
  };
  
  // Get recommended modules for the user
  const getRecommendedModules = async () => {
    try {
      if (!user) return [];
      
      const { data, error } = await supabase.rpc('get_recommended_learning_modules', {
        user_id: user.id,
        limit_count: 5
      });
      
      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Error getting recommended modules:', error);
      setError(error.message);
      return [];
    }
  };
  
  // Get user's learning statistics
  const getUserLearningStats = async () => {
    try {
      if (!user) return null;
      
      const { data, error } = await supabase.rpc('get_user_learning_progress', {
        user_id: user.id
      });
      
      if (error) throw error;
      
      return data[0];
    } catch (error) {
      console.error('Error getting user learning stats:', error);
      setError(error.message);
      return null;
    }
  };

  // Context value
  const value = {
    modules,
    categories,
    difficulties,
    learningPaths,
    userProgress,
    userPathProgress,
    loading,
    error,
    getModuleById,
    getLearningPathById,
    updateModuleProgress,
    updateUnitProgress,
    submitQuizAttempt,
    submitExercise,
    rateModule,
    addModuleComment,
    getModuleComments,
    isModuleCompleted,
    getModuleProgress,
    isPathCompleted,
    getPathProgress,
    getFilteredModules,
    getFilteredLearningPaths,
    getRecommendedModules,
    getUserLearningStats
  };

  return <EnhancedLearningContext.Provider value={value}>{children}</EnhancedLearningContext.Provider>;
}

// Custom hook to use the learning context
export function useEnhancedLearning() {
  const context = useContext(EnhancedLearningContext);
  if (context === undefined) {
    throw new Error('useEnhancedLearning must be used within an EnhancedLearningProvider');
  }
  return context;
}
