import React, { createContext, useContext, useState, useEffect } from 'react';

// Create the context
const LanguageContext = createContext();

// Translations for English and Arabic only
const translations = {
  en: {
    welcome: 'Welcome to CyberForce Competition Training',
    subtitle: 'Prepare for the Department of Energy\'s CyberForce Competition with realistic cybersecurity scenarios and challenges.',
    getStarted: 'Get Started',
    exploreChallenge: 'Explore Challenges',
    join: 'Join',
    students: 'cybersecurity students',
    challenges: 'Challenges',
    learningModules: 'Learning Modules',
    users: 'Users',
    support: '24/7 Support',
    featuredChallenge: 'Featured Challenge',
    testSkills: 'Test your skills with our latest cybersecurity challenge',
    industrial: 'Industrial Control System Security',
    intermediate: 'Intermediate',
    secureIndustrial: 'Learn to secure industrial control systems and SCADA networks. This challenge will test your knowledge of:',
    plcSecurity: 'PLC Security',
    scadaProtection: 'SCADA Network Protection',
    icsProtocol: 'ICS Protocol Vulnerabilities',
    airGapped: 'Air-gapped Network Defense',
    flagFormat: 'Flag Format',
    tryChallenge: 'Try This Challenge',
    completions: 'completions',
    viewAll: 'View All Challenges',
    whyParticipate: 'Why Participate in CyberForce?',
    opportunities: 'The Department of Energy\'s CyberForce Competition offers unique opportunities for cybersecurity students.',
    real: 'Real',
    industry: 'Industry Scenarios',
    practiceDefending: 'Practice defending critical infrastructure systems against realistic cyber threats.',
    career: 'Career',
    careerOpportunities: 'Opportunities',
    connectEmployers: 'Connect with potential employers and showcase your skills to industry professionals.',
    team: 'Team',
    collaboration: 'Collaboration',
    workWithPeers: 'Work with peers to solve complex security challenges and develop teamwork skills.',
    handson: 'Hands-on',
    experience: 'Experience',
    gainPractical: 'Gain practical experience with industrial control systems and critical infrastructure security.',
    whyTrain: 'Why Train With Us?',
    platformOffers: 'Our platform offers specialized training designed specifically for the CyberForce Competition.',
    competitionFocused: 'Competition-Focused',
    challengesDesigned: 'Challenges and scenarios designed specifically to prepare you for the CyberForce Competition format.',
    icsTraining: 'ICS/SCADA Training',
    specializedModules: 'Specialized modules on industrial control systems and SCADA networks that are central to the competition.',
    teamCollaboration: 'Team Collaboration',
    toolsExercises: 'Tools and exercises to help your team practice together and develop effective collaboration strategies.',
    readyPrepare: 'Ready to Prepare for CyberForce?',
    joinThousands: 'Join thousands of students training for the Department of Energy\'s CyberForce Competition.',
    signUp: 'Sign Up Now',
    viewPricing: 'View Pricing',
  },
  ar: {
    welcome: 'مرحبًا بك في تدريب مسابقة سايبر فورس',
    subtitle: 'استعد لمسابقة سايبر فورس التابعة لوزارة الطاقة من خلال سيناريوهات وتحديات واقعية للأمن السيبراني.',
    getStarted: 'ابدأ الآن',
    exploreChallenge: 'استكشف التحديات',
    join: 'انضم إلى',
    students: 'طلاب الأمن السيبراني',
    challenges: 'التحديات',
    learningModules: 'وحدات التعلم',
    users: 'المستخدمين',
    support: 'دعم على مدار الساعة',
    featuredChallenge: 'التحدي المميز',
    testSkills: 'اختبر مهاراتك مع أحدث تحدي للأمن السيبراني',
    industrial: 'أمن نظم التحكم الصناعية',
    intermediate: 'متوسط',
    secureIndustrial: 'تعلم كيفية تأمين أنظمة التحكم الصناعية وشبكات سكادا. سيختبر هذا التحدي معرفتك بـ:',
    plcSecurity: 'أمن PLC',
    scadaProtection: 'حماية شبكة سكادا',
    icsProtocol: 'ثغرات بروتوكول ICS',
    airGapped: 'دفاع الشبكات المعزولة',
    flagFormat: 'تنسيق العلم',
    tryChallenge: 'جرب هذا التحدي',
    completions: 'إكمال',
    viewAll: 'عرض جميع التحديات',
    whyParticipate: 'لماذا تشارك في سايبر فورس؟',
    opportunities: 'توفر مسابقة سايبر فورس التابعة لوزارة الطاقة فرصًا فريدة لطلاب الأمن السيبراني.',
    real: 'واقعي',
    industry: 'سيناريوهات الصناعة',
    practiceDefending: 'تدرب على الدفاع عن أنظمة البنية التحتية الحيوية ضد التهديدات السيبرانية الواقعية.',
    career: 'مهنة',
    careerOpportunities: 'فرص',
    connectEmployers: 'تواصل مع أصحاب العمل المحتملين وأظهر مهاراتك للمتخصصين في الصناعة.',
    team: 'فريق',
    collaboration: 'تعاون',
    workWithPeers: 'اعمل مع زملائك لحل تحديات الأمن المعقدة وتطوير مهارات العمل الجماعي.',
    handson: 'عملي',
    experience: 'خبرة',
    gainPractical: 'اكتسب خبرة عملية في أنظمة التحكم الصناعية وأمن البنية التحتية الحيوية.',
    whyTrain: 'لماذا تتدرب معنا؟',
    platformOffers: 'توفر منصتنا تدريبًا متخصصًا مصممًا خصيصًا لمسابقة سايبر فورس.',
    competitionFocused: 'التركيز على المسابقة',
    challengesDesigned: 'تحديات وسيناريوهات مصممة خصيصًا لإعدادك لتنسيق مسابقة سايبر فورس.',
    icsTraining: 'تدريب ICS/SCADA',
    specializedModules: 'وحدات متخصصة في أنظمة التحكم الصناعية وشبكات سكادا التي تعتبر محورية للمسابقة.',
    teamCollaboration: 'تعاون الفريق',
    toolsExercises: 'أدوات وتمارين لمساعدة فريقك على التدرب معًا وتطوير استراتيجيات تعاون فعالة.',
    readyPrepare: 'هل أنت مستعد للتحضير لسايبر فورس؟',
    joinThousands: 'انضم إلى آلاف الطلاب الذين يتدربون لمسابقة سايبر فورس التابعة لوزارة الطاقة.',
    signUp: 'سجل الآن',
    viewPricing: 'عرض الأسعار',
  }
};

export const LanguageProvider = ({ children }) => {
  // Check if user has a language preference stored in localStorage
  const [language, setLanguage] = useState(() => {
    const savedLanguage = localStorage.getItem('language');
    // If no preference is stored, use browser language (only if it's English or Arabic) or default to English
    if (savedLanguage === null) {
      const browserLang = navigator.language.split('-')[0];
      // Only allow 'en' or 'ar'
      return browserLang === 'ar' ? 'ar' : 'en';
    }
    // Ensure saved language is either 'en' or 'ar'
    return savedLanguage === 'ar' ? 'ar' : 'en';
  });

  // Get translations for the current language
  const getTranslation = (key) => {
    if (!translations[language] || !translations[language][key]) {
      // Fallback to English if translation is missing
      return translations.en[key] || key;
    }
    return translations[language][key];
  };

  // Change language (only allow 'en' or 'ar')
  const changeLanguage = (newLanguage) => {
    // Ensure we only set language to 'en' or 'ar'
    setLanguage(newLanguage === 'ar' ? 'ar' : 'en');
  };

  // Update localStorage when language changes
  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, t: getTranslation }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
