import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';

// Create context
const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize auth state
  useEffect(() => {
    // Get initial session
    const initAuth = async () => {
      try {
        setLoading(true);

        // Check if we're logged in
        const isLoggedIn = localStorage.getItem('supabase.auth.token') !== null;

        // Check for user data
        const mockUserData = localStorage.getItem('supabase.auth.user');
        if (mockUserData) {
          const mockUser = JSON.parse(mockUserData);
          setUser(mockUser);

          // If logged in, try to fetch profile from database
          if (isLoggedIn) {
            try {
              // Try to fetch real profile data from Supabase
              const { data: profileData, error: profileError } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', mockUser.id)
                .single();

              if (profileData && !profileError) {
                // We have real profile data from the database
                setProfile(profileData);
                // Also save to localStorage as a backup
                localStorage.setItem('user_profile', JSON.stringify(profileData));
              } else {
                // Fallback to stored profile or create a mock one
                const storedProfile = localStorage.getItem('user_profile');
                if (storedProfile) {
                  setProfile(JSON.parse(storedProfile));
                } else {
                  // Create a mock profile for the user
                  const mockProfile = {
                    id: mockUser.id,
                    username: mockUser.user_metadata?.username || 'user',
                    full_name: mockUser.user_metadata?.full_name || 'Demo User',
                    avatar_url: null,
                    subscription_tier: 'free',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  };
                  setProfile(mockProfile);
                  localStorage.setItem('user_profile', JSON.stringify(mockProfile));
                }
              }
            } catch (err) {
              console.error('Error fetching profile:', err);
              // Fallback to mock profile
              const mockProfile = {
                id: mockUser.id,
                username: mockUser.user_metadata?.username || 'user',
                full_name: mockUser.user_metadata?.full_name || 'Demo User',
                avatar_url: null,
                subscription_tier: 'free',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              };
              setProfile(mockProfile);
            }
          } else {
            // Not logged in, use mock profile
            const mockProfile = {
              id: mockUser.id,
              username: mockUser.user_metadata?.username || 'user',
              full_name: mockUser.user_metadata?.full_name || 'Demo User',
              avatar_url: null,
              subscription_tier: 'free',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
            setProfile(mockProfile);
          }

          setLoading(false);
          return;
        }

        // Check for existing session
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          setUser(session.user);

          // Fetch user profile
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (profileError) throw profileError;
          setProfile(profileData);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    initAuth();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          setUser(session.user);

          // Fetch user profile
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (!profileError) {
            setProfile(profileData);
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setProfile(null);
        }
      }
    );

    // Cleanup subscription
    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  // Sign up with email and password
  const signUp = async (email, password, userData = {}) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.fullName || '',
            username: userData.username || email.split('@')[0],
          }
        }
      });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error signing up:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign in with email and password
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error signing in:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.auth.signOut();

      if (error) throw error;
    } catch (error) {
      console.error('Error signing out:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update user profile
  const updateProfile = async (profileData) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('You must be logged in to update your profile');

      const { data, error } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;

      setProfile(data);
      return data;
    } catch (error) {
      console.error('Error updating profile:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update subscription tier
  const updateSubscription = async (tier) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('You must be logged in to update your subscription');

      const { data, error } = await supabase
        .from('profiles')
        .update({ subscription_tier: tier })
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;

      // Record subscription change in history
      await supabase
        .from('subscription_history')
        .insert([{
          user_id: user.id,
          tier,
          changed_at: new Date()
        }]);

      setProfile(data);
      return data;
    } catch (error) {
      console.error('Error updating subscription:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Check if user has access to premium content
  const hasPremiumAccess = () => {
    return profile?.subscription_tier === 'premium' || profile?.subscription_tier === 'business';
  };

  // Check if user has access to business content
  const hasBusinessAccess = () => {
    return profile?.subscription_tier === 'business';
  };

  // Context value
  const value = {
    user,
    profile,
    loading,
    error,
    signUp,
    signIn,
    signOut,
    updateProfile,
    updateSubscription,
    hasPremiumAccess,
    hasBusinessAccess
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
