import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const AdminDashboardContext = createContext();

// Custom hook to use the admin dashboard context
export const useAdminDashboard = () => {
  const context = useContext(AdminDashboardContext);
  if (!context) {
    throw new Error('useAdminDashboard must be used within an AdminDashboardProvider');
  }
  return context;
};

export const AdminDashboardProvider = ({ children }) => {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  
  // Dashboard data
  const [stats, setStats] = useState({
    totalUsers: 0,
    freeUsers: 0,
    premiumUsers: 0,
    businessUsers: 0,
    totalRevenue: 0,
    activeSubscriptions: 0,
    totalChallenges: 0,
    totalModules: 0,
    totalNotifications: 0,
    pendingApprovals: 0,
    userGrowth: 0,
    revenueGrowth: 0,
    activeUsers: 0
  });
  
  const [recentUsers, setRecentUsers] = useState([]);
  const [recentSubscriptions, setRecentSubscriptions] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [pendingContent, setPendingContent] = useState([]);
  const [userList, setUserList] = useState([]);
  const [challengeList, setChallengeList] = useState([]);
  const [moduleList, setModuleList] = useState([]);
  
  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setIsAdmin(false);
        setIsSuperAdmin(false);
        setLoading(false);
        return;
      }

      try {
        // For testing purposes, we'll check user_metadata directly
        // In production, use the database query below
        const isAdminUser = user.user_metadata?.is_admin === true;
        const isSuperAdminUser = user.user_metadata?.is_super_admin === true;

        setIsAdmin(isAdminUser || isSuperAdminUser);
        setIsSuperAdmin(isSuperAdminUser);

        // Database query for production
        /*
        const { data, error } = await supabase
          .from('profiles')
          .select('role_id, user_roles(name)')
          .eq('id', user.id)
          .single();

        if (error) throw error;

        const isAdminUser = data?.user_roles?.name === 'admin' || data?.user_roles?.name === 'super_admin';
        const isSuperAdminUser = data?.user_roles?.name === 'super_admin';
        setIsAdmin(isAdminUser);
        setIsSuperAdmin(isSuperAdminUser);
        */

        if (!isAdminUser && !isSuperAdminUser) {
          setError('You do not have permission to access this page');
        } else {
          // If user is admin, fetch dashboard data
          await fetchDashboardData(false);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, [user]);

  // Fetch dashboard data
  const fetchDashboardData = useCallback(async (showRefreshing = true) => {
    if (!isAdmin) return;
    
    try {
      if (showRefreshing) {
        setRefreshing(true);
      }
      setLoading(true);

      // Fetch user stats
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('subscription_tier, count')
        .group('subscription_tier');

      if (userError) throw userError;

      // Fetch subscription stats
      const { data: subData, error: subError } = await supabase
        .from('subscription_tracking')
        .select('status, count')
        .eq('status', 'active')
        .group('status');

      if (subError) throw subError;

      // Fetch revenue stats
      const { data: revenueData, error: revenueError } = await supabase
        .from('subscription_tracking')
        .select('payment_id, amount, created_at')
        .not('payment_id', 'is', null);

      if (revenueError) throw revenueError;

      // Fetch content stats
      const { data: challengeData, error: challengeError } = await supabase
        .from('challenges')
        .select('count');

      if (challengeError) throw challengeError;

      const { data: moduleData, error: moduleError } = await supabase
        .from('learning_modules')
        .select('count');

      if (moduleError) throw moduleError;

      // Fetch recent users
      const { data: recentUserData, error: recentUserError } = await supabase
        .from('profiles')
        .select('id, username, full_name, avatar_url, subscription_tier, created_at')
        .order('created_at', { ascending: false })
        .limit(5);

      if (recentUserError) throw recentUserError;

      // Fetch recent subscriptions
      const { data: recentSubData, error: recentSubError } = await supabase
        .from('subscription_tracking')
        .select(`
          id,
          user_id,
          start_date,
          end_date,
          status,
          amount,
          profiles:user_id (username, full_name, avatar_url),
          subscription_plans (name, price)
        `)
        .order('start_date', { ascending: false })
        .limit(5);

      if (recentSubError) throw recentSubError;

      // Fetch notifications
      const { data: notifData, error: notifError } = await supabase
        .from('notifications')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      if (notifError) throw notifError;

      // Fetch recent user activities
      const { data: activityData, error: activityError } = await supabase
        .from('user_activity')
        .select(`
          id,
          user_id,
          activity_type,
          activity_data,
          created_at,
          profiles:user_id (username, avatar_url)
        `)
        .order('created_at', { ascending: false })
        .limit(10);

      if (activityError) throw activityError;

      // Process user stats
      const totalUsers = userData.reduce((sum, item) => sum + item.count, 0);
      const freeUsers = userData.find(item => item.subscription_tier === 'free')?.count || 0;
      const premiumUsers = userData.find(item => item.subscription_tier === 'premium')?.count || 0;
      const businessUsers = userData.find(item => item.subscription_tier === 'business')?.count || 0;

      // Process subscription stats
      const activeSubscriptions = subData[0]?.count || 0;

      // Calculate total revenue
      const totalRevenue = revenueData.reduce((sum, item) => sum + (item.amount || 0), 0);

      // Calculate growth metrics (mock data for now)
      // In a real implementation, we would compare with previous period
      const userGrowth = Math.round((Math.random() * 20) - 5); // -5% to +15%
      const revenueGrowth = Math.round((Math.random() * 30) - 5); // -5% to +25%
      
      // Calculate active users (mock data)
      const activeUsers = Math.round(totalUsers * (0.3 + (Math.random() * 0.4))); // 30-70% of total users

      // Set stats
      setStats({
        totalUsers,
        freeUsers,
        premiumUsers,
        businessUsers,
        totalRevenue,
        activeSubscriptions,
        totalChallenges: challengeData[0]?.count || 0,
        totalModules: moduleData[0]?.count || 0,
        totalNotifications: notifData.length || 0,
        pendingApprovals: Math.floor(Math.random() * 10), // Mock data
        userGrowth,
        revenueGrowth,
        activeUsers
      });

      // Set recent data
      setRecentUsers(recentUserData || []);
      setRecentSubscriptions(recentSubData || []);
      setNotifications(notifData || []);
      setRecentActivities(activityData || []);

      // Update last updated timestamp
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false);
      if (showRefreshing) {
        setTimeout(() => setRefreshing(false), 500); // Show refresh animation for at least 500ms
      }
    }
  }, [isAdmin]);

  // Fetch user list
  const fetchUserList = useCallback(async () => {
    if (!isAdmin) return;
    
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, full_name, avatar_url, subscription_tier, points, challenges_completed, created_at')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      setUserList(data || []);
    } catch (err) {
      console.error('Error fetching user list:', err);
      setError('Failed to load users. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [isAdmin]);

  // Fetch challenge list
  const fetchChallengeList = useCallback(async () => {
    if (!isAdmin) return;
    
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('challenges')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      setChallengeList(data || []);
    } catch (err) {
      console.error('Error fetching challenge list:', err);
      setError('Failed to load challenges. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [isAdmin]);

  // Fetch module list
  const fetchModuleList = useCallback(async () => {
    if (!isAdmin) return;
    
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('learning_modules')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      setModuleList(data || []);
    } catch (err) {
      console.error('Error fetching module list:', err);
      setError('Failed to load modules. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [isAdmin]);

  // Update user subscription
  const updateUserSubscription = useCallback(async (userId, tier) => {
    if (!isAdmin) return { success: false, error: 'Not authorized' };
    
    try {
      setLoading(true);
      
      // Call the RPC function to update the user's subscription tier
      const { data, error } = await supabase.rpc(
        'update_user_subscription_tier',
        { 
          p_user_id: userId,
          p_tier: tier
        }
      );
      
      if (error) throw error;
      
      // Update the user in the local state
      setUserList(prevUsers => 
        prevUsers.map(user => 
          user.id === userId 
            ? { ...user, subscription_tier: tier } 
            : user
        )
      );
      
      // Refresh dashboard data
      await fetchDashboardData(false);
      
      return { success: true };
    } catch (err) {
      console.error('Error updating subscription tier:', err);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [isAdmin, fetchDashboardData]);

  // Send notification
  const sendNotification = useCallback(async (title, message, targetAudience, priority, isScheduled, scheduledDate) => {
    if (!isAdmin) return { success: false, error: 'Not authorized' };
    
    try {
      setLoading(true);
      
      const notification = {
        title,
        message,
        target_audience: targetAudience,
        priority,
        is_scheduled: isScheduled,
        scheduled_date: isScheduled ? scheduledDate : null,
        status: isScheduled ? 'scheduled' : 'sent',
        created_by: user.id,
        created_at: new Date().toISOString()
      };
      
      const { data, error } = await supabase
        .from('notifications')
        .insert([notification])
        .select();
      
      if (error) throw error;
      
      // Add the new notification to the list
      setNotifications(prev => [data[0], ...prev]);
      
      // Refresh dashboard data
      await fetchDashboardData(false);
      
      return { success: true, notification: data[0] };
    } catch (err) {
      console.error('Error sending notification:', err);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [isAdmin, user, fetchDashboardData]);

  // Set up real-time subscription for updates
  useEffect(() => {
    if (!isAdmin) return;
    
    const subscription = supabase
      .channel('admin-dashboard-changes')
      .on('postgres_changes', { event: '*', schema: 'public' }, () => {
        // When any database change happens, refresh the data
        fetchDashboardData(false);
      })
      .subscribe();

    // Refresh data every 5 minutes
    const intervalId = setInterval(() => {
      fetchDashboardData(false);
    }, 5 * 60 * 1000);

    return () => {
      subscription.unsubscribe();
      clearInterval(intervalId);
    };
  }, [isAdmin, fetchDashboardData]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <AdminDashboardContext.Provider
      value={{
        // Auth state
        isAdmin,
        isSuperAdmin,
        
        // Data
        stats,
        recentUsers,
        recentSubscriptions,
        recentActivities,
        notifications,
        pendingContent,
        userList,
        challengeList,
        moduleList,
        
        // UI state
        loading,
        error,
        refreshing,
        lastUpdated,
        
        // Actions
        fetchDashboardData,
        fetchUserList,
        fetchChallengeList,
        fetchModuleList,
        updateUserSubscription,
        sendNotification,
        
        // Utilities
        formatCurrency,
        formatDate
      }}
    >
      {children}
    </AdminDashboardContext.Provider>
  );
};

export default AdminDashboardProvider;
