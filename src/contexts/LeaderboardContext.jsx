import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';

// Create context
const LeaderboardContext = createContext();

export function LeaderboardProvider({ children }) {
  const [leaderboard, setLeaderboard] = useState([]);
  const [timeFrame, setTimeFrame] = useState('all-time');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch leaderboard data based on time frame
  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        setLoading(true);
        
        let data;
        
        if (timeFrame === 'all-time') {
          // Get all-time leaderboard
          const { data: leaderboardData, error: leaderboardError } = await supabase
            .from('profiles')
            .select('id, username, full_name, avatar_url, points, challenges_completed')
            .order('points', { ascending: false })
            .limit(100);
            
          if (leaderboardError) throw leaderboardError;
          data = leaderboardData;
        } else {
          // Calculate start date based on time frame
          const now = new Date();
          let startDate;
          
          switch (timeFrame) {
            case 'daily':
              startDate = new Date(now.setHours(0, 0, 0, 0));
              break;
            case 'weekly':
              const day = now.getDay();
              startDate = new Date(now.setDate(now.getDate() - day));
              startDate.setHours(0, 0, 0, 0);
              break;
            case 'monthly':
              startDate = new Date(now.getFullYear(), now.getMonth(), 1);
              break;
            default:
              startDate = new Date(0); // Beginning of time
          }
          
          // Get completions within the time frame
          const { data: completionsData, error: completionsError } = await supabase
            .from('challenge_completions')
            .select(`
              user_id,
              user:profiles(id, username, full_name, avatar_url),
              score
            `)
            .gte('completed_at', startDate.toISOString())
            .order('score', { ascending: false });
            
          if (completionsError) throw completionsError;
          
          // Aggregate scores by user
          const userScores = {};
          completionsData.forEach(completion => {
            const userId = completion.user_id;
            if (!userScores[userId]) {
              userScores[userId] = {
                id: userId,
                username: completion.user.username,
                full_name: completion.user.full_name,
                avatar_url: completion.user.avatar_url,
                points: 0,
                challenges_completed: 0
              };
            }
            
            userScores[userId].points += completion.score;
            userScores[userId].challenges_completed += 1;
          });
          
          // Convert to array and sort by points
          data = Object.values(userScores).sort((a, b) => b.points - a.points);
        }
        
        // Add rank to each user
        const rankedData = data.map((user, index) => ({
          ...user,
          rank: index + 1
        }));
        
        setLeaderboard(rankedData);
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchLeaderboard();
    
    // Set up real-time subscription for profile updates
    const subscription = supabase
      .channel('public:profiles')
      .on('UPDATE', (payload) => {
        if (timeFrame === 'all-time') {
          setLeaderboard(prev => {
            // Find if user already exists in leaderboard
            const index = prev.findIndex(user => user.id === payload.new.id);
            
            if (index !== -1) {
              // Update existing user
              const updatedLeaderboard = [...prev];
              updatedLeaderboard[index] = {
                ...updatedLeaderboard[index],
                ...payload.new
              };
              
              // Re-sort and re-rank
              return updatedLeaderboard
                .sort((a, b) => b.points - a.points)
                .map((user, idx) => ({
                  ...user,
                  rank: idx + 1
                }));
            } else if (payload.new.points > 0) {
              // Add new user if they have points
              const updatedLeaderboard = [
                ...prev,
                {
                  ...payload.new,
                  rank: 0 // Temporary rank
                }
              ];
              
              // Re-sort and re-rank
              return updatedLeaderboard
                .sort((a, b) => b.points - a.points)
                .map((user, idx) => ({
                  ...user,
                  rank: idx + 1
                }));
            }
            
            return prev;
          });
        }
      })
      .subscribe();
      
    // Set up real-time subscription for challenge completions
    const completionsSubscription = supabase
      .channel('public:challenge_completions')
      .on('INSERT', (payload) => {
        if (timeFrame !== 'all-time') {
          // Refetch leaderboard for time-based leaderboards
          fetchLeaderboard();
        }
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(subscription);
      supabase.removeChannel(completionsSubscription);
    };
  }, [timeFrame]);
  
  // Change time frame
  const changeTimeFrame = (newTimeFrame) => {
    setTimeFrame(newTimeFrame);
  };
  
  // Search users in leaderboard
  const searchUsers = (query) => {
    if (!query) return leaderboard;
    
    const searchLower = query.toLowerCase();
    return leaderboard.filter(
      user => 
        user.username.toLowerCase().includes(searchLower) ||
        (user.full_name && user.full_name.toLowerCase().includes(searchLower))
    );
  };

  // Context value
  const value = {
    leaderboard,
    timeFrame,
    loading,
    error,
    changeTimeFrame,
    searchUsers
  };

  return <LeaderboardContext.Provider value={value}>{children}</LeaderboardContext.Provider>;
}

// Custom hook to use the leaderboard context
export function useLeaderboard() {
  const context = useContext(LeaderboardContext);
  if (context === undefined) {
    throw new Error('useLeaderboard must be used within a LeaderboardProvider');
  }
  return context;
}
