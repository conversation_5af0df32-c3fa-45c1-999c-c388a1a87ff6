import React, { createContext, useContext, useState } from 'react';

// Create a simplified subscription context
const SimpleSubscriptionContext = createContext();

// Subscription tiers
const SUBSCRIPTION_TIERS = {
  FREE: 'free',
  PREMIUM: 'premium'
};

// Features for each tier
const TIER_FEATURES = {
  [SUBSCRIPTION_TIERS.FREE]: {
    name: 'Free',
    price: 0,
    learnModules: 3,
    challenges: 5,
    startHack: 15,
    features: [
      'Access to limited learning content',
      '5 beginner challenges',
      '15 Start Hack challenges',
      'Basic wallet for purchasing coins',
      'Basic progress tracking',
      'No community access',
    ],
  },
  [SUBSCRIPTION_TIERS.PREMIUM]: {
    name: 'Premium',
    price: 15,
    currency: 'OMR',
    learnModules: 'All',
    challenges: 15,
    startHack: 'All',
    features: [
      'Full access to all learning modules',
      '15 premium challenges',
      'Complete access to all Start Hack features',
      'Advanced wallet with pre-purchase options',
      'Community access with expert support',
      'Access to real Linux boxes for practice',
      'Join up to 15 groups (8 members per group)',
      '30-day subscription with auto-renewal',
    ],
  },
};

// Provider component
export const SimpleSubscriptionProvider = ({ children }) => {
  const [subscriptionTier, setSubscriptionTier] = useState(SUBSCRIPTION_TIERS.FREE);
  const [userCoins, setUserCoins] = useState(100);

  // Check if user has access to a feature
  const hasAccess = (feature, count) => {
    if (feature === 'learnModules') {
      return count < TIER_FEATURES[subscriptionTier].learnModules;
    }
    if (feature === 'challenges') {
      return count < TIER_FEATURES[subscriptionTier].challenges;
    }
    return false;
  };

  // Upgrade subscription
  const upgradeSubscription = (tier) => {
    setSubscriptionTier(tier);
    return Promise.resolve(true);
  };

  // Add coins
  const addCoins = (amount) => {
    setUserCoins(prev => prev + amount);
    return Promise.resolve(true);
  };

  return (
    <SimpleSubscriptionContext.Provider
      value={{
        subscriptionTier,
        userCoins,
        tierFeatures: TIER_FEATURES,
        hasAccess,
        upgradeSubscription,
        addCoins,
        isFree: subscriptionTier === SUBSCRIPTION_TIERS.FREE,
        isPremium: subscriptionTier === SUBSCRIPTION_TIERS.PREMIUM,
      }}
    >
      {children}
    </SimpleSubscriptionContext.Provider>
  );
};

// Hook for using the subscription context
export const useSimpleSubscription = () => {
  const context = useContext(SimpleSubscriptionContext);
  if (!context) {
    throw new Error('useSimpleSubscription must be used within a SimpleSubscriptionProvider');
  }
  return context;
};

export { SUBSCRIPTION_TIERS, TIER_FEATURES };
