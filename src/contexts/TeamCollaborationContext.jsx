import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const TeamCollaborationContext = createContext();

export function TeamCollaborationProvider({ children }) {
  const { user, profile } = useAuth();
  const [teams, setTeams] = useState([]);
  const [userTeams, setUserTeams] = useState([]);
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch user's teams
  useEffect(() => {
    if (!user) {
      setUserTeams([]);
      setInvitations([]);
      return;
    }
    
    const fetchUserTeams = async () => {
      try {
        setLoading(true);
        
        // Fetch teams where user is a member
        const { data: teamData, error: teamError } = await supabase
          .from('team_members')
          .select(`
            team:enhanced_teams(
              id,
              name,
              description,
              logo_url,
              created_by,
              max_members,
              created_at
            ),
            role
          `)
          .eq('user_id', user.id);

        if (teamError) throw teamError;

        // Fetch pending invitations
        const { data: invitationData, error: invitationError } = await supabase
          .from('team_invitations')
          .select(`
            id,
            team_id,
            team:enhanced_teams(name, description),
            invited_by,
            inviter:profiles!team_invitations_invited_by_fkey(username),
            status,
            created_at,
            expires_at
          `)
          .eq('email', user.email)
          .eq('status', 'pending');

        if (invitationError) throw invitationError;

        // Process team data
        const processedTeams = await Promise.all(teamData.map(async (membership) => {
          // Get member count for each team
          const { data: memberCount, error: memberError } = await supabase
            .from('team_members')
            .select('count')
            .eq('team_id', membership.team.id);

          if (memberError) throw memberError;

          return {
            ...membership.team,
            role: membership.role,
            memberCount: memberCount[0]?.count || 0
          };
        }));

        setUserTeams(processedTeams);
        setInvitations(invitationData);
      } catch (error) {
        console.error('Error fetching teams:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUserTeams();
  }, [user]);

  // Create a new team
  const createTeam = async (teamData) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to create a team');
      
      // Check if user has reached team limit
      if (userTeams.length >= 15) {
        throw new Error('You have reached the maximum number of teams (15)');
      }
      
      // Create new team
      const { data: newTeam, error: teamError } = await supabase
        .from('enhanced_teams')
        .insert([{
          name: teamData.name,
          description: teamData.description,
          logo_url: teamData.logoUrl,
          created_by: user.id,
          max_members: 8 // Default max members
        }])
        .select()
        .single();

      if (teamError) throw teamError;

      // Add creator as team owner
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: newTeam.id,
          user_id: user.id,
          role: 'owner'
        }]);

      if (memberError) throw memberError;

      // Create general channel
      const { error: channelError } = await supabase
        .from('team_channels')
        .insert([{
          team_id: newTeam.id,
          name: 'General',
          description: 'General team discussion',
          created_by: user.id
        }]);

      if (channelError) throw channelError;

      // Add new team to state
      setUserTeams([...userTeams, {
        ...newTeam,
        role: 'owner',
        memberCount: 1
      }]);

      return newTeam;
    } catch (error) {
      console.error('Error creating team:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Invite user to team
  const inviteToTeam = async (teamId, email) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to invite users');
      
      // Check if user is admin or owner
      const team = userTeams.find(t => t.id === teamId);
      if (!team || (team.role !== 'admin' && team.role !== 'owner')) {
        throw new Error('You do not have permission to invite users to this team');
      }
      
      // Check if team has reached member limit
      if (team.memberCount >= team.max_members) {
        throw new Error(`Team has reached the maximum number of members (${team.max_members})`);
      }
      
      // Check if user is already a member
      const { data: existingMember, error: memberError } = await supabase
        .from('team_members')
        .select('id')
        .eq('team_id', teamId)
        .eq('user_id', (await supabase.from('profiles').select('id').eq('email', email).single()).data?.id)
        .maybeSingle();

      if (memberError) throw memberError;
      
      if (existingMember) {
        throw new Error('User is already a member of this team');
      }
      
      // Check if invitation already exists
      const { data: existingInvitation, error: invitationError } = await supabase
        .from('team_invitations')
        .select('id, status')
        .eq('team_id', teamId)
        .eq('email', email)
        .maybeSingle();

      if (invitationError) throw invitationError;
      
      if (existingInvitation) {
        if (existingInvitation.status === 'pending') {
          throw new Error('An invitation has already been sent to this email');
        } else {
          // Update existing invitation
          const { error: updateError } = await supabase
            .from('team_invitations')
            .update({
              status: 'pending',
              invited_by: user.id,
              created_at: new Date(),
              expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
            })
            .eq('id', existingInvitation.id);

          if (updateError) throw updateError;
        }
      } else {
        // Create new invitation
        const { error: createError } = await supabase
          .from('team_invitations')
          .insert([{
            team_id: teamId,
            email,
            invited_by: user.id
          }]);

        if (createError) throw createError;
      }

      return { success: true };
    } catch (error) {
      console.error('Error inviting user:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Accept team invitation
  const acceptInvitation = async (invitationId) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to accept an invitation');
      
      // Get invitation details
      const { data: invitation, error: invitationError } = await supabase
        .from('team_invitations')
        .select('team_id, status')
        .eq('id', invitationId)
        .single();

      if (invitationError) throw invitationError;

      // Check if invitation is still pending
      if (invitation.status !== 'pending') {
        throw new Error('This invitation is no longer valid');
      }

      // Update invitation status
      const { error: updateError } = await supabase
        .from('team_invitations')
        .update({ status: 'accepted' })
        .eq('id', invitationId);

      if (updateError) throw updateError;

      // Add user to team
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: invitation.team_id,
          user_id: user.id,
          role: 'member'
        }]);

      if (memberError) throw memberError;

      // Add user to general channel
      const { data: generalChannel, error: channelError } = await supabase
        .from('team_channels')
        .select('id')
        .eq('team_id', invitation.team_id)
        .eq('name', 'General')
        .single();

      if (channelError && channelError.code !== 'PGRST116') throw channelError;

      if (generalChannel) {
        await supabase
          .from('team_channel_members')
          .insert([{
            channel_id: generalChannel.id,
            user_id: user.id
          }]);
      }

      // Remove invitation from state
      setInvitations(invitations.filter(inv => inv.id !== invitationId));

      // Fetch updated teams
      const { data: teamData, error: teamError } = await supabase
        .from('enhanced_teams')
        .select('*')
        .eq('id', invitation.team_id)
        .single();

      if (teamError) throw teamError;

      setUserTeams([...userTeams, {
        ...teamData,
        role: 'member',
        memberCount: 0 // Will be updated on next fetch
      }]);

      return { success: true };
    } catch (error) {
      console.error('Error accepting invitation:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Decline team invitation
  const declineInvitation = async (invitationId) => {
    try {
      setLoading(true);
      
      // Update invitation status
      const { error } = await supabase
        .from('team_invitations')
        .update({ status: 'rejected' })
        .eq('id', invitationId);

      if (error) throw error;

      // Remove invitation from state
      setInvitations(invitations.filter(inv => inv.id !== invitationId));

      return { success: true };
    } catch (error) {
      console.error('Error declining invitation:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Get team details
  const getTeamDetails = async (teamId) => {
    try {
      setLoading(true);
      
      // Check if user is a member
      const team = userTeams.find(t => t.id === teamId);
      if (!team) {
        throw new Error('You are not a member of this team');
      }
      
      // Fetch team details
      const { data, error } = await supabase
        .from('enhanced_teams')
        .select(`
          id,
          name,
          description,
          logo_url,
          created_by,
          max_members,
          created_at,
          creator:profiles!enhanced_teams_created_by_fkey(username),
          members:team_members(
            user_id,
            role,
            joined_at,
            user:profiles(username, avatar_url)
          ),
          channels:team_channels(
            id,
            name,
            description,
            is_private,
            created_at
          )
        `)
        .eq('id', teamId)
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error fetching team details:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Send team message
  const sendTeamMessage = async (teamId, message, attachments = null) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to send a message');
      
      // Check if user is a member
      const team = userTeams.find(t => t.id === teamId);
      if (!team) {
        throw new Error('You are not a member of this team');
      }
      
      // Send message
      const { data, error } = await supabase
        .from('team_messages')
        .insert([{
          team_id: teamId,
          user_id: user.id,
          message,
          attachments
        }])
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Get team messages
  const getTeamMessages = async (teamId, limit = 50, before = null) => {
    try {
      setLoading(true);
      
      // Check if user is a member
      const team = userTeams.find(t => t.id === teamId);
      if (!team) {
        throw new Error('You are not a member of this team');
      }
      
      // Build query
      let query = supabase
        .from('team_messages')
        .select(`
          id,
          message,
          attachments,
          created_at,
          user:profiles(id, username, avatar_url)
        `)
        .eq('team_id', teamId)
        .order('created_at', { ascending: false })
        .limit(limit);
        
      if (before) {
        query = query.lt('created_at', before);
      }
      
      const { data, error } = await query;

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Create team challenge
  const createTeamChallenge = async (teamId, challengeId) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to create a team challenge');
      
      // Check if user is an admin or owner
      const team = userTeams.find(t => t.id === teamId);
      if (!team || (team.role !== 'admin' && team.role !== 'owner')) {
        throw new Error('You do not have permission to create team challenges');
      }
      
      // Check if challenge already exists
      const { data: existingChallenge, error: existingError } = await supabase
        .from('team_challenges')
        .select('id')
        .eq('team_id', teamId)
        .eq('challenge_id', challengeId)
        .maybeSingle();

      if (existingError) throw existingError;
      
      if (existingChallenge) {
        throw new Error('This challenge is already assigned to the team');
      }
      
      // Create team challenge
      const { data, error } = await supabase
        .from('team_challenges')
        .insert([{
          team_id: teamId,
          challenge_id: challengeId,
          status: 'not_started',
          started_at: new Date()
        }])
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error creating team challenge:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Get team challenges
  const getTeamChallenges = async (teamId) => {
    try {
      setLoading(true);
      
      // Check if user is a member
      const team = userTeams.find(t => t.id === teamId);
      if (!team) {
        throw new Error('You are not a member of this team');
      }
      
      // Fetch team challenges
      const { data, error } = await supabase
        .from('team_challenges')
        .select(`
          id,
          status,
          started_at,
          completed_at,
          score,
          challenge:challenges(
            id,
            title,
            description,
            category:challenge_categories(name),
            difficulty:challenge_difficulty_levels(name),
            points
          ),
          assignments:team_challenge_assignments(
            user_id,
            role,
            user:profiles(username)
          )
        `)
        .eq('team_id', teamId);

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error fetching team challenges:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    teams: userTeams,
    invitations,
    loading,
    error,
    createTeam,
    inviteToTeam,
    acceptInvitation,
    declineInvitation,
    getTeamDetails,
    sendTeamMessage,
    getTeamMessages,
    createTeamChallenge,
    getTeamChallenges
  };

  return <TeamCollaborationContext.Provider value={value}>{children}</TeamCollaborationContext.Provider>;
}

// Custom hook to use the team collaboration context
export function useTeamCollaboration() {
  const context = useContext(TeamCollaborationContext);
  if (context === undefined) {
    throw new Error('useTeamCollaboration must be used within a TeamCollaborationProvider');
  }
  return context;
}
