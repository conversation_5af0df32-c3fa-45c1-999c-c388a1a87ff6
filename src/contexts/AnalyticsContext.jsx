import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';
import loggingService from '../services/LoggingService';

// Create context
const AnalyticsContext = createContext();

export function AnalyticsProvider({ children }) {
  const { user } = useAuth();
  const [userActivity, setUserActivity] = useState([]);
  const [userStats, setUserStats] = useState({
    totalPoints: 0,
    challengesCompleted: 0,
    modulesCompleted: 0,
    averageScore: 0,
    skillLevels: {}
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch user activity and stats
  useEffect(() => {
    if (!user) {
      setUserActivity([]);
      setUserStats({
        totalPoints: 0,
        challengesCompleted: 0,
        modulesCompleted: 0,
        averageScore: 0,
        skillLevels: {}
      });
      return;
    }

    const fetchUserData = async () => {
      try {
        setLoading(true);

        // Fetch user activity
        const { data: activityData, error: activityError } = await supabase
          .from('user_events')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(100);

        if (activityError) throw activityError;

        setUserActivity(activityData);

        // Fetch challenge completions
        const { data: challengeData, error: challengeError } = await supabase
          .from('challenge_completions')
          .select(`
            id,
            score,
            completed_at,
            challenge:challenges(id, title, difficulty, category, points)
          `)
          .eq('user_id', user.id);

        if (challengeError) throw challengeError;

        // Fetch module completions
        const { data: moduleData, error: moduleError } = await supabase
          .from('module_progress')
          .select(`
            id,
            progress,
            completed,
            module:learning_modules(id, title, difficulty, category)
          `)
          .eq('user_id', user.id)
          .eq('completed', true);

        if (moduleError) throw moduleError;

        // Fetch user skills
        const { data: skillData, error: skillError } = await supabase
          .from('user_skills')
          .select('category, skill_level')
          .eq('user_id', user.id);

        if (skillError) throw skillError;

        // Calculate stats
        const totalPoints = challengeData.reduce((sum, item) => sum + item.score, 0);
        const challengesCompleted = challengeData.length;
        const modulesCompleted = moduleData.length;
        const averageScore = challengesCompleted > 0
          ? totalPoints / challengesCompleted
          : 0;

        // Convert skill data to object
        const skillLevels = {};
        skillData.forEach(skill => {
          skillLevels[skill.category] = skill.skill_level;
        });

        setUserStats({
          totalPoints,
          challengesCompleted,
          modulesCompleted,
          averageScore,
          skillLevels
        });
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [user]);

  // Track user event
  const trackEvent = async (eventType, eventData = {}) => {
    try {
      if (!user) return;

      // Log to analytics service
      loggingService.logActivity(user.id, eventType, eventData);

      const { error } = await supabase
        .from('user_events')
        .insert([{
          user_id: user.id,
          event_type: eventType,
          event_data: eventData
        }]);

      if (error) throw error;

      // Update local state
      setUserActivity(prev => [{
        id: Date.now().toString(), // Temporary ID
        user_id: user.id,
        event_type: eventType,
        event_data: eventData,
        created_at: new Date()
      }, ...prev]);
    } catch (error) {
      console.error('Error tracking event:', error);
      trackError('event_tracking_error', error.message, error.stack, 'AnalyticsContext');
    }
  };

  // Track page view
  const trackPageView = (pageName, pageUrl = window.location.pathname) => {
    if (!user) return;

    loggingService.logActivity(user.id, 'page_view', {
      pageName,
      pageUrl,
      referrer: document.referrer,
      timestamp: new Date().toISOString()
    });

    trackEvent('page_view', {
      pageName,
      pageUrl,
      referrer: document.referrer
    });
  };

  // Track challenge attempt
  const trackChallengeAttempt = (challengeId, isCorrect, points = 0) => {
    if (!user) return;

    loggingService.logActivity(user.id, 'challenge_attempt', {
      challengeId,
      isCorrect,
      points,
      timestamp: new Date().toISOString()
    });

    loggingService.updateUserStats(user.id, {
      challengeAttempts: 1,
      challengeCompletions: isCorrect ? 1 : 0
    });

    trackEvent('challenge_attempt', {
      challengeId,
      isCorrect,
      points
    });
  };

  // Track module progress
  const trackModuleProgress = (moduleId, progress, completed = false) => {
    if (!user) return;

    loggingService.logActivity(user.id, 'module_progress', {
      moduleId,
      progress,
      completed,
      timestamp: new Date().toISOString()
    });

    loggingService.updateUserStats(user.id, {
      moduleProgress: progress
    });

    trackEvent('module_progress', {
      moduleId,
      progress,
      completed
    });
  };

  // Track coin transaction
  const trackCoinTransaction = (amount, transactionType, description = '') => {
    if (!user) return;

    loggingService.logActivity(user.id, 'coin_transaction', {
      amount,
      transactionType,
      description,
      timestamp: new Date().toISOString()
    });

    if (amount > 0) {
      loggingService.updateUserStats(user.id, {
        coinsEarned: amount
      });
    } else {
      loggingService.updateUserStats(user.id, {
        coinsSpent: Math.abs(amount)
      });
    }

    trackEvent('coin_transaction', {
      amount,
      transactionType,
      description
    });
  };

  // Track error
  const trackError = (errorType, errorMessage, errorStack = null, component = null, additionalData = {}) => {
    loggingService.logError(
      user?.id,
      errorType,
      errorMessage,
      errorStack,
      component,
      additionalData
    );
  };

  // Get user progress over time
  const getProgressOverTime = async (timeFrame = 'month') => {
    try {
      if (!user) return [];

      // Calculate start date based on time frame
      const now = new Date();
      let startDate;
      let interval;

      switch (timeFrame) {
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          interval = 'day';
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          interval = 'day';
          break;
        case 'year':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          interval = 'month';
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          interval = 'day';
      }

      // Fetch challenge completions within time frame
      const { data: challengeData, error: challengeError } = await supabase
        .from('challenge_completions')
        .select('completed_at, score')
        .eq('user_id', user.id)
        .gte('completed_at', startDate.toISOString());

      if (challengeError) throw challengeError;

      // Group by interval
      const progressData = {};

      challengeData.forEach(item => {
        const date = new Date(item.completed_at);
        let key;

        if (interval === 'day') {
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        } else if (interval === 'month') {
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        }

        if (!progressData[key]) {
          progressData[key] = {
            date: key,
            points: 0,
            completions: 0
          };
        }

        progressData[key].points += item.score;
        progressData[key].completions += 1;
      });

      // Convert to array and sort by date
      return Object.values(progressData).sort((a, b) => a.date.localeCompare(b.date));
    } catch (error) {
      console.error('Error getting progress over time:', error);
      setError(error.message);
      return [];
    }
  };

  // Get skill distribution
  const getSkillDistribution = () => {
    const { skillLevels } = userStats;

    return Object.entries(skillLevels).map(([category, level]) => ({
      category,
      level
    }));
  };

  // Get challenge completion rate by difficulty
  const getCompletionRateByDifficulty = async () => {
    try {
      if (!user) return [];

      // Fetch all challenge attempts
      const { data: attemptData, error: attemptError } = await supabase
        .from('challenge_attempts')
        .select(`
          id,
          challenge_id,
          challenge:challenges(difficulty)
        `)
        .eq('user_id', user.id);

      if (attemptError) throw attemptError;

      // Fetch all challenge completions
      const { data: completionData, error: completionError } = await supabase
        .from('challenge_completions')
        .select(`
          id,
          challenge_id,
          challenge:challenges(difficulty)
        `)
        .eq('user_id', user.id);

      if (completionError) throw completionError;

      // Group attempts by difficulty
      const attemptsByDifficulty = {};
      attemptData.forEach(attempt => {
        const difficulty = attempt.challenge.difficulty;
        if (!attemptsByDifficulty[difficulty]) {
          attemptsByDifficulty[difficulty] = 0;
        }
        attemptsByDifficulty[difficulty] += 1;
      });

      // Group completions by difficulty
      const completionsByDifficulty = {};
      completionData.forEach(completion => {
        const difficulty = completion.challenge.difficulty;
        if (!completionsByDifficulty[difficulty]) {
          completionsByDifficulty[difficulty] = 0;
        }
        completionsByDifficulty[difficulty] += 1;
      });

      // Calculate completion rates
      const completionRates = [];
      for (const difficulty in attemptsByDifficulty) {
        const attempts = attemptsByDifficulty[difficulty];
        const completions = completionsByDifficulty[difficulty] || 0;
        const rate = attempts > 0 ? (completions / attempts) * 100 : 0;

        completionRates.push({
          difficulty,
          attempts,
          completions,
          rate
        });
      }

      return completionRates;
    } catch (error) {
      console.error('Error getting completion rates:', error);
      setError(error.message);
      return [];
    }
  };

  // Context value
  const value = {
    userActivity,
    userStats,
    loading,
    error,
    trackEvent,
    trackPageView,
    trackChallengeAttempt,
    trackModuleProgress,
    trackCoinTransaction,
    trackError,
    getProgressOverTime,
    getSkillDistribution,
    getCompletionRateByDifficulty
  };

  return <AnalyticsContext.Provider value={value}>{children}</AnalyticsContext.Provider>;
}

// Custom hook to use the analytics context
export function useAnalytics() {
  const context = useContext(AnalyticsContext);
  if (context === undefined) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
}
