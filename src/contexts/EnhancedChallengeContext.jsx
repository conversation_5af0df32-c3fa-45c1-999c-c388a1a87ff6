import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const EnhancedChallengeContext = createContext();

export function EnhancedChallengeProvider({ children }) {
  const { user, profile } = useAuth();
  const [challenges, setChallenges] = useState([]);
  const [categories, setCategories] = useState([]);
  const [difficulties, setDifficulties] = useState([]);
  const [types, setTypes] = useState([]);
  const [userCompletions, setUserCompletions] = useState({});
  const [userAttempts, setUserAttempts] = useState({});
  const [userHintPurchases, setUserHintPurchases] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch all challenges based on user's subscription tier
  useEffect(() => {
    const fetchChallenges = async () => {
      try {
        setLoading(true);

        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('challenge_categories')
          .select('*')
          .order('display_order', { ascending: true });

        if (categoriesError) throw categoriesError;

        // Fetch difficulties
        const { data: difficultiesData, error: difficultiesError } = await supabase
          .from('challenge_difficulty_levels')
          .select('*')
          .order('display_order', { ascending: true });

        if (difficultiesError) throw difficultiesError;

        // Fetch types
        const { data: typesData, error: typesError } = await supabase
          .from('challenge_types')
          .select('*');

        if (typesError) throw typesError;

        // Fetch challenges
        let query = supabase
          .from('challenges')
          .select(`
            id,
            title,
            slug,
            description,
            category_id,
            difficulty_id,
            type_id,
            points,
            coin_reward,
            estimated_time,
            is_premium,
            is_business,
            created_at,
            category:challenge_categories(name),
            difficulty:challenge_difficulty_levels(name),
            type:challenge_types(name)
          `)
          .eq('is_active', true);

        // If user is not logged in, only fetch non-premium challenges
        if (!user) {
          query = query.eq('is_premium', false).eq('is_business', false);
        }
        // If user is logged in, filter based on subscription tier
        else if (profile) {
          if (profile.subscription_tier === 'free') {
            query = query.eq('is_premium', false).eq('is_business', false);
          } else if (profile.subscription_tier === 'premium') {
            query = query.eq('is_business', false);
          }
          // Business tier users can see all challenges
        }

        const { data: challengesData, error: challengesError } = await query;

        if (challengesError) throw challengesError;

        setCategories(categoriesData);
        setDifficulties(difficultiesData);
        setTypes(typesData);
        setChallenges(challengesData);
      } catch (error) {
        console.error('Error fetching challenges:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchChallenges();
  }, [user, profile]);

  // Fetch user's challenge completions and attempts
  useEffect(() => {
    if (!user) {
      setUserCompletions({});
      setUserAttempts({});
      setUserHintPurchases({});
      return;
    }

    const fetchUserData = async () => {
      try {
        setLoading(true);

        // Fetch completions
        const { data: completionsData, error: completionsError } = await supabase
          .from('challenge_completions')
          .select(`
            id,
            challenge_id,
            points_earned,
            coins_earned,
            completion_time,
            completed_at
          `)
          .eq('user_id', user.id);

        if (completionsError) throw completionsError;

        // Fetch attempts
        const { data: attemptsData, error: attemptsError } = await supabase
          .from('challenge_attempts')
          .select(`
            id,
            challenge_id,
            is_correct,
            points_earned,
            coins_earned,
            attempt_time,
            created_at
          `)
          .eq('user_id', user.id);

        if (attemptsError) throw attemptsError;

        // Fetch hint purchases
        const { data: hintPurchasesData, error: hintPurchasesError } = await supabase
          .from('challenge_hint_purchases')
          .select(`
            id,
            hint_id,
            coins_spent,
            purchased_at,
            hint:challenge_hints(challenge_id)
          `)
          .eq('user_id', user.id);

        if (hintPurchasesError) throw hintPurchasesError;

        // Convert to objects with challenge_id as key
        const completionsObj = {};
        completionsData.forEach(item => {
          completionsObj[item.challenge_id] = item;
        });

        const attemptsObj = {};
        attemptsData.forEach(item => {
          if (!attemptsObj[item.challenge_id]) {
            attemptsObj[item.challenge_id] = [];
          }
          attemptsObj[item.challenge_id].push(item);
        });

        // Group hint purchases by challenge_id
        const hintPurchasesObj = {};
        hintPurchasesData.forEach(item => {
          const challengeId = item.hint.challenge_id;
          if (!hintPurchasesObj[challengeId]) {
            hintPurchasesObj[challengeId] = [];
          }
          hintPurchasesObj[challengeId].push(item);
        });

        setUserCompletions(completionsObj);
        setUserAttempts(attemptsObj);
        setUserHintPurchases(hintPurchasesObj);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();

    // Set up real-time subscription for user's challenge completions
    const completionsSubscription = supabase
      .channel(`user-challenge-completions-${user.id}`)
      .on('INSERT', (payload) => {
        setUserCompletions(prev => ({
          ...prev,
          [payload.new.challenge_id]: payload.new
        }));
      })
      .subscribe();

    // Set up real-time subscription for user's challenge attempts
    const attemptsSubscription = supabase
      .channel(`user-challenge-attempts-${user.id}`)
      .on('INSERT', (payload) => {
        setUserAttempts(prev => {
          const challengeAttempts = prev[payload.new.challenge_id] || [];
          return {
            ...prev,
            [payload.new.challenge_id]: [...challengeAttempts, payload.new]
          };
        });
      })
      .subscribe();

    // Set up real-time subscription for user's hint purchases
    const hintPurchasesSubscription = supabase
      .channel(`user-hint-purchases-${user.id}`)
      .on('INSERT', (payload) => {
        // We need to fetch the challenge_id for this hint
        supabase
          .from('challenge_hints')
          .select('challenge_id')
          .eq('id', payload.new.hint_id)
          .single()
          .then(({ data, error }) => {
            if (error) return;

            const challengeId = data.challenge_id;
            setUserHintPurchases(prev => {
              const challengeHints = prev[challengeId] || [];
              return {
                ...prev,
                [challengeId]: [...challengeHints, {
                  ...payload.new,
                  hint: { challenge_id: challengeId }
                }]
              };
            });
          });
      })
      .subscribe();

    return () => {
      supabase.removeChannel(completionsSubscription);
      supabase.removeChannel(attemptsSubscription);
      supabase.removeChannel(hintPurchasesSubscription);
    };
  }, [user]);

  // Get a single challenge by ID or slug
  const getChallengeById = async (idOrSlug) => {
    try {
      setLoading(true);

      // Determine if the input is a UUID or a slug
      const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(idOrSlug);

      // Build query based on input type
      let query = supabase
        .from('challenges')
        .select(`
          id,
          title,
          slug,
          description,
          category_id,
          difficulty_id,
          type_id,
          points,
          coin_reward,
          estimated_time,
          is_premium,
          is_business,
          created_at,
          category:challenge_categories(name),
          difficulty:challenge_difficulty_levels(name),
          type:challenge_types(name),
          content:challenge_content(content),
          hints:challenge_hints(id, hint, coin_cost, display_order)
        `);

      if (isUuid) {
        query = query.eq('id', idOrSlug);
      } else {
        query = query.eq('slug', idOrSlug);
      }

      const { data, error } = await query.single();

      if (error) throw error;

      // Sort hints by display_order
      if (data.hints) {
        data.hints.sort((a, b) => a.display_order - b.display_order);
      }

      return data;
    } catch (error) {
      console.error('Error fetching challenge:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Submit a challenge attempt
  const submitChallengeAttempt = async (challengeId, solution) => {
    try {
      if (!user) throw new Error('You must be logged in to submit an attempt');

      // Get challenge details
      const challenge = challenges.find(c => c.id === challengeId);
      if (!challenge) throw new Error('Challenge not found');

      // Check if user has already completed this challenge
      if (userCompletions[challengeId]) {
        return {
          success: false,
          message: 'You have already completed this challenge'
        };
      }

      // In a real implementation, we would validate the solution against the correct answer
      // For now, we'll simulate a correct answer with a 50% chance
      const isCorrect = Math.random() >= 0.5;

      // Calculate points and coins earned
      const pointsEarned = isCorrect ? challenge.points : 0;
      const coinsEarned = isCorrect ? challenge.coin_reward : 0;

      // Record the attempt
      const { data: attemptData, error: attemptError } = await supabase
        .from('challenge_attempts')
        .insert([{
          user_id: user.id,
          challenge_id: challengeId,
          solution,
          is_correct: isCorrect,
          points_earned: pointsEarned,
          coins_earned: coinsEarned,
          attempt_time: 60, // Placeholder value
          created_at: new Date()
        }])
        .select()
        .single();

      if (attemptError) throw attemptError;

      // If correct, record completion
      if (isCorrect) {
        const { data: completionData, error: completionError } = await supabase
          .from('challenge_completions')
          .insert([{
            user_id: user.id,
            challenge_id: challengeId,
            points_earned: pointsEarned,
            coins_earned: coinsEarned,
            completion_time: 60, // Placeholder value
            completed_at: new Date()
          }])
          .select()
          .single();

        if (completionError) throw completionError;

        // Update user's coins in profile
        if (coinsEarned > 0) {
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              coins: (profile?.coins || 0) + coinsEarned
            })
            .eq('id', user.id);

          if (updateError) throw updateError;
        }

        return {
          success: true,
          isCorrect,
          pointsEarned,
          coinsEarned,
          message: 'Congratulations! You solved the challenge!'
        };
      }

      return {
        success: true,
        isCorrect,
        pointsEarned: 0,
        coinsEarned: 0,
        message: 'Incorrect solution. Try again!'
      };
    } catch (error) {
      console.error('Error submitting challenge attempt:', error);
      setError(error.message);
      throw error;
    }
  };

  // Purchase a hint
  const purchaseHint = async (hintId, coinCost) => {
    try {
      if (!user) throw new Error('You must be logged in to purchase a hint');

      // Check if user has enough coins
      if ((profile?.coins || 0) < coinCost) {
        throw new Error('Not enough coins to purchase this hint');
      }

      // Check if user has already purchased this hint
      const { data: existingPurchase, error: existingError } = await supabase
        .from('challenge_hint_purchases')
        .select('id')
        .eq('user_id', user.id)
        .eq('hint_id', hintId)
        .maybeSingle();

      if (existingError) throw existingError;

      if (existingPurchase) {
        return {
          success: true,
          message: 'You have already purchased this hint'
        };
      }

      // Record the purchase
      const { data: purchaseData, error: purchaseError } = await supabase
        .from('challenge_hint_purchases')
        .insert([{
          user_id: user.id,
          hint_id: hintId,
          coins_spent: coinCost,
          purchased_at: new Date()
        }])
        .select()
        .single();

      if (purchaseError) throw purchaseError;

      // Update user's coins in profile
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          coins: (profile?.coins || 0) - coinCost
        })
        .eq('id', user.id);

      if (updateError) throw updateError;

      return {
        success: true,
        message: 'Hint purchased successfully'
      };
    } catch (error) {
      console.error('Error purchasing hint:', error);
      setError(error.message);
      throw error;
    }
  };

  // Rate a challenge
  const rateChallenge = async (challengeId, rating, feedback = '') => {
    try {
      if (!user) throw new Error('You must be logged in to rate a challenge');

      // Check if user has already rated this challenge
      const { data: existingRating, error: existingError } = await supabase
        .from('challenge_ratings')
        .select('id')
        .eq('user_id', user.id)
        .eq('challenge_id', challengeId)
        .single();

      if (existingError && existingError.code !== 'PGRST116') throw existingError;

      if (existingRating) {
        // Update existing rating
        const { data, error } = await supabase
          .from('challenge_ratings')
          .update({
            rating,
            feedback
          })
          .eq('id', existingRating.id)
          .select()
          .single();

        if (error) throw error;

        return data;
      } else {
        // Create new rating
        const { data, error } = await supabase
          .from('challenge_ratings')
          .insert([{
            challenge_id: challengeId,
            user_id: user.id,
            rating,
            feedback
          }])
          .select()
          .single();

        if (error) throw error;

        return data;
      }
    } catch (error) {
      console.error('Error rating challenge:', error);
      setError(error.message);
      throw error;
    }
  };

  // Check if a challenge is completed by the user
  const isChallengeCompleted = (challengeId) => {
    return !!userCompletions[challengeId];
  };

  // Get user's attempts for a challenge
  const getChallengeAttempts = (challengeId) => {
    return userAttempts[challengeId] || [];
  };

  // Check if user has purchased a hint
  const hasUserPurchasedHint = (hintId) => {
    // Iterate through all challenge hint purchases
    for (const challengeId in userHintPurchases) {
      const hints = userHintPurchases[challengeId];
      if (hints.some(h => h.hint_id === hintId)) {
        return true;
      }
    }
    return false;
  };

  // Get filtered challenges
  const getFilteredChallenges = (filters = {}) => {
    let filteredChallenges = [...challenges];

    // Filter by category
    if (filters.category) {
      filteredChallenges = filteredChallenges.filter(
        challenge => challenge.category.name === filters.category
      );
    }

    // Filter by difficulty
    if (filters.difficulty) {
      filteredChallenges = filteredChallenges.filter(
        challenge => challenge.difficulty.name === filters.difficulty
      );
    }

    // Filter by type
    if (filters.type) {
      filteredChallenges = filteredChallenges.filter(
        challenge => challenge.type.name === filters.type
      );
    }

    // Filter by search query
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredChallenges = filteredChallenges.filter(
        challenge =>
          challenge.title.toLowerCase().includes(searchLower) ||
          challenge.description.toLowerCase().includes(searchLower)
      );
    }

    // Filter by subscription tier
    if (!profile || profile.subscription_tier === 'free') {
      filteredChallenges = filteredChallenges.filter(
        challenge => !challenge.is_premium && !challenge.is_business
      );
    } else if (profile.subscription_tier === 'premium') {
      filteredChallenges = filteredChallenges.filter(
        challenge => !challenge.is_business
      );
    }

    // Filter by completion status
    if (filters.completed === true) {
      filteredChallenges = filteredChallenges.filter(
        challenge => isChallengeCompleted(challenge.id)
      );
    } else if (filters.completed === false) {
      filteredChallenges = filteredChallenges.filter(
        challenge => !isChallengeCompleted(challenge.id)
      );
    }

    // Filter by points range
    if (filters.minPoints !== undefined) {
      filteredChallenges = filteredChallenges.filter(
        challenge => challenge.points >= filters.minPoints
      );
    }

    if (filters.maxPoints !== undefined) {
      filteredChallenges = filteredChallenges.filter(
        challenge => challenge.points <= filters.maxPoints
      );
    }

    return filteredChallenges;
  };

  // Context value
  const value = {
    challenges,
    categories,
    difficulties,
    types,
    userCompletions,
    userAttempts,
    loading,
    error,
    getChallengeById,
    submitChallengeAttempt,
    purchaseHint,
    rateChallenge,
    isChallengeCompleted,
    getChallengeAttempts,
    hasUserPurchasedHint,
    getFilteredChallenges
  };

  return <EnhancedChallengeContext.Provider value={value}>{children}</EnhancedChallengeContext.Provider>;
}

// Custom hook to use the challenge context
export function useEnhancedChallenge() {
  const context = useContext(EnhancedChallengeContext);
  if (context === undefined) {
    throw new Error('useEnhancedChallenge must be used within an EnhancedChallengeProvider');
  }
  return context;
}
