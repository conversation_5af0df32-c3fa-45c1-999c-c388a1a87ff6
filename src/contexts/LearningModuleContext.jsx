import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const LearningModuleContext = createContext();

export function LearningModuleProvider({ children }) {
  const { user, profile } = useAuth();
  const [modules, setModules] = useState([]);
  const [categories, setCategories] = useState([]);
  const [difficulties, setDifficulties] = useState([]);
  const [userProgress, setUserProgress] = useState({});
  const [learningPaths, setLearningPaths] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch all learning modules based on user's subscription tier
  useEffect(() => {
    const fetchModules = async () => {
      try {
        setLoading(true);

        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('learning_module_categories')
          .select('*')
          .order('display_order', { ascending: true });

        if (categoriesError) throw categoriesError;

        // Fetch difficulties
        const { data: difficultiesData, error: difficultiesError } = await supabase
          .from('learning_module_difficulty_levels')
          .select('*')
          .order('display_order', { ascending: true });

        if (difficultiesError) throw difficultiesError;

        // Fetch modules
        let query = supabase
          .from('learning_modules')
          .select(`
            id,
            title,
            slug,
            description,
            category_id,
            difficulty_id,
            estimated_time,
            is_premium,
            is_business,
            created_at,
            category:learning_module_categories(name),
            difficulty:learning_module_difficulty_levels(name)
          `)
          .eq('is_active', true);

        // If user is not logged in, only fetch non-premium modules
        if (!user) {
          query = query.eq('is_premium', false).eq('is_business', false);
        }
        // If user is logged in, filter based on subscription tier
        else if (profile) {
          if (profile.subscription_tier === 'free') {
            query = query.eq('is_premium', false).eq('is_business', false);
          } else if (profile.subscription_tier === 'premium') {
            query = query.eq('is_business', false);
          }
          // Business tier users can see all modules
        }

        const { data: modulesData, error: modulesError } = await query;

        if (modulesError) throw modulesError;

        // Fetch learning paths
        let pathsQuery = supabase
          .from('learning_paths')
          .select(`
            id,
            title,
            description,
            category_id,
            is_premium,
            is_business,
            created_at,
            category:learning_module_categories(name),
            modules:learning_path_modules(
              module_id,
              display_order
            )
          `)
          .eq('is_active', true);

        // Apply the same subscription tier filtering to learning paths
        if (!user) {
          pathsQuery = pathsQuery.eq('is_premium', false).eq('is_business', false);
        } else if (profile) {
          if (profile.subscription_tier === 'free') {
            pathsQuery = pathsQuery.eq('is_premium', false).eq('is_business', false);
          } else if (profile.subscription_tier === 'premium') {
            pathsQuery = pathsQuery.eq('is_business', false);
          }
        }

        const { data: pathsData, error: pathsError } = await pathsQuery;

        if (pathsError) throw pathsError;

        setCategories(categoriesData);
        setDifficulties(difficultiesData);
        setModules(modulesData);
        setLearningPaths(pathsData);
      } catch (error) {
        console.error('Error fetching learning modules:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchModules();
  }, [user, profile]);

  // Fetch user's module progress
  useEffect(() => {
    if (!user) {
      setUserProgress({});
      return;
    }

    const fetchUserProgress = async () => {
      try {
        setLoading(true);

        // Fetch module progress
        const { data: progressData, error: progressError } = await supabase
          .from('learning_module_progress')
          .select(`
            id,
            module_id,
            progress,
            last_activity,
            completed,
            completed_at
          `)
          .eq('user_id', user.id);

        if (progressError) throw progressError;

        // Convert to object with module_id as key
        const progressObj = {};
        progressData.forEach(item => {
          progressObj[item.module_id] = item;
        });

        setUserProgress(progressObj);
      } catch (error) {
        console.error('Error fetching user progress:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUserProgress();

    // Set up real-time subscription for user's module progress
    const progressSubscription = supabase
      .channel(`user-module-progress-${user.id}`)
      .on('INSERT', (payload) => {
        setUserProgress(prev => ({
          ...prev,
          [payload.new.module_id]: payload.new
        }));
      })
      .on('UPDATE', (payload) => {
        setUserProgress(prev => ({
          ...prev,
          [payload.new.module_id]: payload.new
        }));
      })
      .subscribe();

    return () => {
      supabase.removeChannel(progressSubscription);
    };
  }, [user]);

  // Get a single module by ID or slug
  const getModuleById = async (idOrSlug) => {
    try {
      setLoading(true);

      // Determine if the input is a UUID or a slug
      const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(idOrSlug);

      // Build query based on input type
      let query = supabase
        .from('learning_modules')
        .select(`
          id,
          title,
          slug,
          description,
          category_id,
          difficulty_id,
          estimated_time,
          is_premium,
          is_business,
          created_at,
          category:learning_module_categories(name),
          difficulty:learning_module_difficulty_levels(name),
          content:learning_module_content(content)
        `);

      if (isUuid) {
        query = query.eq('id', idOrSlug);
      } else {
        query = query.eq('slug', idOrSlug);
      }

      const { data, error } = await query.single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error fetching module:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update user's progress on a module
  const updateModuleProgress = async (moduleId, progress, completed = false) => {
    try {
      if (!user) throw new Error('You must be logged in to update progress');

      // Check if progress record exists
      const existingProgress = userProgress[moduleId];

      if (existingProgress) {
        // Update existing progress
        const { data, error } = await supabase
          .from('learning_module_progress')
          .update({
            progress,
            completed,
            completed_at: completed ? new Date() : null,
            last_activity: new Date()
          })
          .eq('id', existingProgress.id)
          .select()
          .single();

        if (error) throw error;

        setUserProgress(prev => ({
          ...prev,
          [moduleId]: data
        }));

        return data;
      } else {
        // Create new progress record
        const { data, error } = await supabase
          .from('learning_module_progress')
          .insert([{
            module_id: moduleId,
            user_id: user.id,
            progress,
            completed,
            completed_at: completed ? new Date() : null,
            last_activity: new Date()
          }])
          .select()
          .single();

        if (error) throw error;

        setUserProgress(prev => ({
          ...prev,
          [moduleId]: data
        }));

        return data;
      }
    } catch (error) {
      console.error('Error updating progress:', error);
      setError(error.message);
      throw error;
    }
  };

  // Rate a module
  const rateModule = async (moduleId, rating, feedback = '') => {
    try {
      if (!user) throw new Error('You must be logged in to rate a module');

      // Check if user has already rated this module
      const { data: existingRating, error: existingError } = await supabase
        .from('learning_module_ratings')
        .select('id')
        .eq('user_id', user.id)
        .eq('module_id', moduleId)
        .single();

      if (existingError && existingError.code !== 'PGRST116') throw existingError;

      if (existingRating) {
        // Update existing rating
        const { data, error } = await supabase
          .from('learning_module_ratings')
          .update({
            rating,
            feedback
          })
          .eq('id', existingRating.id)
          .select()
          .single();

        if (error) throw error;

        return data;
      } else {
        // Create new rating
        const { data, error } = await supabase
          .from('learning_module_ratings')
          .insert([{
            module_id: moduleId,
            user_id: user.id,
            rating,
            feedback
          }])
          .select()
          .single();

        if (error) throw error;

        return data;
      }
    } catch (error) {
      console.error('Error rating module:', error);
      setError(error.message);
      throw error;
    }
  };

  // Check if a module is completed by the user
  const isModuleCompleted = (moduleId) => {
    return userProgress[moduleId]?.completed || false;
  };

  // Get user's progress on a module
  const getModuleProgress = (moduleId) => {
    return userProgress[moduleId]?.progress || 0;
  };

  // Get filtered modules
  const getFilteredModules = (filters = {}) => {
    let filteredModules = [...modules];

    // Filter by category
    if (filters.category) {
      filteredModules = filteredModules.filter(
        module => module.category.name === filters.category
      );
    }

    // Filter by difficulty
    if (filters.difficulty) {
      filteredModules = filteredModules.filter(
        module => module.difficulty.name === filters.difficulty
      );
    }

    // Filter by search query
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredModules = filteredModules.filter(
        module =>
          module.title.toLowerCase().includes(searchLower) ||
          module.description.toLowerCase().includes(searchLower)
      );
    }

    // Filter by subscription tier
    if (!profile || profile.subscription_tier === 'free') {
      filteredModules = filteredModules.filter(
        module => !module.is_premium && !module.is_business
      );
    } else if (profile.subscription_tier === 'premium') {
      filteredModules = filteredModules.filter(
        module => !module.is_business
      );
    }

    // Filter by completion status
    if (filters.completed === true) {
      filteredModules = filteredModules.filter(
        module => isModuleCompleted(module.id)
      );
    } else if (filters.completed === false) {
      filteredModules = filteredModules.filter(
        module => !isModuleCompleted(module.id)
      );
    }

    return filteredModules;
  };

  // Context value
  const value = {
    modules,
    categories,
    difficulties,
    learningPaths,
    userProgress,
    loading,
    error,
    getModuleById,
    updateModuleProgress,
    rateModule,
    isModuleCompleted,
    getModuleProgress,
    getFilteredModules
  };

  return <LearningModuleContext.Provider value={value}>{children}</LearningModuleContext.Provider>;
}

// Custom hook to use the learning module context
export function useLearningModule() {
  const context = useContext(LearningModuleContext);
  if (context === undefined) {
    throw new Error('useLearningModule must be used within a LearningModuleProvider');
  }
  return context;
}
