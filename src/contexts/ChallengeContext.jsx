import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const ChallengeContext = createContext();

export function ChallengeProvider({ children }) {
  const { user, profile } = useAuth();
  const [challenges, setChallenges] = useState([]);
  const [userChallenges, setUserChallenges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch all challenges based on user's subscription tier
  useEffect(() => {
    const fetchChallenges = async () => {
      try {
        setLoading(true);
        
        let query = supabase
          .from('challenges')
          .select(`
            id,
            title,
            description,
            difficulty,
            category,
            points,
            created_at,
            is_premium,
            is_business,
            author:profiles(username)
          `);
        
        const { data, error } = await query;
        
        if (error) throw error;
        
        setChallenges(data);
      } catch (error) {
        console.error('Error fetching challenges:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchChallenges();
    
    // Set up real-time subscription for new challenges
    const subscription = supabase
      .channel('public:challenges')
      .on('INSERT', (payload) => {
        setChallenges(prev => [...prev, payload.new]);
      })
      .on('UPDATE', (payload) => {
        setChallenges(prev => 
          prev.map(challenge => 
            challenge.id === payload.new.id ? payload.new : challenge
          )
        );
      })
      .on('DELETE', (payload) => {
        setChallenges(prev => 
          prev.filter(challenge => challenge.id !== payload.old.id)
        );
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [profile?.subscription_tier]);
  
  // Fetch user's completed challenges
  useEffect(() => {
    if (!user) {
      setUserChallenges([]);
      return;
    }
    
    const fetchUserChallenges = async () => {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('challenge_completions')
          .select(`
            id,
            completed_at,
            score,
            challenge_id
          `)
          .eq('user_id', user.id);
          
        if (error) throw error;
        
        setUserChallenges(data);
      } catch (error) {
        console.error('Error fetching user challenges:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserChallenges();
    
    // Set up real-time subscription for user's challenge completions
    const subscription = supabase
      .channel(`user-completions-${user.id}`)
      .on('INSERT', (payload) => {
        setUserChallenges(prev => [...prev, payload.new]);
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [user]);
  
  // Get a single challenge by ID
  const getChallengeById = async (id) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('challenges')
        .select(`
          id,
          title,
          description,
          difficulty,
          category,
          points,
          content,
          created_at,
          is_premium,
          is_business,
          author:profiles(username),
          resources:challenge_resources(*)
        `)
        .eq('id', id)
        .single();
        
      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Error fetching challenge:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Submit a challenge solution
  const submitSolution = async (challengeId, solution) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to submit a solution');
      
      // Record the attempt
      await supabase
        .from('challenge_attempts')
        .insert([{
          challenge_id: challengeId,
          user_id: user.id,
          attempted_at: new Date()
        }]);
      
      // Call the validation function (this would be an Edge Function in Supabase)
      // For now, we'll simulate it with a direct database call
      const challenge = await getChallengeById(challengeId);
      
      // Simple validation logic (in a real app, this would be more complex)
      const isCorrect = solution.toLowerCase() === challenge.content?.solution?.toLowerCase();
      
      if (isCorrect) {
        // Record completion
        const { data, error } = await supabase
          .from('challenge_completions')
          .insert([{
            challenge_id: challengeId,
            user_id: user.id,
            completed_at: new Date(),
            score: challenge.points
          }])
          .select()
          .single();
          
        if (error) throw error;
        
        // Update user points
        await supabase.rpc('add_user_points', { 
          user_id: user.id, 
          points_to_add: challenge.points 
        });
        
        return { 
          success: true, 
          message: 'Congratulations! You solved the challenge.', 
          points: challenge.points,
          completion: data
        };
      } else {
        return { 
          success: false, 
          message: 'Incorrect solution. Try again!' 
        };
      }
    } catch (error) {
      console.error('Error submitting solution:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Check if a challenge is completed by the user
  const isChallengeCompleted = (challengeId) => {
    return userChallenges.some(completion => completion.challenge_id === challengeId);
  };
  
  // Get filtered challenges
  const getFilteredChallenges = (filters = {}) => {
    let filteredChallenges = [...challenges];
    
    // Filter by difficulty
    if (filters.difficulty) {
      filteredChallenges = filteredChallenges.filter(
        challenge => challenge.difficulty === filters.difficulty
      );
    }
    
    // Filter by category
    if (filters.category) {
      filteredChallenges = filteredChallenges.filter(
        challenge => challenge.category === filters.category
      );
    }
    
    // Filter by search query
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredChallenges = filteredChallenges.filter(
        challenge => 
          challenge.title.toLowerCase().includes(searchLower) ||
          challenge.description.toLowerCase().includes(searchLower)
      );
    }
    
    // Filter by subscription tier
    if (!profile || profile.subscription_tier === 'free') {
      filteredChallenges = filteredChallenges.filter(
        challenge => !challenge.is_premium && !challenge.is_business
      );
    } else if (profile.subscription_tier === 'premium') {
      filteredChallenges = filteredChallenges.filter(
        challenge => !challenge.is_business
      );
    }
    
    // Filter by completion status
    if (filters.completed === true) {
      filteredChallenges = filteredChallenges.filter(
        challenge => isChallengeCompleted(challenge.id)
      );
    } else if (filters.completed === false) {
      filteredChallenges = filteredChallenges.filter(
        challenge => !isChallengeCompleted(challenge.id)
      );
    }
    
    return filteredChallenges;
  };
  
  // Get personalized challenge recommendations
  const getRecommendations = async () => {
    try {
      if (!user) return [];
      
      const { data, error } = await supabase.rpc('get_recommended_challenges', {
        user_id: user.id
      });
      
      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Error getting recommendations:', error);
      setError(error.message);
      return [];
    }
  };

  // Context value
  const value = {
    challenges,
    userChallenges,
    loading,
    error,
    getChallengeById,
    submitSolution,
    isChallengeCompleted,
    getFilteredChallenges,
    getRecommendations
  };

  return <ChallengeContext.Provider value={value}>{children}</ChallengeContext.Provider>;
}

// Custom hook to use the challenge context
export function useChallenges() {
  const context = useContext(ChallengeContext);
  if (context === undefined) {
    throw new Error('useChallenges must be used within a ChallengeProvider');
  }
  return context;
}
