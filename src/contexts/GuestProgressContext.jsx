import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import LocalStorageService from '../services/LocalStorageService';

// Create context
const GuestProgressContext = createContext();

export function GuestProgressProvider({ children }) {
  const { user } = useAuth();
  const [progress, setProgress] = useState({
    challenges: {},
    modules: {},
    lastActivity: null,
  });
  const [achievements, setAchievements] = useState([]);
  const [lastViewed, setLastViewed] = useState({
    page: null,
    timestamp: null,
  });
  
  // Load progress from localStorage on initial render
  useEffect(() => {
    if (user) {
      // If user is logged in, load user-specific progress
      const userProgress = LocalStorageService.get(`user_${user.id}_progress`, {
        challenges: {},
        modules: {},
        lastActivity: null,
      });
      const userAchievements = LocalStorageService.get(`user_${user.id}_achievements`, []);
      const userLastViewed = LocalStorageService.get(`user_${user.id}_last_viewed`, {
        page: null,
        timestamp: null,
      });
      
      setProgress(userProgress);
      setAchievements(userAchievements);
      setLastViewed(userLastViewed);
    } else {
      // If no user, load guest progress
      const guestProgress = LocalStorageService.get('guest_progress', {
        challenges: {},
        modules: {},
        lastActivity: null,
      });
      const guestAchievements = LocalStorageService.get('guest_achievements', []);
      const guestLastViewed = LocalStorageService.get('guest_last_viewed', {
        page: null,
        timestamp: null,
      });
      
      setProgress(guestProgress);
      setAchievements(guestAchievements);
      setLastViewed(guestLastViewed);
    }
  }, [user]);
  
  // Save progress to localStorage whenever it changes
  useEffect(() => {
    if (user) {
      LocalStorageService.set(`user_${user.id}_progress`, progress);
    } else {
      LocalStorageService.set('guest_progress', progress);
    }
  }, [progress, user]);
  
  // Save achievements to localStorage whenever they change
  useEffect(() => {
    if (user) {
      LocalStorageService.set(`user_${user.id}_achievements`, achievements);
    } else {
      LocalStorageService.set('guest_achievements', achievements);
    }
  }, [achievements, user]);
  
  // Save last viewed to localStorage whenever it changes
  useEffect(() => {
    if (user) {
      LocalStorageService.set(`user_${user.id}_last_viewed`, lastViewed);
    } else {
      LocalStorageService.set('guest_last_viewed', lastViewed);
    }
  }, [lastViewed, user]);
  
  /**
   * Update challenge progress
   * @param {string} challengeId - The challenge ID
   * @param {object} data - Progress data to update
   */
  const updateChallengeProgress = (challengeId, data) => {
    setProgress(prev => ({
      ...prev,
      challenges: {
        ...prev.challenges,
        [challengeId]: {
          ...prev.challenges[challengeId],
          ...data,
          lastUpdated: new Date().toISOString(),
        },
      },
      lastActivity: {
        type: 'challenge',
        id: challengeId,
        timestamp: new Date().toISOString(),
      },
    }));
    
    // Check for achievements
    checkForAchievements();
  };
  
  /**
   * Update module progress
   * @param {string} moduleId - The module ID
   * @param {object} data - Progress data to update
   */
  const updateModuleProgress = (moduleId, data) => {
    setProgress(prev => ({
      ...prev,
      modules: {
        ...prev.modules,
        [moduleId]: {
          ...prev.modules[moduleId],
          ...data,
          lastUpdated: new Date().toISOString(),
        },
      },
      lastActivity: {
        type: 'module',
        id: moduleId,
        timestamp: new Date().toISOString(),
      },
    }));
    
    // Check for achievements
    checkForAchievements();
  };
  
  /**
   * Update last viewed page
   * @param {string} page - The page path
   */
  const updateLastViewed = (page) => {
    setLastViewed({
      page,
      timestamp: new Date().toISOString(),
    });
  };
  
  /**
   * Check for and award achievements
   */
  const checkForAchievements = () => {
    const newAchievements = [];
    
    // Check challenge-related achievements
    const completedChallenges = Object.values(progress.challenges).filter(c => c.completed);
    if (completedChallenges.length >= 1 && !hasAchievement('first_challenge')) {
      newAchievements.push({
        id: 'first_challenge',
        title: 'Challenge Accepted',
        description: 'Complete your first challenge',
        icon: 'FaTrophy',
        timestamp: new Date().toISOString(),
      });
    }
    
    if (completedChallenges.length >= 3 && !hasAchievement('three_challenges')) {
      newAchievements.push({
        id: 'three_challenges',
        title: 'Challenge Master',
        description: 'Complete three challenges',
        icon: 'FaMedal',
        timestamp: new Date().toISOString(),
      });
    }
    
    // Check module-related achievements
    const completedModules = Object.values(progress.modules).filter(m => m.completed);
    if (completedModules.length >= 1 && !hasAchievement('first_module')) {
      newAchievements.push({
        id: 'first_module',
        title: 'Learning Pioneer',
        description: 'Complete your first learning module',
        icon: 'FaGraduationCap',
        timestamp: new Date().toISOString(),
      });
    }
    
    if (completedModules.length >= 3 && !hasAchievement('three_modules')) {
      newAchievements.push({
        id: 'three_modules',
        title: 'Knowledge Seeker',
        description: 'Complete three learning modules',
        icon: 'FaBook',
        timestamp: new Date().toISOString(),
      });
    }
    
    // Add explorer achievement
    const uniquePages = new Set();
    if (lastViewed.page) uniquePages.add(lastViewed.page);
    if (uniquePages.size >= 5 && !hasAchievement('explorer')) {
      newAchievements.push({
        id: 'explorer',
        title: 'Explorer',
        description: 'Visit 5 different pages',
        icon: 'FaCompass',
        timestamp: new Date().toISOString(),
      });
    }
    
    // If we have new achievements, add them
    if (newAchievements.length > 0) {
      setAchievements(prev => [...prev, ...newAchievements]);
    }
  };
  
  /**
   * Check if user has a specific achievement
   * @param {string} achievementId - The achievement ID to check
   * @returns {boolean} True if the user has the achievement
   */
  const hasAchievement = (achievementId) => {
    return achievements.some(a => a.id === achievementId);
  };
  
  /**
   * Get challenge progress
   * @param {string} challengeId - The challenge ID
   * @returns {object} The challenge progress data
   */
  const getChallengeProgress = (challengeId) => {
    return progress.challenges[challengeId] || { started: false, completed: false, progress: 0 };
  };
  
  /**
   * Get module progress
   * @param {string} moduleId - The module ID
   * @returns {object} The module progress data
   */
  const getModuleProgress = (moduleId) => {
    return progress.modules[moduleId] || { started: false, completed: false, progress: 0 };
  };
  
  /**
   * Get overall progress statistics
   * @returns {object} Overall progress stats
   */
  const getOverallProgress = () => {
    const challengeCount = Object.keys(progress.challenges).length;
    const completedChallenges = Object.values(progress.challenges).filter(c => c.completed).length;
    
    const moduleCount = Object.keys(progress.modules).length;
    const completedModules = Object.values(progress.modules).filter(m => m.completed).length;
    
    return {
      challengeCount,
      completedChallenges,
      challengePercentage: challengeCount > 0 ? (completedChallenges / challengeCount) * 100 : 0,
      moduleCount,
      completedModules,
      modulePercentage: moduleCount > 0 ? (completedModules / moduleCount) * 100 : 0,
      totalItems: challengeCount + moduleCount,
      completedItems: completedChallenges + completedModules,
      overallPercentage: (challengeCount + moduleCount) > 0 
        ? ((completedChallenges + completedModules) / (challengeCount + moduleCount)) * 100 
        : 0,
    };
  };
  
  // Context value
  const value = {
    progress,
    achievements,
    lastViewed,
    updateChallengeProgress,
    updateModuleProgress,
    updateLastViewed,
    getChallengeProgress,
    getModuleProgress,
    getOverallProgress,
    hasAchievement,
  };
  
  return <GuestProgressContext.Provider value={value}>{children}</GuestProgressContext.Provider>;
}

// Custom hook to use the guest progress context
export function useGuestProgress() {
  const context = useContext(GuestProgressContext);
  if (context === undefined) {
    throw new Error('useGuestProgress must be used within a GuestProgressProvider');
  }
  return context;
}
