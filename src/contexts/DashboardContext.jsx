import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { useSubscription } from './SubscriptionContext';
import dashboardDataService from '../utils/dashboardDataService';

// Create context
const DashboardContext = createContext();

/**
 * DashboardProvider Component
 * 
 * Provides dashboard data and state management for all dashboard components.
 * Handles data fetching, caching, and updates.
 */
export const DashboardProvider = ({ children }) => {
  const { user } = useAuth();
  const { subscriptionLevel } = useSubscription();
  
  // Dashboard state
  const [timeRange, setTimeRange] = useState('month');
  const [isLoading, setIsLoading] = useState({
    learning: false,
    challenges: false,
    team: false,
    recommendations: false
  });
  const [error, setError] = useState({
    learning: null,
    challenges: null,
    team: null,
    recommendations: null
  });
  
  // Dashboard data
  const [learningStats, setLearningStats] = useState(null);
  const [challengeStats, setChallengeStats] = useState(null);
  const [teamMembers, setTeamMembers] = useState([]);
  const [teamStats, setTeamStats] = useState(null);
  const [recommendations, setRecommendations] = useState(null);
  const [userSkills, setUserSkills] = useState({});
  
  // Detail visibility state
  const [showLearningDetails, setShowLearningDetails] = useState(false);
  const [showChallengeDetails, setShowChallengeDetails] = useState(false);
  const [showTeamDetails, setShowTeamDetails] = useState(false);
  
  // Fetch learning progress data
  const fetchLearningProgress = useCallback(async () => {
    if (!user) return;
    
    setIsLoading(prev => ({ ...prev, learning: true }));
    setError(prev => ({ ...prev, learning: null }));
    
    try {
      const data = await dashboardDataService.getLearningProgress(timeRange, user.id);
      setLearningStats(data);
    } catch (err) {
      console.error('Error fetching learning progress:', err);
      setError(prev => ({ ...prev, learning: 'Failed to load learning data' }));
    } finally {
      setIsLoading(prev => ({ ...prev, learning: false }));
    }
  }, [user, timeRange]);
  
  // Fetch challenge progress data
  const fetchChallengeProgress = useCallback(async () => {
    if (!user) return;
    
    setIsLoading(prev => ({ ...prev, challenges: true }));
    setError(prev => ({ ...prev, challenges: null }));
    
    try {
      const data = await dashboardDataService.getChallengeProgress(timeRange, user.id);
      setChallengeStats(data);
    } catch (err) {
      console.error('Error fetching challenge progress:', err);
      setError(prev => ({ ...prev, challenges: 'Failed to load challenge data' }));
    } finally {
      setIsLoading(prev => ({ ...prev, challenges: false }));
    }
  }, [user, timeRange]);
  
  // Fetch team data
  const fetchTeamData = useCallback(async () => {
    if (!user) return;
    
    setIsLoading(prev => ({ ...prev, team: true }));
    setError(prev => ({ ...prev, team: null }));
    
    try {
      // Assuming the user has a teamId property
      const teamId = user.teamId || 'default';
      
      const members = await dashboardDataService.getTeamMembers(teamId);
      const stats = await dashboardDataService.getTeamProgress(teamId, timeRange);
      
      setTeamMembers(members);
      setTeamStats(stats);
    } catch (err) {
      console.error('Error fetching team data:', err);
      setError(prev => ({ ...prev, team: 'Failed to load team data' }));
    } finally {
      setIsLoading(prev => ({ ...prev, team: false }));
    }
  }, [user, timeRange]);
  
  // Fetch recommendations data
  const fetchRecommendations = useCallback(async () => {
    if (!user) return;
    
    setIsLoading(prev => ({ ...prev, recommendations: true }));
    setError(prev => ({ ...prev, recommendations: null }));
    
    try {
      const recs = await dashboardDataService.getRecommendations(user.id);
      const skills = await dashboardDataService.getUserSkills(user.id);
      
      setRecommendations(recs);
      setUserSkills(skills);
    } catch (err) {
      console.error('Error fetching recommendations:', err);
      setError(prev => ({ ...prev, recommendations: 'Failed to load recommendations' }));
    } finally {
      setIsLoading(prev => ({ ...prev, recommendations: false }));
    }
  }, [user]);
  
  // Fetch all dashboard data
  const fetchAllData = useCallback(() => {
    fetchLearningProgress();
    fetchChallengeProgress();
    
    // Only fetch team data for business users
    if (subscriptionLevel === 'business') {
      fetchTeamData();
    }
    
    fetchRecommendations();
  }, [
    fetchLearningProgress, 
    fetchChallengeProgress, 
    fetchTeamData, 
    fetchRecommendations,
    subscriptionLevel
  ]);
  
  // Clear dashboard data
  const clearDashboardData = () => {
    setLearningStats(null);
    setChallengeStats(null);
    setTeamMembers([]);
    setTeamStats(null);
    setRecommendations(null);
    setUserSkills({});
  };
  
  // Toggle detail sections
  const toggleLearningDetails = () => setShowLearningDetails(prev => !prev);
  const toggleChallengeDetails = () => setShowChallengeDetails(prev => !prev);
  const toggleTeamDetails = () => setShowTeamDetails(prev => !prev);
  
  // Handle time range change
  const handleTimeRangeChange = (newRange) => {
    setTimeRange(newRange);
  };
  
  // Fetch data when user or time range changes
  useEffect(() => {
    if (user) {
      fetchAllData();
    } else {
      clearDashboardData();
    }
  }, [user, timeRange, fetchAllData]);
  
  // Context value
  const value = {
    // Data
    learningStats,
    challengeStats,
    teamMembers,
    teamStats,
    recommendations,
    userSkills,
    
    // State
    timeRange,
    isLoading,
    error,
    showLearningDetails,
    showChallengeDetails,
    showTeamDetails,
    
    // Actions
    handleTimeRangeChange,
    toggleLearningDetails,
    toggleChallengeDetails,
    toggleTeamDetails,
    fetchAllData,
    clearDashboardData
  };
  
  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
};

// Custom hook for using the dashboard context
export const useDashboard = () => {
  const context = useContext(DashboardContext);
  
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  
  return context;
};

export default DashboardContext;
