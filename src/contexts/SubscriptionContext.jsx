import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { SUBSCRIPTION_TIERS, TIER_FEATURES, COIN_PACKAGES, CHALLENGE_COSTS } from '../config/subscriptionTiers';

const SubscriptionContext = createContext();

// For backward compatibility
export const SUBSCRIPTION_LEVELS = {
  NONE: 'none',
  FREE: SUBSCRIPTION_TIERS.FREE,
  PREMIUM: SUBSCRIPTION_TIERS.PREMIUM,
  BUSINESS: SUBSCRIPTION_TIERS.BUSINESS
};

export const SubscriptionProvider = ({ children }) => {
  const [subscription, setSubscription] = useState(null);
  const [subscriptionLevel, setSubscriptionLevel] = useState(SUBSCRIPTION_LEVELS.FREE);
  const [userCoins, setUserCoins] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        // Check if we're logged in
        const isLoggedIn = localStorage.getItem('supabase.auth.token') !== null;
        const userData = localStorage.getItem('supabase.auth.user');

        // Check for subscription data
        const storedSubscriptionData = localStorage.getItem('user_subscription');

        if (isLoggedIn && userData) {
          // User is logged in, try to fetch real subscription data
          const user = JSON.parse(userData);

          try {
            // Try to fetch real subscription data from Supabase
            const { data: subscriptionData, error: subscriptionError } = await supabase
              .from('subscriptions')
              .select('*')
              .eq('user_id', user.id)
              .single();

            if (subscriptionData && !subscriptionError) {
              // We have real subscription data from the database
              setSubscription(subscriptionData);
              setSubscriptionLevel(subscriptionData.tier || SUBSCRIPTION_LEVELS.FREE);
              setUserCoins(subscriptionData.coins || 0);
              // Also save to localStorage as a backup
              localStorage.setItem('user_subscription', JSON.stringify(subscriptionData));
            } else if (storedSubscriptionData) {
              // Fallback to stored subscription data
              const parsedSubscription = JSON.parse(storedSubscriptionData);
              setSubscription(parsedSubscription);
              setSubscriptionLevel(parsedSubscription.tier || SUBSCRIPTION_LEVELS.FREE);
              setUserCoins(parsedSubscription.coins || 0);
            } else {
              // Create default subscription
              const defaultSubscription = {
                user_id: user.id,
                tier: SUBSCRIPTION_LEVELS.FREE,
                coins: 100, // Start with some coins
                start_date: new Date().toISOString(),
                end_date: null,
                is_active: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              };
              setSubscription(defaultSubscription);
              setSubscriptionLevel(SUBSCRIPTION_LEVELS.FREE);
              setUserCoins(100);
              localStorage.setItem('user_subscription', JSON.stringify(defaultSubscription));
            }
          } catch (err) {
            console.error('Error fetching subscription:', err);
            // Fallback to stored or default subscription
            if (storedSubscriptionData) {
              const parsedSubscription = JSON.parse(storedSubscriptionData);
              setSubscription(parsedSubscription);
              setSubscriptionLevel(parsedSubscription.tier || SUBSCRIPTION_LEVELS.FREE);
              setUserCoins(parsedSubscription.coins || 0);
            } else {
              // Create default subscription
              const defaultSubscription = {
                user_id: user.id,
                tier: SUBSCRIPTION_LEVELS.FREE,
                coins: 100,
                start_date: new Date().toISOString(),
                end_date: null,
                is_active: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              };
              setSubscription(defaultSubscription);
              setSubscriptionLevel(SUBSCRIPTION_LEVELS.FREE);
              setUserCoins(100);
              localStorage.setItem('user_subscription', JSON.stringify(defaultSubscription));
            }
          }
        } else if (storedSubscriptionData) {
          // Not logged in but we have stored subscription data
          const parsedSubscription = JSON.parse(storedSubscriptionData);
          setSubscription(parsedSubscription);
          setSubscriptionLevel(parsedSubscription.tier || SUBSCRIPTION_LEVELS.FREE);
          setUserCoins(parsedSubscription.coins || 0);
        } else {
          // Default subscription for demo/simulation
          const defaultSubscription = {
            tier: SUBSCRIPTION_LEVELS.FREE,
            coins: 100,
            start_date: new Date().toISOString(),
            end_date: null,
            is_active: true
          };
          setSubscription(defaultSubscription);
          setSubscriptionLevel(SUBSCRIPTION_LEVELS.FREE);
          setUserCoins(100);
          localStorage.setItem('user_subscription', JSON.stringify(defaultSubscription));
        }

        // We've already handled all cases above
        setLoading(false);

        // All cases handled above
      } catch (error) {
        console.error('Error fetching subscription:', error);
        setError(error.message);
        setSubscriptionLevel(SUBSCRIPTION_LEVELS.FREE);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscription();

    const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN') {
          fetchSubscription();
        } else if (event === 'SIGNED_OUT') {
          setSubscription(null);
          setUserCoins(0);
          setSubscriptionLevel(SUBSCRIPTION_LEVELS.NONE);
        }
      }
    );

    return () => {
      authSubscription?.unsubscribe();
    };
  }, []);

  // Check if user has access to a specific feature
  const hasAccess = (feature, contentTier = null) => {
    if (loading) return false;

    const tierFeatures = TIER_FEATURES[subscriptionLevel];
    if (!tierFeatures) return false;

    // If contentTier is provided, we're checking access to a specific tier of content
    if (contentTier) {
      // Free content is accessible to everyone
      if (contentTier === SUBSCRIPTION_TIERS.FREE) return true;

      // If user has business subscription, they have access to everything
      if (subscriptionLevel === SUBSCRIPTION_LEVELS.BUSINESS) return true;

      // Premium users have access to premium content but not business content
      if (subscriptionLevel === SUBSCRIPTION_LEVELS.PREMIUM) {
        return contentTier !== SUBSCRIPTION_LEVELS.BUSINESS;
      }

      // Free users only have access to free content
      return false;
    }

    // Original feature access check
    if (!feature.includes('.')) {
      return tierFeatures[feature] && tierFeatures[feature].access !== 'none';
    }

    // Handle dot notation for subfeatures (e.g., 'learnModules.access')
    const [mainFeature, subFeature] = feature.split('.');
    return tierFeatures[mainFeature] &&
           tierFeatures[mainFeature][subFeature] !== undefined &&
           tierFeatures[mainFeature][subFeature] !== false;
  };

  // Get the number of available items (modules, challenges, etc.)
  const getAvailableCount = (feature) => {
    if (loading) return 0;

    const tierFeatures = TIER_FEATURES[subscriptionLevel];
    if (!tierFeatures || !tierFeatures[feature]) return 0;

    if (tierFeatures[feature].access === 'full') {
      return tierFeatures[feature].totalItems || -1; // -1 means unlimited
    }

    return tierFeatures[feature][`available${feature.charAt(0).toUpperCase() + feature.slice(1)}`] || 0;
  };

  // Get remaining content access information
  const getRemainingContent = (contentType) => {
    if (loading) return { total: 0, used: 0, remaining: 0, percentage: 0 };

    const tierFeatures = TIER_FEATURES[subscriptionLevel];
    if (!tierFeatures || !tierFeatures[contentType]) {
      return { total: 0, used: 0, remaining: 0, percentage: 0 };
    }

    // For premium and business, return full access
    if (subscriptionLevel === SUBSCRIPTION_LEVELS.PREMIUM || subscriptionLevel === SUBSCRIPTION_LEVELS.BUSINESS) {
      const total = tierFeatures[contentType].totalModules || tierFeatures[contentType].totalChallenges || 0;
      return { total, used: 0, remaining: total, percentage: 100 };
    }

    // For free users, calculate remaining
    const usageKey = `${contentType}_used`;
    const usedCount = localStorage.getItem(usageKey) ? parseInt(localStorage.getItem(usageKey), 10) : 0;
    const total = tierFeatures[contentType].availableModules || tierFeatures[contentType].availableChallenges || 0;
    const remaining = Math.max(0, total - usedCount);
    const percentage = total > 0 ? Math.round((remaining / total) * 100) : 0;

    return { total, used: usedCount, remaining, percentage };
  };

  // Track content usage
  const trackContentUsage = (contentType, contentId) => {
    // Only track for free users
    if (subscriptionLevel !== SUBSCRIPTION_LEVELS.FREE) return true;

    // Check if we've already used this content
    const usedContentKey = `${contentType}_used_ids`;
    const usedIds = JSON.parse(localStorage.getItem(usedContentKey) || '[]');

    // If already used, don't count again
    if (usedIds.includes(contentId)) return true;

    // Update used count
    const usageKey = `${contentType}_used`;
    const usedCount = localStorage.getItem(usageKey) ? parseInt(localStorage.getItem(usageKey), 10) : 0;
    localStorage.setItem(usageKey, usedCount + 1);

    // Update used IDs
    usedIds.push(contentId);
    localStorage.setItem(usedContentKey, JSON.stringify(usedIds));

    // Check if we still have access
    return hasAccess(contentType);
  };

  // Check if a specific item requires coins to purchase
  const requiresCoins = (itemType, difficulty) => {
    if (subscriptionLevel === SUBSCRIPTION_LEVELS.BUSINESS) return false;

    if (itemType === 'challenge') {
      const tierFeatures = TIER_FEATURES[subscriptionLevel];
      if (tierFeatures?.challenges?.coinPurchaseRequired) {
        return CHALLENGE_COSTS[difficulty] > 0;
      }
    }

    return false;
  };

  // Get the cost of an item in coins
  const getItemCost = (itemType, difficulty) => {
    if (itemType === 'challenge') {
      return CHALLENGE_COSTS[difficulty] || 0;
    }
    return 0;
  };

  // Check if user has enough coins for an item
  const hasEnoughCoins = (itemType, difficulty) => {
    const cost = getItemCost(itemType, difficulty);
    return userCoins >= cost;
  };

  // Purchase an item with coins
  const purchaseWithCoins = async (itemType, itemId, difficulty) => {
    const cost = getItemCost(itemType, difficulty);

    if (userCoins < cost) {
      throw new Error('Not enough coins');
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('Not authenticated');

      // Update user coins in database
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ coins: userCoins - cost })
        .eq('user_id', session.user.id);

      if (error) throw error;

      // Record the purchase
      const { error: purchaseError } = await supabase
        .from('user_purchases')
        .insert({
          user_id: session.user.id,
          item_type: itemType,
          item_id: itemId,
          cost: cost,
          purchased_at: new Date().toISOString()
        });

      if (purchaseError) throw purchaseError;

      // Update local state
      setUserCoins(userCoins - cost);

      return true;
    } catch (error) {
      console.error('Error purchasing item:', error);
      throw error;
    }
  };

  // Add coins to user account (after purchase)
  const addCoins = async (amount) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('Not authenticated');

      // Update user coins in database
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ coins: userCoins + amount })
        .eq('user_id', session.user.id);

      if (error) throw error;

      // Update local state
      setUserCoins(userCoins + amount);

      return true;
    } catch (error) {
      console.error('Error adding coins:', error);
      throw error;
    }
  };

  // Upgrade subscription
  const upgradeSubscription = async (newTier) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('Not authenticated');

      // In a real app, this would integrate with a payment processor
      // For now, we'll just update the subscription in the database

      // First check if user already has a subscription
      const { data: existingSubscription } = await supabase
        .from('user_subscriptions')
        .select('id')
        .eq('user_id', session.user.id)
        .single();

      // Get the subscription plan ID
      const { data: planData } = await supabase
        .from('subscription_plans')
        .select('id')
        .eq('name', newTier)
        .single();

      if (!planData) throw new Error('Subscription plan not found');

      let result;

      if (existingSubscription) {
        // Update existing subscription
        result = await supabase
          .from('user_subscriptions')
          .update({
            plan_id: planData.id,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingSubscription.id);
      } else {
        // Create new subscription
        result = await supabase
          .from('user_subscriptions')
          .insert({
            user_id: session.user.id,
            plan_id: planData.id,
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
      }

      if (result.error) throw result.error;

      // Update local state
      setSubscriptionLevel(newTier);

      // Update local storage
      const updatedSubscription = {
        ...subscription,
        tier: newTier,
        updated_at: new Date().toISOString()
      };
      setSubscription(updatedSubscription);
      localStorage.setItem('user_subscription', JSON.stringify(updatedSubscription));

      return true;
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      throw error;
    }
  };

  // Update subscription from backend (for admin use)
  const updateSubscriptionFromBackend = async () => {
    try {
      // Check if we're logged in
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return false;

      // Fetch the latest subscription data from the database
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', session.user.id)
        .single();

      if (subscriptionError) throw subscriptionError;

      if (subscriptionData) {
        // Update state with the latest data
        setSubscription(subscriptionData);
        setSubscriptionLevel(subscriptionData.tier || SUBSCRIPTION_LEVELS.FREE);
        setUserCoins(subscriptionData.coins || 0);

        // Update localStorage
        localStorage.setItem('user_subscription', JSON.stringify(subscriptionData));

        return true;
      }

      return false;
    } catch (error) {
      console.error('Error updating subscription from backend:', error);
      return false;
    }
  };

  return (
    <SubscriptionContext.Provider
      value={{
        subscription,
        subscriptionLevel,
        userCoins,
        loading,
        error,
        // Subscription tier helpers
        tierFeatures: TIER_FEATURES[subscriptionLevel],
        isFree: subscriptionLevel === SUBSCRIPTION_LEVELS.FREE,
        isPremium: subscriptionLevel === SUBSCRIPTION_LEVELS.PREMIUM,
        isBusiness: subscriptionLevel === SUBSCRIPTION_LEVELS.BUSINESS,
        isSubscribed: subscriptionLevel !== SUBSCRIPTION_LEVELS.NONE,
        // Feature access methods
        hasAccess,
        getAvailableCount,
        getRemainingContent,
        trackContentUsage,
        // Coin-related methods
        requiresCoins,
        getItemCost,
        hasEnoughCoins,
        purchaseWithCoins,
        addCoins,
        // Subscription management
        upgradeSubscription,
        updateSubscriptionFromBackend,
        // Available packages
        coinPackages: COIN_PACKAGES,
        subscriptionTiers: Object.values(TIER_FEATURES)
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};