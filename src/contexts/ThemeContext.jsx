import React, { createContext, useContext, useState, useEffect } from 'react';
import { colors } from '../config/colors';

const ThemeContext = createContext();

export function ThemeProvider({ children }) {
  const [theme, setTheme] = useState({
    primary: colors.primary.DEFAULT,
    primaryHover: colors.primary.hover,
    primaryLight: colors.primary.light,
    primaryMedium: colors.primary.medium,
    primaryDark: colors.primary.dark,
    text: colors.text.primary,
    background: colors.background.primary
  });

  const updateTheme = (newTheme) => {
    setTheme(newTheme);
    // Update CSS variables
    Object.entries(newTheme).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--color-${key}`, value);
    });
  };

  useEffect(() => {
    // Initialize CSS variables
    Object.entries(theme).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--color-${key}`, value);
    });
  }, []);

  return (
    <ThemeContext.Provider value={{ theme, updateTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}