import React, { createContext, useContext, useState, useEffect } from 'react';

// Create the context
const GlobalThemeContext = createContext();

export const GlobalThemeProvider = ({ children }) => {
  // Check if user has a preference stored in localStorage
  const [darkMode, setDarkMode] = useState(() => {
    const savedTheme = localStorage.getItem('globalTheme');
    // If no preference is stored, use system preference
    if (savedTheme === null) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return savedTheme === 'dark';
  });

  // Toggle between dark and light mode
  const toggleDarkMode = () => {
    setDarkMode(prevMode => !prevMode);
  };

  // Update localStorage and apply theme when darkMode changes
  useEffect(() => {
    localStorage.setItem('globalTheme', darkMode ? 'dark' : 'light');

    // Apply theme to document
    if (darkMode) {
      document.documentElement.classList.add('dark');
      document.body.classList.add('dark-mode');
      document.body.style.backgroundColor = '#0B1120';
      document.body.style.color = '#ffffff';

      // Add CSS variables for theme colors
      document.documentElement.style.setProperty('--bg-primary', '#0B1120');
      document.documentElement.style.setProperty('--bg-secondary', '#1A1F35');
      document.documentElement.style.setProperty('--bg-tertiary', '#252D4A');
      document.documentElement.style.setProperty('--text-primary', '#ffffff');
      document.documentElement.style.setProperty('--text-secondary', '#94a3b8');
      document.documentElement.style.setProperty('--border-color', '#334155');
    } else {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('dark-mode');
      document.body.style.backgroundColor = '#ffffff';
      document.body.style.color = '#1a202c';

      // Add CSS variables for theme colors
      document.documentElement.style.setProperty('--bg-primary', '#ffffff');
      document.documentElement.style.setProperty('--bg-secondary', '#f8fafc');
      document.documentElement.style.setProperty('--bg-tertiary', '#f1f5f9');
      document.documentElement.style.setProperty('--text-primary', '#1e293b');
      document.documentElement.style.setProperty('--text-secondary', '#64748b');
      document.documentElement.style.setProperty('--border-color', '#e2e8f0');
    }
  }, [darkMode]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e) => {
      // Only update if user hasn't set a preference
      if (localStorage.getItem('globalTheme') === null) {
        setDarkMode(e.matches);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return (
    <GlobalThemeContext.Provider value={{ darkMode, toggleDarkMode }}>
      {children}
    </GlobalThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useGlobalTheme = () => {
  const context = useContext(GlobalThemeContext);
  if (!context) {
    throw new Error('useGlobalTheme must be used within a GlobalThemeProvider');
  }
  return context;
};
