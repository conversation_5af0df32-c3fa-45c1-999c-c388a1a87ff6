import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const StoreContext = createContext();

// Define coin packages
export const COIN_PACKAGES = [
  {
    id: 'basic',
    name: 'Basic Pack',
    coins: 100,
    price: 99,
    currency: 'INR',
    popular: false
  },
  {
    id: 'standard',
    name: 'Standard Pack',
    coins: 500,
    price: 399,
    currency: 'INR',
    popular: true
  },
  {
    id: 'premium',
    name: 'Premium Pack',
    coins: 1200,
    price: 799,
    currency: 'INR',
    popular: false
  },
  {
    id: 'ultimate',
    name: 'Ultimate Pack',
    coins: 3000,
    price: 1499,
    currency: 'INR',
    popular: false
  }
];

export function StoreProvider({ children }) {
  const { user, profile } = useAuth();
  const [storeItems, setStoreItems] = useState([]);
  const [userInventory, setUserInventory] = useState([]);
  const [userCoins, setUserCoins] = useState(0);
  const [purchaseHistory, setPurchaseHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch store items
  useEffect(() => {
    const fetchStoreItems = async () => {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('store_items')
          .select('*');
          
        if (error) throw error;
        
        setStoreItems(data);
      } catch (error) {
        console.error('Error fetching store items:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchStoreItems();
  }, []);
  
  // Fetch user inventory and coins
  useEffect(() => {
    if (!user) {
      setUserInventory([]);
      setUserCoins(0);
      setPurchaseHistory([]);
      return;
    }
    
    const fetchUserData = async () => {
      try {
        setLoading(true);
        
        // Fetch user inventory
        const { data: inventoryData, error: inventoryError } = await supabase
          .from('user_inventory')
          .select(`
            id,
            item_id,
            quantity,
            purchased_at,
            item:store_items(*)
          `)
          .eq('user_id', user.id);
          
        if (inventoryError) throw inventoryError;
        
        setUserInventory(inventoryData);
        
        // Fetch user coins
        setUserCoins(profile?.points || 0);
        
        // Fetch purchase history
        const { data: historyData, error: historyError } = await supabase
          .from('purchase_history')
          .select(`
            id,
            item_id,
            quantity,
            coins_spent,
            purchased_at,
            item:store_items(*)
          `)
          .eq('user_id', user.id)
          .order('purchased_at', { ascending: false });
          
        if (historyError) throw historyError;
        
        setPurchaseHistory(historyData);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserData();
  }, [user, profile]);
  
  // Purchase an item
  const purchaseItem = async (itemId, quantity = 1) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to make a purchase');
      
      // Get item details
      const item = storeItems.find(item => item.id === itemId);
      if (!item) throw new Error('Item not found');
      
      // Check if user has enough coins
      const totalCost = item.price * quantity;
      if (userCoins < totalCost) throw new Error('Not enough coins');
      
      // Check subscription requirements
      if (item.is_premium && profile?.subscription_tier === 'free') {
        throw new Error('This item requires a premium subscription');
      }
      
      if (item.is_business && profile?.subscription_tier !== 'business') {
        throw new Error('This item requires a business subscription');
      }
      
      // Update user coins
      const { error: coinsError } = await supabase.rpc('add_user_points', {
        user_id: user.id,
        points_to_add: -totalCost
      });
      
      if (coinsError) throw coinsError;
      
      // Record purchase
      const { error: purchaseError } = await supabase
        .from('purchase_history')
        .insert([{
          user_id: user.id,
          item_id: itemId,
          quantity,
          coins_spent: totalCost,
          purchased_at: new Date()
        }]);
        
      if (purchaseError) throw purchaseError;
      
      // Check if item already in inventory
      const existingItem = userInventory.find(inv => inv.item_id === itemId);
      
      if (existingItem) {
        // Update quantity
        const { error: inventoryError } = await supabase
          .from('user_inventory')
          .update({
            quantity: existingItem.quantity + quantity
          })
          .eq('id', existingItem.id);
          
        if (inventoryError) throw inventoryError;
      } else {
        // Add to inventory
        const { error: inventoryError } = await supabase
          .from('user_inventory')
          .insert([{
            user_id: user.id,
            item_id: itemId,
            quantity,
            purchased_at: new Date()
          }]);
          
        if (inventoryError) throw inventoryError;
      }
      
      // Update local state
      setUserCoins(userCoins - totalCost);
      
      // Refresh inventory and purchase history
      const { data: newInventory } = await supabase
        .from('user_inventory')
        .select(`
          id,
          item_id,
          quantity,
          purchased_at,
          item:store_items(*)
        `)
        .eq('user_id', user.id);
        
      setUserInventory(newInventory);
      
      const { data: newHistory } = await supabase
        .from('purchase_history')
        .select(`
          id,
          item_id,
          quantity,
          coins_spent,
          purchased_at,
          item:store_items(*)
        `)
        .eq('user_id', user.id)
        .order('purchased_at', { ascending: false });
        
      setPurchaseHistory(newHistory);
      
      return { success: true, message: `Successfully purchased ${quantity} ${item.name}` };
    } catch (error) {
      console.error('Error purchasing item:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Purchase coins
  const purchaseCoins = async (packageId) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to purchase coins');
      
      // Get package details
      const coinPackage = COIN_PACKAGES.find(pkg => pkg.id === packageId);
      if (!coinPackage) throw new Error('Package not found');
      
      // In a real app, this would integrate with a payment processor
      // For now, we'll just add the coins to the user's account
      
      // Update user coins
      const { error: coinsError } = await supabase.rpc('add_user_points', {
        user_id: user.id,
        points_to_add: coinPackage.coins
      });
      
      if (coinsError) throw coinsError;
      
      // Update local state
      setUserCoins(userCoins + coinPackage.coins);
      
      return { success: true, message: `Successfully purchased ${coinPackage.coins} coins` };
    } catch (error) {
      console.error('Error purchasing coins:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Use an item from inventory
  const useItem = async (inventoryId, quantity = 1) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to use an item');
      
      // Get inventory item
      const inventoryItem = userInventory.find(item => item.id === inventoryId);
      if (!inventoryItem) throw new Error('Item not found in inventory');
      
      if (inventoryItem.quantity < quantity) {
        throw new Error('Not enough items in inventory');
      }
      
      // Update quantity
      const newQuantity = inventoryItem.quantity - quantity;
      
      if (newQuantity > 0) {
        // Update quantity
        const { error: inventoryError } = await supabase
          .from('user_inventory')
          .update({
            quantity: newQuantity
          })
          .eq('id', inventoryId);
          
        if (inventoryError) throw inventoryError;
      } else {
        // Remove from inventory
        const { error: inventoryError } = await supabase
          .from('user_inventory')
          .delete()
          .eq('id', inventoryId);
          
        if (inventoryError) throw inventoryError;
      }
      
      // Update local state
      setUserInventory(prev => {
        if (newQuantity > 0) {
          return prev.map(item => 
            item.id === inventoryId 
              ? { ...item, quantity: newQuantity } 
              : item
          );
        } else {
          return prev.filter(item => item.id !== inventoryId);
        }
      });
      
      return { success: true, message: `Successfully used ${quantity} ${inventoryItem.item.name}` };
    } catch (error) {
      console.error('Error using item:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Get filtered store items
  const getFilteredItems = (filters = {}) => {
    let filteredItems = [...storeItems];
    
    // Filter by type
    if (filters.type) {
      filteredItems = filteredItems.filter(
        item => item.item_type === filters.type
      );
    }
    
    // Filter by search query
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredItems = filteredItems.filter(
        item => 
          item.name.toLowerCase().includes(searchLower) ||
          item.description.toLowerCase().includes(searchLower)
      );
    }
    
    // Filter by subscription tier
    if (!profile || profile.subscription_tier === 'free') {
      filteredItems = filteredItems.filter(
        item => !item.is_premium && !item.is_business
      );
    } else if (profile.subscription_tier === 'premium') {
      filteredItems = filteredItems.filter(
        item => !item.is_business
      );
    }
    
    return filteredItems;
  };

  // Context value
  const value = {
    storeItems,
    userInventory,
    userCoins,
    purchaseHistory,
    loading,
    error,
    purchaseItem,
    purchaseCoins,
    useItem,
    getFilteredItems,
    coinPackages: COIN_PACKAGES
  };

  return <StoreContext.Provider value={value}>{children}</StoreContext.Provider>;
}

// Custom hook to use the store context
export function useStore() {
  const context = useContext(StoreContext);
  if (context === undefined) {
    throw new Error('useStore must be used within a StoreProvider');
  }
  return context;
}
