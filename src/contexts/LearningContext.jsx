import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

// Create context
const LearningContext = createContext();

export function LearningProvider({ children }) {
  const { user, profile } = useAuth();
  const [modules, setModules] = useState([]);
  const [userProgress, setUserProgress] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch all learning modules based on user's subscription tier
  useEffect(() => {
    const fetchModules = async () => {
      try {
        setLoading(true);
        
        let query = supabase
          .from('learning_modules')
          .select(`
            id,
            title,
            description,
            difficulty,
            category,
            created_at,
            is_premium,
            is_business,
            author:profiles(username)
          `);
        
        const { data, error } = await query;
        
        if (error) throw error;
        
        setModules(data);
      } catch (error) {
        console.error('Error fetching learning modules:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchModules();
    
    // Set up real-time subscription for new modules
    const subscription = supabase
      .channel('public:learning_modules')
      .on('INSERT', (payload) => {
        setModules(prev => [...prev, payload.new]);
      })
      .on('UPDATE', (payload) => {
        setModules(prev => 
          prev.map(module => 
            module.id === payload.new.id ? payload.new : module
          )
        );
      })
      .on('DELETE', (payload) => {
        setModules(prev => 
          prev.filter(module => module.id !== payload.old.id)
        );
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [profile?.subscription_tier]);
  
  // Fetch user's module progress
  useEffect(() => {
    if (!user) {
      setUserProgress({});
      return;
    }
    
    const fetchUserProgress = async () => {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('module_progress')
          .select(`
            id,
            module_id,
            progress,
            last_activity,
            completed
          `)
          .eq('user_id', user.id);
          
        if (error) throw error;
        
        // Convert to object with module_id as key
        const progressObj = {};
        data.forEach(item => {
          progressObj[item.module_id] = item;
        });
        
        setUserProgress(progressObj);
      } catch (error) {
        console.error('Error fetching user progress:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserProgress();
    
    // Set up real-time subscription for user's module progress
    const subscription = supabase
      .channel(`user-progress-${user.id}`)
      .on('INSERT', (payload) => {
        setUserProgress(prev => ({
          ...prev,
          [payload.new.module_id]: payload.new
        }));
      })
      .on('UPDATE', (payload) => {
        setUserProgress(prev => ({
          ...prev,
          [payload.new.module_id]: payload.new
        }));
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [user]);
  
  // Get a single module by ID
  const getModuleById = async (id) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('learning_modules')
        .select(`
          id,
          title,
          description,
          difficulty,
          category,
          content,
          created_at,
          is_premium,
          is_business,
          author:profiles(username)
        `)
        .eq('id', id)
        .single();
        
      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Error fetching module:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Update user's progress on a module
  const updateProgress = async (moduleId, progress, completed = false) => {
    try {
      setLoading(true);
      
      if (!user) throw new Error('You must be logged in to update progress');
      
      // Check if progress record exists
      const existingProgress = userProgress[moduleId];
      
      if (existingProgress) {
        // Update existing progress
        const { data, error } = await supabase
          .from('module_progress')
          .update({
            progress,
            completed,
            last_activity: new Date()
          })
          .eq('id', existingProgress.id)
          .select()
          .single();
          
        if (error) throw error;
        
        setUserProgress(prev => ({
          ...prev,
          [moduleId]: data
        }));
        
        return data;
      } else {
        // Create new progress record
        const { data, error } = await supabase
          .from('module_progress')
          .insert([{
            module_id: moduleId,
            user_id: user.id,
            progress,
            completed,
            last_activity: new Date()
          }])
          .select()
          .single();
          
        if (error) throw error;
        
        setUserProgress(prev => ({
          ...prev,
          [moduleId]: data
        }));
        
        return data;
      }
    } catch (error) {
      console.error('Error updating progress:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Check if a module is completed by the user
  const isModuleCompleted = (moduleId) => {
    return userProgress[moduleId]?.completed || false;
  };
  
  // Get user's progress on a module
  const getModuleProgress = (moduleId) => {
    return userProgress[moduleId]?.progress || 0;
  };
  
  // Get filtered modules
  const getFilteredModules = (filters = {}) => {
    let filteredModules = [...modules];
    
    // Filter by difficulty
    if (filters.difficulty) {
      filteredModules = filteredModules.filter(
        module => module.difficulty === filters.difficulty
      );
    }
    
    // Filter by category
    if (filters.category) {
      filteredModules = filteredModules.filter(
        module => module.category === filters.category
      );
    }
    
    // Filter by search query
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredModules = filteredModules.filter(
        module => 
          module.title.toLowerCase().includes(searchLower) ||
          module.description.toLowerCase().includes(searchLower)
      );
    }
    
    // Filter by subscription tier
    if (!profile || profile.subscription_tier === 'free') {
      filteredModules = filteredModules.filter(
        module => !module.is_premium && !module.is_business
      );
    } else if (profile.subscription_tier === 'premium') {
      filteredModules = filteredModules.filter(
        module => !module.is_business
      );
    }
    
    // Filter by completion status
    if (filters.completed === true) {
      filteredModules = filteredModules.filter(
        module => isModuleCompleted(module.id)
      );
    } else if (filters.completed === false) {
      filteredModules = filteredModules.filter(
        module => !isModuleCompleted(module.id)
      );
    }
    
    return filteredModules;
  };
  
  // Get learning path (ordered sequence of modules)
  const getLearningPath = (category) => {
    // Filter modules by category
    let pathModules = modules.filter(module => module.category === category);
    
    // Sort by difficulty level
    const difficultyOrder = {
      'beginner': 1,
      'intermediate': 2,
      'advanced': 3,
      'expert': 4
    };
    
    pathModules = pathModules.sort((a, b) => 
      difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]
    );
    
    // Add progress information
    return pathModules.map(module => ({
      ...module,
      progress: getModuleProgress(module.id),
      completed: isModuleCompleted(module.id)
    }));
  };

  // Context value
  const value = {
    modules,
    userProgress,
    loading,
    error,
    getModuleById,
    updateProgress,
    isModuleCompleted,
    getModuleProgress,
    getFilteredModules,
    getLearningPath
  };

  return <LearningContext.Provider value={value}>{children}</LearningContext.Provider>;
}

// Custom hook to use the learning context
export function useLearning() {
  const context = useContext(LearningContext);
  if (context === undefined) {
    throw new Error('useLearning must be used within a LearningProvider');
  }
  return context;
}
