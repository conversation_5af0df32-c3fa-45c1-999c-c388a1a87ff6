const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Auth service proxy
  app.use(
    '/api/auth',
    createProxyMiddleware({
      target: 'http://localhost:3001',
      changeOrigin: true,
    })
  );

  // Learning service proxy
  app.use(
    '/api/learning',
    createProxyMiddleware({
      target: 'http://localhost:3002',
      changeOrigin: true,
    })
  );

  // Challenges service proxy
  app.use(
    '/api/challenges',
    createProxyMiddleware({
      target: 'http://localhost:3003',
      changeOrigin: true,
    })
  );

  // AI service proxy
  app.use(
    '/api/generate',
    createProxyMiddleware({
      target: 'http://localhost:3004',
      changeOrigin: true,
    })
  );

  // Threats service proxy
  app.use(
    '/api/threats',
    createProxyMiddleware({
      target: 'http://localhost:3005',
      changeOrigin: true,
    })
  );
};
