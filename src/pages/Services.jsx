import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaShieldAlt, FaGraduationCap, FaUsers, FaLaptopCode,
  FaServer, FaNetworkWired, FaLock, FaDatabase,
  FaUserSecret, FaBug, FaCloudDownloadAlt, FaCode,
  FaBookOpen, FaLightbulb, FaHandshake, FaChalkboardTeacher,
  FaBriefcase, FaUserTie, FaBuilding, FaFileAlt,
  FaComments, FaClipboardCheck, FaTools, FaChartLine
} from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import CyberForceSEO from '../components/common/CyberForceSEO';

function Services() {
  const { darkMode } = useGlobalTheme();
  const [activeService, setActiveService] = useState(null);

  // Service categories
  const serviceCategories = [
    {
      id: 'programs',
      name: 'Programs and Training',
      icon: <FaGraduationCap className="text-2xl" />,
      color: '#4A5CBA',
      description: 'Comprehensive cybersecurity training programs and bootcamps',
      services: [
        {
          title: 'Cybersecurity Bootcamps',
          description: 'Intensive training programs tailored to your organization\'s specific needs',
          icon: <FaBookOpen />,
          features: ['Customized curriculum', 'Industry-specific training', 'Hands-on exercises', 'Real-world scenarios']
        },
        {
          title: 'GRC Programs',
          description: 'Governance, Risk Management, and Compliance training for corporate environments',
          icon: <FaClipboardCheck />,
          features: ['Regulatory compliance', 'Risk assessment', 'Policy development', 'Customized GRC plans']
        },
        {
          title: 'Industry-Recognized Certifications',
          description: 'Training programs aligned with international certifications and standards',
          icon: <FaFileAlt />,
          features: ['Certification preparation', 'Industry-standard methodologies', 'Expert instructors', 'Practical application']
        }
      ]
    },
    {
      id: 'awareness',
      name: 'Awareness and Workshops',
      icon: <FaLightbulb className="text-2xl" />,
      color: '#F5B93F',
      description: 'Security awareness events and specialized workshops',
      services: [
        {
          title: 'Security Awareness Events',
          description: 'Educational events that promote a proactive and watchful attitude to cybersecurity',
          icon: <FaChalkboardTeacher />,
          features: ['Knowledge sharing', 'Practical demonstrations', 'Interactive sessions', 'Customized for all audiences']
        },
        {
          title: 'Security Workshops',
          description: 'Hands-on workshops addressing multiple facets of security',
          icon: <FaTools />,
          features: ['Cybersecurity practices', 'Physical security', 'Data protection', 'Incident response']
        },
        {
          title: 'Executive Briefings',
          description: 'Concise presentations for executives on digital assets, reputation, and financial risks',
          icon: <FaHandshake />,
          features: ['High-level insights', 'Risk assessment', 'Strategic planning', 'Executive-focused content']
        }
      ]
    },
    {
      id: 'manpower',
      name: 'Manpower Solutions',
      icon: <FaUserTie className="text-2xl" />,
      color: '#8B5CF6',
      description: 'Specialized cybersecurity workforce and consulting services',
      services: [
        {
          title: 'GRC Outsourcing',
          description: 'Strategic approach to enhance your GRC capabilities with specialized external providers',
          icon: <FaBuilding />,
          features: ['Focus on core competencies', 'Access to GRC specialists', 'Enhanced compliance', 'Cost-effective solutions']
        },
        {
          title: 'Cybersecurity Manpower Consultation',
          description: 'Specialized advisory service to optimize your cybersecurity workforce',
          icon: <FaBriefcase />,
          features: ['Workforce assessment', 'Skill gap analysis', 'Recruitment strategy', 'Team structure optimization']
        },
        {
          title: 'Security Operations Support',
          description: 'Ongoing operational support for your security team',
          icon: <FaChartLine />,
          features: ['24/7 monitoring', 'Incident response', 'Threat intelligence', 'Performance optimization']
        }
      ]
    },
    {
      id: 'training',
      name: 'Specialized Training',
      icon: <FaCode className="text-2xl" />,
      color: '#10B981',
      description: 'Advanced technical training for cybersecurity professionals',
      services: [
        {
          title: 'Cybersecurity Fundamentals',
          description: 'Master the core concepts and principles of cybersecurity',
          icon: <FaShieldAlt />,
          features: ['Network security basics', 'Threat identification', 'Security best practices', 'Risk assessment fundamentals']
        },
        {
          title: 'Advanced Penetration Testing',
          description: 'Learn ethical hacking techniques to identify and exploit vulnerabilities',
          icon: <FaUserSecret />,
          features: ['Vulnerability scanning', 'Exploit development', 'Post-exploitation techniques', 'Reporting methodologies']
        },
        {
          title: 'Secure Coding Practices',
          description: 'Develop software with security built in from the ground up',
          icon: <FaCode />,
          features: ['OWASP Top 10 vulnerabilities', 'Secure SDLC', 'Code review techniques', 'Security testing']
        }
      ]
    },
    {
      id: 'simulations',
      name: 'Attack Simulations',
      icon: <FaLaptopCode className="text-2xl" />,
      color: '#EC4899',
      description: 'Realistic cybersecurity attack and defense simulations',
      services: [
        {
          title: 'Red Team Exercises',
          description: 'Simulate real-world attacks to test your defenses',
          icon: <FaBug />,
          features: ['Advanced persistent threats', 'Social engineering', 'Physical security testing', 'Custom attack scenarios']
        },
        {
          title: 'Blue Team Defense',
          description: 'Practice detecting and responding to cyber attacks',
          icon: <FaShieldAlt />,
          features: ['Threat hunting', 'Incident response', 'Log analysis', 'Forensic investigation']
        },
        {
          title: 'Capture The Flag (CTF)',
          description: 'Competitive cybersecurity challenges to test your skills',
          icon: <FaLock />,
          features: ['Web exploitation', 'Reverse engineering', 'Cryptography', 'Binary exploitation']
        }
      ]
    },
    {
      id: 'consulting',
      name: 'Security Consulting',
      icon: <FaUsers className="text-2xl" />,
      color: '#3B82F6',
      description: 'Expert cybersecurity consulting services for organizations',
      services: [
        {
          title: 'Security Assessment',
          description: 'Comprehensive evaluation of your security posture',
          icon: <FaCloudDownloadAlt />,
          features: ['Vulnerability assessment', 'Policy review', 'Compliance checking', 'Risk analysis']
        },
        {
          title: 'Incident Response Planning',
          description: 'Develop effective strategies for handling security incidents',
          icon: <FaServer />,
          features: ['Response playbooks', 'Team training', 'Communication plans', 'Recovery procedures']
        },
        {
          title: 'Security Architecture',
          description: 'Design secure systems and networks from the ground up',
          icon: <FaNetworkWired />,
          features: ['Zero trust architecture', 'Defense in depth', 'Secure cloud design', 'Identity management']
        }
      ]
    }
  ];

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-20`}>
      {/* SEO */}
      <CyberForceSEO
        title="Services"
        description="Explore CyberForce's comprehensive cybersecurity services including training programs, attack simulations, security consulting, and specialized workshops."
        keywords={['cybersecurity services', 'security training', 'attack simulations', 'security consulting', 'Oman']}
        canonicalUrl="https://cyberforce.om/services"
      />

      {/* Hero Section */}
      <div className={`${darkMode ? 'bg-[#0B1120]' : 'bg-[#0F172A]'} text-white py-20 relative overflow-hidden`}>
        {/* Cybersecurity Graphics Background */}
        <div className="absolute inset-0 z-0 opacity-20">
          {/* Circuit Board Pattern */}
          <svg className="absolute top-0 left-0 w-full h-full" width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <pattern id="circuit-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
              <path d="M0,0 L20,0 L20,20 L0,20 Z" fill="none" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="20" cy="20" r="2" fill="#4A5CBA" />
              <path d="M20,20 L40,20" stroke="#4A5CBA" strokeWidth="0.5" />
              <path d="M20,20 L20,40" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="40" cy="20" r="2" fill="#4A5CBA" />
              <path d="M40,20 L60,20" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="20" cy="40" r="2" fill="#4A5CBA" />
              <path d="M20,40 L20,60" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="60" cy="20" r="2" fill="#F5B93F" />
              <path d="M60,20 L60,40" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="60" cy="40" r="2" fill="#F5B93F" />
              <path d="M60,40 L40,40" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="40" cy="40" r="2" fill="#F5B93F" />
              <path d="M40,40 L40,60" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="20" cy="60" r="2" fill="#4A5CBA" />
              <path d="M20,60 L40,60" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="40" cy="60" r="2" fill="#F5B93F" />
              <path d="M40,60 L60,60" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="60" cy="60" r="2" fill="#F5B93F" />
              <path d="M60,60 L80,60" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="80" cy="60" r="2" fill="#4A5CBA" />
              <path d="M80,60 L80,80" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="80" cy="80" r="2" fill="#4A5CBA" />
              <path d="M80,80 L60,80" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="60" cy="80" r="2" fill="#F5B93F" />
              <path d="M60,80 L40,80" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="40" cy="80" r="2" fill="#F5B93F" />
              <path d="M40,80 L20,80" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="20" cy="80" r="2" fill="#4A5CBA" />
              <path d="M20,80 L0,80" stroke="#4A5CBA" strokeWidth="0.5" />
            </pattern>
            <rect x="0" y="0" width="100%" height="100%" fill="url(#circuit-pattern)" />
          </svg>
        </div>

        {/* Floating Cybersecurity Elements */}
        <div className="absolute inset-0 z-0">
          {/* Shield Icon */}
          <svg className="absolute top-[15%] left-[10%] w-24 h-24 opacity-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C12 22 20 18 20 12V5L12 2L4 5V12C4 18 12 22 12 22Z" stroke="#F5B93F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>

          {/* Lock Icon */}
          <svg className="absolute top-[60%] left-[15%] w-16 h-16 opacity-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="11" width="18" height="11" rx="2" stroke="#4A5CBA" strokeWidth="2"/>
            <path d="M7 11V7C7 4.23858 9.23858 2 12 2C14.7614 2 17 4.23858 17 7V11" stroke="#4A5CBA" strokeWidth="2"/>
          </svg>

          {/* Code Icon */}
          <svg className="absolute top-[25%] right-[15%] w-20 h-20 opacity-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 18L22 12L16 6" stroke="#F5B93F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 6L2 12L8 18" stroke="#F5B93F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>

          {/* Server Icon */}
          <svg className="absolute top-[65%] right-[10%] w-16 h-16 opacity-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="2" y="3" width="20" height="6" rx="1" stroke="#4A5CBA" strokeWidth="2"/>
            <rect x="2" y="9" width="20" height="6" rx="1" stroke="#4A5CBA" strokeWidth="2"/>
            <rect x="2" y="15" width="20" height="6" rx="1" stroke="#4A5CBA" strokeWidth="2"/>
            <circle cx="6" cy="6" r="1" fill="#4A5CBA"/>
            <circle cx="6" cy="12" r="1" fill="#4A5CBA"/>
            <circle cx="6" cy="18" r="1" fill="#4A5CBA"/>
          </svg>
        </div>

        {/* Animated Particles */}
        <div className="absolute inset-0 z-0">
          {[...Array(25)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full"
              style={{
                width: `${Math.random() * 6 + 2}px`,
                height: `${Math.random() * 6 + 2}px`,
                backgroundColor: i % 2 === 0 ? '#4A5CBA' : '#F5B93F',
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                opacity: 0.4,
                animation: `float-particle ${Math.random() * 10 + 15}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 5}s`
              }}
            />
          ))}
        </div>

        {/* Digital Network Lines */}
        <div className="absolute inset-0 z-0 opacity-10">
          <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern id="network-pattern" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
                {/* Horizontal Lines */}
                <line x1="0" y1="50" x2="200" y2="50" stroke="#4A5CBA" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-slow" />
                <line x1="0" y1="100" x2="200" y2="100" stroke="#F5B93F" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-medium" />
                <line x1="0" y1="150" x2="200" y2="150" stroke="#4A5CBA" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-slow" />

                {/* Vertical Lines */}
                <line x1="50" y1="0" x2="50" y2="200" stroke="#F5B93F" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-medium" />
                <line x1="100" y1="0" x2="100" y2="200" stroke="#4A5CBA" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-slow" />
                <line x1="150" y1="0" x2="150" y2="200" stroke="#F5B93F" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-medium" />

                {/* Diagonal Lines */}
                <line x1="0" y1="0" x2="200" y2="200" stroke="#4A5CBA" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-slow" />
                <line x1="200" y1="0" x2="0" y2="200" stroke="#F5B93F" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-medium" />
              </pattern>
            </defs>
            <rect x="0" y="0" width="100%" height="100%" fill="url(#network-pattern)" />
          </svg>
        </div>

        {/* Glowing Orbs */}
        <div className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-[#4A5CBA]/10 blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-64 h-64 rounded-full bg-[#F5B93F]/10 blur-3xl"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col md:flex-row items-center justify-between">
            {/* Text Content */}
            <div className="md:w-1/2 text-center md:text-left mb-10 md:mb-0">
              <h1 className="text-5xl font-bold mb-6">Our <span className="text-[#F5B93F]">Services</span></h1>
              <p className="text-gray-100 text-xl mb-10 leading-relaxed">
                Comprehensive cybersecurity services to protect, train, and empower your organization in an ever-evolving threat landscape
              </p>
              <div className="flex flex-wrap justify-center md:justify-start gap-4">
                <button onClick={() => setActiveService('programs')} className="bg-[#F5B93F] hover:bg-[#E5A92F] text-[#1A1F35] font-medium px-6 py-3 rounded-lg transition-all duration-300">
                  Explore Programs
                </button>
                <button onClick={() => document.getElementById('services-section').scrollIntoView({ behavior: 'smooth' })} className="bg-transparent hover:bg-white/10 border border-white text-white font-medium px-6 py-3 rounded-lg transition-all duration-300">
                  View All Services
                </button>
              </div>
            </div>

            {/* 3D Cybersecurity Visualization */}
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-[280px] h-[280px] perspective-1200">
                {/* Outer Ring */}
                <div
                  className="absolute inset-0 border-2 border-dashed rounded-full"
                  style={{
                    borderColor: '#4A5CBA',
                    animation: 'spin-forward 20s linear infinite'
                  }}
                >
                  {/* Nodes on Outer Ring */}
                  {[...Array(6)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-3 h-3 rounded-full bg-[#4A5CBA]"
                      style={{
                        top: `${50 + 45 * Math.sin(i * Math.PI / 3)}%`,
                        left: `${50 + 45 * Math.cos(i * Math.PI / 3)}%`,
                        transform: 'translate(-50%, -50%)',
                        boxShadow: '0 0 10px #4A5CBA'
                      }}
                    />
                  ))}
                </div>

                {/* Middle Ring */}
                <div
                  className="absolute inset-[15%] border-2 border-dashed rounded-full"
                  style={{
                    borderColor: '#F5B93F',
                    animation: 'spin-reverse 15s linear infinite'
                  }}
                >
                  {/* Nodes on Middle Ring */}
                  {[...Array(4)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-3 h-3 rounded-full bg-[#F5B93F]"
                      style={{
                        top: `${50 + 35 * Math.sin(i * Math.PI / 2)}%`,
                        left: `${50 + 35 * Math.cos(i * Math.PI / 2)}%`,
                        transform: 'translate(-50%, -50%)',
                        boxShadow: '0 0 10px #F5B93F'
                      }}
                    />
                  ))}
                </div>

                {/* Inner Ring */}
                <div
                  className="absolute inset-[30%] border-2 border-dashed rounded-full"
                  style={{
                    borderColor: '#4A5CBA',
                    animation: 'spin-forward 10s linear infinite'
                  }}
                >
                  {/* Nodes on Inner Ring */}
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-2 h-2 rounded-full bg-[#4A5CBA]"
                      style={{
                        top: `${50 + 20 * Math.sin(i * Math.PI * 2 / 3)}%`,
                        left: `${50 + 20 * Math.cos(i * Math.PI * 2 / 3)}%`,
                        transform: 'translate(-50%, -50%)',
                        boxShadow: '0 0 8px #4A5CBA'
                      }}
                    />
                  ))}
                </div>

                {/* Center Shield */}
                <div className="absolute inset-[42%] flex items-center justify-center">
                  <div className="w-full h-full bg-gradient-to-br from-[#4A5CBA] to-[#F5B93F] rounded-full flex items-center justify-center animate-pulse-medium">
                    <div className="w-[80%] h-[80%] bg-[#0B1120] rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 22C12 22 20 18 20 12V5L12 2L4 5V12C4 18 12 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M9 12L11 14L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Services Section */}
      <div id="services-section" className="container mx-auto px-4 py-20">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
              Comprehensive <span className="text-[#4A5CBA]">Cyber</span><span className="text-[#F5B93F]">Force</span> Services
            </h2>
            <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} max-w-3xl mx-auto`}>
              Select a service category below to explore our offerings designed to strengthen your organization's security posture
            </p>
          </div>

          {/* Service Categories */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
            {serviceCategories.map((category) => (
              <motion.button
                key={category.id}
                whileHover={{ scale: 1.02, y: -5 }}
                whileTap={{ scale: 0.98 }}
                className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} p-6 rounded-xl shadow-md border ${
                  darkMode ? 'border-gray-800' : 'border-gray-200'
                } text-left transition-all duration-300 ${
                  activeService === category.id ? `ring-2 ring-${category.color} shadow-lg shadow-${category.color}/20` : ''
                } relative overflow-hidden`}
                onClick={() => setActiveService(activeService === category.id ? null : category.id)}
              >
                {/* Top accent bar */}
                <div className="absolute top-0 left-0 w-full h-1" style={{ backgroundColor: category.color }}></div>

                {/* Background gradient */}
                <div
                  className="absolute inset-0 opacity-5"
                  style={{
                    background: `radial-gradient(circle at top right, ${category.color}, transparent 70%)`
                  }}
                ></div>

                <div className="relative z-10">
                  <div className="flex items-center mb-4">
                    <div
                      className={`w-14 h-14 rounded-lg flex items-center justify-center mr-4`}
                      style={{ backgroundColor: `${category.color}20` }}
                    >
                      <div className="text-2xl" style={{ color: category.color }}>
                        {category.icon}
                      </div>
                    </div>
                    <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{category.name}</h3>
                  </div>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>{category.description}</p>
                  <div className={`text-sm font-medium flex items-center`} style={{ color: category.color }}>
                    {activeService === category.id ? 'Click to collapse' : 'Click to explore'}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </motion.button>
            ))}
          </div>

          {/* Active Service Details */}
          <AnimatePresence>
            {activeService && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <AnimatedServiceDetails
                  activeService={activeService}
                  serviceCategories={serviceCategories}
                  darkMode={darkMode}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>


    </div>
  );
}

// Animated Service Details Component
function AnimatedServiceDetails({ activeService, serviceCategories, darkMode }) {
  const activeCategory = serviceCategories.find(category => category.id === activeService);

  if (!activeCategory) return null;

  return (
    <div className="mb-16">
      <div className="flex items-center mb-8">
        <div
          className="w-1.5 h-12 rounded-full mr-4"
          style={{ backgroundColor: activeCategory.color }}
        ></div>
        <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          {activeCategory.name}
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {activeCategory.services.map((service, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ y: -5, boxShadow: `0 10px 25px -5px ${activeCategory.color}20` }}
            className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} p-6 rounded-xl shadow-md border ${
              darkMode ? 'border-gray-800' : 'border-gray-200'
            } transition-all duration-300 relative overflow-hidden`}
          >
            {/* Top accent */}
            <div className="absolute top-0 left-0 w-full h-1" style={{ backgroundColor: activeCategory.color }}></div>

            {/* Content */}
            <div className="pt-3">
              <div className="flex items-center mb-4">
                <div
                  className={`w-12 h-12 rounded-lg flex items-center justify-center mr-4`}
                  style={{ backgroundColor: `${activeCategory.color}15` }}
                >
                  <div className="text-xl" style={{ color: activeCategory.color }}>
                    {service.icon}
                  </div>
                </div>
                <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{service.title}</h3>
              </div>

              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} text-sm mb-5`}>{service.description}</p>

              <div className="space-y-2 border-t border-dashed pt-4 mt-4" style={{ borderColor: `${activeCategory.color}30` }}>
                {service.features.map((feature, idx) => (
                  <div key={idx} className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        style={{ color: activeCategory.color }}
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <span className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{feature}</span>
                  </div>
                ))}
              </div>

              {/* Learn More Link */}
              <div className="mt-6 text-right">
                <a
                  href="#"
                  className="text-xs font-medium inline-flex items-center"
                  style={{ color: activeCategory.color }}
                >
                  Learn more
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}

export default Services;
