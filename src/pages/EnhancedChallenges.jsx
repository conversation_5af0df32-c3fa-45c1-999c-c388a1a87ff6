import React from 'react';
import { useParams } from 'react-router-dom';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { EnhancedChallengeProvider } from '../contexts/EnhancedChallengeContext';
import EnhancedChallengeList from '../components/challenges/EnhancedChallengeList';
import EnhancedChallengeDetail from '../components/challenges/EnhancedChallengeDetail';

const EnhancedChallenges = () => {
  const { darkMode } = useGlobalTheme();
  const { challengeId } = useParams();

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <EnhancedChallengeProvider>
          <h1 className="text-3xl font-bold mb-6">
            {challengeId ? 'Challenge' : 'Cybersecurity Challenges'}
          </h1>
          
          {challengeId ? (
            <EnhancedChallengeDetail />
          ) : (
            <EnhancedChallengeList />
          )}
        </EnhancedChallengeProvider>
      </div>
    </div>
  );
};

export default EnhancedChallenges;
