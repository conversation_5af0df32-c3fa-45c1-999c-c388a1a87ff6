import React, { useState, useEffect } from 'react';
import { FaBlog, FaCalendarAlt, FaUser, FaTag, FaSearch, FaRss } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import Card from '../components/ui/Card';
import BlogGenerationService from '../services/BlogGenerationService';

const Blog = () => {
  const { darkMode } = useGlobalTheme();
  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState('');

  // Load blog posts
  useEffect(() => {
    const loadBlogPosts = async () => {
      setLoading(true);
      try {
        // Try to load posts from localStorage first (these would be from the BlogGenerationService)
        const storedPosts = localStorage.getItem('xcerberus_blog_posts');
        let posts = [];

        if (storedPosts) {
          try {
            posts = JSON.parse(storedPosts);
            console.log('Loaded blog posts from localStorage:', posts);
          } catch (e) {
            console.error('Error parsing stored blog posts:', e);
          }
        }

        // If no stored posts or parsing failed, use sample posts
        if (!posts || posts.length === 0) {
          posts = generateSamplePosts();
          console.log('Using sample blog posts');
        }

        setBlogPosts(posts);
      } catch (error) {
        console.error('Error loading blog posts:', error);
        // Fallback to sample posts
        setBlogPosts(generateSamplePosts());
      } finally {
        setLoading(false);
      }
    };

    loadBlogPosts();

    // Set up an interval to check for new posts every minute
    const checkInterval = setInterval(() => {
      try {
        const storedPosts = localStorage.getItem('xcerberus_blog_posts');
        if (storedPosts) {
          const posts = JSON.parse(storedPosts);
          setBlogPosts(posts);
        }
      } catch (e) {
        console.error('Error checking for new blog posts:', e);
      }
    }, 60000); // Check every minute

    return () => clearInterval(checkInterval);
  }, []);

  // Generate sample blog posts
  const generateSamplePosts = () => {
    const posts = [];
    const now = new Date();

    // Current post (most recent)
    posts.push({
      id: 'post-1',
      title: 'Global Threat Intelligence Report: Ransomware Surge Targeting Healthcare',
      content: `
        <h2>Executive Summary</h2>
        <p>Our threat intelligence team has identified a significant 73% increase in ransomware attacks targeting healthcare organizations over the past 30 days. This surge appears to be part of a coordinated campaign originating primarily from threat actors in Eastern Europe.</p>

        <h2>Key Findings</h2>
        <ul>
          <li>73% increase in ransomware attacks targeting healthcare organizations</li>
          <li>New zero-day vulnerability discovered in widely-used networking equipment</li>
          <li>Coordinated DDoS campaign affecting multiple financial institutions</li>
          <li>Advanced persistent threat (APT) group activity increased by 28%</li>
        </ul>

        <h2>Attack Vector Analysis</h2>
        <p>The primary attack vectors include phishing emails with malicious attachments, exploitation of unpatched VPN vulnerabilities, and compromised third-party service providers. The attackers are using sophisticated social engineering techniques to bypass security awareness training.</p>

        <h2>Recommendations</h2>
        <p>Organizations should immediately patch systems, implement multi-factor authentication, and review incident response plans. Security teams should increase monitoring for the specific indicators of compromise detailed in the full report.</p>
      `,
      summary: 'Our threat intelligence team has identified a significant 73% increase in ransomware attacks targeting healthcare organizations over the past 30 days.',
      author: 'XCerberus Research Team',
      date: now.toISOString(),
      tags: ['Ransomware', 'Healthcare', 'Zero-day', 'APT'],
      image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
    });

    // 4 hours ago
    const fourHoursAgo = new Date(now.getTime() - 4 * 60 * 60 * 1000);
    posts.push({
      id: 'post-2',
      title: 'Financial Sector Under Attack: Coordinated DDoS Campaign Analysis',
      content: `
        <h2>Executive Summary</h2>
        <p>A coordinated Distributed Denial of Service (DDoS) campaign has been detected targeting major financial institutions across North America and Europe. The attacks appear to be using a botnet of compromised IoT devices to generate traffic volumes exceeding 1 Tbps.</p>

        <h2>Attack Pattern</h2>
        <p>The attacks follow a distinctive pattern, beginning with a small probing attack followed by a massive volumetric attack 15-20 minutes later. This suggests reconnaissance followed by targeted disruption.</p>

        <h2>Geographic Distribution</h2>
        <p>Attack traffic is originating primarily from compromised devices in Southeast Asia, Eastern Europe, and South America. The command and control infrastructure appears to be hosted on bulletproof hosting providers in multiple jurisdictions.</p>

        <h2>Mitigation Strategies</h2>
        <p>Financial institutions should implement rate limiting, traffic filtering at the network edge, and consider engaging with DDoS protection services. Sharing attack signatures with industry partners is recommended to improve collective defense.</p>
      `,
      summary: 'A coordinated Distributed Denial of Service (DDoS) campaign has been detected targeting major financial institutions across North America and Europe.',
      author: 'XCerberus Research Team',
      date: fourHoursAgo.toISOString(),
      tags: ['DDoS', 'Financial', 'Botnet', 'IoT'],
      image: 'https://images.unsplash.com/photo-**********-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
    });

    // 8 hours ago
    const eightHoursAgo = new Date(now.getTime() - 8 * 60 * 60 * 1000);
    posts.push({
      id: 'post-3',
      title: 'Critical Infrastructure Vulnerability Alert: Energy Sector at Risk',
      content: `
        <h2>Executive Summary</h2>
        <p>Our threat intelligence has identified a new vulnerability affecting SCADA systems commonly used in energy sector infrastructure. The vulnerability allows for remote code execution without authentication and is being actively exploited in the wild.</p>

        <h2>Technical Details</h2>
        <p>The vulnerability (CVE-2023-XXXXX) affects the authentication mechanism in SCADA control systems, allowing attackers to bypass security controls and execute arbitrary code with system privileges.</p>

        <h2>Observed Exploitation</h2>
        <p>We have detected exploitation attempts originating from multiple threat actors, including known APT groups with ties to nation-state activities. The attacks are highly targeted and focused on energy production and distribution facilities.</p>

        <h2>Immediate Actions Required</h2>
        <p>Energy sector organizations should immediately apply the vendor-provided patch or implement the recommended workarounds. Network segmentation should be verified, and enhanced monitoring should be implemented for affected systems.</p>
      `,
      summary: 'Our threat intelligence has identified a new vulnerability affecting SCADA systems commonly used in energy sector infrastructure.',
      author: 'XCerberus Research Team',
      date: eightHoursAgo.toISOString(),
      tags: ['SCADA', 'Energy', 'Critical Infrastructure', 'Vulnerability'],
      image: 'https://images.unsplash.com/photo-1605792657660-596af9009e82?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1402&q=80'
    });

    // 12 hours ago
    const twelveHoursAgo = new Date(now.getTime() - 12 * 60 * 60 * 1000);
    posts.push({
      id: 'post-4',
      title: 'Supply Chain Attack Campaign: Software Repositories Compromised',
      content: `
        <h2>Executive Summary</h2>
        <p>A sophisticated supply chain attack has been detected targeting popular open-source software repositories. Malicious code has been injected into several widely-used development libraries, potentially affecting thousands of downstream applications.</p>

        <h2>Attack Methodology</h2>
        <p>The attackers have compromised developer accounts and injected malicious code that exfiltrates sensitive data while maintaining the original functionality of the libraries. The code is obfuscated to avoid detection by automated scanning tools.</p>

        <h2>Affected Packages</h2>
        <p>The following packages have been confirmed as compromised: [list of package names and versions]. These packages are commonly used in web development, data processing, and cloud infrastructure management.</p>

        <h2>Recommended Actions</h2>
        <p>Organizations should immediately audit their software dependencies, verify the integrity of installed packages, and implement software composition analysis as part of their CI/CD pipeline. Multi-factor authentication should be enforced for all developer accounts.</p>
      `,
      summary: 'A sophisticated supply chain attack has been detected targeting popular open-source software repositories.',
      author: 'XCerberus Research Team',
      date: twelveHoursAgo.toISOString(),
      tags: ['Supply Chain', 'Software', 'Open Source', 'Data Exfiltration'],
      image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80'
    });

    // 16 hours ago
    const sixteenHoursAgo = new Date(now.getTime() - 16 * 60 * 60 * 1000);
    posts.push({
      id: 'post-5',
      title: 'Emerging Threat: AI-Powered Phishing Campaigns on the Rise',
      content: `
        <h2>Executive Summary</h2>
        <p>Our threat intelligence has identified a significant increase in sophisticated phishing campaigns leveraging AI-generated content. These campaigns are notable for their highly personalized content, grammatically correct text, and contextually relevant messaging.</p>

        <h2>AI Techniques Employed</h2>
        <p>The attackers are using large language models to generate personalized phishing emails based on information harvested from social media and data breaches. The AI-generated content is highly convincing and bypasses traditional phishing detection methods.</p>

        <h2>Target Selection</h2>
        <p>These campaigns are primarily targeting executives, finance personnel, and employees with access to sensitive systems. The attackers appear to be conducting extensive reconnaissance to identify high-value targets within organizations.</p>

        <h2>Defense Strategies</h2>
        <p>Organizations should update their security awareness training to include examples of AI-generated phishing, implement DMARC/DKIM/SPF email authentication, and consider AI-powered email security solutions that can detect subtle anomalies in communication patterns.</p>
      `,
      summary: 'Our threat intelligence has identified a significant increase in sophisticated phishing campaigns leveraging AI-generated content.',
      author: 'XCerberus Research Team',
      date: sixteenHoursAgo.toISOString(),
      tags: ['Phishing', 'AI', 'Social Engineering', 'Email Security'],
      image: 'https://images.unsplash.com/photo-1496096265110-f83ad7f96608?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
    });

    return posts;
  };

  // Filter posts based on search term and selected tag
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = searchTerm === '' ||
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.summary.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesTag = selectedTag === '' || post.tags.includes(selectedTag);

    return matchesSearch && matchesTag;
  });

  // Get all unique tags from posts
  const allTags = [...new Set(blogPosts.flatMap(post => post.tags))].sort();

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-800'}`}>
      <div className="container mx-auto px-4 py-8 pt-24">
        <p className="text-[#88cc14] mb-2 text-sm font-medium">THREAT INTELLIGENCE</p>
        <div className="flex justify-between items-center mb-4">
          <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
            Global Cyber Threat Analysis
          </h1>

          <button className="px-3 py-1.5 bg-[#88cc14] text-white rounded-md hover:bg-[#6ba811] transition-colors flex items-center gap-1 text-sm">
            <FaRss className="text-xs" /> Subscribe
          </button>
        </div>

        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-8 text-sm border-b border-gray-800 pb-4`}>
          Analysis of global cyber threats, updated every 4 hours with the latest intelligence.
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <div className="lg:col-span-2">
            {/* Search */}
            <div className="mb-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search threat analysis..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full p-2 pl-8 text-sm rounded-md ${darkMode ? 'bg-gray-800/50 text-white border-gray-700' : 'bg-white text-gray-800 border-gray-300'} border`}
                />
                <FaSearch className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs" />
              </div>
            </div>

            {/* Blog Posts */}
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#88cc14]"></div>
              </div>
            ) : filteredPosts.length === 0 ? (
              <div className={`text-center p-8 ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} rounded-lg`}>
                <p className="text-lg">No blog posts found matching your criteria.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {filteredPosts.map(post => (
                  <div key={post.id} className={`border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} pb-6 mb-6 last:border-0`}>
                    <div className="flex flex-col md:flex-row gap-4">
                      {post.image && (
                        <div className="md:w-1/3 h-48 overflow-hidden rounded-md">
                          <img
                            src={post.image}
                            alt={post.title}
                            className="w-full h-full object-cover transition-transform hover:scale-105"
                          />
                        </div>
                      )}
                      <div className="md:w-2/3">
                        <h2 className="text-xl font-bold mb-2 hover:text-[#88cc14] transition-colors">{post.title}</h2>

                        <div className="flex flex-wrap items-center text-xs text-gray-500 mb-3 gap-3">
                          <div className="flex items-center">
                            <FaCalendarAlt className="mr-1 text-[10px]" />
                            {formatDate(post.date)}
                          </div>
                          <div className="flex items-center">
                            <FaUser className="mr-1 text-[10px]" />
                            XCerberus Research Team
                          </div>
                        </div>

                        <p className={`mb-3 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {post.summary}
                        </p>

                        <div className="flex flex-wrap gap-2 mb-3">
                          {post.tags.map(tag => (
                            <span
                              key={tag}
                              className={`px-2 py-0.5 text-xs rounded-full cursor-pointer flex items-center ${
                                selectedTag === tag
                                  ? 'bg-[#88cc14] text-white'
                                  : darkMode
                                    ? 'bg-gray-800 text-gray-400 hover:bg-gray-700'
                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                              }`}
                              onClick={() => setSelectedTag(tag === selectedTag ? '' : tag)}
                            >
                              <FaTag className="mr-1 text-[8px]" />
                              {tag}
                            </span>
                          ))}
                        </div>

                        <button className="px-3 py-1 text-sm bg-transparent border border-[#88cc14] text-[#88cc14] rounded-md hover:bg-[#88cc14]/10 transition-colors">
                          Read Analysis
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div>
            {/* Tags */}
            <div className="mb-6 bg-[#0F172A] rounded-md p-4 border border-gray-800">
              <h3 className="text-sm font-bold mb-3 text-gray-300">FILTER BY TOPIC</h3>
              <div className="flex flex-wrap gap-2">
                {allTags.map(tag => (
                  <span
                    key={tag}
                    className={`px-2 py-0.5 text-xs rounded-full cursor-pointer flex items-center ${
                      selectedTag === tag
                        ? 'bg-[#88cc14] text-white'
                        : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
                    }`}
                    onClick={() => setSelectedTag(tag === selectedTag ? '' : tag)}
                  >
                    <FaTag className="mr-1 text-[8px]" />
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            {/* About */}
            <div className="bg-[#0F172A] rounded-md p-4 border border-gray-800">
              <h3 className="text-sm font-bold mb-3 text-gray-300">ABOUT THREAT INTELLIGENCE</h3>
              <p className="text-gray-400 text-xs mb-3 leading-relaxed">
                The XCerberus Threat Intelligence feed provides expert analysis of global cyber threats. Our team continuously monitors threat data and publishes new insights every 4 hours.
              </p>
              <p className="text-gray-400 text-xs mb-4 leading-relaxed">
                Each report analyzes patterns in attack data, identifies emerging threats, and provides actionable security recommendations.
              </p>
              <div className="bg-[#88cc14]/5 p-3 rounded-md border border-[#88cc14]/10">
                <h4 className="font-medium text-[#88cc14] text-xs mb-2">Research Methodology</h4>
                <ol className="text-gray-400 text-xs space-y-1 list-decimal list-inside leading-relaxed">
                  <li>Collection of threat data from multiple sources</li>
                  <li>Pattern analysis and threat identification</li>
                  <li>Detailed technical and strategic assessment</li>
                  <li>Publication of findings every 4 hours</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Blog;
