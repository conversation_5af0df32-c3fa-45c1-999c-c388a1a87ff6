import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaUsers,
  FaFileAlt,
  FaBell,
  FaChartLine,
  FaCog,
  FaSignOutAlt,
  FaUserPlus,
  FaSearch,
  FaFilter,
  FaCoins,
  FaCrown,
  FaBuilding,
  FaUserEdit,
  FaUserMinus,
  FaEnvelope,
  FaHome,
  FaChevronDown,
  FaChevronUp,
  FaSync,
  FaExclamationTriangle,
  FaCheckCircle,
  FaLaptopCode,
  FaBook,
  FaGraduationCap,
  FaShieldAlt
} from 'react-icons/fa';
import { supabase } from '../../lib/supabase';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import StatisticsOverview from '../../components/admin/StatisticsOverview';
import UserManagement from '../../components/admin/UserManagement';
import ContentManagement from '../../components/admin/ContentManagement';
import NotificationSystem from '../../components/admin/NotificationSystem';
import SystemSettings from '../../components/admin/SystemSettings';
import UserSubscriptionManager from '../../components/admin/UserSubscriptionManager';

/**
 * EnhancedAdminDashboard Component
 *
 * A comprehensive admin dashboard with statistics, user management,
 * content management, notification system, and settings.
 * Fully dynamic with real-time data updates.
 */
const EnhancedAdminDashboard = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const { subscriptionLevel } = useSubscription();
  const [activeTab, setActiveTab] = useState('overview');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [notificationCount, setNotificationCount] = useState(0);
  const [stats, setStats] = useState({
    totalUsers: 0,
    freeUsers: 0,
    premiumUsers: 0,
    businessUsers: 0,
    totalRevenue: 0,
    activeSubscriptions: 0,
    totalChallenges: 0,
    totalModules: 0,
    totalNotifications: 0,
    pendingApprovals: 0,
    userGrowth: 0,
    revenueGrowth: 0,
    activeUsers: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [recentSubscriptions, setRecentSubscriptions] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);

  // Fetch dashboard data
  const fetchDashboardData = useCallback(async (showRefreshing = true) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
        setLoading(true);
      }

      // Fetch user stats
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('subscription_tier, count')
        .group('subscription_tier');

      if (userError) throw userError;

      // Fetch subscription stats
      const { data: subData, error: subError } = await supabase
        .from('subscription_tracking')
        .select('status, count')
        .eq('status', 'active')
        .group('status');

      if (subError) throw subError;

      // Fetch revenue stats
      const { data: revenueData, error: revenueError } = await supabase
        .from('subscription_tracking')
        .select('payment_id, amount, created_at')
        .not('payment_id', 'is', null);

      if (revenueError) throw revenueError;

      // Fetch content stats
      const { data: challengeData, error: challengeError } = await supabase
        .from('challenges')
        .select('count');

      if (challengeError) throw challengeError;

      const { data: moduleData, error: moduleError } = await supabase
        .from('learning_modules')
        .select('count');

      if (moduleError) throw moduleError;

      // Fetch recent subscriptions
      const { data: recentSubData, error: recentSubError } = await supabase
        .from('subscription_tracking')
        .select(`
          id,
          user_id,
          start_date,
          status,
          amount,
          profiles:user_id (username, avatar_url),
          subscription_plans (name)
        `)
        .order('start_date', { ascending: false })
        .limit(5);

      if (recentSubError) throw recentSubError;

      // Fetch notifications count
      const { data: notifData, error: notifError } = await supabase
        .from('notifications')
        .select('count')
        .eq('is_read', false);

      if (notifError) throw notifError;

      // Fetch recent user activities
      const { data: activityData, error: activityError } = await supabase
        .from('user_activity')
        .select(`
          id,
          user_id,
          activity_type,
          activity_data,
          created_at,
          profiles:user_id (username, avatar_url)
        `)
        .order('created_at', { ascending: false })
        .limit(10);

      if (activityError) throw activityError;

      // Process user stats
      const totalUsers = userData.reduce((sum, item) => sum + item.count, 0);
      const freeUsers = userData.find(item => item.subscription_tier === 'free')?.count || 0;
      const premiumUsers = userData.find(item => item.subscription_tier === 'premium')?.count || 0;
      const businessUsers = userData.find(item => item.subscription_tier === 'business')?.count || 0;

      // Process subscription stats
      const activeSubscriptions = subData[0]?.count || 0;

      // Calculate total revenue
      const totalRevenue = revenueData.reduce((sum, item) => sum + (item.amount || 0), 0);

      // Calculate growth metrics (mock data for now)
      // In a real implementation, we would compare with previous period
      const userGrowth = Math.round((Math.random() * 20) - 5); // -5% to +15%
      const revenueGrowth = Math.round((Math.random() * 30) - 5); // -5% to +25%

      // Calculate active users (mock data)
      const activeUsers = Math.round(totalUsers * (0.3 + (Math.random() * 0.4))); // 30-70% of total users

      // Set stats
      setStats({
        totalUsers,
        freeUsers,
        premiumUsers,
        businessUsers,
        totalRevenue,
        activeSubscriptions,
        totalChallenges: challengeData[0]?.count || 0,
        totalModules: moduleData[0]?.count || 0,
        totalNotifications: notifData[0]?.count || 0,
        pendingApprovals: Math.floor(Math.random() * 10), // Mock data
        userGrowth,
        revenueGrowth,
        activeUsers
      });

      // Set notification count
      setNotificationCount(notifData[0]?.count || 0);

      // Set recent subscriptions
      setRecentSubscriptions(recentSubData || []);

      // Set recent activities
      setRecentActivities(activityData || []);

      // Update last updated timestamp
      setLastUpdated(new Date());

    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false);
      if (showRefreshing) {
        setTimeout(() => setRefreshing(false), 500); // Show refresh animation for at least 500ms
      }
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchDashboardData(false);

    // Set up real-time subscription for updates
    const subscription = supabase
      .channel('dashboard-changes')
      .on('postgres_changes', { event: '*', schema: 'public' }, () => {
        // When any database change happens, refresh the data
        fetchDashboardData(false);
      })
      .subscribe();

    // Refresh data every 5 minutes
    const intervalId = setInterval(() => {
      fetchDashboardData(false);
    }, 5 * 60 * 1000);

    return () => {
      subscription.unsubscribe();
      clearInterval(intervalId);
    };
  }, [fetchDashboardData]);

  // Handle logout
  const handleLogout = () => {
    // Clear local storage
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('supabase.auth.user');
    localStorage.removeItem('user_subscription');

    // Redirect to login
    navigate('/login', { replace: true });
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-100 text-gray-900'}`}>
      <div className="flex">
        {/* Sidebar */}
        <div className={`w-64 fixed inset-y-0 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-r`}>
          <div className="p-6">
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Manage your platform
            </p>
          </div>

          <nav className="mt-6">
            <button
              onClick={() => setActiveTab('overview')}
              className={`w-full flex items-center gap-3 px-6 py-3 ${
                activeTab === 'overview'
                  ? darkMode
                    ? 'bg-[#88cc14]/10 text-[#88cc14]'
                    : 'bg-[#88cc14]/10 text-[#88cc14]'
                  : darkMode
                    ? 'text-gray-400 hover:bg-gray-800'
                    : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <FaChartLine />
              <span>Overview</span>
            </button>

            <button
              onClick={() => setActiveTab('users')}
              className={`w-full flex items-center gap-3 px-6 py-3 ${
                activeTab === 'users'
                  ? darkMode
                    ? 'bg-[#88cc14]/10 text-[#88cc14]'
                    : 'bg-[#88cc14]/10 text-[#88cc14]'
                  : darkMode
                    ? 'text-gray-400 hover:bg-gray-800'
                    : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <FaUsers />
              <span>User Management</span>
            </button>

            <button
              onClick={() => setActiveTab('subscriptions')}
              className={`w-full flex items-center gap-3 px-6 py-3 ${
                activeTab === 'subscriptions'
                  ? darkMode
                    ? 'bg-[#88cc14]/10 text-[#88cc14]'
                    : 'bg-[#88cc14]/10 text-[#88cc14]'
                  : darkMode
                    ? 'text-gray-400 hover:bg-gray-800'
                    : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <FaCrown />
              <span>Subscription Management</span>
            </button>

            <button
              onClick={() => setActiveTab('content')}
              className={`w-full flex items-center gap-3 px-6 py-3 ${
                activeTab === 'content'
                  ? darkMode
                    ? 'bg-[#88cc14]/10 text-[#88cc14]'
                    : 'bg-[#88cc14]/10 text-[#88cc14]'
                  : darkMode
                    ? 'text-gray-400 hover:bg-gray-800'
                    : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <FaFileAlt />
              <span>Content Management</span>
            </button>

            <button
              onClick={() => setActiveTab('notifications')}
              className={`w-full flex items-center gap-3 px-6 py-3 ${
                activeTab === 'notifications'
                  ? darkMode
                    ? 'bg-[#88cc14]/10 text-[#88cc14]'
                    : 'bg-[#88cc14]/10 text-[#88cc14]'
                  : darkMode
                    ? 'text-gray-400 hover:bg-gray-800'
                    : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <FaBell />
              <span>Notifications</span>
            </button>

            <button
              onClick={() => setActiveTab('settings')}
              className={`w-full flex items-center gap-3 px-6 py-3 ${
                activeTab === 'settings'
                  ? darkMode
                    ? 'bg-[#88cc14]/10 text-[#88cc14]'
                    : 'bg-[#88cc14]/10 text-[#88cc14]'
                  : darkMode
                    ? 'text-gray-400 hover:bg-gray-800'
                    : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <FaCog />
              <span>Settings</span>
            </button>

            <button
              onClick={handleLogout}
              className={`w-full flex items-center gap-3 px-6 py-3 ${
                darkMode
                  ? 'text-red-400 hover:bg-red-400/10'
                  : 'text-red-600 hover:bg-red-50'
              }`}
            >
              <FaSignOutAlt />
              <span>Sign Out</span>
            </button>
          </nav>
        </div>

        {/* Main Content */}
        <div className="ml-64 flex-1">
          <div className="p-6">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div>
                <h1 className="text-2xl font-bold mb-6">Dashboard Overview</h1>

                {loading ? (
                  <div className="flex justify-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"></div>
                  </div>
                ) : error ? (
                  <div className={`p-4 rounded-lg ${darkMode ? 'bg-red-900/20 text-red-400' : 'bg-red-100 text-red-800'}`}>
                    {error}
                  </div>
                ) : (
                  <>
                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-blue-500 bg-opacity-10">
                            <FaUsers className="text-blue-500" />
                          </div>
                          <div className="ml-4">
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Total Users</p>
                            <p className="text-2xl font-bold">{stats.totalUsers}</p>
                          </div>
                        </div>
                      </div>

                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-green-500 bg-opacity-10">
                            <FaCrown className="text-green-500" />
                          </div>
                          <div className="ml-4">
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Premium Users</p>
                            <p className="text-2xl font-bold">{stats.premiumUsers}</p>
                          </div>
                        </div>
                      </div>

                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-purple-500 bg-opacity-10">
                            <FaBuilding className="text-purple-500" />
                          </div>
                          <div className="ml-4">
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Business Users</p>
                            <p className="text-2xl font-bold">{stats.businessUsers}</p>
                          </div>
                        </div>
                      </div>

                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-yellow-500 bg-opacity-10">
                            <FaCoins className="text-yellow-500" />
                          </div>
                          <div className="ml-4">
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Total Revenue</p>
                            <p className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Statistics Overview */}
                    <div className="mb-6">
                      <StatisticsOverview />
                    </div>

                    {/* Recent Subscriptions */}
                    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4 mb-6`}>
                      <h2 className="text-xl font-bold mb-4">Recent Subscriptions</h2>

                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className={`${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                              <th className="px-4 py-3 text-left">User</th>
                              <th className="px-4 py-3 text-left">Plan</th>
                              <th className="px-4 py-3 text-left">Amount</th>
                              <th className="px-4 py-3 text-left">Date</th>
                              <th className="px-4 py-3 text-left">Status</th>
                            </tr>
                          </thead>
                          <tbody>
                            {recentSubscriptions.length > 0 ? (
                              recentSubscriptions.map((sub) => (
                                <tr
                                  key={sub.id}
                                  className={`border-t ${darkMode ? 'border-gray-800 hover:bg-gray-800' : 'border-gray-200 hover:bg-gray-50'}`}
                                >
                                  <td className="px-4 py-3">
                                    <div className="flex items-center gap-3">
                                      <div className="w-8 h-8 rounded-full bg-[#88cc14]/20 flex items-center justify-center">
                                        {sub.profiles?.avatar_url ? (
                                          <img
                                            src={sub.profiles.avatar_url}
                                            alt={sub.profiles.username}
                                            className="w-full h-full rounded-full object-cover"
                                          />
                                        ) : (
                                          <span className="text-[#88cc14]">
                                            {sub.profiles?.username?.charAt(0).toUpperCase() || 'U'}
                                          </span>
                                        )}
                                      </div>
                                      <span>{sub.profiles?.username || 'Unknown User'}</span>
                                    </div>
                                  </td>
                                  <td className="px-4 py-3">{sub.subscription_plans?.name || 'Unknown Plan'}</td>
                                  <td className="px-4 py-3">{formatCurrency(sub.amount || 0)}</td>
                                  <td className="px-4 py-3">{formatDate(sub.start_date)}</td>
                                  <td className="px-4 py-3">
                                    <span className={`px-2 py-1 rounded text-xs ${
                                      sub.status === 'active'
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-yellow-100 text-yellow-800'
                                    }`}>
                                      {sub.status?.charAt(0).toUpperCase() + sub.status?.slice(1) || 'Unknown'}
                                    </span>
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan="5" className="px-4 py-3 text-center">
                                  No recent subscriptions
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </>
                )}
              </div>
            )}

            {/* User Management Tab */}
            {activeTab === 'users' && (
              <UserManagement />
            )}

            {/* Subscription Management Tab */}
            {activeTab === 'subscriptions' && (
              <UserSubscriptionManager />
            )}

            {/* Content Management Tab */}
            {activeTab === 'content' && (
              <ContentManagement />
            )}

            {/* Notifications Tab */}
            {activeTab === 'notifications' && (
              <NotificationSystem />
            )}

            {/* Settings Tab */}
            {activeTab === 'settings' && (
              <SystemSettings />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAdminDashboard;
