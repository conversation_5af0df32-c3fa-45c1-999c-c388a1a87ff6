import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { FaUsers, FaCoins, FaChartLine, FaUserShield, FaFileAlt, FaLaptopCode, FaBell } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { Navigate } from 'react-router-dom';

const AdminDashboard = () => {
  const { user } = useAuth();
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState({
    totalUsers: 0,
    premiumUsers: 0,
    totalRevenue: 0,
    activeSubscriptions: 0,
    totalChallenges: 0,
    totalModules: 0
  });
  const [recentUsers, setRecentUsers] = useState([]);
  const [recentSubscriptions, setRecentSubscriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) return;

      try {
        // For testing purposes, we'll check user_metadata directly
        const isAdminUser = user.user_metadata?.is_admin === true;
        const isSuperAdminUser = user.user_metadata?.is_super_admin === true;

        setIsAdmin(isAdminUser || isSuperAdminUser);
        setIsSuperAdmin(isSuperAdminUser);

        if (!isAdminUser && !isSuperAdminUser) {
          setError('You do not have permission to access this page');
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        setError(error.message);
      }
    };

    checkAdminStatus();
  }, [user]);

  // Fetch dashboard data
  useEffect(() => {
    if (!isAdmin) return;

    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        // Simplified for build purposes
        setStats({
          totalUsers: 100,
          premiumUsers: 25,
          totalRevenue: 15000,
          activeSubscriptions: 30,
          totalChallenges: 50,
          totalModules: 20
        });
        
        setRecentUsers([
          { id: 1, username: 'user1', subscription_tier: 'premium', created_at: new Date() },
          { id: 2, username: 'user2', subscription_tier: 'free', created_at: new Date() }
        ]);
        
        setRecentSubscriptions([
          { 
            id: 1, 
            status: 'active', 
            end_date: new Date(), 
            user: { username: 'user1' }, 
            plan: { name: 'Premium', price: 15 } 
          }
        ]);
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError(error.message);
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAdmin]);

  // If not admin, redirect to home
  if (!loading && !isAdmin) {
    return <Navigate to="/" />;
  }

  return (
    <>
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row">
            {/* Sidebar */}
            <div className={`w-full md:w-64 mb-6 md:mb-0 md:mr-6 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
              <h2 className="text-xl font-bold mb-4">Admin Panel</h2>
              <nav>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => setActiveTab('overview')}
                      className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                        activeTab === 'overview'
                          ? 'bg-[#88cc14] text-black'
                          : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                      }`}
                    >
                      <FaChartLine className="mr-2" /> Overview
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => setActiveTab('users')}
                      className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                        activeTab === 'users'
                          ? 'bg-[#88cc14] text-black'
                          : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                      }`}
                    >
                      <FaUsers className="mr-2" /> Users
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => setActiveTab('revenue')}
                      className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                        activeTab === 'revenue'
                          ? 'bg-[#88cc14] text-black'
                          : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                      }`}
                    >
                      <FaCoins className="mr-2" /> Revenue
                    </button>
                  </li>
                </ul>
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              {loading ? (
                <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
                  <p>Loading dashboard data...</p>
                </div>
              ) : error ? (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                  <p>{error}</p>
                </div>
              ) : (
                <div>
                  {/* Overview Tab */}
                  {activeTab === 'overview' && (
                    <div>
                      <h1 className="text-2xl font-bold mb-6">Dashboard Overview</h1>
                      <p>Overview content here</p>
                    </div>
                  )}
                  
                  {/* Users Tab */}
                  {activeTab === 'users' && (
                    <div>
                      <h1 className="text-2xl font-bold mb-6">User Management</h1>
                      <p>User management interface will be implemented here.</p>
                    </div>
                  )}
                  
                  {/* Revenue Tab */}
                  {activeTab === 'revenue' && (
                    <div>
                      <h1 className="text-2xl font-bold mb-6">Revenue Analytics</h1>
                      <p>Revenue analytics interface will be implemented here.</p>
                    </div>
                  )}
                  
                  {/* Notifications Tab */}
                  {activeTab === 'notifications' && (
                    <div>
                      <h2 className="text-2xl font-bold mb-6">Notifications</h2>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminDashboard;
