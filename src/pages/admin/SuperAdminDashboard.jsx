import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaUsers, FaFileAlt, FaBell, FaPalette, FaBuilding, FaChartLine, FaCog, FaSignOutAlt } from 'react-icons/fa';

const SuperAdminDashboard = () => {
  const navigate = useNavigate();
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');

  // Handle logout
  const handleLogout = () => {
    // Clear local storage
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('supabase.auth.user');
    localStorage.removeItem('user_subscription');
    
    // Redirect to login
    navigate('/login', { replace: true });
  };

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'users':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">User Management</h2>
            <p>Manage all users, roles, and permissions.</p>
            <div className="mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg">
              This section will allow you to:
              <ul className="list-disc ml-5 mt-2">
                <li>View all registered users</li>
                <li>Edit user details and subscription tiers</li>
                <li>Manage user roles and permissions</li>
                <li>Ban or suspend accounts</li>
                <li>Reset passwords</li>
              </ul>
            </div>
          </div>
        );
      case 'content':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Content Management</h2>
            <p>Manage all learning content, challenges, and posts.</p>
            <div className="mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg">
              This section will allow you to:
              <ul className="list-disc ml-5 mt-2">
                <li>Create and edit learning modules</li>
                <li>Manage challenges and their solutions</li>
                <li>Review and approve user-submitted content</li>
                <li>Organize content categories</li>
                <li>Schedule content publication</li>
              </ul>
            </div>
          </div>
        );
      case 'notifications':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Notification System</h2>
            <p>Manage system notifications and announcements.</p>
            <div className="mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg">
              This section will allow you to:
              <ul className="list-disc ml-5 mt-2">
                <li>Create system-wide announcements</li>
                <li>Send targeted notifications to specific user groups</li>
                <li>Schedule notification delivery</li>
                <li>View notification delivery statistics</li>
                <li>Manage notification templates</li>
              </ul>
            </div>
          </div>
        );
      case 'customization':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Page Customization</h2>
            <p>Customize the appearance and content of various pages.</p>
            <div className="mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg">
              This section will allow you to:
              <ul className="list-disc ml-5 mt-2">
                <li>Customize the landing page layout</li>
                <li>Edit featured content sections</li>
                <li>Manage navigation menus</li>
                <li>Customize dashboard layouts for different user tiers</li>
                <li>Edit system-wide text and messaging</li>
              </ul>
            </div>
          </div>
        );
      case 'business':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Business Setup</h2>
            <p>Manage business settings, pricing, and subscription plans.</p>
            <div className="mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg">
              This section will allow you to:
              <ul className="list-disc ml-5 mt-2">
                <li>Configure subscription tiers and pricing</li>
                <li>Manage payment gateway settings</li>
                <li>View and export financial reports</li>
                <li>Configure business information</li>
                <li>Manage tax settings</li>
              </ul>
            </div>
          </div>
        );
      case 'statistics':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Statistics & Tracking</h2>
            <p>View detailed analytics and user activity tracking.</p>
            <div className="mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg">
              This section will allow you to:
              <ul className="list-disc ml-5 mt-2">
                <li>View user engagement metrics</li>
                <li>Track content popularity and completion rates</li>
                <li>Monitor subscription conversions and churn</li>
                <li>Analyze user learning patterns</li>
                <li>Export detailed reports</li>
              </ul>
            </div>
          </div>
        );
      case 'settings':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">System Settings</h2>
            <p>Configure system-wide settings and preferences.</p>
            <div className="mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg">
              This section will allow you to:
              <ul className="list-disc ml-5 mt-2">
                <li>Configure email settings</li>
                <li>Manage API integrations</li>
                <li>Set up backup and maintenance schedules</li>
                <li>Configure security settings</li>
                <li>Manage system logs</li>
              </ul>
            </div>
          </div>
        );
      case 'overview':
      default:
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Super Admin Dashboard</h2>
            <p className="mb-6">Welcome to the Super Admin Dashboard. From here, you can manage all aspects of the platform.</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-blue-500 bg-opacity-10">
                    <FaUsers className="text-blue-500" />
                  </div>
                  <h3 className="ml-3 text-lg font-semibold">User Management</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  Manage users, roles, and permissions.
                </p>
                <button 
                  onClick={() => setActiveTab('users')}
                  className="text-blue-500 hover:text-blue-700 font-medium"
                >
                  Manage Users →
                </button>
              </div>
              
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-green-500 bg-opacity-10">
                    <FaFileAlt className="text-green-500" />
                  </div>
                  <h3 className="ml-3 text-lg font-semibold">Content Management</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  Manage learning content and challenges.
                </p>
                <button 
                  onClick={() => setActiveTab('content')}
                  className="text-green-500 hover:text-green-700 font-medium"
                >
                  Manage Content →
                </button>
              </div>
              
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-purple-500 bg-opacity-10">
                    <FaBell className="text-purple-500" />
                  </div>
                  <h3 className="ml-3 text-lg font-semibold">Notifications</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  Manage system notifications and announcements.
                </p>
                <button 
                  onClick={() => setActiveTab('notifications')}
                  className="text-purple-500 hover:text-purple-700 font-medium"
                >
                  Manage Notifications →
                </button>
              </div>
              
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-yellow-500 bg-opacity-10">
                    <FaPalette className="text-yellow-500" />
                  </div>
                  <h3 className="ml-3 text-lg font-semibold">Page Customization</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  Customize the appearance and content of pages.
                </p>
                <button 
                  onClick={() => setActiveTab('customization')}
                  className="text-yellow-500 hover:text-yellow-700 font-medium"
                >
                  Customize Pages →
                </button>
              </div>
              
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-red-500 bg-opacity-10">
                    <FaBuilding className="text-red-500" />
                  </div>
                  <h3 className="ml-3 text-lg font-semibold">Business Setup</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  Manage business settings and subscription plans.
                </p>
                <button 
                  onClick={() => setActiveTab('business')}
                  className="text-red-500 hover:text-red-700 font-medium"
                >
                  Configure Business →
                </button>
              </div>
              
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-indigo-500 bg-opacity-10">
                    <FaChartLine className="text-indigo-500" />
                  </div>
                  <h3 className="ml-3 text-lg font-semibold">Statistics & Tracking</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  View analytics and user activity tracking.
                </p>
                <button 
                  onClick={() => setActiveTab('statistics')}
                  className="text-indigo-500 hover:text-indigo-700 font-medium"
                >
                  View Statistics →
                </button>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <header className={`fixed top-0 left-0 right-0 z-10 ${darkMode ? 'bg-[#0B1120] border-gray-800' : 'bg-white border-gray-200'} border-b`}>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold">Super Admin Dashboard</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button 
                onClick={handleLogout}
                className={`flex items-center px-3 py-2 rounded-md ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
              >
                <FaSignOutAlt className="mr-2" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>
      
      <div className="flex pt-16">
        {/* Sidebar */}
        <aside className={`fixed left-0 top-16 bottom-0 w-64 ${darkMode ? 'bg-[#0B1120] border-gray-800' : 'bg-white border-gray-200'} border-r overflow-y-auto`}>
          <nav className="p-4">
            <ul className="space-y-2">
              <li>
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                    activeTab === 'overview'
                      ? 'bg-[#88cc14] text-black'
                      : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  }`}
                >
                  <FaChartLine className="mr-2" /> Overview
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveTab('users')}
                  className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                    activeTab === 'users'
                      ? 'bg-[#88cc14] text-black'
                      : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  }`}
                >
                  <FaUsers className="mr-2" /> User Management
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveTab('content')}
                  className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                    activeTab === 'content'
                      ? 'bg-[#88cc14] text-black'
                      : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  }`}
                >
                  <FaFileAlt className="mr-2" /> Content Management
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveTab('notifications')}
                  className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                    activeTab === 'notifications'
                      ? 'bg-[#88cc14] text-black'
                      : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  }`}
                >
                  <FaBell className="mr-2" /> Notifications
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveTab('customization')}
                  className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                    activeTab === 'customization'
                      ? 'bg-[#88cc14] text-black'
                      : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  }`}
                >
                  <FaPalette className="mr-2" /> Page Customization
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveTab('business')}
                  className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                    activeTab === 'business'
                      ? 'bg-[#88cc14] text-black'
                      : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  }`}
                >
                  <FaBuilding className="mr-2" /> Business Setup
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveTab('statistics')}
                  className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                    activeTab === 'statistics'
                      ? 'bg-[#88cc14] text-black'
                      : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  }`}
                >
                  <FaChartLine className="mr-2" /> Statistics & Tracking
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveTab('settings')}
                  className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                    activeTab === 'settings'
                      ? 'bg-[#88cc14] text-black'
                      : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  }`}
                >
                  <FaCog className="mr-2" /> System Settings
                </button>
              </li>
            </ul>
          </nav>
        </aside>
        
        {/* Main Content */}
        <main className="flex-1 ml-64 p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default SuperAdminDashboard;
