import React, { useState, useEffect } from 'react';
import {
  FaShieldAlt,
  FaExclamationTriangle,
  FaGraduationCap,
  FaChartPie,
  FaChartBar,
  FaRobot,
  FaTrophy,
  FaNewspaper,
  FaLaptopCode,
  FaSearch,
  FaUserGraduate
} from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import CyberThreatDashboard from '../components/visualizations/CyberThreatDashboard';
import ThreatOfTheWeek from '../components/education/ThreatOfTheWeek';
import ThreatHuntingAcademy from '../components/education/ThreatHuntingAcademy';
import AIThreatAssistant from '../components/education/AIThreatAssistant';
import ThreatAnalyticsDashboard from '../components/visualizations/ThreatAnalyticsDashboard';
import threatAnalyticsService from '../services/api/threatAnalyticsService';

const EnhancedSecurityInsights = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [userLevel, setUserLevel] = useState('beginner'); // beginner, intermediate, advanced
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading data
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // Define tabs with educational focus
  const tabs = [
    { id: 'dashboard', label: 'Threat Dashboard', icon: FaChartPie, level: 'all' },
    { id: 'threat-of-week', label: 'Threat of the Week', icon: FaNewspaper, level: 'all' },
    { id: 'hunting-academy', label: 'Hunting Academy', icon: FaTrophy, level: 'all' },
    { id: 'ai-assistant', label: 'AI Threat Assistant', icon: FaRobot, level: 'all' },
    { id: 'analytics', label: 'Advanced Analytics', icon: FaChartBar, level: 'intermediate' },
    { id: 'learning-path', label: 'Learning Path', icon: FaGraduationCap, level: 'all' },
    { id: 'practice-lab', label: 'Practice Lab', icon: FaLaptopCode, level: 'intermediate' }
  ];

  // Filter tabs based on user level
  const visibleTabs = tabs.filter(tab =>
    tab.level === 'all' ||
    (tab.level === 'intermediate' && (userLevel === 'intermediate' || userLevel === 'advanced')) ||
    (tab.level === 'advanced' && userLevel === 'advanced')
  );

  // Render content based on active tab
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      );
    }

    switch (activeTab) {
      case 'dashboard':
        return (
          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-xl font-bold">Cyber Threat Intelligence Dashboard</h2>
                <p className="text-gray-400">
                  Comprehensive visualization of global cyber threat landscape and attack patterns.
                  <span className="ml-2 text-blue-400">Learn as you explore real-world threat data.</span>
                </p>
              </div>
            </div>
            <CyberThreatDashboard />
          </div>
        );
      case 'threat-of-week':
        return <ThreatOfTheWeek />;
      case 'hunting-academy':
        return <ThreatHuntingAcademy userLevel={userLevel} />;
      case 'ai-assistant':
        return <AIThreatAssistant />;
      case 'analytics':
        return (
          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-xl font-bold">Advanced Threat Analytics</h2>
                <p className="text-gray-400">
                  In-depth analysis of threat data from multiple intelligence sources.
                  <span className="ml-2 text-blue-400">For intermediate and advanced cybersecurity students.</span>
                </p>
              </div>
            </div>
            <ThreatAnalyticsDashboard />
          </div>
        );
      case 'learning-path':
        return (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-bold mb-4">Personalized Learning Path</h2>
            <p className="text-gray-400 mb-6">
              Customized cybersecurity learning recommendations based on current threat landscape and your progress.
            </p>
            <div className="bg-gray-700 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold mb-3">Your Current Level: {userLevel.charAt(0).toUpperCase() + userLevel.slice(1)}</h3>
              <div className="flex items-center mb-4">
                <div className="w-full bg-gray-600 rounded-full h-2.5">
                  <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: userLevel === 'beginner' ? '25%' : userLevel === 'intermediate' ? '65%' : '90%' }}></div>
                </div>
                <span className="ml-2 text-sm text-gray-400">
                  {userLevel === 'beginner' ? '25%' : userLevel === 'intermediate' ? '65%' : '90%'}
                </span>
              </div>
              <div className="flex space-x-2 mb-6">
                <button
                  className={`px-3 py-1 rounded ${userLevel === 'beginner' ? 'bg-blue-600' : 'bg-gray-600'}`}
                  onClick={() => setUserLevel('beginner')}
                >
                  Beginner
                </button>
                <button
                  className={`px-3 py-1 rounded ${userLevel === 'intermediate' ? 'bg-blue-600' : 'bg-gray-600'}`}
                  onClick={() => setUserLevel('intermediate')}
                >
                  Intermediate
                </button>
                <button
                  className={`px-3 py-1 rounded ${userLevel === 'advanced' ? 'bg-blue-600' : 'bg-gray-600'}`}
                  onClick={() => setUserLevel('advanced')}
                >
                  Advanced
                </button>
              </div>
              <p className="text-sm text-gray-400 mb-4">
                Based on current threat trends and your progress, we recommend focusing on these skills:
              </p>
              <div className="space-y-4">
                <div className="border-l-4 border-blue-500 pl-3 py-1">
                  <div className="font-medium">Malware Analysis Fundamentals</div>
                  <div className="text-sm text-gray-400">Related to 43% increase in polymorphic malware attacks</div>
                </div>
                <div className="border-l-4 border-yellow-500 pl-3 py-1">
                  <div className="font-medium">Network Traffic Analysis</div>
                  <div className="text-sm text-gray-400">Critical for detecting C2 communications in recent attacks</div>
                </div>
                <div className="border-l-4 border-green-500 pl-3 py-1">
                  <div className="font-medium">OSINT Techniques</div>
                  <div className="text-sm text-gray-400">Valuable for early threat detection and reconnaissance</div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'practice-lab':
        return (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-bold mb-4">Hands-on Practice Lab</h2>
            <p className="text-gray-400 mb-6">
              Apply your knowledge in realistic scenarios based on actual threat intelligence.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Available Scenarios</h3>
                <div className="space-y-3">
                  <div className="border-l-4 border-blue-500 pl-3 py-1">
                    <div className="font-medium">Ransomware Incident Response</div>
                    <div className="text-sm text-gray-400">Difficulty: Intermediate</div>
                    <button className="mt-2 px-3 py-1 bg-blue-600 rounded text-sm">Start Scenario</button>
                  </div>
                  <div className="border-l-4 border-yellow-500 pl-3 py-1">
                    <div className="font-medium">APT Detection Challenge</div>
                    <div className="text-sm text-gray-400">Difficulty: Advanced</div>
                    <button className="mt-2 px-3 py-1 bg-blue-600 rounded text-sm">Start Scenario</button>
                  </div>
                  <div className="border-l-4 border-red-500 pl-3 py-1">
                    <div className="font-medium">Supply Chain Attack Analysis</div>
                    <div className="text-sm text-gray-400">Difficulty: Advanced</div>
                    <button className="mt-2 px-3 py-1 bg-blue-600 rounded text-sm">Start Scenario</button>
                  </div>
                </div>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Your Progress</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div>Completed Scenarios</div>
                    <div className="font-semibold">3/12</div>
                  </div>
                  <div className="w-full bg-gray-600 rounded-full h-2.5">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: '25%' }}></div>
                  </div>
                  <div className="flex justify-between items-center mt-4">
                    <div>Earned Badges</div>
                    <div className="font-semibold">2</div>
                  </div>
                  <div className="flex space-x-2">
                    <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                      <FaShieldAlt className="text-blue-500" />
                    </div>
                    <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                      <FaUserGraduate className="text-green-500" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-bold mb-4">Coming Soon</h2>
            <p className="text-gray-400">
              This feature is currently under development. Check back soon!
            </p>
          </div>
        );
    }
  };

  return (
    <div className={`p-6 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Security Insights & Learning Hub</h1>
        <p className="text-gray-400">
          Explore real-world threat intelligence and enhance your cybersecurity skills through interactive learning experiences.
        </p>
      </div>

      {/* Experience Level Indicator */}
      <div className="mb-6 bg-gray-800 rounded-lg p-4 flex justify-between items-center">
        <div>
          <span className="text-sm text-gray-400 mr-2">Experience Level:</span>
          <span className={`px-2 py-1 rounded text-xs ${
            userLevel === 'beginner' ? 'bg-blue-900 text-blue-200' :
            userLevel === 'intermediate' ? 'bg-yellow-900 text-yellow-200' :
            'bg-green-900 text-green-200'
          }`}>
            {userLevel.charAt(0).toUpperCase() + userLevel.slice(1)}
          </span>
        </div>
        <div className="flex items-center">
          <span className="text-sm text-gray-400 mr-2">Quick Search:</span>
          <div className="relative">
            <input
              type="text"
              placeholder="Search threats, techniques..."
              className="bg-gray-700 border border-gray-600 rounded pl-8 pr-4 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <FaSearch className="absolute left-2 top-2 text-gray-400" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex flex-wrap border-b border-gray-700 mb-6">
        {visibleTabs.map(tab => (
          <button
            key={tab.id}
            className={`flex items-center px-4 py-2 ${
              activeTab === tab.id
                ? 'border-b-2 border-blue-500 text-blue-500'
                : 'text-gray-400 hover:text-gray-200'
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            <tab.icon className="mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      {renderContent()}
    </div>
  );
};

export default EnhancedSecurityInsights;
