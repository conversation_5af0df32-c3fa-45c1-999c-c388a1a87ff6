import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import UnifiedDashboard from '../components/dashboards/UnifiedDashboard';
import { supabase } from '../lib/supabase';
import { useSubscription } from '../contexts/SubscriptionContext';
import { fetchDashboardData, getRecommendations } from '../lib/dashboard';
import { DashboardProvider } from '../contexts/DashboardContext';

function Dashboard() {
  const navigate = useNavigate();
  const { subscriptionLevel } = useSubscription();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [recommendations, setRecommendations] = useState(null);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        // Get current user session
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          navigate('/login');
          return;
        }

        // Fetch all dashboard data
        const data = await fetchDashboardData(session.user.id);
        setDashboardData(data);

        // Get personalized recommendations
        const userRecommendations = await getRecommendations(session.user.id);
        setRecommendations(userRecommendations);

      } catch (error) {
        console.error('Error loading dashboard:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();

    // Subscribe to realtime updates
    const challengesSubscription = supabase
      .channel('challenges')
      .on('*', payload => {
        setDashboardData(prev => ({
          ...prev,
          challenges: [...(prev?.challenges || []), payload.new]
        }));
      })
      .subscribe();

    const activitySubscription = supabase
      .channel('user_activity')
      .on('*', payload => {
        setDashboardData(prev => ({
          ...prev,
          recentActivity: [payload.new, ...(prev?.recentActivity || []).slice(0, 9)]
        }));
      })
      .subscribe();

    return () => {
      challengesSubscription.unsubscribe();
      activitySubscription.unsubscribe();
    };
  }, [navigate]);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0B1120] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"></div>
      </div>
    );
  }

  // Render the unified dashboard wrapped in DashboardProvider
  return (
    <DashboardProvider>
      <UnifiedDashboard
        {...dashboardData}
        recommendations={recommendations}
      />
    </DashboardProvider>
  );
}

export default Dashboard;