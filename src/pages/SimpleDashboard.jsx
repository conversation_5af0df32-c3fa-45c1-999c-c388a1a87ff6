import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaHome, FaGraduationCap, FaTrophy, FaUsers, FaStore, FaChartLine, FaCog, FaSignOutAlt, FaBell, FaSearch, FaUser, FaLock, FaCrown, FaCoins, FaArrowRight, FaCode, FaCheck, FaSun, FaMoon } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { useAuth } from '../contexts/AuthContext';
import { TIER_FEATURES } from '../config/subscriptionTiers';

const SimpleDashboard = () => {
  const navigate = useNavigate();
  const { darkMode, toggleDarkMode } = useGlobalTheme();
  const { user, profile } = useAuth();
  const { subscriptionLevel, userCoins, isPremium, isBusiness } = useSubscription();
  const [activeTab, setActiveTab] = useState('overview');

  // Redirect if not logged in
  useEffect(() => {
    if (!user) {
      console.log('No user found, but continuing for testing purposes');
      // For testing purposes, we'll allow access without a user
      // In production, uncomment the line below
      // window.location.replace('/login');
    }
  }, [user]);

  // Debug activeTab changes
  useEffect(() => {
    console.log('Active tab changed to:', activeTab);
  }, [activeTab]);

  // Get subscription features
  const subscriptionFeatures = TIER_FEATURES[subscriptionLevel] || TIER_FEATURES.free;
  const isFree = subscriptionLevel === 'free';

  // User data
  const userData = {
    name: profile?.full_name || user?.email || 'User',
    email: user?.email || '<EMAIL>',
    avatar: profile?.avatar_url || null,
    progress: 25, // Lower for free users
    completedChallenges: isFree ? 2 : 12,
    completedModules: isFree ? 1 : 8,
    points: isFree ? 250 : 1250,
    coins: userCoins || 0,
    rank: isFree ? 'Beginner' : 'Advanced Beginner',
    recentActivity: [
      { id: 1, type: 'challenge', name: 'SQL Injection Basics', date: '2 hours ago', points: 100 },
      { id: 2, type: 'module', name: 'Web Security Fundamentals', date: '1 day ago', points: 150 },
      { id: 3, type: 'challenge', name: 'Password Cracking Basics', date: '3 days ago', points: 100 },
    ],
    recommendedContent: [
      { id: 1, type: 'challenge', name: 'Network Traffic Analysis', difficulty: 'Easy', locked: isFree },
      { id: 2, type: 'module', name: 'Network Security Basics', difficulty: 'Beginner', locked: false },
      { id: 3, type: 'challenge', name: 'OSINT Investigation', difficulty: 'Easy', locked: isFree },
    ]
  };

  // Sidebar links with locked status for free users
  const sidebarLinks = [
    { name: 'Overview', icon: <FaHome />, id: 'overview', locked: false, path: '/simplified-dashboard' },
    { name: 'Learning Modules', icon: <FaGraduationCap />, id: 'learning', locked: false, limited: isFree, path: '/simplified-dashboard' },
    { name: 'Challenges', icon: <FaTrophy />, id: 'challenges', locked: false, limited: isFree, path: '/simplified-dashboard' },
    { name: 'Leaderboard', icon: <FaUsers />, id: 'leaderboard', locked: false, path: '/simplified-dashboard' },
    { name: 'Store', icon: <FaStore />, id: 'store', locked: false, path: '/simplified-dashboard' },
    { name: 'Analytics', icon: <FaChartLine />, id: 'analytics', locked: isFree, path: '/simplified-dashboard' },
    { name: 'Settings', icon: <FaCog />, id: 'settings', locked: false, path: '/simplified-dashboard' },
  ];

  // Additional links for premium/business users
  if (isPremium || isBusiness) {
    sidebarLinks.splice(3, 0, { name: 'Start Hack', icon: <FaCode />, id: 'starthack', locked: false });
  }

  if (isBusiness) {
    sidebarLinks.splice(4, 0, { name: 'Team Management', icon: <FaUsers />, id: 'teams', locked: false });
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <header className={`fixed top-0 left-0 right-0 z-40 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-b`}>
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex items-center">
                <span className="text-[#88cc14] font-bold text-2xl mr-1">X</span>
                <span className={`font-bold text-2xl ${darkMode ? 'text-white' : 'text-gray-900'}`}>Cerberus</span>
              </div>

              {/* Subscription Badge */}
              <div className="ml-4">
                {isBusiness ? (
                  <span className="bg-purple-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                    Business
                  </span>
                ) : isPremium ? (
                  <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-medium">
                    Premium
                  </span>
                ) : (
                  <span className="bg-gray-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                    Free Tier
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Dark/Light Mode Toggle */}
              <button
                onClick={toggleDarkMode}
                className={`p-2 rounded-full ${darkMode ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                aria-label="Toggle dark/light mode"
              >
                {darkMode ? <FaSun /> : <FaMoon />}
              </button>

              {/* Coins Display for Premium/Free Users */}
              {!isBusiness && (
                <div className="hidden md:flex items-center mr-2">
                  <FaCoins className="text-yellow-500 mr-1" />
                  <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {userData.coins}
                  </span>
                </div>
              )}

              {/* Upgrade Button for Free Users */}
              {isFree && (
                <button
                  onClick={() => navigate('/pricing')}
                  className="hidden md:flex items-center text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded-lg"
                >
                  <FaCrown className="mr-1" />
                  Upgrade
                </button>
              )}

              {/* Search */}
              <div className={`relative hidden md:block ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <FaSearch className="w-4 h-4" />
                </div>
                <input
                  type="text"
                  className={`py-2 pl-10 pr-4 rounded-lg ${
                    darkMode
                      ? 'bg-[#0B1120] border-gray-800 focus:border-gray-700'
                      : 'bg-gray-100 border-gray-200 focus:border-gray-300'
                  } border focus:outline-none focus:ring-1 focus:ring-[#88cc14]`}
                  placeholder="Search..."
                />
              </div>

              {/* Notifications */}
              <button className={`p-2 rounded-full ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'} relative`}>
                <FaBell className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
                <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>

              {/* User Menu */}
              <div className="relative">
                <button className={`flex items-center gap-2 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'} p-2 rounded-lg`}>
                  <div className="w-8 h-8 bg-[#88cc14] rounded-full flex items-center justify-center text-black">
                    {userData.avatar || <FaUser />}
                  </div>
                  <span className="hidden md:inline">{userData.name}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex pt-16">
        {/* Sidebar */}
        <aside className={`fixed left-0 top-16 bottom-0 w-20 md:w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-r z-30 transition-all duration-300 overflow-y-auto`}>
          <div className="p-4">
            <nav className="space-y-1">
              {sidebarLinks.map((link) => (
                <button
                  key={link.id}
                  onClick={() => {
                    if (!link.locked) {
                      setActiveTab(link.id);
                      // Ensure we stay on the dashboard page
                      if (window.location.pathname !== '/simplified-dashboard') {
                        navigate('/simplified-dashboard');
                      }
                    }
                  }}
                  disabled={link.locked}
                  className={`flex items-center w-full p-3 rounded-lg transition-colors ${
                    link.locked
                      ? darkMode
                        ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
                        : 'bg-gray-100/50 text-gray-400 cursor-not-allowed'
                      : activeTab === link.id
                        ? 'bg-[#88cc14] text-black'
                        : darkMode
                          ? 'text-gray-400 hover:bg-gray-800 hover:text-white'
                          : 'text-gray-600 hover:bg-gray-100 hover:text-black'
                  }`}
                >
                  <div className="flex items-center justify-center w-6 h-6 md:mr-3">
                    {link.icon}
                  </div>
                  <span className="hidden md:block flex-1 text-left">{link.name}</span>
                  {link.locked && (
                    <FaLock className="hidden md:block text-xs opacity-70 ml-2" />
                  )}
                  {link.limited && !link.locked && (
                    <span className="hidden md:block text-xs px-1.5 py-0.5 bg-gray-500/20 rounded text-gray-500 ml-2">
                      Limited
                    </span>
                  )}
                </button>
              ))}

              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  // Clear all auth data
                  localStorage.removeItem('supabase.auth.token');
                  localStorage.removeItem('supabase.auth.user');
                  localStorage.removeItem('user_subscription');
                  localStorage.removeItem('user_profile');
                  sessionStorage.clear();
                  // Use replace to avoid adding to history stack
                  window.location.replace('/login');
                }}
                className={`flex items-center w-full p-3 rounded-lg transition-colors mt-8 ${
                  darkMode
                    ? 'text-gray-400 hover:bg-gray-800 hover:text-white'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-black'
                }`}
              >
                <div className="flex items-center justify-center w-6 h-6 md:mr-3">
                  <FaSignOutAlt />
                </div>
                <span className="hidden md:block flex-1 text-left">Sign Out</span>
              </button>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 ml-20 md:ml-64 p-6">
          {/* Free Tier Upgrade Banner (only shown on overview) */}
          {activeTab === 'overview' && isFree && (
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6 relative overflow-hidden`}>
              <div className="absolute -top-20 -right-20 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-20 -left-20 w-64 h-64 bg-yellow-500/5 rounded-full blur-3xl"></div>

              <div className="relative z-10 flex flex-col md:flex-row items-center justify-between gap-8">
                <div>
                  <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                    Upgrade to Premium
                  </h2>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>
                    Unlock all features and accelerate your cybersecurity learning journey.
                  </p>

                  <ul className="space-y-2 mb-6">
                    <li className="flex items-center gap-2">
                      <FaCheck className="text-[#88cc14]" />
                      <span className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Access all 50 learning modules</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <FaCheck className="text-[#88cc14]" />
                      <span className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Unlock 100+ challenges</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <FaCheck className="text-[#88cc14]" />
                      <span className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Start Hack simulations</span>
                    </li>
                  </ul>
                </div>

                <div className="flex flex-col items-center">
                  <div className="text-center mb-4">
                    <div className="text-3xl font-bold text-yellow-500">₹399</div>
                    <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>per month</div>
                  </div>

                  <button
                    onClick={() => window.open('/pricing', '_blank')}
                    className="py-2 px-6 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors flex items-center"
                  >
                    Upgrade Now <FaArrowRight className="ml-2" />
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Welcome Section */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <h1 className="text-2xl font-bold mb-2">Welcome back, {userData.name}!</h1>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  {isFree
                    ? "Start your cybersecurity journey with our free learning resources."
                    : "Continue your cybersecurity journey. You're making great progress!"}
                </p>

                <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                  <div className="bg-[#88cc14] h-2.5 rounded-full" style={{ width: `${userData.progress}%` }}></div>
                </div>
                <div className="flex justify-between mt-2 text-sm">
                  <span>{userData.progress}% complete</span>
                  <span>Level: {userData.rank}</span>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Challenges</h3>
                    <FaTrophy className="text-yellow-500" />
                  </div>
                  <p className="text-3xl font-bold">{userData.completedChallenges}</p>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>completed</p>
                  {isFree && (
                    <div className="mt-2 text-xs text-[#88cc14]">
                      {subscriptionFeatures.challenges.availableChallenges} available in free tier
                    </div>
                  )}
                </div>

                <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Learning Modules</h3>
                    <FaGraduationCap className="text-blue-500" />
                  </div>
                  <p className="text-3xl font-bold">{userData.completedModules}</p>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>completed</p>
                  {isFree && (
                    <div className="mt-2 text-xs text-[#88cc14]">
                      {subscriptionFeatures.learnModules.availableModules} available in free tier
                    </div>
                  )}
                </div>

                <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Points</h3>
                    <FaChartLine className="text-green-500" />
                  </div>
                  <p className="text-3xl font-bold">{userData.points}</p>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>total earned</p>
                </div>

                <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 relative`}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Coins</h3>
                    <FaStore className="text-purple-500" />
                  </div>
                  <p className="text-3xl font-bold">{userData.coins}</p>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>available</p>

                  {isFree && (
                    <button
                      onClick={() => navigate('/store')}
                      className="absolute bottom-2 right-2 text-xs text-[#88cc14] hover:underline flex items-center"
                    >
                      Get more <FaArrowRight className="ml-1 text-[8px]" />
                    </button>
                  )}
                </div>
              </div>

              {/* Recent Activity */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-bold">Recent Activity</h2>
                  {isFree && userData.recentActivity.length > 0 && (
                    <span className="text-xs px-2 py-1 bg-gray-500/20 rounded text-gray-500">
                      Limited History
                    </span>
                  )}
                </div>

                {userData.recentActivity.length > 0 ? (
                  <div className="space-y-4">
                    {userData.recentActivity.map((activity) => (
                      <div key={activity.id} className={`flex items-start p-3 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'}`}>
                        <div className={`p-2 rounded-lg mr-4 ${
                          activity.type === 'challenge'
                            ? 'bg-yellow-500/20 text-yellow-500'
                            : 'bg-blue-500/20 text-blue-500'
                        }`}>
                          {activity.type === 'challenge' ? <FaTrophy /> : <FaGraduationCap />}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{activity.name}</h3>
                          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Completed {activity.date}
                          </p>
                        </div>
                        <div className="text-right">
                          <span className="font-bold text-[#88cc14]">+{activity.points}</span>
                          <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>points</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className={`p-6 text-center ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    <p>No recent activity yet.</p>
                    <p className="mt-2 text-sm">Complete challenges and modules to see your activity here.</p>
                  </div>
                )}
              </div>

              {/* Recommended Content */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <h2 className="text-xl font-bold mb-4">Recommended for You</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {userData.recommendedContent.map((content) => (
                    <div
                      key={content.id}
                      className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'} relative`}
                    >
                      {content.locked && (
                        <div className="absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4">
                          <FaLock className="text-2xl text-yellow-500 mb-2" />
                          <p className="text-white text-center text-sm mb-3">Upgrade to Premium to access this content</p>
                          <button
                            onClick={() => navigate('/pricing')}
                            className="py-1 px-4 bg-yellow-500 hover:bg-yellow-600 text-black rounded-lg transition-colors text-sm"
                          >
                            Upgrade
                          </button>
                        </div>
                      )}

                      <div className="flex items-center mb-3">
                        <div className={`p-2 rounded-lg mr-3 ${
                          content.type === 'challenge'
                            ? 'bg-yellow-500/20 text-yellow-500'
                            : 'bg-blue-500/20 text-blue-500'
                        }`}>
                          {content.type === 'challenge' ? <FaTrophy /> : <FaGraduationCap />}
                        </div>
                        <div>
                          <h3 className="font-medium">{content.name}</h3>
                          <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {content.difficulty} • {content.type}
                          </p>
                        </div>
                      </div>

                      <button
                        onClick={() => !content.locked && window.open(content.type === 'challenge' ? `/challenges/${content.id}` : `/learn/${content.id}`, '_blank')}
                        className={`w-full py-2 px-4 ${content.locked ? 'bg-gray-500 cursor-not-allowed' : 'bg-[#88cc14] hover:bg-[#7ab811]'} text-black rounded-lg transition-colors block text-center`}
                      >
                        {content.type === 'challenge' ? 'Start Challenge' : 'Start Learning'}
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Premium Features Section (only for free users) */}
              {isFree && (
                <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                  <h2 className="text-xl font-bold mb-4">Premium Features You're Missing</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'}`}>
                      <div className="flex items-center mb-3">
                        <div className="p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500">
                          <FaTrophy />
                        </div>
                        <div>
                          <h3 className="font-medium">100+ Challenges</h3>
                          <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Advanced cybersecurity challenges
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'}`}>
                      <div className="flex items-center mb-3">
                        <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                          <FaGraduationCap />
                        </div>
                        <div>
                          <h3 className="font-medium">Full Learning Modules</h3>
                          <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Complete learning curriculum
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'}`}>
                      <div className="flex items-center mb-3">
                        <div className="p-2 rounded-lg mr-3 bg-purple-500/20 text-purple-500">
                          <FaUsers />
                        </div>
                        <div>
                          <h3 className="font-medium">Community Access</h3>
                          <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Connect with other security professionals
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="text-center">
                    <button
                      onClick={() => navigate('/pricing')}
                      className="inline-flex items-center py-2 px-6 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors"
                    >
                      Upgrade to Premium <FaArrowRight className="ml-2" />
                    </button>
                    <p className={`mt-2 text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Starting at just ₹399/month
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'challenges' && (
            <div className="space-y-6">
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <h2 className="text-2xl font-bold mb-4">Available Challenges</h2>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
                  Start with these free challenges to test your cybersecurity skills.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Challenge 1 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500">
                        <FaTrophy />
                      </div>
                      <div>
                        <h3 className="font-medium">SQL Injection Basics</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • Web Security
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs bg-green-500/20 text-green-500 px-2 py-1 rounded">
                        Completed
                      </span>
                      <button
                        onClick={() => navigate('/challenges/sql-injection-basics')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        View Challenge
                      </button>
                    </div>
                  </div>

                  {/* Challenge 2 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500">
                        <FaTrophy />
                      </div>
                      <div>
                        <h3 className="font-medium">Password Cracking Basics</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • Authentication
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs bg-green-500/20 text-green-500 px-2 py-1 rounded">
                        Completed
                      </span>
                      <button
                        onClick={() => navigate('/challenges/password-cracking-basics')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        View Challenge
                      </button>
                    </div>
                  </div>

                  {/* Challenge 3 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500">
                        <FaTrophy />
                      </div>
                      <div>
                        <h3 className="font-medium">XSS Attack Simulation</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • Web Security
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                        Available
                      </span>
                      <button
                        onClick={() => navigate('/challenges/xss-attack-simulation')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Challenge
                      </button>
                    </div>
                  </div>

                  {/* Challenge 4 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500">
                        <FaTrophy />
                      </div>
                      <div>
                        <h3 className="font-medium">Network Traffic Analysis</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • Network Security
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                        Available
                      </span>
                      <button
                        onClick={() => navigate('/challenges/network-traffic-analysis')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Challenge
                      </button>
                    </div>
                  </div>

                  {/* Challenge 5 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500">
                        <FaTrophy />
                      </div>
                      <div>
                        <h3 className="font-medium">Basic Cryptography</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • Cryptography
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                        Available
                      </span>
                      <button
                        onClick={() => navigate('/challenges/basic-cryptography')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Challenge
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Premium Challenges Preview */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 relative overflow-hidden`}>
                <div className="absolute -top-20 -right-20 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl"></div>

                <h2 className="text-2xl font-bold mb-4 relative z-10">Premium Challenges</h2>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6 relative z-10`}>
                  Upgrade to access 100+ advanced challenges across all difficulty levels.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 relative z-10">
                  {/* Premium Challenge Preview 1 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'} relative`}>
                    <div className="absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4">
                      <FaLock className="text-2xl text-yellow-500 mb-2" />
                      <p className="text-white text-center text-sm">Premium Challenge</p>
                    </div>

                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-red-500/20 text-red-500">
                        <FaTrophy />
                      </div>
                      <div>
                        <h3 className="font-medium">Advanced Penetration Testing</h3>
                        <p className="text-xs text-gray-500">
                          Advanced • Offensive Security
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Premium Challenge Preview 2 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'} relative`}>
                    <div className="absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4">
                      <FaLock className="text-2xl text-yellow-500 mb-2" />
                      <p className="text-white text-center text-sm">Premium Challenge</p>
                    </div>

                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-purple-500/20 text-purple-500">
                        <FaTrophy />
                      </div>
                      <div>
                        <h3 className="font-medium">Reverse Engineering Malware</h3>
                        <p className="text-xs text-gray-500">
                          Intermediate • Malware Analysis
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Premium Challenge Preview 3 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'} relative`}>
                    <div className="absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4">
                      <FaLock className="text-2xl text-yellow-500 mb-2" />
                      <p className="text-white text-center text-sm">Premium Challenge</p>
                    </div>

                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaTrophy />
                      </div>
                      <div>
                        <h3 className="font-medium">Cloud Security Assessment</h3>
                        <p className="text-xs text-gray-500">
                          Intermediate • Cloud Security
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center relative z-10">
                  <button
                    onClick={() => navigate('/pricing')}
                    className="inline-flex items-center py-2 px-6 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors"
                  >
                    Upgrade to Premium <FaArrowRight className="ml-2" />
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'learning' && (
            <div className="space-y-6">
              {/* Learning Path Overview */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <h2 className="text-2xl font-bold mb-2">Learning Modules</h2>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  Start your cybersecurity journey with our comprehensive learning paths.
                </p>

                {/* Learning Progress */}
                <div className="mb-6">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm font-medium">Your Learning Progress</span>
                    <span className="text-sm font-medium">1/3 Modules Completed</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                    <div className="bg-[#88cc14] h-2.5 rounded-full" style={{ width: '33%' }}></div>
                  </div>
                </div>

                {/* Learning Paths */}
                <div className="flex overflow-x-auto pb-4 space-x-4 mb-6">
                  <div className={`flex-shrink-0 w-64 p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <h3 className="font-bold text-lg mb-2">Foundations</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>Master the essential concepts needed for cybersecurity</p>
                    <div className="flex justify-between items-center">
                      <span className="text-xs bg-green-500/20 text-green-500 px-2 py-1 rounded">1/3 Complete</span>
                      <button className="text-xs text-[#88cc14] hover:underline">View Path</button>
                    </div>
                  </div>

                  <div className={`flex-shrink-0 w-64 p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <h3 className="font-bold text-lg mb-2">Defensive Security</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>Learn to protect systems and respond to threats</p>
                    <div className="flex justify-between items-center">
                      <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">Not Started</span>
                      <button className="text-xs text-[#88cc14] hover:underline">View Path</button>
                    </div>
                  </div>

                  <div className={`flex-shrink-0 w-64 p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <h3 className="font-bold text-lg mb-2">Offensive Security</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>Master penetration testing and ethical hacking</p>
                    <div className="flex justify-between items-center">
                      <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">Not Started</span>
                      <button className="text-xs text-[#88cc14] hover:underline">View Path</button>
                    </div>
                  </div>

                  <div className={`flex-shrink-0 w-64 p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <h3 className="font-bold text-lg mb-2">Specialized Areas</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>Explore advanced topics like malware analysis</p>
                    <div className="flex justify-between items-center">
                      <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">Not Started</span>
                      <button className="text-xs text-[#88cc14] hover:underline">View Path</button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Foundations Category */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-2 rounded-lg mr-3 bg-green-500/20 text-green-500">
                    <FaGraduationCap className="text-xl" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold">Foundations</h2>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Master the essential concepts needed for cybersecurity
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Module 1 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Introduction to Cybersecurity</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • 60 min • 4 sections
                        </p>
                      </div>
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                      Learn the fundamentals of cybersecurity, key concepts, and why it matters.
                    </p>
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-xs bg-green-500/20 text-green-500 px-2 py-1 rounded">
                          Completed
                        </span>
                      </div>
                      <button
                        onClick={() => navigate('/learn/intro-to-cybersecurity')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Continue Learning
                      </button>
                    </div>
                  </div>

                  {/* Module 2 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Operating System Concepts</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • 90 min • 5 sections
                        </p>
                      </div>
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                      Understand how operating systems work and their security implications.
                    </p>
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                          Available
                        </span>
                      </div>
                      <button
                        onClick={() => navigate('/learn/os-concepts')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Learning
                      </button>
                    </div>
                  </div>

                  {/* Module 3 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Network Fundamentals</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • 120 min • 6 sections
                        </p>
                      </div>
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                      Learn how networks function, protocols, and network security basics.
                    </p>
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                          Available
                        </span>
                      </div>
                      <button
                        onClick={() => navigate('/learn/network-fundamentals')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Learning
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Defensive Security Category */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                    <FaGraduationCap className="text-xl" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold">Defensive Security</h2>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Learn to protect systems and respond to threats
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Module 1 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Security Operations Basics</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • 90 min • 5 sections
                        </p>
                      </div>
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                      Learn the fundamentals of SOC operations and threat monitoring.
                    </p>
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                          Available
                        </span>
                      </div>
                      <button
                        onClick={() => window.open('/learn/soc-basics', '_blank')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Learning
                      </button>
                    </div>
                  </div>

                  {/* Module 2 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Threat Hunting Fundamentals</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • 75 min • 4 sections
                        </p>
                      </div>
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                      Learn the basics of proactively searching for threats in your environment.
                    </p>
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                          Available
                        </span>
                      </div>
                      <button
                        onClick={() => window.open('/learn/threat-hunting-basics', '_blank')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Learning
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Offensive Security Category */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-2 rounded-lg mr-3 bg-red-500/20 text-red-500">
                    <FaGraduationCap className="text-xl" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold">Offensive Security</h2>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Master penetration testing and ethical hacking
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Module 1 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Ethical Hacking Fundamentals</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • 90 min • 5 sections
                        </p>
                      </div>
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                      Learn the basics of ethical hacking and penetration testing methodology.
                    </p>
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                          Available
                        </span>
                      </div>
                      <button
                        onClick={() => window.open('/learn/ethical-hacking-basics', '_blank')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Learning
                      </button>
                    </div>
                  </div>

                  {/* Module 2 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Web Application Security</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • 120 min • 6 sections
                        </p>
                      </div>
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                      Learn about common web vulnerabilities and how to exploit them ethically.
                    </p>
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                          Available
                        </span>
                      </div>
                      <button
                        onClick={() => window.open('/learn/web-app-security', '_blank')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Learning
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Specialized Areas Category */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <div className="flex items-center mb-4">
                  <div className="p-2 rounded-lg mr-3 bg-purple-500/20 text-purple-500">
                    <FaGraduationCap className="text-xl" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold">Specialized Areas</h2>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Explore advanced topics in cybersecurity
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Module 1 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Linux for Cybersecurity</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • 120 min • 6 sections
                        </p>
                      </div>
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                      Learn essential Linux skills for cybersecurity professionals.
                    </p>
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                          Available
                        </span>
                      </div>
                      <button
                        onClick={() => window.open('/learn/linux-for-cybersecurity', '_blank')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Learning
                      </button>
                    </div>
                  </div>

                  {/* Module 2 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Scripting for Security</h3>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Beginner • 90 min • 5 sections
                        </p>
                      </div>
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                      Learn basic scripting with Python and Bash for security automation.
                    </p>
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded">
                          Available
                        </span>
                      </div>
                      <button
                        onClick={() => window.open('/learn/scripting-for-security', '_blank')}
                        className="text-xs bg-[#88cc14] hover:bg-[#7ab811] text-black px-2 py-1 rounded"
                      >
                        Start Learning
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Premium Learning Modules Preview */}
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 relative overflow-hidden`}>
                <div className="absolute -top-20 -right-20 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl"></div>

                <h2 className="text-2xl font-bold mb-4 relative z-10">Premium Learning Modules</h2>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6 relative z-10`}>
                  Upgrade to access all 50 learning modules with in-depth content and hands-on labs.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 relative z-10">
                  {/* Premium Module Preview 1 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'} relative`}>
                    <div className="absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4">
                      <FaLock className="text-2xl text-yellow-500 mb-2" />
                      <p className="text-white text-center text-sm">Premium Module</p>
                    </div>

                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Advanced Penetration Testing</h3>
                        <p className="text-xs text-gray-500">
                          Advanced • 120 min
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Premium Module Preview 2 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'} relative`}>
                    <div className="absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4">
                      <FaLock className="text-2xl text-yellow-500 mb-2" />
                      <p className="text-white text-center text-sm">Premium Module</p>
                    </div>

                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Secure Coding Practices</h3>
                        <p className="text-xs text-gray-500">
                          Intermediate • 90 min
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Premium Module Preview 3 */}
                  <div className={`p-4 rounded-lg border ${darkMode ? 'border-gray-800 bg-[#0B1120]' : 'border-gray-200 bg-gray-50'} relative`}>
                    <div className="absolute inset-0 bg-black/50 backdrop-blur-[1px] rounded-lg flex flex-col items-center justify-center z-10 p-4">
                      <FaLock className="text-2xl text-yellow-500 mb-2" />
                      <p className="text-white text-center text-sm">Premium Module</p>
                    </div>

                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-lg mr-3 bg-blue-500/20 text-blue-500">
                        <FaGraduationCap />
                      </div>
                      <div>
                        <h3 className="font-medium">Cloud Security Architecture</h3>
                        <p className="text-xs text-gray-500">
                          Intermediate • 105 min
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center relative z-10">
                  <button
                    onClick={() => navigate('/pricing')}
                    className="inline-flex items-center py-2 px-6 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors"
                  >
                    Upgrade to Premium <FaArrowRight className="ml-2" />
                  </button>
                </div>
              </div>
            </div>
          )}

          {(activeTab !== 'overview' && activeTab !== 'challenges' && activeTab !== 'learning') && (
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
              <h2 className="text-2xl font-bold mb-4">{sidebarLinks.find(link => link.id === activeTab)?.name}</h2>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                This section is under development. Please check back later!
              </p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default SimpleDashboard;
