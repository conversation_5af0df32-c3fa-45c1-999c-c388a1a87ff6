import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaChevronRight, FaShieldAlt, FaGraduationCap, FaCode, FaTrophy, FaRocket } from 'react-icons/fa';
import HeroSection from '../components/home/<USER>';
import FeaturesSection from '../components/home/<USER>';
import ChallengesSection from '../components/home/<USER>';
import LearningPathsSection from '../components/home/<USER>';
import PricingSection from '../components/home/<USER>';
import CTASection from '../components/home/<USER>';
import ChallengeModal from '../components/home/<USER>';
import DemoOverlay from '../components/demo/DemoOverlay';
import CyberForceSEO from '../components/common/CyberForceSEO';

function Home() {
  const [selectedChallenge, setSelectedChallenge] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showDemoOverlay, setShowDemoOverlay] = useState(false);
  const [demoType, setDemoType] = useState('learn');

  const handleStartChallenge = (challenge) => {
    setSelectedChallenge(challenge);
    setShowModal(true);
  };

  const handleShowDemo = (type) => {
    setDemoType(type);
    setShowDemoOverlay(true);
  };

  return (
    <div className="relative overflow-hidden">
      {/* SEO */}
      <CyberForceSEO
        title="Home"
        description="CyberForce is Oman's premier cybersecurity training platform offering hands-on challenges, learning modules, and expert mentorship for cybersecurity professionals and enthusiasts."
        keywords={['cybersecurity training', 'hands-on labs', 'cyber challenges', 'security learning', 'Oman']}
        canonicalUrl="https://cyberforce.om/"
      />

      {/* Main Content */}
      <HeroSection onShowDemo={handleShowDemo} />
      <FeaturesSection />
      <ChallengesSection onStartChallenge={handleStartChallenge} />
      <LearningPathsSection onShowDemo={handleShowDemo} />
      <PricingSection />
      <CTASection />

      {/* Challenge Modal */}
      {showModal && (
        <ChallengeModal
          challenge={selectedChallenge}
          onClose={() => setShowModal(false)}
        />
      )}

      {/* Demo Overlay */}
      <DemoOverlay
        isOpen={showDemoOverlay}
        onClose={() => setShowDemoOverlay(false)}
        demoType={demoType}
      />
    </div>
  );
}

export default Home;