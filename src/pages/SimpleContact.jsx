import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Fa<PERSON><PERSON>ing, FaUser, FaEnvelope, FaPhone, FaGlobe, FaArrowLeft, FaCheck } from 'react-icons/fa';

const SimpleContact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    employees: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-[#0B1120] py-16 px-4">
      <div className="container mx-auto max-w-4xl">
        {/* Back to pricing */}
        <div className="mb-8">
          <Link to="/pricing" className="text-gray-400 hover:text-white flex items-center gap-2">
            <FaArrowLeft />
            <span>Back to Pricing</span>
          </Link>
        </div>
        
        <div className="bg-[#1A1F35] rounded-xl border border-gray-800 overflow-hidden">
          {isSubmitted ? (
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-[#88cc14]/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <FaCheck className="text-[#88cc14] text-3xl" />
              </div>
              <h2 className="text-3xl font-bold text-white mb-4">Thank You!</h2>
              <p className="text-xl text-gray-300 mb-6">
                Your business inquiry has been received. Our team will contact you shortly to discuss your requirements.
              </p>
              <Link 
                to="/" 
                className="inline-block bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold px-6 py-3 rounded-lg transition-colors"
              >
                Return to Home
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2">
              {/* Left side - Business info */}
              <div className="bg-[#0B1120] p-8">
                <div className="flex items-center gap-3 mb-6">
                  <FaBuilding className="text-[#88cc14] text-2xl" />
                  <h2 className="text-2xl font-bold text-white">Business Tier</h2>
                </div>
                
                <p className="text-gray-300 mb-8">
                  Contact our sales team to learn more about our Business tier and how we can help your organization improve its cybersecurity posture.
                </p>
                
                <div className="space-y-6">
                  <h3 className="text-xl font-bold text-white mb-4">Business Tier Includes:</h3>
                  
                  <ul className="space-y-4">
                    <li className="flex items-start gap-3">
                      <FaCheck className="text-[#88cc14] mt-1" />
                      <div>
                        <span className="text-white font-medium">Unlimited Access</span>
                        <p className="text-gray-400 text-sm">Full access to all 150 challenges and 50 learning modules</p>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <FaCheck className="text-[#88cc14] mt-1" />
                      <div>
                        <span className="text-white font-medium">Team Management</span>
                        <p className="text-gray-400 text-sm">Manage your team's progress and assign specific learning paths</p>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <FaCheck className="text-[#88cc14] mt-1" />
                      <div>
                        <span className="text-white font-medium">Custom Learning Paths</span>
                        <p className="text-gray-400 text-sm">Tailored learning experiences for different roles in your organization</p>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <FaCheck className="text-[#88cc14] mt-1" />
                      <div>
                        <span className="text-white font-medium">Dedicated Support</span>
                        <p className="text-gray-400 text-sm">Priority support with a dedicated account manager</p>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <FaCheck className="text-[#88cc14] mt-1" />
                      <div>
                        <span className="text-white font-medium">Advanced Reporting</span>
                        <p className="text-gray-400 text-sm">Detailed analytics and progress reports for your team</p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              
              {/* Right side - Contact form */}
              <div className="p-8">
                <h2 className="text-2xl font-bold text-white mb-6">Contact Sales</h2>
                
                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <label htmlFor="name" className="block text-gray-400 mb-2">Full Name *</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <FaUser className="text-gray-500" />
                      </div>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        className="bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]"
                        placeholder="John Doe"
                        value={formData.name}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="email" className="block text-gray-400 mb-2">Email Address *</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <FaEnvelope className="text-gray-500" />
                      </div>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        className="bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="company" className="block text-gray-400 mb-2">Company Name *</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <FaBuilding className="text-gray-500" />
                      </div>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        className="bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]"
                        placeholder="Company Inc."
                        value={formData.company}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label htmlFor="phone" className="block text-gray-400 mb-2">Phone Number</label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <FaPhone className="text-gray-500" />
                        </div>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          className="bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]"
                          placeholder="+****************"
                          value={formData.phone}
                          onChange={handleChange}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="employees" className="block text-gray-400 mb-2">Number of Employees</label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <FaUsers className="text-gray-500" />
                        </div>
                        <select
                          id="employees"
                          name="employees"
                          className="bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]"
                          value={formData.employees}
                          onChange={handleChange}
                        >
                          <option value="" className="bg-[#0B1120]">Select...</option>
                          <option value="1-10" className="bg-[#0B1120]">1-10</option>
                          <option value="11-50" className="bg-[#0B1120]">11-50</option>
                          <option value="51-200" className="bg-[#0B1120]">51-200</option>
                          <option value="201-500" className="bg-[#0B1120]">201-500</option>
                          <option value="501+" className="bg-[#0B1120]">501+</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="message" className="block text-gray-400 mb-2">Message</label>
                    <textarea
                      id="message"
                      name="message"
                      rows="4"
                      className="bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]"
                      placeholder="Tell us about your requirements..."
                      value={formData.message}
                      onChange={handleChange}
                    ></textarea>
                  </div>
                  
                  <button
                    type="submit"
                    className={`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${
                      isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                    }`}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Inquiry'}
                  </button>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const FaUsers = ({ className }) => {
  return (
    <svg className={className} fill="currentColor" viewBox="0 0 640 512">
      <path d="M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z" />
    </svg>
  );
};

export default SimpleContact;
