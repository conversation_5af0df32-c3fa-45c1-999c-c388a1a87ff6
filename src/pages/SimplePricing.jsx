import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaCheck, FaTimes, FaCrown, FaBuilding, FaRocket } from 'react-icons/fa';
import { TIER_FEATURES } from '../contexts/SimpleSubscriptionContext';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

const SimplePricing = () => {
  // Get the subscription tiers
  const tiers = Object.values(TIER_FEATURES);
  const { darkMode } = useGlobalTheme();

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} pt-24 pb-16 px-4`}>
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h1 className={`text-4xl md:text-5xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Choose Your <span className="text-[#88cc14]">Subscription</span> Plan
          </h1>
          <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} max-w-3xl mx-auto`}>
            Select the plan that best fits your cybersecurity learning journey. Upgrade anytime as your skills grow.
          </p>
        </div>

        {/* Pricing Plans */}
        <div className="flex flex-col md:flex-row justify-center items-stretch gap-8 max-w-4xl mx-auto mb-16">
          {tiers.map((tier, index) => (
            <div
              key={tier.name}
              className={`flex-1 bg-[#1A1F35] rounded-xl overflow-hidden border ${
                tier.name === 'Premium'
                  ? 'border-[#88cc14] shadow-lg shadow-[#88cc14]/10'
                  : 'border-gray-800'
              }`}
            >
              {/* Popular badge */}
              {tier.name === 'Premium' && (
                <div className="bg-[#88cc14] text-black text-center py-1 font-bold text-sm">
                  MOST POPULAR
                </div>
              )}

              <div className="p-6 flex flex-col h-full">
                {/* Tier header */}
                <div className="flex items-center gap-2 mb-2">
                  {tier.name === 'Premium' ? (
                    <FaCrown className="text-[#88cc14] text-xl" />
                  ) : (
                    <FaRocket className="text-[#88cc14] text-xl" />
                  )}
                  <h2 className="text-2xl font-bold text-white">{tier.name}</h2>
                </div>

                {/* Price */}
                <div className="mb-6">
                  <div className="flex items-end gap-1">
                    {tier.name === 'Free' ? (
                      <span className="text-4xl font-bold text-white">Free</span>
                    ) : (
                      <>
                        <span className="text-4xl font-bold text-white">OMR {tier.price}</span>
                        <span className="text-gray-400 mb-1">/month</span>
                      </>
                    )}
                  </div>
                  {tier.price > 0 && (
                    <p className="text-gray-400 text-sm mt-1">Billed monthly, cancel anytime</p>
                  )}
                </div>

                {/* Features */}
                <ul className="space-y-3 mb-auto">
                  {tier.features.map((feature, i) => (
                    <li key={i} className="flex items-start gap-2">
                      <FaCheck className="text-[#88cc14] mt-1 flex-shrink-0" />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Action button */}
                <div className="mt-8">
                  <Link
                    to={tier.name === 'Free' ? '/signup' : '/login'}
                    className={`inline-block w-full text-center py-2 px-4 rounded-lg font-medium ${
                      tier.name === 'Premium'
                        ? 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                        : 'bg-[#1A1F35] hover:bg-[#252D4A] text-white border border-gray-700'
                    }`}
                  >
                    {tier.name === 'Free' ? 'Get Started' : 'Upgrade to Premium'}
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Feature Comparison Table */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-white text-center mb-10">Feature Comparison</h2>

          <div className="overflow-x-auto">
            <table className="w-full bg-[#1A1F35] rounded-lg border border-gray-800">
              <thead>
                <tr className="border-b border-gray-800">
                  <th className="px-6 py-4 text-left text-white">Feature</th>
                  <th className="px-6 py-4 text-center text-white">Free</th>
                  <th className="px-6 py-4 text-center text-white">Premium</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Learning Modules</td>
                  <td className="px-6 py-4 text-center text-gray-300">3</td>
                  <td className="px-6 py-4 text-center text-gray-300">All 50</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Challenges</td>
                  <td className="px-6 py-4 text-center text-gray-300">5</td>
                  <td className="px-6 py-4 text-center text-gray-300">100</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Pricing</td>
                  <td className="px-6 py-4 text-center text-gray-300">Free</td>
                  <td className="px-6 py-4 text-center text-gray-300">15 OMR/month</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Start Hack</td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaTimes className="mx-auto text-red-500" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaCheck className="mx-auto text-[#88cc14]" /></td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Dashboard</td>
                  <td className="px-6 py-4 text-center text-gray-300">Basic</td>
                  <td className="px-6 py-4 text-center text-gray-300">Advanced</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Team Management</td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaTimes className="mx-auto text-red-500" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaCheck className="mx-auto text-[#88cc14]" /></td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Support</td>
                  <td className="px-6 py-4 text-center text-gray-300">Community</td>
                  <td className="px-6 py-4 text-center text-gray-300">Priority</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplePricing;
