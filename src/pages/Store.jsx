import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Fa<PERSON>oins, FaArrowRight, FaCrown, FaCheck } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { useSubscription } from '../contexts/SubscriptionContext';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

const Store = () => {
  const navigate = useNavigate();
  const { darkMode } = useGlobalTheme();
  const { userCoins, addCoins, coinPackages, subscriptionLevel, isPremium, isBusiness } = useSubscription();
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState(null);

  const handlePurchase = async (coinPackage) => {
    setSelectedPackage(coinPackage.id);
    setIsProcessing(true);

    try {
      // In a real app, this would integrate with a payment processor
      // For demo purposes, we'll just add the coins directly
      await addCoins(coinPackage.coins + (coinPackage.bonus || 0));

      // Show success message or redirect
      // For now, we'll just reset the state
      setIsProcessing(false);
      setSelectedPackage(null);
    } catch (error) {
      console.error('Purchase failed:', error);
      setIsProcessing(false);
      setSelectedPackage(null);
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} py-16 px-4`}>
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h1 className={`text-4xl md:text-5xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            XCerberus <span className="text-[#88cc14]">Store</span>
          </h1>
          <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} max-w-3xl mx-auto`}>
            Purchase coins to unlock premium challenges and accelerate your learning journey.
          </p>
        </div>

        {/* Current Balance */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-xl p-6 mb-12 border`}>
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center">
                <FaCoins className="text-yellow-500 text-3xl" />
              </div>
              <div>
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Your Balance</h2>
                <p className="text-3xl font-bold text-yellow-500">{userCoins} coins</p>
              </div>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <Button
                variant="secondary"
                to="/challenges"
              >
                Browse Challenges
              </Button>

              {!isBusiness && (
                <Button
                  variant="primary"
                  to="/pricing"
                  icon={<FaCrown />}
                  iconPosition="left"
                >
                  Upgrade to Business
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Coin Packages */}
        <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-8`}>Coin Packages</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {coinPackages.map((pkg, index) => (
            <motion.div
              key={pkg.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card
                className={`h-full ${pkg.popular ? 'border-[#88cc14]' : ''}`}
                accent={pkg.popular}
                accentColor="green"
              >
                {pkg.popular && (
                  <div className="bg-[#88cc14] text-black text-center py-1 font-bold text-sm">
                    BEST VALUE
                  </div>
                )}

                <div className="flex flex-col h-full">
                  <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>

                  <div className="flex items-center gap-2 mb-6">
                    <FaCoins className="text-yellow-500" />
                    <span className="text-3xl font-bold text-white">{pkg.coins}</span>
                    {pkg.bonus && (
                      <span className="bg-yellow-500/20 text-yellow-500 px-2 py-1 rounded text-sm">
                        +{pkg.bonus} BONUS
                      </span>
                    )}
                  </div>

                  <div className="text-2xl font-bold text-white mb-6">${pkg.price}</div>

                  <div className="mt-auto">
                    <Button
                      variant={pkg.popular ? 'primary' : 'secondary'}
                      fullWidth
                      onClick={() => handlePurchase(pkg)}
                      disabled={isProcessing && selectedPackage === pkg.id}
                    >
                      {isProcessing && selectedPackage === pkg.id
                        ? 'Processing...'
                        : 'Purchase'}
                    </Button>
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Business Tier Promotion */}
        {!isBusiness && (
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-xl p-8 border relative overflow-hidden`}>
            <div className="absolute -top-20 -right-20 w-64 h-64 bg-[#88cc14]/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-20 -left-20 w-64 h-64 bg-[#88cc14]/5 rounded-full blur-3xl"></div>

            <div className="relative z-10 flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>Unlimited Access with Business Tier</h2>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>
                  Upgrade to our Business tier and get unlimited access to all challenges without needing to purchase coins.
                </p>

                <ul className="space-y-2 mb-6">
                  <li className="flex items-center gap-2">
                    <FaCheck className="text-[#88cc14]" />
                    <span className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Access all 150 challenges</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <FaCheck className="text-[#88cc14]" />
                    <span className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>No coin purchases required</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <FaCheck className="text-[#88cc14]" />
                    <span className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Team management features</span>
                  </li>
                </ul>
              </div>

              <Button
                variant="primary"
                size="lg"
                to="/pricing"
                icon={<FaArrowRight />}
                iconPosition="right"
              >
                Upgrade Now
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Store;
