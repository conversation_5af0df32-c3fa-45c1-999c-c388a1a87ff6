import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaCrown, FaMedal, FaTrophy, FaSearch, FaUser } from 'react-icons/fa';
import { getLeaderboard, getUserProfile, supabase } from '../lib/supabase';

function Leaderboard() {
  const [users, setUsers] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [timeframe, setTimeframe] = useState('all');

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get leaderboard data
        const leaderboardData = await getLeaderboard();
        setUsers(leaderboardData || []);
        
        // Check if user is logged in
        const { data: { session } } = await supabase.auth.getSession();
        
        // If user is logged in, get their profile
        if (session?.user) {
          const profileData = await getUserProfile().catch(() => null);
          setCurrentUser(profileData);
        }
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeframe]);

  const filteredUsers = users.filter(user => 
    user?.users?.username?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <FaCrown className="text-yellow-400 text-2xl" />;
      case 2:
        return <FaMedal className="text-gray-400 text-2xl" />;
      case 3:
        return <FaMedal className="text-amber-600 text-2xl" />;
      default:
        return <span className="text-gray-500 font-mono text-lg">#{rank}</span>;
    }
  };

  // Find current user's position
  const currentUserRank = currentUser ? users.findIndex(user => user.user_id === currentUser?.id) + 1 : 0;

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 flex items-center justify-center">
        <div className="bg-red-50 text-red-500 p-4 rounded-lg">
          Error loading leaderboard: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Header */}
      <div className="bg-black text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-4">
              Global Leaderboard
            </h1>
            <p className="text-gray-400 text-lg">
              Top cybersecurity experts competing for glory and rewards.
              Complete challenges to earn points and climb the ranks.
            </p>
          </div>
        </div>
      </div>

      {/* Leaderboard Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Controls */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-primary"
            />
          </div>
          
          <div className="flex gap-2">
            {['all', 'month', 'week'].map((period) => (
              <button
                key={period}
                onClick={() => setTimeframe(period)}
                className={`px-4 py-2 rounded-lg transition-all ${
                  timeframe === period
                    ? 'bg-primary text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-100'
                }`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)} Time
              </button>
            ))}
          </div>
        </div>

        {/* Current User Card (if logged in) */}
        {currentUser && (
          <div className="bg-primary/10 border border-primary/20 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  {currentUser.avatar_url ? (
                    <img
                      src={currentUser.avatar_url}
                      alt={currentUser.username}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <FaUser className="text-primary text-xl" />
                  )}
                </div>
                <div>
                  <div className="font-bold text-gray-900">Your Rank: {currentUserRank > 0 ? currentUserRank : 'Not Ranked'}</div>
                  <div className="text-gray-600">{currentUser.username}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-primary">
                  {users.find(u => u.user_id === currentUser.id)?.total_points || 0} pts
                </div>
                <div className="text-gray-600">
                  {users.find(u => u.user_id === currentUser.id)?.challenges_completed || 0} challenges completed
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Leaderboard Table */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <p className="text-gray-500">No users found</p>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 text-left">
                    <th className="px-6 py-4 text-gray-600">Rank</th>
                    <th className="px-6 py-4 text-gray-600">User</th>
                    <th className="px-6 py-4 text-gray-600">Points</th>
                    <th className="px-6 py-4 text-gray-600">Challenges</th>
                    <th className="px-6 py-4 text-gray-600">Last Active</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user, index) => (
                    <motion.tr
                      key={user.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className={`border-t border-gray-100 ${
                        currentUser && user.user_id === currentUser.id 
                          ? 'bg-primary/5 border-l-4 border-l-primary' 
                          : ''
                      }`}
                    >
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          {getRankIcon(user.rank)}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                            {user.users?.avatar_url ? (
                              <img
                                src={user.users.avatar_url}
                                alt={user.users.username}
                                className="w-full h-full rounded-full"
                              />
                            ) : (
                              <span className="text-primary font-bold">
                                {user.users?.username?.charAt(0).toUpperCase() || '?'}
                              </span>
                            )}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">
                              {user.users?.username || 'Anonymous User'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-primary font-bold">
                          {user.total_points?.toLocaleString() || 0} pts
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-gray-600">
                          {user.challenges_completed || 0}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-gray-600">
                          {user.updated_at ? new Date(user.updated_at).toLocaleDateString() : 'Never'}
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Leaderboard;