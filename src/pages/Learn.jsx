import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaChevronRight, FaChevronDown, FaBars, FaTimes, FaChevronLeft } from 'react-icons/fa';
import { learningPaths } from '../data/learningPaths';
import { getContent } from '../utils/contentLoader';
import OSLayersVisualization from '../components/visualizations/OSLayersVisualization';
import OSTypesVisualization from '../components/visualizations/OSTypesVisualization';
import LabTerminal from '../components/LabTerminal';
import KnowledgeCheck from '../components/KnowledgeCheck';

const visualizationComponents = {
  OSLayersVisualization,
  OSTypesVisualization
};

function Learn() {
  const [selectedPath, setSelectedPath] = useState('fundamentals');
  const [selectedModule, setSelectedModule] = useState('os-concepts');
  const [selectedTopic, setSelectedTopic] = useState('os-introduction');
  const [expandedPaths, setExpandedPaths] = useState(['fundamentals']);
  const [expandedModules, setExpandedModules] = useState(['os-concepts']);
  const [isSidebarOpen, setSidebarOpen] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [labStarted, setLabStarted] = useState(false);

  const content = getContent(selectedTopic);

  // Get current path, module and topic information
  const currentPath = learningPaths.find(p => p.id === selectedPath);
  const currentModule = currentPath?.modules.find(m => m.id === selectedModule);
  const currentTopicIndex = currentModule?.topics?.findIndex(t => t.id === selectedTopic) ?? -1;

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call once on mount
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [selectedTopic]);

  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  const togglePath = (pathId) => {
    setExpandedPaths(prev => 
      prev.includes(pathId)
        ? prev.filter(p => p !== pathId)
        : [...prev, pathId]
    );
  };

  const toggleModule = (moduleId) => {
    setExpandedModules(prev => 
      prev.includes(moduleId)
        ? prev.filter(m => m !== moduleId)
        : [...prev, moduleId]
    );
  };

  const handleNavigation = (direction) => {
    if (!currentModule?.topics) return;

    if (direction === 'prev' && currentTopicIndex > 0) {
      setSelectedTopic(currentModule.topics[currentTopicIndex - 1].id);
    } else if (direction === 'next' && currentTopicIndex < currentModule.topics.length - 1) {
      setSelectedTopic(currentModule.topics[currentTopicIndex + 1].id);
    }
  };

  const renderVisualization = (visualization) => {
    if (!visualization || !visualization.type) return null;

    if (visualization.type === 'interactive') {
      const VisualizationComponent = visualizationComponents[visualization.component];
      if (!VisualizationComponent) return null;

      return (
        <div className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg my-4 sm:my-6">
          <VisualizationComponent data={visualization.data} />
        </div>
      );
    }
    return null;
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-16">
      <div className="flex">
        {/* Mobile Menu Toggle */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="fixed top-20 left-4 z-50 lg:hidden bg-white p-2 rounded-lg shadow-md border border-gray-200"
        >
          {isMobileMenuOpen ? (
            <FaTimes className="text-gray-600" />
          ) : (
            <FaBars className="text-gray-600" />
          )}
        </button>

        {/* Sidebar */}
        <aside 
          className={`fixed inset-y-0 left-0 z-40 bg-gray-50 border-r border-gray-200 transition-all duration-300 ease-in-out ${
            isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
          } ${
            isSidebarOpen ? 'w-64' : 'w-20'
          } lg:relative`}
        >
          {/* Collapse Button */}
          <button
            onClick={toggleSidebar}
            className="absolute -right-3 top-4 bg-white border border-gray-200 rounded-full p-1.5 text-gray-600 hover:text-gray-900 transition-colors z-40 hidden lg:block"
          >
            {isSidebarOpen ? <FaChevronLeft className="w-4 h-4" /> : <FaChevronRight className="w-4 h-4" />}
          </button>

          {/* Sidebar Content - Independently Scrollable */}
          <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
            <div className="p-4">
              <h2 className={`text-lg font-bold text-gray-900 mb-4 transition-opacity duration-200 ${!isSidebarOpen && 'lg:opacity-0'}`}>
                Learning Path
              </h2>
              <div className="space-y-2">
                {learningPaths.map(path => (
                  <div key={path.id}>
                    <button
                      onClick={() => togglePath(path.id)}
                      className={`w-full flex items-center gap-2 p-2 rounded-lg text-left transition-colors ${
                        selectedPath === path.id
                          ? 'bg-primary/10 text-primary'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {expandedPaths.includes(path.id) ? (
                        <FaChevronDown className="flex-shrink-0 w-4 h-4" />
                      ) : (
                        <FaChevronRight className="flex-shrink-0 w-4 h-4" />
                      )}
                      <path.icon className="flex-shrink-0 w-5 h-5" />
                      <span className={`font-medium transition-all duration-200 ${!isSidebarOpen && 'lg:hidden'}`}>
                        {path.title}
                      </span>
                    </button>
                    
                    {expandedPaths.includes(path.id) && path.modules && (
                      <div className={`ml-6 mt-2 space-y-1 transition-all duration-200 ${!isSidebarOpen && 'lg:ml-2'}`}>
                        {path.modules.map(module => (
                          <div key={module.id}>
                            <button
                              onClick={() => {
                                setSelectedPath(path.id);
                                setSelectedModule(module.id);
                                toggleModule(module.id);
                                setIsMobileMenuOpen(false);
                              }}
                              className={`w-full flex items-center gap-2 p-2 rounded-lg text-left text-sm transition-colors ${
                                selectedModule === module.id
                                  ? 'bg-primary text-white'
                                  : 'text-gray-600 hover:bg-gray-100'
                              }`}
                            >
                              <module.icon className="flex-shrink-0 w-4 h-4" />
                              <span className={`transition-all duration-200 ${!isSidebarOpen && 'lg:hidden'}`}>
                                {module.title}
                              </span>
                            </button>

                            {expandedModules.includes(module.id) && module.topics && (
                              <div className={`ml-4 mt-1 space-y-1 transition-all duration-200 ${!isSidebarOpen && 'lg:ml-2'}`}>
                                {module.topics.map(topic => (
                                  <button
                                    key={topic.id}
                                    onClick={() => {
                                      setSelectedTopic(topic.id);
                                      setIsMobileMenuOpen(false);
                                    }}
                                    className={`w-full flex items-center gap-2 p-2 rounded-lg text-left text-sm transition-colors ${
                                      selectedTopic === topic.id
                                        ? 'bg-primary/10 text-primary'
                                        : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                                  >
                                    <topic.icon className="flex-shrink-0 w-3 h-3" />
                                    <span className={`text-xs transition-all duration-200 ${!isSidebarOpen && 'lg:hidden'}`}>
                                      {topic.title}
                                    </span>
                                  </button>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className={`flex-1 transition-all duration-300 ${!isSidebarOpen ? 'lg:ml-20' : ''}`}>
          <div className="px-4 py-8 lg:px-8">
            <div className="max-w-4xl mx-auto">
              {content && (
                <div className="space-y-6 sm:space-y-8">
                  {/* Introduction */}
                  <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">{content.title}</h2>
                        <p className="text-gray-600 text-sm sm:text-base">{content.description}</p>
                      </div>
                    </div>
                  </div>

                  {/* Main Content Sections */}
                  {content.sections && content.sections.map((section, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
                    >
                      <div className="p-4 sm:p-6">
                        <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-4">
                          {section.title}
                        </h3>
                        <div className="prose max-w-none text-sm sm:text-base">
                          {section.content.split('\n\n').map((paragraph, i) => (
                            <p key={i} className="mb-4 text-gray-600 leading-relaxed">
                              {paragraph}
                            </p>
                          ))}
                        </div>
                        {section.visualization && renderVisualization(section.visualization)}
                      </div>
                    </motion.div>
                  ))}

                  {/* Practical Lab */}
                  {content.practicalLab && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                      <div className="p-4 sm:p-6">
                        <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-2">
                          {content.practicalLab.title}
                        </h3>
                        <p className="text-gray-600 text-sm sm:text-base mb-6">
                          {content.practicalLab.description}
                        </p>

                        {!labStarted ? (
                          <button
                            onClick={() => setLabStarted(true)}
                            className="w-full sm:w-auto bg-primary text-white px-6 py-2 rounded-lg font-bold hover:bg-primary-dark transition-colors"
                          >
                            Start Lab
                          </button>
                        ) : (
                          <div className="overflow-x-auto">
                            <LabTerminal commands={content.practicalLab.tasks} />
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Knowledge Check */}
                  {content.conceptualQuiz && content.conceptualQuiz.length > 0 && (
                    <div className="mt-8">
                      <KnowledgeCheck questions={content.conceptualQuiz} />
                    </div>
                  )}

                  {/* Navigation */}
                  <div className="flex items-center justify-between pt-6 sm:pt-8 border-t border-gray-200">
                    <button 
                      onClick={() => handleNavigation('prev')}
                      className={`flex items-center gap-2 text-sm sm:text-base transition-colors ${
                        currentTopicIndex > 0
                          ? 'text-gray-600 hover:text-primary'
                          : 'text-gray-400 cursor-not-allowed'
                      }`}
                      disabled={currentTopicIndex === 0}
                    >
                      <FaChevronLeft />
                      <span>Previous</span>
                    </button>
                    <button 
                      onClick={() => handleNavigation('next')}
                      className={`flex items-center gap-2 text-sm sm:text-base transition-colors ${
                        currentTopicIndex < (currentModule?.topics?.length || 0) - 1
                          ? 'text-gray-600 hover:text-primary'
                          : 'text-gray-400 cursor-not-allowed'
                      }`}
                      disabled={currentTopicIndex === (currentModule?.topics?.length || 0) - 1}
                    >
                      <span>Next</span>
                      <FaChevronRight />
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default Learn;