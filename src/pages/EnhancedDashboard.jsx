import React, { useState } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/dashboard/DashboardLayout';
import DashboardHome from '../components/dashboard/DashboardHome';
import LearningPaths from '../components/dashboard/LearningPaths';
import ModuleView from '../components/dashboard/ModuleView';
import ProgressTracker from '../components/dashboard/ProgressTracker';
import AILearningAssistant from '../components/dashboard/AILearningAssistant';
import { FaLightbulb } from 'react-icons/fa';

const EnhancedDashboard = () => {
  const [isAIAssistantOpen, setIsAIAssistantOpen] = useState(false);
  const navigate = useNavigate();

  return (
    <>
      <DashboardLayout>
        <Routes>
          <Route path="/" element={<DashboardHome />} />
          <Route path="/learning-paths" element={<LearningPaths />} />
          <Route path="/learning-paths/:pathId/modules/:moduleId" element={<ModuleView />} />
          <Route path="/progress" element={<ProgressTracker />} />
          <Route path="*" element={<DashboardHome />} />
        </Routes>
        
        {/* AI Assistant Button */}
        <button
          onClick={() => setIsAIAssistantOpen(true)}
          className="fixed bottom-6 right-6 w-12 h-12 rounded-full bg-primary text-black flex items-center justify-center shadow-lg hover:bg-primary-hover transition-colors"
        >
          <FaLightbulb className="text-xl" />
        </button>
      </DashboardLayout>
      
      {/* AI Learning Assistant Modal */}
      <AILearningAssistant 
        isOpen={isAIAssistantOpen} 
        onClose={() => setIsAIAssistantOpen(false)} 
      />
    </>
  );
};

export default EnhancedDashboard;
