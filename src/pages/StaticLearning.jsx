import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { FaArrowLeft, FaGraduationCap, FaCode, FaShieldAlt, FaNetworkWired, FaClock, FaCheckCircle, FaStar, FaLock, FaUnlock } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { useAuth } from '../contexts/AuthContext';

// Helper function to format time
const formatTime = (minutes) => {
  if (minutes < 60) {
    return `${minutes} min`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

// Enhanced learning modules data with free/premium designation
export const MODULES = [
  {
    id: 'm1',
    slug: 'intro-to-cybersecurity',
    title: 'Introduction to Cybersecurity',
    description: 'Learn the fundamentals of cybersecurity, including key concepts, common threats, and basic security practices. This module provides a solid foundation for beginners.',
    category: { name: 'Web Security' },
    difficulty: { name: 'Begin<PERSON>' },
    estimated_time: 60,
    icon: <FaShieldAlt />,
    isFree: true, // Free module
    content: {
      introduction: "Welcome to the Introduction to Cybersecurity module! In this module, you will learn the fundamental concepts of cybersecurity, understand common threats, and discover basic security practices that everyone should follow. By the end of this module, you will have a solid foundation in cybersecurity principles that will prepare you for more advanced topics.",
      sections: [
        "cybersecurity-basics",
        "common-threats",
        "security-practices",
        "career-paths"
      ],
      section_details: {
        "cybersecurity-basics": {
          title: "Cybersecurity Basics",
          content: "Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These attacks are usually aimed at accessing, changing, or destroying sensitive information; extorting money from users; or interrupting normal business processes. The field encompasses various disciplines, including network security, application security, information security, operational security, and more.",
          key_points: [
            "The CIA Triad: Confidentiality, Integrity, and Availability",
            "Authentication vs. Authorization",
            "Defense in Depth Strategy",
            "Security by Design Principles"
          ]
        },
        "common-threats": {
          title: "Common Cybersecurity Threats",
          content: "Understanding the threat landscape is crucial for effective cybersecurity. This section covers the most common types of cyber threats and attack vectors that organizations and individuals face today.",
          key_points: [
            "Malware: Viruses, Worms, Trojans, Ransomware",
            "Social Engineering: Phishing, Spear Phishing, Pretexting",
            "Man-in-the-Middle Attacks",
            "Denial of Service (DoS) and Distributed Denial of Service (DDoS)",
            "SQL Injection and Cross-Site Scripting (XSS)"
          ]
        },
        "security-practices": {
          title: "Basic Security Practices",
          content: "Implementing good security practices is essential for protecting yourself and your organization from cyber threats. This section covers fundamental security measures that everyone should follow.",
          key_points: [
            "Strong Password Management",
            "Multi-Factor Authentication",
            "Regular Software Updates and Patching",
            "Data Backup and Recovery",
            "Security Awareness and Training"
          ]
        },
        "career-paths": {
          title: "Cybersecurity Career Paths",
          content: "The field of cybersecurity offers diverse career opportunities. This section provides an overview of various cybersecurity roles and the skills required for each.",
          key_points: [
            "Security Analyst",
            "Penetration Tester",
            "Security Engineer",
            "Security Architect",
            "Chief Information Security Officer (CISO)"
          ]
        }
      },
      quiz: {
        questions: [
          {
            question: "What does the 'I' in the CIA triad stand for?",
            options: ["Intelligence", "Integrity", "Internet", "Interface"],
            correct_answer: "Integrity"
          },
          {
            question: "Which of the following is NOT a type of malware?",
            options: ["Virus", "Worm", "Phishing", "Ransomware"],
            correct_answer: "Phishing"
          },
          {
            question: "What is the primary purpose of multi-factor authentication?",
            options: [
              "To make login faster",
              "To eliminate the need for passwords",
              "To add additional layers of security beyond just a password",
              "To track user activity"
            ],
            correct_answer: "To add additional layers of security beyond just a password"
          }
        ]
      },
      resources: [
        {
          title: "NIST Cybersecurity Framework",
          url: "https://www.nist.gov/cyberframework"
        },
        {
          title: "OWASP Top Ten",
          url: "https://owasp.org/www-project-top-ten/"
        }
      ]
    }
  },
  {
    id: 'm2',
    slug: 'web-security-fundamentals',
    title: 'Web Security Fundamentals',
    description: 'Learn about common web vulnerabilities, how they are exploited, and best practices for securing web applications. This module covers essential web security concepts.',
    category: { name: 'Web Security' },
    difficulty: { name: 'Beginner' },
    estimated_time: 75,
    icon: <FaCode />,
    isFree: true, // Free module
    content: {
      introduction: "Welcome to the Web Security Fundamentals module! In this module, you will learn about common web vulnerabilities, how they are exploited by attackers, and best practices for securing web applications. Web security is crucial as most modern businesses rely on web applications for their operations and customer interactions.",
      sections: [
        "web-architecture",
        "common-vulnerabilities",
        "secure-coding",
        "security-testing"
      ],
      section_details: {
        "web-architecture": {
          title: "Web Application Architecture",
          content: "Understanding how web applications work is essential for securing them. This section covers the basic components of web architecture and how they interact.",
          key_points: [
            "Client-Server Model",
            "HTTP/HTTPS Protocols",
            "Frontend vs. Backend",
            "Databases and Data Storage",
            "Authentication and Session Management"
          ]
        },
        "common-vulnerabilities": {
          title: "Common Web Vulnerabilities",
          content: "Web applications are susceptible to various security vulnerabilities. This section covers the most common web vulnerabilities based on the OWASP Top Ten.",
          key_points: [
            "Injection Flaws (SQL, NoSQL, OS, LDAP)",
            "Broken Authentication and Session Management",
            "Cross-Site Scripting (XSS)",
            "Cross-Site Request Forgery (CSRF)",
            "Security Misconfigurations"
          ]
        },
        "secure-coding": {
          title: "Secure Coding Practices",
          content: "Implementing secure coding practices is essential for developing secure web applications. This section covers fundamental principles and techniques for secure coding.",
          key_points: [
            "Input Validation and Sanitization",
            "Parameterized Queries",
            "Output Encoding",
            "Secure Authentication Implementation",
            "Proper Error Handling"
          ]
        },
        "security-testing": {
          title: "Web Application Security Testing",
          content: "Regular security testing is crucial for identifying and addressing vulnerabilities in web applications. This section introduces various approaches to web application security testing.",
          key_points: [
            "Static Application Security Testing (SAST)",
            "Dynamic Application Security Testing (DAST)",
            "Manual Penetration Testing",
            "Security Code Reviews",
            "Continuous Security Testing"
          ]
        }
      },
      quiz: {
        questions: [
          {
            question: "Which of the following is an example of an injection attack?",
            options: ["Cross-Site Scripting", "SQL Injection", "Session Hijacking", "Brute Force Attack"],
            correct_answer: "SQL Injection"
          },
          {
            question: "What is the primary defense against Cross-Site Scripting (XSS) attacks?",
            options: [
              "Input validation",
              "Output encoding",
              "Using HTTPS",
              "Strong passwords"
            ],
            correct_answer: "Output encoding"
          },
          {
            question: "Which of the following is NOT a secure coding practice?",
            options: [
              "Using parameterized queries",
              "Storing passwords in plain text",
              "Implementing proper error handling",
              "Input validation"
            ],
            correct_answer: "Storing passwords in plain text"
          }
        ]
      },
      resources: [
        {
          title: "OWASP Web Security Testing Guide",
          url: "https://owasp.org/www-project-web-security-testing-guide/"
        },
        {
          title: "Mozilla Web Security Guidelines",
          url: "https://infosec.mozilla.org/guidelines/web_security"
        }
      ]
    }
  },
  {
    id: 'm3',
    slug: 'network-security-basics',
    title: 'Network Security Basics',
    description: 'Learn the fundamentals of network security, including common network attacks, defense mechanisms, and monitoring techniques. This module provides essential knowledge for securing networks.',
    category: { name: 'Network Security' },
    difficulty: { name: 'Beginner' },
    estimated_time: 70,
    icon: <FaNetworkWired />,
    isFree: false, // Premium module
    content: {
      introduction: "Welcome to the Network Security Basics module! In this module, you will learn the fundamentals of network security, including network architecture, common attack vectors, defense mechanisms, and monitoring techniques. Network security is critical as it protects the underlying infrastructure that supports information systems and communications.",
      sections: [
        "network-fundamentals",
        "network-threats",
        "defense-mechanisms",
        "monitoring-detection"
      ],
      section_details: {
        "network-fundamentals": {
          title: "Network Fundamentals",
          content: "Understanding how networks function is essential for securing them. This section covers basic network concepts and components.",
          key_points: [
            "OSI and TCP/IP Models",
            "Network Protocols (TCP, UDP, ICMP, etc.)",
            "Network Devices (Routers, Switches, Firewalls)",
            "IP Addressing and Subnetting",
            "Network Topologies"
          ]
        },
        "network-threats": {
          title: "Common Network Threats",
          content: "Networks face various security threats. This section covers common network attacks and vulnerabilities.",
          key_points: [
            "Man-in-the-Middle Attacks",
            "Denial of Service (DoS) and Distributed Denial of Service (DDoS)",
            "ARP Poisoning and MAC Flooding",
            "DNS Attacks (Cache Poisoning, Tunneling)",
            "Wireless Network Attacks"
          ]
        },
        "defense-mechanisms": {
          title: "Network Defense Mechanisms",
          content: "Implementing proper defense mechanisms is crucial for network security. This section covers various tools and techniques for protecting networks.",
          key_points: [
            "Firewalls and Access Control Lists",
            "Intrusion Detection and Prevention Systems",
            "Virtual Private Networks (VPNs)",
            "Network Segmentation",
            "Encryption Protocols (TLS/SSL, IPsec)"
          ]
        },
        "monitoring-detection": {
          title: "Network Monitoring and Detection",
          content: "Continuous monitoring is essential for detecting and responding to network security incidents. This section covers monitoring tools and techniques.",
          key_points: [
            "Network Traffic Analysis",
            "Log Management and Analysis",
            "Security Information and Event Management (SIEM)",
            "Network Behavior Analysis",
            "Incident Response Procedures"
          ]
        }
      },
      quiz: {
        questions: [
          {
            question: "Which layer of the OSI model is responsible for routing?",
            options: ["Physical Layer", "Data Link Layer", "Network Layer", "Transport Layer"],
            correct_answer: "Network Layer"
          },
          {
            question: "What type of attack aims to exhaust a system's resources, making it unavailable to legitimate users?",
            options: [
              "Man-in-the-Middle Attack",
              "SQL Injection",
              "Denial of Service Attack",
              "Cross-Site Scripting"
            ],
            correct_answer: "Denial of Service Attack"
          },
          {
            question: "Which of the following is NOT a function of a firewall?",
            options: [
              "Filtering network traffic",
              "Blocking unauthorized access",
              "Encrypting data",
              "Monitoring network traffic"
            ],
            correct_answer: "Encrypting data"
          }
        ]
      },
      resources: [
        {
          title: "SANS Network Security Resources",
          url: "https://www.sans.org/network-security/"
        },
        {
          title: "Wireshark Network Protocol Analyzer",
          url: "https://www.wireshark.org/"
        }
      ]
    }
  }
];

// Helper function to get module progress from localStorage
const getModuleProgress = (moduleId) => {
  try {
    const progressData = localStorage.getItem('learning_progress');
    if (progressData) {
      const progress = JSON.parse(progressData);
      return progress[moduleId] || 0;
    }
    return 0;
  } catch (error) {
    console.error('Error getting module progress:', error);
    return 0;
  }
};

// Helper function to save module progress to localStorage
const saveModuleProgress = (moduleId, progress) => {
  try {
    const progressData = localStorage.getItem('learning_progress');
    const currentProgress = progressData ? JSON.parse(progressData) : {};
    currentProgress[moduleId] = progress;
    localStorage.setItem('learning_progress', JSON.stringify(currentProgress));
  } catch (error) {
    console.error('Error saving module progress:', error);
  }
};

// Module List Component
const ModuleList = () => {
  const { darkMode } = useGlobalTheme();
  const { profile } = useAuth();
  const [moduleProgress, setModuleProgress] = useState({});

  // Load progress data on component mount
  useEffect(() => {
    try {
      const progressData = localStorage.getItem('learning_progress');
      if (progressData) {
        setModuleProgress(JSON.parse(progressData));
      }
    } catch (error) {
      console.error('Error loading progress data:', error);
    }
  }, []);

  return (
    <div className="learning-page">
      {/* Static content for all users */}
      <div className="mb-8">
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
          <h2 className="text-2xl font-bold mb-4">Welcome to XCerberus Learning Platform</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
            Explore our comprehensive cybersecurity learning modules designed to help you master essential skills and advance your career.
          </p>
          {!profile && (
            <div className="flex flex-wrap gap-3 mt-4">
              <Link to="/login" className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors">
                Sign In to Track Progress
              </Link>
              <Link to="/signup" className="px-4 py-2 border border-[#88cc14] text-[#88cc14] hover:bg-[#88cc14]/10 font-medium rounded-lg transition-colors">
                Create Free Account
              </Link>
            </div>
          )}
        </div>

        {/* Learning Modules */}
        <h2 className="text-xl font-bold mb-4">Available Learning Modules</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          {MODULES.map(module => (
            <div
              key={module.id}
              className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden transition-transform hover:transform hover:scale-[1.02] flex flex-col h-full`}
            >
              <div className="p-6 flex flex-col flex-grow">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-xl font-bold">{module.title}</h3>
                  <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
                    {module.icon}
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 mb-3">
                  <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                    {module.category.name}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                    {module.difficulty.name}
                  </span>
                  {module.estimated_time && (
                    <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
                      <FaClock className="mr-1" /> {formatTime(module.estimated_time)}
                    </span>
                  )}
                  {/* Free/Premium Badge */}
                  <span className={`px-2 py-1 rounded text-xs flex items-center ${module.isFree
                    ? darkMode ? 'bg-green-900/20 text-green-300' : 'bg-green-100 text-green-800'
                    : darkMode ? 'bg-amber-900/20 text-amber-300' : 'bg-amber-100 text-amber-800'}`}>
                    {module.isFree ? <FaUnlock className="mr-1" /> : <FaLock className="mr-1" />}
                    {module.isFree ? 'Free' : 'Premium'}
                  </span>
                </div>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  {module.description}
                </p>

                {/* Preview Content Section */}
                <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-3 rounded-lg mb-4 border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  <h4 className="font-medium mb-2 flex items-center">
                    <FaGraduationCap className="mr-2 text-[#88cc14]" /> Preview Content
                  </h4>
                  <ul className={`list-disc list-inside space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {module.topics && module.topics.slice(0, 3).map((topic, index) => (
                      <li key={index}>{topic}</li>
                    ))}
                    {!module.topics && [
                      <li key="1">Introduction to {module.title}</li>,
                      <li key="2">Core concepts and terminology</li>,
                      <li key="3">Hands-on practice exercises</li>
                    ]}
                  </ul>
                </div>

                {/* Progress Bar */}
                {moduleProgress[module.id] > 0 && (
                  <div className="mb-4">
                    <div className="flex justify-between text-xs mb-1">
                      <span>{moduleProgress[module.id] === 100 ? 'Completed' : 'In Progress'}</span>
                      <span>{moduleProgress[module.id]}%</span>
                    </div>
                    <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
                      <div
                        className={`h-2 rounded-full ${moduleProgress[module.id] === 100 ? 'bg-green-500' : 'bg-[#88cc14]'}`}
                        style={{ width: `${moduleProgress[module.id]}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                <div className="mt-auto pt-4 flex flex-col gap-2">
                  <Link
                    to={`/learn/preview/${module.slug}`}
                    className={`block w-full text-center px-4 py-2 ${module.isFree
                      ? 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                      : profile?.subscription_tier === 'premium' || profile?.subscription_tier === 'business'
                        ? 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                        : 'bg-gray-600 text-gray-300 cursor-not-allowed'} font-medium rounded-lg transition-colors`}
                    onClick={(e) => {
                      if (!module.isFree && (!profile || profile.subscription_tier === 'free')) {
                        e.preventDefault();
                        alert('This is a premium module. Please upgrade to access this content.');
                      }
                    }}
                  >
                    {moduleProgress[module.id] > 0
                      ? moduleProgress[module.id] === 100
                        ? 'Review Module'
                        : 'Continue Learning'
                      : 'Start Learning'}
                  </Link>

                  {!profile && (
                    <Link to="/login" className={`w-full text-center px-4 py-2 ${darkMode ? 'bg-gray-800 hover:bg-gray-700 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'} font-medium rounded-lg transition-colors flex items-center justify-center`}>
                      <FaGraduationCap className="mr-2" /> Sign In to Track Progress
                    </Link>
                  )}

                  {profile && !module.isFree && profile.subscription_tier === 'free' && (
                    <Link to="/pricing" className={`w-full text-center px-4 py-2 ${darkMode ? 'bg-amber-800 hover:bg-amber-700 text-white' : 'bg-amber-500 hover:bg-amber-600 text-white'} font-medium rounded-lg transition-colors flex items-center justify-center`}>
                      <FaLock className="mr-2" /> Upgrade to Access
                    </Link>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
              <FaGraduationCap className="text-[#88cc14]" />
            </div>
            <h2 className="text-xl font-bold">Why Learn With Us?</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-2">
            <div className="p-4 border border-dashed rounded-lg border-gray-700">
              <h3 className="font-bold mb-2">Practical Skills</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Hands-on exercises and real-world scenarios to build practical cybersecurity skills.</p>
            </div>
            <div className="p-4 border border-dashed rounded-lg border-gray-700">
              <h3 className="font-bold mb-2">Industry Relevant</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Content aligned with industry standards and current cybersecurity practices.</p>
            </div>
            <div className="p-4 border border-dashed rounded-lg border-gray-700">
              <h3 className="font-bold mb-2">Career Growth</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Structured learning paths to help you advance your cybersecurity career.</p>
            </div>
          </div>
        </div>

        {/* Current Learning Paths */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
          <h2 className="text-xl font-bold mb-4">Current Learning Paths</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg`}>
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                  <FaShieldAlt className="text-blue-500" />
                </div>
                <h3 className="font-bold">Defensive Security</h3>
              </div>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm mb-3`}>
                Learn to protect systems and networks from cyber threats with defensive security techniques.
              </p>
              <div className="flex items-center text-sm">
                <span className="flex items-center mr-3"><FaGraduationCap className="mr-1" /> 8 Modules</span>
                <span className="flex items-center"><FaClock className="mr-1" /> 24 Hours</span>
              </div>
            </div>

            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg`}>
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
                  <FaCode className="text-red-500" />
                </div>
                <h3 className="font-bold">Offensive Security</h3>
              </div>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm mb-3`}>
                Master ethical hacking and penetration testing to identify and exploit vulnerabilities.
              </p>
              <div className="flex items-center text-sm">
                <span className="flex items-center mr-3"><FaGraduationCap className="mr-1" /> 10 Modules</span>
                <span className="flex items-center"><FaClock className="mr-1" /> 30 Hours</span>
              </div>
            </div>
          </div>
        </div>

        {/* Learning Experience */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
          <h2 className="text-xl font-bold mb-4">Your Learning Experience</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex flex-col h-full">
              <div className="bg-gradient-to-r from-[#88cc14]/20 to-transparent p-5 rounded-lg mb-4 flex-grow">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-[#88cc14]/30 rounded-full flex items-center justify-center">
                    <FaShieldAlt className="text-[#88cc14]" />
                  </div>
                  <h3 className="text-lg font-bold">Interactive Labs</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-3`}>
                  Practice in real-world environments with our hands-on virtual labs. Apply what you learn immediately with guided exercises that reinforce key concepts.
                </p>
                <ul className={`list-disc list-inside space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  <li>Secure sandbox environments</li>
                  <li>Real-world attack simulations</li>
                  <li>Guided step-by-step exercises</li>
                </ul>
              </div>
            </div>

            <div className="flex flex-col h-full">
              <div className="bg-gradient-to-r from-blue-500/20 to-transparent p-5 rounded-lg mb-4 flex-grow">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-blue-500/30 rounded-full flex items-center justify-center">
                    <FaGraduationCap className="text-blue-500" />
                  </div>
                  <h3 className="text-lg font-bold">Structured Learning</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-3`}>
                  Follow a carefully designed curriculum that builds your skills progressively. Track your progress and earn certificates as you complete modules.
                </p>
                <ul className={`list-disc list-inside space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  <li>Progressive skill development</li>
                  <li>Knowledge checkpoints</li>
                  <li>Completion certificates</li>
                </ul>
              </div>
            </div>

            <div className="flex flex-col h-full">
              <div className="bg-gradient-to-r from-purple-500/20 to-transparent p-5 rounded-lg flex-grow">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-purple-500/30 rounded-full flex items-center justify-center">
                    <FaNetworkWired className="text-purple-500" />
                  </div>
                  <h3 className="text-lg font-bold">Community Support</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-3`}>
                  Join a community of cybersecurity enthusiasts. Collaborate on challenges, share insights, and learn from peers and industry experts.
                </p>
                <ul className={`list-disc list-inside space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  <li>Discussion forums</li>
                  <li>Peer collaboration</li>
                  <li>Expert mentorship</li>
                </ul>
              </div>
            </div>

            <div className="flex flex-col h-full">
              <div className="bg-gradient-to-r from-red-500/20 to-transparent p-5 rounded-lg flex-grow">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-red-500/30 rounded-full flex items-center justify-center">
                    <FaCode className="text-red-500" />
                  </div>
                  <h3 className="text-lg font-bold">Practical Challenges</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-3`}>
                  Test your skills with realistic cybersecurity challenges. Solve puzzles, find vulnerabilities, and build your problem-solving abilities.
                </p>
                <ul className={`list-disc list-inside space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  <li>CTF-style challenges</li>
                  <li>Vulnerability hunting</li>
                  <li>Defensive exercises</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

// Module Detail Component
const ModuleDetail = () => {
  const { darkMode } = useGlobalTheme();
  const { moduleId } = useParams();
  const { profile } = useAuth();
  const [progress, setProgress] = useState(0);
  const [currentSection, setCurrentSection] = useState(null);

  // Find the module by slug
  const module = MODULES.find(m => m.slug === moduleId || m.id === moduleId);

  // Load progress on component mount
  useEffect(() => {
    if (module) {
      const savedProgress = getModuleProgress(module.id);
      setProgress(savedProgress);
    }
  }, [module]);

  // Handle section click
  const handleSectionClick = (sectionKey) => {
    setCurrentSection(sectionKey);

    // Update progress when a section is viewed
    // In a real implementation, this would be more sophisticated
    // based on actual content consumption
    if (progress < 100) {
      const sectionCount = module.content.sections.length;
      const sectionProgress = Math.floor(100 / sectionCount);
      const newProgress = Math.min(progress + sectionProgress, 100);
      setProgress(newProgress);
      saveModuleProgress(module.id, newProgress);
    }
  };

  // Handle module completion
  const handleCompleteModule = () => {
    saveModuleProgress(module.id, 100);
    setProgress(100);
    alert('Congratulations! You have completed this module.');
  };

  // If module not found
  if (!module) {
    return (
      <div>
        <div className="mb-6">
          <Link to="/learn" className="flex items-center text-[#88cc14] hover:underline">
            <FaArrowLeft className="mr-2" /> Back to Learning Modules
          </Link>
        </div>

        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
          <h2 className="text-2xl font-bold mb-4">Module Not Found</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            The learning module you're looking for doesn't exist or has been removed.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <Link to="/learn" className="flex items-center text-[#88cc14] hover:underline">
          <FaArrowLeft className="mr-2" /> Back to Learning Modules
        </Link>
      </div>

      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
        <div className="flex justify-between items-start mb-4">
          <h1 className="text-2xl font-bold">{module.title}</h1>
          <div className="w-12 h-12 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
            {module.icon}
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mb-4">
          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
            {module.category.name}
          </span>
          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
            {module.difficulty.name}
          </span>
          {module.estimated_time && (
            <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
              <FaClock className="mr-1" /> {formatTime(module.estimated_time)}
            </span>
          )}
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm mb-1">
            <span>
              {progress === 0 ? 'Not Started' :
               progress === 100 ? 'Completed' : 'In Progress'}
            </span>
            <span>{progress}%</span>
          </div>
          <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
            <div
              className={`h-2 rounded-full ${progress === 100 ? 'bg-green-500' : 'bg-[#88cc14]'}`}
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Module Introduction */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Introduction</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {module.content.introduction}
          </p>
        </div>

        {/* Module Sections */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">Module Content</h2>

          {module.content.sections && module.content.sections.length > 0 ? (
            <div>
              <ul className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg divide-y ${darkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                {module.content.sections.map((sectionKey, index) => {
                  const section = module.content.section_details[sectionKey];
                  return (
                    <li key={sectionKey} className={`p-4 ${currentSection === sectionKey ? `${darkMode ? 'bg-gray-800' : 'bg-gray-100'}` : ''}`}>
                      <div className="flex items-start">
                        <span className={`w-8 h-8 flex items-center justify-center rounded-full mr-3 ${
                          currentSection === sectionKey
                            ? 'bg-[#88cc14] text-black'
                            : darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                        }`}>
                          {index + 1}
                        </span>
                        <div className="flex-1">
                          <h3 className="font-medium">{section.title}</h3>
                          <p className={`mt-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {section.content.substring(0, 100)}...
                          </p>
                          <div className="mt-2">
                            <button
                              onClick={() => handleSectionClick(sectionKey)}
                              className={`text-sm px-3 py-1 rounded-md ${
                                currentSection === sectionKey
                                  ? darkMode ? 'bg-[#88cc14] text-black' : 'bg-[#88cc14] text-black'
                                  : darkMode ? 'text-[#88cc14] hover:bg-gray-700' : 'text-[#88cc14] hover:bg-gray-200'
                              }`}
                            >
                              {currentSection === sectionKey ? 'Currently Viewing' : 'View Section'}
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            </div>
          ) : (
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              No sections available for this module.
            </p>
          )}
        </div>

        {/* Resources */}
        {module.content.resources && module.content.resources.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Additional Resources</h2>
            <ul className={`list-disc pl-5 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {module.content.resources.map((resource, index) => (
                <li key={index} className="mb-1">
                  <a
                    href={resource.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#88cc14] hover:underline"
                  >
                    {resource.title}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Selected Section Content */}
        {currentSection && (
          <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6 mb-6`}>
            <h2 className="text-xl font-semibold mb-4">{module.content.section_details[currentSection].title}</h2>
            <div className="prose prose-lg max-w-none dark:prose-invert">
              <p>{module.content.section_details[currentSection].content}</p>

              {/* Display key points if available */}
              {module.content.section_details[currentSection].key_points && (
                <div className="mt-4">
                  <h3 className="text-lg font-semibold mb-2">Key Points</h3>
                  <ul className="list-disc pl-5">
                    {module.content.section_details[currentSection].key_points.map((point, idx) => (
                      <li key={idx} className="mb-1">{point}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Display quiz if available */}
              {module.content.section_details[currentSection].quiz && (
                <div className="mt-6 p-4 border border-dashed rounded-lg border-gray-500">
                  <h3 className="text-lg font-semibold mb-3">Knowledge Check</h3>
                  <p className="mb-4">Test your understanding of this section with these questions:</p>

                  <div className="space-y-4">
                    {module.content.section_details[currentSection].quiz.map((question, idx) => (
                      <div key={idx} className="p-3 bg-opacity-50 rounded-lg bg-gray-800">
                        <p className="font-medium mb-2">{question.question}</p>
                        <div className="space-y-2">
                          {question.options.map((option, optIdx) => (
                            <div key={optIdx} className="flex items-center">
                              <input
                                type="radio"
                                id={`q${idx}-opt${optIdx}`}
                                name={`question-${idx}`}
                                className="mr-2"
                              />
                              <label htmlFor={`q${idx}-opt${optIdx}`}>{option}</label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Display exercise if available */}
              {module.content.section_details[currentSection].exercise && (
                <div className="mt-6 p-4 border border-dashed rounded-lg border-gray-500">
                  <h3 className="text-lg font-semibold mb-2">{module.content.section_details[currentSection].exercise.title}</h3>
                  <p className="mb-3">{module.content.section_details[currentSection].exercise.description}</p>

                  <div className="space-y-3">
                    {module.content.section_details[currentSection].exercise.scenarios.map((scenario, idx) => (
                      <div key={idx} className="p-3 bg-opacity-50 rounded-lg bg-gray-800">
                        <p className="mb-2"><strong>Scenario {idx + 1}:</strong> {scenario}</p>
                        <input
                          type="text"
                          placeholder="Your answer..."
                          className="w-full p-2 rounded bg-gray-700 text-white border border-gray-600"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Module Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mt-8 pt-6 border-t border-gray-700">
          <button
            onClick={handleCompleteModule}
            className={`w-full sm:w-auto px-6 py-3 ${progress === 100 ? 'bg-green-500 hover:bg-green-600' : 'bg-[#88cc14] hover:bg-[#7ab811]'} text-black font-medium rounded-lg transition-colors flex items-center justify-center`}
          >
            <FaCheckCircle className="mr-2" /> {progress === 100 ? 'Completed!' : 'Mark as Completed'}
          </button>

          {/* Rating */}
          <div className="flex items-center">
            <span className="mr-2">Rate:</span>
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  className="text-2xl focus:outline-none"
                >
                  <FaStar
                    className={`${
                      darkMode ? 'text-gray-700' : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Component
const StaticLearning = () => {
  const { darkMode } = useGlobalTheme();
  const { moduleId } = useParams();

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold mb-6">
          {moduleId ? 'Learning Module' : 'Learning Modules'}
        </h1>

        {moduleId ? <ModuleDetail /> : <ModuleList />}
      </div>
    </div>
  );
};

export default StaticLearning;
