import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { FaArrowLeft, FaGraduationCap, FaLock, FaCheckCircle, FaExternalLinkAlt, FaLightbulb, FaQuestion, FaCode, FaShieldAlt, FaUserSecret, FaServer, FaDatabase, FaDesktop, FaNetworkWired } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { MODULES } from './StaticLearning';

const ModulePreview = () => {
  const { darkMode } = useGlobalTheme();
  const { moduleSlug } = useParams();
  const [activeTab, setActiveTab] = useState('content');
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [quizSubmitted, setQuizSubmitted] = useState(false);
  const [password, setPassword] = useState('');
  const [passwordStrength, setPasswordStrength] = useState(null);
  // We don't need the activeTopic state anymore as we'll display content based on the module directly
  const [localProgress, setLocalProgress] = useState(() => {
    const savedProgress = localStorage.getItem('moduleProgress');
    return savedProgress ? JSON.parse(savedProgress) : { viewed: [], completed: [] };
  });

  // Find the module by slug
  const module = MODULES.find(m => m.slug === moduleSlug);

  // Function to check password strength
  const checkPasswordStrength = (pwd) => {
    if (!pwd) return null;

    // Calculate password strength
    let score = 0;

    // Length check
    if (pwd.length >= 8) score += 1;
    if (pwd.length >= 12) score += 1;

    // Complexity checks
    if (/[A-Z]/.test(pwd)) score += 1; // Has uppercase
    if (/[a-z]/.test(pwd)) score += 1; // Has lowercase
    if (/[0-9]/.test(pwd)) score += 1; // Has number
    if (/[^A-Za-z0-9]/.test(pwd)) score += 1; // Has special char

    // Return strength category
    if (score <= 2) return { strength: 'weak', color: 'red', text: 'Very Weak' };
    if (score <= 4) return { strength: 'moderate', color: 'yellow', text: 'Moderate' };
    return { strength: 'strong', color: 'green', text: 'Strong' };
  };

  // Handle password check
  const handlePasswordCheck = () => {
    const result = checkPasswordStrength(password);
    setPasswordStrength(result);
  };

  // Save progress to localStorage when sections are viewed
  useEffect(() => {
    if (module) {
      // Mark this module as viewed
      if (!localProgress.viewed.includes(moduleSlug)) {
        const updatedProgress = {
          ...localProgress,
          viewed: [...localProgress.viewed, moduleSlug]
        };
        setLocalProgress(updatedProgress);
        localStorage.setItem('moduleProgress', JSON.stringify(updatedProgress));
      }
    }
  }, [module, moduleSlug, localProgress]);

  // If module not found
  if (!module) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
        <div className="container mx-auto px-4">
          <div className="mb-6">
            <Link to="/learn" className="flex items-center text-[#88cc14] hover:underline">
              <FaArrowLeft className="mr-2" /> <span className="hidden sm:inline">Back to Learning Modules</span><span className="sm:hidden">Back</span>
            </Link>
          </div>

          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
            <h2 className="text-2xl font-bold mb-4">Module Not Found</h2>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              The learning module you're looking for doesn't exist or has been removed.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <div className="mb-6">
          <Link to="/learn" className="flex items-center text-[#88cc14] hover:underline">
            <FaArrowLeft className="mr-2" /> Back to Learning Modules
          </Link>
        </div>

        {/* Module Header */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-4 sm:p-6 mb-6`}>
          <div className="flex flex-col sm:flex-row items-center sm:items-start sm:justify-between">
            <div className="text-center sm:text-left mb-4 sm:mb-0">
              <h1 className="text-2xl sm:text-3xl font-bold mb-2">{module.title}</h1>
              <div className="flex flex-wrap justify-center sm:justify-start gap-2 mb-4">
                <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                  {module.category.name}
                </span>
                <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                  {module.difficulty.name}
                </span>
              </div>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl text-sm sm:text-base`}>
                {module.description}
              </p>
            </div>
            <div className="w-16 h-16 bg-[#88cc14]/20 rounded-full flex items-center justify-center flex-shrink-0">
              {module.icon}
            </div>
          </div>
        </div>

        {/* Preview Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar - Module Progress (Now on the left) */}
          <div className="lg:col-span-1 order-2 lg:order-1">
            {/* Mobile Progress Toggle */}
            <div className="block lg:hidden mb-4">
              <button
                onClick={() => document.getElementById('mobile-progress').classList.toggle('hidden')}
                className="w-full flex items-center justify-between p-3 bg-[#1A1F35] border border-gray-700 rounded-lg"
              >
                <span className="font-medium">Show Module Progress</span>
                <FaCheckCircle className="text-[#88cc14]" />
              </button>
            </div>

            {/* Module Progress - Fixed position when scrolling */}
            <div id="mobile-progress" className="hidden lg:block sticky top-24 h-[calc(100vh-120px)] overflow-y-auto pb-6 pr-2 -mr-2">
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
                <h3 className="font-bold mb-4">Module Progress</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="font-medium">Introduction</span>
                      <span className="flex items-center text-green-500"><FaCheckCircle className="mr-1" /> Free</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden">
                      <div className="bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:bg-green-400" style={{ width: '100%' }}>
                        <div className="absolute inset-0 bg-white/20 animate-pulse"></div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="font-medium">Core Concepts</span>
                      <span className="flex items-center text-gray-500"><FaLock className="mr-1" /> Premium</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden">
                      <div className="bg-gray-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:w-[10%]" style={{ width: '0%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="font-medium">Practical Applications</span>
                      <span className="flex items-center text-gray-500"><FaLock className="mr-1" /> Premium</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden">
                      <div className="bg-gray-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:w-[10%]" style={{ width: '0%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="font-medium">Hands-on Lab</span>
                      <span className="flex items-center text-gray-500"><FaLock className="mr-1" /> Premium</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden">
                      <div className="bg-gray-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:w-[10%]" style={{ width: '0%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="font-medium">Assessment Quiz</span>
                      <span className="flex items-center text-gray-500"><FaLock className="mr-1" /> Premium</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden">
                      <div className="bg-gray-500 h-2.5 rounded-full transition-all duration-500 ease-out hover:w-[10%]" style={{ width: '0%' }}></div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <Link to="/signup" className="block w-full text-center px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors">
                    Unlock Full Module
                  </Link>
                </div>

                {/* Preview Content List */}
                <div className="mt-6 pt-6 border-t border-gray-700">
                  <h3 className="font-bold mb-4">Preview Content</h3>
                  <div className="space-y-2">
                    <div className="flex items-center text-green-500">
                      <FaCheckCircle className="mr-2" /> Introduction to {module.title}
                    </div>
                    {module.content?.sections?.slice(0, 2).map((section, index) => {
                      const sectionDetail = module.content?.section_details?.[section];
                      return sectionDetail ? (
                        <div key={index} className="flex items-center text-green-500">
                          <FaCheckCircle className="mr-2" /> Core concepts and terminology
                        </div>
                      ) : null;
                    })}
                    <div className="flex items-center text-green-500">
                      <FaCheckCircle className="mr-2" /> Hands-on practice exercises
                    </div>
                  </div>
                </div>

                {/* Related Modules */}
                <div className="mt-6 pt-6 border-t border-gray-700">
                  <h3 className="font-bold mb-4">Related Modules</h3>
                  <div className="space-y-3">
                    {MODULES.filter(m => m.category.name === module.category.name && m.id !== module.id)
                      .slice(0, 2)
                      .map(relatedModule => (
                        <div key={relatedModule.id} className={`p-3 rounded-lg ${darkMode ? 'bg-[#252D4A] hover:bg-[#2A3356]' : 'bg-gray-50 hover:bg-gray-100'} transition-colors`}>
                          <Link to={`/learn/preview/${relatedModule.slug}`} className="flex items-start">
                            <div className="w-8 h-8 bg-[#88cc14]/20 rounded-full flex items-center justify-center mr-3">
                              {relatedModule.icon}
                            </div>
                            <div>
                              <h4 className="font-medium">{relatedModule.title}</h4>
                              <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                {relatedModule.difficulty.name}
                              </p>
                            </div>
                          </Link>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content (Now wider and on the right) */}
          <div className="lg:col-span-3 order-1 lg:order-2">
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-4 sm:p-6 mb-6`}>
              <div className="flex flex-col sm:flex-row items-center gap-3 mb-6 text-center sm:text-left">
                <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center mb-2 sm:mb-0">
                  <FaGraduationCap className="text-[#88cc14]" />
                </div>
                <h2 className="text-xl sm:text-2xl font-bold">{module.title}</h2>
              </div>

              <div className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} leading-relaxed text-sm sm:text-base`}>
                <p className="text-base sm:text-lg mb-6">
                  {module.content?.introduction || `Welcome to this introductory lesson on ${module.title}. This free preview will give you a taste of what the full module offers.`}
                </p>

                {/* Interactive tabs for navigation - Mobile Friendly */}
                <div className="flex flex-wrap border-b border-gray-700 mb-6">
                  <button
                    onClick={() => setActiveTab('content')}
                    className={`px-3 sm:px-4 py-2 text-sm sm:text-base font-medium ${activeTab === 'content' ? 'border-b-2 border-[#88cc14] text-[#88cc14]' : 'text-gray-400 hover:text-gray-300'}`}
                  >
                    Content
                  </button>
                  <button
                    onClick={() => setActiveTab('exercises')}
                    className={`px-3 sm:px-4 py-2 text-sm sm:text-base font-medium ${activeTab === 'exercises' ? 'border-b-2 border-[#88cc14] text-[#88cc14]' : 'text-gray-400 hover:text-gray-300'}`}
                  >
                    Exercises
                  </button>
                  <button
                    onClick={() => setActiveTab('quiz')}
                    className={`px-3 sm:px-4 py-2 text-sm sm:text-base font-medium ${activeTab === 'quiz' ? 'border-b-2 border-[#88cc14] text-[#88cc14]' : 'text-gray-400 hover:text-gray-300'}`}
                  >
                    Quiz
                  </button>

                  {/* Progress Indicator */}
                  <div className="ml-auto flex items-center text-xs text-gray-400 px-2">
                    <div className="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
                    <span>Progress saved locally</span>
                  </div>
                </div>

                {activeTab === 'content' && (
                  <>


                    {/* Cybersecurity Basics Content */}
                    {moduleSlug === 'intro-to-cybersecurity' && (
                      <>
                        <div className="mb-8">
                          <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">Learning Objectives</h3>
                          <ul className="space-y-2 list-disc pl-5">
                            {module.content?.sections?.map((section, index) => {
                              const sectionDetail = module.content?.section_details?.[section];
                              return sectionDetail ? (
                                <li key={index}>Understand {sectionDetail.title.toLowerCase()}</li>
                              ) : null;
                            }) || [
                              <li key="default1">Understand the core principles of cybersecurity</li>,
                              <li key="default2">Learn about common threats and vulnerabilities</li>,
                              <li key="default3">Explore basic security practices and controls</li>
                            ]}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Web Security Content */}
                    {moduleSlug === 'web-security-fundamentals' && (
                      <>
                        {/* Engaging Story */}
                        <div className="mb-8">
                          <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">The Vulnerable Storefront: A Web Security Tale</h3>
                          <div className="p-4 bg-[#1A1F35] rounded-lg border border-gray-700 mb-4">
                            <p className="mb-3">Alex had just launched his dream e-commerce site selling custom artwork. Business was booming until one day, customers started reporting unauthorized charges on their credit cards after shopping on his site.</p>

                            <p className="mb-3">An investigation revealed that hackers had exploited a Cross-Site Scripting (XSS) vulnerability in his website's comment section. The attackers injected malicious JavaScript code that captured payment information as customers entered it.</p>

                            <p className="mb-3">Additionally, they discovered an SQL injection vulnerability in the site's search function that allowed attackers to access the customer database directly. The breach not only cost Alex financially but damaged his reputation with customers.</p>

                            <p>Working with a security consultant, Alex implemented proper input validation, parameterized queries, content security policies, and HTTPS encryption. He also set up regular security scans and penetration testing. The site was now secure, but Alex learned that web security should have been a priority from day one, not an afterthought.</p>
                          </div>
                          <p className="text-sm text-gray-400 italic">This story demonstrates how common web vulnerabilities can impact real businesses and why implementing proper security measures is essential for any web application.</p>
                        </div>

                        <div className="mb-8">
                          <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">Web Security Fundamentals</h3>
                          <p className="mb-4">Web security focuses on protecting websites, web applications, and web services from security threats that could compromise data, disrupt service, or damage reputation.</p>

                          {/* Think of it like... section */}
                          <div className="bg-[#2A3356] p-4 rounded-lg mb-6 border border-[#3D4976]">
                            <div className="flex items-center gap-2 mb-2">
                              <div className="w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center">
                                <FaLightbulb className="text-yellow-500" />
                              </div>
                              <h4 className="font-bold text-yellow-400">Think of it like...</h4>
                            </div>
                            <p>
                              Web security is like securing a storefront. You need locks on the doors (authentication), security cameras (logging), alarm systems (intrusion detection), and trained staff (secure coding practices) to protect your valuable merchandise (data).
                            </p>
                          </div>

                          <h4 className="font-bold text-lg mb-3">Common Web Security Threats</h4>
                          <div className="space-y-4 mb-6">
                            <div className="flex items-start">
                              <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center mt-1 mr-3">
                                <span className="text-red-500 font-bold">1</span>
                              </div>
                              <div>
                                <h5 className="font-bold">Cross-Site Scripting (XSS)</h5>
                                <p>Attackers inject malicious scripts into trusted websites that execute in users' browsers, potentially stealing cookies, session tokens, or other sensitive information.</p>
                              </div>
                            </div>

                            <div className="flex items-start">
                              <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center mt-1 mr-3">
                                <span className="text-red-500 font-bold">2</span>
                              </div>
                              <div>
                                <h5 className="font-bold">SQL Injection</h5>
                                <p>Attackers insert malicious SQL code into database queries through web application inputs, potentially allowing them to access, modify, or delete data.</p>
                              </div>
                            </div>

                            <div className="flex items-start">
                              <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center mt-1 mr-3">
                                <span className="text-red-500 font-bold">3</span>
                              </div>
                              <div>
                                <h5 className="font-bold">Cross-Site Request Forgery (CSRF)</h5>
                                <p>Attackers trick authenticated users into executing unwanted actions on websites where they're logged in, by exploiting the trust a site has in a user's browser.</p>
                              </div>
                            </div>
                          </div>

                          <h4 className="font-bold text-lg mb-3">Key Protection Measures</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                              <h5 className="font-bold mb-2">HTTPS & TLS</h5>
                              <p>
                                Encrypting data in transit between clients and servers to prevent eavesdropping and man-in-the-middle attacks.
                              </p>
                            </div>

                            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                              <h5 className="font-bold mb-2">Content Security Policy</h5>
                              <p>
                                Restricting which resources can be loaded and executed on your website to mitigate XSS attacks.
                              </p>
                            </div>

                            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                              <h5 className="font-bold mb-2">Input Validation</h5>
                              <p>
                                Sanitizing and validating all user inputs to prevent injection attacks like SQL injection and XSS.
                              </p>
                            </div>

                            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                              <h5 className="font-bold mb-2">Authentication & Authorization</h5>
                              <p>
                                Implementing strong user authentication and proper access controls to protect sensitive functionality and data.
                              </p>
                            </div>
                          </div>
                        </div>
                      </>
                    )}

                    {/* Network Security Content */}
                    {moduleSlug === 'network-security-basics' && (
                      <>
                        {/* Engaging Story */}
                        <div className="mb-8">
                          <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">The Hospital Breach: A Network Security Case Study</h3>
                          <div className="p-4 bg-[#1A1F35] rounded-lg border border-gray-700 mb-4">
                            <p className="mb-3">Memorial Hospital prided itself on its modern medical facilities, but its network security hadn't been updated in years. One winter morning, the hospital's systems began behaving strangely—medical records were inaccessible, and equipment was going offline.</p>

                            <p className="mb-3">The IT team discovered they were experiencing a massive Distributed Denial of Service (DDoS) attack. While the staff was distracted by this obvious attack, hackers had quietly exploited an unpatched vulnerability in the hospital's network perimeter to gain access to internal systems.</p>

                            <p className="mb-3">The attackers had been inside the network for weeks, moving laterally between systems through unsegmented networks. They had accessed sensitive patient data and were preparing to deploy ransomware across the entire hospital infrastructure.</p>

                            <p>Fortunately, an alert security analyst noticed unusual traffic patterns and isolated critical systems before the ransomware could be fully deployed. In the aftermath, the hospital implemented a defense-in-depth strategy: properly configured firewalls, network segmentation, intrusion detection systems, regular patching, and 24/7 monitoring. The incident became a powerful reminder that in network security, a single vulnerability can put an entire organization at risk.</p>
                          </div>
                          <p className="text-sm text-gray-400 italic">This story illustrates the importance of comprehensive network security measures and how multiple layers of protection work together to safeguard critical infrastructure.</p>
                        </div>

                        <div className="mb-8">
                          <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">Network Security Basics</h3>
                          <p className="mb-4">Network security involves protecting the integrity, confidentiality, and accessibility of computer networks and data using both hardware and software technologies.</p>

                          {/* Think of it like... section */}
                          <div className="bg-[#2A3356] p-4 rounded-lg mb-6 border border-[#3D4976]">
                            <div className="flex items-center gap-2 mb-2">
                              <div className="w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center">
                                <FaLightbulb className="text-yellow-500" />
                              </div>
                              <h4 className="font-bold text-yellow-400">Think of it like...</h4>
                            </div>
                            <p>
                              Network security is like securing a castle. You have walls (firewalls), guards at the gates (access controls), moats (network segmentation), watchtowers (intrusion detection systems), and secure communication channels (encryption) to protect everything inside.
                            </p>
                          </div>

                          <h4 className="font-bold text-lg mb-3">Network Security Components</h4>
                          <div className="space-y-4 mb-6">
                            <div className="flex items-start">
                              <div className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mt-1 mr-3">
                                <span className="text-blue-500 font-bold">1</span>
                              </div>
                              <div>
                                <h5 className="font-bold">Firewalls</h5>
                                <p>Hardware or software barriers that monitor and control incoming and outgoing network traffic based on predetermined security rules.</p>
                              </div>
                            </div>

                            <div className="flex items-start">
                              <div className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mt-1 mr-3">
                                <span className="text-blue-500 font-bold">2</span>
                              </div>
                              <div>
                                <h5 className="font-bold">Intrusion Detection/Prevention Systems</h5>
                                <p>Systems that monitor network traffic for suspicious activity and policy violations, alerting administrators or taking automated actions when threats are detected.</p>
                              </div>
                            </div>

                            <div className="flex items-start">
                              <div className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mt-1 mr-3">
                                <span className="text-blue-500 font-bold">3</span>
                              </div>
                              <div>
                                <h5 className="font-bold">Virtual Private Networks (VPNs)</h5>
                                <p>Encrypted connections that provide secure access to a private network over public infrastructure, protecting data in transit.</p>
                              </div>
                            </div>
                          </div>

                          <h4 className="font-bold text-lg mb-3">Common Network Attacks</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                              <h5 className="font-bold mb-2">Denial of Service (DoS)</h5>
                              <p>
                                Overwhelming a system with traffic or requests to make it unavailable to legitimate users.
                              </p>
                            </div>

                            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                              <h5 className="font-bold mb-2">Man-in-the-Middle</h5>
                              <p>
                                Intercepting and potentially altering communications between two parties without their knowledge.
                              </p>
                            </div>

                            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                              <h5 className="font-bold mb-2">ARP Spoofing</h5>
                              <p>
                                Linking an attacker's MAC address with a legitimate IP address, allowing them to intercept network traffic.
                              </p>
                            </div>

                            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                              <h5 className="font-bold mb-2">Port Scanning</h5>
                              <p>
                                Probing a network to identify open ports and potential vulnerabilities that could be exploited.
                              </p>
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </>
                )}

                {activeTab === 'content' && moduleSlug === 'intro-to-cybersecurity' && (
                  <>
                    {/* Engaging Story */}
                    <div className="mb-8">
                      <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">The Digital Fortress: A Cybersecurity Story</h3>
                      <div className="p-4 bg-[#1A1F35] rounded-lg border border-gray-700 mb-4">
                        <p className="mb-3">Sarah, a small business owner, never thought much about cybersecurity until one morning when she couldn't access her company's customer database. A message appeared on her screen demanding $10,000 in Bitcoin to restore her data.</p>

                        <p className="mb-3">Her business had fallen victim to ransomware—malicious software that encrypted all her important files. The attack had come through a seemingly innocent email attachment opened by one of her employees.</p>

                        <p className="mb-3">After a costly recovery process and nearly a week of downtime, Sarah implemented proper security measures: regular backups, employee training on recognizing phishing attempts, strong password policies, and updated software on all devices.</p>

                        <p>Six months later, when a similar attack targeted businesses in her area, Sarah's digital fortress remained secure. The same security principles that protected Sarah's business—confidentiality, integrity, and availability—form the foundation of all effective cybersecurity strategies.</p>
                      </div>
                      <p className="text-sm text-gray-400 italic">This story illustrates how basic cybersecurity principles can protect against common threats and why they matter to everyone, not just large organizations.</p>
                    </div>

                    {/* Interactive Diagram */}
                    <div className="mb-8">
                      <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">Interactive Security Diagram</h3>
                      <p className="mb-4">Explore the different components of a secure network by hovering over each element:</p>

                      <div className="relative w-full h-[300px] md:h-[400px] bg-[#1A1F35] rounded-lg border border-gray-700 p-4 mb-6 overflow-hidden">
                        {/* Network Diagram */}
                        <div className="absolute inset-0 flex items-center justify-center">
                          {/* Connection Lines - Draw these first so they appear behind nodes */}
                          <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 0 }}>
                            <line x1="28%" y1="50%" x2="42%" y2="30%" stroke="#4B5563" strokeWidth="2" strokeDasharray="5,5" />
                            <line x1="28%" y1="50%" x2="42%" y2="70%" stroke="#4B5563" strokeWidth="2" strokeDasharray="5,5" />
                            <line x1="50%" y1="38%" x2="50%" y2="62%" stroke="#4B5563" strokeWidth="2" />
                            <line x1="58%" y1="30%" x2="72%" y2="40%" stroke="#4B5563" strokeWidth="2" />
                            <line x1="58%" y1="70%" x2="72%" y2="70%" stroke="#4B5563" strokeWidth="2" strokeDasharray="5,5" />
                          </svg>
                          {/* Firewall */}
                          <div className="absolute left-[20%] top-[50%] transform -translate-y-1/2 group cursor-pointer">
                            <div className="w-16 h-16 bg-red-900/30 rounded-full flex items-center justify-center border-2 border-red-500/50 group-hover:bg-red-900/50 transition-all duration-300">
                              <FaShieldAlt className="text-red-400 text-2xl group-hover:text-red-300" />
                            </div>
                            <div className="opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none">
                              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"></div>
                              <p className="font-bold mb-1">Firewall</p>
                              <p>Monitors and filters incoming and outgoing network traffic based on security rules.</p>
                            </div>
                          </div>

                          {/* Server */}
                          <div className="absolute left-[50%] top-[30%] transform -translate-x-1/2 -translate-y-1/2 group cursor-pointer">
                            <div className="w-16 h-16 bg-blue-900/30 rounded-full flex items-center justify-center border-2 border-blue-500/50 group-hover:bg-blue-900/50 transition-all duration-300">
                              <FaServer className="text-blue-400 text-2xl group-hover:text-blue-300" />
                            </div>
                            <div className="opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none">
                              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"></div>
                              <p className="font-bold mb-1">Secure Server</p>
                              <p>Hosts applications and services with security patches and hardened configurations.</p>
                            </div>
                          </div>

                          {/* Database */}
                          <div className="absolute left-[50%] top-[70%] transform -translate-x-1/2 -translate-y-1/2 group cursor-pointer">
                            <div className="w-16 h-16 bg-green-900/30 rounded-full flex items-center justify-center border-2 border-green-500/50 group-hover:bg-green-900/50 transition-all duration-300">
                              <FaDatabase className="text-green-400 text-2xl group-hover:text-green-300" />
                            </div>
                            <div className="opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none">
                              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"></div>
                              <p className="font-bold mb-1">Encrypted Database</p>
                              <p>Stores sensitive data with encryption at rest and in transit.</p>
                            </div>
                          </div>

                          {/* Client Computer */}
                          <div className="absolute right-[20%] top-[40%] transform -translate-y-1/2 group cursor-pointer">
                            <div className="w-16 h-16 bg-purple-900/30 rounded-full flex items-center justify-center border-2 border-purple-500/50 group-hover:bg-purple-900/50 transition-all duration-300">
                              <FaDesktop className="text-purple-400 text-2xl group-hover:text-purple-300" />
                            </div>
                            <div className="opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none">
                              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"></div>
                              <p className="font-bold mb-1">Client Computer</p>
                              <p>End-user device with antivirus, firewall, and security updates.</p>
                            </div>
                          </div>

                          {/* Hacker */}
                          <div className="absolute right-[20%] top-[70%] transform -translate-y-1/2 group cursor-pointer">
                            <div className="w-16 h-16 bg-yellow-900/30 rounded-full flex items-center justify-center border-2 border-yellow-500/50 group-hover:bg-yellow-900/50 transition-all duration-300">
                              <FaUserSecret className="text-yellow-400 text-2xl group-hover:text-yellow-300" />
                            </div>
                            <div className="opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-3 bg-gray-800/95 rounded shadow-lg text-xs text-white w-48 transition-all duration-300 z-20 touch-action-manipulation pointer-events-none">
                              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gray-800/95"></div>
                              <p className="font-bold mb-1">Threat Actor</p>
                              <p>Malicious entity attempting to gain unauthorized access to systems and data.</p>
                            </div>
                          </div>


                        </div>
                      </div>
                      <p className="text-sm text-gray-400 italic">Hover or tap on each component to learn more about its role in cybersecurity.</p>

                      {/* Mobile-friendly component list */}
                      <div className="md:hidden mt-4 space-y-2">
                        <div className="flex items-center p-2 bg-[#252D4A] rounded border border-gray-700">
                          <div className="w-8 h-8 bg-red-900/30 rounded-full flex items-center justify-center border-2 border-red-500/50 mr-3">
                            <FaShieldAlt className="text-red-400" />
                          </div>
                          <div>
                            <p className="font-bold text-sm">Firewall</p>
                            <p className="text-xs text-gray-400">Monitors and filters network traffic</p>
                          </div>
                        </div>
                        <div className="flex items-center p-2 bg-[#252D4A] rounded border border-gray-700">
                          <div className="w-8 h-8 bg-blue-900/30 rounded-full flex items-center justify-center border-2 border-blue-500/50 mr-3">
                            <FaServer className="text-blue-400" />
                          </div>
                          <div>
                            <p className="font-bold text-sm">Secure Server</p>
                            <p className="text-xs text-gray-400">Hosts applications and services</p>
                          </div>
                        </div>
                        <div className="flex items-center p-2 bg-[#252D4A] rounded border border-gray-700">
                          <div className="w-8 h-8 bg-green-900/30 rounded-full flex items-center justify-center border-2 border-green-500/50 mr-3">
                            <FaDatabase className="text-green-400" />
                          </div>
                          <div>
                            <p className="font-bold text-sm">Encrypted Database</p>
                            <p className="text-xs text-gray-400">Stores sensitive data securely</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Code Snippet Example */}
                    <div className="mb-8">
                      <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">Code Example</h3>
                      <div className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'} mb-4`}>
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <FaCode className="mr-2 text-[#88cc14]" />
                            <span className="font-mono font-medium">Simple Encryption Example</span>
                          </div>
                          <div className="flex gap-2">
                            <button className="px-2 py-1 text-xs bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">Copy</button>
                            <button className="px-2 py-1 text-xs bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">Run</button>
                          </div>
                        </div>
                        <pre className="font-mono text-sm overflow-x-auto p-2 bg-[#0B1120] rounded">
                          <code className="text-gray-300">
{`# Simple Caesar Cipher implementation
def encrypt(text, shift):
    result = ""
    # traverse the plain text
    for char in text:
        if char.isalpha():
            # Determine if uppercase or lowercase
            ascii_offset = ord('A') if char.isupper() else ord('a')
            # shift the current character
            result += chr((ord(char) - ascii_offset + shift) % 26 + ascii_offset)
        else:
            # If not a letter, keep it as is
            result += char
    return result

# Example usage
message = "Hello World"
shift = 3
encrypted = encrypt(message, shift)
print(f"Original: {message}")
print(f"Encrypted: {encrypted}")`}
                          </code>
                        </pre>
                      </div>
                    </div>
                  </>
                )}

                {activeTab === 'exercises' && (
                  <div className="mb-8">
                    <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">Interactive Exercises</h3>

                    {/* Cybersecurity Module Exercises */}
                    {moduleSlug === 'intro-to-cybersecurity' && (
                      <>
                        {/* Security Audit Challenge */}
                        <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-5 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'} mb-6`}>
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-bold">Security Audit Challenge</h4>
                            <span className="px-2 py-1 bg-blue-900/30 text-blue-400 rounded text-xs font-medium">Interactive Demo</span>
                          </div>
                          <p className="mb-4">Identify security vulnerabilities in this virtual office environment. Click on areas that might pose security risks.</p>

                          {/* Interactive Office Environment */}
                          <div className="relative w-full h-[300px] bg-[#1A1F35] rounded-lg border border-gray-700 mb-4 overflow-hidden">
                            {/* Office Background */}
                            <div className="absolute inset-0 p-4 flex flex-col">
                              <div className="grid grid-cols-2 gap-4 h-full">
                                {/* Left Side - Workstations */}
                                <div className="flex flex-col space-y-4">
                                  {/* Workstation 1 - Post-it with password */}
                                  <div className="relative bg-[#252D4A] rounded p-3 flex-1 cursor-pointer hover:bg-[#2A3356] transition-colors"
                                    onClick={() => {
                                      const foundIssues = JSON.parse(localStorage.getItem('securityIssuesFound') || '[]');
                                      if (!foundIssues.includes('password-postit')) {
                                        foundIssues.push('password-postit');
                                        localStorage.setItem('securityIssuesFound', JSON.stringify(foundIssues));
                                        alert('Issue found: Password written on a post-it note! This is a security risk as anyone can see it.');
                                      }
                                    }}
                                  >
                                    <div className="flex justify-between">
                                      <span className="text-sm font-medium">Workstation 1</span>
                                      <span className="text-xs text-gray-400">Marketing Dept</span>
                                    </div>
                                    <div className="mt-2 flex items-center">
                                      <div className="w-8 h-8 bg-gray-700 rounded-full mr-2"></div>
                                      <div className="text-xs">John\'s Computer</div>
                                    </div>
                                    <div className="absolute bottom-2 right-2 w-6 h-6 bg-yellow-200 rotate-3 flex items-center justify-center">
                                      <span className="text-[8px] text-black">PWD: admin123</span>
                                    </div>
                                  </div>

                                  {/* Workstation 2 - Unlocked computer */}
                                  <div className="relative bg-[#252D4A] rounded p-3 flex-1 cursor-pointer hover:bg-[#2A3356] transition-colors"
                                    onClick={() => {
                                      const foundIssues = JSON.parse(localStorage.getItem('securityIssuesFound') || '[]');
                                      if (!foundIssues.includes('unlocked-computer')) {
                                        foundIssues.push('unlocked-computer');
                                        localStorage.setItem('securityIssuesFound', JSON.stringify(foundIssues));
                                        alert('Issue found: Unlocked computer with no screen timeout! This allows unauthorized access when the user is away.');
                                      }
                                    }}
                                  >
                                    <div className="flex justify-between">
                                      <span className="text-sm font-medium">Workstation 2</span>
                                      <span className="text-xs text-gray-400">Sales Dept</span>
                                    </div>
                                    <div className="mt-2 flex items-center">
                                      <div className="w-8 h-8 bg-gray-700 rounded-full mr-2"></div>
                                      <div className="text-xs">Sarah's Computer</div>
                                    </div>
                                    <div className="absolute top-2 right-2 text-xs text-green-400">Unlocked</div>
                                  </div>
                                </div>

                                {/* Right Side - Server and Network */}
                                <div className="flex flex-col space-y-4">
                                  {/* Server - Outdated software */}
                                  <div className="relative bg-[#252D4A] rounded p-3 flex-1 cursor-pointer hover:bg-[#2A3356] transition-colors"
                                    onClick={() => {
                                      const foundIssues = JSON.parse(localStorage.getItem('securityIssuesFound') || '[]');
                                      if (!foundIssues.includes('outdated-software')) {
                                        foundIssues.push('outdated-software');
                                        localStorage.setItem('securityIssuesFound', JSON.stringify(foundIssues));
                                        alert('Issue found: Server running outdated software with known vulnerabilities! This needs to be updated immediately.');
                                      }
                                    }}
                                  >
                                    <div className="flex justify-between">
                                      <span className="text-sm font-medium">File Server</span>
                                      <span className="text-xs text-gray-400">Main Office</span>
                                    </div>
                                    <div className="mt-2 flex items-center">
                                      <div className="w-8 h-8 bg-gray-700 rounded mr-2 flex items-center justify-center">
                                        <div className="w-4 h-4 bg-blue-500 rounded"></div>
                                      </div>
                                      <div className="text-xs">FileShare-01</div>
                                    </div>
                                    <div className="absolute bottom-2 right-2 text-xs text-red-400">OS v2.1 (2018)</div>
                                  </div>

                                  {/* WiFi - Open network */}
                                  <div className="relative bg-[#252D4A] rounded p-3 flex-1 cursor-pointer hover:bg-[#2A3356] transition-colors"
                                    onClick={() => {
                                      const foundIssues = JSON.parse(localStorage.getItem('securityIssuesFound') || '[]');
                                      if (!foundIssues.includes('open-wifi')) {
                                        foundIssues.push('open-wifi');
                                        localStorage.setItem('securityIssuesFound', JSON.stringify(foundIssues));
                                        alert('Issue found: Open WiFi network with no password! This allows anyone to connect and potentially access internal resources.');
                                      }
                                    }}
                                  >
                                    <div className="flex justify-between">
                                      <span className="text-sm font-medium">Office WiFi</span>
                                      <span className="text-xs text-gray-400">All Floors</span>
                                    </div>
                                    <div className="mt-2 flex items-center">
                                      <div className="w-8 h-8 bg-gray-700 rounded-full mr-2 flex items-center justify-center">
                                        <div className="text-white text-xs">WiFi</div>
                                      </div>
                                      <div className="text-xs">CompanyNet</div>
                                    </div>
                                    <div className="absolute top-2 right-2 text-xs text-red-400">Open Network</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Challenge Progress */}
                          <div className="mb-4">
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Security Issues Found</span>
                              <span className="text-sm" id="security-issues-count">0/4</span>
                            </div>
                            <div className="w-full bg-gray-700 rounded-full h-2.5 relative overflow-hidden">
                              <div className="bg-[#88cc14] h-2.5 rounded-full transition-all duration-500 ease-out" id="security-issues-progress" style={{ width: '0%' }}></div>
                            </div>
                          </div>

                          <div className="flex justify-between items-center">
                            <button
                              onClick={() => {
                                localStorage.setItem('securityIssuesFound', '[]');
                                document.getElementById('security-issues-count').textContent = '0/4';
                                document.getElementById('security-issues-progress').style.width = '0%';
                              }}
                              className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors text-sm"
                            >
                              Reset Challenge
                            </button>
                            <p className="text-sm text-gray-400 italic">Click on areas that look insecure</p>
                          </div>

                          <script dangerouslySetInnerHTML={{ __html: `
                            function updateSecurityProgress() {
                              const foundIssues = JSON.parse(localStorage.getItem('securityIssuesFound') || '[]');
                              const count = foundIssues.length;
                              const progressElement = document.getElementById('security-issues-progress');
                              const countElement = document.getElementById('security-issues-count');

                              if (progressElement && countElement) {
                                progressElement.style.width = (count / 4 * 100) + '%';
                                countElement.textContent = count + '/4';

                                if (count === 4) {
                                  setTimeout(() => {
                                    alert('Congratulations! You found all the security issues. Sign up to access more advanced security audit challenges!');
                                  }, 500);
                                }
                              }
                            }

                            // Run on page load
                            updateSecurityProgress();

                            // Set up a mutation observer to watch for DOM changes
                            const observer = new MutationObserver(updateSecurityProgress);
                            observer.observe(document.body, { subtree: true, childList: true });
                          ` }}
                          />
                        </div>

                        {/* Password Strength Checker */}
                        <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-5 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'} mb-4`}>
                          <h4 className="font-bold mb-3">Password Strength Checker</h4>
                          <p className="mb-4">Test your understanding of password security by evaluating the strength of these passwords:</p>

                          <div className="space-y-3 mb-4">
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2">password123</span>
                              <span className="px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium">Very Weak</span>
                            </div>
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2">P@ssw0rd!</span>
                              <span className="px-2 py-1 bg-yellow-900/30 text-yellow-400 rounded text-xs font-medium">Moderate</span>
                            </div>
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2">uR5%9Lq*zX@2vB</span>
                              <span className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium">Strong</span>
                            </div>
                          </div>

                          <div className="flex flex-col sm:flex-row items-center gap-2 mt-4">
                            <input
                              type="text"
                              placeholder="Try your own password"
                              className="w-full p-2 bg-[#1A1F35] border border-gray-700 rounded"
                              value={password}
                              onChange={(e) => setPassword(e.target.value)}
                              onKeyPress={(e) => e.key === 'Enter' && handlePasswordCheck()}
                            />
                            <button
                              onClick={handlePasswordCheck}
                              className="w-full sm:w-auto px-4 py-2 bg-[#88cc14] text-black rounded hover:bg-[#7ab811] transition-colors mt-2 sm:mt-0"
                            >
                              Check
                            </button>
                          </div>
                          {passwordStrength && (
                            <div className="mt-3 p-2 rounded border flex items-center"
                              style={{
                                backgroundColor: `${passwordStrength.color === 'red' ? 'rgba(220, 38, 38, 0.1)' :
                                  passwordStrength.color === 'yellow' ? 'rgba(234, 179, 8, 0.1)' : 'rgba(34, 197, 94, 0.1)'}`,
                                borderColor: `${passwordStrength.color === 'red' ? 'rgba(220, 38, 38, 0.3)' :
                                  passwordStrength.color === 'yellow' ? 'rgba(234, 179, 8, 0.3)' : 'rgba(34, 197, 94, 0.3)'}`
                              }}
                            >
                              <div className="w-2 h-2 rounded-full mr-2"
                                style={{
                                  backgroundColor: `${passwordStrength.color === 'red' ? 'rgb(220, 38, 38)' :
                                    passwordStrength.color === 'yellow' ? 'rgb(234, 179, 8)' : 'rgb(34, 197, 94)'}`
                                }}
                              ></div>
                              <span style={{
                                color: `${passwordStrength.color === 'red' ? 'rgb(248, 113, 113)' :
                                  passwordStrength.color === 'yellow' ? 'rgb(250, 204, 21)' : 'rgb(74, 222, 128)'}`
                              }}>
                                {passwordStrength.text}
                              </span>
                            </div>
                          )}
                          <p className="text-sm text-gray-400 mt-2">Note: Your password is not stored or transmitted</p>
                        </div>
                      </>
                    )}

                    {/* Web Security Module Exercises */}
                    {moduleSlug === 'web-security-fundamentals' && (
                      <>
                        {/* SQL Injection Challenge */}
                        <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-5 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'} mb-6`}>
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-bold">SQL Injection Challenge</h4>
                            <span className="px-2 py-1 bg-blue-900/30 text-blue-400 rounded text-xs font-medium">Interactive Demo</span>
                          </div>
                          <p className="mb-4">Fix the vulnerable code to prevent SQL injection attacks. Replace the unsafe query with a parameterized query.</p>

                          {/* Code Editor Simulation */}
                          <div className="mb-4">
                            <div className="flex items-center justify-between bg-[#1A1F35] rounded-t border border-gray-700 p-2">
                              <div className="flex items-center">
                                <div className="w-3 h-3 rounded-full bg-red-500 mr-1.5"></div>
                                <div className="w-3 h-3 rounded-full bg-yellow-500 mr-1.5"></div>
                                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                              </div>
                              <span className="text-xs text-gray-400">login.php</span>
                            </div>
                            <div className="bg-[#0B1120] p-4 rounded-b border-x border-b border-gray-700 font-mono text-sm overflow-x-auto">
                              <pre className="text-gray-300">
                                <code>
{`<?php
// Get user input
$username = $_POST['username'];
$password = $_POST['password'];

// VULNERABLE CODE - SQL Injection possible!
$query = "SELECT * FROM users WHERE username = '$username' AND password = '$password'";
$result = mysqli_query($connection, $query);

// SECURE THIS CODE BELOW:
// ...
// ...
// ...
`}
                                </code>
                              </pre>
                            </div>
                          </div>

                          {/* Solution Input */}
                          <div className="mb-4">
                            <label className="block text-sm font-medium mb-2">Your Solution:</label>
                            <textarea
                              id="sql-solution"
                              rows="4"
                              className="w-full p-3 bg-[#1A1F35] border border-gray-700 rounded font-mono text-sm"
                              placeholder="Write your secure code here..."
                            ></textarea>
                          </div>

                          <div className="flex justify-between items-center">
                            <button
                              onClick={() => {
                                const solution = document.getElementById('sql-solution').value.toLowerCase();
                                if (solution.includes('prepare') ||
                                    (solution.includes('?') && solution.includes('bind_param')) ||
                                    solution.includes('placeholder') && solution.includes('bindvalue')) {
                                  alert('Correct! You\'ve used parameterized queries to prevent SQL injection. This is the right approach!');
                                  localStorage.setItem('sqlChallengeCompleted', 'true');
                                  document.getElementById('sql-challenge-status').textContent = 'Completed';
                                  document.getElementById('sql-challenge-status').className = 'px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium';
                                } else if (solution.trim() === '') {
                                  alert('Please enter your solution first.');
                                } else {
                                  alert('Not quite right. Your solution might still be vulnerable to SQL injection. Try using prepared statements with placeholders.');
                                }
                              }}
                              className="px-4 py-2 bg-[#88cc14] text-black rounded hover:bg-[#7ab811] transition-colors"
                            >
                              Check Solution
                            </button>
                            <span id="sql-challenge-status" className="px-2 py-1 bg-yellow-900/30 text-yellow-400 rounded text-xs font-medium">
                              {localStorage.getItem('sqlChallengeCompleted') === 'true' ? 'Completed' : 'Pending'}
                            </span>
                          </div>

                          <div className="mt-4 p-3 bg-[#1A1F35] border border-gray-700 rounded">
                            <h5 className="font-medium mb-2">Hint:</h5>
                            <p className="text-sm text-gray-400">Use prepared statements with placeholders (?) and bind the parameters separately from the SQL query.</p>
                          </div>
                        </div>

                        {/* SQL Injection Detector */}
                        <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-5 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'} mb-4`}>
                          <h4 className="font-bold mb-3">SQL Injection Detector</h4>
                          <p className="mb-4">Identify which of these inputs contain potential SQL injection attacks:</p>

                          <div className="space-y-3 mb-4">
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2 font-mono text-sm">John Smith</span>
                              <span className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium">Safe</span>
                            </div>
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2 font-mono text-sm">admin' OR 1=1 --</span>
                              <span className="px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium">SQL Injection</span>
                            </div>
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2 font-mono text-sm"><EMAIL></span>
                              <span className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium">Safe</span>
                            </div>
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2 font-mono text-sm">'; DROP TABLE users; --</span>
                              <span className="px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium">SQL Injection</span>
                            </div>
                          </div>

                          <div className="mt-4 p-3 bg-[#1A1F35] border border-gray-700 rounded">
                            <h5 className="font-medium mb-2">How to Prevent SQL Injection:</h5>
                            <ul className="list-disc pl-5 space-y-1 text-sm">
                              <li>Use parameterized queries or prepared statements</li>
                              <li>Implement input validation and sanitization</li>
                              <li>Apply the principle of least privilege for database accounts</li>
                              <li>Use ORM (Object-Relational Mapping) libraries</li>
                            </ul>
                          </div>
                        </div>

                        {/* XSS Vulnerability Checker */}
                        <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-5 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'} mb-4`}>
                          <h4 className="font-bold mb-3">XSS Vulnerability Checker</h4>
                          <p className="mb-4">Identify which of these inputs contain potential Cross-Site Scripting (XSS) attacks:</p>

                          <div className="space-y-3 mb-4">
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2 font-mono text-sm">Hello, world!</span>
                              <span className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium">Safe</span>
                            </div>
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2 font-mono text-sm">&lt;script&gt;alert('XSS')&lt;/script&gt;</span>
                              <span className="px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium">XSS Attack</span>
                            </div>
                            <div className="flex flex-wrap items-center justify-between p-2 bg-[#1A1F35] rounded border border-gray-700">
                              <span className="mr-2 font-mono text-sm">img src="x" onerror="alert('XSS')"</span>
                              <span className="px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-medium">XSS Attack</span>
                            </div>
                          </div>
                        </div>
                      </>
                    )}

                    {/* Network Security Module Exercises */}
                    {moduleSlug === 'network-security-basics' && (
                      <>
                        {/* Network Traffic Analysis Challenge */}
                        <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-5 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'} mb-6`}>
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-bold">Network Traffic Analysis Challenge</h4>
                            <span className="px-2 py-1 bg-blue-900/30 text-blue-400 rounded text-xs font-medium">Interactive Demo</span>
                          </div>
                          <p className="mb-4">Analyze this network traffic capture and identify suspicious activities. Select all packets that indicate potential security threats.</p>

                          {/* Packet Capture Simulation */}
                          <div className="mb-4 overflow-x-auto">
                            <table className="w-full min-w-full text-xs border border-gray-700 rounded">
                              <thead className="bg-[#1A1F35]">
                                <tr>
                                  <th className="p-2 border-b border-r border-gray-700 text-left">Select</th>
                                  <th className="p-2 border-b border-r border-gray-700 text-left">Time</th>
                                  <th className="p-2 border-b border-r border-gray-700 text-left">Source</th>
                                  <th className="p-2 border-b border-r border-gray-700 text-left">Destination</th>
                                  <th className="p-2 border-b border-r border-gray-700 text-left">Protocol</th>
                                  <th className="p-2 border-b border-gray-700 text-left">Info</th>
                                </tr>
                              </thead>
                              <tbody className="bg-[#0B1120]">
                                <tr className="packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors">
                                  <td className="p-2 border-b border-r border-gray-700">
                                    <input type="checkbox" className="packet-checkbox" data-threat="false" />
                                  </td>
                                  <td className="p-2 border-b border-r border-gray-700">10:15:23</td>
                                  <td className="p-2 border-b border-r border-gray-700">***********</td>
                                  <td className="p-2 border-b border-r border-gray-700">8.8.8.8</td>
                                  <td className="p-2 border-b border-r border-gray-700">DNS</td>
                                  <td className="p-2 border-b border-gray-700">Standard DNS query for google.com</td>
                                </tr>
                                <tr className="packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors">
                                  <td className="p-2 border-b border-r border-gray-700">
                                    <input type="checkbox" className="packet-checkbox" data-threat="true" />
                                  </td>
                                  <td className="p-2 border-b border-r border-gray-700">10:15:45</td>
                                  <td className="p-2 border-b border-r border-gray-700">***********</td>
                                  <td className="p-2 border-b border-r border-gray-700">***********</td>
                                  <td className="p-2 border-b border-r border-gray-700">ARP</td>
                                  <td className="p-2 border-b border-gray-700 text-yellow-400">ARP: Duplicate IP address detected</td>
                                </tr>
                                <tr className="packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors">
                                  <td className="p-2 border-b border-r border-gray-700">
                                    <input type="checkbox" className="packet-checkbox" data-threat="false" />
                                  </td>
                                  <td className="p-2 border-b border-r border-gray-700">10:16:02</td>
                                  <td className="p-2 border-b border-r border-gray-700">***********0</td>
                                  <td className="p-2 border-b border-r border-gray-700">*************</td>
                                  <td className="p-2 border-b border-r border-gray-700">HTTPS</td>
                                  <td className="p-2 border-b border-gray-700">TLSv1.2 Client Hello</td>
                                </tr>
                                <tr className="packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors">
                                  <td className="p-2 border-b border-r border-gray-700">
                                    <input type="checkbox" className="packet-checkbox" data-threat="true" />
                                  </td>
                                  <td className="p-2 border-b border-r border-gray-700">10:16:30</td>
                                  <td className="p-2 border-b border-r border-gray-700">************</td>
                                  <td className="p-2 border-b border-r border-gray-700">***********</td>
                                  <td className="p-2 border-b border-r border-gray-700">TCP</td>
                                  <td className="p-2 border-b border-gray-700 text-red-400">Port scan detected (multiple ports)</td>
                                </tr>
                                <tr className="packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors">
                                  <td className="p-2 border-b border-r border-gray-700">
                                    <input type="checkbox" className="packet-checkbox" data-threat="false" />
                                  </td>
                                  <td className="p-2 border-b border-r border-gray-700">10:17:05</td>
                                  <td className="p-2 border-b border-r border-gray-700">***********5</td>
                                  <td className="p-2 border-b border-r border-gray-700">***********</td>
                                  <td className="p-2 border-b border-r border-gray-700">ICMP</td>
                                  <td className="p-2 border-b border-gray-700">Echo (ping) request</td>
                                </tr>
                                <tr className="packet-row hover:bg-[#1A1F35] cursor-pointer transition-colors">
                                  <td className="p-2 border-b border-r border-gray-700">
                                    <input type="checkbox" className="packet-checkbox" data-threat="true" />
                                  </td>
                                  <td className="p-2 border-b border-r border-gray-700">10:17:45</td>
                                  <td className="p-2 border-b border-r border-gray-700">***********0</td>
                                  <td className="p-2 border-b border-r border-gray-700">192.168.1.255</td>
                                  <td className="p-2 border-b border-r border-gray-700">UDP</td>
                                  <td className="p-2 border-b border-gray-700 text-red-400">Excessive broadcast traffic (possible DoS)</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>

                          <div className="flex justify-between items-center mb-4">
                            <button
                              onClick={() => {
                                const checkboxes = document.querySelectorAll('.packet-checkbox');
                                let correctCount = 0;
                                let totalThreats = 0;

                                checkboxes.forEach(checkbox => {
                                  const isThreat = checkbox.getAttribute('data-threat') === 'true';
                                  if (isThreat) {
                                    totalThreats++;
                                    if (checkbox.checked) correctCount++;
                                  } else if (checkbox.checked) {
                                    correctCount--; // Penalty for false positives
                                  }
                                });

                                if (correctCount === totalThreats) {
                                  alert('Great job! You correctly identified all the suspicious network traffic patterns.');
                                  localStorage.setItem('networkAnalysisCompleted', 'true');
                                  document.getElementById('network-analysis-status').textContent = 'Completed';
                                  document.getElementById('network-analysis-status').className = 'px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-medium';
                                } else if (correctCount <= 0) {
                                  alert('Try again. You missed some suspicious packets or selected normal traffic.');
                                } else {
                                  alert(`You found ${correctCount} out of ${totalThreats} suspicious packets. Keep looking!`);
                                }
                              }}
                              className="px-4 py-2 bg-[#88cc14] text-black rounded hover:bg-[#7ab811] transition-colors"
                            >
                              Analyze Selected Packets
                            </button>
                            <span id="network-analysis-status" className="px-2 py-1 bg-yellow-900/30 text-yellow-400 rounded text-xs font-medium">
                              {localStorage.getItem('networkAnalysisCompleted') === 'true' ? 'Completed' : 'Pending'}
                            </span>
                          </div>

                          <div className="p-3 bg-[#1A1F35] border border-gray-700 rounded">
                            <h5 className="font-medium mb-2">Hint:</h5>
                            <p className="text-sm text-gray-400">Look for unusual patterns like port scans, duplicate IP addresses, excessive broadcast traffic, and other anomalies that could indicate an attack.</p>
                          </div>
                        </div>

                        {/* Firewall Rule Configuration */}
                        <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-5 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'} mb-4`}>
                          <h4 className="font-bold mb-3">Firewall Rule Configuration</h4>
                          <p className="mb-4">Determine which network traffic should be allowed or blocked based on security best practices:</p>

                          <div className="overflow-x-auto">
                            <table className="w-full min-w-full text-sm">
                              <thead>
                                <tr className="border-b border-gray-700">
                                  <th className="text-left py-2 px-3">Source</th>
                                  <th className="text-left py-2 px-3">Destination</th>
                                  <th className="text-left py-2 px-3">Port</th>
                                  <th className="text-left py-2 px-3">Protocol</th>
                                  <th className="text-left py-2 px-3">Decision</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr className="border-b border-gray-700">
                                  <td className="py-2 px-3">Internet</td>
                                  <td className="py-2 px-3">Web Server</td>
                                  <td className="py-2 px-3">443</td>
                                  <td className="py-2 px-3">HTTPS</td>
                                  <td className="py-2 px-3"><span className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs">Allow</span></td>
                                </tr>
                                <tr className="border-b border-gray-700">
                                  <td className="py-2 px-3">Internet</td>
                                  <td className="py-2 px-3">Database Server</td>
                                  <td className="py-2 px-3">3306</td>
                                  <td className="py-2 px-3">MySQL</td>
                                  <td className="py-2 px-3"><span className="px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs">Block</span></td>
                                </tr>
                                <tr className="border-b border-gray-700">
                                  <td className="py-2 px-3">Admin IP</td>
                                  <td className="py-2 px-3">All Servers</td>
                                  <td className="py-2 px-3">22</td>
                                  <td className="py-2 px-3">SSH</td>
                                  <td className="py-2 px-3"><span className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs">Allow</span></td>
                                </tr>
                                <tr>
                                  <td className="py-2 px-3">Internet</td>
                                  <td className="py-2 px-3">Internal Network</td>
                                  <td className="py-2 px-3">Any</td>
                                  <td className="py-2 px-3">Any</td>
                                  <td className="py-2 px-3"><span className="px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs">Block</span></td>
                                </tr>
                              </tbody>
                            </table>
                          </div>

                          <div className="mt-4 p-3 bg-[#1A1F35] border border-gray-700 rounded">
                            <h5 className="font-medium mb-2">Firewall Best Practices:</h5>
                            <ul className="list-disc pl-5 space-y-1 text-sm">
                              <li>Default deny policy: Block all traffic by default, then allow only what's necessary</li>
                              <li>Least privilege: Only open ports required for essential services</li>
                              <li>Segment networks: Use zones to isolate sensitive systems</li>
                              <li>Regular audits: Review and update firewall rules periodically</li>
                            </ul>
                          </div>
                        </div>
                      </>
                    )}

                    <div className="mt-6 p-4 border border-dashed border-gray-700 rounded-lg">
                      <h4 className="font-bold mb-2 flex items-center">
                        <FaLock className="mr-2 text-[#88cc14]" /> Premium Exercises
                      </h4>
                      <p className="mb-4">Sign up to access more interactive exercises:</p>
                      <ul className="space-y-2">
                        {moduleSlug === 'intro-to-cybersecurity' && (
                          <>
                            <li className="flex items-center">
                              <FaCheckCircle className="text-gray-500 mr-2" /> Encryption Challenge
                            </li>
                            <li className="flex items-center">
                              <FaCheckCircle className="text-gray-500 mr-2" /> Security Risk Assessment
                            </li>
                            <li className="flex items-center">
                              <FaCheckCircle className="text-gray-500 mr-2" /> Social Engineering Simulator
                            </li>
                          </>
                        )}
                        {moduleSlug === 'web-security-fundamentals' && (
                          <>
                            <li className="flex items-center">
                              <FaCheckCircle className="text-gray-500 mr-2" /> CSRF Attack Simulator
                            </li>
                            <li className="flex items-center">
                              <FaCheckCircle className="text-gray-500 mr-2" /> Secure Coding Practice Lab
                            </li>
                            <li className="flex items-center">
                              <FaCheckCircle className="text-gray-500 mr-2" /> Web Application Firewall Configuration
                            </li>
                          </>
                        )}
                        {moduleSlug === 'network-security-basics' && (
                          <>
                            <li className="flex items-center">
                              <FaCheckCircle className="text-gray-500 mr-2" /> Network Traffic Analysis Lab
                            </li>
                            <li className="flex items-center">
                              <FaCheckCircle className="text-gray-500 mr-2" /> IDS/IPS Configuration Exercise
                            </li>
                            <li className="flex items-center">
                              <FaCheckCircle className="text-gray-500 mr-2" /> VPN Setup and Testing
                            </li>
                          </>
                        )}
                      </ul>
                    </div>
                  </div>
                )}

                {activeTab === 'quiz' && (
                  <div className="mb-8">
                    <h3 className="text-xl font-bold mb-4 border-b border-gray-700 pb-2">Knowledge Check Quiz</h3>

                    {/* Cybersecurity Module Quiz */}
                    {moduleSlug === 'intro-to-cybersecurity' && (
                      <>
                        <p className="mb-4">Test your understanding of cybersecurity basics with this quick quiz:</p>

                        <div className="space-y-6">
                          <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                            <h4 className="font-bold mb-2">1. What is the primary goal of cybersecurity?</h4>
                            <div className="space-y-2 mt-3">
                              {['To make computers faster', 'To protect systems and data from attacks', 'To develop new software', 'To connect networks together'].map((option, idx) => (
                                <div
                                  key={idx}
                                  onClick={() => setSelectedAnswers({...selectedAnswers, q1: idx})}
                                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedAnswers.q1 === idx
                                    ? 'bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]'
                                    : darkMode ? 'bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]' : 'bg-white border-gray-200 hover:bg-gray-50'}`}
                                >
                                  {option}
                                </div>
                              ))}
                            </div>
                            {quizSubmitted && selectedAnswers.q1 === 1 && (
                              <div className="mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400">
                                Correct! Cybersecurity is all about protecting systems, networks, and data from digital attacks.
                              </div>
                            )}
                            {quizSubmitted && selectedAnswers.q1 !== undefined && selectedAnswers.q1 !== 1 && (
                              <div className="mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400">
                                Not quite. The primary goal of cybersecurity is to protect systems and data from attacks.
                              </div>
                            )}
                          </div>

                          <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                            <h4 className="font-bold mb-2">2. Which of the following is NOT one of the three main principles of information security?</h4>
                            <div className="space-y-2 mt-3">
                              {['Confidentiality', 'Integrity', 'Availability', 'Profitability'].map((option, idx) => (
                                <div
                                  key={idx}
                                  onClick={() => setSelectedAnswers({...selectedAnswers, q2: idx})}
                                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedAnswers.q2 === idx
                                    ? 'bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]'
                                    : darkMode ? 'bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]' : 'bg-white border-gray-200 hover:bg-gray-50'}`}
                                >
                                  {option}
                                </div>
                              ))}
                            </div>
                            {quizSubmitted && selectedAnswers.q2 === 3 && (
                              <div className="mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400">
                                Correct! The three main principles are Confidentiality, Integrity, and Availability (CIA triad).
                              </div>
                            )}
                            {quizSubmitted && selectedAnswers.q2 !== undefined && selectedAnswers.q2 !== 3 && (
                              <div className="mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400">
                                Not quite. Profitability is not one of the three main principles of information security.
                              </div>
                            )}
                          </div>
                        </div>
                      </>
                    )}

                    {/* Web Security Module Quiz */}
                    {moduleSlug === 'web-security-fundamentals' && (
                      <>
                        <p className="mb-4">Test your understanding of web security fundamentals with this quick quiz:</p>

                        <div className="space-y-6">
                          <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                            <h4 className="font-bold mb-2">1. Which of the following is a common web application vulnerability?</h4>
                            <div className="space-y-2 mt-3">
                              {['Memory leaks', 'Cross-Site Scripting (XSS)', 'CPU throttling', 'Bandwidth limitation'].map((option, idx) => (
                                <div
                                  key={idx}
                                  onClick={() => setSelectedAnswers({...selectedAnswers, q1: idx})}
                                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedAnswers.q1 === idx
                                    ? 'bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]'
                                    : darkMode ? 'bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]' : 'bg-white border-gray-200 hover:bg-gray-50'}`}
                                >
                                  {option}
                                </div>
                              ))}
                            </div>
                            {quizSubmitted && selectedAnswers.q1 === 1 && (
                              <div className="mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400">
                                Correct! Cross-Site Scripting (XSS) is a common web application vulnerability where attackers inject malicious scripts into trusted websites.
                              </div>
                            )}
                            {quizSubmitted && selectedAnswers.q1 !== undefined && selectedAnswers.q1 !== 1 && (
                              <div className="mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400">
                                Not quite. Cross-Site Scripting (XSS) is a common web application vulnerability where attackers inject malicious scripts into trusted websites.
                              </div>
                            )}
                          </div>

                          <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                            <h4 className="font-bold mb-2">2. Which of the following is the best defense against SQL injection attacks?</h4>
                            <div className="space-y-2 mt-3">
                              {['Using more complex SQL queries', 'Parameterized queries', 'Disabling database access', 'Removing all forms from websites'].map((option, idx) => (
                                <div
                                  key={idx}
                                  onClick={() => setSelectedAnswers({...selectedAnswers, q2: idx})}
                                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedAnswers.q2 === idx
                                    ? 'bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]'
                                    : darkMode ? 'bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]' : 'bg-white border-gray-200 hover:bg-gray-50'}`}
                                >
                                  {option}
                                </div>
                              ))}
                            </div>
                            {quizSubmitted && selectedAnswers.q2 === 1 && (
                              <div className="mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400">
                                Correct! Parameterized queries (prepared statements) prevent SQL injection by separating SQL code from user input data.
                              </div>
                            )}
                            {quizSubmitted && selectedAnswers.q2 !== undefined && selectedAnswers.q2 !== 1 && (
                              <div className="mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400">
                                Not quite. Parameterized queries (prepared statements) are the best defense as they separate SQL code from user input data.
                              </div>
                            )}
                          </div>
                        </div>
                      </>
                    )}

                    {/* Network Security Module Quiz */}
                    {moduleSlug === 'network-security-basics' && (
                      <>
                        <p className="mb-4">Test your understanding of network security basics with this quick quiz:</p>

                        <div className="space-y-6">
                          <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                            <h4 className="font-bold mb-2">1. What is the primary purpose of a firewall in network security?</h4>
                            <div className="space-y-2 mt-3">
                              {['To speed up network traffic', 'To monitor and filter network traffic based on security rules', 'To encrypt all network communications', 'To compress data for faster transmission'].map((option, idx) => (
                                <div
                                  key={idx}
                                  onClick={() => setSelectedAnswers({...selectedAnswers, q1: idx})}
                                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedAnswers.q1 === idx
                                    ? 'bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]'
                                    : darkMode ? 'bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]' : 'bg-white border-gray-200 hover:bg-gray-50'}`}
                                >
                                  {option}
                                </div>
                              ))}
                            </div>
                            {quizSubmitted && selectedAnswers.q1 === 1 && (
                              <div className="mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400">
                                Correct! Firewalls monitor and filter network traffic based on predetermined security rules to protect networks from unauthorized access.
                              </div>
                            )}
                            {quizSubmitted && selectedAnswers.q1 !== undefined && selectedAnswers.q1 !== 1 && (
                              <div className="mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400">
                                Not quite. The primary purpose of a firewall is to monitor and filter network traffic based on security rules.
                              </div>
                            )}
                          </div>

                          <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                            <h4 className="font-bold mb-2">2. Which of the following is NOT a common network attack?</h4>
                            <div className="space-y-2 mt-3">
                              {['Denial of Service (DoS)', 'Man-in-the-Middle', 'ARP Spoofing', 'Database Normalization'].map((option, idx) => (
                                <div
                                  key={idx}
                                  onClick={() => setSelectedAnswers({...selectedAnswers, q2: idx})}
                                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedAnswers.q2 === idx
                                    ? 'bg-[#88cc14]/20 border-[#88cc14] text-[#88cc14]'
                                    : darkMode ? 'bg-[#1A1F35] border-gray-700 hover:bg-[#252D4A]' : 'bg-white border-gray-200 hover:bg-gray-50'}`}
                                >
                                  {option}
                                </div>
                              ))}
                            </div>
                            {quizSubmitted && selectedAnswers.q2 === 3 && (
                              <div className="mt-3 p-2 bg-green-900/20 border border-green-900/30 rounded text-green-400">
                                Correct! Database Normalization is a database design technique, not a network attack. The others are all common network attacks.
                              </div>
                            )}
                            {quizSubmitted && selectedAnswers.q2 !== undefined && selectedAnswers.q2 !== 3 && (
                              <div className="mt-3 p-2 bg-red-900/20 border border-red-900/30 rounded text-red-400">
                                Not quite. Database Normalization is a database design technique, not a network attack.
                              </div>
                            )}
                          </div>
                        </div>
                      </>
                    )}

                    <div className="flex justify-center mt-6">
                      <button
                        onClick={() => setQuizSubmitted(true)}
                        className="px-6 py-3 bg-[#88cc14] text-black font-medium rounded-lg hover:bg-[#7ab811] transition-colors"
                      >
                        Check Answers
                      </button>
                    </div>

                    {quizSubmitted && (
                      <div className="mt-4 p-4 border border-dashed border-gray-700 rounded-lg text-center">
                        <h4 className="font-bold mb-2">Want to continue learning?</h4>
                        <p className="mb-4">Sign up to access the full module with more quizzes and interactive content.</p>
                        <Link to="/signup" className="inline-block px-6 py-2 bg-[#88cc14] text-black font-medium rounded-lg hover:bg-[#7ab811] transition-colors">
                          Create Free Account
                        </Link>
                      </div>
                    )}
                  </div>
                )}

                {/* Premium Content Preview - show on all tabs */}
                <div className={`${darkMode ? 'bg-yellow-900/20 border-yellow-900/30' : 'bg-yellow-50 border-yellow-200'} border p-5 rounded-lg my-8`}>
                  <h3 className="text-xl font-bold mb-3 flex items-center">
                    <FaLock className="mr-2 text-yellow-500" /> Premium Content Preview
                  </h3>
                  <p className="mb-4">
                    The full module includes comprehensive materials to deepen your understanding of cybersecurity:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start">
                      <FaCheckCircle className="text-green-500 mt-1 mr-2 flex-shrink-0" />
                      <span>Interactive labs with real-world scenarios</span>
                    </div>
                    <div className="flex items-start">
                      <FaCheckCircle className="text-green-500 mt-1 mr-2 flex-shrink-0" />
                      <span>Detailed explanations of advanced concepts</span>
                    </div>
                    <div className="flex items-start">
                      <FaCheckCircle className="text-green-500 mt-1 mr-2 flex-shrink-0" />
                      <span>Practical exercises to apply your knowledge</span>
                    </div>
                    <div className="flex items-start">
                      <FaCheckCircle className="text-green-500 mt-1 mr-2 flex-shrink-0" />
                      <span>Downloadable resources and cheat sheets</span>
                    </div>
                    <div className="flex items-start">
                      <FaCheckCircle className="text-green-500 mt-1 mr-2 flex-shrink-0" />
                      <span>Assessment quizzes to test your understanding</span>
                    </div>
                    <div className="flex items-start">
                      <FaCheckCircle className="text-green-500 mt-1 mr-2 flex-shrink-0" />
                      <span>Certificate of completion</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 pt-6 border-t border-gray-700">
                <h3 className="text-xl font-bold mb-4 text-center sm:text-left">Ready to continue learning?</h3>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link to="/login" className="w-full sm:w-auto px-6 py-3 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors flex items-center justify-center">
                    <FaGraduationCap className="mr-2" /> Sign In to Continue Learning
                  </Link>
                  <Link to="/signup" className="w-full sm:w-auto px-6 py-3 border-2 border-[#88cc14] text-[#88cc14] hover:bg-[#88cc14]/10 font-medium rounded-lg transition-colors flex items-center justify-center">
                    <FaExternalLinkAlt className="mr-2" /> Create Free Account
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModulePreview;
