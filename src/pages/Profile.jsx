import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaUser, FaWallet, FaShoppingBag, FaCog, FaSignOutAlt, FaTrophy, FaCoins } from 'react-icons/fa';
import { getUserProfile, getUserCoins, getOrders, getUserChallenges, signOut } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';

function Profile() {
  const navigate = useNavigate();
  const [profile, setProfile] = useState(null);
  const [coins, setCoins] = useState(null);
  const [orders, setOrders] = useState([]);
  const [challenges, setChallenges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [profileData, coinsData, ordersData, challengesData] = await Promise.all([
          getUserProfile().catch(err => {
            console.error('Error fetching profile:', err);
            return null;
          }),
          getUserCoins().catch(err => {
            console.error('Error fetching coins:', err);
            return { balance: 0 };
          }),
          getOrders().catch(err => {
            console.error('Error fetching orders:', err);
            return [];
          }),
          getUserChallenges().catch(err => {
            console.error('Error fetching challenges:', err);
            return [];
          })
        ]);
        
        setProfile(profileData);
        setCoins(coinsData);
        setOrders(ordersData || []);
        setChallenges(challengesData || []);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError('Failed to load profile data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
      setError('Failed to sign out. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 flex items-center justify-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative max-w-md">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
          <p className="mt-2">Please try <button onClick={() => window.location.reload()} className="underline">refreshing the page</button>.</p>
        </div>
      </div>
    );
  }

  // If profile is still null after loading, show error
  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 flex items-center justify-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative max-w-md">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">Could not load profile data.</span>
          <p className="mt-2">Please try <button onClick={() => window.location.reload()} className="underline">refreshing the page</button> or <button onClick={handleSignOut} className="underline">signing out</button> and back in.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              {/* Profile Header */}
              <div className="text-center mb-6">
                <div className="w-24 h-24 rounded-full bg-[#88cc14]/10 flex items-center justify-center mx-auto mb-4">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile.username}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <FaUser className="text-[#88cc14] text-3xl" />
                  )}
                </div>
                <h2 className="text-xl font-bold text-gray-900">{profile?.username || 'User'}</h2>
                <p className="text-gray-500">{profile?.email || '<EMAIL>'}</p>
              </div>

              {/* Navigation */}
              <nav className="space-y-2">
                {[
                  { id: 'overview', label: 'Overview', icon: FaUser },
                  { id: 'wallet', label: 'Wallet', icon: FaWallet },
                  { id: 'orders', label: 'Orders', icon: FaShoppingBag },
                  { id: 'settings', label: 'Settings', icon: FaCog }
                ].map(item => (
                  <button
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={`w-full flex items-center gap-3 p-3 rounded-lg transition-colors ${
                      activeTab === item.id
                        ? 'bg-[#88cc14]/10 text-[#88cc14]'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <item.icon />
                    <span>{item.label}</span>
                  </button>
                ))}
              </nav>

              <button
                onClick={handleSignOut}
                className="w-full mt-6 flex items-center gap-3 p-3 rounded-lg text-red-500 hover:bg-red-50 transition-colors"
              >
                <FaSignOutAlt />
                <span>Sign Out</span>
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="md:col-span-3">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                        <FaCoins className="text-[#88cc14] text-xl" />
                      </div>
                      <div>
                        <h3 className="text-gray-500">Balance</h3>
                        <p className="text-2xl font-bold text-[#88cc14]">
                          {coins?.balance || 0} XC
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                        <FaShoppingBag className="text-[#88cc14] text-xl" />
                      </div>
                      <div>
                        <h3 className="text-gray-500">Orders</h3>
                        <p className="text-2xl font-bold text-[#88cc14]">
                          {orders.length}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                        <FaTrophy className="text-[#88cc14] text-xl" />
                      </div>
                      <div>
                        <h3 className="text-gray-500">Challenges</h3>
                        <p className="text-2xl font-bold text-[#88cc14]">
                          {challenges.filter(c => c.status === 'completed').length}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Recent Activity</h3>
                  {challenges.length > 0 ? (
                    <div className="space-y-4">
                      {challenges.slice(0, 5).map((challenge, index) => (
                        <div key={index} className="flex items-center gap-4 p-4 rounded-lg hover:bg-gray-50">
                          <FaTrophy className={challenge.status === 'completed' ? 'text-[#88cc14]' : 'text-gray-400'} />
                          <div className="flex-1">
                            <p className="font-medium text-gray-900">
                              {challenge.status === 'completed' ? 'Completed' : 'Started'} challenge: {challenge.challenges?.title || 'Challenge'}
                            </p>
                            <p className="text-sm text-gray-500">
                              {new Date(challenge.submission_time).toLocaleString()}
                            </p>
                          </div>
                          {challenge.status === 'completed' && challenge.points_earned > 0 && (
                            <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-medium">
                              +{challenge.points_earned} points
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No recent activity</p>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'wallet' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Wallet</h3>
                <div className="flex items-center gap-4 p-6 bg-[#88cc14]/10 rounded-lg mb-6">
                  <div className="w-16 h-16 rounded-full bg-[#88cc14]/20 flex items-center justify-center">
                    <FaCoins className="text-[#88cc14] text-2xl" />
                  </div>
                  <div>
                    <p className="text-gray-600">Current Balance</p>
                    <p className="text-3xl font-bold text-[#88cc14]">{coins?.balance || 0} XC</p>
                  </div>
                </div>
                <p className="text-gray-500 text-center">Transaction history will appear here</p>
              </div>
            )}

            {activeTab === 'orders' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Orders</h3>
                {orders.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No orders yet</p>
                ) : (
                  <div className="space-y-4">
                    {orders.map(order => (
                      <div
                        key={order.id}
                        className="border border-gray-200 rounded-lg p-4"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <p className="font-medium text-gray-900">
                              Order #{order.id.slice(0, 8)}
                            </p>
                            <p className="text-sm text-gray-500">
                              {new Date(order.created_at).toLocaleDateString()}
                            </p>
                          </div>
                          <div className={`px-3 py-1 rounded-full text-sm ${
                            order.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : order.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {order.status}
                          </div>
                        </div>

                        <div className="space-y-2">
                          {order.order_items && order.order_items.map(item => (
                            <div
                              key={item.id}
                              className="flex items-center gap-4"
                            >
                              <img
                                src={item.products?.image_url || 'https://via.placeholder.com/64'}
                                alt={item.products?.name || 'Product'}
                                className="w-16 h-16 object-cover rounded"
                              />
                              <div className="flex-1">
                                <p className="font-medium text-gray-900">
                                  {item.products?.name || 'Product'}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Quantity: {item.quantity}
                                </p>
                              </div>
                              <p className="font-medium text-gray-900">
                                ₹{(item.price * item.quantity).toFixed(2)}
                              </p>
                            </div>
                          ))}
                        </div>

                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <div className="flex justify-between">
                            <span className="font-medium text-gray-900">Total</span>
                            <span className="font-bold text-[#88cc14]">
                              ₹{order.total_amount?.toFixed(2) || '0.00'}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Settings</h3>
                <form className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Username
                    </label>
                    <input
                      type="text"
                      defaultValue={profile?.username || ''}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name
                    </label>
                    <input
                      type="text"
                      defaultValue={profile?.full_name || ''}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      defaultValue={profile?.email || ''}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"
                      disabled
                    />
                    <p className="text-sm text-gray-500 mt-1">Email cannot be changed</p>
                  </div>
                  
                  <button
                    type="button"
                    className="w-full bg-[#88cc14] text-black font-bold py-3 px-4 rounded-lg hover:bg-[#7ab811] transition-colors"
                  >
                    Save Changes
                  </button>
                </form>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Profile;