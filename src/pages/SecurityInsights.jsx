import React, { useState, lazy, Suspense } from 'react';
import { FaShieldAlt, FaExclamationTriangle, FaGraduationCap, FaRobot, FaChart<PERSON>ie, FaChartBar } from 'react-icons/fa';
import ThreatAnalyticsDashboard from '../components/visualizations/ThreatAnalyticsDashboard';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

// Lazy load the ThreatIntelligenceDashboard component
const ThreatIntelligenceDashboard = lazy(() => import('../components/visualizations/ThreatIntelligenceDashboard'));

// Loading spinner
const LoadingSpinner = () => (
  <div className="flex items-center justify-center h-[400px]">
    <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
  </div>
);

const SecurityInsights = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('dashboard');

  // Define tabs
  const tabs = [
    { id: 'dashboard', label: 'Threat Dashboard', icon: FaChartPie },
    { id: 'intelligence', label: 'Intelligence', icon: FaShieldAlt },
    { id: 'analytics', label: 'Advanced Analytics', icon: FaChartBar },
    { id: 'assessment', label: 'Skill Assessment', icon: FaGraduationCap },
    { id: 'assistant', label: 'AI Assistant', icon: FaRobot }
  ];

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <div className="bg-gray-800 rounded-lg p-4 mb-6 h-[800px]">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-xl font-bold">Cyber Threat Intelligence Dashboard</h2>
                <p className="text-gray-400">
                  Comprehensive visualization of global cyber threat landscape and attack patterns.
                </p>
              </div>
            </div>
            <div className="h-[calc(100%-60px)]">
              <Suspense fallback={<LoadingSpinner />}>
                <ThreatIntelligenceDashboard />
              </Suspense>
            </div>
          </div>
        );
      case 'analytics':
        return (
          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-xl font-bold">Advanced Threat Analytics</h2>
                <p className="text-gray-400">
                  In-depth analysis of threat data from multiple intelligence sources.
                </p>
              </div>
            </div>
            <ThreatAnalyticsDashboard />
          </div>
        );

      case 'intelligence':
        return (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-bold mb-4">Threat Intelligence</h2>
            <p className="text-gray-400 mb-6">
              Access detailed threat intelligence reports and analysis from global security researchers.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Latest Intelligence Reports</h3>
                <p className="text-gray-400 mb-4">
                  Stay informed with the most recent threat intelligence reports.
                </p>
                <div className="space-y-3">
                  <div className="border-l-4 border-blue-500 pl-3 py-1">
                    <div className="font-medium">Ransomware Evolution Trends</div>
                    <div className="text-sm text-gray-400">Updated 2 days ago</div>
                  </div>
                  <div className="border-l-4 border-red-500 pl-3 py-1">
                    <div className="font-medium">Critical Infrastructure Vulnerabilities</div>
                    <div className="text-sm text-gray-400">Updated 5 days ago</div>
                  </div>
                  <div className="border-l-4 border-yellow-500 pl-3 py-1">
                    <div className="font-medium">Emerging Phishing Techniques</div>
                    <div className="text-sm text-gray-400">Updated 1 week ago</div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Threat Actors</h3>
                <p className="text-gray-400 mb-4">
                  Information about known threat actors and their tactics.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center p-2 bg-gray-800 rounded">
                    <div className="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center mr-3">
                      <FaExclamationTriangle className="text-red-500" />
                    </div>
                    <div>
                      <div className="font-medium">APT29</div>
                      <div className="text-sm text-gray-400">State-sponsored group</div>
                    </div>
                  </div>
                  <div className="flex items-center p-2 bg-gray-800 rounded">
                    <div className="w-10 h-10 rounded-full bg-orange-500/20 flex items-center justify-center mr-3">
                      <FaExclamationTriangle className="text-orange-500" />
                    </div>
                    <div>
                      <div className="font-medium">Lazarus Group</div>
                      <div className="text-sm text-gray-400">Financial targeting</div>
                    </div>
                  </div>
                  <div className="flex items-center p-2 bg-gray-800 rounded">
                    <div className="w-10 h-10 rounded-full bg-yellow-500/20 flex items-center justify-center mr-3">
                      <FaExclamationTriangle className="text-yellow-500" />
                    </div>
                    <div>
                      <div className="font-medium">Sandworm</div>
                      <div className="text-sm text-gray-400">Infrastructure targeting</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'assessment':
        return (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-bold mb-4">Skill Assessment</h2>
            <div className="bg-blue-900/30 border border-blue-800 rounded-lg p-6 mb-6 text-center">
              <FaGraduationCap className="text-4xl text-blue-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Feature Under Development</h3>
              <p className="text-gray-300 mb-4">
                We're currently enhancing our Skill Assessment feature to provide you with a more comprehensive learning experience.
              </p>
              <p className="text-gray-400">
                The updated version will include interactive challenges, real-time feedback, and personalized learning paths.
                Check back soon for the improved experience!
              </p>
            </div>

            <div className="bg-gray-700 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Coming Soon</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-800 p-3 rounded-lg opacity-75">
                  <div className="font-medium mb-1">Network Traffic Analysis</div>
                  <div className="text-sm text-gray-400 mb-2">Intermediate • 45 min</div>
                  <button disabled className="w-full bg-gray-600 text-gray-300 py-1.5 rounded text-sm cursor-not-allowed">
                    Coming Soon
                  </button>
                </div>
                <div className="bg-gray-800 p-3 rounded-lg opacity-75">
                  <div className="font-medium mb-1">Web Application Security</div>
                  <div className="text-sm text-gray-400 mb-2">Beginner • 30 min</div>
                  <button disabled className="w-full bg-gray-600 text-gray-300 py-1.5 rounded text-sm cursor-not-allowed">
                    Coming Soon
                  </button>
                </div>
                <div className="bg-gray-800 p-3 rounded-lg opacity-75">
                  <div className="font-medium mb-1">Incident Response Planning</div>
                  <div className="text-sm text-gray-400 mb-2">Advanced • 60 min</div>
                  <button disabled className="w-full bg-gray-600 text-gray-300 py-1.5 rounded text-sm cursor-not-allowed">
                    Coming Soon
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
      case 'assistant':
        return (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-bold mb-4">AI Security Assistant</h2>
            <p className="text-gray-400 mb-6">
              Get personalized cybersecurity advice and answers to your security questions.
            </p>
            <div className="bg-gray-700 rounded-lg p-4 mb-6">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mr-3">
                  <FaRobot className="text-blue-500" />
                </div>
                <div>
                  <div className="font-medium">Security Assistant</div>
                  <div className="text-sm text-gray-400">Powered by advanced AI</div>
                </div>
              </div>
              <div className="space-y-4 mb-4">
                <div className="bg-gray-800 p-3 rounded-lg">
                  <div className="text-sm text-gray-400 mb-1">Assistant</div>
                  <div>Hello! I'm your cybersecurity assistant. How can I help you today?</div>
                </div>
                <div className="bg-blue-900/30 p-3 rounded-lg ml-6">
                  <div className="text-sm text-gray-400 mb-1">You</div>
                  <div>What are the best practices for securing a home network?</div>
                </div>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <div className="text-sm text-gray-400 mb-1">Assistant</div>
                  <div>
                    Here are some best practices for securing your home network:
                    <ol className="list-decimal pl-5 mt-2 space-y-1">
                      <li>Change default router credentials</li>
                      <li>Use WPA3 encryption if available</li>
                      <li>Keep router firmware updated</li>
                      <li>Enable firewall protection</li>
                      <li>Use strong, unique passwords</li>
                      <li>Set up a guest network for visitors</li>
                      <li>Consider using a VPN for additional privacy</li>
                    </ol>
                  </div>
                </div>
              </div>
              <div className="flex">
                <input
                  type="text"
                  placeholder="Type your security question here..."
                  className="flex-1 bg-gray-800 border border-gray-600 rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-r-lg transition-colors">
                  Send
                </button>
              </div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">Suggested Topics</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <button className="bg-gray-800 hover:bg-gray-600 p-2 rounded-lg text-sm transition-colors">
                  Password security best practices
                </button>
                <button className="bg-gray-800 hover:bg-gray-600 p-2 rounded-lg text-sm transition-colors">
                  How to identify phishing attempts
                </button>
                <button className="bg-gray-800 hover:bg-gray-600 p-2 rounded-lg text-sm transition-colors">
                  Securing remote work environments
                </button>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-bold mb-4">Cyber Threat Intelligence Dashboard</h2>
            <p className="text-gray-400">
              Select a tab to view different security insights and tools.
            </p>
          </div>
        );
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-800'}`}>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Security Insights</h1>

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-6">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <tab.icon /> {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        {renderContent()}
      </div>
    </div>
  );
};

export default SecurityInsights;
