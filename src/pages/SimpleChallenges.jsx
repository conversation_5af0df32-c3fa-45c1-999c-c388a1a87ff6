import React from 'react';
import { useParams } from 'react-router-dom';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { useAuth } from '../contexts/AuthContext';
import SimpleUpgradeBanner from '../components/access/SimpleUpgradeBanner';

const SimpleChallenges = () => {
  const { darkMode } = useGlobalTheme();
  const { user, profile } = useAuth();
  const { challengeId } = useParams();

  // Placeholder components
  const ChallengeList = () => (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
      <h2 className="text-2xl font-bold mb-4">Challenges Coming Soon</h2>
      <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        We're working on exciting cybersecurity challenges for you. Check back soon!
      </p>
    </div>
  );

  const ChallengeInterface = () => (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
      <h2 className="text-2xl font-bold mb-4">Challenge {challengeId}</h2>
      <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        This challenge is being prepared. Please check back soon!
      </p>
    </div>
  );

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        {!profile || profile.subscription_tier === 'free' ? (
          <SimpleUpgradeBanner
            title="Upgrade to Premium"
            description="Get access to all challenges and features with a premium subscription."
            buttonText="View Plans"
            buttonLink="/pricing"
            className="mb-6"
          />
        ) : null}

        <h1 className="text-3xl font-bold mb-6">Cybersecurity Challenges</h1>

        {challengeId ? (
          <ChallengeInterface />
        ) : (
          <ChallengeList />
        )}
      </div>
    </div>
  );
};

export default SimpleChallenges;
