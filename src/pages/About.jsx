import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaShieldAlt, FaGraduationCap, FaUsers, FaChartLine, FaCode,
  FaTrophy, FaLightbulb, FaRocket, FaServer, FaLock,
  FaDatabase, FaNetworkWired, FaBug, FaUserSecret, FaFingerprint,
  FaCloudDownloadAlt, FaGlobe, FaLaptopCode, FaRegLightbulb,
  FaArrowRight, FaChevronDown, FaQuoteLeft
} from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import CyberForceSEO from '../components/common/CyberForceSEO';

function About() {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('about');
  const aboutRef = useRef(null);
  const visionRef = useRef(null);
  const featuresRef = useRef(null);

  // Simple animation variants for professional transitions
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariant = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)",
      transition: { duration: 0.3 }
    }
  };

  // Cyber security layers for 3D visualization
  const securityLayers = [
    { name: 'Physical Security', icon: <FaLock />, color: '#F5B93F' },
    { name: 'Network Security', icon: <FaNetworkWired />, color: '#4A5CBA' },
    { name: 'Application Security', icon: <FaCode />, color: '#10B981' },
    { name: 'Data Security', icon: <FaDatabase />, color: '#EC4899' },
    { name: 'Identity Security', icon: <FaFingerprint />, color: '#8B5CF6' },
    { name: 'Cloud Security', icon: <FaCloudDownloadAlt />, color: '#3B82F6' },
  ];

  // Core values
  const coreValues = [
    { title: 'Excellence', description: 'Striving for the highest standards in all our training programs', icon: <FaTrophy /> },
    { title: 'Innovation', description: 'Continuously evolving our methods to stay ahead of emerging threats', icon: <FaLightbulb /> },
    { title: 'Integrity', description: 'Maintaining ethical standards and transparency in all our operations', icon: <FaShieldAlt /> },
    { title: 'Collaboration', description: 'Working together with partners and students to achieve shared goals', icon: <FaUsers /> },
  ];

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* SEO */}
      <CyberForceSEO
        title="About Us"
        description="CyberForce was established in 2023 through collaboration with Telecoms and Digital Technologies Academy. Learn about our vision to be a global leader in innovative cybersecurity training."
        keywords={['cybersecurity training', 'Oman cybersecurity', 'security education', 'cyber defense training']}
        canonicalUrl="https://cyberforce.om/about"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "CyberForce",
          "url": "https://cyberforce.om",
          "logo": "https://cyberforce.om/images/CyberForce.png",
          "foundingDate": "2023",
          "founders": [
            {
              "@type": "Person",
              "name": "CyberForce Team"
            }
          ],
          "description": "CyberForce was established in 2023 through collaboration with Telecoms and Digital Technologies Academy, with a vision to be a global leader in innovative cybersecurity training.",
          "sameAs": [
            "https://www.linkedin.com/company/cyberforceoman/",
            "https://www.instagram.com/cyberforce_om/",
            "https://x.com/cyberforce_om"
          ]
        }}
      />

      {/* Professional Hero Section */}
      <div className={`${darkMode ? 'bg-[#0B1120]' : 'bg-[#0F172A]'} text-white pt-24 pb-16 relative overflow-hidden`}>
        {/* Cybersecurity Graphics Background */}
        <div className="absolute inset-0 z-0 opacity-20">
          {/* Circuit Board Pattern */}
          <svg className="absolute top-0 left-0 w-full h-full" width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <pattern id="circuit-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
              <path d="M0,0 L20,0 L20,20 L0,20 Z" fill="none" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="20" cy="20" r="2" fill="#4A5CBA" />
              <path d="M20,20 L40,20" stroke="#4A5CBA" strokeWidth="0.5" />
              <path d="M20,20 L20,40" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="40" cy="20" r="2" fill="#4A5CBA" />
              <path d="M40,20 L60,20" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="20" cy="40" r="2" fill="#4A5CBA" />
              <path d="M20,40 L20,60" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="60" cy="20" r="2" fill="#F5B93F" />
              <path d="M60,20 L60,40" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="60" cy="40" r="2" fill="#F5B93F" />
              <path d="M60,40 L40,40" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="40" cy="40" r="2" fill="#F5B93F" />
              <path d="M40,40 L40,60" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="20" cy="60" r="2" fill="#4A5CBA" />
              <path d="M20,60 L40,60" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="40" cy="60" r="2" fill="#F5B93F" />
              <path d="M40,60 L60,60" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="60" cy="60" r="2" fill="#F5B93F" />
              <path d="M60,60 L80,60" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="80" cy="60" r="2" fill="#4A5CBA" />
              <path d="M80,60 L80,80" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="80" cy="80" r="2" fill="#4A5CBA" />
              <path d="M80,80 L60,80" stroke="#4A5CBA" strokeWidth="0.5" />
              <circle cx="60" cy="80" r="2" fill="#F5B93F" />
              <path d="M60,80 L40,80" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="40" cy="80" r="2" fill="#F5B93F" />
              <path d="M40,80 L20,80" stroke="#F5B93F" strokeWidth="0.5" />
              <circle cx="20" cy="80" r="2" fill="#4A5CBA" />
              <path d="M20,80 L0,80" stroke="#4A5CBA" strokeWidth="0.5" />
            </pattern>
            <rect x="0" y="0" width="100%" height="100%" fill="url(#circuit-pattern)" />
          </svg>
        </div>

        {/* Floating Cybersecurity Elements */}
        <div className="absolute inset-0 z-0">
          {/* Shield Icon */}
          <svg className="absolute top-[15%] left-[10%] w-24 h-24 opacity-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C12 22 20 18 20 12V5L12 2L4 5V12C4 18 12 22 12 22Z" stroke="#F5B93F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>

          {/* Lock Icon */}
          <svg className="absolute top-[60%] left-[15%] w-16 h-16 opacity-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="11" width="18" height="11" rx="2" stroke="#4A5CBA" strokeWidth="2"/>
            <path d="M7 11V7C7 4.23858 9.23858 2 12 2C14.7614 2 17 4.23858 17 7V11" stroke="#4A5CBA" strokeWidth="2"/>
          </svg>

          {/* Code Icon */}
          <svg className="absolute top-[25%] right-[15%] w-20 h-20 opacity-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 18L22 12L16 6" stroke="#F5B93F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 6L2 12L8 18" stroke="#F5B93F" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>

          {/* Server Icon */}
          <svg className="absolute top-[65%] right-[10%] w-16 h-16 opacity-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="2" y="3" width="20" height="6" rx="1" stroke="#4A5CBA" strokeWidth="2"/>
            <rect x="2" y="9" width="20" height="6" rx="1" stroke="#4A5CBA" strokeWidth="2"/>
            <rect x="2" y="15" width="20" height="6" rx="1" stroke="#4A5CBA" strokeWidth="2"/>
            <circle cx="6" cy="6" r="1" fill="#4A5CBA"/>
            <circle cx="6" cy="12" r="1" fill="#4A5CBA"/>
            <circle cx="6" cy="18" r="1" fill="#4A5CBA"/>
          </svg>
        </div>

        {/* Animated Particles */}
        <div className="absolute inset-0 z-0">
          {[...Array(25)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full"
              style={{
                width: `${Math.random() * 6 + 2}px`,
                height: `${Math.random() * 6 + 2}px`,
                backgroundColor: i % 2 === 0 ? '#4A5CBA' : '#F5B93F',
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                opacity: 0.4,
                animation: `float-particle ${Math.random() * 10 + 15}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 5}s`
              }}
            />
          ))}
        </div>

        {/* Digital Network Lines */}
        <div className="absolute inset-0 z-0 opacity-10">
          <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern id="network-pattern" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
                {/* Horizontal Lines */}
                <line x1="0" y1="50" x2="200" y2="50" stroke="#4A5CBA" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-slow" />
                <line x1="0" y1="100" x2="200" y2="100" stroke="#F5B93F" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-medium" />
                <line x1="0" y1="150" x2="200" y2="150" stroke="#4A5CBA" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-slow" />

                {/* Vertical Lines */}
                <line x1="50" y1="0" x2="50" y2="200" stroke="#F5B93F" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-medium" />
                <line x1="100" y1="0" x2="100" y2="200" stroke="#4A5CBA" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-slow" />
                <line x1="150" y1="0" x2="150" y2="200" stroke="#F5B93F" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-medium" />

                {/* Diagonal Lines */}
                <line x1="0" y1="0" x2="200" y2="200" stroke="#4A5CBA" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-slow" />
                <line x1="200" y1="0" x2="0" y2="200" stroke="#F5B93F" strokeWidth="0.5" strokeDasharray="10,10" className="animate-dash-medium" />
              </pattern>
            </defs>
            <rect x="0" y="0" width="100%" height="100%" fill="url(#network-pattern)" />
          </svg>
        </div>

        {/* Glowing Orbs */}
        <div className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-[#4A5CBA]/10 blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-64 h-64 rounded-full bg-[#F5B93F]/10 blur-3xl"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row items-center justify-between gap-10">
              {/* Left content */}
              <div className="text-center lg:text-left lg:w-1/2 mb-10 lg:mb-0">
                <motion.div
                  variants={fadeInUp}
                  initial="hidden"
                  animate="visible"
                >
                  <h1 className="text-4xl md:text-5xl font-bold mb-4">
                    About <span className="text-white">Cyber</span><span className="text-[#F5B93F]">Force</span>
                  </h1>
                  <div className="w-20 h-1 bg-[#F5B93F] mx-auto lg:mx-0 mb-6"></div>
                  <p className="text-gray-100 text-lg md:text-xl mb-8 max-w-xl leading-relaxed">
                    Empowering individuals and organizations with cutting-edge cybersecurity training and capacity building.
                  </p>
                </motion.div>

                {/* Professional Tab Navigation */}
                <motion.div
                  variants={fadeIn}
                  initial="hidden"
                  animate="visible"
                  className="bg-white/10 inline-flex p-1 rounded-lg shadow-md"
                >
                  {[
                    { id: 'about', label: 'About Us' },
                    { id: 'vision', label: 'Vision & Mission' },
                    { id: 'features', label: 'What Sets Us Apart' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 ${
                        activeTab === tab.id
                          ? 'bg-[#F5B93F] text-[#1A1F35] shadow-md'
                          : 'text-white hover:bg-white/10'
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </motion.div>
              </div>

              {/* Right content - Cyber Security Layers Visualization - New Design */}
              <motion.div
                variants={fadeIn}
                initial="hidden"
                animate="visible"
                className="lg:w-1/2 flex justify-center"
              >
                <div className="relative w-[400px] h-[400px]">
                  {/* Dark background with grid pattern */}
                  <div className="absolute inset-0 bg-[#0B1120] rounded-lg overflow-hidden">
                    {/* Grid background */}
                    <div className="absolute inset-0 opacity-20">
                      <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                        <pattern id="grid-pattern" width="40" height="40" patternUnits="userSpaceOnUse">
                          <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" />
                        </pattern>
                        <rect width="100%" height="100%" fill="url(#grid-pattern)" />
                      </svg>
                    </div>

                    {/* Connection dots */}
                    <div className="absolute inset-0">
                      {[...Array(20)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute rounded-full w-1 h-1 bg-white opacity-40"
                          style={{
                            top: `${Math.random() * 100}%`,
                            left: `${Math.random() * 100}%`,
                            animation: `pulse ${2 + Math.random() * 3}s infinite`
                          }}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Nested Squares with Glowing Borders */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    {/* Outer square - Gold */}
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.8 }}
                      className="relative w-[95%] h-[95%] rounded-lg"
                    >
                      <div className="absolute inset-0 rounded-lg border-2 border-[#F5B93F] opacity-80">
                        {/* Corner accents */}
                        <div className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-[#F5B93F]" style={{ borderRadius: '4px 0 0 0' }}></div>
                        <div className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-[#F5B93F]" style={{ borderRadius: '0 4px 0 0' }}></div>
                        <div className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-[#F5B93F]" style={{ borderRadius: '0 0 0 4px' }}></div>
                        <div className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-[#F5B93F]" style={{ borderRadius: '0 0 4px 0' }}></div>
                      </div>

                      {/* Animated glow effect */}
                      <div className="absolute inset-0 rounded-lg">
                        <div className="absolute inset-0 rounded-lg border-2 border-[#F5B93F] opacity-0 animate-pulse-slow"
                          style={{
                            boxShadow: '0 0 15px #F5B93F',
                            filter: 'blur(2px)'
                          }}
                        ></div>
                      </div>

                      {/* Second square - Green */}
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                        className="absolute inset-[8%] rounded-lg"
                      >
                        <div className="absolute inset-0 rounded-lg border-2 border-[#10B981] opacity-80">
                          {/* Corner accents */}
                          <div className="absolute top-0 left-0 w-5 h-5 border-t-2 border-l-2 border-[#10B981]" style={{ borderRadius: '4px 0 0 0' }}></div>
                          <div className="absolute top-0 right-0 w-5 h-5 border-t-2 border-r-2 border-[#10B981]" style={{ borderRadius: '0 4px 0 0' }}></div>
                          <div className="absolute bottom-0 left-0 w-5 h-5 border-b-2 border-l-2 border-[#10B981]" style={{ borderRadius: '0 0 0 4px' }}></div>
                          <div className="absolute bottom-0 right-0 w-5 h-5 border-b-2 border-r-2 border-[#10B981]" style={{ borderRadius: '0 0 4px 0' }}></div>
                        </div>

                        {/* Animated glow effect */}
                        <div className="absolute inset-0 rounded-lg">
                          <div className="absolute inset-0 rounded-lg border-2 border-[#10B981] opacity-0 animate-pulse-medium"
                            style={{
                              boxShadow: '0 0 15px #10B981',
                              filter: 'blur(2px)',
                              animationDelay: '0.5s'
                            }}
                          ></div>
                        </div>

                        {/* Third square - Pink */}
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.8, delay: 0.4 }}
                          className="absolute inset-[10%] rounded-lg"
                        >
                          <div className="absolute inset-0 rounded-lg border-2 border-[#EC4899] opacity-80">
                            {/* Corner accents */}
                            <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#EC4899]" style={{ borderRadius: '4px 0 0 0' }}></div>
                            <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#EC4899]" style={{ borderRadius: '0 4px 0 0' }}></div>
                            <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#EC4899]" style={{ borderRadius: '0 0 0 4px' }}></div>
                            <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#EC4899]" style={{ borderRadius: '0 0 4px 0' }}></div>
                          </div>

                          {/* Animated glow effect */}
                          <div className="absolute inset-0 rounded-lg">
                            <div className="absolute inset-0 rounded-lg border-2 border-[#EC4899] opacity-0 animate-pulse-medium"
                              style={{
                                boxShadow: '0 0 15px #EC4899',
                                filter: 'blur(2px)',
                                animationDelay: '1s'
                              }}
                            ></div>
                          </div>

                          {/* Fourth square - Purple */}
                          <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.8, delay: 0.6 }}
                            className="absolute inset-[12%] rounded-lg"
                          >
                            <div className="absolute inset-0 rounded-lg border-2 border-[#8B5CF6] opacity-80">
                              {/* Corner accents */}
                              <div className="absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 border-[#8B5CF6]" style={{ borderRadius: '4px 0 0 0' }}></div>
                              <div className="absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 border-[#8B5CF6]" style={{ borderRadius: '0 4px 0 0' }}></div>
                              <div className="absolute bottom-0 left-0 w-3 h-3 border-b-2 border-l-2 border-[#8B5CF6]" style={{ borderRadius: '0 0 0 4px' }}></div>
                              <div className="absolute bottom-0 right-0 w-3 h-3 border-b-2 border-r-2 border-[#8B5CF6]" style={{ borderRadius: '0 0 4px 0' }}></div>
                            </div>

                            {/* Animated glow effect */}
                            <div className="absolute inset-0 rounded-lg">
                              <div className="absolute inset-0 rounded-lg border-2 border-[#8B5CF6] opacity-0 animate-pulse-medium"
                                style={{
                                  boxShadow: '0 0 15px #8B5CF6',
                                  filter: 'blur(2px)',
                                  animationDelay: '1.5s'
                                }}
                              ></div>
                            </div>

                            {/* Fifth square - Blue */}
                            <motion.div
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ duration: 0.8, delay: 0.8 }}
                              className="absolute inset-[14%] rounded-lg"
                            >
                              <div className="absolute inset-0 rounded-lg border-2 border-[#4A5CBA] opacity-80">
                                {/* Corner accents */}
                                <div className="absolute top-0 left-0 w-2 h-2 border-t-2 border-l-2 border-[#4A5CBA]" style={{ borderRadius: '4px 0 0 0' }}></div>
                                <div className="absolute top-0 right-0 w-2 h-2 border-t-2 border-r-2 border-[#4A5CBA]" style={{ borderRadius: '0 4px 0 0' }}></div>
                                <div className="absolute bottom-0 left-0 w-2 h-2 border-b-2 border-l-2 border-[#4A5CBA]" style={{ borderRadius: '0 0 0 4px' }}></div>
                                <div className="absolute bottom-0 right-0 w-2 h-2 border-b-2 border-r-2 border-[#4A5CBA]" style={{ borderRadius: '0 0 4px 0' }}></div>
                              </div>

                              {/* Animated glow effect */}
                              <div className="absolute inset-0 rounded-lg">
                                <div className="absolute inset-0 rounded-lg border-2 border-[#4A5CBA] opacity-0 animate-pulse-medium"
                                  style={{
                                    boxShadow: '0 0 15px #4A5CBA',
                                    filter: 'blur(2px)',
                                    animationDelay: '2s'
                                  }}
                                ></div>
                              </div>

                              {/* Center circle with shield */}
                              <motion.div
                                initial={{ opacity: 0, scale: 0 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{
                                  type: "spring",
                                  stiffness: 260,
                                  damping: 20,
                                  delay: 1.2
                                }}
                                className="absolute inset-0 flex items-center justify-center"
                              >
                                {/* Outer circle with gold border */}
                                <div className="relative w-32 h-32">
                                  {/* Animated rotating border */}
                                  <div className="absolute inset-0 rounded-full border-2 border-[#F5B93F] animate-spin-slow"
                                    style={{
                                      boxShadow: '0 0 20px rgba(245, 185, 63, 0.5)',
                                      animationDuration: '20s'
                                    }}
                                  ></div>

                                  {/* Inner circle with shield */}
                                  <div className="absolute inset-[15%] rounded-full bg-[#0B1120] border-2 border-[#F5B93F] flex items-center justify-center overflow-hidden">
                                    {/* Glowing background */}
                                    <div className="absolute inset-0 bg-gradient-to-r from-[#4A5CBA]/20 to-[#F5B93F]/20 animate-pulse-slow"></div>

                                    {/* Shield icon */}
                                    <div className="relative z-10">
                                      <div className="text-[#F5B93F] text-4xl">
                                        <FaShieldAlt />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            </motion.div>
                          </motion.div>
                        </motion.div>
                      </motion.div>
                    </motion.div>
                  </div>

                  {/* Floating particles */}
                  <div className="absolute inset-0 pointer-events-none">
                    {[...Array(20)].map((_, i) => (
                      <motion.div
                        key={i}
                        initial={{
                          x: Math.random() * 400 - 200,
                          y: Math.random() * 400 - 200,
                          opacity: 0
                        }}
                        animate={{
                          x: Math.random() * 400 - 200,
                          y: Math.random() * 400 - 200,
                          opacity: [0, 0.7, 0]
                        }}
                        transition={{
                          duration: Math.random() * 8 + 8,
                          repeat: Infinity,
                          delay: Math.random() * 5
                        }}
                        className="absolute top-1/2 left-1/2 w-1.5 h-1.5 rounded-full"
                        style={{
                          backgroundColor: i % 5 === 0 ? '#F5B93F' :
                                          i % 5 === 1 ? '#10B981' :
                                          i % 5 === 2 ? '#EC4899' :
                                          i % 5 === 3 ? '#8B5CF6' : '#4A5CBA',
                          boxShadow: i % 5 === 0 ? '0 0 5px #F5B93F' :
                                     i % 5 === 1 ? '0 0 5px #10B981' :
                                     i % 5 === 2 ? '0 0 5px #EC4899' :
                                     i % 5 === 3 ? '0 0 5px #8B5CF6' : '0 0 5px #4A5CBA'
                        }}
                      />
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Enhanced Stats Section */}
            <motion.div
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
              className="mt-12"
            >
              <div className="glass-card-dark rounded-xl p-1 shadow-lg border border-white/10 relative overflow-hidden">
                {/* Background pattern for stats */}
                <div className="absolute inset-0 opacity-5">
                  <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                    <pattern id="stats-grid" width="40" height="40" patternUnits="userSpaceOnUse">
                      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.8)" strokeWidth="0.5" />
                    </pattern>
                    <rect width="100%" height="100%" fill="url(#stats-grid)" />
                  </svg>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-1 relative z-10">
                  {[
                    {
                      icon: FaUsers,
                      value: '5,000+',
                      label: 'Students',
                      color: '#4A5CBA',
                      delay: 0
                    },
                    {
                      icon: FaCode,
                      value: '100+',
                      label: 'Challenges',
                      color: '#F5B93F',
                      delay: 0.1
                    },
                    {
                      icon: FaTrophy,
                      value: '40+',
                      label: 'Learning Modules',
                      color: '#10B981',
                      delay: 0.2
                    },
                    {
                      icon: FaChartLine,
                      value: '95%',
                      label: 'Success Rate',
                      color: '#8B5CF6',
                      delay: 0.3
                    }
                  ].map((stat, index) => (
                    <motion.div
                      key={index}
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.5 + stat.delay, duration: 0.5 }}
                      className="rounded-lg p-4 text-center transition-all duration-300 hover:bg-white/5 relative"
                      whileHover={{
                        y: -5,
                        transition: { duration: 0.2 }
                      }}
                    >
                      {/* Subtle top border */}
                      <div
                        className="absolute top-0 left-1/2 transform -translate-x-1/2 h-1 rounded-b-full"
                        style={{
                          backgroundColor: stat.color,
                          width: '40%'
                        }}
                      ></div>

                      <div
                        className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-3 relative"
                        style={{ backgroundColor: `${stat.color}20` }}
                      >
                        {/* Pulsing background */}
                        <div
                          className="absolute inset-0 rounded-full animate-pulse-slow"
                          style={{
                            backgroundColor: `${stat.color}10`,
                            boxShadow: `0 0 15px ${stat.color}30`
                          }}
                        ></div>

                        <stat.icon
                          className="text-2xl relative z-10"
                          style={{ color: stat.color }}
                        />
                      </div>

                      <div className="relative">
                        <motion.div
                          initial={{ opacity: 0, scale: 0.5 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.7 + stat.delay, duration: 0.5 }}
                          className="text-3xl font-bold text-white mb-1"
                        >
                          {stat.value}
                        </motion.div>
                        <div className="text-gray-300 text-sm">{stat.label}</div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Simple divider */}
        <div className="absolute bottom-0 left-0 w-full h-6 bg-gradient-to-r from-[#4A5CBA] via-[#F5B93F] to-[#4A5CBA] opacity-20"></div>
      </div>

      {/* Professional Content Section with Tabs */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-5xl mx-auto">
          {/* Tab Content with Clean Animations */}
          <AnimatePresence mode="wait">
            {/* About Us Tab */}
            {activeTab === 'about' && (
              <motion.div
                ref={aboutRef}
                key="about"
                variants={fadeIn}
                initial="hidden"
                animate="visible"
                exit={{ opacity: 0 }}
                className="mb-16"
              >
                <div className="flex items-center mb-8">
                  <div className={`w-12 h-12 ${darkMode ? 'bg-[#4A5CBA]/20' : 'bg-[#4A5CBA]/10'} rounded-lg flex items-center justify-center mr-4`}>
                    <FaUsers className="text-[#4A5CBA] text-2xl" />
                  </div>
                  <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>About Us</h2>
                </div>

                <div className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} space-y-10`}>
                  {/* Our Story Card - Professional Design */}
                  <motion.div
                    variants={fadeInUp}
                    className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} rounded-xl overflow-hidden shadow-md`}
                  >
                    <div className="h-2 bg-[#4A5CBA]"></div>
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 rounded-full bg-[#4A5CBA]/10 flex items-center justify-center mr-4">
                          <FaRegLightbulb className="text-[#4A5CBA] text-xl" />
                        </div>
                        <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Our Story</h3>
                      </div>

                      <div className="space-y-4">
                        <p>
                          Cyber Force, established in 2023 through a collaboration with the Telecoms and Digital Technologies Academy (www.tdta.om), emerges as the premier destination for comprehensive cyber security capacity building and training.
                        </p>
                        <p>
                          At Cyber Force, we position ourselves as a cutting-edge organization committed to empowering individuals and organizations with the indispensable knowledge and skills required to effectively navigate the intricate realm of cyber security.
                        </p>
                      </div>
                    </div>
                  </motion.div>

                  {/* Core Values - Clean Grid */}
                  <div className="py-8">
                    <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-6`}>Our Core Values</h3>

                    <motion.div
                      variants={staggerContainer}
                      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5"
                    >
                      {coreValues.map((value, index) => (
                        <motion.div
                          key={index}
                          variants={cardVariant}
                          whileHover="hover"
                          className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} p-5 rounded-lg shadow-sm text-center`}
                        >
                          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-[#4A5CBA]/10 mb-3">
                            <div className="text-[#4A5CBA] text-xl">
                              {value.icon}
                            </div>
                          </div>
                          <h4 className={`text-lg font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>{value.title}</h4>
                          <p className="text-sm">{value.description}</p>
                        </motion.div>
                      ))}
                    </motion.div>
                  </div>

                  {/* Professional Timeline */}
                  <motion.div
                    variants={fadeInUp}
                    className="py-8"
                  >
                    <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-6`}>Our Journey</h3>

                    <div className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} p-6 rounded-xl shadow-md`}>
                      <div className="relative pl-8 border-l-2 border-[#4A5CBA]">
                        {[
                          { year: '2023', title: 'Foundation', description: 'Established through collaboration with TDTA' },
                          { year: '2023', title: 'First Training Programs', description: 'Launched our first cybersecurity courses' },
                          { year: '2024', title: 'Expansion', description: 'Growing our team and course offerings' },
                          { year: '2024', title: 'Future Vision', description: 'Becoming a global leader in cybersecurity education' }
                        ].map((item, index) => (
                          <div
                            key={index}
                            className="mb-6 relative"
                          >
                            <div className="absolute -left-[9px] w-4 h-4 rounded-full bg-[#4A5CBA] border-2 border-white dark:border-[#0B1120]" />
                            <div className="pl-6">
                              <div className="text-[#F5B93F] font-bold text-sm mb-1">{item.year}</div>
                              <h4 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-1`}>{item.title}</h4>
                              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{item.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </motion.div>

                  {/* Final Summary Card */}
                  <motion.div
                    variants={fadeInUp}
                    className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} p-6 rounded-xl shadow-md border-l-4 border-[#F5B93F]`}
                  >
                    <div className="flex flex-col md:flex-row gap-6 items-center">
                      <div className="md:w-2/3">
                        <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>Our Commitment</h3>
                        <p className="mb-4">
                          At Cyber Force, we go beyond theoretical knowledge, emphasizing practical, hands-on experiences to fortify your cyber security skills. Our comprehensive training programs cover a wide range of topics, including threat intelligence, network security, incident response, ethical hacking, secure coding, and much more.
                        </p>
                        <p>
                          Whether you are an individual seeking to enhance your cyber security skills or an organization aiming to fortify your defenses, Cyber Force is here to support your journey.
                        </p>
                      </div>

                      <div className="md:w-1/3 flex flex-col items-center">
                        <div className="w-32 h-32 rounded-full border-4 border-dashed border-[#F5B93F] flex items-center justify-center mb-4">
                          <div className="w-24 h-24 rounded-full bg-[#4A5CBA]/20 flex items-center justify-center">
                            <FaShieldAlt className="text-[#F5B93F] text-4xl" />
                          </div>
                        </div>

                        <button className="bg-[#4A5CBA] hover:bg-[#3A4CAA] text-white px-6 py-2 rounded-lg font-medium transition-all duration-300">
                          Learn More
                        </button>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            )}

            {/* Vision & Mission Tab */}
            {activeTab === 'vision' && (
              <motion.div
                ref={visionRef}
                key="vision"
                variants={fadeIn}
                initial="hidden"
                animate="visible"
                exit={{ opacity: 0 }}
                className="mb-16"
              >
                <div className="flex items-center mb-8">
                  <div className={`w-12 h-12 ${darkMode ? 'bg-[#F5B93F]/20' : 'bg-[#F5B93F]/10'} rounded-lg flex items-center justify-center mr-4`}>
                    <FaLightbulb className="text-[#F5B93F] text-2xl" />
                  </div>
                  <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Vision & Mission</h2>
                </div>

                {/* Vision & Mission Cards - Professional Design */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                  {/* Vision Card */}
                  <motion.div
                    variants={fadeInUp}
                    className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} rounded-xl overflow-hidden shadow-md`}
                  >
                    <div className="h-2 bg-[#4A5CBA]"></div>
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className="w-14 h-14 rounded-full bg-[#4A5CBA]/10 flex items-center justify-center mr-4">
                          <FaLightbulb className="text-[#4A5CBA] text-2xl" />
                        </div>
                        <h3 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Our Vision</h3>
                      </div>

                      <div className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-6`}>
                        <p className="text-xl font-bold mb-4">
                          Global leader in innovative cybersecurity training and capacity building.
                        </p>
                        <p className="mb-6">
                          We envision a future where organizations and individuals are empowered with the knowledge and skills to protect themselves in an increasingly complex digital landscape.
                        </p>
                      </div>

                      <div className="space-y-2">
                        {[
                          'Setting the standard for cybersecurity education',
                          'Pioneering innovative training methodologies',
                          'Building a global community of security professionals',
                          'Advancing the cybersecurity field through research'
                        ].map((item, index) => (
                          <div key={index} className="flex items-center">
                            <div className="w-5 h-5 rounded-full bg-[#4A5CBA]/10 flex items-center justify-center mr-2">
                              <span className="text-[#4A5CBA] text-xs">✓</span>
                            </div>
                            <span className="text-sm">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </motion.div>

                  {/* Mission Card */}
                  <motion.div
                    variants={fadeInUp}
                    className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} rounded-xl overflow-hidden shadow-md`}
                  >
                    <div className="h-2 bg-[#F5B93F]"></div>
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className="w-14 h-14 rounded-full bg-[#F5B93F]/10 flex items-center justify-center mr-4">
                          <FaRocket className="text-[#F5B93F] text-2xl" />
                        </div>
                        <h3 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Our Mission</h3>
                      </div>

                      <div className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-6`}>
                        <p className="text-xl font-bold mb-4">
                          Empowering individuals and organizations to proactively defend against cyber threats.
                        </p>
                        <p className="mb-6">
                          We are committed to delivering exceptional cybersecurity training that prepares our students for real-world challenges through cutting-edge education and collaborative partnerships.
                        </p>
                      </div>

                      <div className="space-y-2">
                        {[
                          'Providing hands-on, practical training experiences',
                          'Developing cutting-edge cybersecurity curriculum',
                          'Fostering a collaborative learning environment',
                          'Staying at the forefront of emerging threats'
                        ].map((item, index) => (
                          <div key={index} className="flex items-center">
                            <div className="w-5 h-5 rounded-full bg-[#F5B93F]/10 flex items-center justify-center mr-2">
                              <span className="text-[#F5B93F] text-xs">✓</span>
                            </div>
                            <span className="text-sm">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Our Approach Section */}
                <motion.div
                  variants={fadeInUp}
                  className="mb-12"
                >
                  <div className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} rounded-xl overflow-hidden shadow-md`}>
                    <div className="p-6">
                      <div className="flex flex-col lg:flex-row items-center gap-8">
                        {/* Left side - Dynamic 3D Visualization */}
                        <div className="lg:w-1/3 flex justify-center">
                          <div className="relative w-[220px] h-[220px] perspective-800">
                            {/* 3D Hexagonal Shield */}
                            <motion.div
                              initial={{ rotateY: -30, rotateX: 15, opacity: 0 }}
                              animate={{ rotateY: 0, rotateX: 0, opacity: 1 }}
                              transition={{ duration: 1 }}
                              className="absolute inset-0 flex items-center justify-center"
                              style={{ transformStyle: 'preserve-3d' }}
                            >
                              {/* Hexagon layers */}
                              {[...Array(3)].map((_, index) => (
                                <motion.div
                                  key={index}
                                  initial={{ z: -50 }}
                                  animate={{
                                    z: -20 * (3 - index),
                                    rotateZ: index % 2 === 0 ? 0 : 30
                                  }}
                                  transition={{ delay: 0.3 * index, duration: 0.8 }}
                                  className="absolute"
                                  style={{
                                    width: `${100 - (index * 15)}%`,
                                    height: `${100 - (index * 15)}%`,
                                    transform: `translateZ(${-20 * (3 - index)}px) rotateZ(${index % 2 === 0 ? 0 : 30}deg)`,
                                  }}
                                >
                                  <svg viewBox="0 0 100 100" width="100%" height="100%">
                                    <polygon
                                      points="50,3 100,28 100,72 50,97 0,72 0,28"
                                      fill="none"
                                      stroke={index % 2 === 0 ? '#4A5CBA' : '#F5B93F'}
                                      strokeWidth="2"
                                      strokeDasharray={index === 0 ? "0" : "5,5"}
                                      className={index % 2 === 0 ? "animate-spin-forward" : "animate-spin-reverse"}
                                      style={{
                                        animationDuration: `${30 - (index * 5)}s`,
                                        opacity: 0.7,
                                      }}
                                    />
                                  </svg>
                                </motion.div>
                              ))}

                              {/* Animated particles inside */}
                              <div className="absolute inset-[20%] overflow-hidden">
                                {[...Array(8)].map((_, i) => (
                                  <motion.div
                                    key={i}
                                    initial={{
                                      x: Math.random() * 100 - 50,
                                      y: Math.random() * 100 - 50,
                                      opacity: 0
                                    }}
                                    animate={{
                                      x: [
                                        Math.random() * 60 - 30,
                                        Math.random() * 60 - 30,
                                        Math.random() * 60 - 30
                                      ],
                                      y: [
                                        Math.random() * 60 - 30,
                                        Math.random() * 60 - 30,
                                        Math.random() * 60 - 30
                                      ],
                                      opacity: [0.2, 0.8, 0.2]
                                    }}
                                    transition={{
                                      duration: Math.random() * 10 + 10,
                                      repeat: Infinity,
                                      delay: Math.random() * 2
                                    }}
                                    className="absolute rounded-full"
                                    style={{
                                      width: `${Math.random() * 6 + 2}px`,
                                      height: `${Math.random() * 6 + 2}px`,
                                      backgroundColor: i % 2 === 0 ? '#4A5CBA' : '#F5B93F',
                                      top: '50%',
                                      left: '50%',
                                      transform: 'translate(-50%, -50%)',
                                    }}
                                  />
                                ))}
                              </div>

                              {/* Center shield */}
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.9, duration: 0.5, type: 'spring' }}
                                className="absolute inset-0 flex items-center justify-center"
                              >
                                <div className="relative">
                                  <div className="absolute inset-0 bg-gradient-to-r from-[#4A5CBA] to-[#F5B93F] rounded-full opacity-30 blur-md animate-pulse-slow"></div>
                                  <div className="relative bg-[#0B1120] w-16 h-16 rounded-full flex items-center justify-center shadow-lg border-2 border-[#F5B93F]">
                                    <div className="text-[#F5B93F] text-2xl animate-pulse-medium">
                                      <FaShieldAlt />
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            </motion.div>
                          </div>
                        </div>

                        {/* Right side - Text Content */}
                        <div className="lg:w-2/3">
                          <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>Our Approach</h3>
                          <div className="w-16 h-1 bg-[#4A5CBA] mb-4"></div>

                          <div className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} space-y-4`}>
                            <p>
                              At CyberForce, our vision and mission are at the core of everything we do. We believe that cybersecurity education should be accessible, practical, and cutting-edge.
                            </p>
                            <p>
                              Our approach combines theoretical knowledge with hands-on experience, ensuring that our students are well-prepared to face real-world challenges in the ever-evolving landscape of cybersecurity.
                            </p>

                            <div className="pt-2">
                              <button className="bg-[#4A5CBA] hover:bg-[#3A4CAA] text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 flex items-center">
                                Learn More
                                <FaArrowRight className="ml-2" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Quote Section */}
                <motion.div
                  variants={fadeInUp}
                  className={`${darkMode ? 'bg-[#1A1F35]/50' : 'bg-white'} p-6 rounded-xl shadow-sm border-l-4 border-[#4A5CBA] mb-8`}
                >
                  <div className="flex items-start">
                    <FaQuoteLeft className="text-[#F5B93F] text-2xl mr-4 mt-1 opacity-70" />
                    <div>
                      <p className="italic text-lg mb-4">
                        "Our vision and mission guide everything we do at CyberForce, from curriculum development to student engagement. We are committed to creating a safer digital world through education and innovation."
                      </p>
                      <p className={`text-right text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        — CyberForce Leadership Team
                      </p>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            )}

            {/* Features Tab */}
            {activeTab === 'features' && (
              <motion.div
                ref={featuresRef}
                key="features"
                variants={fadeIn}
                initial="hidden"
                animate="visible"
                exit={{ opacity: 0 }}
                className="mb-16"
              >
                <div className="flex items-center mb-8 relative">
                  {/* Animated background for section header */}
                  <div className="absolute -left-4 -top-4 w-20 h-20 opacity-10">
                    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                      <path fill="#4A5CBA" d="M47.5,-61.7C59.9,-51.3,67.7,-35.1,71.8,-18.1C75.9,-1.1,76.2,16.7,69.2,31.1C62.2,45.4,47.9,56.4,32.4,63.1C16.9,69.8,0.2,72.3,-17.4,69.8C-35,67.3,-53.5,59.9,-65.2,46.2C-76.9,32.5,-81.8,12.6,-78.8,-5.4C-75.8,-23.4,-64.9,-39.5,-51,-51.2C-37.1,-62.9,-20.1,-70.2,-1.8,-68.1C16.5,-66,35.1,-72.1,47.5,-61.7Z" transform="translate(100 100)" />
                    </svg>
                  </div>

                  <div className={`w-14 h-14 ${darkMode ? 'bg-[#4A5CBA]/20' : 'bg-[#4A5CBA]/10'} rounded-lg flex items-center justify-center mr-4 relative z-10`}>
                    <div className="absolute inset-0 bg-[#4A5CBA]/10 rounded-lg animate-pulse-slow"></div>
                    <FaShieldAlt className="text-[#4A5CBA] text-2xl relative z-10" />
                  </div>
                  <div>
                    <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>What Sets Us Apart</h2>
                    <div className="w-20 h-1 bg-[#4A5CBA] mt-2"></div>
                  </div>
                </div>

                {/* Introduction */}
                <motion.div
                  variants={fadeInUp}
                  className={`${darkMode ? 'glass-card-dark' : 'glass-card'} p-6 rounded-xl shadow-md mb-8 relative overflow-hidden`}
                >
                  {/* Background pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                      <pattern id="intro-grid" width="20" height="20" patternUnits="userSpaceOnUse">
                        <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.8)" strokeWidth="0.5" />
                      </pattern>
                      <rect width="100%" height="100%" fill="url(#intro-grid)" />
                    </svg>
                  </div>

                  {/* Accent elements */}
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#4A5CBA] via-[#F5B93F] to-[#4A5CBA]"></div>
                  <div className="absolute -top-6 -right-6 w-16 h-16 rounded-full bg-[#4A5CBA]/10 blur-xl"></div>

                  <div className="relative z-10 flex items-start">
                    <FaQuoteLeft className="text-[#4A5CBA] text-xl mr-4 mt-1 opacity-70" />
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} leading-relaxed`}>
                      At CyberForce, we pride ourselves on delivering exceptional cybersecurity training through our unique approach and commitment to excellence. Here are the key features that distinguish our programs:
                    </p>
                  </div>
                </motion.div>

                {/* Clean Feature Grid */}
                <motion.div
                  variants={staggerContainer}
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mb-8"
                >
                  {[
                    {
                      title: 'Practical Learning',
                      description: 'Hands-on experience with real-world scenarios',
                      icon: <FaLaptopCode />,
                      color: '#4A5CBA',
                      features: ['Interactive labs', 'Real-world simulations', 'Hands-on exercises', 'Practical assessments']
                    },
                    {
                      title: 'Expert Instructors',
                      description: 'Learn from seasoned cybersecurity professionals',
                      icon: <FaGraduationCap />,
                      color: '#F5B93F',
                      features: ['Industry veterans', 'Certified professionals', 'Active practitioners', 'Passionate educators']
                    },
                    {
                      title: 'Cutting-Edge Curriculum',
                      description: 'Content reflecting the latest threats and techniques',
                      icon: <FaCode />,
                      color: '#10B981',
                      features: ['Regularly updated', 'Industry-aligned', 'Emerging threats', 'Latest technologies']
                    },
                    {
                      title: 'Collaborative Environment',
                      description: 'Share knowledge and grow with peers',
                      icon: <FaUsers />,
                      color: '#8B5CF6',
                      features: ['Peer learning', 'Team exercises', 'Knowledge sharing', 'Networking opportunities']
                    },
                    {
                      title: 'Comprehensive Training',
                      description: 'Full spectrum of cybersecurity topics',
                      icon: <FaShieldAlt />,
                      color: '#EC4899',
                      features: ['Beginner to advanced', 'Multiple domains', 'Specialized tracks', 'Certification preparation']
                    },
                    {
                      title: 'Innovative Approach',
                      description: 'Modern educational methodologies',
                      icon: <FaRegLightbulb />,
                      color: '#3B82F6',
                      features: ['Gamified learning', 'Adaptive content', 'Interactive tools', 'Continuous assessment']
                    }
                  ].map((feature, index) => (
                    <motion.div
                      key={index}
                      variants={cardVariant}
                      whileHover="hover"
                      className={`${darkMode ? 'glass-card-dark' : 'glass-card'} rounded-xl overflow-hidden shadow-md relative`}
                    >
                      {/* Background pattern */}
                      <div className="absolute inset-0 opacity-5">
                        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <pattern id={`feature-grid-${index}`} width="20" height="20" patternUnits="userSpaceOnUse">
                              <path d="M 20 0 L 0 0 0 20" fill="none" stroke={feature.color} strokeWidth="0.5" />
                            </pattern>
                          </defs>
                          <rect width="100%" height="100%" fill={`url(#feature-grid-${index})`} />
                        </svg>
                      </div>

                      {/* Top accent bar */}
                      <div className="h-1.5 bg-gradient-to-r" style={{
                        backgroundImage: `linear-gradient(to right, ${feature.color}, ${feature.color}50)`
                      }}></div>

                      {/* Glowing corner */}
                      <div className="absolute -top-6 -right-6 w-16 h-16 rounded-full blur-xl" style={{
                        backgroundColor: `${feature.color}20`
                      }}></div>

                      <div className="p-5 relative z-10">
                        <div className="flex items-center mb-4">
                          <div
                            className="w-12 h-12 rounded-lg flex items-center justify-center mr-4 relative"
                            style={{ backgroundColor: `${feature.color}15` }}
                          >
                            {/* Pulsing effect */}
                            <div
                              className="absolute inset-0 rounded-lg animate-pulse-slow"
                              style={{
                                backgroundColor: `${feature.color}10`,
                                boxShadow: `0 0 10px ${feature.color}30`
                              }}
                            ></div>

                            <div className="text-xl relative z-10" style={{ color: feature.color }}>
                              {feature.icon}
                            </div>
                          </div>
                          <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{feature.title}</h3>
                        </div>

                        <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} text-sm mb-4`}>
                          {feature.description}
                        </p>

                        <div className="border-t border-dashed pt-3 mt-3" style={{ borderColor: `${feature.color}30` }}>
                          <div className="grid grid-cols-2 gap-2">
                            {feature.features.map((item, idx) => (
                              <div key={idx} className="flex items-start">
                                <div className="flex-shrink-0 mt-1">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-3.5 w-3.5 mr-1.5"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    style={{ color: feature.color }}
                                  >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                </div>
                                <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{item}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>

                {/* Enhanced Call to Action */}
                <motion.div
                  variants={fadeInUp}
                  className={`${darkMode ? 'glass-card-dark' : 'glass-card'} p-8 rounded-xl shadow-lg relative overflow-hidden`}
                >
                  {/* Background elements */}
                  <div className="absolute inset-0 opacity-10">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                      <defs>
                        <pattern id="cta-grid" width="40" height="40" patternUnits="userSpaceOnUse">
                          <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.8)" strokeWidth="0.5" />
                        </pattern>
                      </defs>
                      <rect width="100%" height="100%" fill="url(#cta-grid)" />
                    </svg>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#4A5CBA] via-[#F5B93F] to-[#4A5CBA]"></div>
                  <div className="absolute -top-10 -right-10 w-40 h-40 rounded-full bg-[#4A5CBA]/10 blur-2xl"></div>
                  <div className="absolute -bottom-10 -left-10 w-40 h-40 rounded-full bg-[#F5B93F]/10 blur-2xl"></div>

                  {/* Floating icons */}
                  <div className="absolute top-6 left-6 text-[#4A5CBA]/20 text-4xl">
                    <FaShieldAlt />
                  </div>
                  <div className="absolute bottom-6 right-6 text-[#F5B93F]/20 text-4xl">
                    <FaGraduationCap />
                  </div>

                  <div className="relative z-10 flex flex-col items-center text-center">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-[#4A5CBA] to-[#F5B93F] flex items-center justify-center mb-4 shadow-lg">
                      <div className="w-14 h-14 rounded-full bg-[#0B1120] flex items-center justify-center">
                        <FaRocket className="text-[#F5B93F] text-2xl" />
                      </div>
                    </div>

                    <h3 className={`text-2xl font-bold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      Ready to Experience the <span className="text-[#4A5CBA]">Cyber</span><span className="text-[#F5B93F]">Force</span> Difference?
                    </h3>

                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-6 max-w-2xl`}>
                      Join us today and discover why our approach to cybersecurity training is transforming the industry. Our innovative programs and expert instructors are ready to help you achieve your cybersecurity goals.
                    </p>

                    <div className="flex flex-wrap justify-center gap-4">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                        className="bg-gradient-to-r from-[#4A5CBA] to-[#3A4CAA] text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg shadow-[#4A5CBA]/20"
                      >
                        Explore Our Programs
                      </motion.button>

                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                        className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border px-8 py-3 rounded-lg font-medium transition-all duration-300 shadow-md`}
                      >
                        Contact Us
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}

export default About;