import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { FaLock, FaTrophy, FaUsers, FaClock, FaShieldAlt, FaCode, FaKey, FaServer, FaNetworkWired, FaUserSecret, FaHackerNews, FaBug, FaUnlock, FaLightbulb, FaCheck, FaInfoCircle, FaExclamationTriangle, FaArrowLeft } from 'react-icons/fa';

// SQL Injection Simulator Component
const SQLInjectionSimulator = () => {
  const { darkMode } = useGlobalTheme();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [message, setMessage] = useState(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [hintLevel, setHintLevel] = useState(0);
  const [startTime] = useState(new Date());
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'info', text: 'SQL Injection Challenge initialized...' },
    { type: 'info', text: 'Target: BankSecure Login Portal' },
    { type: 'info', text: 'Objective: Bypass authentication without valid credentials' },
    { type: 'system', text: 'Type your SQL injection payload in the username field.' }
  ]);

  // Simulated backend query
  const simulateLoginQuery = (user, pass) => {
    // This simulates what would happen on the server
    const simulatedQuery = `SELECT * FROM users WHERE username='${user}' AND password='${pass}'`;

    // Add the query to console output
    addToConsole('query', `Executing: ${simulatedQuery}`);

    // Simplified SQL injection detection
    // Check for common SQL injection patterns
    const successPatterns = [
      "' or '", // Matches most variations like ' OR '1'='1
      "' or 1=",
      "' or 1=1",
      "' --",
      "'#",
      "' or true",
      "admin'--",
      "\" or \""
    ];

    // Check if any of the patterns are in the username
    const hasInjection = user.includes("'") || user.includes('"');
    const isSuccessful = successPatterns.some(pattern =>
      user.toLowerCase().includes(pattern.toLowerCase())
    );

    if (hasInjection) {
      if (isSuccessful) {
        // Successful SQL injection
        return { success: true, query: simulatedQuery };
      } else {
        // SQL injection attempt, but not quite right
        return { success: false, error: 'SQL syntax error', query: simulatedQuery };
      }
    }

    // Regular login attempt (will always fail in this simulation)
    return { success: false, error: 'Invalid credentials', query: simulatedQuery };
  };

  const addToConsole = (type, text) => {
    setConsoleOutput(prev => [...prev, { type, text }]);
    // Auto-scroll console to bottom
    setTimeout(() => {
      const consoleElement = document.getElementById('sql-console');
      if (consoleElement) {
        consoleElement.scrollTop = consoleElement.scrollHeight;
      }
    }, 100);
  };

  const handleLogin = (e) => {
    e.preventDefault();
    setAttempts(prev => prev + 1);

    // Simulate server request
    addToConsole('system', 'Sending login request...');

    setTimeout(() => {
      try {
        const result = simulateLoginQuery(username, password);

        if (result.success) {
          setIsSuccess(true);
          setMessage({ type: 'success', text: 'Authentication bypassed! You have successfully exploited the SQL injection vulnerability.' });
          addToConsole('success', 'Authentication bypassed successfully!');
          addToConsole('success', 'Accessing admin panel...');
          addToConsole('success', 'Flag found: flag\{sql_injection_master_2023\}');

          // Calculate time spent
          const endTime = new Date();
          const timeSpent = Math.floor((endTime - startTime) / 1000);
          const minutes = Math.floor(timeSpent / 60);
          const seconds = timeSpent % 60;
          const formattedTime = `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;

          addToConsole('info', `Challenge completed in ${formattedTime}`);
        } else {
          if (result.error === 'SQL syntax error') {
            setMessage({ type: 'warning', text: 'SQL syntax error in your injection attempt. You\'re on the right track!' });
            addToConsole('error', 'Database returned: SQL syntax error near "' + username + '"');
          } else {
            setMessage({ type: 'error', text: 'Login failed: Invalid credentials' });
            addToConsole('error', 'Authentication failed: Invalid username or password');
          }
        }
      } catch (error) {
        console.error('Error in challenge simulation:', error);
        setMessage({ type: 'error', text: 'An unexpected error occurred. Please try again.' });
        addToConsole('error', 'System error: ' + (error.message || 'Unknown error'));
      }
    }, 800); // Simulate network delay
  };

  const getNextHint = () => {
    setHintLevel(prev => prev + 1);
    setShowHint(true);

    // Add hint request to console
    addToConsole('system', 'Hint requested...');

    const hints = [
      "Try entering special characters like a single quote (') in the username field to see how the application responds.",
      "The login query might look like: SELECT * FROM users WHERE username='[input]' AND password='[input]'",
      "Using a statement like ' OR '1'='1 might help you bypass the authentication by making the WHERE clause always true.",
      "Try this payload in the username field: ' OR '1'='1'-- (The -- comments out the rest of the query)"
    ];

    if (hintLevel < hints.length) {
      addToConsole('hint', hints[hintLevel]);
    }
  };

  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
      {/* Challenge Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex justify-between items-center`}>
        <h2 className="text-xl font-bold flex items-center">
          {isSuccess ? <FaUnlock className="text-green-500 mr-2" /> : <FaLock className="text-red-500 mr-2" />}
          SQL Injection Challenge
        </h2>
        <div>
          <button
            onClick={getNextHint}
            className={`px-3 py-1 rounded-lg flex items-center ${
              darkMode
                ? 'bg-gray-800 hover:bg-gray-700 text-gray-300'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
            }`}
          >
            <FaLightbulb className="mr-1 text-yellow-500" /> Get Hint
          </button>
        </div>
      </div>

      {/* Challenge Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
        {/* Left Column - Login Form */}
        <div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border mb-4`}>
            <h3 className="font-bold mb-4">BankSecure Login Portal</h3>

            {message && (
              <div className={`p-3 rounded-lg mb-4 ${
                message.type === 'success' ? (darkMode ? 'bg-green-900/30 text-green-400' : 'bg-green-100 text-green-800') :
                message.type === 'warning' ? (darkMode ? 'bg-yellow-900/30 text-yellow-400' : 'bg-yellow-100 text-yellow-800') :
                (darkMode ? 'bg-red-900/30 text-red-400' : 'bg-red-100 text-red-800')
              }`}>
                <div className="flex items-start">
                  {message.type === 'success' && <FaCheck className="mt-1 mr-2" />}
                  {message.type === 'warning' && <FaExclamationTriangle className="mt-1 mr-2" />}
                  {message.type === 'error' && <FaInfoCircle className="mt-1 mr-2" />}
                  <p>{message.text}</p>
                </div>
              </div>
            )}

            <form onSubmit={handleLogin}>
              <div className="mb-4">
                <label htmlFor="username" className="block mb-2 font-medium">Username</label>
                <input
                  type="text"
                  id="username"
                  className={`w-full p-2 rounded-lg ${darkMode ? 'bg-[#1A1F35] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Enter username"
                  required
                />
              </div>

              <div className="mb-4">
                <label htmlFor="password" className="block mb-2 font-medium">Password</label>
                <input
                  type="password"
                  id="password"
                  className={`w-full p-2 rounded-lg ${darkMode ? 'bg-[#1A1F35] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                />
              </div>

              <button
                type="submit"
                className={`w-full py-2 px-4 rounded-lg ${isSuccess ? 'bg-green-600 hover:bg-green-700' : 'bg-[#88cc14] hover:bg-[#7ab811]'} text-black font-medium`}
                disabled={isSuccess}
              >
                {isSuccess ? 'Authentication Bypassed' : 'Login'}
              </button>
            </form>
          </div>

          {/* Hints Section */}
          {showHint && (
            <div className={`p-4 rounded-lg ${
              darkMode ? 'bg-yellow-900/20 border-yellow-900/30' : 'bg-yellow-50 border-yellow-200'
            } border`}>
              <div className="flex items-start">
                <FaLightbulb className={`mt-1 mr-2 ${darkMode ? 'text-yellow-400' : 'text-yellow-600'}`} />
                <div>
                  <h4 className={`font-medium ${darkMode ? 'text-yellow-300' : 'text-yellow-800'}`}>Hint {hintLevel}:</h4>
                  {hintLevel === 1 && (
                    <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                      Try entering special characters like a single quote (') in the username field to see how the application responds.
                    </p>
                  )}
                  {hintLevel === 2 && (
                    <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                      The login query might look like: SELECT * FROM users WHERE username='[input]' AND password='[input]'
                    </p>
                  )}
                  {hintLevel === 3 && (
                    <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                      Using a statement like ' OR '1'='1 might help you bypass the authentication by making the WHERE clause always true.
                    </p>
                  )}
                  {hintLevel === 4 && (
                    <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                      Try this payload in the username field: ' OR '1'='1'-- (The -- comments out the rest of the query)
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right Column - Console Output */}
        <div>
          <div className={`rounded-lg overflow-hidden border ${darkMode ? 'border-gray-700' : 'border-gray-300'}`}>
            <div className="bg-black p-2 text-xs text-gray-400 border-b border-gray-700 flex justify-between items-center">
              <span>SQL Injection Challenge Console</span>
              <span className="px-2 py-0.5 rounded bg-gray-800 text-gray-400">Attempts: {attempts}</span>
            </div>
            <div
              id="sql-console"
              className="bg-black text-green-400 p-4 font-mono text-sm h-80 overflow-y-auto"
            >
              {consoleOutput.map((line, index) => (
                <div key={index} className={`mb-1 ${line.type === 'error' ? 'text-red-400' : line.type === 'success' ? 'text-green-400' : line.type === 'query' ? 'text-blue-400' : line.type === 'hint' ? 'text-yellow-400' : 'text-green-400'}`}>
                  {line.type === 'error' && '[!] '}
                  {line.type === 'success' && '[+] '}
                  {line.type === 'query' && '[>] '}
                  {line.type === 'hint' && '[?] '}
                  {line.type === 'system' && '[*] '}
                  {line.type === 'info' && '[i] '}
                  {line.text}
                </div>
              ))}
            </div>
          </div>

          {isSuccess && (
            <div className={`mt-4 p-4 rounded-lg ${darkMode ? 'bg-green-900/20 border-green-900/30' : 'bg-green-50 border-green-200'} border`}>
              <div className="flex items-start">
                <FaTrophy className={`mt-1 mr-2 ${darkMode ? 'text-yellow-400' : 'text-yellow-600'}`} />
                <div>
                  <h4 className={`font-medium ${darkMode ? 'text-green-300' : 'text-green-800'}`}>Challenge Completed!</h4>
                  <p className={darkMode ? 'text-green-200' : 'text-green-700'}>
                    You've successfully exploited the SQL injection vulnerability. In a real application, this would give you unauthorized access to user accounts.
                  </p>
                  <div className="mt-2">
                    <span className="font-medium">Flag: </span>
                    <code className="px-2 py-1 rounded bg-black text-green-400">{'flag{sql_injection_master_2023}'}</code>
                  </div>
                  <div className={`mt-4 pt-4 border-t ${darkMode ? 'border-green-900/30' : 'border-green-200'}`}>
                    <h4 className={`font-medium ${darkMode ? 'text-green-300' : 'text-green-800'} mb-2`}>Ready for more challenges?</h4>
                    <p className={`${darkMode ? 'text-green-200' : 'text-green-700'} mb-3`}>
                      This is just a taste of what XCerberus has to offer. Sign up to access dozens more challenges across different cybersecurity domains!
                    </p>
                    <Link to="/signup" className="inline-block px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors">
                      Create Free Account
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const StaticChallenges = () => {
  const { darkMode } = useGlobalTheme();
  const { profile } = useAuth();

  // Sample challenge data
  const challengeCategories = [
    {
      id: 'web',
      name: 'Web Security',
      icon: <FaCode className="text-blue-500" />,
      description: 'Exploit vulnerabilities in web applications, including SQL injection, XSS, CSRF, and more.',
      challenges: [
        {
          title: 'SQL Injection Basics',
          difficulty: 'Beginner',
          points: 100,
          participants: 1243,
          completion_rate: 68,
          estimated_time: 30
        },
        {
          title: 'XSS Challenge',
          difficulty: 'Intermediate',
          points: 250,
          participants: 876,
          completion_rate: 42,
          estimated_time: 45
        },
        {
          title: 'Advanced CSRF Attack',
          difficulty: 'Advanced',
          points: 500,
          participants: 432,
          completion_rate: 21,
          estimated_time: 60
        }
      ]
    },
    {
      id: 'crypto',
      name: 'Cryptography',
      icon: <FaKey className="text-purple-500" />,
      description: 'Break cryptographic implementations, crack ciphers, and solve encryption challenges.',
      challenges: [
        {
          title: 'Caesar Cipher',
          difficulty: 'Beginner',
          points: 75,
          participants: 1567,
          completion_rate: 82,
          estimated_time: 20
        },
        {
          title: 'RSA Implementation Flaws',
          difficulty: 'Intermediate',
          points: 300,
          participants: 654,
          completion_rate: 35,
          estimated_time: 50
        }
      ]
    },
    {
      id: 'network',
      name: 'Network Security',
      icon: <FaNetworkWired className="text-green-500" />,
      description: 'Analyze network traffic, exploit protocol vulnerabilities, and secure network infrastructure.',
      challenges: [
        {
          title: 'Packet Analysis',
          difficulty: 'Intermediate',
          points: 200,
          participants: 876,
          completion_rate: 58,
          estimated_time: 40
        },
        {
          title: 'Man-in-the-Middle Attack',
          difficulty: 'Advanced',
          points: 450,
          participants: 321,
          completion_rate: 28,
          estimated_time: 75
        }
      ]
    }
  ];

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold mb-6">Cybersecurity Challenges</h1>

        {/* Introduction */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
          <h2 className="text-2xl font-bold mb-4">Test Your Skills with Real-World Challenges</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
            Put your cybersecurity knowledge to the test with our hands-on challenges. Solve realistic scenarios, exploit vulnerabilities, and improve your skills in a safe environment.
          </p>
          {!profile && (
            <div className="flex flex-wrap gap-3 mt-4">
              <Link to="/login" className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors">
                Sign In to Start Challenges
              </Link>
              <Link to="/signup" className="px-4 py-2 border border-[#88cc14] text-[#88cc14] hover:bg-[#88cc14]/10 font-medium rounded-lg transition-colors">
                Create Free Account
              </Link>
            </div>
          )}
        </div>

        {/* Interactive Demo Challenge */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
          <h2 className="text-xl font-bold mb-4">Interactive Demo: SQL Injection Challenge</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
            Try this fully functional SQL injection challenge without creating an account. Bypass the login form by exploiting a SQL injection vulnerability.
          </p>

          {/* SQL Injection Simulator */}
          <SQLInjectionSimulator />
        </div>

        {/* Challenge Categories */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {challengeCategories.map(category => (
            <div key={category.id} className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden flex flex-col h-full`}>
              <div className="p-6 flex flex-col flex-grow">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-opacity-20 rounded-full flex items-center justify-center" style={{ backgroundColor: darkMode ? 'rgba(136, 204, 20, 0.1)' : 'rgba(136, 204, 20, 0.1)' }}>
                    {category.icon}
                  </div>
                  <h3 className="text-xl font-bold">{category.name}</h3>
                </div>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                  {category.description}
                </p>
                <div className="flex items-center text-sm mb-4">
                  <span className="flex items-center mr-4"><FaUsers className="mr-1" /> {category.challenges.reduce((sum, c) => sum + c.participants, 0)} participants</span>
                  <span className="flex items-center"><FaTrophy className="mr-1" /> {category.challenges.length} challenges</span>
                </div>
                <div className="mt-auto pt-4">
                  <Link to="/login" className="block w-full text-center px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors">
                    View Challenges
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Featured Challenges */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
          <h2 className="text-xl font-bold mb-4">Featured Challenges</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {challengeCategories.flatMap(category =>
              category.challenges.slice(0, 1).map((challenge, index) => (
                <div key={`${category.id}-${index}`} className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg`}>
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-opacity-20 rounded-full flex items-center justify-center" style={{ backgroundColor: darkMode ? 'rgba(136, 204, 20, 0.1)' : 'rgba(136, 204, 20, 0.1)' }}>
                        {category.icon}
                      </div>
                      <h3 className="font-bold">{challenge.title}</h3>
                    </div>
                    <span className={`px-2 py-0.5 rounded-full text-xs ${getDifficultyColor(challenge.difficulty, darkMode)}`}>
                      {challenge.difficulty}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-3">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center"><FaTrophy className="mr-1" /> {challenge.points} pts</span>
                      <span className="flex items-center"><FaUsers className="mr-1" /> {challenge.participants}</span>
                      <span className="flex items-center"><FaClock className="mr-1" /> {challenge.estimated_time} min</span>
                    </div>
                    <span className={`${challenge.completion_rate > 50 ? 'text-green-500' : 'text-yellow-500'}`}>
                      {challenge.completion_rate}% completion
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* How Challenges Work */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
          <h2 className="text-xl font-bold mb-4">How Challenges Work</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mb-4">
                <FaShieldAlt className="text-blue-500 text-2xl" />
              </div>
              <h3 className="text-lg font-bold mb-2">Secure Environment</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                All challenges run in isolated environments, allowing you to practice offensive techniques safely and legally.
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
                <FaTrophy className="text-green-500 text-2xl" />
              </div>
              <h3 className="text-lg font-bold mb-2">Earn Points & Badges</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Complete challenges to earn points, unlock badges, and climb the global leaderboard.
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mb-4">
                <FaUserSecret className="text-purple-500 text-2xl" />
              </div>
              <h3 className="text-lg font-bold mb-2">Learn by Doing</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Each challenge includes hints, resources, and detailed solutions to help you learn as you solve.
              </p>
            </div>
          </div>
        </div>

        {/* Challenge Difficulty Levels */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
          <h2 className="text-xl font-bold mb-4">Challenge Difficulty Levels</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border-l-4 border-green-500`}>
              <h3 className="font-bold text-green-500 mb-2">Beginner</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>
                Perfect for those new to cybersecurity. These challenges teach fundamental concepts and basic techniques.
              </p>
            </div>
            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border-l-4 border-yellow-500`}>
              <h3 className="font-bold text-yellow-500 mb-2">Intermediate</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>
                For those with some experience. These challenges require deeper knowledge and more advanced techniques.
              </p>
            </div>
            <div className={`${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'} p-4 rounded-lg border-l-4 border-red-500`}>
              <h3 className="font-bold text-red-500 mb-2">Advanced</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>
                For experienced security professionals. These challenges simulate complex, real-world scenarios.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to get color based on difficulty
const getDifficultyColor = (difficulty, darkMode) => {
  switch (difficulty.toLowerCase()) {
    case 'beginner':
      return darkMode ? 'bg-green-900/30 text-green-400' : 'bg-green-100 text-green-800';
    case 'intermediate':
      return darkMode ? 'bg-yellow-900/30 text-yellow-400' : 'bg-yellow-100 text-yellow-800';
    case 'advanced':
      return darkMode ? 'bg-red-900/30 text-red-400' : 'bg-red-100 text-red-800';
    default:
      return darkMode ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-800';
  }
};

export default StaticChallenges;
