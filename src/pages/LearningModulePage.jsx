import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaGraduationCap, FaCode, FaLaptopCode, FaQuestionCircle, FaCheck, FaLock, FaCrown } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { useAuth } from '../contexts/AuthContext';

// Mock data for learning modules
const MODULES = {
  'intro-to-cybersecurity': {
    id: 'intro-to-cybersecurity',
    title: 'Introduction to Cybersecurity',
    description: 'Learn the fundamentals of cybersecurity, key concepts, and why it matters.',
    level: 'Beginner',
    duration: '60 min',
    sections: [
      {
        id: 'section-1',
        title: 'What is Cybersecurity?',
        type: 'theory',
        completed: true,
        content: `
          <h2>What is Cybersecurity?</h2>
          <p>Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These attacks are usually aimed at accessing, changing, or destroying sensitive information; extorting money from users; or interrupting normal business processes.</p>
          
          <p>Implementing effective cybersecurity measures is particularly challenging today because there are more devices than people, and attackers are becoming more innovative.</p>
          
          <h3>Why Cybersecurity Matters</h3>
          <p>In today's connected world, everyone benefits from advanced cyber defense programs. At an individual level, a cybersecurity attack can result in everything from identity theft, to extortion attempts, to the loss of important data like family photos. Everyone relies on critical infrastructure like power plants, hospitals, and financial service companies. Securing these and other organizations is essential to keeping our society functioning.</p>
          
          <h3>Key Cybersecurity Concepts</h3>
          <ul>
            <li><strong>Confidentiality:</strong> Ensuring that information is accessible only to those authorized to have access.</li>
            <li><strong>Integrity:</strong> Maintaining and assuring the accuracy and completeness of data over its entire lifecycle.</li>
            <li><strong>Availability:</strong> Ensuring that information and resources are available to those who need them.</li>
          </ul>
        `
      },
      {
        id: 'section-2',
        title: 'Types of Cyber Threats',
        type: 'theory',
        completed: true,
        content: `
          <h2>Types of Cyber Threats</h2>
          <p>Cyber threats come in many forms. Some of the most common include:</p>
          
          <h3>Malware</h3>
          <p>Malware is malicious software designed to cause damage to a computer, server, client, or computer network. Types of malware include:</p>
          <ul>
            <li><strong>Viruses:</strong> Programs that can replicate themselves and spread to other computers.</li>
            <li><strong>Worms:</strong> Similar to viruses but can spread without human action.</li>
            <li><strong>Trojans:</strong> Malware disguised as legitimate software.</li>
            <li><strong>Ransomware:</strong> Malware that encrypts files and demands payment for the decryption key.</li>
            <li><strong>Spyware:</strong> Software that secretly monitors user activity.</li>
          </ul>
          
          <h3>Phishing</h3>
          <p>Phishing is a type of social engineering attack often used to steal user data, including login credentials and credit card numbers. It occurs when an attacker, masquerading as a trusted entity, dupes a victim into opening an email, instant message, or text message.</p>
          
          <h3>Man-in-the-Middle (MitM) Attacks</h3>
          <p>A MitM attack occurs when attackers insert themselves into a two-party transaction. Once the attackers interrupt the traffic, they can filter and steal data.</p>
          
          <h3>Denial-of-Service (DoS) Attacks</h3>
          <p>A DoS attack floods systems, servers, or networks with traffic to exhaust resources and bandwidth. This eventually brings the system down and prevents legitimate users from accessing the service.</p>
        `
      },
      {
        id: 'section-3',
        title: 'Basic Security Practices',
        type: 'theory',
        completed: false,
        content: `
          <h2>Basic Security Practices</h2>
          <p>Everyone can take simple steps to improve their cybersecurity posture:</p>
          
          <h3>Strong Password Practices</h3>
          <ul>
            <li>Use long, complex passwords with a mix of letters, numbers, and special characters.</li>
            <li>Don't reuse passwords across different accounts.</li>
            <li>Consider using a password manager to generate and store strong passwords.</li>
            <li>Enable two-factor authentication (2FA) whenever possible.</li>
          </ul>
          
          <h3>Software Updates</h3>
          <p>Keep your software, operating systems, and applications up-to-date. Updates often include security patches for recently discovered vulnerabilities.</p>
          
          <h3>Data Backups</h3>
          <p>Regularly back up important data to an external hard drive or cloud storage service. This can help you recover from ransomware attacks or other data loss incidents.</p>
          
          <h3>Be Cautious Online</h3>
          <ul>
            <li>Be skeptical of unsolicited emails, especially those containing links or attachments.</li>
            <li>Verify the source before providing personal information.</li>
            <li>Use secure, encrypted connections (look for HTTPS) when transmitting sensitive data.</li>
            <li>Be careful what you share on social media.</li>
          </ul>
        `
      },
      {
        id: 'section-4',
        title: 'Cybersecurity Lab: Password Strength',
        type: 'lab',
        completed: false,
        content: `
          <h2>Lab: Testing Password Strength</h2>
          <p>In this lab, you'll learn how to evaluate password strength and understand why strong passwords are important.</p>
          
          <h3>Objectives</h3>
          <ul>
            <li>Understand what makes a password strong or weak</li>
            <li>Learn how to use a password strength checker</li>
            <li>Create a strong password policy</li>
          </ul>
          
          <h3>Instructions</h3>
          <ol>
            <li>Open the password strength testing tool below</li>
            <li>Enter different passwords and observe their strength ratings</li>
            <li>Try to create a password that achieves the highest strength rating</li>
            <li>Document what characteristics make a password stronger</li>
          </ol>
          
          <div class="lab-interface">
            <h4>Password Strength Tester</h4>
            <div class="lab-input">
              <input type="password" id="password-input" placeholder="Enter a password to test" />
              <button id="check-button">Check Strength</button>
            </div>
            <div class="strength-meter">
              <div class="meter-bar">
                <div class="meter-fill" style="width: 0%"></div>
              </div>
              <p class="strength-text">Password strength: Not tested</p>
            </div>
            <div class="password-feedback"></div>
          </div>
        `
      },
      {
        id: 'section-5',
        title: 'Module Quiz',
        type: 'quiz',
        completed: false,
        questions: [
          {
            id: 'q1',
            question: 'What are the three main principles of cybersecurity?',
            options: [
              'Confidentiality, Integrity, Availability',
              'Protection, Detection, Response',
              'Prevention, Mitigation, Recovery',
              'Authentication, Authorization, Accounting'
            ],
            correctAnswer: 0
          },
          {
            id: 'q2',
            question: 'Which of the following is NOT a type of malware?',
            options: [
              'Virus',
              'Firewall',
              'Ransomware',
              'Spyware'
            ],
            correctAnswer: 1
          },
          {
            id: 'q3',
            question: 'What is phishing?',
            options: [
              'A type of malware that encrypts files',
              'A social engineering attack that tricks users into revealing sensitive information',
              'A method to strengthen passwords',
              'A technique to secure network communications'
            ],
            correctAnswer: 1
          },
          {
            id: 'q4',
            question: 'Which of the following is a good password practice?',
            options: [
              'Using the same password for all accounts',
              'Writing down passwords on sticky notes',
              'Using short, simple passwords that are easy to remember',
              'Enabling two-factor authentication'
            ],
            correctAnswer: 3
          },
          {
            id: 'q5',
            question: 'Why is it important to keep software updated?',
            options: [
              'To get new features only',
              'To make the software run faster',
              'To install security patches for vulnerabilities',
              'To use more disk space'
            ],
            correctAnswer: 2
          }
        ]
      }
    ]
  },
  'os-concepts': {
    id: 'os-concepts',
    title: 'Operating System Concepts',
    description: 'Understand how operating systems work and their security implications.',
    level: 'Beginner',
    duration: '90 min',
    sections: [
      {
        id: 'section-1',
        title: 'Introduction to Operating Systems',
        type: 'theory',
        completed: false,
        content: `
          <h2>Introduction to Operating Systems</h2>
          <p>An operating system (OS) is system software that manages computer hardware, software resources, and provides common services for computer programs.</p>
          
          <h3>Key Functions of an Operating System</h3>
          <ul>
            <li><strong>Process Management:</strong> Creating, scheduling, and terminating processes</li>
            <li><strong>Memory Management:</strong> Allocating and deallocating memory space</li>
            <li><strong>File System Management:</strong> Creating, reading, writing, and deleting files</li>
            <li><strong>Device Management:</strong> Managing input/output devices</li>
            <li><strong>Security:</strong> Protecting system resources and user data</li>
          </ul>
          
          <h3>Common Operating Systems</h3>
          <ul>
            <li><strong>Windows:</strong> Microsoft's widely used OS for personal computers</li>
            <li><strong>macOS:</strong> Apple's operating system for Macintosh computers</li>
            <li><strong>Linux:</strong> An open-source Unix-like operating system</li>
            <li><strong>Android:</strong> Google's mobile operating system based on Linux</li>
            <li><strong>iOS:</strong> Apple's mobile operating system for iPhones and iPads</li>
          </ul>
        `
      },
      // Additional sections would be defined here
    ]
  },
  // Additional modules would be defined here
};

const LearningModulePage = () => {
  const { moduleId } = useParams();
  const navigate = useNavigate();
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const { subscriptionLevel, isPremium } = useSubscription();
  const [activeSection, setActiveSection] = useState(0);
  const [module, setModule] = useState(null);
  const [quizAnswers, setQuizAnswers] = useState({});
  const [quizSubmitted, setQuizSubmitted] = useState(false);
  const [quizScore, setQuizScore] = useState(0);
  
  // Redirect if not logged in
  useEffect(() => {
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);
  
  // Load module data
  useEffect(() => {
    if (moduleId && MODULES[moduleId]) {
      setModule(MODULES[moduleId]);
      // Reset quiz state when module changes
      setQuizAnswers({});
      setQuizSubmitted(false);
      setQuizScore(0);
    } else {
      // Module not found
      navigate('/simplified-dashboard');
    }
  }, [moduleId, navigate]);
  
  if (!module) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-20 px-4`}>
        <div className="container mx-auto">
          <p>Loading module...</p>
        </div>
      </div>
    );
  }
  
  const handleSectionChange = (index) => {
    setActiveSection(index);
    // Reset quiz state when changing to a new section
    if (module.sections[index].type === 'quiz') {
      setQuizAnswers({});
      setQuizSubmitted(false);
      setQuizScore(0);
    }
  };
  
  const handleQuizAnswer = (questionId, answerIndex) => {
    if (!quizSubmitted) {
      setQuizAnswers(prev => ({
        ...prev,
        [questionId]: answerIndex
      }));
    }
  };
  
  const handleQuizSubmit = () => {
    const currentSection = module.sections[activeSection];
    if (currentSection.type !== 'quiz') return;
    
    let score = 0;
    currentSection.questions.forEach(question => {
      if (quizAnswers[question.id] === question.correctAnswer) {
        score++;
      }
    });
    
    setQuizScore(score);
    setQuizSubmitted(true);
  };
  
  const currentSection = module.sections[activeSection];
  const isQuizSection = currentSection.type === 'quiz';
  const allQuestionsAnswered = isQuizSection ? 
    currentSection.questions.every(q => quizAnswers[q.id] !== undefined) : 
    true;
  
  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-20 px-4`}>
      <div className="container mx-auto">
        {/* Module Header */}
        <div className="mb-6">
          <Link 
            to="/simplified-dashboard" 
            className={`inline-flex items-center ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} mb-4`}
          >
            <FaArrowLeft className="mr-2" /> Back to Dashboard
          </Link>
          
          <h1 className="text-3xl font-bold mb-2">{module.title}</h1>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>{module.description}</p>
          
          <div className="flex flex-wrap gap-3 mb-6">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
              {module.level}
            </span>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
              {module.duration}
            </span>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
              {module.sections.length} Sections
            </span>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row gap-6">
          {/* Module Navigation */}
          <div className="md:w-1/4">
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-4 sticky top-24`}>
              <h2 className="font-bold text-lg mb-4">Module Sections</h2>
              <ul className="space-y-2">
                {module.sections.map((section, index) => (
                  <li key={section.id}>
                    <button
                      onClick={() => handleSectionChange(index)}
                      className={`w-full text-left p-2 rounded flex items-center ${
                        activeSection === index
                          ? 'bg-[#88cc14] text-black'
                          : darkMode
                            ? 'hover:bg-gray-800'
                            : 'hover:bg-gray-100'
                      }`}
                    >
                      {section.type === 'theory' && <FaGraduationCap className="mr-2" />}
                      {section.type === 'lab' && <FaLaptopCode className="mr-2" />}
                      {section.type === 'quiz' && <FaQuestionCircle className="mr-2" />}
                      
                      <span className="flex-1 text-sm">{section.title}</span>
                      
                      {section.completed && (
                        <FaCheck className="text-green-500 ml-2" />
                      )}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          {/* Module Content */}
          <div className="md:w-3/4">
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
              {currentSection.type === 'theory' && (
                <div 
                  className="prose max-w-none dark:prose-invert"
                  dangerouslySetInnerHTML={{ __html: currentSection.content }}
                />
              )}
              
              {currentSection.type === 'lab' && (
                <div>
                  <div 
                    className="prose max-w-none dark:prose-invert mb-6"
                    dangerouslySetInnerHTML={{ __html: currentSection.content }}
                  />
                  
                  <div className={`mt-6 p-4 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'}`}>
                    <h3 className="font-bold mb-2">Lab Controls</h3>
                    <div className="flex space-x-2">
                      <button className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded">
                        Start Lab
                      </button>
                      <button className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded">
                        Reset Lab
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {currentSection.type === 'quiz' && (
                <div>
                  <h2 className="text-xl font-bold mb-6">Module Quiz</h2>
                  
                  {quizSubmitted ? (
                    <div className="mb-6">
                      <div className={`p-4 rounded-lg ${
                        quizScore === currentSection.questions.length
                          ? 'bg-green-500/20 text-green-500'
                          : quizScore >= currentSection.questions.length / 2
                            ? 'bg-yellow-500/20 text-yellow-500'
                            : 'bg-red-500/20 text-red-500'
                      }`}>
                        <h3 className="font-bold text-lg">Quiz Results</h3>
                        <p>You scored {quizScore} out of {currentSection.questions.length}</p>
                        {quizScore === currentSection.questions.length ? (
                          <p className="mt-2">Perfect score! Great job!</p>
                        ) : quizScore >= currentSection.questions.length / 2 ? (
                          <p className="mt-2">Good job! Review the questions you missed and try again.</p>
                        ) : (
                          <p className="mt-2">You might want to review the module content and try again.</p>
                        )}
                      </div>
                      
                      <button 
                        onClick={() => {
                          setQuizAnswers({});
                          setQuizSubmitted(false);
                          setQuizScore(0);
                        }}
                        className="mt-4 px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded"
                      >
                        Retry Quiz
                      </button>
                    </div>
                  ) : (
                    <div>
                      {currentSection.questions.map((question, qIndex) => (
                        <div key={question.id} className={`mb-6 p-4 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'}`}>
                          <h3 className="font-bold mb-3">Question {qIndex + 1}: {question.question}</h3>
                          <div className="space-y-2">
                            {question.options.map((option, oIndex) => (
                              <div 
                                key={oIndex}
                                className={`p-3 rounded-lg cursor-pointer ${
                                  quizAnswers[question.id] === oIndex
                                    ? 'bg-[#88cc14] text-black'
                                    : darkMode
                                      ? 'bg-gray-800 hover:bg-gray-700'
                                      : 'bg-white hover:bg-gray-100 border border-gray-200'
                                }`}
                                onClick={() => handleQuizAnswer(question.id, oIndex)}
                              >
                                {option}
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                      
                      <button 
                        onClick={handleQuizSubmit}
                        disabled={!allQuestionsAnswered}
                        className={`px-4 py-2 rounded ${
                          allQuestionsAnswered
                            ? 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                            : 'bg-gray-400 cursor-not-allowed text-gray-700'
                        }`}
                      >
                        Submit Quiz
                      </button>
                    </div>
                  )}
                </div>
              )}
              
              {/* Navigation Buttons */}
              <div className="mt-8 flex justify-between">
                <button 
                  onClick={() => handleSectionChange(Math.max(0, activeSection - 1))}
                  disabled={activeSection === 0}
                  className={`px-4 py-2 rounded ${
                    activeSection === 0
                      ? 'bg-gray-400 cursor-not-allowed text-gray-700'
                      : darkMode
                        ? 'bg-gray-800 hover:bg-gray-700 text-white'
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
                  }`}
                >
                  Previous
                </button>
                
                {activeSection < module.sections.length - 1 ? (
                  <button 
                    onClick={() => handleSectionChange(activeSection + 1)}
                    className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded"
                  >
                    Next
                  </button>
                ) : (
                  <Link
                    to="/simplified-dashboard"
                    className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded"
                  >
                    Complete Module
                  </Link>
                )}
              </div>
            </div>
            
            {/* Premium Content Teaser */}
            {!isPremium && (
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mt-6 relative overflow-hidden`}>
                <div className="absolute -top-20 -right-20 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl"></div>
                
                <div className="relative z-10">
                  <div className="flex items-center mb-4">
                    <div className="p-2 rounded-lg mr-3 bg-yellow-500/20 text-yellow-500">
                      <FaCrown />
                    </div>
                    <h2 className="text-xl font-bold">Unlock Premium Content</h2>
                  </div>
                  
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                    Upgrade to premium to access advanced labs, interactive simulations, and certification preparation materials.
                  </p>
                  
                  <div className="flex space-x-4 mb-4">
                    <div className={`p-3 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex-1`}>
                      <div className="flex items-center mb-2">
                        <FaLock className="text-yellow-500 mr-2" />
                        <h3 className="font-medium">Advanced Labs</h3>
                      </div>
                      <p className="text-sm text-gray-500">Hands-on exercises in realistic environments</p>
                    </div>
                    
                    <div className={`p-3 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex-1`}>
                      <div className="flex items-center mb-2">
                        <FaLock className="text-yellow-500 mr-2" />
                        <h3 className="font-medium">Interactive Simulations</h3>
                      </div>
                      <p className="text-sm text-gray-500">Practice in real-world scenarios</p>
                    </div>
                  </div>
                  
                  <Link
                    to="/pricing"
                    className="inline-flex items-center py-2 px-4 bg-yellow-500 hover:bg-yellow-600 text-black rounded"
                  >
                    Upgrade to Premium <FaArrowRight className="ml-2" />
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LearningModulePage;
