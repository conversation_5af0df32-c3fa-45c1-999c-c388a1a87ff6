import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { signIn, signUp } from '../lib/auth';
import { FaLock, FaEnvelope, <PERSON>a<PERSON><PERSON>, FaUserCircle } from 'react-icons/fa';
import Navbar from '../components/Navbar';

function Login() {
  const navigate = useNavigate();
  const location = useLocation();
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    fullName: ''
  });

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setError(null);
  };



  const validateForm = () => {
    if (!formData.email || !formData.password) {
      setError('Please fill in all required fields');
      return false;
    }

    if (!isLogin) {
      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match');
        return false;
      }
      if (!formData.username) {
        setError('Username is required');
        return false;
      }
      if (formData.password.length < 6) {
        setError('Password must be at least 6 characters');
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (isLogin) {
        console.log("Attempting login with:", formData.email);
        const { session } = await signIn(formData.email, formData.password);

        if (session) {
          console.log("Login successful, redirecting...");

          // Force a delay to ensure session is properly set
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Redirect to appropriate page
          const redirectTo = location.state?.from || '/simplified-dashboard';
          navigate(redirectTo, { replace: true });
        }
      } else {
        console.log("Attempting registration with:", formData.email, formData.username);
        const { user } = await signUp(
          formData.email,
          formData.password,
          formData.username,
          formData.fullName
        );

        if (user) {
          console.log("Registration successful, attempting auto-login");
          setSuccess('Account created successfully! Logging you in...');

          // Auto-login after successful registration
          try {
            const { session } = await signIn(formData.email, formData.password);
            if (session) {
              console.log("Auto-login successful, redirecting...");
              navigate('/dashboard', { replace: true });
            }
          } catch (loginErr) {
            console.error("Auto-login failed:", loginErr);
            setIsLogin(true);
            setFormData({
              ...formData,
              password: '',
              confirmPassword: ''
            });
          }
        }
      }
    } catch (err) {
      console.error('Auth error:', err);
      setError(err.message || 'An error occurred during authentication');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-black flex flex-col items-center justify-center px-4 py-20">
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Background subtle pattern */}
          <div
            className="w-full h-full opacity-[0.02]"
            style={{
              backgroundImage: `
                linear-gradient(to right, rgb(45, 212, 191) 1px, transparent 1px),
                linear-gradient(to bottom, rgb(45, 212, 191) 1px, transparent 1px)
              `,
              backgroundSize: '100px 100px'
            }}
          />

          {/* Subtle accent elements */}
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="w-full max-w-md bg-black rounded-lg border border-gray-800 shadow-xl overflow-hidden relative z-10"
        >
          <div className="p-6 sm:p-8">
            <div className="text-center mb-6">
              <div className="inline-block mx-auto">
                <span className="font-bold text-3xl">
                  <span className="text-[#2DD4BF]">Cyber</span>
                  <span className="text-amber-500">XCerberus</span>
                </span>
              </div>
            </div>

            <h2 className="text-2xl sm:text-3xl font-bold text-center mb-8 bg-gradient-to-r from-[#2DD4BF] to-amber-500 text-transparent bg-clip-text">
              {isLogin ? 'Welcome Back' : 'Join XCerberus'}
            </h2>

            <div className="flex justify-center space-x-4 mb-8">
              <button
                onClick={() => {
                  setIsLogin(true);
                  setError(null);
                  setSuccess(null);
                }}
                className={`px-4 py-2 rounded-full transition-all duration-300 ${
                  isLogin
                    ? 'bg-[#2DD4BF] text-black font-bold'
                    : 'text-gray-500 hover:text-gray-300'
                }`}
              >
                Login
              </button>
              <button
                onClick={() => {
                  setIsLogin(false);
                  setError(null);
                  setSuccess(null);
                }}
                className={`px-4 py-2 rounded-full transition-all duration-300 ${
                  !isLogin
                    ? 'bg-[#2DD4BF] text-black font-bold'
                    : 'text-gray-500 hover:text-gray-300'
                }`}
              >
                Register
              </button>
            </div>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-3 rounded-lg bg-red-900/50 border border-red-500/50 text-red-400 text-sm text-center"
              >
                {error}
              </motion.div>
            )}

            {success && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-3 rounded-lg bg-green-900/50 border border-green-500/50 text-green-400 text-sm text-center"
              >
                {success}
              </motion.div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="email">
                  <FaEnvelope className="mr-2 text-[#2DD4BF]" />
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>

              {!isLogin && (
                <>
                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="username">
                      <FaUser className="mr-2 text-[#2DD4BF]" />
                      Username
                    </label>
                    <input
                      type="text"
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                      placeholder="Choose a username"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="fullName">
                      <FaUserCircle className="mr-2 text-[#2DD4BF]" />
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="fullName"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                      placeholder="Your full name"
                    />
                  </div>
                </>
              )}

              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="password">
                  <FaLock className="mr-2 text-[#2DD4BF]" />
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                  placeholder="••••••••"
                />
              </div>

              {!isLogin && (
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="confirmPassword">
                    <FaLock className="mr-2 text-[#2DD4BF]" />
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                    placeholder="••••••••"
                  />
                </div>
              )}

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-[#2DD4BF] text-black font-bold py-3 px-4 rounded-lg hover:bg-[#2DD4BF]/80 transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group"
              >
                <span className="relative z-10">
                  {loading ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </span>
                  ) : (
                    isLogin ? 'Sign In' : 'Create Account'
                  )}
                </span>
                <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-500" style={{transform: 'translateX(-100%)'}}></span>
              </button>
            </form>

            {isLogin && (
              <>
                <div className="mt-6">
                  <p className="text-center text-sm text-gray-500 mb-4">Test Accounts</p>
                  <div className="grid grid-cols-3 gap-2">
                    <button
                      type="button"
                      onClick={() => handleTestLogin('free')}
                      className="flex flex-col items-center justify-center p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      <FaUserAlt className="text-blue-400 text-xl mb-1" />
                      <span className="text-xs text-gray-300">Free</span>
                    </button>
                    <button
                      type="button"
                      onClick={() => handleTestLogin('premium')}
                      className="flex flex-col items-center justify-center p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      <FaUserShield className="text-green-400 text-xl mb-1" />
                      <span className="text-xs text-gray-300">Premium</span>
                    </button>
                    <button
                      type="button"
                      onClick={() => handleTestLogin('business')}
                      className="flex flex-col items-center justify-center p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      <FaUserTie className="text-purple-400 text-xl mb-1" />
                      <span className="text-xs text-gray-300">Business</span>
                    </button>
                  </div>
                </div>

                <p className="mt-6 text-center text-sm text-gray-500">
                  <a href="#" className="text-[#2DD4BF] hover:text-[#2DD4BF]/80 transition-colors">
                    Forgot your password?
                  </a>
                </p>
              </>
            )}
          </div>

          <div className="px-6 py-4 bg-gray-900 text-center text-sm border-t border-gray-800">
            <p className="text-gray-400">
              {isLogin ? "Don't have an account? " : "Already have an account? "}
              <button
                onClick={() => {
                  setIsLogin(!isLogin);
                  setError(null);
                }}
                className="text-[#2DD4BF] hover:text-[#2DD4BF]/80 font-medium transition-colors"
              >
                {isLogin ? 'Sign up' : 'Sign in'}
              </button>
            </p>
          </div>
        </motion.div>
      </div>
    </>
  );
}

export default Login;