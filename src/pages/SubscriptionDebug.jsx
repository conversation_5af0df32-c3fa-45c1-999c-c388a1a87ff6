import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { SUBSCRIPTION_LEVELS } from '../contexts/SubscriptionContext';
import { useSubscription } from '../contexts/SubscriptionContext';

/**
 * SubscriptionDebug Component
 * 
 * A utility page for testing different subscription levels.
 * This is for development purposes only.
 */
const SubscriptionDebug = () => {
  const navigate = useNavigate();
  const { subscriptionLevel, subscription } = useSubscription();
  const [currentLevel, setCurrentLevel] = useState(subscriptionLevel);
  const [message, setMessage] = useState('');

  // Set subscription level in localStorage
  const setSubscriptionLevel = (level) => {
    try {
      // Get current user subscription data
      const storedSubscriptionData = localStorage.getItem('user_subscription');
      let subscriptionData = storedSubscriptionData ? JSON.parse(storedSubscriptionData) : {};
      
      // Update the tier
      subscriptionData.tier = level;
      
      // Save back to localStorage
      localStorage.setItem('user_subscription', JSON.stringify(subscriptionData));
      
      setCurrentLevel(level);
      setMessage(`Subscription level set to ${level}. Please refresh the page or navigate to the dashboard.`);
    } catch (error) {
      console.error('Error setting subscription level:', error);
      setMessage(`Error: ${error.message}`);
    }
  };

  // Create a mock user if none exists
  const createMockUser = () => {
    try {
      // Create mock user data
      const mockUser = {
        id: 'mock-user-id',
        email: '<EMAIL>',
        user_metadata: {
          username: 'dev_user',
          full_name: 'Development User'
        }
      };
      
      // Create mock subscription
      const mockSubscription = {
        user_id: mockUser.id,
        tier: SUBSCRIPTION_LEVELS.PREMIUM,
        coins: 500,
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        is_active: true
      };
      
      // Save to localStorage
      localStorage.setItem('supabase.auth.user', JSON.stringify(mockUser));
      localStorage.setItem('supabase.auth.token', 'mock-token');
      localStorage.setItem('user_subscription', JSON.stringify(mockSubscription));
      
      setMessage('Mock user created with Premium subscription. Please refresh the page.');
    } catch (error) {
      console.error('Error creating mock user:', error);
      setMessage(`Error: ${error.message}`);
    }
  };

  // Go to dashboard
  const goToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-[#0B1120] text-white flex items-center justify-center">
      <div className="bg-[#1A1F35] border border-gray-800 rounded-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold mb-6">Subscription Debug</h1>
        
        <div className="mb-6">
          <p className="text-gray-400 mb-2">Current Subscription Level:</p>
          <p className="text-xl font-bold text-[#88cc14]">{currentLevel}</p>
        </div>
        
        <div className="space-y-4 mb-8">
          <button
            onClick={() => setSubscriptionLevel(SUBSCRIPTION_LEVELS.FREE)}
            className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
          >
            Set to Free
          </button>
          
          <button
            onClick={() => setSubscriptionLevel(SUBSCRIPTION_LEVELS.PREMIUM)}
            className="w-full px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg"
          >
            Set to Premium
          </button>
          
          <button
            onClick={() => setSubscriptionLevel(SUBSCRIPTION_LEVELS.BUSINESS)}
            className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg"
          >
            Set to Business
          </button>
        </div>
        
        <div className="mb-8">
          <button
            onClick={createMockUser}
            className="w-full px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg"
          >
            Create Mock User (Premium)
          </button>
        </div>
        
        <div className="mb-8">
          <button
            onClick={goToDashboard}
            className="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg"
          >
            Go to Dashboard
          </button>
        </div>
        
        {message && (
          <div className="p-4 bg-gray-800 rounded-lg text-center">
            {message}
          </div>
        )}
      </div>
    </div>
  );
};

export default SubscriptionDebug;
