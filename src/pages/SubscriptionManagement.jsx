import React, { useState, useEffect } from 'react';
import { Link, Navigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { FaCrown, FaCalendarAlt, FaHistory, FaExclamationTriangle, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';

const SubscriptionManagement = () => {
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const [subscription, setSubscription] = useState(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [upgradeLoading, setUpgradeLoading] = useState(false);
  const [upgradeSuccess, setUpgradeSuccess] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);

  // Fetch subscription data
  useEffect(() => {
    const fetchSubscriptionData = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Fetch active subscription
        const { data: subData, error: subError } = await supabase
          .from('subscription_tracking')
          .select(`
            id,
            plan:subscription_plans(id, name, price, currency, features),
            start_date,
            end_date,
            status,
            auto_renew,
            payment_id,
            payment_method
          `)
          .eq('user_id', user.id)
          .eq('status', 'active')
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (subError && subError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" error
          throw subError;
        }

        // Fetch subscription history
        const { data: historyData, error: historyError } = await supabase
          .from('subscription_tracking')
          .select(`
            id,
            plan:subscription_plans(id, name, price, currency),
            start_date,
            end_date,
            status,
            created_at
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(10);

        if (historyError) throw historyError;

        setSubscription(subData);
        setSubscriptionHistory(historyData);
      } catch (error) {
        console.error('Error fetching subscription data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptionData();
  }, [user]);

  // Handle subscription upgrade
  const handleUpgrade = async (planName) => {
    if (!user) return;

    try {
      setUpgradeLoading(true);
      setError(null);

      // Get plan details
      const { data: planData, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('name', planName)
        .single();

      if (planError) throw planError;

      // In a real app, this would integrate with a payment processor
      // For now, we'll just create a new subscription

      // Calculate end date (30 days from now)
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30);

      // Create new subscription
      const { data: subData, error: subError } = await supabase
        .from('subscription_tracking')
        .insert([{
          user_id: user.id,
          plan_id: planData.id,
          start_date: new Date(),
          end_date: endDate,
          status: 'active',
          auto_renew: true,
          payment_id: `sim_${Date.now()}`, // Simulated payment ID
          payment_method: 'credit_card'
        }])
        .select()
        .single();

      if (subError) throw subError;

      // Update user profile
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          subscription_tier: planName,
          subscription_start_date: new Date(),
          subscription_end_date: endDate,
          auto_renew: true,
          updated_at: new Date()
        })
        .eq('id', user.id);

      if (profileError) throw profileError;

      // Set any existing active subscriptions to cancelled
      if (subscription) {
        const { error: cancelError } = await supabase
          .from('subscription_tracking')
          .update({
            status: 'cancelled',
            updated_at: new Date()
          })
          .eq('id', subscription.id);

        if (cancelError) throw cancelError;
      }

      setUpgradeSuccess(true);

      // Reload page after 2 seconds
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      setError(error.message);
    } finally {
      setUpgradeLoading(false);
    }
  };

  // Handle subscription cancellation
  const handleCancel = async () => {
    if (!user || !subscription) return;

    try {
      setCancelLoading(true);
      setError(null);

      // Update subscription status
      const { error: subError } = await supabase
        .from('subscription_tracking')
        .update({
          status: 'cancelled',
          auto_renew: false,
          updated_at: new Date()
        })
        .eq('id', subscription.id);

      if (subError) throw subError;

      // Update user profile
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          auto_renew: false,
          updated_at: new Date()
        })
        .eq('id', user.id);

      if (profileError) throw profileError;

      // Update local state
      setSubscription({
        ...subscription,
        status: 'cancelled',
        auto_renew: false
      });

      // Show success message
      setUpgradeSuccess(true);

      // Reload page after 2 seconds
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      setError(error.message);
    } finally {
      setCancelLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (endDateString) => {
    if (!endDateString) return 0;
    
    const endDate = new Date(endDateString);
    const today = new Date();
    const diffTime = endDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 ? diffDays : 0;
  };

  // If user is not logged in, redirect to login
  if (!loading && !user) {
    return <Navigate to="/login" />;
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <h1 className="text-2xl font-bold mb-6">Subscription Management</h1>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <p>{error}</p>
          </div>
        )}

        {upgradeSuccess && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 flex items-center">
            <FaCheckCircle className="mr-2" />
            <p>Your subscription has been updated successfully!</p>
          </div>
        )}

        {/* Current Subscription */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6 mb-6`}>
          <h2 className="text-xl font-bold mb-4 flex items-center">
            <FaCrown className="mr-2 text-[#88cc14]" /> Current Subscription
          </h2>

          {loading ? (
            <p>Loading subscription data...</p>
          ) : subscription ? (
            <div>
              <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-bold">{subscription.plan.name.charAt(0).toUpperCase() + subscription.plan.name.slice(1)} Plan</h3>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {subscription.plan.price ? `${subscription.plan.currency || '₹'}${subscription.plan.price}/month` : 'Custom pricing'}
                  </p>
                </div>
                
                <div className="mt-4 md:mt-0">
                  <span className={`px-3 py-1 rounded-full text-sm ${
                    subscription.status === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : subscription.status === 'cancelled'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-300'} rounded-lg border p-4`}>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>Start Date</p>
                  <p className="font-medium">{formatDate(subscription.start_date)}</p>
                </div>
                
                <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-300'} rounded-lg border p-4`}>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>End Date</p>
                  <p className="font-medium">{formatDate(subscription.end_date)}</p>
                </div>
                
                <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-300'} rounded-lg border p-4`}>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>Days Remaining</p>
                  <p className="font-medium">{getDaysRemaining(subscription.end_date)} days</p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>Auto-Renewal</p>
                  <div className="flex items-center">
                    {subscription.auto_renew ? (
                      <>
                        <FaCheckCircle className="text-green-500 mr-2" />
                        <span>Enabled - Your subscription will renew on {formatDate(subscription.end_date)}</span>
                      </>
                    ) : (
                      <>
                        <FaTimesCircle className="text-red-500 mr-2" />
                        <span>Disabled - Your subscription will expire on {formatDate(subscription.end_date)}</span>
                      </>
                    )}
                  </div>
                </div>
                
                {subscription.status === 'active' && (
                  <button
                    onClick={handleCancel}
                    className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg"
                    disabled={cancelLoading}
                  >
                    {cancelLoading ? 'Processing...' : 'Cancel Subscription'}
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div>
              <p className="mb-4">You don't have an active subscription.</p>
              <Link to="/pricing" className="theme-button-primary px-4 py-2 rounded-lg inline-block">
                View Subscription Plans
              </Link>
            </div>
          )}
        </div>

        {/* Upgrade Options */}
        {profile?.subscription_tier !== 'business' && (
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6 mb-6`}>
            <h2 className="text-xl font-bold mb-4">Upgrade Your Subscription</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {profile?.subscription_tier !== 'premium' && (
                <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-300'} rounded-lg border p-6`}>
                  <h3 className="text-lg font-bold mb-2">Premium Plan</h3>
                  <p className="text-2xl font-bold mb-4">₹399<span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>/month</span></p>
                  
                  <ul className="mb-6 space-y-2">
                    <li className="flex items-center">
                      <FaCheckCircle className="text-green-500 mr-2" />
                      Full access to all learning modules
                    </li>
                    <li className="flex items-center">
                      <FaCheckCircle className="text-green-500 mr-2" />
                      15 premium challenges
                    </li>
                    <li className="flex items-center">
                      <FaCheckCircle className="text-green-500 mr-2" />
                      Complete access to all Start Hack features
                    </li>
                    <li className="flex items-center">
                      <FaCheckCircle className="text-green-500 mr-2" />
                      Community access with expert support
                    </li>
                    <li className="flex items-center">
                      <FaCheckCircle className="text-green-500 mr-2" />
                      Access to real Linux boxes for practice
                    </li>
                  </ul>
                  
                  <button
                    onClick={() => handleUpgrade('premium')}
                    className="theme-button-primary w-full py-2 rounded-lg"
                    disabled={upgradeLoading}
                  >
                    {upgradeLoading ? 'Processing...' : 'Upgrade to Premium'}
                  </button>
                </div>
              )}
              
              <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-300'} rounded-lg border p-6`}>
                <h3 className="text-lg font-bold mb-2">Business Plan</h3>
                <p className="text-2xl font-bold mb-4">Contact Sales</p>
                
                <ul className="mb-6 space-y-2">
                  <li className="flex items-center">
                    <FaCheckCircle className="text-green-500 mr-2" />
                    Everything in Premium
                  </li>
                  <li className="flex items-center">
                    <FaCheckCircle className="text-green-500 mr-2" />
                    Team formation capabilities
                  </li>
                  <li className="flex items-center">
                    <FaCheckCircle className="text-green-500 mr-2" />
                    Group participation in challenges
                  </li>
                  <li className="flex items-center">
                    <FaCheckCircle className="text-green-500 mr-2" />
                    Internal team messaging system
                  </li>
                  <li className="flex items-center">
                    <FaCheckCircle className="text-green-500 mr-2" />
                    Custom learning paths for teams
                  </li>
                </ul>
                
                <Link to="/contact" className={`${darkMode ? 'bg-[#1A1F35] hover:bg-[#252D4A] text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-800'} w-full py-2 rounded-lg block text-center`}>
                  Contact Sales
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Subscription History */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <h2 className="text-xl font-bold mb-4 flex items-center">
            <FaHistory className="mr-2" /> Subscription History
          </h2>
          
          {loading ? (
            <p>Loading subscription history...</p>
          ) : subscriptionHistory.length === 0 ? (
            <p>No subscription history found.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className={`${darkMode ? 'border-gray-800' : 'border-gray-200'} border-b`}>
                    <th className="px-4 py-2 text-left">Plan</th>
                    <th className="px-4 py-2 text-left">Start Date</th>
                    <th className="px-4 py-2 text-left">End Date</th>
                    <th className="px-4 py-2 text-left">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {subscriptionHistory.map(sub => (
                    <tr key={sub.id} className={`${darkMode ? 'border-gray-800 hover:bg-[#252D4A]' : 'border-gray-200 hover:bg-gray-100'} border-b`}>
                      <td className="px-4 py-2">{sub.plan.name.charAt(0).toUpperCase() + sub.plan.name.slice(1)}</td>
                      <td className="px-4 py-2">{formatDate(sub.start_date)}</td>
                      <td className="px-4 py-2">{formatDate(sub.end_date)}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          sub.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : sub.status === 'expired'
                              ? 'bg-red-100 text-red-800'
                              : sub.status === 'cancelled'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800'
                        }`}>
                          {sub.status.charAt(0).toUpperCase() + sub.status.slice(1)}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionManagement;
