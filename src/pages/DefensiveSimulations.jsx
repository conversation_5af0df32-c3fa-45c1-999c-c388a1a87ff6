import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  FaShieldAlt, FaSearch, FaFileAlt, FaExclamationTriangle, 
  FaChartLine, FaNetworkWired, FaServer, FaLock, FaChevronRight, 
  FaArrowRight, FaChevronDown, FaChevronUp, FaInfoCircle, FaGraduationCap
} from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import CyberForceSEO from '../components/common/CyberForceSEO';

const DefensiveSimulations = () => {
  const { darkMode } = useGlobalTheme();
  const [expandedFaq, setExpandedFaq] = useState(null);

  const simulationScenarios = [
    {
      id: 'soc-analyst',
      title: 'SOC Analyst Simulation',
      description: 'Experience a day in the life of a Security Operations Center analyst',
      icon: <FaChartLine className="text-blue-500" />,
      difficulty: 'Beginner to Intermediate',
      duration: '2-4 hours',
      skills: ['Alert Triage', 'Log Analysis', 'Incident Classification', 'Threat Detection'],
      color: 'from-blue-500/20 to-blue-600/5',
      borderColor: 'border-blue-500/20',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80'
    },
    {
      id: 'incident-response',
      title: 'Incident Response Scenario',
      description: 'Respond to and mitigate an active security incident',
      icon: <FaExclamationTriangle className="text-orange-500" />,
      difficulty: 'Intermediate',
      duration: '3-5 hours',
      skills: ['Incident Handling', 'Containment Strategies', 'Evidence Collection', 'Root Cause Analysis'],
      color: 'from-orange-500/20 to-orange-600/5',
      borderColor: 'border-orange-500/20',
      image: 'https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80'
    },
    {
      id: 'threat-hunting',
      title: 'Threat Hunting Exercise',
      description: 'Proactively search for threats that have evaded existing security controls',
      icon: <FaSearch className="text-purple-500" />,
      difficulty: 'Intermediate to Advanced',
      duration: '2-4 hours',
      skills: ['IOC Analysis', 'Behavioral Analysis', 'SIEM Query Building', 'Threat Intelligence'],
      color: 'from-purple-500/20 to-purple-600/5',
      borderColor: 'border-purple-500/20',
      image: 'https://images.unsplash.com/photo-1614064641938-3bbee52942c7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80'
    },
    {
      id: 'forensic-investigation',
      title: 'Digital Forensics Investigation',
      description: 'Analyze digital evidence to reconstruct a security incident',
      icon: <FaFileAlt className="text-green-500" />,
      difficulty: 'Intermediate to Advanced',
      duration: '3-6 hours',
      skills: ['Disk Forensics', 'Memory Analysis', 'Timeline Creation', 'Evidence Handling'],
      color: 'from-green-500/20 to-green-600/5',
      borderColor: 'border-green-500/20',
      image: 'https://images.unsplash.com/photo-1633265486064-086b219458ec?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80'
    }
  ];

  const faqs = [
    {
      question: "What experience level is required for defensive simulations?",
      answer: "Our defensive simulations are designed for various skill levels, from beginners to advanced practitioners. Each simulation clearly indicates the recommended experience level, and we provide guidance throughout the exercises."
    },
    {
      question: "What tools will I use in these simulations?",
      answer: "You'll work with industry-standard security tools including SIEM platforms (like Splunk and ELK Stack), EDR solutions, network monitoring tools, and forensic analysis software. All tools are provided in our browser-based lab environment."
    },
    {
      question: "Are the scenarios based on real-world incidents?",
      answer: "Yes, our defensive simulations are modeled after real-world cyber attacks and incidents, incorporating current threat actor tactics, techniques, and procedures (TTPs). We regularly update our scenarios to reflect emerging threats."
    },
    {
      question: "Will I receive feedback on my performance?",
      answer: "Absolutely. Each simulation includes detailed performance metrics and feedback on your actions. You'll receive recommendations for improvement and can compare your approach to expert solutions."
    }
  ];

  const toggleFaq = (index) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-20`}>
      {/* SEO */}
      <CyberForceSEO 
        title="Defensive Security Simulations"
        description="Develop blue team skills with CyberForce's defensive security simulations. Practice SOC operations, incident response, threat hunting, and digital forensics."
        keywords={['defensive security', 'blue team', 'SOC training', 'incident response', 'threat hunting', 'digital forensics']}
        canonicalUrl="https://cyberforce.om/simulations/defensive"
      />
      
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-b from-[#0B1120] to-[#1A1F35] py-16">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20"></div>
          <div className="grid grid-cols-10 grid-rows-10 h-full w-full">
            {Array.from({ length: 100 }).map((_, i) => (
              <div key={i} className="border-[0.5px] border-white/5"></div>
            ))}
          </div>
        </div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6">
              <FaShieldAlt className="text-blue-500" />
              <span className="text-sm font-medium text-blue-400">Blue Team Training</span>
            </div>
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
            >
              Defensive Security <span className="text-blue-500">Simulations</span>
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-xl text-gray-300 mb-8"
            >
              Develop blue team skills through realistic incident detection and response scenarios
            </motion.p>
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="flex flex-wrap justify-center gap-4"
            >
              <Link 
                to="/pricing" 
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium flex items-center gap-2 transition-colors"
              >
                <FaShieldAlt /> Start Training
              </Link>
              <Link 
                to="/simulations" 
                className="px-6 py-3 bg-gray-700 hover:bg-gray-800 text-white rounded-lg font-medium flex items-center gap-2 transition-colors"
              >
                View All Simulations
              </Link>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-16">
        {/* Introduction */}
        <div className="max-w-3xl mx-auto mb-16">
          <div className="flex items-center gap-4 mb-6">
            <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <FaShieldAlt className="text-blue-500 text-xl" />
            </div>
            <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              What Are Defensive Simulations?
            </h2>
          </div>
          <p className={`text-lg mb-6 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Defensive security simulations provide hands-on experience with blue team techniques in realistic environments. These exercises help security professionals develop skills in threat detection, incident response, and security operations to effectively protect organizations from cyber threats.
          </p>
          <div className={`p-4 rounded-lg border ${darkMode ? 'bg-blue-900/10 border-blue-900/30' : 'bg-blue-50 border-blue-200'} flex items-start gap-3 mb-8`}>
            <FaInfoCircle className="text-blue-500 text-xl flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-semibold text-blue-500 mb-1">Why Defensive Skills Matter</h3>
              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                While offensive security identifies vulnerabilities, defensive security ensures organizations can detect, respond to, and recover from attacks. Both skill sets are essential for a comprehensive security program, but most real-world security roles focus on defensive capabilities.
              </p>
            </div>
          </div>
        </div>

        {/* Available Simulations */}
        <div className="mb-16">
          <h2 className={`text-3xl font-bold mb-8 text-center ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Available Defensive Simulations
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {simulationScenarios.map((scenario) => (
              <div 
                key={scenario.id}
                className={`rounded-xl overflow-hidden border ${scenario.borderColor} transition-all duration-300 ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'}`}
              >
                <div className="h-48 overflow-hidden relative">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10"></div>
                  <img 
                    src={scenario.image} 
                    alt={scenario.title} 
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute bottom-0 left-0 p-4 z-20">
                    <h3 className="text-xl font-bold text-white mb-1">{scenario.title}</h3>
                    <div className="flex items-center gap-2">
                      <span className="px-2 py-1 bg-black/50 rounded text-xs text-white">
                        {scenario.difficulty}
                      </span>
                      <span className="px-2 py-1 bg-black/50 rounded text-xs text-white">
                        {scenario.duration}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <p className={`mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{scenario.description}</p>
                  <h4 className="font-semibold mb-2">Skills You'll Practice:</h4>
                  <div className="flex flex-wrap gap-2 mb-6">
                    {scenario.skills.map((skill, index) => (
                      <span 
                        key={index}
                        className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-700'}`}
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                  <button className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                    Start Simulation
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto mb-16">
          <h2 className={`text-3xl font-bold mb-8 text-center ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Frequently Asked Questions
          </h2>
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div 
                key={index}
                className={`rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} overflow-hidden`}
              >
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between"
                >
                  <span className="font-semibold">{faq.question}</span>
                  {expandedFaq === index ? <FaChevronUp /> : <FaChevronDown />}
                </button>
                {expandedFaq === index && (
                  <div className={`px-6 py-4 border-t ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className={`rounded-xl overflow-hidden relative ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10"></div>
          <div className="relative z-10 p-8 md:p-12 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Test Your Defensive Security Skills?</h2>
            <p className={`text-lg mb-8 max-w-2xl mx-auto ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Join CyberForce today and gain access to our full range of defensive security simulations and training resources.
            </p>
            <Link 
              to="/pricing" 
              className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium inline-flex items-center gap-2 transition-colors"
            >
              Start Training <FaArrowRight />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DefensiveSimulations;
