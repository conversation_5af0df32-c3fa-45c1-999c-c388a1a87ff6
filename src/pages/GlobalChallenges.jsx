import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaFlag, FaLock, FaInfoCircle, FaTrophy } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import GlobalChallengeList from '../components/challenges/GlobalChallengeList';
import UpgradeBanner from '../components/access/UpgradeBanner';
import { SUBSCRIPTION_TIERS } from '../config/subscriptionTiers';

/**
 * GlobalChallenges Page
 *
 * A unified challenges page that adapts based on user subscription level.
 * Provides appropriate access controls and upgrade prompts for different content tiers.
 */
const GlobalChallenges = () => {
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();
  const {
    subscriptionLevel,
    loading: subscriptionLoading,
    getRemainingContent
  } = useSubscription();

  const [remainingChallenges, setRemainingChallenges] = useState(0);
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);

  useEffect(() => {
    // Check if this is the first visit to the challenges page
    const hasVisitedChallenges = localStorage.getItem('has_visited_challenges');
    if (!hasVisitedChallenges) {
      setShowWelcomeModal(true);
      localStorage.setItem('has_visited_challenges', 'true');
    }

    // Get remaining challenges only if user is logged in
    if (!subscriptionLoading && user) {
      const { remaining } = getRemainingContent('challenges');
      setRemainingChallenges(remaining);
    }
  }, [subscriptionLoading, getRemainingContent, user]);

  // Welcome modal for first-time visitors
  const renderWelcomeModal = () => {
    if (!showWelcomeModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
              <FaFlag className="text-blue-600 dark:text-blue-300 text-2xl" />
            </div>
          </div>

          <h2 className="text-2xl font-bold text-center mb-4 text-gray-800 dark:text-white">
            Welcome to XCerberus Challenges
          </h2>

          <p className="text-gray-600 dark:text-gray-300 mb-6 text-center">
            Test your cybersecurity skills with our hands-on challenges across various categories and difficulty levels.
            {subscriptionLevel === SUBSCRIPTION_TIERS.FREE && (
              <span className="block mt-2 text-sm">
                You have access to <span className="font-bold">{remainingChallenges}</span> challenges with your free account.
              </span>
            )}
          </p>

          <div className="flex flex-col space-y-3">
            <button
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
              onClick={() => setShowWelcomeModal(false)}
            >
              Start Challenges
            </button>

            {subscriptionLevel === SUBSCRIPTION_TIERS.FREE && (
              <button
                className="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-lg transition-colors"
                onClick={() => navigate('/pricing')}
              >
                Explore Premium
              </button>
            )}
          </div>
        </motion.div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Page header */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <FaFlag className="mr-2 text-blue-600 dark:text-blue-400" />
                Cybersecurity Challenges
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Put your skills to the test with real-world security scenarios
              </p>
            </div>

            {subscriptionLevel === SUBSCRIPTION_TIERS.FREE && (
              <div className="hidden sm:block">
                <button
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  onClick={() => navigate('/pricing')}
                >
                  Upgrade for Full Access
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main content */}
      <div>
        <GlobalChallengeList />
      </div>

      {/* Welcome modal */}
      {renderWelcomeModal()}
    </div>
  );
};

export default GlobalChallenges;
