import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>ilter, FaStar, FaLock, FaCheck, FaPlay, FaBook, FaArrowRight } from 'react-icons/fa';
import SimpleUpgradeBanner from '../components/access/SimpleUpgradeBanner';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

// Sample module data
const MODULES = [
  {
    id: 1,
    title: 'Introduction to Cybersecurity',
    description: 'Learn the fundamentals of cybersecurity and understand the importance of security in today\'s digital world.',
    difficulty: 'Beginner',
    tags: ['Fundamentals', 'Security Basics'],
    duration: '2 hours',
    rating: 4.8,
    completed: false,
    progress: 0,
    free: true
  },
  {
    id: 2,
    title: 'Network Security Fundamentals',
    description: 'Understand how networks function and learn about common network vulnerabilities and how to protect against them.',
    difficulty: 'Beginner',
    tags: ['Network', 'Security'],
    duration: '3 hours',
    rating: 4.7,
    completed: false,
    progress: 0,
    free: true
  },
  {
    id: 3,
    title: 'Web Application Security',
    description: 'Learn about common web application vulnerabilities like XSS, CSRF, and SQL injection and how to prevent them.',
    difficulty: 'Intermediate',
    tags: ['Web Security', 'OWASP'],
    duration: '4 hours',
    rating: 4.9,
    completed: false,
    progress: 0,
    free: true
  },
  {
    id: 4,
    title: 'Ethical Hacking Methodology',
    description: 'Understand the ethical hacking process and learn about the methodologies used by security professionals.',
    difficulty: 'Intermediate',
    tags: ['Ethical Hacking', 'Methodology'],
    duration: '5 hours',
    rating: 4.6,
    completed: false,
    progress: 0,
    free: false
  },
  {
    id: 5,
    title: 'Cryptography Basics',
    description: 'Learn about encryption, hashing, and other cryptographic concepts essential for secure communications.',
    difficulty: 'Intermediate',
    tags: ['Cryptography', 'Encryption'],
    duration: '3 hours',
    rating: 4.5,
    completed: false,
    progress: 0,
    free: false
  },
  {
    id: 6,
    title: 'Malware Analysis',
    description: 'Learn how to analyze malware and understand its behavior to better protect systems and networks.',
    difficulty: 'Advanced',
    tags: ['Malware', 'Analysis'],
    duration: '6 hours',
    rating: 4.8,
    completed: false,
    progress: 0,
    free: false
  },
];

const SimpleLearnModules = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const { darkMode } = useGlobalTheme();

  // Filter modules based on search query and active filter
  const filteredModules = MODULES.filter(module => {
    const matchesSearch = module.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         module.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         module.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    if (activeFilter === 'all') return matchesSearch;
    if (activeFilter === 'beginner') return matchesSearch && module.difficulty === 'Beginner';
    if (activeFilter === 'intermediate') return matchesSearch && module.difficulty === 'Intermediate';
    if (activeFilter === 'advanced') return matchesSearch && module.difficulty === 'Advanced';

    return matchesSearch;
  });

  // Helper function to get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-500/20 text-green-500';
      case 'Intermediate':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'Advanced':
        return 'bg-red-500/20 text-red-500';
      default:
        return 'bg-blue-500/20 text-blue-500';
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold mb-2">Learning Modules</h1>
            <p className="text-gray-400">Master cybersecurity skills with our comprehensive learning modules</p>
          </div>

          <div className="mt-4 md:mt-0 flex items-center gap-2">
            <Link to="/learn/paths" className="theme-button-secondary px-4 py-2 rounded-md transition-colors">
              View Career Paths
            </Link>
            <Link to="/learn/certifications" className="theme-button-primary px-4 py-2 rounded-md font-medium transition-colors">
              Certifications
            </Link>
          </div>
        </div>

        {/* Upgrade Banner */}
        <SimpleUpgradeBanner
          message="Upgrade to Premium to unlock all 50 learning modules and accelerate your cybersecurity journey"
        />

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <FaSearch className="text-gray-500" />
              </div>
              <input
                type="text"
                className="theme-input rounded-lg block w-full pl-10 p-2.5 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]"
                placeholder="Search modules..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex items-center gap-2">
              <div className="theme-card rounded-lg p-2.5 flex items-center gap-2">
                <FaFilter className="text-gray-500" />
                <select
                  className="bg-transparent theme-text-primary focus:outline-none"
                  value={activeFilter}
                  onChange={(e) => setActiveFilter(e.target.value)}
                >
                  <option value="all" className="theme-bg-secondary theme-text-primary">All Levels</option>
                  <option value="beginner" className="theme-bg-secondary theme-text-primary">Beginner</option>
                  <option value="intermediate" className="theme-bg-secondary theme-text-primary">Intermediate</option>
                  <option value="advanced" className="theme-bg-secondary theme-text-primary">Advanced</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Modules Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredModules.map(module => (
            <div key={module.id} className="theme-card rounded-lg overflow-hidden border theme-border hover:border-[#88cc14]/50 transition-colors">
              {/* Module Header */}
              <div className="p-4 border-b theme-border">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-bold theme-text-primary">{module.title}</h3>
                  <span className={`px-2 py-0.5 rounded text-xs ${getDifficultyColor(module.difficulty)}`}>
                    {module.difficulty}
                  </span>
                </div>

                <p className="text-sm theme-text-secondary mb-4 line-clamp-2">{module.description}</p>

                <div className="flex flex-wrap gap-2 mb-4">
                  {module.tags.map((tag, index) => (
                    <span key={index} className="px-2 py-0.5 theme-bg-primary theme-text-secondary rounded text-xs">
                      {tag}
                    </span>
                  ))}
                </div>

                <div className="flex justify-between items-center text-sm theme-text-secondary">
                  <span>Duration: {module.duration}</span>
                  <div className="flex items-center gap-1">
                    <FaStar className="text-yellow-500 text-xs" />
                    <span>{module.rating}</span>
                  </div>
                </div>
              </div>

              {/* Module Footer */}
              <div className="p-4 theme-bg-tertiary">
                {module.free ? (
                  <Link
                    to={`/learn/modules/${module.id}`}
                    className="theme-button-primary flex items-center justify-center gap-2 font-medium py-2 rounded-lg transition-colors w-full"
                  >
                    <FaBook className="text-xs" />
                    Start Learning
                  </Link>
                ) : (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1 text-gray-400">
                      <FaLock className="text-xs" />
                      <span>Premium</span>
                    </div>
                    <Link to="/pricing" className="text-[#88cc14] hover:underline flex items-center gap-1">
                      Upgrade <FaArrowRight className="text-xs" />
                    </Link>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SimpleLearnModules;
