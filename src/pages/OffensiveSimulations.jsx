import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  FaUserSecret, FaLaptopCode, FaNetworkWired, FaCode, FaDatabase,
  FaServer, FaWifi, FaLock, FaChevronRight, FaArrowRight, FaChevronDown,
  FaChevronUp, FaExclamationTriangle, FaInfoCircle, FaGraduationCap
} from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import CyberForceSEO from '../components/common/CyberForceSEO';

const OffensiveSimulations = () => {
  const { darkMode } = useGlobalTheme();
  const [expandedFaq, setExpandedFaq] = useState(null);

  const simulationScenarios = [
    {
      id: 'web-app-pentest',
      title: 'Web Application Penetration Testing',
      description: 'Identify and exploit vulnerabilities in web applications',
      icon: <FaCode className="text-red-500" />,
      difficulty: 'Beginner to Advanced',
      duration: '1-3 hours',
      skills: ['OWASP Top 10', 'SQL Injection', 'XSS', 'CSRF', 'Authentication Bypass'],
      color: 'from-red-500/20 to-red-600/5',
      borderColor: 'border-red-500/20',
      image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80'
    },
    {
      id: 'network-pentest',
      title: 'Network Penetration Testing',
      description: 'Discover and exploit vulnerabilities in network infrastructure',
      icon: <FaNetworkWired className="text-orange-500" />,
      difficulty: 'Intermediate',
      duration: '2-4 hours',
      skills: ['Network Scanning', 'Service Enumeration', 'Vulnerability Assessment', 'Exploitation'],
      color: 'from-orange-500/20 to-orange-600/5',
      borderColor: 'border-orange-500/20',
      image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80'
    },
    {
      id: 'wireless-security',
      title: 'Wireless Security Assessment',
      description: 'Test the security of wireless networks and identify vulnerabilities',
      icon: <FaWifi className="text-blue-500" />,
      difficulty: 'Intermediate',
      duration: '1-2 hours',
      skills: ['WiFi Encryption', 'WPA/WPA2 Cracking', 'Evil Twin Attacks', 'Client-Side Attacks'],
      color: 'from-blue-500/20 to-blue-600/5',
      borderColor: 'border-blue-500/20',
      image: 'https://images.unsplash.com/photo-1562408590-e32931084e23?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80'
    },
    {
      id: 'social-engineering',
      title: 'Social Engineering Scenarios',
      description: 'Practice ethical social engineering techniques and defenses',
      icon: <FaUserSecret className="text-purple-500" />,
      difficulty: 'Beginner to Advanced',
      duration: '1-3 hours',
      skills: ['Phishing', 'Pretexting', 'Baiting', 'Psychological Manipulation'],
      color: 'from-purple-500/20 to-purple-600/5',
      borderColor: 'border-purple-500/20',
      image: 'https://images.unsplash.com/photo-1563237023-b1e970526dcb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1742&q=80'
    }
  ];

  const faqs = [
    {
      question: "What experience level is required for offensive simulations?",
      answer: "Our offensive simulations are designed for various skill levels, from beginners to advanced practitioners. Each simulation clearly indicates the recommended experience level, and we provide guidance throughout the exercises."
    },
    {
      question: "Are these simulations legal to perform?",
      answer: "Yes, all our simulations are conducted in controlled, isolated environments specifically designed for training purposes. You will never be targeting real systems outside of our training environment."
    },
    {
      question: "What tools will I need to participate?",
      answer: "Most simulations can be completed using our browser-based virtual lab environment. For more advanced scenarios, we provide a pre-configured virtual machine with all necessary tools installed."
    },
    {
      question: "How realistic are these offensive simulations?",
      answer: "Our simulations are designed to closely mimic real-world scenarios and vulnerabilities found in actual organizations. We regularly update our environments to reflect current attack techniques and vulnerabilities."
    }
  ];

  const toggleFaq = (index) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-20`}>
      {/* SEO */}
      <CyberForceSEO 
        title="Offensive Security Simulations"
        description="Practice ethical hacking and penetration testing with CyberForce's offensive security simulations. Learn web app testing, network exploitation, and more."
        keywords={['offensive security', 'penetration testing', 'ethical hacking', 'web app security', 'network security']}
        canonicalUrl="https://cyberforce.om/simulations/offensive"
      />
      
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-b from-[#0B1120] to-[#1A1F35] py-16">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 via-orange-500/20 to-yellow-500/20"></div>
          <div className="grid grid-cols-10 grid-rows-10 h-full w-full">
            {Array.from({ length: 100 }).map((_, i) => (
              <div key={i} className="border-[0.5px] border-white/5"></div>
            ))}
          </div>
        </div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-red-500/10 border border-red-500/20 mb-6">
              <FaExclamationTriangle className="text-red-500" />
              <span className="text-sm font-medium text-red-400">For Educational Purposes Only</span>
            </div>
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
            >
              Offensive Security <span className="text-red-500">Simulations</span>
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-xl text-gray-300 mb-8"
            >
              Develop ethical hacking skills through realistic penetration testing scenarios
            </motion.p>
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="flex flex-wrap justify-center gap-4"
            >
              <Link 
                to="/pricing" 
                className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium flex items-center gap-2 transition-colors"
              >
                <FaUserSecret /> Start Training
              </Link>
              <Link 
                to="/simulations" 
                className="px-6 py-3 bg-gray-700 hover:bg-gray-800 text-white rounded-lg font-medium flex items-center gap-2 transition-colors"
              >
                View All Simulations
              </Link>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-16">
        {/* Introduction */}
        <div className="max-w-3xl mx-auto mb-16">
          <div className="flex items-center gap-4 mb-6">
            <div className="w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center">
              <FaUserSecret className="text-red-500 text-xl" />
            </div>
            <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              What Are Offensive Simulations?
            </h2>
          </div>
          <p className={`text-lg mb-6 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Offensive security simulations provide hands-on experience with ethical hacking techniques in controlled environments. These exercises help security professionals identify vulnerabilities, understand attack vectors, and develop effective defensive strategies.
          </p>
          <div className={`p-4 rounded-lg border ${darkMode ? 'bg-red-900/10 border-red-900/30' : 'bg-red-50 border-red-200'} flex items-start gap-3 mb-8`}>
            <FaExclamationTriangle className="text-red-500 text-xl flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-semibold text-red-500 mb-1">Important Disclaimer</h3>
              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                These techniques should only be practiced in authorized environments. Unauthorized testing against systems you don't own is illegal and unethical. All CyberForce simulations are conducted in isolated, legal training environments.
              </p>
            </div>
          </div>
        </div>

        {/* Available Simulations */}
        <div className="mb-16">
          <h2 className={`text-3xl font-bold mb-8 text-center ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Available Offensive Simulations
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {simulationScenarios.map((scenario) => (
              <div 
                key={scenario.id}
                className={`rounded-xl overflow-hidden border ${scenario.borderColor} transition-all duration-300 ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'}`}
              >
                <div className="h-48 overflow-hidden relative">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10"></div>
                  <img 
                    src={scenario.image} 
                    alt={scenario.title} 
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute bottom-0 left-0 p-4 z-20">
                    <h3 className="text-xl font-bold text-white mb-1">{scenario.title}</h3>
                    <div className="flex items-center gap-2">
                      <span className="px-2 py-1 bg-black/50 rounded text-xs text-white">
                        {scenario.difficulty}
                      </span>
                      <span className="px-2 py-1 bg-black/50 rounded text-xs text-white">
                        {scenario.duration}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <p className={`mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{scenario.description}</p>
                  <h4 className="font-semibold mb-2">Skills You'll Practice:</h4>
                  <div className="flex flex-wrap gap-2 mb-6">
                    {scenario.skills.map((skill, index) => (
                      <span 
                        key={index}
                        className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-700'}`}
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                  <button className="w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                    Start Simulation
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto mb-16">
          <h2 className={`text-3xl font-bold mb-8 text-center ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Frequently Asked Questions
          </h2>
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div 
                key={index}
                className={`rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} overflow-hidden`}
              >
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between"
                >
                  <span className="font-semibold">{faq.question}</span>
                  {expandedFaq === index ? <FaChevronUp /> : <FaChevronDown />}
                </button>
                {expandedFaq === index && (
                  <div className={`px-6 py-4 border-t ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className={`rounded-xl overflow-hidden relative ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-orange-500/10"></div>
          <div className="relative z-10 p-8 md:p-12 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Test Your Offensive Security Skills?</h2>
            <p className={`text-lg mb-8 max-w-2xl mx-auto ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Join CyberForce today and gain access to our full range of offensive security simulations and training resources.
            </p>
            <Link 
              to="/pricing" 
              className="px-8 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium inline-flex items-center gap-2 transition-colors"
            >
              Start Training <FaArrowRight />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OffensiveSimulations;
