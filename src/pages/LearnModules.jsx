import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>ch, FaFilter, FaStar, FaLock, FaCheck, FaPlay, FaBook, FaArrowRight, FaCrown } from 'react-icons/fa';
import { useSubscription } from '../contexts/SubscriptionContext';
import UpgradeBanner from '../components/access/UpgradeBanner';
import LockedItem from '../components/access/LockedItem';

const LearnModules = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const { hasAccess, getAvailableCount, isPremium, isBusiness } = useSubscription();

  // Check if user has access to a module
  const canAccessModule = (moduleIndex) => {
    // Free users can access the first 3 modules
    const availableModules = getAvailableCount('learnModules');
    return moduleIndex < availableModules || isPremium || isBusiness;
  };

  // Mock data for learning modules
  const modules = [
    {
      id: 1,
      title: 'Web Requests',
      category: 'web',
      difficulty: 'Easy',
      progress: 100,
      completed: true,
      description: 'Learn how HTTP requests work and how to manipulate them.',
      duration: '2 hours',
      rating: 4.8,
      tags: ['HTTP', 'Web', 'Basics']
    },
    {
      id: 2,
      title: 'Introduction to Web Applications',
      category: 'web',
      difficulty: 'Fundamental',
      progress: 45,
      completed: false,
      description: 'Understand the architecture and components of modern web applications.',
      duration: '3 hours',
      rating: 4.6,
      tags: ['Web', 'Architecture', 'Basics']
    },
    {
      id: 3,
      title: 'Using Web Proxies',
      category: 'web',
      difficulty: 'Easy',
      progress: 0,
      completed: false,
      description: 'Learn how to use web proxies to intercept and modify HTTP traffic.',
      duration: '2.5 hours',
      rating: 4.7,
      tags: ['Proxy', 'Burp Suite', 'Web']
    },
    {
      id: 4,
      title: 'JavaScript Deobfuscation',
      category: 'web',
      difficulty: 'Medium',
      progress: 0,
      completed: false,
      description: 'Techniques to deobfuscate and analyze JavaScript code.',
      duration: '4 hours',
      rating: 4.5,
      tags: ['JavaScript', 'Web', 'Code Analysis']
    },
    {
      id: 5,
      title: 'SQL Injection Fundamentals',
      category: 'web',
      difficulty: 'Medium',
      progress: 0,
      completed: false,
      description: 'Learn how to identify and exploit SQL injection vulnerabilities.',
      duration: '5 hours',
      rating: 4.9,
      tags: ['SQL', 'Web', 'Injection']
    },
    {
      id: 6,
      title: 'Cross-Site Scripting (XSS)',
      category: 'web',
      difficulty: 'Medium',
      progress: 0,
      completed: false,
      description: 'Understanding and exploiting XSS vulnerabilities.',
      duration: '4 hours',
      rating: 4.7,
      tags: ['XSS', 'Web', 'JavaScript']
    },
    {
      id: 7,
      title: 'Network Traffic Analysis',
      category: 'network',
      difficulty: 'Medium',
      progress: 0,
      completed: false,
      description: 'Learn how to analyze network traffic using Wireshark and other tools.',
      duration: '6 hours',
      rating: 4.6,
      tags: ['Network', 'Wireshark', 'Analysis']
    },
    {
      id: 8,
      title: 'Active Directory Basics',
      category: 'windows',
      difficulty: 'Medium',
      progress: 0,
      completed: false,
      description: 'Understanding Active Directory structure and common misconfigurations.',
      duration: '5 hours',
      rating: 4.8,
      tags: ['Windows', 'Active Directory', 'Authentication']
    },
    {
      id: 9,
      title: 'Linux Privilege Escalation',
      category: 'linux',
      difficulty: 'Hard',
      progress: 0,
      completed: false,
      description: 'Techniques to escalate privileges on Linux systems.',
      duration: '7 hours',
      rating: 4.9,
      tags: ['Linux', 'Privilege Escalation', 'Post-Exploitation']
    },
    {
      id: 10,
      title: 'Cryptography Fundamentals',
      category: 'crypto',
      difficulty: 'Medium',
      progress: 0,
      completed: false,
      description: 'Learn the basics of cryptography and common encryption algorithms.',
      duration: '4 hours',
      rating: 4.5,
      tags: ['Cryptography', 'Encryption', 'Security']
    }
  ];

  // Filter categories
  const categories = [
    { id: 'all', label: 'All Modules' },
    { id: 'web', label: 'Web Security' },
    { id: 'network', label: 'Network Security' },
    { id: 'windows', label: 'Windows' },
    { id: 'linux', label: 'Linux' },
    { id: 'crypto', label: 'Cryptography' }
  ];

  // Filter modules based on search query and active filter
  const filteredModules = modules.filter(module => {
    const matchesCategory = activeFilter === 'all' || module.category === activeFilter;
    const matchesSearch = module.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          module.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          module.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    return matchesCategory && matchesSearch;
  });

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Fundamental':
        return 'bg-blue-500/20 text-blue-500';
      case 'Easy':
        return 'bg-green-500/20 text-green-500';
      case 'Medium':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'Hard':
        return 'bg-red-500/20 text-red-500';
      default:
        return 'bg-gray-500/20 text-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-[#0B1120] text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold mb-2">Learning Modules</h1>
            <p className="text-gray-400">Master cybersecurity skills with our comprehensive learning modules</p>
          </div>

          <div className="mt-4 md:mt-0 flex items-center gap-2">
            <Link to="/learn/paths" className="bg-[#1A1F35] hover:bg-[#252D4A] text-white px-4 py-2 rounded-md transition-colors">
              View Career Paths
            </Link>
            <Link to="/learn/certifications" className="bg-[#88cc14] hover:bg-[#7ab811] text-black px-4 py-2 rounded-md font-medium transition-colors">
              Certifications
            </Link>
          </div>
        </div>

        {/* Upgrade Banner - Show only for free users */}
        {!isPremium && !isBusiness && (
          <UpgradeBanner
            targetTier="premium"
            message="Upgrade to Premium to unlock all 50 learning modules and accelerate your cybersecurity journey"
            showClose={true}
          />
        )}

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
            <input
              type="text"
              placeholder="Search modules..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-[#1A1F35] border border-gray-800 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-[#88cc14]"
            />
          </div>

          <div className="flex overflow-x-auto pb-2 gap-2">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                className={`px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                  activeFilter === category.id
                    ? 'bg-[#88cc14] text-black font-medium'
                    : 'bg-[#1A1F35] text-gray-400 hover:bg-[#252D4A] hover:text-white'
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Modules Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredModules.map((module, index) => (
            canAccessModule(index) ? (
              <div key={module.id} className="bg-[#1A1F35] rounded-lg overflow-hidden border border-gray-800 hover:border-[#88cc14]/50 transition-colors">
                {/* Module Header */}
                <div className="p-4 border-b border-gray-800">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-bold">{module.title}</h3>
                    <span className={`px-2 py-0.5 rounded text-xs ${getDifficultyColor(module.difficulty)}`}>
                      {module.difficulty}
                    </span>
                  </div>

                  <p className="text-sm text-gray-400 mb-4 line-clamp-2">{module.description}</p>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {module.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-0.5 bg-[#0B1120] text-gray-400 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>

                  <div className="flex justify-between items-center text-sm text-gray-400">
                    <span>Duration: {module.duration}</span>
                    <div className="flex items-center gap-1">
                      <FaStar className="text-yellow-500 text-xs" />
                      <span>{module.rating}</span>
                    </div>
                  </div>
                </div>

                {/* Module Footer */}
                <div className="p-4 bg-[#0F172A]">
                  {module.completed ? (
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2 text-green-500">
                        <FaCheck />
                        <span>Completed</span>
                      </div>
                      <Link to={`/learn/modules/${module.id}`} className="text-[#88cc14] hover:underline flex items-center gap-1">
                        Review <FaArrowRight className="text-xs" />
                      </Link>
                    </div>
                  ) : module.progress > 0 ? (
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Progress</span>
                        <span className="text-[#88cc14]">{module.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-800 rounded-full h-2 mb-2">
                        <div
                          className="bg-[#88cc14] h-2 rounded-full"
                          style={{ width: `${module.progress}%` }}
                        ></div>
                      </div>
                      <Link
                        to={`/learn/modules/${module.id}`}
                        className="flex items-center justify-center gap-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium py-2 rounded-lg transition-colors w-full"
                      >
                        <FaPlay className="text-xs" />
                        Continue
                      </Link>
                    </div>
                  ) : (
                    <Link
                      to={`/learn/modules/${module.id}`}
                      className="flex items-center justify-center gap-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium py-2 rounded-lg transition-colors w-full"
                    >
                      <FaBook className="text-xs" />
                      Start Learning
                    </Link>
                  )}
                </div>
              </div>
            ) : (
              <LockedItem
                key={module.id}
                title={module.title}
                description={module.description}
                type="subscription"
                feature="learnModules"
                requiredTier="premium"
                to="/pricing"
              />
            )
          ))}
        </div>

        {filteredModules.length === 0 && (
          <div className="bg-[#1A1F35] rounded-lg p-8 text-center">
            <div className="text-gray-400 mb-2">No modules found matching your criteria</div>
            <button
              onClick={() => {
                setSearchQuery('');
                setActiveFilter('all');
              }}
              className="text-[#88cc14] hover:underline"
            >
              Clear filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default LearnModules;
