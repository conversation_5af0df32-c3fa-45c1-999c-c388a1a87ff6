import React from 'react';
import { Link } from 'react-router-dom';
import { FaRocket, FaLaptopCode, FaShieldAlt, FaTrophy, FaGraduationCap, FaChartLine, FaUsers, FaArrowRight } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

const ZilalLinuxLanding = () => {
  const { darkMode } = useGlobalTheme();

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Hero Section - with proper spacing for the fixed header */}
      <div className="pt-20">
        <section className={`py-16 md:py-24 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                  Master <span className="text-[#88cc14]">Cybersecurity</span> Skills Through Real-World Challenges
                </h1>
                <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-8`}>
                  Join the platform where security professionals learn, practice, and compete in realistic cybersecurity scenarios.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link to="/signup" className="theme-button-primary font-bold px-6 py-3 rounded-lg transition-colors text-center">
                    Get Started
                  </Link>
                  <Link to="/challenges" className={`${darkMode ? 'bg-[#1A1F35] hover:bg-[#252D4A] text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'} font-medium px-6 py-3 rounded-lg transition-colors text-center`}>
                    Explore Challenges
                  </Link>
                </div>
                <div className={`mt-8 flex items-center ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  <span className="mr-2">Join</span>
                  <span className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>10,000+</span>
                  <span className="ml-2">security professionals</span>
                </div>
              </div>

              <div className="relative flex justify-center py-12">
                {/* Enhanced Background */}
                <div className="absolute inset-0 overflow-hidden">
                  {/* Hexagon Grid Pattern */}
                  <div className={`absolute inset-0 ${darkMode ? 'opacity-10' : 'opacity-5'}`}>
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                      <pattern id="hexagons" width="50" height="43.4" patternUnits="userSpaceOnUse" patternTransform="scale(2)">
                        <polygon points="25,0 50,14.4 50,43.4 25,57.8 0,43.4 0,14.4" fill="none" stroke={darkMode ? '#3A5E8C' : '#88cc14'} strokeWidth="1"/>
                      </pattern>
                      <rect width="100%" height="100%" fill="url(#hexagons)" />
                    </svg>
                  </div>

                  {/* Subtle Glowing Elements */}
                  <div className={`absolute top-1/4 left-1/4 w-32 h-32 rounded-full ${darkMode ? 'bg-[#88cc14]/5' : 'bg-[#88cc14]/10'} filter blur-xl`}></div>
                  <div className={`absolute bottom-1/4 right-1/4 w-40 h-40 rounded-full ${darkMode ? 'bg-[#4A90E2]/5' : 'bg-[#88cc14]/10'} filter blur-xl`}></div>
                </div>

                {/* Single Challenge Card */}
                <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden w-full max-w-xl hover:border-[#88cc14]/50 transition-colors shadow-xl relative z-10`}>
                  {/* Card Header */}
                  <div className={`${darkMode ? 'bg-[#252D4A] border-gray-800' : 'bg-gray-50 border-gray-200'} p-4 border-b flex items-center`}>
                    <div className="w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center mr-3">
                      <FaShieldAlt className="text-[#88cc14]" />
                    </div>
                    <div>
                      <h3 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>Web Application Security</h3>
                      <div className="flex items-center mt-1">
                        <span className={`text-xs ${darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'} px-2 py-0.5 rounded`}>Beginner</span>
                        <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} ml-2`}>30 min</span>
                        <span className="text-xs text-[#88cc14] ml-2">500 pts</span>
                      </div>
                    </div>
                  </div>

                  {/* Card Body */}
                  <div className="p-6">
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>Learn to identify and exploit common web vulnerabilities in a safe environment. This challenge will test your knowledge of:</p>

                    <ul className={`list-disc list-inside ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6 space-y-2`}>
                      <li>Cross-Site Scripting (XSS)</li>
                      <li>SQL Injection</li>
                      <li>Cross-Site Request Forgery (CSRF)</li>
                      <li>Authentication Bypasses</li>
                    </ul>

                    <div className={`${darkMode ? 'bg-[#0B1120] border-gray-800' : 'bg-gray-50 border-gray-200'} border rounded-lg p-4 mb-6`}>
                      <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>Flag Format</h4>
                      <div className="font-mono text-sm text-[#88cc14]">ZilalLinux{'{found_the_vulnerability}'}</div>
                    </div>

                    <div className="flex justify-center">
                      <Link to="/signup" className="bg-[#88cc14] hover:bg-[#88cc14]/90 text-black px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center">
                        <FaRocket className="mr-2" /> Try This Challenge
                      </Link>
                    </div>
                  </div>

                  {/* Card Footer */}
                  <div className={`${darkMode ? 'bg-black/50 border-gray-800' : 'bg-gray-50 border-gray-200'} border-t p-4 flex justify-between items-center`}>
                    <div className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>1,245 completions</div>
                    <Link to="/challenges" className="text-[#88cc14] text-sm font-medium hover:underline flex items-center">
                      View All Challenges <FaArrowRight className="ml-1" />
                    </Link>
                  </div>
                </div>

                {/* Additional Decorative Elements */}
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-[#88cc14]/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 -right-8 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl"></div>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center bg-gradient-to-b from-transparent to-[#88cc14]/5 rounded-xl p-6">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                  <FaTrophy className="text-[#88cc14] text-xl" />
                </div>
                <div className="text-4xl font-bold text-[#88cc14] mb-2">150+</div>
                <div className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Challenges</div>
              </div>
              <div className="text-center bg-gradient-to-b from-transparent to-[#88cc14]/5 rounded-xl p-6">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                  <FaGraduationCap className="text-[#88cc14] text-xl" />
                </div>
                <div className="text-4xl font-bold text-[#88cc14] mb-2">50+</div>
                <div className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Learning Modules</div>
              </div>
              <div className="text-center bg-gradient-to-b from-transparent to-[#88cc14]/5 rounded-xl p-6">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                  <FaUsers className="text-[#88cc14] text-xl" />
                </div>
                <div className="text-4xl font-bold text-[#88cc14] mb-2">10K+</div>
                <div className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Users</div>
              </div>
              <div className="text-center bg-gradient-to-b from-transparent to-[#88cc14]/5 rounded-xl p-6">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                  <FaRocket className="text-[#88cc14] text-xl" />
                </div>
                <div className="text-4xl font-bold text-[#88cc14] mb-2">24/7</div>
                <div className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Support</div>
              </div>
            </div>
          </div>
        </section>

        {/* Why Cybersecurity Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                <FaChartLine className="text-[#88cc14] text-2xl" />
              </div>
              <h2 className={`text-3xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Why Learn Cybersecurity?</h2>
              <p className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
                In today's digital world, cybersecurity skills are more critical than ever before.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              <div className="bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-6 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden">
                <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-red-500/10 rounded-full blur-xl"></div>
                <div className="text-red-500 text-4xl font-bold mb-2">$4.35M</div>
                <h3 className="text-lg font-semibold mb-2 text-white">Average Cost of a Data Breach</h3>
                <p className="text-gray-400 text-sm">Organizations face increasing financial risks from cyber attacks, with costs rising 13% since 2020.</p>
              </div>

              <div className="bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-6 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden">
                <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-yellow-500/10 rounded-full blur-xl"></div>
                <div className="text-yellow-500 text-4xl font-bold mb-2">3.5M</div>
                <h3 className="text-lg font-semibold mb-2 text-white">Unfilled Security Positions</h3>
                <p className="text-gray-400 text-sm">The cybersecurity talent gap continues to grow, creating tremendous career opportunities.</p>
              </div>

              <div className="bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-6 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden">
                <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-blue-500/10 rounded-full blur-xl"></div>
                <div className="text-blue-500 text-4xl font-bold mb-2">+15%</div>
                <h3 className="text-lg font-semibold mb-2 text-white">Annual Salary Premium</h3>
                <p className="text-gray-400 text-sm">Cybersecurity professionals earn significantly more than their IT counterparts with similar experience.</p>
              </div>

              <div className="bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-6 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden">
                <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-green-500/10 rounded-full blur-xl"></div>
                <div className="text-green-500 text-4xl font-bold mb-2">300%</div>
                <h3 className="text-lg font-semibold mb-2 text-white">Increase in Attacks</h3>
                <p className="text-gray-400 text-sm">Cyber attacks have tripled since 2019, making security skills essential for all organizations.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                <FaShieldAlt className="text-[#88cc14] text-2xl" />
              </div>
              <h2 className={`text-3xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Why Choose Zilal Linux?</h2>
              <p className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
                Our platform offers a unique approach to cybersecurity education through practical, hands-on learning.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-8 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden group hover:border-[#88cc14]/50 transition-colors">
                <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl group-hover:bg-[#88cc14]/10 transition-colors"></div>
                <div className="w-16 h-16 bg-[#88cc14]/10 rounded-full flex items-center justify-center mb-6 group-hover:bg-[#88cc14]/20 transition-colors">
                  <FaLaptopCode className="text-[#88cc14] text-2xl" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-white">Hands-on Learning</h3>
                <p className="text-gray-400">
                  Learn by doing with interactive challenges that simulate real-world scenarios and vulnerabilities.
                </p>
              </div>

              <div className="bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-8 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden group hover:border-[#88cc14]/50 transition-colors">
                <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl group-hover:bg-[#88cc14]/10 transition-colors"></div>
                <div className="w-16 h-16 bg-[#88cc14]/10 rounded-full flex items-center justify-center mb-6 group-hover:bg-[#88cc14]/20 transition-colors">
                  <FaShieldAlt className="text-[#88cc14] text-2xl" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-white">Comprehensive Curriculum</h3>
                <p className="text-gray-400">
                  Access a wide range of topics from web security to network penetration testing and malware analysis.
                </p>
              </div>

              <div className="bg-gradient-to-br from-[#1A1F35] to-[#1A1F35]/70 p-8 rounded-xl border border-gray-800 backdrop-blur-sm relative overflow-hidden group hover:border-[#88cc14]/50 transition-colors">
                <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl group-hover:bg-[#88cc14]/10 transition-colors"></div>
                <div className="w-16 h-16 bg-[#88cc14]/10 rounded-full flex items-center justify-center mb-6 group-hover:bg-[#88cc14]/20 transition-colors">
                  <FaTrophy className="text-[#88cc14] text-2xl" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-white">Competitive Learning</h3>
                <p className="text-gray-400">
                  Compete with peers on our leaderboard and track your progress as you master new cybersecurity skills.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="bg-gradient-to-br from-[#1A1F35] to-[#0B1120] rounded-xl border border-gray-800 p-8 md:p-12 text-center relative overflow-hidden">
              {/* Background Elements */}
              <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                <div className="absolute -top-20 -right-20 w-64 h-64 bg-[#88cc14]/5 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-20 -left-20 w-64 h-64 bg-[#88cc14]/5 rounded-full blur-3xl"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full opacity-10">
                  <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                    <pattern id="hexagons-cta" width="50" height="43.4" patternUnits="userSpaceOnUse" patternTransform="scale(2)">
                      <polygon points="25,0 50,14.4 50,43.4 25,57.8 0,43.4 0,14.4" fill="none" stroke="#3A5E8C" strokeWidth="1"/>
                    </pattern>
                    <rect width="100%" height="100%" fill="url(#hexagons-cta)" />
                  </svg>
                </div>
              </div>

              {/* Content */}
              <div className="relative z-10">
                <div className="w-20 h-20 mx-auto mb-6 relative">
                  <div className="absolute inset-0 rounded-full bg-[#88cc14]/20 animate-ping opacity-50"></div>
                  <div className="absolute inset-0 rounded-full bg-[#88cc14]/10"></div>
                  <div className="absolute inset-3 rounded-full bg-[#0B1120] flex items-center justify-center">
                    <FaRocket className="text-[#88cc14] text-3xl" />
                  </div>
                </div>

                <h2 className="text-3xl font-bold mb-4 text-white">Ready to Start Your Cybersecurity Journey?</h2>
                <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
                  Join thousands of security professionals who are leveling up their skills on Zilal Linux.
                </p>

                <div className="flex flex-wrap justify-center gap-4">
                  <Link to="/signup" className="bg-[#88cc14] hover:bg-[#88cc14]/90 text-black font-bold px-8 py-3 rounded-lg transition-colors text-center">
                    Sign Up Now
                  </Link>
                  <Link to="/pricing" className="bg-[#1A1F35]/50 hover:bg-[#252D4A] text-white border border-gray-700 font-medium px-8 py-3 rounded-lg transition-colors text-center backdrop-blur-sm">
                    View Pricing
                  </Link>
                </div>

                <div className="mt-8 flex flex-wrap justify-center gap-8">
                  <Link to="/challenges" className="flex items-center text-[#88cc14] hover:text-[#88cc14]/80 transition-colors group">
                    <div className="w-10 h-10 rounded-full bg-[#88cc14]/10 flex items-center justify-center mr-3 group-hover:bg-[#88cc14]/20 transition-colors">
                      <FaTrophy className="text-[#88cc14]" />
                    </div>
                    <span>Explore Challenges</span>
                  </Link>
                  <Link to="/learn" className="flex items-center text-[#88cc14] hover:text-[#88cc14]/80 transition-colors group">
                    <div className="w-10 h-10 rounded-full bg-[#88cc14]/10 flex items-center justify-center mr-3 group-hover:bg-[#88cc14]/20 transition-colors">
                      <FaGraduationCap className="text-[#88cc14]" />
                    </div>
                    <span>Start Learning</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>


      </div>
    </div>
  );
};

export default ZilalLinuxLanding;
