import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { FaRocket, FaLaptopCode, FaShieldAlt, FaTrophy, FaGraduationCap, FaChartLine,
  FaUsers, FaArrowRight, FaServer, FaNetworkWired, FaLock, FaHeadset,
  FaGlobe, FaCode, FaBrain, FaUserShield, FaDatabase, FaCloudDownloadAlt } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import SecurityTrainingOptions from '../components/SecurityTrainingOptions';

const CyberForceLanding = () => {
  const { darkMode } = useGlobalTheme();
  const { t, language, changeLanguage } = useLanguage();
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>

      {/* Hero Section - with proper spacing for the fixed header */}
      <div className="pt-16">
        <section className={`py-16 md:py-24 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                {/* Logo at the top */}
                <div className="mb-8 flex justify-start">
                  <img
                    src="/images/CyberForce.png"
                    alt="CyberForce Logo"
                    className="h-16 md:h-20"
                  />
                </div>

                <div className="flex items-center mb-6">
                  <div className="h-1 w-12 bg-[#4A5CBA] mr-4"></div>
                  <span className="text-[#4A5CBA] font-semibold uppercase tracking-wider text-sm">Department of Energy</span>
                </div>
                <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                  {t('welcome')}
                </h1>
                <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-8`}>
                  {t('subtitle')}
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link
                    to="/signup"
                    className="bg-[#4A5CBA] hover:bg-[#3A4CAA] text-white font-bold px-6 py-3 rounded-lg transition-colors text-center shadow-lg hover:shadow-cyberforce"
                  >
                    {t('getStarted')}
                  </Link>
                  <Link
                    to="/challenges"
                    className={`border-2 border-[#F5B93F] ${darkMode ? 'text-[#F5B93F]' : 'text-[#F5B93F]'} font-medium px-6 py-3 rounded-lg hover:bg-[#F5B93F]/10 transition-colors text-center shadow-md`}
                  >
                    {t('exploreChallenge')}
                  </Link>
                </div>
                <div className={`mt-8 flex items-center ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  <span className="mr-2">{t('join')}</span>
                  <span className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>5,000+</span>
                  <span className="ml-2">{t('students')}</span>
                </div>
              </div>

              <div className="relative flex justify-center py-8 order-1 lg:order-2">
                {/* Enhanced Background */}
                <div className="absolute inset-0 overflow-hidden">
                  {/* Hexagon Grid Pattern */}
                  <div className={`absolute inset-0 ${darkMode ? 'opacity-10' : 'opacity-5'}`}>
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                      <pattern id="hexagons" width="50" height="43.4" patternUnits="userSpaceOnUse" patternTransform="scale(2)">
                        <polygon points="25,0 50,14.4 50,43.4 25,57.8 0,43.4 0,14.4" fill="none" stroke={darkMode ? '#3A5E8C' : '#0066cc'} strokeWidth="1"/>
                      </pattern>
                      <rect width="100%" height="100%" fill="url(#hexagons)" />
                    </svg>
                  </div>
                </div>

                {/* Hero Image */}
                <div className="relative z-10 flex flex-col justify-center items-center transform transition-all duration-700">
                  {/* Main Image */}
                  <div className="relative mb-8">
                    <div className="absolute inset-0 bg-[#0066cc]/10 rounded-2xl blur-xl animate-pulse-slow"></div>
                    <img
                      src="https://cyberforce.om/wp-content/uploads/2023/07/0H9A0030-min.jpg"
                      alt="CyberForce Training"
                      className="max-w-full h-auto rounded-2xl shadow-cyberforce-strong relative z-10"
                      style={{ maxHeight: '320px', maxWidth: '100%' }}
                    />

                    {/* Overlay Text */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 rounded-b-2xl">
                      <h3 className="text-white text-xl font-bold">Stay ahead of cyber threats</h3>
                      <p className="text-white/80 text-sm">with CyberForce Training</p>
                    </div>
                  </div>

                  {/* Features List */}
                  <div className={`${darkMode ? 'bg-[#1A1F35]/70' : 'bg-white'} p-4 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg w-full max-w-md`}>
                    <ul className="space-y-2">
                      <li className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-[#0066cc]/20 flex items-center justify-center mr-3">
                          <span className="text-[#0066cc] text-xs">✓</span>
                        </div>
                        <span>Innovation-driven training</span>
                      </li>
                      <li className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-[#0066cc]/20 flex items-center justify-center mr-3">
                          <span className="text-[#0066cc] text-xs">✓</span>
                        </div>
                        <span>Ethical excellence</span>
                      </li>
                      <li className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-[#0066cc]/20 flex items-center justify-center mr-3">
                          <span className="text-[#0066cc] text-xs">✓</span>
                        </div>
                        <span>Community collaboration</span>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Additional Decorative Elements */}
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-[#0066cc]/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 -right-8 w-40 h-40 bg-[#0066cc]/5 rounded-full blur-xl"></div>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-8">
              <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>Our Impact</h2>
              <p className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
                Empowering cybersecurity professionals with cutting-edge training and real-world scenarios
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className={`text-center ${darkMode ? 'bg-[#1A1F35]/50' : 'bg-white'} rounded-xl p-6 shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 border ${darkMode ? 'border-gray-800' : 'border-gray-100'}`}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[#4A5CBA]/10 flex items-center justify-center">
                  <FaTrophy className="text-[#4A5CBA] text-2xl" />
                </div>
                <div className="text-4xl font-bold text-[#4A5CBA] mb-2">100+</div>
                <div className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} font-medium`}>{t('challenges')}</div>
              </div>

              <div className={`text-center ${darkMode ? 'bg-[#1A1F35]/50' : 'bg-white'} rounded-xl p-6 shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 border ${darkMode ? 'border-gray-800' : 'border-gray-100'}`}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[#4A5CBA]/10 flex items-center justify-center">
                  <FaGraduationCap className="text-[#4A5CBA] text-2xl" />
                </div>
                <div className="text-4xl font-bold text-[#4A5CBA] mb-2">40+</div>
                <div className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} font-medium`}>{t('learningModules')}</div>
              </div>

              <div className={`text-center ${darkMode ? 'bg-[#1A1F35]/50' : 'bg-white'} rounded-xl p-6 shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 border ${darkMode ? 'border-gray-800' : 'border-gray-100'}`}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[#F5B93F]/10 flex items-center justify-center">
                  <FaLock className="text-[#F5B93F] text-2xl" />
                </div>
                <div className="text-4xl font-bold text-[#F5B93F] mb-2">95%</div>
                <div className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} font-medium`}>Success Rate</div>
              </div>

              <div className={`text-center ${darkMode ? 'bg-[#1A1F35]/50' : 'bg-white'} rounded-xl p-6 shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 border ${darkMode ? 'border-gray-800' : 'border-gray-100'}`}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[#F5B93F]/10 flex items-center justify-center">
                  <FaNetworkWired className="text-[#F5B93F] text-2xl" />
                </div>
                <div className="text-4xl font-bold text-[#F5B93F] mb-2">12+</div>
                <div className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} font-medium`}>Industry Partners</div>
              </div>
            </div>
          </div>
        </section>

        {/* Live Cyber Threat Map Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="flex items-center justify-center mb-4">
                <div className="h-1 w-12 bg-[#4A5CBA] mr-4"></div>
                <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Live Cyber Threat Intelligence</h2>
                <div className="h-1 w-12 bg-[#4A5CBA] ml-4"></div>
              </div>
              <p className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
                Monitor real-time cyber threats and attacks happening around the world
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Threat Map */}
              <div className="lg:col-span-2">
                <div className={`${darkMode ? 'bg-[#1A1F35]/70' : 'bg-white'} p-6 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg relative overflow-hidden h-[400px]`}>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <iframe
                      src="https://cybermap.kaspersky.com/en/widget/dynamic/dark"
                      title="Live Cyber Threat Map"
                      className="w-full h-full border-0"
                      style={{ pointerEvents: 'none' }}
                    ></iframe>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                    <p className="text-white text-sm">Data provided by Kaspersky Cyberthreat Map</p>
                  </div>
                </div>
              </div>

              {/* Threat Stats */}
              <div className="lg:col-span-1">
                <div className={`${darkMode ? 'bg-[#1A1F35]/70' : 'bg-white'} p-6 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg h-full`}>
                  <h3 className="text-xl font-bold mb-6 text-[#F5B93F]">Latest Threat Intelligence</h3>

                  <div className="space-y-6">
                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-red-500/10 flex items-center justify-center mr-3 mt-1">
                        <FaShieldAlt className="text-red-500" />
                      </div>
                      <div>
                        <h4 className="font-bold mb-1">Ransomware Attacks</h4>
                        <div className="flex items-center">
                          <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                            <div className="h-full bg-red-500 rounded-full" style={{ width: '78%' }}></div>
                          </div>
                          <span className="ml-2 text-sm font-medium">78%</span>
                        </div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>Increase in the last 30 days</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-yellow-500/10 flex items-center justify-center mr-3 mt-1">
                        <FaDatabase className="text-yellow-500" />
                      </div>
                      <div>
                        <h4 className="font-bold mb-1">Data Breaches</h4>
                        <div className="flex items-center">
                          <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                            <div className="h-full bg-yellow-500 rounded-full" style={{ width: '65%' }}></div>
                          </div>
                          <span className="ml-2 text-sm font-medium">65%</span>
                        </div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>Of breaches involve stolen credentials</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-green-500/10 flex items-center justify-center mr-3 mt-1">
                        <FaUserShield className="text-green-500" />
                      </div>
                      <div>
                        <h4 className="font-bold mb-1">Security Posture</h4>
                        <div className="flex items-center">
                          <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                            <div className="h-full bg-green-500 rounded-full" style={{ width: '42%' }}></div>
                          </div>
                          <span className="ml-2 text-sm font-medium">42%</span>
                        </div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>Organizations with mature security programs</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <Link to="/threat-intelligence" className="text-[#4A5CBA] hover:text-[#3A4CAA] font-medium flex items-center group">
                      View detailed threat intelligence
                      <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>



        {/* Why Participate in CyberForce Section - Enhanced */}
        <section className={`py-20 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} relative overflow-hidden`}>
          {/* Background Elements */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-br from-[#4A5CBA]/10 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-tr from-[#F5B93F]/10 to-transparent rounded-full blur-3xl"></div>

            {/* Animated Particles */}
            <div className="absolute inset-0">
              <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-[#4A5CBA]/40 rounded-full animate-particle1"></div>
              <div className="absolute top-3/4 left-1/3 w-2 h-2 bg-[#F5B93F]/40 rounded-full animate-particle2"></div>
              <div className="absolute top-1/2 right-1/4 w-2 h-2 bg-[#4A5CBA]/40 rounded-full animate-particle3"></div>
              <div className="absolute top-1/3 right-1/3 w-2 h-2 bg-[#F5B93F]/40 rounded-full animate-particle4"></div>
              <div className="absolute bottom-1/4 right-1/2 w-2 h-2 bg-[#4A5CBA]/40 rounded-full animate-particle5"></div>
            </div>
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="text-center mb-16">
              <div className="inline-block bg-[#4A5CBA]/10 px-4 py-1 rounded-full mb-4 animate-fade-in">
                <span className="text-[#4A5CBA] font-semibold text-sm tracking-wider uppercase flex items-center">
                  <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 6V12L16 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Limited Time Enrollment
                </span>
              </div>

              <h2 className={`text-3xl md:text-4xl font-bold mb-6 ${darkMode ? 'text-white' : 'text-gray-900'} animate-fade-in-up`}>
                Why Participate in <span className="text-[#4A5CBA]">Cyber</span><span className="text-[#F5B93F]">Force</span>?
              </h2>

              <div className="w-24 h-1 bg-gradient-to-r from-[#4A5CBA] to-[#F5B93F] mx-auto mb-6 rounded-full animate-fade-in animation-delay-200"></div>

              <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} max-w-3xl mx-auto animate-fade-in-up animation-delay-300`}>
                Unlock exceptional opportunities to advance your cybersecurity career through hands-on learning and real-world challenges
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {/* Card 1 - Real-world Scenarios */}
              <div className={`${darkMode ? 'bg-gradient-to-br from-[#1A1F35]/90 to-[#1A1F35]/70' : 'bg-white'} p-8 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover-lift relative overflow-hidden group animate-fade-in-up animation-delay-100`}>
                {/* Decorative Elements */}
                <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-red-500/10 rounded-full blur-xl opacity-70 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -top-10 -left-10 w-24 h-24 bg-red-500/5 rounded-full blur-xl"></div>

                {/* Icon with animated background */}
                <div className="relative mb-6 inline-block">
                  <div className="absolute inset-0 bg-red-500/20 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-red-500/20 to-red-600/20 flex items-center justify-center relative z-10 border border-red-500/30">
                    <FaServer className="text-red-500 text-2xl group-hover:scale-110 transition-transform duration-300" />
                  </div>
                </div>

                {/* Content */}
                <div className="text-red-500 text-3xl font-bold mb-3">{t('real')}</div>
                <h3 className="text-lg font-semibold mb-3 text-[#4A5CBA] group-hover:text-[#F5B93F] transition-colors duration-300">{t('industry')}</h3>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Practice defending critical infrastructure against sophisticated cyber attacks in realistic environments designed by industry experts.
                </p>

                {/* Animated border on hover */}
                <div className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-red-500 to-red-400 group-hover:w-full transition-all duration-700"></div>
              </div>

              {/* Card 2 - Career Opportunities */}
              <div className={`${darkMode ? 'bg-gradient-to-br from-[#1A1F35]/90 to-[#1A1F35]/70' : 'bg-white'} p-8 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover-lift relative overflow-hidden group animate-fade-in-up animation-delay-200`}>
                {/* Decorative Elements */}
                <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-yellow-500/10 rounded-full blur-xl opacity-70 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -top-10 -left-10 w-24 h-24 bg-yellow-500/5 rounded-full blur-xl"></div>

                {/* Icon with animated background */}
                <div className="relative mb-6 inline-block">
                  <div className="absolute inset-0 bg-yellow-500/20 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-yellow-500/20 to-yellow-600/20 flex items-center justify-center relative z-10 border border-yellow-500/30">
                    <FaUsers className="text-yellow-500 text-2xl group-hover:scale-110 transition-transform duration-300" />
                  </div>
                </div>

                {/* Content */}
                <div className="text-yellow-500 text-3xl font-bold mb-3">{t('career')}</div>
                <h3 className="text-lg font-semibold mb-3 text-[#4A5CBA] group-hover:text-[#F5B93F] transition-colors duration-300">{t('careerOpportunities')}</h3>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Connect with leading employers in the cybersecurity industry and showcase your skills to potential recruiters and hiring managers.
                </p>

                {/* Animated border on hover */}
                <div className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-yellow-500 to-yellow-400 group-hover:w-full transition-all duration-700"></div>
              </div>

              {/* Card 3 - Team Collaboration */}
              <div className={`${darkMode ? 'bg-gradient-to-br from-[#1A1F35]/90 to-[#1A1F35]/70' : 'bg-white'} p-8 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover-lift relative overflow-hidden group animate-fade-in-up animation-delay-300`}>
                {/* Decorative Elements */}
                <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-blue-500/10 rounded-full blur-xl opacity-70 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -top-10 -left-10 w-24 h-24 bg-blue-500/5 rounded-full blur-xl"></div>

                {/* Icon with animated background */}
                <div className="relative mb-6 inline-block">
                  <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500/20 to-blue-600/20 flex items-center justify-center relative z-10 border border-blue-500/30">
                    <FaUsers className="text-blue-500 text-2xl group-hover:scale-110 transition-transform duration-300" />
                  </div>
                </div>

                {/* Content */}
                <div className="text-blue-500 text-3xl font-bold mb-3">{t('team')}</div>
                <h3 className="text-lg font-semibold mb-3 text-[#4A5CBA] group-hover:text-[#F5B93F] transition-colors duration-300">{t('collaboration')}</h3>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Work with peers from diverse backgrounds to solve complex security challenges, building valuable teamwork and communication skills.
                </p>

                {/* Animated border on hover */}
                <div className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-blue-500 to-blue-400 group-hover:w-full transition-all duration-700"></div>
              </div>

              {/* Card 4 - Hands-on Experience */}
              <div className={`${darkMode ? 'bg-gradient-to-br from-[#1A1F35]/90 to-[#1A1F35]/70' : 'bg-white'} p-8 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover-lift relative overflow-hidden group animate-fade-in-up animation-delay-400`}>
                {/* Decorative Elements */}
                <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-green-500/10 rounded-full blur-xl opacity-70 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -top-10 -left-10 w-24 h-24 bg-green-500/5 rounded-full blur-xl"></div>

                {/* Icon with animated background */}
                <div className="relative mb-6 inline-block">
                  <div className="absolute inset-0 bg-green-500/20 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-green-500/20 to-green-600/20 flex items-center justify-center relative z-10 border border-green-500/30">
                    <FaLaptopCode className="text-green-500 text-2xl group-hover:scale-110 transition-transform duration-300" />
                  </div>
                </div>

                {/* Content */}
                <div className="text-green-500 text-3xl font-bold mb-3">{t('handson')}</div>
                <h3 className="text-lg font-semibold mb-3 text-[#4A5CBA] group-hover:text-[#F5B93F] transition-colors duration-300">{t('experience')}</h3>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Gain practical experience with industry-standard tools and techniques that will prepare you for real-world cybersecurity roles.
                </p>

                {/* Animated border on hover */}
                <div className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-green-500 to-green-400 group-hover:w-full transition-all duration-700"></div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center animate-fade-in animation-delay-600">
              <Link
                to="/signup"
                className="bg-gradient-to-r from-[#4A5CBA] to-[#3A4CAA] text-white font-bold px-8 py-4 rounded-lg transition-all duration-300 inline-flex items-center shadow-lg hover:shadow-[#4A5CBA]/50 transform hover:-translate-y-1 group"
              >
                <span className="flex items-center justify-center">
                  Start Your Cybersecurity Journey
                  <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
              </Link>

              <p className="mt-6 text-sm text-gray-500 dark:text-gray-400 animate-fade-in animation-delay-700">
                Join thousands of students already enrolled in our program
              </p>
            </div>
          </div>
        </section>

        {/* About CyberForce Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="flex items-center justify-center mb-4">
                <div className="h-1 w-12 bg-[#0066cc] mr-4"></div>
                <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>About CyberForce</h2>
                <div className="h-1 w-12 bg-[#0066cc] ml-4"></div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-bold mb-6 text-[#0066cc]">Cyber Force</h3>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-6 text-lg leading-relaxed`}>
                  is your premier destination for cyber security capacity building and training. We are a cutting-edge company dedicated to equipping individuals and organizations with the knowledge and skills necessary to navigate the complex world of cyber security. Our focus lies in providing comprehensive training programs that empower individuals to tackle the ever-evolving cyber threats of today's digital landscape.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-[#0066cc]/10 flex items-center justify-center mr-3 mt-1">
                      <span className="text-[#0066cc] text-xs">✓</span>
                    </div>
                    <div>
                      <h4 className="font-bold mb-1">Innovation-driven</h4>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Cutting-edge training methods</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-[#0066cc]/10 flex items-center justify-center mr-3 mt-1">
                      <span className="text-[#0066cc] text-xs">✓</span>
                    </div>
                    <div>
                      <h4 className="font-bold mb-1">Empowerment</h4>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Building skills for success</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-[#0066cc]/10 flex items-center justify-center mr-3 mt-1">
                      <span className="text-[#0066cc] text-xs">✓</span>
                    </div>
                    <div>
                      <h4 className="font-bold mb-1">Ethical Excellence</h4>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Promoting responsible security</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-[#0066cc]/10 flex items-center justify-center mr-3 mt-1">
                      <span className="text-[#0066cc] text-xs">✓</span>
                    </div>
                    <div>
                      <h4 className="font-bold mb-1">Community Collaboration</h4>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Learning together, growing together</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8">
                  <Link
                    to="/courses"
                    className="bg-[#0066cc] hover:bg-[#0055aa] text-white font-bold px-6 py-3 rounded-lg transition-all duration-300 inline-flex items-center shadow-lg hover:shadow-cyberforce transform hover:-translate-y-1"
                  >
                    View All Courses <FaArrowRight className="ml-2" />
                  </Link>
                </div>
              </div>

              <div className="relative">
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-[#0066cc]/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 -right-8 w-40 h-40 bg-[#0066cc]/5 rounded-full blur-xl"></div>

                <div className="relative z-10 rounded-xl overflow-hidden shadow-xl">
                  <img
                    src="https://cyberforce.om/wp-content/uploads/2023/07/0H9A0045-scaled.jpg"
                    alt="CyberForce Training"
                    className="w-full h-auto"
                  />
                  <div className={`absolute bottom-0 left-0 right-0 ${darkMode ? 'bg-[#0B1120]/80' : 'bg-white/80'} p-6 backdrop-blur-sm`}>
                    <h3 className="text-xl font-bold mb-2 text-[#0066cc]">Our Vision</h3>
                    <p className={`${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                      To be a leading cybersecurity training and development hub, empowering individuals and organizations to protect against evolving cyber threats.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Training Paths Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="flex items-center justify-center mb-4">
                <div className="h-1 w-12 bg-[#4A5CBA] mr-4"></div>
                <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Specialized Training Paths</h2>
                <div className="h-1 w-12 bg-[#4A5CBA] ml-4"></div>
              </div>
              <p className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
                Accelerate your cybersecurity career with our industry-recognized training paths
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Defensive Security Path */}
              <div className={`${darkMode ? 'bg-[#1A1F35]/70' : 'bg-white'} rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 overflow-hidden group`}>
                <div className="h-3 bg-blue-500 w-full"></div>
                <div className="p-8">
                  <div className="w-16 h-16 bg-blue-500/10 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                    <FaShieldAlt className="text-blue-500 text-2xl" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-blue-500">Defensive Security</h3>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>
                    Master the art of protecting systems and networks from cyber threats
                  </p>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-blue-500/10 flex items-center justify-center mr-2">
                        <span className="text-blue-500 text-xs">✓</span>
                      </div>
                      <span className="text-sm">Security Operations</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-blue-500/10 flex items-center justify-center mr-2">
                        <span className="text-blue-500 text-xs">✓</span>
                      </div>
                      <span className="text-sm">Incident Response</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-blue-500/10 flex items-center justify-center mr-2">
                        <span className="text-blue-500 text-xs">✓</span>
                      </div>
                      <span className="text-sm">Threat Hunting</span>
                    </div>
                  </div>

                  <Link to="/paths/defensive" className="text-blue-500 hover:text-blue-600 font-medium flex items-center group">
                    Explore path <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>

              {/* Offensive Security Path */}
              <div className={`${darkMode ? 'bg-[#1A1F35]/70' : 'bg-white'} rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 overflow-hidden group`}>
                <div className="h-3 bg-red-500 w-full"></div>
                <div className="p-8">
                  <div className="w-16 h-16 bg-red-500/10 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                    <FaCode className="text-red-500 text-2xl" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-red-500">Offensive Security</h3>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>
                    Learn ethical hacking techniques to identify and exploit vulnerabilities
                  </p>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-red-500/10 flex items-center justify-center mr-2">
                        <span className="text-red-500 text-xs">✓</span>
                      </div>
                      <span className="text-sm">Penetration Testing</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-red-500/10 flex items-center justify-center mr-2">
                        <span className="text-red-500 text-xs">✓</span>
                      </div>
                      <span className="text-sm">Vulnerability Assessment</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-red-500/10 flex items-center justify-center mr-2">
                        <span className="text-red-500 text-xs">✓</span>
                      </div>
                      <span className="text-sm">Exploit Development</span>
                    </div>
                  </div>

                  <Link to="/paths/offensive" className="text-red-500 hover:text-red-600 font-medium flex items-center group">
                    Explore path <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>

              {/* Cloud Security Path */}
              <div className={`${darkMode ? 'bg-[#1A1F35]/70' : 'bg-white'} rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 overflow-hidden group`}>
                <div className="h-3 bg-[#F5B93F] w-full"></div>
                <div className="p-8">
                  <div className="w-16 h-16 bg-[#F5B93F]/10 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                    <FaCloudDownloadAlt className="text-[#F5B93F] text-2xl" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-[#F5B93F]">Cloud Security</h3>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>
                    Secure cloud environments and protect data across multiple platforms
                  </p>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-[#F5B93F]/10 flex items-center justify-center mr-2">
                        <span className="text-[#F5B93F] text-xs">✓</span>
                      </div>
                      <span className="text-sm">AWS/Azure/GCP Security</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-[#F5B93F]/10 flex items-center justify-center mr-2">
                        <span className="text-[#F5B93F] text-xs">✓</span>
                      </div>
                      <span className="text-sm">Container Security</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-[#F5B93F]/10 flex items-center justify-center mr-2">
                        <span className="text-[#F5B93F] text-xs">✓</span>
                      </div>
                      <span className="text-sm">Serverless Security</span>
                    </div>
                  </div>

                  <Link to="/paths/cloud" className="text-[#F5B93F] hover:text-[#E5A92F] font-medium flex items-center group">
                    Explore path <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </div>

            <div className="mt-12 text-center">
              <Link to="/paths" className="bg-[#4A5CBA] hover:bg-[#3A4CAA] text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 inline-flex items-center shadow-lg hover:shadow-cyberforce">
                View all training paths <FaArrowRight className="ml-2" />
              </Link>
            </div>
          </div>
        </section>

        {/* Why Train With Us Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="flex items-center justify-center mb-4">
                <div className="h-1 w-12 bg-[#4A5CBA] mr-4"></div>
                <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{t('whyTrain')}</h2>
                <div className="h-1 w-12 bg-[#4A5CBA] ml-4"></div>
              </div>
              <p className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
                {t('platformOffers')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className={`${darkMode ? 'bg-[#1A1F35]/70' : 'bg-white'} p-8 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 relative overflow-hidden`}>
                <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-[#4A5CBA]/5 rounded-full blur-xl"></div>
                <div className="w-16 h-16 bg-[#4A5CBA]/10 rounded-full flex items-center justify-center mb-6">
                  <FaLaptopCode className="text-[#4A5CBA] text-2xl" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-[#4A5CBA]">{t('competitionFocused')}</h3>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {t('challengesDesigned')}
                </p>
              </div>

              <div className={`${darkMode ? 'bg-[#1A1F35]/70' : 'bg-white'} p-8 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 relative overflow-hidden`}>
                <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-[#4A5CBA]/5 rounded-full blur-xl"></div>
                <div className="w-16 h-16 bg-[#4A5CBA]/10 rounded-full flex items-center justify-center mb-6">
                  <FaNetworkWired className="text-[#4A5CBA] text-2xl" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-[#4A5CBA]">{t('icsTraining')}</h3>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {t('specializedModules')}
                </p>
              </div>

              <div className={`${darkMode ? 'bg-[#1A1F35]/70' : 'bg-white'} p-8 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 transform hover:-translate-y-1 relative overflow-hidden`}>
                <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-[#4A5CBA]/5 rounded-full blur-xl"></div>
                <div className="w-16 h-16 bg-[#4A5CBA]/10 rounded-full flex items-center justify-center mb-6">
                  <FaUsers className="text-[#4A5CBA] text-2xl" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-[#4A5CBA]">{t('teamCollaboration')}</h3>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {t('toolsExercises')}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Cybersecurity Attack Simulations Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="flex items-center justify-center mb-4">
                <div className="h-1 w-12 bg-[#4A5CBA] mr-4"></div>
                <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Attack & Defense Simulations</h2>
                <div className="h-1 w-12 bg-[#4A5CBA] ml-4"></div>
              </div>
              <p className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
                Experience real-world offensive and defensive cybersecurity scenarios in our interactive simulations
              </p>
            </div>

            {/* Security Training Options Component */}
            <SecurityTrainingOptions />

            <div className="mt-12 text-center">
              <p className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto mb-8`}>
                Our simulations provide hands-on experience with real-world scenarios, allowing you to develop practical skills in a safe environment.
              </p>
              <Link to="/simulations" className="bg-[#4A5CBA] hover:bg-[#3A4CAA] text-white px-8 py-4 rounded-lg font-medium transition-all duration-300 inline-flex items-center shadow-lg hover:shadow-cyberforce">
                Explore all simulations <FaArrowRight className="ml-2" />
              </Link>
            </div>
          </div>
        </section>

        {/* Partners Section */}
        <section className={`py-16 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="flex items-center justify-center mb-4">
                <div className="h-1 w-12 bg-[#0066cc] mr-4"></div>
                <h2 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Who Will You Learn With?</h2>
                <div className="h-1 w-12 bg-[#0066cc] ml-4"></div>
              </div>
              <p className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
                Our trusted certification partners
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center">
              <div className={`${darkMode ? 'bg-[#1A1F35]/30' : 'bg-white'} p-6 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 flex items-center justify-center h-32`}>
                <img
                  src="http://52.221.116.190/wp-content/uploads/2023/07/1200px-ISACA_logo.png"
                  alt="ISACA"
                  className="max-h-16 max-w-full filter grayscale hover:grayscale-0 transition-all duration-300"
                />
              </div>

              <div className={`${darkMode ? 'bg-[#1A1F35]/30' : 'bg-white'} p-6 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 flex items-center justify-center h-32`}>
                <img
                  src="http://52.221.116.190/wp-content/uploads/2023/07/2560px-ISC²_logo_vectorized.svg.png"
                  alt="ISC2"
                  className="max-h-16 max-w-full filter grayscale hover:grayscale-0 transition-all duration-300"
                />
              </div>

              <div className={`${darkMode ? 'bg-[#1A1F35]/30' : 'bg-white'} p-6 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 flex items-center justify-center h-32`}>
                <img
                  src="http://52.221.116.190/wp-content/uploads/2023/07/Ec_Council_Logo.png"
                  alt="EC-Council"
                  className="max-h-16 max-w-full filter grayscale hover:grayscale-0 transition-all duration-300"
                />
              </div>

              <div className={`${darkMode ? 'bg-[#1A1F35]/30' : 'bg-white'} p-6 rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-lg hover:shadow-cyberforce transition-all duration-300 flex items-center justify-center h-32`}>
                <img
                  src="http://52.221.116.190/wp-content/uploads/2023/07/iso-31-logo-png-transparent.png"
                  alt="ISO"
                  className="max-h-16 max-w-full filter grayscale hover:grayscale-0 transition-all duration-300"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Interactive Challenge Preview Section */}
        <section className={`py-20 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="inline-block bg-[#F5B93F]/10 px-4 py-1 rounded-full mb-2">
                <span className="text-[#F5B93F] font-semibold text-sm tracking-wider uppercase">Try It Yourself</span>
              </div>
              <h2 className={`text-3xl md:text-4xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Experience a <span className="text-[#4A5CBA]">Cyber</span><span className="text-[#F5B93F]">Force</span> Challenge
              </h2>
              <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} max-w-3xl mx-auto`}>
                Get a taste of our interactive cybersecurity challenges right here on the landing page
              </p>
            </div>

            <div className={`${darkMode ? 'bg-[#1A1F35]/80' : 'bg-white'} rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} shadow-xl overflow-hidden`}>
              <div className="p-6 border-b border-gray-800 bg-black flex items-center">
                <div className="flex space-x-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <div className="ml-4 text-gray-400 text-sm font-mono">CyberForce Terminal</div>
              </div>

              <div className="p-6 bg-black text-green-500 font-mono text-sm h-[400px] overflow-y-auto relative">
                <div className="mb-4">
                  <p className="mb-2">Welcome to the CyberForce Challenge Preview!</p>
                  <p className="mb-2">You've discovered a suspicious file on a server. Your task is to analyze it and determine if it's malicious.</p>
                  <p className="mb-4">Let's examine the file properties:</p>

                  <div className="bg-gray-900 p-3 rounded mb-4 overflow-x-auto">
                    <p>$ file suspicious.exe</p>
                    <p className="text-gray-400">suspicious.exe: PE32 executable (GUI) Intel 80386, for MS Windows</p>
                    <p>$ md5sum suspicious.exe</p>
                    <p className="text-gray-400">e1112134b6dcc8bed54e0e34d8ac272d suspicious.exe</p>
                  </div>

                  <p className="mb-2">What would you like to do next?</p>
                </div>

                <div className="space-y-3 mb-6">
                  <button
                    className="w-full text-left bg-gray-900 hover:bg-gray-800 p-3 rounded transition-colors duration-200 border border-gray-700 hover:border-[#4A5CBA]"
                    onClick={() => window.alert("This is a preview. Sign up to access the full challenge!")}
                  >
                    1. Run the file in a sandbox environment
                  </button>

                  <button
                    className="w-full text-left bg-gray-900 hover:bg-gray-800 p-3 rounded transition-colors duration-200 border border-gray-700 hover:border-[#4A5CBA]"
                    onClick={() => window.alert("This is a preview. Sign up to access the full challenge!")}
                  >
                    2. Check for known malware signatures
                  </button>

                  <button
                    className="w-full text-left bg-gray-900 hover:bg-gray-800 p-3 rounded transition-colors duration-200 border border-gray-700 hover:border-[#4A5CBA]"
                    onClick={() => window.alert("This is a preview. Sign up to access the full challenge!")}
                  >
                    3. Analyze network traffic when the file runs
                  </button>

                  <button
                    className="w-full text-left bg-gray-900 hover:bg-gray-800 p-3 rounded transition-colors duration-200 border border-gray-700 hover:border-[#4A5CBA]"
                    onClick={() => window.alert("This is a preview. Sign up to access the full challenge!")}
                  >
                    4. Examine the file's strings and code structure
                  </button>
                </div>

                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent h-20 pointer-events-none"></div>
              </div>

              <div className="p-6 bg-gray-900 text-center">
                <p className="text-gray-300 mb-4">This is just a preview. Sign up to access hundreds of interactive challenges!</p>
                <Link
                  to="/signup"
                  className="bg-gradient-to-r from-[#4A5CBA] to-[#3A4CAA] text-white font-bold px-6 py-3 rounded-lg transition-all duration-300 inline-flex items-center shadow-lg hover:shadow-[#4A5CBA]/50 transform hover:-translate-y-1 group"
                >
                  <span className="flex items-center justify-center">
                    Get Full Access
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className={`${darkMode ? 'bg-gradient-to-br from-[#1A1F35] to-[#0B1120]' : 'bg-gradient-to-br from-white to-gray-50'} rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} p-8 md:p-12 text-center relative overflow-hidden shadow-xl`}>
              {/* Background Elements */}
              <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                <div className="absolute -top-20 -right-20 w-64 h-64 bg-[#0066cc]/5 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-20 -left-20 w-64 h-64 bg-[#0066cc]/5 rounded-full blur-3xl"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full opacity-10">
                  <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                    <pattern id="hexagons-cta" width="50" height="43.4" patternUnits="userSpaceOnUse" patternTransform="scale(2)">
                      <polygon points="25,0 50,14.4 50,43.4 25,57.8 0,43.4 0,14.4" fill="none" stroke={darkMode ? '#3A5E8C' : '#0066cc'} strokeWidth="1"/>
                    </pattern>
                    <rect width="100%" height="100%" fill="url(#hexagons-cta)" />
                  </svg>
                </div>
              </div>

              {/* Content */}
              <div className="relative z-10">
                <div className="w-24 h-24 mx-auto mb-8 relative">
                  <div className="absolute inset-0 rounded-full bg-[#0066cc]/20 animate-pulse-slow opacity-50"></div>
                  <div className="absolute inset-0 rounded-full bg-[#0066cc]/10"></div>
                  <div className={`absolute inset-4 rounded-full ${darkMode ? 'bg-[#0B1120]' : 'bg-white'} flex items-center justify-center shadow-lg`}>
                    <FaRocket className="text-[#0066cc] text-3xl" />
                  </div>
                </div>

                <h2 className={`text-3xl md:text-4xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>{t('readyPrepare')}</h2>
                <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} max-w-3xl mx-auto mb-8`}>
                  {t('joinThousands')}
                </p>

                <div className="flex flex-wrap justify-center gap-4">
                  <Link
                    to="/signup"
                    className="bg-[#0066cc] hover:bg-[#0055aa] text-white font-bold px-8 py-4 rounded-lg transition-all duration-300 text-center shadow-lg hover:shadow-cyberforce transform hover:-translate-y-1"
                  >
                    {t('signUp')}
                  </Link>
                  <Link
                    to="/pricing"
                    className={`${darkMode ? 'bg-[#1A1F35]/50 hover:bg-[#252D4A] text-white border-gray-700' : 'bg-white hover:bg-gray-50 text-gray-800 border-gray-200'} border font-medium px-8 py-4 rounded-lg transition-all duration-300 text-center backdrop-blur-sm shadow-md hover:shadow-lg transform hover:-translate-y-1`}
                  >
                    {t('viewPricing')}
                  </Link>
                </div>

                <div className="mt-12 flex flex-wrap justify-center gap-8">
                  <Link to="/challenges" className="flex items-center text-[#0066cc] hover:text-[#0055aa] transition-all duration-300 group">
                    <div className="w-12 h-12 rounded-full bg-[#0066cc]/10 flex items-center justify-center mr-3 group-hover:bg-[#0066cc]/20 transition-colors shadow-md">
                      <FaTrophy className="text-[#0066cc]" />
                    </div>
                    <span className="text-lg">{t('exploreChallenge')}</span>
                  </Link>
                  <Link to="/learn" className="flex items-center text-[#0066cc] hover:text-[#0055aa] transition-all duration-300 group">
                    <div className="w-12 h-12 rounded-full bg-[#0066cc]/10 flex items-center justify-center mr-3 group-hover:bg-[#0066cc]/20 transition-colors shadow-md">
                      <FaGraduationCap className="text-[#0066cc]" />
                    </div>
                    <span className="text-lg">{t('getStarted')}</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default CyberForceLanding;
