import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaLaptopCode, FaLock, FaInfoCircle, FaBuilding, FaServer, FaNetworkWired, FaShieldAlt } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import UpgradeBanner from '../components/access/UpgradeBanner';
import LockedItem from '../components/access/LockedItem';
import { SUBSCRIPTION_TIERS } from '../config/subscriptionTiers';

// Sample simulation scenarios
const simulationScenarios = [
  {
    id: 'enterprise-breach',
    title: 'Enterprise Network Breach',
    description: 'Simulate a full enterprise network breach scenario. Defend against attackers trying to compromise your infrastructure.',
    difficulty: 'advanced',
    estimatedTime: '4 hours',
    participants: '1-5',
    skills: ['Network Security', 'Incident Response', 'Log Analysis', 'Threat Hunting'],
    image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80'
  },
  {
    id: 'ransomware-response',
    title: 'Ransomware Incident Response',
    description: 'Respond to a ransomware attack in progress. Contain the threat, recover systems, and prevent further damage.',
    difficulty: 'intermediate',
    estimatedTime: '3 hours',
    participants: '1-3',
    skills: ['Malware Analysis', 'Digital Forensics', 'Incident Response', 'System Recovery'],
    image: 'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80'
  },
  {
    id: 'web-app-pentest',
    title: 'Web Application Penetration Test',
    description: 'Conduct a full penetration test on a complex web application. Identify and exploit vulnerabilities to improve security.',
    difficulty: 'intermediate',
    estimatedTime: '3 hours',
    participants: '1-2',
    skills: ['Web Security', 'Vulnerability Assessment', 'Exploitation', 'Reporting'],
    image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80'
  },
  {
    id: 'cloud-security',
    title: 'Cloud Infrastructure Security',
    description: 'Secure a complex cloud infrastructure against common attack vectors. Implement defense-in-depth strategies.',
    difficulty: 'advanced',
    estimatedTime: '4 hours',
    participants: '1-4',
    skills: ['Cloud Security', 'IAM', 'Network Security', 'Compliance'],
    image: 'https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80'
  }
];

/**
 * GlobalStartHack Page
 *
 * A business-tier exclusive feature for advanced simulations.
 * Provides a preview for free/premium users with upgrade prompts.
 */
const GlobalStartHack = () => {
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();
  const {
    subscriptionLevel,
    loading: subscriptionLoading,
    hasAccess
  } = useSubscription();

  const [scenarios, setScenarios] = useState([]);
  const [hasStartHackAccess, setHasStartHackAccess] = useState(false);

  useEffect(() => {
    // Load scenarios
    setScenarios(simulationScenarios);

    // Check access only if user is logged in
    if (!subscriptionLoading && user) {
      setHasStartHackAccess(hasAccess('startHack'));
    } else {
      // Not logged in, no access
      setHasStartHackAccess(false);
    }
  }, [subscriptionLoading, hasAccess, user]);

  // Render difficulty badge
  const renderDifficultyBadge = (difficulty) => {
    let bgColor, textColor;

    switch(difficulty) {
      case 'beginner':
        bgColor = 'bg-green-100 dark:bg-green-900';
        textColor = 'text-green-800 dark:text-green-300';
        break;
      case 'intermediate':
        bgColor = 'bg-yellow-100 dark:bg-yellow-900';
        textColor = 'text-yellow-800 dark:text-yellow-300';
        break;
      case 'advanced':
        bgColor = 'bg-red-100 dark:bg-red-900';
        textColor = 'text-red-800 dark:text-red-300';
        break;
      default:
        bgColor = 'bg-gray-100 dark:bg-gray-900';
        textColor = 'text-gray-800 dark:text-gray-300';
    }

    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${bgColor} ${textColor}`}>
        {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
      </span>
    );
  };

  // Render scenario card
  const renderScenarioCard = (scenario) => {
    return (
      <div
        key={scenario.id}
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-all duration-200 ${
          hasStartHackAccess
            ? 'hover:shadow-lg cursor-pointer transform hover:-translate-y-1'
            : 'opacity-75'
        }`}
        onClick={() => hasStartHackAccess && navigate(`/start-hack/${scenario.id}`)}
      >
        <div className="h-48 overflow-hidden">
          <img
            src={scenario.image}
            alt={scenario.title}
            className="w-full h-full object-cover"
          />
        </div>

        <div className="p-5">
          <div className="flex justify-between items-start">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {scenario.title}
            </h3>

            {!hasStartHackAccess && (
              <FaLock className="text-gray-400 dark:text-gray-500" />
            )}
          </div>

          <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
            {scenario.description}
          </p>

          <div className="mt-4 flex flex-wrap gap-2">
            {renderDifficultyBadge(scenario.difficulty)}
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
              {scenario.estimatedTime}
            </span>
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
              {scenario.participants} participants
            </span>
          </div>

          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Skills</h4>
            <div className="mt-2 flex flex-wrap gap-2">
              {scenario.skills.map((skill, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>

          {!hasStartHackAccess && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                className="w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate('/pricing');
                }}
              >
                Upgrade to Business
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render content based on subscription level and login status
  const renderContent = () => {
    // If user is not logged in, show login prompt
    if (!user) {
      return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-8">
            <div className="p-6">
              <div className="flex items-center justify-center mb-6">
                <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                  <FaLaptopCode className="text-blue-600 dark:text-blue-300 text-3xl" />
                </div>
              </div>

              <h2 className="text-2xl font-bold text-center mb-4 text-gray-900 dark:text-white">
                Sign In to Access Start Hack
              </h2>

              <p className="text-center text-gray-600 dark:text-gray-300 mb-6">
                Create an account or sign in to access our advanced cybersecurity simulations.
              </p>

              <div className="flex justify-center">
                <button
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-md transition-colors"
                  onClick={() => navigate('/login')}
                >
                  Sign In
                </button>
              </div>
            </div>
          </div>

          <div className="mt-8">
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
              Preview Available Scenarios
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {scenarios.slice(0, 2).map(scenario => renderScenarioCard(scenario))}
            </div>
          </div>
        </div>
      );
    }

    // If user has access, show full content
    if (hasStartHackAccess) {
      return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {scenarios.map(scenario => renderScenarioCard(scenario))}
          </div>
        </div>
      );
    }

    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          <div className="p-6">
            <div className="flex items-center justify-center mb-6">
              <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                <FaBuilding className="text-blue-600 dark:text-blue-300 text-3xl" />
              </div>
            </div>

            <h2 className="text-2xl font-bold text-center mb-4 text-gray-900 dark:text-white">
              Business-Exclusive Feature
            </h2>

            <p className="text-center text-gray-600 dark:text-gray-300 mb-6">
              Start Hack is our premium simulation environment for enterprise-grade cybersecurity training.
              Upgrade to our Business plan to access these advanced features.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                <FaServer className="mx-auto text-blue-500 text-2xl mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">Full Infrastructure</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Complete enterprise environments</p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                <FaNetworkWired className="mx-auto text-blue-500 text-2xl mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">Team Collaboration</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Multi-user simulation exercises</p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                <FaShieldAlt className="mx-auto text-blue-500 text-2xl mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">Advanced Scenarios</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Real-world attack simulations</p>
              </div>
            </div>

            <div className="flex justify-center">
              <button
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-md transition-colors"
                onClick={() => navigate('/pricing')}
              >
                Upgrade to Business Plan
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            Preview Available Scenarios
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {scenarios.slice(0, 2).map(scenario => renderScenarioCard(scenario))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Page header */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <FaLaptopCode className="mr-2 text-blue-600 dark:text-blue-400" />
                Start Hack
                {!hasStartHackAccess && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                    <FaBuilding className="mr-1" /> Business
                  </span>
                )}
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Advanced cybersecurity simulations for enterprise training
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      {renderContent()}
    </div>
  );
};

export default GlobalStartHack;
