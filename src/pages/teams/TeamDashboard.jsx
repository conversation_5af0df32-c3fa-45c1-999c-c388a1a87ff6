import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaUsers, FaUserPlus, FaComments, FaTrophy, FaExclamationTriangle } from 'react-icons/fa';
import { Link, Navigate } from 'react-router-dom';

const TeamDashboard = () => {
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const [teams, setTeams] = useState([]);
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [canCreateTeam, setCanCreateTeam] = useState(false);
  const [newTeamName, setNewTeamName] = useState('');
  const [newTeamDescription, setNewTeamDescription] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Check if user can access teams
  useEffect(() => {
    const checkTeamAccess = async () => {
      if (!user || !profile) return;

      // Only premium and business users can access teams
      if (profile.subscription_tier === 'premium' || profile.subscription_tier === 'business') {
        setCanCreateTeam(true);
      } else {
        setError('You need a premium or business subscription to access teams');
      }
    };

    checkTeamAccess();
  }, [user, profile]);

  // Fetch user's teams
  useEffect(() => {
    if (!user || !canCreateTeam) return;

    const fetchTeams = async () => {
      try {
        setLoading(true);

        // Fetch teams where user is a member
        const { data: teamData, error: teamError } = await supabase
          .from('team_members')
          .select(`
            team:teams(
              id,
              name,
              description,
              created_at,
              created_by,
              max_members
            ),
            role
          `)
          .eq('user_id', user.id);

        if (teamError) throw teamError;

        // Fetch pending invitations
        const { data: invitationData, error: invitationError } = await supabase
          .from('team_invitations')
          .select(`
            id,
            team_id,
            team:teams(name, description),
            invited_by,
            inviter:profiles!team_invitations_invited_by_fkey(username),
            status,
            created_at,
            expires_at
          `)
          .eq('email', user.email)
          .eq('status', 'pending');

        if (invitationError) throw invitationError;

        // Process team data
        const processedTeams = await Promise.all(teamData.map(async (membership) => {
          // Get member count for each team
          const { data: memberCount, error: memberError } = await supabase
            .from('team_members')
            .select('count')
            .eq('team_id', membership.team.id);

          if (memberError) throw memberError;

          return {
            ...membership.team,
            role: membership.role,
            memberCount: memberCount[0]?.count || 0
          };
        }));

        setTeams(processedTeams);
        setInvitations(invitationData);
      } catch (error) {
        console.error('Error fetching teams:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTeams();
  }, [user, canCreateTeam]);

  // Create a new team
  const handleCreateTeam = async (e) => {
    e.preventDefault();
    
    if (!newTeamName.trim()) {
      setError('Team name is required');
      return;
    }

    try {
      setLoading(true);

      // Check if user has reached team limit
      if (teams.length >= 15) {
        setError('You have reached the maximum number of teams (15)');
        return;
      }

      // Create new team
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .insert([{
          name: newTeamName,
          description: newTeamDescription,
          created_by: user.id,
          max_members: 8 // Default max members
        }])
        .select()
        .single();

      if (teamError) throw teamError;

      // Add creator as team owner
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: teamData.id,
          user_id: user.id,
          role: 'owner'
        }]);

      if (memberError) throw memberError;

      // Reset form
      setNewTeamName('');
      setNewTeamDescription('');
      setShowCreateForm(false);

      // Add new team to state
      setTeams([...teams, {
        ...teamData,
        role: 'owner',
        memberCount: 1
      }]);
    } catch (error) {
      console.error('Error creating team:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Accept team invitation
  const handleAcceptInvitation = async (invitationId) => {
    try {
      setLoading(true);

      // Get invitation details
      const { data: invitation, error: invitationError } = await supabase
        .from('team_invitations')
        .select('team_id, status')
        .eq('id', invitationId)
        .single();

      if (invitationError) throw invitationError;

      // Check if invitation is still pending
      if (invitation.status !== 'pending') {
        setError('This invitation is no longer valid');
        return;
      }

      // Update invitation status
      const { error: updateError } = await supabase
        .from('team_invitations')
        .update({ status: 'accepted' })
        .eq('id', invitationId);

      if (updateError) throw updateError;

      // Add user to team
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: invitation.team_id,
          user_id: user.id,
          role: 'member'
        }]);

      if (memberError) throw memberError;

      // Refresh teams and invitations
      window.location.reload();
    } catch (error) {
      console.error('Error accepting invitation:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Decline team invitation
  const handleDeclineInvitation = async (invitationId) => {
    try {
      setLoading(true);

      // Update invitation status
      const { error } = await supabase
        .from('team_invitations')
        .update({ status: 'rejected' })
        .eq('id', invitationId);

      if (error) throw error;

      // Remove invitation from state
      setInvitations(invitations.filter(inv => inv.id !== invitationId));
    } catch (error) {
      console.error('Error declining invitation:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // If user doesn't have access, redirect to pricing
  if (!loading && !canCreateTeam) {
    return <Navigate to="/pricing" />;
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Team Dashboard</h1>
          {canCreateTeam && (
            <button
              onClick={() => setShowCreateForm(!showCreateForm)}
              className="theme-button-primary flex items-center gap-2 px-4 py-2 rounded-lg"
            >
              <FaUserPlus /> {showCreateForm ? 'Cancel' : 'Create Team'}
            </button>
          )}
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <p>{error}</p>
          </div>
        )}

        {/* Create Team Form */}
        {showCreateForm && (
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6 mb-6`}>
            <h2 className="text-xl font-bold mb-4">Create New Team</h2>
            <form onSubmit={handleCreateTeam}>
              <div className="mb-4">
                <label className="block mb-2">Team Name</label>
                <input
                  type="text"
                  className="theme-input w-full p-2 rounded"
                  value={newTeamName}
                  onChange={(e) => setNewTeamName(e.target.value)}
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block mb-2">Description</label>
                <textarea
                  className="theme-input w-full p-2 rounded"
                  value={newTeamDescription}
                  onChange={(e) => setNewTeamDescription(e.target.value)}
                  rows="3"
                ></textarea>
              </div>
              <button
                type="submit"
                className="theme-button-primary px-4 py-2 rounded-lg"
                disabled={loading}
              >
                {loading ? 'Creating...' : 'Create Team'}
              </button>
            </form>
          </div>
        )}

        {/* Team Invitations */}
        {invitations.length > 0 && (
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6 mb-6`}>
            <h2 className="text-xl font-bold mb-4">Team Invitations</h2>
            <div className="space-y-4">
              {invitations.map(invitation => (
                <div key={invitation.id} className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-300'} rounded-lg border p-4 flex flex-col md:flex-row md:items-center justify-between`}>
                  <div className="mb-4 md:mb-0">
                    <h3 className="font-bold">{invitation.team.name}</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Invited by {invitation.inviter.username} on {new Date(invitation.created_at).toLocaleDateString()}
                    </p>
                    {invitation.team.description && (
                      <p className="mt-2">{invitation.team.description}</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleAcceptInvitation(invitation.id)}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
                      disabled={loading}
                    >
                      Accept
                    </button>
                    <button
                      onClick={() => handleDeclineInvitation(invitation.id)}
                      className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg"
                      disabled={loading}
                    >
                      Decline
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* My Teams */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <h2 className="text-xl font-bold mb-4">My Teams</h2>
          
          {loading ? (
            <p>Loading teams...</p>
          ) : teams.length === 0 ? (
            <div className="text-center py-8">
              <FaUsers className="mx-auto text-4xl mb-4 text-gray-400" />
              <p className="mb-4">You are not a member of any teams yet.</p>
              {canCreateTeam && !showCreateForm && (
                <button
                  onClick={() => setShowCreateForm(true)}
                  className="theme-button-primary px-4 py-2 rounded-lg"
                >
                  Create Your First Team
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {teams.map(team => (
                <div key={team.id} className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-300'} rounded-lg border p-4`}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold">{team.name}</h3>
                    <span className={`px-2 py-1 rounded text-xs ${
                      team.role === 'owner' 
                        ? 'bg-purple-100 text-purple-800' 
                        : team.role === 'admin'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}>
                      {team.role}
                    </span>
                  </div>
                  
                  {team.description && (
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>{team.description}</p>
                  )}
                  
                  <div className="flex justify-between items-center text-sm mb-4">
                    <span>{team.memberCount} / {team.max_members} members</span>
                    <span>Created {new Date(team.created_at).toLocaleDateString()}</span>
                  </div>
                  
                  <div className="flex gap-2">
                    <Link
                      to={`/teams/${team.id}`}
                      className="theme-button-primary flex-1 text-center py-2 rounded-lg"
                    >
                      View Team
                    </Link>
                    {team.role === 'owner' && (
                      <Link
                        to={`/teams/${team.id}/manage`}
                        className={`${darkMode ? 'bg-[#1A1F35] hover:bg-[#252D4A] text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-800'} flex-1 text-center py-2 rounded-lg`}
                      >
                        Manage
                      </Link>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TeamDashboard;
