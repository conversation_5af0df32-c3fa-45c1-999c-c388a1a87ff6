import React, { useState, useEffect, useRef } from 'react';
import { useParams, Link, Navigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaArrowLeft, FaPaperPlane, FaUsers, FaExclamationTriangle } from 'react-icons/fa';

const TeamChat = () => {
  const { teamId } = useParams();
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const [team, setTeam] = useState(null);
  const [members, setMembers] = useState([]);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isMember, setIsMember] = useState(false);
  const messagesEndRef = useRef(null);

  // Check if user is a member of the team
  useEffect(() => {
    const checkMembership = async () => {
      if (!user || !teamId) return;

      try {
        setLoading(true);

        // Check if user is a member of the team
        const { data: memberData, error: memberError } = await supabase
          .from('team_members')
          .select('id, role')
          .eq('team_id', teamId)
          .eq('user_id', user.id)
          .single();

        if (memberError && memberError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" error
          throw memberError;
        }

        if (!memberData) {
          setIsMember(false);
          setError('You are not a member of this team');
          return;
        }

        setIsMember(true);

        // Fetch team details
        const { data: teamData, error: teamError } = await supabase
          .from('teams')
          .select('*')
          .eq('id', teamId)
          .single();

        if (teamError) throw teamError;

        setTeam(teamData);

        // Fetch team members
        const { data: membersData, error: membersError } = await supabase
          .from('team_members')
          .select(`
            id,
            role,
            user:profiles(id, username, full_name, avatar_url)
          `)
          .eq('team_id', teamId);

        if (membersError) throw membersError;

        setMembers(membersData);

        // Fetch team messages
        const { data: messagesData, error: messagesError } = await supabase
          .from('team_messages')
          .select(`
            id,
            message,
            created_at,
            user:profiles(id, username, full_name, avatar_url)
          `)
          .eq('team_id', teamId)
          .order('created_at', { ascending: true });

        if (messagesError) throw messagesError;

        setMessages(messagesData);
      } catch (error) {
        console.error('Error fetching team data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    checkMembership();
  }, [user, teamId]);

  // Set up real-time subscription for new messages
  useEffect(() => {
    if (!teamId || !isMember) return;

    const subscription = supabase
      .channel(`team-messages-${teamId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'team_messages',
        filter: `team_id=eq.${teamId}`
      }, (payload) => {
        // Fetch the complete message with user data
        const fetchMessage = async () => {
          const { data, error } = await supabase
            .from('team_messages')
            .select(`
              id,
              message,
              created_at,
              user:profiles(id, username, full_name, avatar_url)
            `)
            .eq('id', payload.new.id)
            .single();

          if (!error && data) {
            setMessages(prev => [...prev, data]);
          }
        };

        fetchMessage();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [teamId, isMember]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Send a new message
  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !user || !teamId) return;

    try {
      const { error } = await supabase
        .from('team_messages')
        .insert([{
          team_id: teamId,
          user_id: user.id,
          message: newMessage.trim()
        }]);

      if (error) throw error;

      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      setError(error.message);
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // If user is not a member, redirect to teams dashboard
  if (!loading && !isMember) {
    return <Navigate to="/teams" />;
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Link to="/teams" className={`mr-4 ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'}`}>
            <FaArrowLeft />
          </Link>
          {loading ? (
            <h1 className="text-2xl font-bold">Loading...</h1>
          ) : (
            <h1 className="text-2xl font-bold">{team?.name} - Team Chat</h1>
          )}
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <p>{error}</p>
          </div>
        )}

        {/* Chat Container */}
        <div className="flex flex-col md:flex-row gap-4">
          {/* Messages */}
          <div className={`flex-1 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4 flex flex-col h-[calc(100vh-200px)]`}>
            {/* Messages List */}
            <div className="flex-1 overflow-y-auto mb-4">
              {loading ? (
                <p>Loading messages...</p>
              ) : messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <FaPaperPlane className="text-4xl mb-4 text-gray-400" />
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>No messages yet. Start the conversation!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map(message => {
                    const isCurrentUser = message.user.id === user?.id;
                    
                    return (
                      <div 
                        key={message.id} 
                        className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`max-w-[80%] ${
                          isCurrentUser 
                            ? `${darkMode ? 'bg-[#88cc14]/20 text-white' : 'bg-[#88cc14]/10 text-gray-900'} rounded-tl-lg rounded-tr-none` 
                            : `${darkMode ? 'bg-[#252D4A] text-white' : 'bg-gray-100 text-gray-900'} rounded-tr-lg rounded-tl-none`
                        } rounded-bl-lg rounded-br-lg p-3`}>
                          {!isCurrentUser && (
                            <div className="font-bold text-sm mb-1">{message.user.username}</div>
                          )}
                          <div className="break-words">{message.message}</div>
                          <div className={`text-xs mt-1 text-right ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            {formatTimestamp(message.created_at)}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>

            {/* Message Input */}
            <form onSubmit={handleSendMessage} className="flex gap-2">
              <input
                type="text"
                className="theme-input flex-1 p-2 rounded-lg"
                placeholder="Type your message..."
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                disabled={loading || !isMember}
              />
              <button
                type="submit"
                className="theme-button-primary px-4 py-2 rounded-lg"
                disabled={loading || !newMessage.trim() || !isMember}
              >
                <FaPaperPlane />
              </button>
            </form>
          </div>

          {/* Team Members */}
          <div className={`w-full md:w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
            <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
              <FaUsers /> Members
            </h2>
            
            {loading ? (
              <p>Loading members...</p>
            ) : (
              <div className="space-y-2">
                {members.map(member => (
                  <div key={member.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      {member.user.avatar_url ? (
                        <img 
                          src={member.user.avatar_url} 
                          alt={member.user.username} 
                          className="w-8 h-8 rounded-full mr-2"
                        />
                      ) : (
                        <div className={`w-8 h-8 rounded-full mr-2 flex items-center justify-center ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                          {member.user.username.charAt(0).toUpperCase()}
                        </div>
                      )}
                      <span>{member.user.username}</span>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded ${
                      member.role === 'owner' 
                        ? 'bg-purple-100 text-purple-800' 
                        : member.role === 'admin'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}>
                      {member.role}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamChat;
