import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaLock, FaEnvelope, FaGoogle, FaGithub, FaArrowLeft, FaUser } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

const SimpleSignup = () => {
  const navigate = useNavigate();
  const { darkMode } = useGlobalTheme();
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    // Basic validation
    if (password !== confirmPassword) {
      setError("Passwords don't match");
      setIsLoading(false);
      return;
    }

    // Simulate signup
    setTimeout(() => {
      setIsLoading(false);
      // For demo purposes, just navigate to dashboard
      navigate('/simplified-dashboard');
    }, 1000);
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-center p-4`}>
      <div className="max-w-md w-full">
        {/* Back to home */}
        <div className="mb-8">
          <Link to="/" className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} flex items-center gap-2`}>
            <FaArrowLeft />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Signup form */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-xl border overflow-hidden`}>
          <div className="p-8">
            <div className="text-center mb-8">
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>Create an Account</h1>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Join the XCerberus cybersecurity community</p>
            </div>

            {error && (
              <div className={`${darkMode ? 'bg-red-500/20 text-red-400' : 'bg-red-100 text-red-800'} px-4 py-3 rounded mb-6`}>
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="username" className={`block ${darkMode ? 'text-gray-400' : 'text-gray-700'} mb-2`}>Username</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <FaUser className={`${darkMode ? 'text-gray-500' : 'text-gray-500'}`} />
                  </div>
                  <input
                    type="text"
                    id="username"
                    className={`${darkMode ? 'bg-[#0B1120] border-gray-800 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`}
                    placeholder="cyberhacker"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="email" className={`block ${darkMode ? 'text-gray-400' : 'text-gray-700'} mb-2`}>Email Address</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <FaEnvelope className={`${darkMode ? 'text-gray-500' : 'text-gray-500'}`} />
                  </div>
                  <input
                    type="email"
                    id="email"
                    className={`${darkMode ? 'bg-[#0B1120] border-gray-800 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`}
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="password" className={`block ${darkMode ? 'text-gray-400' : 'text-gray-700'} mb-2`}>Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <FaLock className={`${darkMode ? 'text-gray-500' : 'text-gray-500'}`} />
                  </div>
                  <input
                    type="password"
                    id="password"
                    className={`${darkMode ? 'bg-[#0B1120] border-gray-800 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`}
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    minLength={8}
                  />
                </div>
                <p className={`mt-1 text-sm ${darkMode ? 'text-gray-500' : 'text-gray-600'}`}>
                  Password must be at least 8 characters
                </p>
              </div>

              <div className="mb-6">
                <label htmlFor="confirmPassword" className={`block ${darkMode ? 'text-gray-400' : 'text-gray-700'} mb-2`}>Confirm Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <FaLock className={`${darkMode ? 'text-gray-500' : 'text-gray-500'}`} />
                  </div>
                  <input
                    type="password"
                    id="confirmPassword"
                    className={`${darkMode ? 'bg-[#0B1120] border-gray-800 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`}
                    placeholder="••••••••"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="mb-6">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="terms"
                      type="checkbox"
                      className="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-[#88cc14]"
                      required
                    />
                  </div>
                  <label htmlFor="terms" className={`ml-2 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    I agree to the <Link to="/terms" className="text-[#88cc14] hover:underline">Terms of Service</Link> and <Link to="/privacy" className="text-[#88cc14] hover:underline">Privacy Policy</Link>
                  </label>
                </div>
              </div>

              <button
                type="submit"
                className={`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${
                  isLoading ? 'opacity-70 cursor-not-allowed' : ''
                }`}
                disabled={isLoading}
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </button>
            </form>

            <div className="mt-6 text-center">
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Already have an account?{' '}
                <Link to="/login" className="text-[#88cc14] hover:underline">
                  Sign In
                </Link>
              </p>
            </div>

            <div className="mt-8">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className={`w-full border-t ${darkMode ? 'border-gray-800' : 'border-gray-300'}`}></div>
                </div>
                <div className="relative flex justify-center">
                  <span className={`${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} px-4 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Or continue with</span>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-4">
                <button
                  type="button"
                  className={`flex items-center justify-center gap-2 ${
                    darkMode 
                      ? 'bg-[#0B1120] hover:bg-[#151F38] text-white border-gray-800' 
                      : 'bg-gray-50 hover:bg-gray-100 text-gray-900 border-gray-300'
                  } py-3 px-4 rounded-lg border transition-colors`}
                >
                  <FaGoogle />
                  <span>Google</span>
                </button>
                <button
                  type="button"
                  className={`flex items-center justify-center gap-2 ${
                    darkMode 
                      ? 'bg-[#0B1120] hover:bg-[#151F38] text-white border-gray-800' 
                      : 'bg-gray-50 hover:bg-gray-100 text-gray-900 border-gray-300'
                  } py-3 px-4 rounded-lg border transition-colors`}
                >
                  <FaGithub />
                  <span>GitHub</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleSignup;
