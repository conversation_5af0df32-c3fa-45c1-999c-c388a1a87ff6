import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  FaShieldAlt, FaLaptopCode, FaServer, FaNetworkWired, FaLock, 
  FaUserSecret, FaBug, FaChevronRight, FaArrowRight, FaGraduationCap 
} from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import CyberForceSEO from '../components/common/CyberForceSEO';

const Simulations = () => {
  const { darkMode } = useGlobalTheme();
  const [hoveredCard, setHoveredCard] = useState(null);

  const simulationCategories = [
    {
      id: 'offensive',
      title: 'Offensive Simulations',
      description: 'Practice ethical hacking techniques in realistic environments',
      icon: <FaUserSecret className="text-red-500" size={36} />,
      path: '/simulations/offensive',
      color: 'from-red-500/20 to-red-600/5',
      borderColor: 'border-red-500/20',
      hoverBorderColor: 'hover:border-red-500',
      features: [
        'Web Application Penetration Testing',
        'Network Exploitation',
        'Social Engineering Scenarios',
        'Privilege Escalation Challenges'
      ]
    },
    {
      id: 'defensive',
      title: 'Defensive Simulations',
      description: 'Develop blue team skills to detect and respond to cyber threats',
      icon: <FaShieldAlt className="text-blue-500" size={36} />,
      path: '/simulations/defensive',
      color: 'from-blue-500/20 to-blue-600/5',
      borderColor: 'border-blue-500/20',
      hoverBorderColor: 'hover:border-blue-500',
      features: [
        'Security Operations Center (SOC)',
        'Incident Response Scenarios',
        'Threat Hunting Exercises',
        'Log Analysis Challenges'
      ]
    }
  ];

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-20`}>
      {/* SEO */}
      <CyberForceSEO 
        title="Cybersecurity Simulations"
        description="Experience realistic cybersecurity simulations with CyberForce. Practice offensive and defensive security techniques in safe, controlled environments."
        keywords={['cybersecurity simulations', 'offensive security', 'defensive security', 'SOC training', 'penetration testing']}
        canonicalUrl="https://cyberforce.om/simulations"
      />
      
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-b from-[#0B1120] to-[#1A1F35] py-16">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-red-500/20"></div>
          <div className="grid grid-cols-10 grid-rows-10 h-full w-full">
            {Array.from({ length: 100 }).map((_, i) => (
              <div key={i} className="border-[0.5px] border-white/5"></div>
            ))}
          </div>
        </div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl md:text-5xl font-bold mb-6 text-white"
            >
              Realistic Cybersecurity <span className="text-[#F5B93F]">Simulations</span>
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-xl text-gray-300 mb-8"
            >
              Develop practical skills through immersive offensive and defensive security scenarios
            </motion.p>
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="flex flex-wrap justify-center gap-4"
            >
              <Link 
                to="/simulations/offensive" 
                className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium flex items-center gap-2 transition-colors"
              >
                <FaUserSecret /> Offensive Simulations
              </Link>
              <Link 
                to="/simulations/defensive" 
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium flex items-center gap-2 transition-colors"
              >
                <FaShieldAlt /> Defensive Simulations
              </Link>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-16">
        {/* Introduction */}
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className={`text-3xl font-bold mb-6 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Why Train with CyberForce Simulations?
          </h2>
          <p className={`text-lg mb-8 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Our simulations provide hands-on experience in realistic environments, allowing you to develop practical skills that are directly applicable to real-world cybersecurity challenges.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border`}>
              <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center mx-auto mb-4">
                <FaGraduationCap className="text-blue-500 text-xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Learn by Doing</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Practical experience is the most effective way to develop cybersecurity skills
              </p>
            </div>
            <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border`}>
              <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center mx-auto mb-4">
                <FaLaptopCode className="text-purple-500 text-xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Real-World Scenarios</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Based on actual cyber attacks and defense strategies used in the industry
              </p>
            </div>
            <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border`}>
              <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center mx-auto mb-4">
                <FaShieldAlt className="text-green-500 text-xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Safe Environment</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Practice offensive and defensive techniques in controlled, legal environments
              </p>
            </div>
          </div>
        </div>

        {/* Simulation Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {simulationCategories.map((category) => (
            <Link 
              key={category.id}
              to={category.path}
              className={`relative overflow-hidden rounded-xl border ${category.borderColor} ${category.hoverBorderColor} transition-all duration-300 ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'}`}
              onMouseEnter={() => setHoveredCard(category.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-50`}></div>
              <div className="relative z-10 p-8">
                <div className="flex items-start justify-between mb-6">
                  <div className="w-16 h-16 rounded-lg bg-white/10 backdrop-blur-sm flex items-center justify-center">
                    {category.icon}
                  </div>
                  <FaArrowRight className={`text-xl transition-transform duration-300 ${hoveredCard === category.id ? 'translate-x-0 opacity-100' : '-translate-x-4 opacity-0'}`} />
                </div>
                <h3 className="text-2xl font-bold mb-3">{category.title}</h3>
                <p className={`mb-6 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{category.description}</p>
                <div className="space-y-2">
                  {category.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <FaChevronRight className="text-xs flex-shrink-0" />
                      <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* CTA Section */}
        <div className={`rounded-xl overflow-hidden relative ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10"></div>
          <div className="relative z-10 p-8 md:p-12 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Test Your Skills?</h2>
            <p className={`text-lg mb-8 max-w-2xl mx-auto ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Join CyberForce today and gain access to our full range of cybersecurity simulations, challenges, and learning resources.
            </p>
            <Link 
              to="/pricing" 
              className="px-8 py-3 bg-[#4A5CBA] hover:bg-[#3A4CAA] text-white rounded-lg font-medium inline-flex items-center gap-2 transition-colors"
            >
              Get Started <FaArrowRight />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Simulations;
