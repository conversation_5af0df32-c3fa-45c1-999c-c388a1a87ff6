import React from 'react';
import { Link } from 'react-router-dom';

function SimpleHome() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
      <div className="text-center max-w-4xl mx-auto px-4">
        <h1 className="text-5xl font-bold mb-6 text-green-400">XCerberus</h1>
        <p className="text-2xl mb-8">The Ultimate Cybersecurity Learning Platform</p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-3 text-green-400">Learn Cybersecurity</h2>
            <p className="mb-4">Master offensive and defensive security skills through interactive modules.</p>
            <Link
              to="/challenges"
              className="inline-block bg-green-500 hover:bg-green-600 text-black font-bold py-2 px-6 rounded-lg transition-colors"
            >
              View Challenges
            </Link>
          </div>

          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-3 text-green-400">Premium Features</h2>
            <p className="mb-4">Unlock advanced learning paths and hands-on hacking labs.</p>
            <Link
              to="/pricing"
              className="inline-block bg-green-500 hover:bg-green-600 text-black font-bold py-2 px-6 rounded-lg transition-colors"
            >
              View Plans
            </Link>
          </div>
        </div>

        <div className="flex flex-wrap justify-center gap-4">
          <Link to="/simplified-dashboard" className="text-green-400 hover:underline">Dashboard</Link>
          <Link to="/learn/modules" className="text-green-400 hover:underline">Learning Modules</Link>
          <Link to="/challenges" className="text-green-400 hover:underline">Challenges</Link>
          <Link to="/pricing" className="text-green-400 hover:underline">Pricing</Link>
          <Link to="/about" className="text-green-400 hover:underline">About</Link>
        </div>
      </div>
    </div>
  );
}

export default SimpleHome;
