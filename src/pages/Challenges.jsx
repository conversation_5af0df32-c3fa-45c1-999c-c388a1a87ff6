import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>a<PERSON><PERSON>, <PERSON>aT<PERSON>hy, Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>lock, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaUserCircle } from 'react-icons/fa';
import { getOpenChallenges, getUserChallenges, submitChallenge, supabase } from '../lib/supabase';
import { getChallengeAttempts, getChallengeSummary } from '../lib/challenges';
import { useSubscription } from '../contexts/SubscriptionContext';
import SubscriptionRequired from '../components/SubscriptionRequired';
import FeatureLimit from '../components/FeatureLimit';
import WebExploitChallenge from '../components/challenges/WebExploitChallenge';
import BinaryChallenge from '../components/challenges/BinaryChallenge';
import CryptoChallenge from '../components/challenges/CryptoChallenge';
import { useNavigate } from 'react-router-dom';

function Challenges() {
  const navigate = useNavigate();
  const [challenges, setChallenges] = useState([]);
  const [userChallenges, setUserChallenges] = useState([]);
  const [challengeStats, setChallengeStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [activeChallenge, setActiveChallenge] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [user, setUser] = useState(null);
  const { subscriptionLevel, getFeatureLimit } = useSubscription();

  useEffect(() => {
    const fetchChallenges = async () => {
      try {
        // Get open challenges
        const challengesData = await getOpenChallenges();
        
        // Get attempt counts for each challenge
        const statsPromises = challengesData.map(challenge => 
          getChallengeSummary(challenge.id)
        );
        const stats = await Promise.all(statsPromises);
        
        // Combine challenges with their stats
        const challengesWithStats = challengesData.map((challenge, index) => ({
          ...challenge,
          stats: stats[index]
        }));
        
        setChallenges(challengesWithStats);
        
        // Check if user is logged in
        const { data: { session } } = await supabase.auth.getSession();
        setUser(session?.user || null);
        
        // If user is logged in, get their challenges
        if (session?.user) {
          const userChallengesData = await getUserChallenges();
          setUserChallenges(userChallengesData || []);
        }
      } catch (error) {
        console.error('Error fetching challenges:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchChallenges();

    // Subscribe to challenge updates
    const subscription = supabase
      .channel('challenges')
      .on('*', async (payload) => {
        if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
          // Update challenge stats
          const stats = await getChallengeSummary(payload.new.id);
          setChallengeStats(prev => ({
            ...prev,
            [payload.new.id]: stats
          }));
        }
      })
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const categories = [
    { id: 'all', name: 'All Challenges' },
    { id: 'web', name: 'Web Security' },
    { id: 'crypto', name: 'Cryptography' },
    { id: 'binary', name: 'Binary Exploitation' }
  ];

  const filteredChallenges = filter === 'all' 
    ? challenges 
    : challenges.filter(challenge => challenge.category.toLowerCase().includes(filter));

  // Get the maximum number of challenges the user can access based on subscription
  const maxChallenges = getFeatureLimit('maxChallenges');
  const hasUnlimitedChallenges = maxChallenges < 0;

  // Check if user has already completed a challenge
  const isCompleted = (challengeId) => {
    return userChallenges.some(
      uc => uc.challenge_id === challengeId && uc.status === 'completed'
    );
  };

  const handleStartChallenge = (challenge) => {
    if (!user) {
      // Redirect to login if not logged in
      navigate('/login', { state: { from: '/challenges' } });
      return;
    }
    
    setActiveChallenge(challenge);
    setShowModal(true);
  };

  // Challenge Modal Component
  const ChallengeModal = ({ challenge }) => {
    const [started, setStarted] = useState(false);
    const [completed, setCompleted] = useState(false);

    if (!challenge) return null;

    const handleComplete = async () => {
      setCompleted(true);
      // Update user challenges
      try {
        await submitChallenge(challenge.id, 'completed', challenge.points);
        // Refresh user challenges
        const userChallengesData = await getUserChallenges();
        setUserChallenges(userChallengesData || []);
      } catch (error) {
        console.error('Error submitting challenge:', error);
      }
    };

    const renderChallenge = () => {
      switch (challenge.category.toLowerCase()) {
        case 'web':
          return <WebExploitChallenge onComplete={handleComplete} />;
        case 'binary':
          return <BinaryChallenge onComplete={handleComplete} />;
        case 'crypto':
          return <CryptoChallenge onComplete={handleComplete} />;
        default:
          return <WebExploitChallenge onComplete={handleComplete} />;
      }
    };

    return (
      <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4 overflow-y-auto">
        <motion.div 
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="bg-gray-900 rounded-lg w-full max-w-4xl my-8 relative"
        >
          <button 
            onClick={() => setShowModal(false)}
            className="absolute top-4 right-4 text-gray-400 hover:text-white bg-gray-800 hover:bg-gray-700 rounded-full p-2 transition-colors"
            aria-label="Close challenge"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <div className="p-6">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-lg flex items-center justify-center mr-4 bg-primary/10">
                <FaLock className="text-2xl text-primary" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white">{challenge.title}</h3>
                <div className="flex items-center mt-1">
                  <span className="text-sm text-gray-400">{challenge.difficulty}</span>
                  <span className="mx-2 text-gray-600">•</span>
                  <span className="text-sm text-primary">{challenge.points} pts</span>
                </div>
              </div>
            </div>

            {!started ? (
              <div className="space-y-6 text-gray-300">
                <div>
                  <h4 className="text-lg font-bold text-white mb-2">Objective</h4>
                  <p>{challenge.description}</p>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-white mb-2">Tasks</h4>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <span className="w-5 h-5 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 text-sm">
                        1
                      </span>
                      Find and exploit vulnerabilities
                    </li>
                    <li className="flex items-center">
                      <span className="w-5 h-5 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 text-sm">
                        2
                      </span>
                      Bypass security measures
                    </li>
                    <li className="flex items-center">
                      <span className="w-5 h-5 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 text-sm">
                        3
                      </span>
                      Capture the flag
                    </li>
                  </ul>
                </div>

                <button
                  onClick={() => setStarted(true)}
                  className="w-full bg-primary text-black font-bold py-3 px-4 rounded-lg hover:bg-primary-hover transition-all duration-300"
                >
                  Start Challenge
                </button>
              </div>
            ) : (
              <div className="challenge-content">
                {renderChallenge()}
                
                {completed && (
                  <div className="mt-6 flex justify-end">
                    <button
                      onClick={() => setShowModal(false)}
                      className="bg-primary text-black font-bold py-2 px-6 rounded-lg hover:bg-primary-hover transition-all duration-300"
                    >
                      Close Challenge
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </motion.div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Header */}
      <div className="bg-black text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-4">
              Open Challenges
            </h1>
            <p className="text-gray-400 text-lg mb-8">
              Test your skills with real-world cybersecurity challenges. 
              Complete challenges to earn XCerberus coins and climb the leaderboard.
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { icon: FaUsers, value: '1000+', label: 'Participants' },
                { icon: FaTrophy, value: '50+', label: 'Active Challenges' },
                { icon: FaLock, value: '₹10K+', label: 'In Rewards' },
                { icon: FaClock, value: '24/7', label: 'Support' }
              ].map((stat, index) => (
                <div key={index} className="bg-gray-800 rounded-lg p-4">
                  <stat.icon className="text-primary text-2xl mx-auto mb-2" />
                  <div className="text-xl font-bold">{stat.value}</div>
                  <div className="text-gray-400 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Subscription Status */}
      {user && !hasUnlimitedChallenges && (
        <div className="container mx-auto px-4 py-4">
          <div className="bg-primary/10 border border-primary/20 rounded-lg p-4 flex justify-between items-center">
            <div>
              <p className="text-gray-800">
                <span className="font-bold">Your Challenge Access: </span>
                {userChallenges.length} of {maxChallenges} challenges used
              </p>
              <div className="w-64 h-2 bg-gray-200 rounded-full mt-2">
                <div 
                  className="h-full bg-primary rounded-full"
                  style={{ width: `${(userChallenges.length / maxChallenges) * 100}%` }}
                />
              </div>
            </div>
            <a 
              href="/pricing" 
              className="bg-primary text-black px-4 py-2 rounded-lg font-bold hover:bg-primary-hover transition-colors flex items-center gap-2"
            >
              <FaCrown />
              <span>Upgrade</span>
            </a>
          </div>
        </div>
      )}

      {/* Category Filter */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-wrap gap-4 mb-8">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setFilter(category.id)}
              className={`px-4 py-2 rounded-full transition-all ${
                filter === category.id
                  ? 'bg-primary text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Challenges Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredChallenges.map((challenge, index) => (
              <motion.div
                key={challenge.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6"
              >
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {challenge.title}
                    </h3>
                    <div className="flex items-center gap-2 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        challenge.difficulty === 'Easy' ? 'bg-green-100 text-green-800' :
                        challenge.difficulty === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {challenge.difficulty}
                      </span>
                      <span className="text-primary">{challenge.points} pts</span>
                    </div>
                  </div>
                  <div className={`p-2 rounded-lg ${
                    user && isCompleted(challenge.id) 
                      ? 'bg-green-100' 
                      : 'bg-primary/10'
                  }`}>
                    {user && isCompleted(challenge.id) ? (
                      <FaCheck className="text-green-600" />
                    ) : (
                      <FaLock className="text-primary" />
                    )}
                  </div>
                </div>

                <p className="text-gray-600 mb-6 line-clamp-2">
                  {challenge.description}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <FaUsers />
                    <span>{challenge.stats?.attempts || 0} attempts</span>
                    {challenge.stats?.completions > 0 && (
                      <>
                        <span className="mx-2">•</span>
                        <span className="text-green-500">{challenge.stats.successRate}% success rate</span>
                      </>
                    )}
                  </div>
                  
                  <button 
                    onClick={() => handleStartChallenge(challenge)}
                    className={`bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors ${
                      user && isCompleted(challenge.id) ? 'bg-green-600 hover:bg-green-700' : ''
                    }`}
                  >
                    {user && isCompleted(challenge.id) ? 'Completed' : 'Start Challenge'}
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Challenge Modal */}
      {showModal && <ChallengeModal challenge={activeChallenge} />}
    </div>
  );
}

export default Challenges;