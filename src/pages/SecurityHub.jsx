import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaShieldAlt, FaChartLine, FaExclamationTriangle, FaTools, FaGraduationCap, FaGamepad } from 'react-icons/fa';
import EnhancedEDRTips from '../components/security/EnhancedEDRTips';
import ThreatIntelligenceFeed from '../components/security/ThreatIntelligenceFeed';
import SecurityToolsMatrix from '../components/tools/SecurityToolsMatrix';
import SkillAssessment from '../components/learning/SkillAssessment';
import EDRSimulator from '../components/interactive/EDRSimulator';

const SecurityHub = () => {
  const [activeTab, setActiveTab] = useState('tips');
  
  const tabs = [
    { id: 'tips', label: 'Security Tips', icon: FaShieldAlt, color: '#88cc14' },
    { id: 'threats', label: 'Threat Intelligence', icon: FaExclamationTriangle, color: '#ef4444' },
    { id: 'tools', label: 'Security Tools', icon: FaTools, color: '#3b82f6' },
    { id: 'assessment', label: 'Skill Assessment', icon: FaChartLine, color: '#8b5cf6' },
    { id: 'simulator', label: 'EDR Simulator', icon: FaGamepad, color: '#f59e0b' }
  ];
  
  const renderContent = () => {
    switch (activeTab) {
      case 'tips':
        return <EnhancedEDRTips />;
      case 'threats':
        return <ThreatIntelligenceFeed />;
      case 'tools':
        return <SecurityToolsMatrix category="edr" />;
      case 'assessment':
        return <SkillAssessment />;
      case 'simulator':
        return <EDRSimulator />;
      default:
        return <EnhancedEDRTips />;
    }
  };
  
  return (
    <div className="min-h-screen bg-[#0B1120] pt-20 pb-16">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">Security Hub</h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Your comprehensive resource for cybersecurity knowledge, tools, and practical skills development
          </p>
        </div>
        
        {/* Tabs */}
        <div className="flex overflow-x-auto mb-8 pb-2">
          <div className="flex gap-2 mx-auto">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-3 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-[#1A1F35] text-white'
                    : 'bg-transparent text-gray-400 hover:text-white hover:bg-[#1A1F35]/50'
                }`}
                style={{ 
                  borderLeft: activeTab === tab.id ? `3px solid ${tab.color}` : 'none'
                }}
              >
                <tab.icon style={{ color: tab.color }} />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
        
        {/* Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderContent()}
        </motion.div>
        
        {/* Related Resources */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-white mb-6">Related Resources</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <motion.div
              whileHover={{ y: -5 }}
              className="bg-[#1A1F35] p-6 rounded-lg"
            >
              <div className="w-12 h-12 rounded-full bg-[#88cc14]/10 flex items-center justify-center mb-4">
                <FaShieldAlt className="text-[#88cc14] text-xl" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">EDR Certification</h3>
              <p className="text-gray-400 mb-4">
                Get certified in EDR implementation and management
              </p>
              <button className="text-[#88cc14] hover:underline">Learn More</button>
            </motion.div>
            
            <motion.div
              whileHover={{ y: -5 }}
              className="bg-[#1A1F35] p-6 rounded-lg"
            >
              <div className="w-12 h-12 rounded-full bg-[#3b82f6]/10 flex items-center justify-center mb-4">
                <FaGraduationCap className="text-[#3b82f6] text-xl" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Security Courses</h3>
              <p className="text-gray-400 mb-4">
                Explore our comprehensive cybersecurity curriculum
              </p>
              <button className="text-[#3b82f6] hover:underline">Browse Courses</button>
            </motion.div>
            
            <motion.div
              whileHover={{ y: -5 }}
              className="bg-[#1A1F35] p-6 rounded-lg"
            >
              <div className="w-12 h-12 rounded-full bg-[#f59e0b]/10 flex items-center justify-center mb-4">
                <FaGamepad className="text-[#f59e0b] text-xl" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Cyber Range</h3>
              <p className="text-gray-400 mb-4">
                Practice your skills in our virtual cyber range environment
              </p>
              <button className="text-[#f59e0b] hover:underline">Start Training</button>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityHub;
