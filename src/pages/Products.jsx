import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaShoppingCart, FaTshirt, FaTag, FaCoins, FaTimes } from 'react-icons/fa';
import { getProducts } from '../lib/supabase';
import Cart from '../components/Cart';
import Checkout from '../components/Checkout';

function Products() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [cart, setCart] = useState([]);
  const [showCart, setShowCart] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);
  const [addresses, setAddresses] = useState([]);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const data = await getProducts();
        setProducts(data);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const categories = [
    { id: 'all', name: 'All Products', icon: FaTag },
    { id: 'apparel', name: 'Apparel', icon: FaTshirt },
    { id: 'coins', name: 'Coin Packs', icon: FaCoins }
  ];

  const filteredProducts = filter === 'all' 
    ? products 
    : products.filter(product => product.category.toLowerCase() === filter);

  const addToCart = (product, size = null) => {
    setCart(prev => {
      const existingItem = prev.find(item => 
        item.id === product.id && (!size || item.size === size)
      );
      
      if (existingItem) {
        return prev.map(item => 
          item.id === product.id && (!size || item.size === size)
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      }
      
      return [...prev, { ...product, quantity: 1, size }];
    });
    setShowCart(true);
  };

  const updateCartQuantity = (itemId, newQuantity) => {
    if (newQuantity < 1) return;
    setCart(prev => 
      prev.map(item => 
        item.id === itemId ? { ...item, quantity: newQuantity } : item
      )
    );
  };

  const removeFromCart = (itemId) => {
    setCart(prev => prev.filter(item => item.id !== itemId));
  };

  const handleCheckout = () => {
    setShowCart(false);
    setShowCheckout(true);
  };

  const handleAddAddress = async (addressData) => {
    // TODO: Implement address addition
    console.log('Adding address:', addressData);
  };

  const handlePlaceOrder = async (orderData) => {
    // TODO: Implement order placement
    console.log('Placing order:', orderData);
  };

  // Cart/Checkout Modal
  const Modal = ({ children, onClose }) => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto relative"
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <FaTimes className="text-xl" />
        </button>
        <div className="p-6">
          {children}
        </div>
      </motion.div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Header */}
      <div className="bg-black text-white py-12 md:py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              XCerberus Store
            </h1>
            <p className="text-gray-400 text-base md:text-lg mb-8">
              Get official XCerberus merchandise and coin packs to unlock challenges.
            </p>

            {/* Cart Summary Button */}
            <button
              onClick={() => setShowCart(true)}
              className="inline-flex items-center gap-3 bg-primary text-black px-6 py-3 rounded-lg hover:bg-primary-hover transition-colors"
            >
              <FaShoppingCart className="text-lg" />
              <span className="font-bold">{cart.reduce((sum, item) => sum + item.quantity, 0)} items</span>
              <span className="mx-2 text-black/60">|</span>
              <span className="font-bold">₹{cart.reduce((sum, item) => sum + (item.price * item.quantity), 0).toFixed(2)}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="container mx-auto px-4 md:px-6 py-8">
        <div className="flex flex-wrap gap-3 mb-8 justify-center md:justify-start">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setFilter(category.id)}
              className={`flex items-center gap-2 px-4 py-2.5 rounded-full transition-all ${
                filter === category.id
                  ? 'bg-primary text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              }`}
            >
              <category.icon className="text-lg" />
              <span className="font-medium">{category.name}</span>
            </button>
          ))}
        </div>

        {/* Products Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {filteredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6"
              >
                <div className="relative mb-6 aspect-square rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={product.image_url}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                  {product.category === 'Coins' && (
                    <div className="absolute top-3 right-3 bg-primary text-black text-sm font-bold px-3 py-1 rounded-full">
                      Best Value
                    </div>
                  )}
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {product.name}
                </h3>
                <p className="text-gray-600 mb-6 line-clamp-2 text-sm">
                  {product.description}
                </p>

                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold text-primary">
                    ₹{product.price.toFixed(2)}
                  </div>
                  <button
                    onClick={() => addToCart(product)}
                    className="bg-black text-white px-5 py-2.5 rounded-lg hover:bg-gray-800 transition-colors font-medium"
                  >
                    Add to Cart
                  </button>
                </div>

                {product.is_physical && (
                  <div className="mt-6 pt-4 border-t border-gray-100">
                    <div className="flex flex-wrap gap-2">
                      {['S', 'M', 'L', 'XL'].map(size => (
                        <button
                          key={size}
                          onClick={() => addToCart(product, size)}
                          className="min-w-[3rem] px-3 py-2 rounded-lg border border-gray-200 text-sm font-medium hover:border-primary hover:text-primary transition-colors"
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Cart Modal */}
      <AnimatePresence>
        {showCart && (
          <Modal onClose={() => setShowCart(false)}>
            <Cart
              items={cart}
              onUpdateQuantity={updateCartQuantity}
              onRemoveItem={removeFromCart}
              onCheckout={handleCheckout}
            />
          </Modal>
        )}
      </AnimatePresence>

      {/* Checkout Modal */}
      <AnimatePresence>
        {showCheckout && (
          <Modal onClose={() => setShowCheckout(false)}>
            <Checkout
              cart={cart}
              addresses={addresses}
              onAddAddress={handleAddAddress}
              onPlaceOrder={handlePlaceOrder}
            />
          </Modal>
        )}
      </AnimatePresence>
    </div>
  );
}

export default Products;