import React from 'react';
import { useParams } from 'react-router-dom';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { LearningModuleProvider } from '../contexts/LearningModuleContext';
import LearningModuleList from '../components/learning/LearningModuleList';
import LearningModuleDetail from '../components/learning/LearningModuleDetail';

const SimpleLearning = () => {
  const { darkMode } = useGlobalTheme();
  const { moduleId } = useParams();

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <LearningModuleProvider>
          <h1 className="text-3xl font-bold mb-6">
            {moduleId ? 'Learning Module' : 'Learning Modules'}
          </h1>
          
          {moduleId ? (
            <LearningModuleDetail />
          ) : (
            <LearningModuleList />
          )}
        </LearningModuleProvider>
      </div>
    </div>
  );
};

export default SimpleLearning;
