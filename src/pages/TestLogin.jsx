import React from 'react';
import { useNavigate } from 'react-router-dom';
import { loginWithTestAccount } from '../utils/testLogin';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

const TestLogin = () => {
  const navigate = useNavigate();
  const { darkMode } = useGlobalTheme();

  const handleLogin = (tier) => {
    try {
      console.log(`Logging in with ${tier} account...`);
      const result = loginWithTestAccount(tier);
      console.log('Login result:', result);

      // Redirect based on account type
      if (tier === 'admin') {
        console.log('Redirecting to super admin dashboard...');
        navigate('/super-admin', { replace: true });
      } else {
        console.log('Redirecting to simplified dashboard...');
        navigate('/simplified-dashboard', { replace: true });
      }
    } catch (error) {
      console.error('Error during test login:', error);
      alert(`Login failed: ${error.message || 'Unknown error'}`);
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <div className="max-w-md mx-auto">
          <h1 className="text-3xl font-bold mb-6 text-center">Test Login</h1>

          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
            <p className="mb-4 text-center">Select a test account to login:</p>

            <div className="space-y-3">
              <button
                onClick={() => handleLogin('free')}
                className="w-full py-3 px-4 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                Free Tier Account
              </button>

              <button
                onClick={() => handleLogin('premium')}
                className="w-full py-3 px-4 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
              >
                Premium Tier Account
              </button>

              <button
                onClick={() => handleLogin('business')}
                className="w-full py-3 px-4 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors"
              >
                Business Tier Account
              </button>

              <button
                onClick={() => handleLogin('chitti')}
                className="w-full py-3 px-4 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors"
              >
                Chitti's Account
              </button>

              <button
                onClick={() => handleLogin('admin')}
                className="w-full py-3 px-4 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
              >
                Super Admin Account
              </button>
            </div>

            <div className="mt-6 text-center">
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                These are test accounts for demonstration purposes.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestLogin;
