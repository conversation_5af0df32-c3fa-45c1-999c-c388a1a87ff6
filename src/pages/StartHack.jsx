import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaServer, FaLock, FaCode, FaShieldAlt, FaTerminal, FaUserSecret, FaNetworkWired } from 'react-icons/fa';
import ServerBreachGame from '../components/games/ServerBreachGame';

const missions = [
  {
    id: 'server-breach',
    title: 'Server Breach',
    description: "Break into a high-security server by exploiting vulnerabilities.",
    icon: FaServer,
    difficulty: 'Hard',
    points: 500,
    tasks: [
      {
        description: "Run a port scan on the target server",
        command: "nmap -sV *************",
        hint: "Use nmap to discover open ports and services",
        points: 20,
        expectedOutput: "Starting Nmap scan...\nPort 22/tcp open ssh\nPort 80/tcp open http"
      },
      {
        description: "Attempt SSH connection",
        command: "ssh admin@*************",
        hint: "Try connecting via SSH to see what authentication methods are allowed",
        points: 20,
        expectedOutput: "SSH connection attempt...\nPermission denied (publickey,password).\n[!] Note: Server allows password authentication despite config\n[!] Default credentials might work"
      },
      {
        description: "Brute force SSH password",
        command: "hydra -l admin -P /usr/share/wordlists/common.txt ssh://*************",
        hint: "Use Hydra to try common passwords",
        points: 30,
        expectedOutput: "Hydra v9.1 (c) 2020 by van Hauser/THC & David Maciejak\n[DATA] max 16 tasks per 1 server\n[22][ssh] host: *************   login: admin   password: admin123\n[STATUS] attack finished for ************* (valid pair found)"
      }
    ]
  },
  {
    id: 'web-exploit',
    title: 'Web Application Exploit',
    description: "Exploit vulnerabilities in a web application to gain unauthorized access.",
    icon: FaCode,
    difficulty: 'Medium',
    points: 300,
    tasks: [
      {
        description: "Scan web application",
        command: "nikto -h http://*************",
        hint: "Use Nikto to discover web vulnerabilities",
        points: 20,
        expectedOutput: "- Nikto v2.1.6\n- Target IP: *************\n- Web Server: Apache/2.4.41\n[+] Multiple SQL injection vulnerabilities found\n[+] Possible admin interface at /admin"
      }
    ]
  },
  {
    id: 'network-breach',
    title: 'Network Infiltration',
    description: "Infiltrate a secured network and establish persistence.",
    icon: FaNetworkWired,
    difficulty: 'Expert',
    points: 750,
    tasks: [
      {
        description: "Network enumeration",
        command: "nmap -sn ***********/24",
        hint: "Map out the network topology",
        points: 25,
        expectedOutput: "Starting Nmap scan...\nHost *********** is up\nHost ************* is up\nHost ************* is up"
      }
    ]
  }
];

function StartHack() {
  const [selectedMission, setSelectedMission] = useState(null);

  const handleMissionComplete = (missionId) => {
    console.log(`Mission ${missionId} completed`);
    setSelectedMission(null);
  };

  return (
    <div className="min-h-screen bg-cyber-black pt-20">
      {!selectedMission ? (
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 relative inline-block">
              <span className="relative z-10">Choose Your Mission</span>
              <div className="absolute inset-0 bg-[#88cc14]/20 blur-xl -z-10"></div>
            </h1>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              Select your mission and demonstrate your hacking prowess. Each challenge tests different skills and techniques.
            </p>
          </div>

          {/* Mission Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {missions.map((mission, index) => (
              <motion.div
                key={mission.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-xl p-6 hover:border-[#88cc14] transition-all duration-300 group relative overflow-hidden"
              >
                {/* Glowing background effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#88cc14]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                {/* Mission Icon */}
                <div className="mb-6 relative">
                  <div className="w-16 h-16 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                    <mission.icon className="text-[#88cc14] text-2xl" />
                  </div>
                  <div className="absolute top-0 right-0">
                    <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                      mission.difficulty === 'Easy' ? 'bg-green-400/10 text-green-400' :
                      mission.difficulty === 'Medium' ? 'bg-yellow-400/10 text-yellow-400' :
                      'bg-red-400/10 text-red-400'
                    }`}>
                      {mission.difficulty}
                    </span>
                  </div>
                </div>

                {/* Mission Details */}
                <h3 className="text-xl font-bold text-white mb-3 relative z-10">
                  {mission.title}
                </h3>
                <p className="text-gray-400 mb-6 h-24 relative z-10">
                  {mission.description}
                </p>

                {/* Points */}
                <div className="mb-6 relative z-10">
                  <span className="text-[#88cc14] font-bold">{mission.points} points</span>
                </div>

                {/* Start Button */}
                <button
                  onClick={() => setSelectedMission(mission)}
                  className="w-full bg-black/50 text-[#88cc14] border border-[#88cc14]/30 font-bold py-3 px-4 rounded-lg group-hover:bg-[#88cc14] group-hover:text-black transition-all duration-300 relative z-10 flex items-center justify-center gap-2"
                >
                  <FaTerminal className="text-lg" />
                  <span>Start Mission</span>
                </button>
              </motion.div>
            ))}
          </div>
        </div>
      ) : (
        <ServerBreachGame
          mission={selectedMission}
          onComplete={() => handleMissionComplete(selectedMission.id)}
          onBack={() => setSelectedMission(null)}
        />
      )}
    </div>
  );
}

export default StartHack;