import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaSearch, FaTrophy, FaMedal, Fa<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaChartLine } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

// Sample leaderboard data
const USERS = [
  {
    id: 1,
    username: 'CyberNinja',
    points: 12500,
    challenges_completed: 78,
    rank: 1,
    country: 'India',
    badge: 'Master Hacker'
  },
  {
    id: 2,
    username: 'HackMaster',
    points: 11200,
    challenges_completed: 65,
    rank: 2,
    country: 'India',
    badge: 'Elite Hacker'
  },
  {
    id: 3,
    username: 'Security<PERSON>ro',
    points: 10800,
    challenges_completed: 62,
    rank: 3,
    country: 'India',
    badge: 'Elite Hacker'
  },
  {
    id: 4,
    username: 'CodeBreaker',
    points: 9500,
    challenges_completed: 58,
    rank: 4,
    country: 'USA',
    badge: 'Advanced Hacker'
  },
  {
    id: 5,
    username: 'ByteDefender',
    points: 9200,
    challenges_completed: 55,
    rank: 5,
    country: 'UK',
    badge: 'Advanced Hacker'
  },
  {
    id: 6,
    username: 'CipherQueen',
    points: 8900,
    challenges_completed: 52,
    rank: 6,
    country: 'Canada',
    badge: 'Advanced Hacker'
  },
  {
    id: 7,
    username: 'NetRunner',
    points: 8600,
    challenges_completed: 50,
    rank: 7,
    country: 'Germany',
    badge: 'Advanced Hacker'
  },
  {
    id: 8,
    username: 'BinaryWizard',
    points: 8300,
    challenges_completed: 48,
    rank: 8,
    country: 'Australia',
    badge: 'Advanced Hacker'
  },
  {
    id: 9,
    username: 'FirewallBreacher',
    points: 8000,
    challenges_completed: 46,
    rank: 9,
    country: 'Japan',
    badge: 'Intermediate Hacker'
  },
  {
    id: 10,
    username: 'PacketSniffer',
    points: 7800,
    challenges_completed: 45,
    rank: 10,
    country: 'India',
    badge: 'Intermediate Hacker'
  },
  {
    id: 11,
    username: 'RootAccess',
    points: 7500,
    challenges_completed: 43,
    rank: 11,
    country: 'Brazil',
    badge: 'Intermediate Hacker'
  },
  {
    id: 12,
    username: 'ShellShock',
    points: 7200,
    challenges_completed: 41,
    rank: 12,
    country: 'France',
    badge: 'Intermediate Hacker'
  },
  {
    id: 13,
    username: 'ZeroDayHunter',
    points: 7000,
    challenges_completed: 40,
    rank: 13,
    country: 'Russia',
    badge: 'Intermediate Hacker'
  },
  {
    id: 14,
    username: 'ExploitMaster',
    points: 6800,
    challenges_completed: 38,
    rank: 14,
    country: 'China',
    badge: 'Intermediate Hacker'
  },
  {
    id: 15,
    username: 'VulnScanner',
    points: 6500,
    challenges_completed: 36,
    rank: 15,
    country: 'South Korea',
    badge: 'Intermediate Hacker'
  }
];

const SimpleLeaderboard = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [timeFrame, setTimeFrame] = useState('all-time');
  const { darkMode } = useGlobalTheme();

  // Filter users based on search query
  const filteredUsers = USERS.filter(user =>
    user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.country.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.badge.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get rank icon based on position
  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <FaTrophy className="text-yellow-500 text-xl" />;
      case 2:
        return <FaMedal className="text-gray-400 text-xl" />;
      case 3:
        return <FaMedal className="text-amber-700 text-xl" />;
      default:
        return <FaAward className="text-blue-500 text-xl" />;
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold mb-2">Leaderboard</h1>
            <p className="text-gray-400">See how you rank against other cybersecurity enthusiasts</p>
          </div>

          <div className="mt-4 md:mt-0 flex items-center gap-2">
            <Link to="/challenges" className="bg-[#1A1F35] hover:bg-[#252D4A] text-white px-4 py-2 rounded-md transition-colors">
              Challenges
            </Link>
            <Link to="/profile" className="bg-[#88cc14] hover:bg-[#7ab811] text-black px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2">
              <FaUser />
              My Profile
            </Link>
          </div>
        </div>

        {/* Top 3 Users */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 mt-8">
          {filteredUsers.slice(0, 3).map((user, index) => (
            <div
              key={user.id}
              className={`theme-card rounded-xl p-6 ${
                index === 0
                  ? 'border-yellow-500 shadow-lg shadow-yellow-500/10'
                  : index === 1
                    ? 'border-gray-400 shadow-lg shadow-gray-400/10'
                    : 'border-amber-700 shadow-lg shadow-amber-700/10'
              }`}
            >
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-[#0B1120] rounded-full flex items-center justify-center">
                  {getRankIcon(user.rank)}
                </div>
                <div>
                  <h3 className="text-xl font-bold">{user.username}</h3>
                  <p className="text-gray-400">{user.country}</p>
                </div>
              </div>

              <div className="flex justify-between items-center mb-3">
                <span className="text-gray-400">Points:</span>
                <span className="font-bold text-[#88cc14]">{user.points.toLocaleString()}</span>
              </div>

              <div className="flex justify-between items-center mb-3">
                <span className="text-gray-400">Challenges:</span>
                <span className="font-bold">{user.challenges_completed}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Badge:</span>
                <span className="bg-[#0B1120] text-[#88cc14] px-2 py-1 rounded text-sm">{user.badge}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <FaSearch className="text-gray-500" />
              </div>
              <input
                type="text"
                className="theme-input rounded-lg block w-full pl-10 p-2.5 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]"
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex items-center gap-2">
              <div className="theme-card rounded-lg p-2.5 flex items-center gap-2">
                <FaChartLine className="text-gray-500" />
                <select
                  className="bg-transparent theme-text-primary focus:outline-none"
                  value={timeFrame}
                  onChange={(e) => setTimeFrame(e.target.value)}
                >
                  <option value="all-time" className="theme-bg-secondary theme-text-primary">All Time</option>
                  <option value="monthly" className="theme-bg-secondary theme-text-primary">This Month</option>
                  <option value="weekly" className="theme-bg-secondary theme-text-primary">This Week</option>
                  <option value="daily" className="theme-bg-secondary theme-text-primary">Today</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Leaderboard Table */}
        <div className="theme-card rounded-xl overflow-hidden shadow-lg">
          <div className="overflow-x-auto">
            <table className="theme-table w-full">
              <thead>
                <tr className="theme-border border-b">
                  <th className="px-6 py-4 text-left">Rank</th>
                  <th className="px-6 py-4 text-left">User</th>
                  <th className="px-6 py-4 text-left hidden md:table-cell">Country</th>
                  <th className="px-6 py-4 text-right hidden sm:table-cell">Challenges</th>
                  <th className="px-6 py-4 text-right">Points</th>
                  <th className="px-6 py-4 text-right hidden lg:table-cell">Badge</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="theme-border border-b transition-colors hover:theme-bg-tertiary">
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        {user.rank <= 3 ? getRankIcon(user.rank) : <span className="font-bold">{user.rank}</span>}
                      </div>
                    </td>
                    <td className="px-6 py-4 font-medium theme-text-primary">{user.username}</td>
                    <td className="px-6 py-4 hidden md:table-cell theme-text-primary">{user.country}</td>
                    <td className="px-6 py-4 text-right hidden sm:table-cell theme-text-primary">{user.challenges_completed}</td>
                    <td className="px-6 py-4 text-right font-bold text-[#88cc14]">{user.points.toLocaleString()}</td>
                    <td className="px-6 py-4 text-right hidden lg:table-cell">
                      <span className="theme-bg-primary text-[#88cc14] px-2 py-1 rounded text-sm">{user.badge}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleLeaderboard;
