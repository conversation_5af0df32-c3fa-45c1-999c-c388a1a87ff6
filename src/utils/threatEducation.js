// Educational content system for cyber threats
const threatEducation = {
  // Detailed information about each threat type
  threatTypes: {
    'Ransomware': {
      description: 'Malware that encrypts files and demands payment for decryption keys. Ransomware attacks can cripple organizations by making critical data and systems inaccessible until a ransom is paid, often in cryptocurrency.',
      examples: ['Wanna<PERSON>ry', 'Ryuk', 'REvil', '<PERSON><PERSON>', 'LockBit'],
      impacts: [
        'Data loss or encryption',
        'Business disruption and downtime',
        'Financial costs from ransom payments',
        'Recovery and remediation expenses',
        'Reputational damage',
        'Potential data exposure in double-extortion attacks'
      ],
      defenses: [
        'Regular, tested backups stored offline',
        'Security awareness training for employees',
        'Email filtering and attachment scanning',
        'Timely patch management for all systems',
        'Network segmentation to limit spread',
        'Endpoint protection with anti-ransomware capabilities',
        'Principle of least privilege for user accounts'
      ],
      technicalDetails: `
        Ransomware typically enters systems through phishing emails with malicious attachments, 
        vulnerable remote services (like RDP), or malicious downloads. Once executed, 
        it uses strong encryption algorithms (like RSA-2048 or AES-256) to lock files.
        
        Modern ransomware often employs a two-stage attack: data encryption and data exfiltration
        for "double-extortion" where victims are threatened with data leaks if ransom isn't paid.
        
        Many ransomware operations now function as Ransomware-as-a-Service (RaaS), where developers
        create the malware and affiliates distribute it, sharing profits from ransom payments.
      `,
      resources: [
        { title: 'CISA Ransomware Guide', url: 'https://www.cisa.gov/stopransomware/ransomware-guide' },
        { title: 'FBI Ransomware Prevention', url: 'https://www.fbi.gov/scams-and-safety/common-scams-and-crimes/ransomware' },
        { title: 'No More Ransom Project', url: 'https://www.nomoreransom.org/' }
      ]
    },
    'DDoS': {
      description: 'Distributed Denial of Service attacks flood servers, services, or networks with traffic to disrupt normal operations. These attacks overwhelm the target\'s resources, making it unavailable to legitimate users.',
      examples: ['Mirai Botnet Attack', 'Memcached Reflection Attack', 'AWS Shield 2.3 Tbps Attack', 'TCP SYN Flood'],
      impacts: [
        'Service unavailability and downtime',
        'Lost revenue and business opportunities',
        'Customer frustration and dissatisfaction',
        'IT resource strain during mitigation',
        'Potential breach of service level agreements',
        'Distraction from other simultaneous attacks'
      ],
      defenses: [
        'DDoS protection services from cloud providers',
        'Traffic filtering and scrubbing',
        'Rate limiting and connection throttling',
        'Network redundancy and overcapacity',
        'Anycast network distribution',
        'Web application firewalls (WAFs)',
        'Traffic baseline monitoring for early detection'
      ],
      technicalDetails: `
        DDoS attacks overwhelm targets with traffic from multiple sources, often from compromised devices in a botnet.
        
        Common types include:
        - Volumetric attacks: Consume bandwidth with massive traffic (e.g., UDP floods)
        - Protocol attacks: Exploit TCP/IP vulnerabilities (e.g., SYN floods)
        - Application layer attacks: Target specific applications (e.g., HTTP floods, Slowloris)
        
        Amplification attacks use techniques like DNS or NTP reflection, where small queries generate
        large responses directed at the victim, multiplying the attacker's capabilities.
      `,
      resources: [
        { title: 'Cloudflare DDoS Protection', url: 'https://www.cloudflare.com/ddos/' },
        { title: 'NIST DDoS Guide', url: 'https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-189.pdf' },
        { title: 'AWS Shield', url: 'https://aws.amazon.com/shield/' }
      ]
    },
    'Phishing': {
      description: 'Deceptive attempts to steal sensitive information by disguising as trustworthy entities. Phishing attacks trick users into revealing credentials, financial details, or other sensitive information through social engineering.',
      examples: ['Spear Phishing', 'Business Email Compromise (BEC)', 'Clone Phishing', 'Vishing (Voice Phishing)', 'Smishing (SMS Phishing)'],
      impacts: [
        'Credential theft and account compromise',
        'Financial fraud and direct monetary loss',
        'Data breaches from compromised accounts',
        'Malware installation on systems',
        'Reputational damage to impersonated entities',
        'Loss of customer trust'
      ],
      defenses: [
        'Security awareness training for all users',
        'Email filtering and anti-phishing solutions',
        'Multi-factor authentication (MFA)',
        'DMARC, SPF, and DKIM email authentication',
        'URL filtering and web protection',
        'Regular phishing simulations',
        'Reporting mechanisms for suspicious messages'
      ],
      technicalDetails: `
        Phishing attacks use various techniques to appear legitimate:
        - Domain spoofing or lookalike domains (e.g., "g00gle.com" instead of "google.com")
        - Email header forgery to fake sender information
        - HTML/CSS manipulation to hide actual link destinations
        - Stolen branding and design elements from legitimate organizations
        
        Advanced phishing may include:
        - Exploiting current events or crises for urgency
        - Highly targeted information specific to the victim (spear phishing)
        - Multi-stage attacks that build trust before the actual phishing attempt
      `,
      resources: [
        { title: 'CISA Phishing Guidance', url: 'https://www.cisa.gov/topics/cyber-threats-and-advisories/phishing' },
        { title: 'Anti-Phishing Working Group', url: 'https://apwg.org/' },
        { title: 'PhishTank', url: 'https://phishtank.org/' }
      ]
    },
    'Malware': {
      description: 'Malicious software designed to damage, disrupt, or gain unauthorized access to computer systems. Malware comes in many forms and can perform a wide range of harmful actions on infected systems.',
      examples: ['Trojans', 'Worms', 'Spyware', 'Adware', 'Rootkits', 'Keyloggers', 'Cryptominers'],
      impacts: [
        'Data theft or corruption',
        'System performance degradation',
        'Unauthorized access to systems',
        'Privacy violations',
        'Financial losses',
        'Damage to system integrity',
        'Potential gateway for other attacks'
      ],
      defenses: [
        'Endpoint protection platforms (EPP)',
        'Regular software updates and patching',
        'Application whitelisting',
        'User education on safe computing practices',
        'Network monitoring for unusual traffic',
        'Email and web filtering',
        'Regular system scanning'
      ],
      technicalDetails: `
        Malware uses various techniques to infect systems and evade detection:
        
        - Polymorphic code that changes its signature to avoid detection
        - Fileless malware that operates in memory without writing to disk
        - Living-off-the-land techniques using legitimate system tools
        - Encrypted communication with command and control servers
        - Anti-VM and anti-sandbox techniques to evade analysis
        
        Modern malware often employs modular design, where initial infection
        downloads additional payloads based on the target environment.
      `,
      resources: [
        { title: 'CISA Malware Analysis', url: 'https://www.cisa.gov/uscert/malware-analysis-reports' },
        { title: 'VirusTotal', url: 'https://www.virustotal.com/' },
        { title: 'MITRE ATT&CK Malware', url: 'https://attack.mitre.org/techniques/T1587/001/' }
      ]
    },
    'Data Breach': {
      description: 'Unauthorized access to sensitive, protected, or confidential data resulting in exposure. Data breaches involve the compromise of data confidentiality and can affect personal information, intellectual property, or business secrets.',
      examples: ['Equifax Breach (2017)', 'Marriott/Starwood Breach (2018)', 'Capital One Breach (2019)', 'SolarWinds Supply Chain Attack (2020)'],
      impacts: [
        'Exposure of sensitive customer information',
        'Intellectual property theft',
        'Regulatory fines and legal consequences',
        'Identity theft and fraud affecting customers',
        'Remediation costs and technical debt',
        'Long-term reputational damage',
        'Loss of competitive advantage'
      ],
      defenses: [
        'Data encryption (at rest and in transit)',
        'Access control and principle of least privilege',
        'Data loss prevention (DLP) solutions',
        'Security monitoring and breach detection',
        'Incident response planning',
        'Regular security assessments and penetration testing',
        'Employee security awareness training'
      ],
      technicalDetails: `
        Data breaches occur through various attack vectors:
        
        - Exploitation of unpatched vulnerabilities
        - SQL injection and other web application attacks
        - Insider threats and privileged account abuse
        - Social engineering and phishing
        - Third-party/supply chain compromises
        - Misconfigured cloud storage or databases
        
        After initial access, attackers typically move laterally through networks,
        escalate privileges, and exfiltrate data through encrypted channels or
        by hiding data in legitimate traffic (steganography).
      `,
      resources: [
        { title: 'NIST Data Breach Response', url: 'https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-61r2.pdf' },
        { title: 'Privacy Rights Clearinghouse', url: 'https://privacyrights.org/data-breaches' },
        { title: 'Have I Been Pwned', url: 'https://haveibeenpwned.com/' }
      ]
    },
    'Brute Force': {
      description: 'Attempts to crack passwords or find hidden pages by systematically checking all possible combinations. Brute force attacks rely on computational power and persistence rather than vulnerabilities or deception.',
      examples: ['Password Spraying', 'Credential Stuffing', 'Dictionary Attacks', 'Rainbow Table Attacks', 'RDP Brute Force'],
      impacts: [
        'Account compromise and unauthorized access',
        'System lockouts affecting legitimate users',
        'Increased load on authentication systems',
        'Potential for privilege escalation after initial access',
        'Exposure of password policy weaknesses',
        'Gateway to deeper network penetration'
      ],
      defenses: [
        'Strong password policies and complexity requirements',
        'Multi-factor authentication (MFA)',
        'Account lockout policies',
        'CAPTCHA and other human verification',
        'Rate limiting on authentication attempts',
        'Failed login monitoring and alerting',
        'Password managers for unique, complex passwords'
      ],
      technicalDetails: `
        Modern brute force attacks use sophisticated techniques:
        
        - Distributed attacks from multiple IP addresses to avoid lockouts
        - Password spraying (trying a few common passwords across many accounts)
        - Credential stuffing using previously leaked username/password pairs
        - GPU-accelerated password cracking for offline attacks
        - Intelligent word mangling rules based on common password patterns
        
        Attackers often target specific high-value accounts (executives, admins)
        or focus on services with weaker protection (legacy systems, dev environments).
      `,
      resources: [
        { title: 'OWASP Brute Force Prevention', url: 'https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html' },
        { title: 'NIST Password Guidelines', url: 'https://pages.nist.gov/800-63-3/sp800-63b.html' },
        { title: 'Microsoft Account Lockout Best Practices', url: 'https://docs.microsoft.com/en-us/windows/security/threat-protection/security-policy-settings/account-lockout-policy' }
      ]
    },
    'Port Scan': {
      description: 'Reconnaissance technique that probes a server or network for open ports and services. Port scanning is often the first step in identifying potential vulnerabilities in a target system.',
      examples: ['TCP SYN Scan', 'TCP Connect Scan', 'UDP Scan', 'FIN Scan', 'XMAS Scan', 'Null Scan'],
      impacts: [
        'Exposure of network topology and services',
        'Identification of potentially vulnerable services',
        'Information leakage about system versions',
        'Mapping of firewall and security controls',
        'Preparation for more targeted attacks',
        'Network performance degradation during aggressive scans'
      ],
      defenses: [
        'Firewall configuration to block unauthorized scanning',
        'Intrusion detection/prevention systems',
        'Network monitoring for scan activity',
        'Port knocking for sensitive services',
        'Disabling unnecessary services and closing unused ports',
        'Regular vulnerability scanning of your own network',
        'Honeypots to detect and analyze scanning activity'
      ],
      technicalDetails: `
        Port scanning techniques vary in their approach:
        
        - TCP SYN scans send SYN packets but don't complete the handshake
        - TCP Connect scans complete the full TCP handshake
        - UDP scans look for responses or lack of ICMP unreachable messages
        - Stealth scans (FIN, XMAS, NULL) use unusual flag combinations
        
        Advanced scanners like Nmap can determine service versions,
        operating systems, and even attempt to bypass simple firewall rules
        through fragmentation, timing adjustments, and decoys.
      `,
      resources: [
        { title: 'Nmap Network Scanning', url: 'https://nmap.org/book/' },
        { title: 'SANS Port Scanning Detection', url: 'https://www.sans.org/white-papers/33594/' },
        { title: 'Port Scanning Techniques', url: 'https://nmap.org/book/man-port-scanning-techniques.html' }
      ]
    },
    'APT': {
      description: 'Advanced Persistent Threats are sophisticated, targeted cyber attacks where an unauthorized user gains access to a system and remains undetected for an extended period. APTs are typically conducted by nation-states or well-funded threat actors.',
      examples: ['APT29 (Cozy Bear)', 'APT28 (Fancy Bear)', 'Lazarus Group', 'Equation Group', 'Winnti Group'],
      impacts: [
        'Long-term data exfiltration and espionage',
        'Intellectual property theft',
        'Strategic intelligence gathering',
        'Persistent backdoor access to systems',
        'Potential for destructive attacks',
        'Compromise of critical infrastructure',
        'Erosion of trust in affected organizations'
      ],
      defenses: [
        'Threat hunting and advanced monitoring',
        'Network segmentation and zero trust architecture',
        'Endpoint detection and response (EDR)',
        'User and entity behavior analytics (UEBA)',
        'Regular penetration testing and red team exercises',
        'Intelligence-driven security operations',
        'Comprehensive incident response capabilities'
      ],
      technicalDetails: `
        APT attacks follow a sophisticated lifecycle:
        
        1. Initial Reconnaissance: Extensive research on targets and vulnerabilities
        2. Initial Compromise: Often via spear-phishing, supply chain, or zero-day exploits
        3. Establishing Persistence: Backdoors, modified system files, scheduled tasks
        4. Privilege Escalation: Moving from initial access to administrative rights
        5. Internal Reconnaissance: Mapping the network and identifying valuable assets
        6. Lateral Movement: Expanding access across the network
        7. Data Collection & Exfiltration: Stealing targeted information over time
        8. Covering Tracks: Removing evidence and avoiding detection
        
        APTs often use custom malware, living-off-the-land techniques, and
        sophisticated command and control infrastructure with multiple fallback channels.
      `,
      resources: [
        { title: 'MITRE ATT&CK Framework', url: 'https://attack.mitre.org/' },
        { title: 'FireEye APT Reports', url: 'https://www.fireeye.com/current-threats/apt-groups.html' },
        { title: 'US-CERT APT Guidance', url: 'https://www.cisa.gov/uscert/ncas/alerts' }
      ]
    }
  },
  
  // Get educational content for a specific threat
  getContent(threatType) {
    return this.threatTypes[threatType] || {
      description: 'Information about this threat type is not available',
      examples: [],
      impacts: [],
      defenses: [],
      technicalDetails: '',
      resources: []
    };
  },
  
  // Get a "Did You Know" fact about a threat type
  getDidYouKnowFact(threatType) {
    const facts = {
      'Ransomware': [
        'Ransomware attacks increased by 150% in 2021, with an average ransom payment of $220,000.',
        'The healthcare sector is particularly targeted by ransomware, with 66% of healthcare organizations reporting attacks.',
        'Some ransomware groups operate like businesses, with customer service to help victims pay and decrypt files.',
        'The FBI estimates that ransomware costs exceeded $20 billion globally in 2021, up from $11.5 billion in 2019.',
        'The average downtime after a ransomware attack is 21 days, causing significant business disruption.'
      ],
      'DDoS': [
        'The largest DDoS attack ever recorded reached 3.47 Tbps, targeting Azure servers in 2022.',
        'DDoS attacks can be rented on the dark web for as little as $10 per hour.',
        'Some DDoS attacks are used as smokescreens to distract from other attacks happening simultaneously.',
        'The gaming industry is the most targeted sector for DDoS attacks, followed by software and internet services.',
        'The average DDoS attack duration has decreased to 50 minutes, but the frequency has increased dramatically.'
      ],
      'Phishing': [
        '90% of data breaches start with a phishing email, making it the most common attack vector.',
        'Phishing attacks increased by 220% during the COVID-19 pandemic compared to the yearly average.',
        'Business Email Compromise (BEC) scams have cost organizations over $26 billion between 2016 and 2021.',
        'The average click rate for phishing emails is around 4%, but just one click can compromise an organization.',
        'AI-generated phishing emails have a 30% higher success rate than traditional phishing attempts.'
      ],
      'Malware': [
        'Over 450,000 new malware samples are detected every day, with most targeting Windows systems.',
        'Mobile malware attacks increased by 54% in 2021, with banking trojans being the most common type.',
        'Fileless malware attacks, which don\'t write to disk, increased by 888% from 2019 to 2020.',
        '68% of malware hides within encrypted traffic to avoid detection by traditional security tools.',
        'The average time from malware infection to detection (dwell time) is 24 days.'
      ],
      'Data Breach': [
        'The average cost of a data breach reached $4.35 million in 2022, a 13% increase since 2020.',
        'Personally Identifiable Information (PII) is the most commonly exposed data type in breaches.',
        'Organizations with fully deployed security automation save an average of $3.05 million per breach compared to those without.',
        'The healthcare industry has the highest average data breach cost at $10.10 million.',
        'Data breaches caused by compromised credentials take the longest to identify and contain (327 days on average).'
      ],
      'Brute Force': [
        '80% of data breaches involve brute force or stolen credentials.',
        'The most common password is still "123456", used by over 23 million accounts worldwide.',
        'A 12-character password would take about 62 trillion times longer to crack than an 8-character password.',
        'Password spraying attacks have a success rate of 0.5% to 1%, which is significant across large user bases.',
        'RDP brute force attacks increased by 768% in 2020, largely due to remote work adoption.'
      ],
      'Port Scan': [
        'Port scanning is often the first step in 70% of targeted cyber attacks.',
        'The most commonly scanned ports are 22 (SSH), 80 (HTTP), 443 (HTTPS), 445 (SMB), and 3389 (RDP).',
        'Organizations face an average of 1,000 port scans per day, with some experiencing over 10,000.',
        'Sophisticated port scans can evade detection by spreading the scan over days or weeks.',
        'Honeypots detect an average of 5 port scans within 15 minutes of being connected to the internet.'
      ],
      'APT': [
        'APT attacks typically persist in networks for over 200 days before detection.',
        'Nation-state APT groups have targeted COVID-19 vaccine research and healthcare organizations.',
        'The average cost to remediate an APT attack exceeds $9 million for large organizations.',
        'APT groups increasingly target supply chain vendors to gain access to their primary targets.',
        'Some APT malware can survive system reinstallation by infecting firmware or hardware components.'
      ]
    };
    
    const typeFacts = facts[threatType] || [
      'Cybercrime costs are projected to reach $10.5 trillion annually by 2025.',
      'Organizations with strong security awareness training report 70% fewer successful cyber attacks.',
      'The average time to identify a breach is 207 days, with an additional 73 days to contain it.',
      'Zero-day vulnerabilities can sell for up to $2 million on the dark web.',
      'Over 60% of small businesses that suffer a cyber attack go out of business within six months.'
    ];
    
    return typeFacts[Math.floor(Math.random() * typeFacts.length)];
  }
};

export default threatEducation;
