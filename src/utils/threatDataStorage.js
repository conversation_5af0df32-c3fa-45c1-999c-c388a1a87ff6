/**
 * Threat Data Storage Utility
 * 
 * This utility provides functions for storing and retrieving threat data
 * using localStorage for persistence between sessions.
 */

// Maximum number of threats to store
const MAX_STORED_THREATS = 100;

// Initialize the storage
const initStorage = () => {
  try {
    // Check if we have existing data
    const storedThreats = localStorage.getItem('xcerberus_threats');
    if (storedThreats) {
      return JSON.parse(storedThreats);
    }
  } catch (error) {
    console.error('Error initializing threat storage:', error);
  }
  
  // Return empty array if no data or error
  return [];
};

// In-memory cache of threats
let threatCache = initStorage();

// Save threats to localStorage
const saveThreats = () => {
  try {
    localStorage.setItem('xcerberus_threats', JSON.stringify(threatCache));
  } catch (error) {
    console.error('Error saving threat data:', error);
  }
};

// Clean up old threats (older than 24 hours)
const cleanupOldThreats = () => {
  const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
  threatCache = threatCache.filter(threat => threat.timestamp >= oneDayAgo);
  saveThreats();
};

// Run cleanup on initialization
cleanupOldThreats();

const threatDataStorage = {
  /**
   * Add a new threat to storage
   * @param {Object} threat - The threat to add
   * @returns {Object} - The added threat
   */
  addThreat: (threat) => {
    // Ensure the threat has an ID and timestamp
    const threatToAdd = {
      ...threat,
      id: threat.id || `threat-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
      timestamp: threat.timestamp || Date.now()
    };
    
    // Check if this threat already exists (by ID)
    const existingIndex = threatCache.findIndex(t => t.id === threatToAdd.id);
    if (existingIndex >= 0) {
      // Update existing threat
      threatCache[existingIndex] = threatToAdd;
    } else {
      // Add new threat
      threatCache.push(threatToAdd);
      
      // If we have too many threats, remove the oldest ones
      if (threatCache.length > MAX_STORED_THREATS) {
        threatCache.sort((a, b) => b.timestamp - a.timestamp);
        threatCache = threatCache.slice(0, MAX_STORED_THREATS);
      }
    }
    
    // Save to localStorage
    saveThreats();
    
    return threatToAdd;
  },
  
  /**
   * Get threats from storage with optional filtering
   * @param {Object} options - Filter options
   * @param {number} options.timeRange - Time range in milliseconds (e.g., 24 * 60 * 60 * 1000 for 24 hours)
   * @param {string} options.type - Filter by threat type
   * @param {number} options.severity - Filter by severity level
   * @param {string} options.country - Filter by source or target country
   * @param {number} options.limit - Maximum number of threats to return
   * @returns {Array} - Filtered threats
   */
  getThreats: (options = {}) => {
    let filteredThreats = [...threatCache];
    
    // Filter by time range
    if (options.timeRange) {
      const cutoffTime = Date.now() - options.timeRange;
      filteredThreats = filteredThreats.filter(threat => threat.timestamp >= cutoffTime);
    }
    
    // Filter by type
    if (options.type) {
      filteredThreats = filteredThreats.filter(threat => threat.type === options.type);
    }
    
    // Filter by severity
    if (options.severity) {
      filteredThreats = filteredThreats.filter(threat => threat.severity === options.severity);
    }
    
    // Filter by country (source or target)
    if (options.country) {
      filteredThreats = filteredThreats.filter(threat => 
        threat.source.name === options.country || threat.target.name === options.country
      );
    }
    
    // Sort by timestamp (newest first)
    filteredThreats.sort((a, b) => b.timestamp - a.timestamp);
    
    // Limit the number of results
    if (options.limit && options.limit > 0) {
      filteredThreats = filteredThreats.slice(0, options.limit);
    }
    
    return filteredThreats;
  },
  
  /**
   * Get threat statistics
   * @returns {Object} - Statistics about the threats
   */
  getStats: () => {
    // Only consider threats from the last 24 hours
    const recentThreats = threatDataStorage.getThreats({ timeRange: 24 * 60 * 60 * 1000 });
    
    // Count threats by type
    const threatsByType = {};
    recentThreats.forEach(threat => {
      threatsByType[threat.type] = (threatsByType[threat.type] || 0) + 1;
    });
    
    // Count threats by source country
    const threatsByCountry = {};
    recentThreats.forEach(threat => {
      threatsByCountry[threat.source.name] = (threatsByCountry[threat.source.name] || 0) + 1;
    });
    
    // Count threats by severity
    const threatsBySeverity = {
      1: 0, // Low
      2: 0, // Medium
      3: 0  // High
    };
    recentThreats.forEach(threat => {
      threatsBySeverity[threat.severity] = (threatsBySeverity[threat.severity] || 0) + 1;
    });
    
    return {
      total: recentThreats.length,
      threatsByType,
      threatsByCountry,
      threatsBySeverity
    };
  },
  
  /**
   * Clear all threats from storage
   */
  clearThreats: () => {
    threatCache = [];
    saveThreats();
  }
};

export default threatDataStorage;
