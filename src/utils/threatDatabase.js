// Threat database to store and manage threat data
const threatDatabase = {
  threats: [], // All threats from the past 24 hours
  
  // Add a new threat to the database
  addThreat(threat) {
    // Add timestamp if not present
    if (!threat.timestamp) {
      threat.timestamp = new Date().getTime();
    }
    
    // Add unique ID if not present
    if (!threat.id) {
      threat.id = `threat-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    }
    
    this.threats.push(threat);
    
    // Remove threats older than 24 hours
    const oneDayAgo = new Date().getTime() - (24 * 60 * 60 * 1000);
    this.threats = this.threats.filter(t => t.timestamp >= oneDayAgo);
    
    // Save to localStorage for persistence
    this.saveToStorage();
    
    return threat;
  },
  
  // Get threats filtered by various criteria
  getThreats(filters = {}) {
    let filteredThreats = [...this.threats];
    
    // Apply filters (type, severity, region, etc.)
    if (filters.type) {
      filteredThreats = filteredThreats.filter(t => t.type === filters.type);
    }
    
    if (filters.minSeverity) {
      filteredThreats = filteredThreats.filter(t => t.severity >= filters.minSeverity);
    }
    
    if (filters.region) {
      filteredThreats = filteredThreats.filter(t => 
        t.source.name === filters.region || t.target.name === filters.region
      );
    }
    
    if (filters.timeRange) {
      const startTime = new Date().getTime() - filters.timeRange;
      filteredThreats = filteredThreats.filter(t => t.timestamp >= startTime);
    }
    
    return filteredThreats;
  },
  
  // Get threat statistics and aggregations
  getStatistics() {
    const stats = {
      totalThreats: this.threats.length,
      byType: {},
      byCountry: {},
      bySeverity: {1: 0, 2: 0, 3: 0},
      byHour: Array(24).fill(0)
    };
    
    this.threats.forEach(threat => {
      // Count by type
      stats.byType[threat.type] = (stats.byType[threat.type] || 0) + 1;
      
      // Count by country
      stats.byCountry[threat.source.name] = (stats.byCountry[threat.source.name] || 0) + 1;
      stats.byCountry[threat.target.name] = (stats.byCountry[threat.target.name] || 0) + 1;
      
      // Count by severity
      stats.bySeverity[threat.severity] += 1;
      
      // Count by hour
      const hour = new Date(threat.timestamp).getHours();
      stats.byHour[hour] += 1;
    });
    
    return stats;
  },
  
  // Get the most common threat types
  getTopThreatTypes(limit = 3) {
    const stats = this.getStatistics();
    return Object.entries(stats.byType)
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([type, count]) => ({ type, count }));
  },
  
  // Get the most targeted countries
  getTopTargetedCountries(limit = 3) {
    const countryTargets = {};
    
    this.threats.forEach(threat => {
      countryTargets[threat.target.name] = (countryTargets[threat.target.name] || 0) + 1;
    });
    
    return Object.entries(countryTargets)
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([country, count]) => ({ country, count }));
  },
  
  // Save database to localStorage
  saveToStorage() {
    try {
      localStorage.setItem('threatDatabase', JSON.stringify(this.threats));
    } catch (error) {
      console.error('Error saving threat database to localStorage:', error);
    }
  },
  
  // Load database from localStorage
  loadFromStorage() {
    try {
      const stored = localStorage.getItem('threatDatabase');
      if (stored) {
        this.threats = JSON.parse(stored);
        
        // Remove threats older than 24 hours
        const oneDayAgo = new Date().getTime() - (24 * 60 * 60 * 1000);
        this.threats = this.threats.filter(t => t.timestamp >= oneDayAgo);
      }
    } catch (error) {
      console.error('Error loading threat database from localStorage:', error);
      this.threats = [];
    }
  },
  
  // Clear all threats from the database
  clearThreats() {
    this.threats = [];
    this.saveToStorage();
  }
};

// Initialize the database when the module is loaded
threatDatabase.loadFromStorage();

export default threatDatabase;
