/**
 * Dashboard Data Service
 * 
 * Provides utilities for fetching and processing data for dashboard widgets.
 * Handles API calls, data transformation, and caching.
 */

import { SUBSCRIPTION_TIERS } from '../config/subscriptionTiers';

// API endpoints
const API_ENDPOINTS = {
  // Learning endpoints
  LEARNING_PROGRESS: '/api/learning/progress',
  LEARNING_MODULES: '/api/learning/modules',
  LEARNING_ACTIVITY: '/api/learning/activity',
  
  // Challenge endpoints
  CHALLENGE_PROGRESS: '/api/challenges/progress',
  CHALLENGE_LIST: '/api/challenges/list',
  CHALLENGE_ACTIVITY: '/api/challenges/activity',
  
  // Team endpoints
  TEAM_MEMBERS: '/api/team/members',
  TEAM_PROGRESS: '/api/team/progress',
  TEAM_SKILLS: '/api/team/skills',
  
  // Recommendations endpoints
  RECOMMENDATIONS: '/api/recommendations',
  USER_SKILLS: '/api/user/skills',
};

// Cache configuration
const CACHE_CONFIG = {
  ENABLED: true,
  TTL: 5 * 60 * 1000, // 5 minutes in milliseconds
};

// Cache storage
const cache = {
  data: {},
  timestamps: {},
};

/**
 * Make an API request with caching
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} - Response data
 */
const fetchWithCache = async (endpoint, options = {}) => {
  const cacheKey = `${endpoint}:${JSON.stringify(options)}`;
  
  // Check if cached data exists and is still valid
  if (CACHE_CONFIG.ENABLED && 
      cache.data[cacheKey] && 
      (Date.now() - cache.timestamps[cacheKey]) < CACHE_CONFIG.TTL) {
    return cache.data[cacheKey];
  }
  
  try {
    // For now, return mock data instead of making actual API calls
    const mockData = getMockData(endpoint, options);
    
    // Cache the result
    if (CACHE_CONFIG.ENABLED) {
      cache.data[cacheKey] = mockData;
      cache.timestamps[cacheKey] = Date.now();
    }
    
    return mockData;
  } catch (error) {
    console.error(`Error fetching data from ${endpoint}:`, error);
    throw error;
  }
};

/**
 * Clear the cache
 * @param {string} endpoint - Optional specific endpoint to clear
 */
const clearCache = (endpoint = null) => {
  if (endpoint) {
    // Clear cache for specific endpoint
    Object.keys(cache.data).forEach(key => {
      if (key.startsWith(`${endpoint}:`)) {
        delete cache.data[key];
        delete cache.timestamps[key];
      }
    });
  } else {
    // Clear entire cache
    cache.data = {};
    cache.timestamps = {};
  }
};

/**
 * Get mock data for development
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Request options
 * @returns {Object} - Mock data
 */
const getMockData = (endpoint, options = {}) => {
  // Extract parameters from options
  const { timeRange = 'month', userId, teamId } = options.params || {};
  
  switch (endpoint) {
    case API_ENDPOINTS.LEARNING_PROGRESS:
      return getMockLearningProgress(timeRange, userId);
    case API_ENDPOINTS.CHALLENGE_PROGRESS:
      return getMockChallengeProgress(timeRange, userId);
    case API_ENDPOINTS.TEAM_MEMBERS:
      return getMockTeamMembers(teamId);
    case API_ENDPOINTS.TEAM_PROGRESS:
      return getMockTeamProgress(teamId, timeRange);
    case API_ENDPOINTS.RECOMMENDATIONS:
      return getMockRecommendations(userId);
    case API_ENDPOINTS.USER_SKILLS:
      return getMockUserSkills(userId);
    default:
      return { error: 'Endpoint not implemented in mock data' };
  }
};

/**
 * Get mock learning progress data
 * @param {string} timeRange - Time range for data
 * @param {string} userId - User ID
 * @returns {Object} - Mock learning progress data
 */
const getMockLearningProgress = (timeRange, userId) => {
  return {
    modulesCompleted: 12,
    totalModules: 30,
    timeSpent: 840, // 14 hours in minutes
    averageScore: 85,
    streak: 7,
    topSkills: [
      { name: 'Web Security', level: 75 },
      { name: 'Network Security', level: 60 },
      { name: 'Cryptography', level: 45 }
    ],
    recentActivity: [
      { type: 'completion', title: 'Completed "Web Application Security" module', date: '2 days ago', duration: '45m' },
      { type: 'progress', title: 'Started "Advanced Cryptography" module', date: '3 days ago', duration: '30m' },
      { type: 'completion', title: 'Completed "Network Security Basics" module', date: '5 days ago', duration: '1h 15m' }
    ],
    progressByCategory: [
      { name: 'Web Security', completed: 5, total: 8 },
      { name: 'Network Security', completed: 3, total: 7 },
      { name: 'Cryptography', completed: 2, total: 5 },
      { name: 'System Security', completed: 1, total: 6 },
      { name: 'Mobile Security', completed: 1, total: 4 }
    ]
  };
};

/**
 * Get mock challenge progress data
 * @param {string} timeRange - Time range for data
 * @param {string} userId - User ID
 * @returns {Object} - Mock challenge progress data
 */
const getMockChallengeProgress = (timeRange, userId) => {
  return {
    challengesCompleted: 8,
    totalChallenges: 25,
    timeSpent: 960, // 16 hours in minutes
    averageScore: 78,
    successRate: 70,
    pointsEarned: 1250,
    topCategories: [
      { name: 'Web', count: 4, percentage: 80 },
      { name: 'Network', count: 2, percentage: 50 },
      { name: 'Crypto', count: 2, percentage: 40 }
    ],
    recentActivity: [
      { title: 'SQL Injection Challenge', date: '1 day ago', duration: '35m', success: true, points: 150 },
      { title: 'XSS Challenge', date: '3 days ago', duration: '45m', success: true, points: 200 },
      { title: 'Buffer Overflow Challenge', date: '6 days ago', duration: '1h 20m', success: false }
    ],
    difficultyBreakdown: [
      { name: 'Beginner', completed: 5, total: 8 },
      { name: 'Intermediate', completed: 2, total: 10 },
      { name: 'Advanced', completed: 1, total: 7 }
    ]
  };
};

/**
 * Get mock team members data
 * @param {string} teamId - Team ID
 * @returns {Array} - Mock team members data
 */
const getMockTeamMembers = (teamId) => {
  return [
    { 
      id: 1, 
      name: 'John Doe', 
      role: 'Security Analyst', 
      progress: 75, 
      completedModules: 15, 
      totalModules: 20,
      completedChallenges: 12,
      totalChallenges: 25,
      points: 1850
    },
    { 
      id: 2, 
      name: 'Jane Smith', 
      role: 'Network Engineer', 
      progress: 60, 
      completedModules: 12, 
      totalModules: 20,
      completedChallenges: 8,
      totalChallenges: 25,
      points: 1200
    },
    { 
      id: 3, 
      name: 'Mike Johnson', 
      role: 'Developer', 
      progress: 45, 
      completedModules: 9, 
      totalModules: 20,
      completedChallenges: 6,
      totalChallenges: 25,
      points: 950
    },
    { 
      id: 4, 
      name: 'Sarah Williams', 
      role: 'IT Manager', 
      progress: 90, 
      completedModules: 18, 
      totalModules: 20,
      completedChallenges: 20,
      totalChallenges: 25,
      points: 2500
    },
    { 
      id: 5, 
      name: 'David Brown', 
      role: 'Security Specialist', 
      progress: 30, 
      completedModules: 6, 
      totalModules: 20,
      completedChallenges: 4,
      totalChallenges: 25,
      points: 650
    }
  ];
};

/**
 * Get mock team progress data
 * @param {string} teamId - Team ID
 * @param {string} timeRange - Time range for data
 * @returns {Object} - Mock team progress data
 */
const getMockTeamProgress = (teamId, timeRange) => {
  return {
    averageProgress: 60,
    totalModulesCompleted: 60,
    totalChallengesCompleted: 50,
    totalModules: 100,
    totalChallenges: 125,
    topSkills: [
      { name: 'Web Security', level: 75 },
      { name: 'Network Security', level: 60 },
      { name: 'Cryptography', level: 45 }
    ],
    skillGaps: [
      { name: 'Cloud Security', level: 25 },
      { name: 'Mobile Security', level: 30 },
      { name: 'IoT Security', level: 15 }
    ]
  };
};

/**
 * Get mock recommendations data
 * @param {string} userId - User ID
 * @returns {Object} - Mock recommendations data
 */
const getMockRecommendations = (userId) => {
  return {
    modules: [
      { id: 'advanced-web-sec', title: 'Advanced Web Security', description: 'Learn advanced techniques for securing web applications', category: 'web' },
      { id: 'network-forensics', title: 'Network Forensics', description: 'Master the art of network traffic analysis', category: 'network' }
    ],
    challenges: [
      { id: 'csrf-challenge', title: 'CSRF Protection Bypass', difficulty: 'Intermediate', estimatedTime: '1 hour', category: 'web' },
      { id: 'packet-analysis', title: 'Network Packet Analysis', difficulty: 'Intermediate', estimatedTime: '45 min', category: 'network' }
    ],
    simulations: [
      { id: 'enterprise-breach', title: 'Enterprise Network Breach', difficulty: 'Advanced', estimatedTime: '4 hours', category: 'network' },
      { id: 'ransomware-response', title: 'Ransomware Incident Response', difficulty: 'Intermediate', estimatedTime: '3 hours', category: 'security' }
    ],
    careerPaths: [
      { id: 'security-analyst', title: 'Security Analyst Path', description: 'Develop the skills needed to become a security analyst' }
    ]
  };
};

/**
 * Get mock user skills data
 * @param {string} userId - User ID
 * @returns {Object} - Mock user skills data
 */
const getMockUserSkills = (userId) => {
  return {
    'Web Security': 75,
    'Network Security': 60,
    'Cryptography': 45,
    'System Security': 30,
    'Mobile Security': 25
  };
};

// Public API
export default {
  // Learning data
  getLearningProgress: (timeRange = 'month', userId) => 
    fetchWithCache(API_ENDPOINTS.LEARNING_PROGRESS, { params: { timeRange, userId } }),
  
  // Challenge data
  getChallengeProgress: (timeRange = 'month', userId) => 
    fetchWithCache(API_ENDPOINTS.CHALLENGE_PROGRESS, { params: { timeRange, userId } }),
  
  // Team data
  getTeamMembers: (teamId) => 
    fetchWithCache(API_ENDPOINTS.TEAM_MEMBERS, { params: { teamId } }),
  getTeamProgress: (teamId, timeRange = 'month') => 
    fetchWithCache(API_ENDPOINTS.TEAM_PROGRESS, { params: { teamId, timeRange } }),
  
  // Recommendations data
  getRecommendations: (userId) => 
    fetchWithCache(API_ENDPOINTS.RECOMMENDATIONS, { params: { userId } }),
  getUserSkills: (userId) => 
    fetchWithCache(API_ENDPOINTS.USER_SKILLS, { params: { userId } }),
  
  // Cache management
  clearCache,
};
