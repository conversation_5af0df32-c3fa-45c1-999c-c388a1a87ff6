/**
 * Utility function to simulate login with test accounts
 */

export const loginWithTestAccount = (tier) => {
  console.log(`Attempting to login with ${tier} account...`);
  // Define test accounts
  const TEST_ACCOUNTS = {
    free: {
      email: '<EMAIL>',
      password: 'FreeUser123!',
      user_metadata: {
        username: 'free_user',
        full_name: 'Free User',
        subscription_tier: 'free',
        coins: 100
      }
    },
    premium: {
      email: '<EMAIL>',
      password: 'PremiumUser123!',
      user_metadata: {
        username: 'premium_user',
        full_name: 'Premium User',
        subscription_tier: 'premium',
        coins: 500
      }
    },
    business: {
      email: '<EMAIL>',
      password: 'BusinessUser123!',
      user_metadata: {
        username: 'business_user',
        full_name: 'Business User',
        subscription_tier: 'business',
        coins: 1000
      }
    },
    chitti: {
      email: 'chitti.gouth<PERSON><PERSON>@gmail.com',
      password: '<PERSON><PERSON>123!',
      user_metadata: {
        username: 'chitti',
        full_name: '<PERSON><PERSON><PERSON>',
        subscription_tier: 'free',
        coins: 100
      }
    },
    admin: {
      email: '<EMAIL>',
      password: 'Admin123!',
      user_metadata: {
        username: 'admin',
        full_name: 'Super Admin',
        subscription_tier: 'business',
        coins: 9999,
        is_admin: true,
        is_super_admin: true,
        role: 'super_admin'
      }
    }
  };

  // Get the account data
  console.log(`Looking for account with tier: ${tier}`);
  console.log(`Available tiers: ${Object.keys(TEST_ACCOUNTS).join(', ')}`);

  const account = TEST_ACCOUNTS[tier] || TEST_ACCOUNTS.free;
  console.log(`Selected account: ${account.email} (${account.user_metadata.subscription_tier} tier)`);

  // Create mock auth token
  const authToken = {
    access_token: `mock_token_${tier}_${Date.now()}`,
    refresh_token: `mock_refresh_${tier}_${Date.now()}`,
    expires_at: Date.now() + 3600 * 1000 // 1 hour from now
  };

  // Create mock user data
  const userData = {
    id: `user_${tier}_${Date.now()}`,
    email: account.email,
    user_metadata: account.user_metadata,
    created_at: new Date().toISOString()
  };

  // Create mock subscription data
  const subscriptionData = {
    tier: account.user_metadata.subscription_tier,
    coins: account.user_metadata.coins,
    start_date: new Date().toISOString(),
    end_date: account.user_metadata.subscription_tier === 'free'
      ? null
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
  };

  // Store data in localStorage
  localStorage.setItem('supabase.auth.token', JSON.stringify(authToken));
  localStorage.setItem('supabase.auth.user', JSON.stringify(userData));
  localStorage.setItem('user_subscription', JSON.stringify(subscriptionData));

  console.log(`Logged in as ${account.email} (${tier} tier)`);

  return {
    user: userData,
    session: {
      access_token: authToken.access_token
    }
  };
};
