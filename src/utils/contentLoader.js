import { osIntroductionContent } from '../data/content/os/introduction';
import { processManagementContent } from '../data/content/os/process-management';
import { memoryManagementContent } from '../data/content/os/memory-management';
import { fileSystemsContent } from '../data/content/os/file-systems';
import { ioManagementContent } from '../data/content/os/io-management';
import { cpuSchedulingContent } from '../data/content/os/cpu-scheduling';
import { deadlocksContent } from '../data/content/os/deadlocks';
import { deviceManagementContent } from '../data/content/os/device-management';
import { osInterviewContent } from '../data/content/os/os-interview';
import { linuxBasicsContent } from '../data/content/linux/basics';
import { linuxIntroductionContent } from '../data/content/linux/introduction';
import { shellScriptingContent } from '../data/content/linux/shell-scripting';

// Create a map of content by topic ID
const contentMap = {
  'os-introduction': osIntroductionContent,
  'process-management': processManagementContent,
  'memory-management': memoryManagementContent,
  'file-systems': fileSystemsContent,
  'io-management': ioManagementContent,
  'cpu-scheduling': cpuSchedulingContent,
  'deadlocks': deadlocksContent,
  'device-management': deviceManagementContent,
  'os-basics-interview': osInterviewContent,
  'process-thread-interview': osInterviewContent,
  'memory-interview': osInterviewContent,
  'scheduling-interview': osInterviewContent,
  'deadlock-interview': osInterviewContent,
  'filesystem-interview': osInterviewContent,
  'device-io-interview': osInterviewContent,
  'security-interview': osInterviewContent,
  'real-world-scenarios': osInterviewContent,
  'coding-problems': osInterviewContent,
  'linux-basics': linuxBasicsContent,
  'linux-introduction': linuxIntroductionContent,
  'shell-scripting': shellScriptingContent
};

// Sample content for global learning modules
const globalContentMap = {
  'os-introduction': {
    title: 'Introduction to Operating Systems',
    sections: [
      {
        title: 'What is an Operating System?',
        content: [
          {
            type: 'text',
            value: 'An operating system (OS) is system software that manages computer hardware, software resources, and provides common services for computer programs. It acts as an intermediary between users and the computer hardware.'
          },
          {
            type: 'text',
            value: 'The operating system is a critical component in the functioning of a computer system. Without an operating system, a user cannot run an application program on their computer.'
          }
        ]
      },
      {
        title: 'Core Functions of an Operating System',
        content: [
          {
            type: 'text',
            value: 'Operating systems perform several key functions:'
          },
          {
            type: 'text',
            value: '<ul><li><strong>Process Management:</strong> Creating, scheduling, and terminating processes</li><li><strong>Memory Management:</strong> Allocating and deallocating memory space</li><li><strong>File System Management:</strong> Creating, deleting, and organizing files</li><li><strong>Device Management:</strong> Managing device communication</li><li><strong>Security:</strong> Protecting system resources and user data</li></ul>'
          }
        ]
      }
    ]
  }
};

// Fallback content for missing topics
const fallbackContent = {
  title: 'Content Coming Soon',
  description: 'This content is currently being developed and will be available soon.',
  sections: [
    {
      title: 'Under Development',
      content: 'We are working hard to bring you high-quality content for this topic. Please check back later.'
    }
  ],
  practicalLab: {
    title: "Lab Under Development",
    description: "Our team is creating hands-on labs for this topic.",
    tasks: []
  },
  conceptualQuiz: []
};

export const getContent = (topicId) => {
  // First check the global content map
  if (globalContentMap[topicId]) {
    return globalContentMap[topicId];
  }

  // Then check the regular content map
  return contentMap[topicId] || fallbackContent;
};