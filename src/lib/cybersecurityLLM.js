import { supabase } from './supabase';

// This file now uses a mock response system instead of the Gemini API
// to ensure reliable responses for cybersecurity topics

// Response feedback types
export const FEEDBACK_TYPES = {
  HEART: 'heart',   // Save to hot cache
  LIKE: 'like',     // Save to cold cache
  DISLIKE: 'dislike' // Don't save
};

// Cybersecurity knowledge base with predefined responses
export const cybersecurityResponses = {
  // Malware-related responses
  malware: `## Topic Overview
Malware (short for "malicious software") is any software intentionally designed to cause damage to computers, servers, clients, or computer networks. Malware operates by breaching network defenses and exploiting system vulnerabilities.

### Key Points
* Malware is designed with malicious intent to damage or disrupt systems
* Common types include viruses, worms, trojans, ransomware, spyware, and adware
* Malware can spread through email attachments, malicious downloads, or network vulnerabilities
* Modern malware often uses evasion techniques to avoid detection

### Technical Details
Malware operates through various mechanisms depending on its type:

**Viruses**: Attach to legitimate files and replicate when the infected file is executed
**Worms**: Self-replicate and spread across networks without user interaction
**Trojans**: Disguise as legitimate software but contain hidden malicious functions
**Ransomware**: Encrypts victim's data and demands payment for decryption
**Spyware**: Covertly gathers information about users without their knowledge
**Rootkits**: Provide privileged access while hiding their presence

Malware typically follows a kill chain: delivery, exploitation, installation, command and control, and actions on objectives.

### Best Practices
* Keep all software and operating systems updated with security patches
* Use reputable antivirus and anti-malware solutions with real-time protection
* Implement email filtering and web filtering to block malicious content
* Practice the principle of least privilege for user accounts
* Regularly back up important data using the 3-2-1 backup strategy

### Security Implications
Malware poses significant threats to organizations and individuals, including:

* Data theft and intellectual property loss
* Financial damage through fraud or ransomware payments
* System downtime and business disruption
* Damage to reputation and customer trust
* Potential regulatory fines for data breaches

Modern malware is increasingly sophisticated, using techniques like polymorphism, obfuscation, and fileless execution to evade detection.`,

  // SIEM-related responses
  siem: `## Topic Overview
SIEM (Security Information and Event Management) is a comprehensive approach to security management that combines SIM (Security Information Management) and SEM (Security Event Management) functions into one security management system. SIEM technology provides real-time analysis of security alerts generated by applications and network hardware.

### Key Points
* SIEM collects, aggregates, and analyzes security data from multiple sources
* Provides real-time monitoring and incident detection capabilities
* Enables automated alerting and response to security threats
* Helps with compliance reporting and security investigations

### Technical Details
SIEM systems typically include the following components:

**Log Collection**: Gathers security event data from network devices, servers, applications, and security controls
**Normalization**: Converts diverse log formats into a standardized format for analysis
**Correlation Engine**: Analyzes events across multiple sources to identify patterns and potential threats
**Analytics**: Uses statistical analysis, machine learning, and behavioral analysis to detect anomalies
**Alerting System**: Generates notifications based on predefined rules or detected anomalies
**Dashboard**: Provides visualization of security events and metrics
**Storage**: Maintains historical data for compliance and forensic analysis

Modern SIEM solutions often incorporate SOAR (Security Orchestration, Automation and Response) capabilities to automate incident response workflows.

### Best Practices
* Define clear use cases and detection requirements before implementation
* Properly tune the system to reduce false positives while maintaining detection capabilities
* Regularly update correlation rules and detection logic
* Implement proper log source management and monitoring
* Establish incident response procedures for SIEM alerts

### Security Implications
SIEM systems are critical components of a mature security program, providing:

* Enhanced threat detection and reduced dwell time for attackers
* Improved incident response through centralized visibility
* Better compliance management and reporting
* Historical data for forensic investigations
* Metrics for measuring security program effectiveness

However, SIEM systems require significant resources for implementation, tuning, and ongoing management to be effective.`,

  // Firewall-related responses
  firewall: `## Topic Overview
A firewall is a network security device or software that monitors and filters incoming and outgoing network traffic based on predetermined security rules. It establishes a barrier between a trusted internal network and untrusted external networks, such as the internet.

### Key Points
* Firewalls control network traffic based on predefined rules
* They can be hardware appliances, software applications, or cloud-based services
* Modern firewalls provide deep packet inspection and application-level filtering
* Next-generation firewalls (NGFWs) combine traditional firewall capabilities with advanced features

### Technical Details
Firewalls operate at different layers of the OSI model:

**Packet Filtering Firewalls**: Examine packets and enforce rules based on IP addresses, ports, and protocols (Layer 3-4)
**Stateful Inspection Firewalls**: Track the state of active connections and make decisions based on context (Layer 3-4)
**Application Firewalls**: Analyze and filter traffic based on specific applications or services (Layer 7)
**Next-Generation Firewalls**: Combine traditional capabilities with intrusion prevention, deep packet inspection, and application awareness

Firewall rules typically specify source and destination addresses, ports, protocols, and actions (allow, deny, log).

### Best Practices
* Implement a default deny policy and only allow necessary traffic
* Regularly audit and update firewall rules to remove outdated entries
* Use proper change management procedures for firewall modifications
* Implement egress filtering to control outbound traffic
* Deploy firewalls in a defense-in-depth architecture

### Security Implications
Firewalls are fundamental security controls that provide:

* Perimeter defense against unauthorized access
* Network segmentation to limit lateral movement
* Visibility into network traffic patterns
* Compliance with regulatory requirements
* Protection against common network-based attacks

However, firewalls alone are insufficient for complete security and should be part of a comprehensive security strategy.`,

  // Encryption-related responses
  encryption: `## Topic Overview
Encryption is the process of converting information or data into a code to prevent unauthorized access. It transforms readable data (plaintext) into an unreadable format (ciphertext) using mathematical algorithms and keys, which can only be deciphered by authorized parties with the correct decryption key.

### Key Points
* Encryption protects data confidentiality during storage and transmission
* Uses mathematical algorithms and keys to transform data
* Symmetric encryption uses the same key for encryption and decryption
* Asymmetric encryption uses different public and private keys

### Technical Details
Encryption systems generally fall into two categories:

**Symmetric Encryption**: Uses the same key for both encryption and decryption. Examples include AES (Advanced Encryption Standard), DES (Data Encryption Standard), and 3DES (Triple DES).

**Asymmetric Encryption**: Uses a pair of keys - a public key for encryption and a private key for decryption. Examples include RSA, ECC (Elliptic Curve Cryptography), and DSA (Digital Signature Algorithm).

**Hashing**: While not technically encryption, cryptographic hash functions like SHA-256 and MD5 create fixed-length outputs from input data and are often used alongside encryption.

### Best Practices
* Use strong, industry-standard encryption algorithms (AES-256, RSA-2048)
* Implement proper key management procedures
* Encrypt sensitive data both at rest and in transit
* Use TLS/SSL for secure communications
* Regularly update encryption implementations to address vulnerabilities

### Security Implications
Encryption provides critical security benefits:

* Protects sensitive data from unauthorized access
* Maintains data integrity and confidentiality
* Supports compliance with regulations like GDPR, HIPAA, and PCI DSS
* Enables secure communications over untrusted networks
* Provides authentication through digital signatures

However, encryption must be implemented correctly to be effective, as poor key management or weak algorithms can compromise security.`,

  // Default response for other cybersecurity topics
  default: `## Topic Overview
Cybersecurity refers to the practice of protecting systems, networks, and programs from digital attacks. These attacks often aim to access, change, or destroy sensitive information, extort money from users, or interrupt normal business processes.

### Key Points
* Cybersecurity involves protecting against unauthorized access to data and systems
* It requires a multi-layered approach covering people, processes, and technology
* Threats constantly evolve, requiring continuous monitoring and adaptation
* Both technical controls and user awareness are essential components

### Technical Details
Effective cybersecurity strategies typically include multiple layers of protection spread across computers, networks, programs, and data:

**Network Security**: Protecting network infrastructure from unauthorized access and misuse
**Endpoint Security**: Securing end-user devices like computers, laptops, and mobile devices
**Application Security**: Ensuring software and applications are secure through proper design and testing
**Data Security**: Protecting information through encryption, access controls, and backup strategies
**Identity Management**: Ensuring users have appropriate access to resources
**Security Awareness**: Training users to recognize threats and follow security procedures

### Best Practices
* Implement strong authentication and access controls
* Keep systems and software updated with security patches
* Use encryption for sensitive data storage and transmission
* Regularly back up critical data
* Conduct security assessments and penetration testing

### Security Implications
The importance of cybersecurity continues to grow as organizations increasingly rely on digital systems:

* Data breaches can lead to financial losses and reputational damage
* Critical infrastructure protection is essential for national security
* Privacy regulations require organizations to protect personal data
* Security incidents can disrupt business operations and services
* Cyber threats continue to evolve in sophistication and impact

A comprehensive cybersecurity program requires ongoing attention, resources, and adaptation to address emerging threats.`
};

// Generate response using the mock system
export const generateCybersecurityResponse = async (query, language = 'english', userId = null) => {
  try {
    console.log('Generating response for query:', query);

    // Add a delay to simulate processing (3 seconds)
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Determine which response to use based on the query
    const lowerQuery = query.toLowerCase();
    let responseContent = '';
    let category = 'general';

    // Check for specific topics in the query
    if (lowerQuery.includes('malware') || lowerQuery.includes('virus') ||
        lowerQuery.includes('trojan') || lowerQuery.includes('worm') ||
        lowerQuery.includes('ransomware')) {
      responseContent = cybersecurityResponses.malware;
      category = 'malware';
    }
    else if (lowerQuery.includes('siem') || lowerQuery.includes('security information') ||
             lowerQuery.includes('event management')) {
      responseContent = cybersecurityResponses.siem;
      category = 'security';
    }
    else if (lowerQuery.includes('firewall') || lowerQuery.includes('network security') ||
             lowerQuery.includes('packet filter')) {
      responseContent = cybersecurityResponses.firewall;
      category = 'security';
    }
    else if (lowerQuery.includes('encrypt') || lowerQuery.includes('cryptography') ||
             lowerQuery.includes('cipher') || lowerQuery.includes('aes') ||
             lowerQuery.includes('rsa')) {
      responseContent = cybersecurityResponses.encryption;
      category = 'security';
    }
    else {
      // Use default response for other cybersecurity topics
      responseContent = cybersecurityResponses.default;
      category = 'general';
    }

    // Create the response object
    const response = {
      content: responseContent,
      category: category,
      language: language,
      cached: false,
      source: 'mock'
    };

    // Save to database for future reference if user is logged in
    if (userId) {
      try {
        await supabase.from('ai_responses').insert({
          keyword: query.toLowerCase(),
          content: response.content,
          category: response.category,
          domain: 'CYBERSECURITY',
          language: language,
          source: 'mock',
          user_id: userId,
          created_at: new Date().toISOString()
        });
      } catch (dbError) {
        console.error('Error saving response to database:', dbError);
        // Continue even if saving fails
      }
    }

    return response;
  } catch (error) {
    console.error('Error generating response:', error);

    // Even if there's an error, return a default response
    return {
      content: cybersecurityResponses.default,
      category: "general",
      language: language,
      cached: false,
      source: 'fallback'
    };
  }
};

// Handle user feedback - simplified version that just logs the feedback
export const handleResponseFeedback = async (query, response, feedback) => {
  try {
    console.log('Received feedback:', feedback, 'for query:', query);
    return true;
  } catch (error) {
    console.error('Error handling feedback:', error);
    return false;
  }
};