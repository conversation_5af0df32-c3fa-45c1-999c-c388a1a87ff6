import cybersecurityKnowledgeBase from '../data/cybersecurityKnowledgeBase';

/**
 * Local Cybersecurity AI
 *
 * This module provides functions for generating cybersecurity responses
 * using a local knowledge base instead of external APIs.
 */

/**
 * Find the best matching entry in the knowledge base using a more sophisticated matching algorithm
 * @param {string} query - The user's question
 * @returns {object} The best matching response from the knowledge base
 */
const findBestMatch = (query) => {
  // Convert query to lowercase and remove punctuation for case-insensitive matching
  const normalizedQuery = query.toLowerCase().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '');

  // Common acronyms and their expanded forms
  const acronyms = {
    'siem': 'security information and event management',
    'edr': 'endpoint detection and response',
    'xdr': 'extended detection and response',
    'soar': 'security orchestration automation and response',
    'soc': 'security operations center',
    'ids': 'intrusion detection system',
    'ips': 'intrusion prevention system',
    'dlp': 'data loss prevention',
    'iam': 'identity and access management',
    'mfa': 'multi factor authentication',
    'pam': 'privileged access management',
    'cspm': 'cloud security posture management',
    'casb': 'cloud access security broker',
    'ztna': 'zero trust network access',
    'nist': 'national institute of standards and technology',
    'gdpr': 'general data protection regulation',
    'hipaa': 'health insurance portability and accountability act',
    'pci dss': 'payment card industry data security standard',
    'apt': 'advanced persistent threat',
    'c2': 'command and control',
    'iot': 'internet of things',
    'ir': 'incident response',
    'vm': 'vulnerability management',
    'va': 'vulnerability assessment',
    'osint': 'open source intelligence',
    'csrf': 'cross site request forgery',
    'xss': 'cross site scripting',
    'sqli': 'sql injection',
    'rce': 'remote code execution',
    'dos': 'denial of service',
    'ddos': 'distributed denial of service',
    'mitm': 'man in the middle',
    'cve': 'common vulnerabilities and exposures',
    'cvss': 'common vulnerability scoring system',
    'cti': 'cyber threat intelligence',
    'ttp': 'tactics techniques and procedures',
    'ioc': 'indicator of compromise',
    'mssp': 'managed security service provider',
    'mdr': 'managed detection and response',
    'sla': 'service level agreement',
    'vpn': 'virtual private network',
    'ssl': 'secure sockets layer',
    'tls': 'transport layer security',
    'pki': 'public key infrastructure',
    'ca': 'certificate authority',
    'aes': 'advanced encryption standard',
    'rsa': 'rivest shamir adleman',
    'sha': 'secure hash algorithm',
    'hmac': 'hash-based message authentication code',
    'owasp': 'open web application security project',
    'rbac': 'role based access control',
    'abac': 'attribute based access control',
    'dmarc': 'domain-based message authentication reporting and conformance',
    'spf': 'sender policy framework',
    'dkim': 'domain keys identified mail',
    'bec': 'business email compromise',
    'byod': 'bring your own device',
    'ueba': 'user and entity behavior analytics',
    'ndr': 'network detection and response',
    'uba': 'user behavior analytics',
    'sase': 'secure access service edge',
    'sdp': 'software defined perimeter',
    'sdwan': 'software defined wide area network',
    'ot': 'operational technology',
    'ics': 'industrial control system',
    'scada': 'supervisory control and data acquisition',
    'dlp': 'data loss prevention',
    'drm': 'digital rights management',
    'cmmc': 'cybersecurity maturity model certification',
    'iso27001': 'international organization for standardization 27001',
    'soc2': 'service organization control 2',
    'ciso': 'chief information security officer',
    'cio': 'chief information officer',
    'cto': 'chief technology officer',
    'cro': 'chief risk officer',
    'dpo': 'data protection officer',
    'mssp': 'managed security service provider',
    'mdr': 'managed detection and response',
    'ndr': 'network detection and response',
    'uba': 'user behavior analytics',
    'ueba': 'user and entity behavior analytics',
    'sase': 'secure access service edge',
    'sdp': 'software defined perimeter',
    'sdwan': 'software defined wide area network',
    'ot': 'operational technology',
    'ics': 'industrial control system',
    'scada': 'supervisory control and data acquisition'
  };

  // Check for exact matches first
  for (const [key, value] of Object.entries(cybersecurityKnowledgeBase)) {
    if (normalizedQuery === key) {
      return value;
    }
  }

  // Check for acronyms in the query
  for (const [acronym, expanded] of Object.entries(acronyms)) {
    // Special handling for common queries like "what is X"
    if (normalizedQuery.includes('what is ' + acronym) ||
        normalizedQuery.includes('tell me about ' + acronym) ||
        normalizedQuery.includes('explain ' + acronym)) {
      // Only log in development mode
      if (import.meta.env.DEV) {
        console.log('Found direct question about acronym:', acronym);
      }

      // Check if we have a direct match for this acronym
      if (cybersecurityKnowledgeBase[acronym]) {
        return cybersecurityKnowledgeBase[acronym];
      }
    }

    // Check for the acronym anywhere in the query
    if (normalizedQuery.includes(acronym)) {
      // Only log in development mode
      if (import.meta.env.DEV) {
        console.log('Found acronym in query:', acronym);
      }

      // Check if we have a direct match for this acronym
      if (cybersecurityKnowledgeBase[acronym]) {
        return cybersecurityKnowledgeBase[acronym];
      }

      // Check if we have a match for the expanded form
      for (const [key, value] of Object.entries(cybersecurityKnowledgeBase)) {
        if (expanded.includes(key) || key.includes(expanded)) {
          return value;
        }
      }
    }
  }

  // Check for partial matches with scoring
  let bestMatch = null;
  let bestScore = 0;

  for (const [key, value] of Object.entries(cybersecurityKnowledgeBase)) {
    // Skip the default entry when scoring
    if (key === 'default') continue;

    // Calculate match score
    let score = 0;

    // Exact inclusion of the key phrase
    if (normalizedQuery.includes(key)) {
      score += 10;

      // Bonus if it's at the beginning of the query
      if (normalizedQuery.startsWith(key)) {
        score += 5;
      }

      // Bonus if it's a significant portion of the query
      if (key.length > normalizedQuery.length / 3) {
        score += 3;
      }
    }

    // Check for word-by-word matches
    const queryWords = normalizedQuery.split(/\s+/);
    const keyWords = key.split(/\s+/);

    for (const queryWord of queryWords) {
      if (queryWord.length < 3) continue; // Skip very short words

      if (keyWords.includes(queryWord)) {
        score += 2;
      }

      // Check for partial word matches
      for (const keyWord of keyWords) {
        if (keyWord.length < 3) continue; // Skip very short words

        if (queryWord.includes(keyWord) || keyWord.includes(queryWord)) {
          score += 1;
        }
      }
    }

    // Update best match if we found a better score
    if (score > bestScore) {
      bestScore = score;
      bestMatch = value;
    }
  }

  // Only log in development mode
  if (import.meta.env.DEV) {
    console.log('Best match score:', bestScore, 'for query:', query);
  }

  // Calculate confidence score (0-1) based on match quality
  const confidence = bestScore > 10 ? 0.9 :
                    bestScore > 7 ? 0.8 :
                    bestScore > 5 ? 0.7 :
                    bestScore > 3 ? 0.6 :
                    bestScore > 1 ? 0.4 : 0.2;

  // Only log in development mode
  if (import.meta.env.DEV) {
    console.log('Confidence score:', confidence, 'for query:', query);
  }

  // Return the best match if we found one with a reasonable score
  if (bestMatch && bestScore >= 3) {
    // Add confidence to the response
    return {
      ...bestMatch,
      confidence
    };
  }

  // For very low scores, use the fallback response which asks for clarification
  if (bestScore < 1) {
    return {
      ...cybersecurityKnowledgeBase.fallback,
      confidence
    };
  }

  // Return default response if no good match found but score is not terrible
  return {
    ...cybersecurityKnowledgeBase.default,
    confidence
  };
};

/**
 * Generate a cybersecurity response based on the query
 * @param {string} query - The user's question
 * @param {string} language - The language to respond in (currently only supports English)
 * @param {object|null} userContext - Optional user context for personalization
 * @returns {object} Response object with content, category, and language
 */
export const generateLocalCybersecurityResponse = async (query, language = 'english', userContext = null) => {
  try {
    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('Generating response for query:', query);
    }

    // Preprocess the query to extract key concepts
    const preprocessedQuery = preprocessQuery(query);

    // Find the best matching response in the knowledge base
    const response = findBestMatch(preprocessedQuery.processedQuery);

    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('Found matching response for category:', response.category);
      console.log('Extracted key concepts:', preprocessedQuery.keyTerms);
    }

    // Add personalization if user context is available
    let content = response.content;

    // Enhance the response with extracted key terms if confidence is high enough
    if (response.confidence > 0.6 && preprocessedQuery.keyTerms.length > 0) {
      content = enhanceResponseWithKeyTerms(content, preprocessedQuery.keyTerms, query);
    }

    if (userContext) {
      // Add personalized recommendations based on user's learning progress
      content += "\n\n### Personalized Recommendations";

      if (userContext.learningProgress && userContext.learningProgress.length > 0) {
        content += "\nBased on your learning progress, you might be interested in:";
        userContext.learningProgress.slice(0, 2).forEach(module => {
          content += `\n* Continuing "${module.module_name}" (${module.progress_percentage}% complete)`;
        });
      }

      if (userContext.challengeProgress && userContext.challengeProgress.length > 0) {
        content += "\n\nChallenges you might want to try:";
        userContext.challengeProgress.slice(0, 2).forEach(challenge => {
          content += `\n* "${challenge.challenge_name}" (${challenge.status})`;
        });
      }
    }

    return {
      content,
      category: response.category || "general",
      language: language || "english",
      cached: false,
      source: "local"
    };
  } catch (error) {
    console.error('Error generating local response:', error);
    return {
      content: "I apologize, but I encountered an error processing your request. Please try again with a different question.",
      category: "error",
      language,
      cached: false,
      source: "local"
    };
  }
};

/**
 * Determine category based on query content
 * @param {string} query - The user's question
 * @returns {string} Category name
 */
/**
 * Preprocess a query to extract key terms and clean it for matching
 * @param {string} query - The user's question
 * @returns {object} Object with processed query and extracted key terms
 */
const preprocessQuery = (query) => {
  // Convert to lowercase and remove punctuation
  const processedQuery = query.toLowerCase().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '');

  // Remove common question starters
  const cleanedQuery = processedQuery
    .replace(/^(what is|what are|how does|how do|can you|could you|please|explain|tell me about|describe)/i, '')
    .trim();

  // Extract key cybersecurity terms
  const keyTerms = [];

  // Common cybersecurity terms to look for
  const securityTerms = [
    'firewall', 'encryption', 'malware', 'virus', 'ransomware', 'phishing',
    'ddos', 'injection', 'xss', 'csrf', 'authentication', 'authorization',
    'vulnerability', 'exploit', 'patch', 'zero-day', 'backdoor', 'trojan',
    'worm', 'botnet', 'keylogger', 'spyware', 'rootkit', 'siem', 'edr',
    'dlp', 'ips', 'ids', 'waf', 'vpn', 'mfa', '2fa', 'encryption', 'hash',
    'ssl', 'tls', 'certificate', 'penetration', 'pentest', 'red team',
    'blue team', 'threat', 'risk', 'compliance', 'audit', 'forensics',
    'incident', 'response', 'soc', 'security operations', 'zero trust'
  ];

  // Check for security terms in the query
  for (const term of securityTerms) {
    if (processedQuery.includes(term)) {
      keyTerms.push(term);
    }
  }

  return {
    processedQuery: cleanedQuery || processedQuery, // Use cleaned if available, otherwise original
    keyTerms
  };
};

/**
 * Enhance a response with key terms extracted from the query
 * @param {string} content - The original response content
 * @param {string[]} keyTerms - The key terms extracted from the query
 * @param {string} originalQuery - The original user query
 * @returns {string} Enhanced response content
 */
const enhanceResponseWithKeyTerms = (content, keyTerms, originalQuery) => {
  // Don't modify the content if it already seems personalized to the query
  if (content.includes(originalQuery.substring(0, 15))) {
    return content;
  }

  // Add a personalized introduction if key terms were found
  if (keyTerms.length > 0) {
    const keyTermsText = keyTerms.join(', ');

    // Check if the content already has a heading
    if (content.startsWith('##')) {
      // Insert after the first heading
      const firstLineEnd = content.indexOf('\n');
      if (firstLineEnd !== -1) {
        return content.substring(0, firstLineEnd + 1) +
               `\nRegarding your question about ${keyTermsText}, here's what you need to know:\n\n` +
               content.substring(firstLineEnd + 1);
      }
    }

    // Otherwise add to the beginning
    return `## ${keyTerms[0].charAt(0).toUpperCase() + keyTerms[0].slice(1)} Information\n\nRegarding your question about ${keyTermsText}, here's what you need to know:\n\n${content}`;
  }

  return content;
};

/**
 * Determine category based on query content
 * @param {string} query - The user's question
 * @returns {string} Category name
 */
export const determineCategoryFromQuery = (query) => {
  const lowerQuery = query.toLowerCase();

  if (lowerQuery.includes('hack') || lowerQuery.includes('attack') ||
      lowerQuery.includes('exploit') || lowerQuery.includes('vulnerability')) {
    return 'security';
  }

  if (lowerQuery.includes('challenge') || lowerQuery.includes('ctf') ||
      lowerQuery.includes('puzzle') || lowerQuery.includes('problem')) {
    return 'challenges';
  }

  if (lowerQuery.includes('code') || lowerQuery.includes('program') ||
      lowerQuery.includes('script') || lowerQuery.includes('development')) {
    return 'technical';
  }

  return 'general';
};
