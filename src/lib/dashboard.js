import { supabase } from './supabase';

export const fetchDashboardData = async (userId) => {
  try {
    const [
      profileResponse,
      subscriptionResponse,
      coinsResponse,
      challengesResponse,
      learningResponse,
      activityResponse
    ] = await Promise.all([
      // Get user profile
      supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single(),

      // Get subscription
      supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans (*)
        `)
        .eq('user_id', userId)
        .single(),

      // Get coins
      supabase
        .from('user_coins')
        .select('*')
        .eq('user_id', userId)
        .single(),

      // Get challenges
      supabase
        .from('challenge_submissions')
        .select(`
          *,
          challenges (*)
        `)
        .eq('user_id', userId)
        .order('submission_time', { ascending: false }),

      // Get learning progress
      supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false }),

      // Get recent activity
      supabase
        .from('user_activity')
        .select(`
          *,
          challenges (*)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10)
    ]);

    return {
      profile: profileResponse.data,
      subscription: subscriptionResponse.data,
      coins: coinsResponse.data,
      challenges: challengesResponse.data || [],
      learningProgress: learningResponse.data || [],
      recentActivity: activityResponse.data || [],
      completedChallenges: (challengesResponse.data || []).filter(c => c.status === 'completed').length,
      totalPoints: (challengesResponse.data || []).reduce((sum, c) => sum + (c.points_earned || 0), 0)
    };
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    throw error;
  }
};

export const updateUserProgress = async (userId, data) => {
  try {
    const { error } = await supabase
      .from('user_activity')
      .insert([{
        user_id: userId,
        activity_type: data.type,
        description: data.description,
        points_earned: data.points || 0,
        created_at: new Date().toISOString()
      }]);

    if (error) throw error;
  } catch (error) {
    console.error('Error updating progress:', error);
    throw error;
  }
};

export const trackModuleCompletion = async (userId, moduleId) => {
  try {
    const { error } = await supabase
      .from('learning_progress')
      .insert([{
        user_id: userId,
        module_id: moduleId,
        completed_at: new Date().toISOString()
      }]);

    if (error) throw error;
  } catch (error) {
    console.error('Error tracking module completion:', error);
    throw error;
  }
};

export const getRecommendations = async (userId) => {
  try {
    // Get user's completed challenges and learning progress
    const [challengesResponse, learningResponse] = await Promise.all([
      supabase
        .from('challenge_submissions')
        .select('*, challenges(*)')
        .eq('user_id', userId)
        .eq('status', 'completed'),
      
      supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', userId)
    ]);

    const completedChallenges = challengesResponse.data || [];
    const learningProgress = learningResponse.data || [];

    // Analyze patterns
    const patterns = {
      strengths: [],
      weaknesses: [],
      recommendedPaths: [],
      nextChallenges: []
    };

    // Analyze completed challenges
    const categories = completedChallenges.reduce((acc, c) => {
      acc[c.challenges?.category] = (acc[c.challenges?.category] || 0) + 1;
      return acc;
    }, {});

    // Find strengths (categories with high completion rates)
    patterns.strengths = Object.entries(categories)
      .filter(([_, count]) => count >= 3)
      .map(([category]) => category);

    // Find weaknesses (categories with low completion rates)
    patterns.weaknesses = Object.entries(categories)
      .filter(([_, count]) => count < 2)
      .map(([category]) => category);

    // Recommend next challenges
    patterns.nextChallenges = [
      { category: 'Web Security', recommendedDifficulty: 'Medium' },
      { category: 'Network Security', recommendedDifficulty: 'Hard' }
    ];

    return patterns;
  } catch (error) {
    console.error('Error getting recommendations:', error);
    throw error;
  }
};