import { supabase } from './supabase';
import { startSubscription } from './subscription';

/**
 * Upgrade a user's subscription to a new plan
 * @param {string} newPlanName - The name of the new plan (Free, Premium, Business)
 * @param {boolean} trackUpgrade - Whether to track the upgrade in analytics
 * @returns {Promise<Object>} - The updated subscription data
 */
export const upgradeSubscription = async (newPlanName, trackUpgrade = true) => {
  try {
    // Get current user session
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    // Get current subscription
    const { data: currentSubscription } = await supabase
      .from('user_subscriptions')
      .select(`
        *,
        subscription_plans (name)
      `)
      .eq('user_id', session.user.id)
      .single();

    const currentPlanName = currentSubscription?.subscription_plans?.name || 'Free';

    // Don't process if already on the requested plan
    if (currentPlanName === newPlanName) {
      return currentSubscription;
    }

    // Start the new subscription
    const updatedSubscription = await startSubscription(newPlanName);

    // Track the upgrade if requested
    if (trackUpgrade) {
      await trackSubscriptionChange(currentPlanName, newPlanName);
    }

    return updatedSubscription;
  } catch (error) {
    console.error('Error upgrading subscription:', error);
    throw error;
  }
};

/**
 * Track a subscription change in the analytics
 * @param {string} fromPlan - The previous plan
 * @param {string} toPlan - The new plan
 * @returns {Promise<void>}
 */
export const trackSubscriptionChange = async (fromPlan, toPlan) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) return;

    // Record the subscription change
    await supabase
      .from('subscription_changes')
      .insert({
        user_id: session.user.id,
        from_plan: fromPlan,
        to_plan: toPlan,
        change_time: new Date().toISOString()
      });

    // Also track in user activity for dashboard display
    await supabase
      .from('user_activity')
      .insert({
        user_id: session.user.id,
        activity_type: 'subscription_change',
        description: `Upgraded from ${fromPlan} to ${toPlan}`,
        created_at: new Date().toISOString()
      });
  } catch (error) {
    console.error('Error tracking subscription change:', error);
    // Don't throw - this is a non-critical operation
  }
};

/**
 * Check if a user can access a specific dashboard
 * @param {string} dashboardType - The dashboard type (Free, Premium, Business)
 * @returns {Promise<boolean>} - Whether the user can access the dashboard
 */
export const canAccessDashboard = async (dashboardType) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) return false;

    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select(`
        *,
        subscription_plans (name)
      `)
      .eq('user_id', session.user.id)
      .single();

    if (!subscription || subscription.status !== 'active') {
      return dashboardType === 'Free';
    }

    const currentPlan = subscription.subscription_plans?.name || 'Free';

    // Define access levels
    const accessLevels = {
      'Free': ['Free'],
      'Premium': ['Free', 'Premium'],
      'Business': ['Free', 'Premium', 'Business']
    };

    return accessLevels[currentPlan]?.includes(dashboardType) || false;
  } catch (error) {
    console.error('Error checking dashboard access:', error);
    return false;
  }
};
