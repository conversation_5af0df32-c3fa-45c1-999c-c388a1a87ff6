import { supabase } from './supabase';

// Get challenge summary with all stats
export const getChallengeSummary = async (challengeId) => {
  try {
    const [attemptsData, completionsData, uniqueUsersData, avgTimeData] = await Promise.all([
      // Total attempts
      supabase
        .from('challenge_attempts')
        .select('*', { count: 'exact' })
        .eq('challenge_id', challengeId),
      
      // Successful completions
      supabase
        .from('challenge_attempts')
        .select('*', { count: 'exact' })
        .eq('challenge_id', challengeId)
        .eq('status', 'success'),
      
      // Unique users who attempted
      supabase
        .from('challenge_attempts')
        .select('user_id', { count: 'exact', distinct: true })
        .eq('challenge_id', challengeId),
      
      // Average completion time
      supabase
        .from('challenge_attempts')
        .select('execution_time')
        .eq('challenge_id', challengeId)
        .eq('status', 'success')
    ]);

    // Calculate average completion time
    const avgTime = avgTimeData.data?.reduce((sum, attempt) => 
      sum + attempt.execution_time, 0) / (avgTimeData.data?.length || 1);

    return {
      attempts: attemptsData.count || 0,
      completions: completionsData.count || 0,
      uniqueUsers: uniqueUsersData.count || 0,
      successRate: attemptsData.count ? 
        Math.round((completionsData.count / attemptsData.count) * 100) : 0,
      averageTime: avgTime ? Math.round(avgTime / 1000 / 60) : 0 // in minutes
    };
  } catch (error) {
    console.error('Error getting challenge summary:', error);
    return {
      attempts: 0,
      completions: 0,
      uniqueUsers: 0,
      successRate: 0,
      averageTime: 0
    };
  }
};

// Get challenge attempts with detailed stats
export const getChallengeAttempts = async (challengeId) => {
  try {
    const { data: analytics, error } = await supabase
      .from('challenge_analytics')
      .select('*')
      .eq('challenge_id', challengeId)
      .single();

    if (error) throw error;

    return {
      totalAttempts: analytics.total_attempts,
      completions: analytics.successful_attempts,
      successRate: analytics.success_rate,
      averageTime: analytics.average_completion_time,
      difficultyRating: analytics.difficulty_rating,
      popularTechniques: analytics.popular_techniques,
      commonMistakes: analytics.common_mistakes
    };
  } catch (error) {
    console.error('Error getting challenge attempts:', error);
    return {
      totalAttempts: 0,
      completions: 0,
      successRate: 0,
      averageTime: 0,
      difficultyRating: 0,
      popularTechniques: [],
      commonMistakes: []
    };
  }
};

// Track challenge attempt
export const trackAttempt = async (challengeId, userId, payload, result) => {
  try {
    const startTime = Date.now();
    const { error } = await supabase
      .from('challenge_attempts')
      .insert({
        challenge_id: challengeId,
        user_id: userId,
        payload,
        result,
        execution_time: Date.now() - startTime,
        status: result.success ? 'success' : 'failure',
        error_message: result.error
      });

    if (error) throw error;
  } catch (error) {
    console.error('Error tracking attempt:', error);
    throw error;
  }
};

// Get challenge feedback
export const getChallengeFeedback = async (challengeId) => {
  try {
    const { data, error } = await supabase
      .from('challenge_feedback')
      .select(`
        *,
        users (
          username,
          avatar_url
        )
      `)
      .eq('challenge_id', challengeId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting challenge feedback:', error);
    return [];
  }
};

// Submit challenge feedback
export const submitFeedback = async (challengeId, userId, feedback) => {
  try {
    const { error } = await supabase
      .from('challenge_feedback')
      .insert({
        challenge_id: challengeId,
        user_id: userId,
        rating: feedback.rating,
        difficulty_rating: feedback.difficultyRating,
        feedback_text: feedback.text
      });

    if (error) throw error;
  } catch (error) {
    console.error('Error submitting feedback:', error);
    throw error;
  }
};