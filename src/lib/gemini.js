import { supabase } from './supabase';

const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

// Rate limiting configuration
const RATE_LIMIT = 50; // requests per minute
const RATE_WINDOW = 60 * 1000; // 1 minute in milliseconds
let requestCount = 0;
let windowStart = Date.now();

// Cache configuration
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const responseCache = new Map();

// Check rate limit
const checkRateLimit = () => {
  const now = Date.now();
  if (now - windowStart > RATE_WINDOW) {
    requestCount = 0;
    windowStart = now;
  }
  return requestCount < RATE_LIMIT;
};

// Get cached response
const getCachedResponse = (prompt) => {
  const cached = responseCache.get(prompt);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.response;
  }
  return null;
};

// Cache response
const cacheResponse = (prompt, response) => {
  responseCache.set(prompt, {
    response,
    timestamp: Date.now()
  });
};

// Get AI response with caching and rate limiting
export const getAIResponse = async (input) => {
  try {
    // Check cache first
    const cached = getCachedResponse(input);
    if (cached) {
      return cached;
    }

    // Check rate limit
    if (!checkRateLimit()) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    // First try to get response from local database using exact match
    try {
      const { data: exactMatch, error: exactError } = await supabase
        .from('ai_responses')
        .select('*')
        .eq('keyword', input.toLowerCase())
        .limit(1)
        .single();

      if (!exactError && exactMatch) {
        return exactMatch;
      }
    } catch (exactMatchError) {
      console.log('No exact match found, trying fuzzy search');
    }

    // Try fuzzy search if exact match fails
    try {
      const { data: fuzzyMatches, error: fuzzyError } = await supabase
        .from('ai_responses')
        .select('*')
        .ilike('keyword', `%${input.toLowerCase()}%`)
        .limit(5);

      if (!fuzzyError && fuzzyMatches && fuzzyMatches.length > 0) {
        // Return the first match
        return fuzzyMatches[0];
      }
    } catch (fuzzySearchError) {
      console.log('No fuzzy matches found, generating new response');
    }

    // If no local response, call Gemini API
    requestCount++;

    try {
      // Prepare the request to Gemini API
      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: `You are a cybersecurity expert assistant. Provide a detailed, educational response to the following question about cybersecurity: "${input}"`
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Extract the response text
      const responseText = data.candidates?.[0]?.content?.parts?.[0]?.text ||
        'I apologize, but I was unable to generate a response. Please try again with a different question.';

      const aiResponse = {
        content: responseText,
        category: 'cybersecurity'
      };

      return aiResponse;
    } catch (apiError) {
      console.error('Error calling Gemini API:', apiError);

      // Try to get response from database as a last resort
      try {
        const { data: fuzzyMatches, error: fuzzyError } = await supabase
          .from('ai_responses')
          .select('*')
          .ilike('keyword', `%${input.toLowerCase()}%`)
          .limit(1);

        if (!fuzzyError && fuzzyMatches && fuzzyMatches.length > 0) {
          // Return the match from database
          return fuzzyMatches[0];
        }
      } catch (dbError) {
        console.error('Error searching database for fallback response:', dbError);
      }

      // If all else fails, throw an error to be handled by the caller
      throw new Error('Unable to connect to AI service. Please try again later.');
    }
  } catch (error) {
    console.error('Error getting AI response:', error);

    // Default response if all else fails
    return {
      content: `I understand you're asking about "${input}". Could you please provide more details or rephrase your question? I want to give you the most accurate and helpful response.`,
      category: 'general'
    };
  }
};

// Save chat message with error handling and retries
export const saveChatMessage = async (userId, message, retries = 3) => {
  try {
    const { data, error } = await supabase
      .from('chat_messages')
      .insert([{
        user_id: userId,
        type: message.type,
        content: message.content,
        category: message.category || null,
        created_at: new Date().toISOString()
      }]);

    if (error) {
      console.error("Supabase request failed", error);
      throw error;
    }
    return data;
  } catch (error) {
    console.error('Error saving chat message:', error);
    if (retries > 0) {
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));
      return saveChatMessage(userId, message, retries - 1);
    }
    return null;
  }
};

// Get chat history with pagination
export const getChatHistory = async (userId, page = 1, limit = 50) => {
  try {
    const { data, error, count } = await supabase
      .from('chat_messages')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw error;

    return {
      messages: data || [],
      total: count || 0,
      page,
      limit
    };
  } catch (error) {
    console.error('Error fetching chat history:', error);
    return {
      messages: [],
      total: 0,
      page,
      limit
    };
  }
};