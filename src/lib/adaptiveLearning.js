import { GoogleGenerativeAI } from "@google/generative-ai";
import { supabase } from './supabase';
import * as brain from 'brain.js';

// Initialize Gemini API
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const genAI = new GoogleGenerativeAI(API_KEY);

// Neural network for learning style prediction
const learningStyleNetwork = new brain.NeuralNetwork({
  hiddenLayers: [10, 8],
});

// User performance tracking
export class AdaptiveLearningSystem {
  constructor() {
    this.userProfile = null;
    this.sessionData = {};
  }

  async initialize(userId) {
    // Load user profile and history
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();

    this.userProfile = profile;
  }

  async generatePersonalizedContent(topic, performance) {
    try {
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
      
      const prompt = `Generate personalized learning content for:
Topic: ${topic}
User Performance Level: ${performance}
Learning Style: ${this.userProfile?.learning_style || 'visual'}

Include:
1. Concept explanation
2. Examples
3. Practice exercises
4. Visual aids description
5. Interactive elements`;

      const result = await model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Error generating content:', error);
      throw error;
    }
  }

  async saveProgress(data) {
    try {
      const { error } = await supabase
        .from('learning_progress')
        .insert({
          user_id: this.userProfile.id,
          topic: data.topic,
          score: data.score,
          time_spent: data.timeSpent,
          completed_at: new Date()
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error saving progress:', error);
      throw error;
    }
  }

  async getRecommendations() {
    try {
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
      
      const prompt = `Based on the user's learning history, suggest next steps.
Current topic: ${this.sessionData.currentTopic}
Strengths: ${this.sessionData.strengths.join(', ')}
Areas for improvement: ${this.sessionData.areasToImprove.join(', ')}

Provide:
1. Next recommended topics
2. Practice exercises
3. Additional resources
4. Learning strategy adjustments`;

      const result = await model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Error getting recommendations:', error);
      throw error;
    }
  }
}

export const adaptiveLearning = new AdaptiveLearningSystem();