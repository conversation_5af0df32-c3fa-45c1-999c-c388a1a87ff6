import { supabase } from './supabase';

// Get chat messages for a user
export const getChatMessages = async (userId) => {
  try {
    const { data, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching chat messages:', error);
    return [];
  }
};

// Save a new chat message
export const saveChatMessage = async (userId, message) => {
  try {
    const { data, error } = await supabase
      .from('chat_messages')
      .insert([{
        user_id: userId,
        type: message.type,
        content: message.content,
        category: message.category,
        created_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error saving chat message:', error);
    return null;
  }
};

// Get AI responses from local database
export const getAIResponse = async (input) => {
  try {
    const { data, error } = await supabase
      .from('ai_responses')
      .select('*')
      .textSearch('keywords', input.toLowerCase())
      .limit(1)
      .single();

    if (error) {
      // If no exact match found, try fuzzy search
      const { data: fuzzyData, error: fuzzyError } = await supabase
        .from('ai_responses')
        .select('*')
        .ilike('keywords', `%${input.toLowerCase()}%`)
        .limit(1)
        .single();

      if (fuzzyError) {
        return {
          content: `I understand you're asking about "${input}". Could you please provide more details or rephrase your question? I want to give you the most accurate and helpful response.`,
          category: 'general'
        };
      }

      return fuzzyData;
    }

    return data;
  } catch (error) {
    console.error('Error getting AI response:', error);
    return {
      content: "I apologize, but I'm having trouble processing your request. Could you try asking again?",
      category: 'general'
    };
  }
};