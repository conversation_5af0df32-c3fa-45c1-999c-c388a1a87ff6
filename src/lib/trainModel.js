import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { GoogleGenerativeAI } from "@google/generative-ai";
import fetch from 'node-fetch';
import RSSParser from 'rss-parser';
import * as cheerio from 'cheerio';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Gemini API
const API_KEY = process.env.VITE_GEMINI_API_KEY;
const genAI = new GoogleGenerativeAI(API_KEY);

// Cybersecurity domains and their keywords for training
const DOMAINS = {
  TECHNICAL: [
    'what is a buffer overflow',
    'explain sql injection',
    'how does xss work',
    'what is csrf',
    'explain network segmentation',
    'what is a firewall',
    'how does encryption work',
    'what is a hash function',
    'explain public key cryptography',
    'what is a zero-day vulnerability',
    'how to perform penetration testing',
    'what is a reverse shell',
    'explain privilege escalation',
    'what is a rootkit',
    'how does ransomware work'
  ],
  GOVERNANCE: [
    'what is iso 27001',
    'explain nist cybersecurity framework',
    'what is pci dss compliance',
    'how to implement gdpr',
    'what is a security policy',
    'explain risk assessment',
    'what is a security audit',
    'how to create a security program',
    'what is security governance',
    'explain security controls',
    'what is a business impact analysis',
    'how to conduct a risk assessment',
    'what is a security framework',
    'explain security compliance',
    'what is a security standard'
  ],
  INCIDENT_RESPONSE: [
    'what is incident response',
    'explain digital forensics',
    'how to handle a security breach',
    'what is a soc',
    'explain security monitoring',
    'what is a csirt',
    'how to create an incident response plan',
    'what is security incident management',
    'explain forensic analysis',
    'what is memory forensics',
    'how to perform malware analysis',
    'what is network forensics',
    'explain incident containment',
    'what is incident eradication',
    'how to conduct a post-incident review'
  ],
  LATEST_TRENDS: [
    'what are the latest cybersecurity threats',
    'explain current ransomware trends',
    'what is the latest in ai security',
    'how is cloud security evolving',
    'what are the emerging threats in iot',
    'explain zero trust architecture',
    'what is the future of authentication',
    'how is quantum computing affecting cryptography',
    'what are the latest phishing techniques',
    'explain supply chain attacks',
    'what are the trends in devsecops',
    'how is 5g affecting security',
    'what are the latest mobile security threats',
    'explain blockchain security',
    'what are the trends in security automation'
  ]
};

// Telugu translations for training
const TELUGU_QUERIES = [
  'సైబర్ సెక్యూరిటీ అంటే ఏమిటి',
  'బఫర్ ఓవర్‌ఫ్లో దాడి అంటే ఏమిటి',
  'SQL ఇంజెక్షన్ అంటే ఏమిటి',
  'XSS దాడి అంటే ఏమిటి',
  'ఫైర్‌వాల్ అంటే ఏమిటి',
  'ఎన్‌క్రిప్షన్ అంటే ఏమిటి',
  'సైబర్ సెక్యూరిటీలో తాజా ట్రెండ్‌లు ఏమిటి',
  'సైబర్ సెక్యూరిటీ ఇన్సిడెంట్ రెస్పాన్స్ అంటే ఏమిటి',
  'సైబర్ సెక్యూరిటీ గవర్నెన్స్ అంటే ఏమిటి',
  'సైబర్ సెక్యూరిటీ రిస్క్ అసెస్‌మెంట్ అంటే ఏమిటి'
];

// Generate response using Gemini API
const generateWithGemini = async (input, domain, language = 'english') => {
  try {
    const modelName = domain === 'TECHNICAL' ? "gemini-1.5-flash" : "gemini-1.5-pro";
    const model = genAI.getGenerativeModel({ model: modelName });
    
    // Prepare domain-specific prompt
    let prompt = `As a cybersecurity expert specializing in ${domain.replace('_', ' ').toLowerCase()}, provide a detailed response to: ${input}`;
    
    // Add language instruction
    if (language === 'telugu') {
      prompt += " Please respond in Telugu language.";
    }
    
    // Generate response
    const result = await model.generateContent(prompt);
    const response = result.response;
    const content = response.text().trim();
    
    return {
      content,
      category: domain.toLowerCase(),
      language
    };
  } catch (error) {
    console.error('Error generating with Gemini:', error);
    throw error;
  }
};

// Save response to database
const saveResponseToDatabase = async (input, response, domain, language = 'english') => {
  try {
    const { error } = await supabase.from('ai_responses').insert({
      keyword: input.toLowerCase(),
      content: response.content,
      category: response.category,
      domain: domain,
      language: language
    });

    if (error) {
      console.error('Error saving to database:', error);
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error saving response:', error);
    return false;
  }
};

// Fetch latest cybersecurity news for training
const fetchLatestNews = async () => {
  try {
    const parser = new RSSParser();
    const feed = await parser.parseURL('https://feeds.feedburner.com/TheHackersNews');
    
    return feed.items.slice(0, 10).map(item => ({
      title: item.title,
      link: item.link,
      pubDate: item.pubDate,
      snippet: item.contentSnippet?.substring(0, 100) + '...'
    }));
  } catch (error) {
    console.error('Error fetching news:', error);
    return [];
  }
};

// Main training function
const trainModel = async () => {
  console.log('Starting XCerberus AI training...');
  
  let totalGenerated = 0;
  let totalSaved = 0;
  
  // Train for each domain
  for (const [domain, queries] of Object.entries(DOMAINS)) {
    console.log(`\nTraining for domain: ${domain}`);
    
    for (const query of queries) {
      try {
        console.log(`Generating response for: "${query}"`);
        const response = await generateWithGemini(query, domain);
        totalGenerated++;
        
        console.log(`Saving response to database...`);
        const saved = await saveResponseToDatabase(query, response, domain);
        if (saved) totalSaved++;
        
        // Add a delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`Error processing query "${query}":`, error);
      }
    }
  }
  
  // Train for Telugu language
  console.log('\nTraining for Telugu language...');
  for (const query of TELUGU_QUERIES) {
    try {
      console.log(`Generating Telugu response for: "${query}"`);
      const response = await generateWithGemini(query, 'TECHNICAL', 'telugu');
      totalGenerated++;
      
      console.log(`Saving Telugu response to database...`);
      const saved = await saveResponseToDatabase(query, response, 'TECHNICAL', 'telugu');
      if (saved) totalSaved++;
      
      // Add a delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error processing Telugu query "${query}":`, error);
    }
  }
  
  // Train with latest news
  console.log('\nTraining with latest cybersecurity news...');
  try {
    const news = await fetchLatestNews();
    if (news.length > 0) {
      for (const item of news) {
        const query = `Tell me about ${item.title}`;
        console.log(`Generating response for news: "${query}"`);
        
        const response = await generateWithGemini(query, 'LATEST_TRENDS');
        totalGenerated++;
        
        console.log(`Saving news response to database...`);
        const saved = await saveResponseToDatabase(query, response, 'LATEST_TRENDS');
        if (saved) totalSaved++;
        
        // Add a delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  } catch (error) {
    console.error('Error training with news:', error);
  }
  
  console.log(`\nTraining complete!`);
  console.log(`Total responses generated: ${totalGenerated}`);
  console.log(`Total responses saved to database: ${totalSaved}`);
};

// Run the training
trainModel().catch(error => {
  console.error('Training failed:', error);
  process.exit(1);
});