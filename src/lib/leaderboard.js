import { supabase } from './supabase';

export const getLeaderboard = async (timeframe = 'all') => {
  try {
    let query = supabase
      .from('leaderboard')
      .select(`
        *,
        users (
          username,
          avatar_url,
          full_name
        )
      `)
      .order('total_points', { ascending: false });

    // Add timeframe filter
    if (timeframe === 'week') {
      query = query.gte('updated_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());
    } else if (timeframe === 'month') {
      query = query.gte('updated_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());
    }

    const { data, error } = await query;
    if (error) throw error;

    // Calculate ranks
    return data.map((user, index) => ({
      ...user,
      rank: index + 1
    }));
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    return [];
  }
};

export const getUserRank = async (userId) => {
  try {
    const { data: leaderboard } = await supabase
      .from('leaderboard')
      .select('user_id, total_points')
      .order('total_points', { ascending: false });

    const userIndex = leaderboard.findIndex(entry => entry.user_id === userId);
    return userIndex !== -1 ? userIndex + 1 : null;
  } catch (error) {
    console.error('Error getting user rank:', error);
    return null;
  }
};

export const updateUserPoints = async (userId, points, challengeId = null) => {
  try {
    // Start a transaction
    const { data: currentPoints } = await supabase
      .from('leaderboard')
      .select('total_points, challenges_completed')
      .eq('user_id', userId)
      .single();

    const newTotal = (currentPoints?.total_points || 0) + points;
    const newCompleted = challengeId ? (currentPoints?.challenges_completed || 0) + 1 : currentPoints?.challenges_completed || 0;

    const { error } = await supabase
      .from('leaderboard')
      .upsert({
        user_id: userId,
        total_points: newTotal,
        challenges_completed: newCompleted,
        updated_at: new Date().toISOString()
      });

    if (error) throw error;

    // Update user activity
    await supabase.from('user_activity').insert({
      user_id: userId,
      activity_type: 'points_earned',
      description: `Earned ${points} points`,
      points_earned: points,
      challenge_id: challengeId
    });

    return { total_points: newTotal, challenges_completed: newCompleted };
  } catch (error) {
    console.error('Error updating points:', error);
    throw error;
  }
};

export const getLeaderboardStats = async (userId) => {
  try {
    const [weeklyResponse, monthlyResponse, allTimeResponse] = await Promise.all([
      // Weekly stats
      supabase
        .from('leaderboard')
        .select('total_points')
        .gte('updated_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('total_points', { ascending: false })
        .limit(10),

      // Monthly stats
      supabase
        .from('leaderboard')
        .select('total_points')
        .gte('updated_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('total_points', { ascending: false })
        .limit(10),

      // All-time stats
      supabase
        .from('leaderboard')
        .select('total_points')
        .order('total_points', { ascending: false })
        .limit(10)
    ]);

    return {
      weekly: weeklyResponse.data || [],
      monthly: monthlyResponse.data || [],
      allTime: allTimeResponse.data || []
    };
  } catch (error) {
    console.error('Error fetching leaderboard stats:', error);
    return { weekly: [], monthly: [], allTime: [] };
  }
};

export const getTopPerformers = async (category = null) => {
  try {
    let query = supabase
      .from('leaderboard')
      .select(`
        *,
        users (
          username,
          avatar_url
        )
      `)
      .order('total_points', { ascending: false })
      .limit(3);

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query;
    if (error) throw error;

    return data;
  } catch (error) {
    console.error('Error fetching top performers:', error);
    return [];
  }
};

export const getCategoryLeaders = async () => {
  try {
    const categories = ['web', 'network', 'crypto', 'binary'];
    const leaders = {};

    for (const category of categories) {
      const { data } = await supabase
        .from('leaderboard')
        .select(`
          *,
          users (
            username,
            avatar_url
          )
        `)
        .eq('category', category)
        .order('total_points', { ascending: false })
        .limit(1)
        .single();

      if (data) {
        leaders[category] = data;
      }
    }

    return leaders;
  } catch (error) {
    console.error('Error fetching category leaders:', error);
    return {};
  }
};