import { GoogleGenerativeAI } from "@google/generative-ai";
import { supabase } from './supabase';

// Initialize Gemini API
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const genAI = new GoogleGenerativeAI(API_KEY);

class UserAgent {
  constructor() {
    this.model = null;
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      this.model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
      this.initialized = true;
    } catch (error) {
      console.error('Error initializing UserAgent:', error);
      throw error;
    }
  }

  async analyzeUserBehavior(userId) {
    try {
      // Get user's activity data
      const { data: activityData } = await supabase
        .from('user_activity')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Get challenge completions
      const { data: challengeData } = await supabase
        .from('challenge_submissions')
        .select('*, challenges(*)')
        .eq('user_id', userId);

      // Get learning progress
      const { data: learningData } = await supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', userId);

      // Analyze patterns
      const patterns = this.analyzeLearningPatterns(activityData, challengeData, learningData);

      return patterns;
    } catch (error) {
      console.error('Error analyzing user behavior:', error);
      return null;
    }
  }

  analyzeLearningPatterns(activity, challenges, learning) {
    const patterns = {
      strengths: [],
      weaknesses: [],
      recommendedPaths: [],
      nextChallenges: []
    };

    // Analyze completed challenges
    const completedChallenges = challenges?.filter(c => c.status === 'completed') || [];
    const categories = completedChallenges.reduce((acc, c) => {
      acc[c.challenges?.category] = (acc[c.challenges?.category] || 0) + 1;
      return acc;
    }, {});

    // Find strengths (categories with high completion rates)
    patterns.strengths = Object.entries(categories)
      .filter(([_, count]) => count >= 3)
      .map(([category]) => category);

    // Find weaknesses (categories with low completion rates or high failure rates)
    patterns.weaknesses = Object.entries(categories)
      .filter(([_, count]) => count < 2)
      .map(([category]) => category);

    // Analyze learning progress
    if (learning && learning.length > 0) {
      const recentTopics = learning
        .sort((a, b) => new Date(b.completed_at) - new Date(a.completed_at))
        .slice(0, 5);

      // Recommend next topics based on recent learning
      patterns.recommendedPaths = this.getRecommendedPaths(recentTopics);
    }

    // Recommend next challenges based on performance
    patterns.nextChallenges = this.getNextChallenges(completedChallenges);

    return patterns;
  }

  async generateRecommendations(userId) {
    try {
      const patterns = await this.analyzeUserBehavior(userId);
      if (!patterns) return null;

      const prompt = `Based on this user's learning patterns:
Strengths: ${patterns.strengths.join(', ')}
Weaknesses: ${patterns.weaknesses.join(', ')}
Recent Topics: ${patterns.recommendedPaths.join(', ')}

Generate personalized recommendations for:
1. Next challenges to attempt
2. Skills to focus on
3. Learning path adjustments
4. Practice exercises
5. Additional resources`;

      const result = await this.model.generateContent(prompt);
      return {
        patterns,
        recommendations: result.response.text()
      };
    } catch (error) {
      console.error('Error generating recommendations:', error);
      return null;
    }
  }

  getRecommendedPaths(recentTopics) {
    // Map of related topics
    const topicRelations = {
      'linux-basics': ['shell-scripting', 'file-systems', 'permissions'],
      'web-security': ['sql-injection', 'xss', 'csrf'],
      'network-security': ['wireshark', 'nmap', 'metasploit'],
      'cryptography': ['encryption', 'hashing', 'certificates']
    };

    const recommendations = new Set();
    recentTopics.forEach(topic => {
      const related = topicRelations[topic.topic_id] || [];
      related.forEach(r => recommendations.add(r));
    });

    return Array.from(recommendations);
  }

  getNextChallenges(completedChallenges) {
    const categories = new Set(completedChallenges.map(c => c.challenges?.category));
    const difficulties = ['Easy', 'Medium', 'Hard'];
    
    const recommendations = [];
    categories.forEach(category => {
      const maxDifficulty = completedChallenges
        .filter(c => c.challenges?.category === category)
        .reduce((max, c) => {
          const diffIndex = difficulties.indexOf(c.challenges?.difficulty);
          return Math.max(max, diffIndex);
        }, -1);

      if (maxDifficulty < difficulties.length - 1) {
        recommendations.push({
          category,
          recommendedDifficulty: difficulties[maxDifficulty + 1]
        });
      }
    });

    return recommendations;
  }
}

export const userAgent = new UserAgent();