import { supabase } from './supabase';

export const getUserSubscription = async () => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) return null;

    const { data, error } = await supabase
      .from('user_subscriptions')
      .select(`
        *,
        subscription_plans (*)
      `)
      .eq('user_id', session.user.id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting subscription:', error);
    return null;
  }
};

export const startSubscription = async (planName) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    // Use the database function to handle the upgrade
    const { data, error } = await supabase
      .rpc('handle_subscription_upgrade', {
        p_user_id: session.user.id,
        p_new_plan_name: planName
      });

    if (error) throw error;

    // Get the updated subscription
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .select(`
        *,
        subscription_plans (*)
      `)
      .eq('user_id', session.user.id)
      .single();

    if (subscriptionError) throw subscriptionError;
    return subscriptionData;
  } catch (error) {
    console.error('Error starting subscription:', error);
    throw error;
  }
};

export const getSubscriptionPlans = async () => {
  try {
    const { data, error } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('price');

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching plans:', error);
    return [];
  }
};

export const checkFeatureAccess = async (feature) => {
  try {
    const subscription = await getUserSubscription();
    if (!subscription || subscription.status !== 'active') return false;

    const features = subscription.subscription_plans?.features;
    return features ? features[feature] !== undefined : false;
  } catch (error) {
    console.error('Error checking feature access:', error);
    return false;
  }
};

export const getFeatureLimit = async (feature) => {
  try {
    const subscription = await getUserSubscription();
    if (!subscription || subscription.status !== 'active') return 0;

    const features = subscription.subscription_plans?.features;
    return features ? (features[feature] || 0) : 0;
  } catch (error) {
    console.error('Error getting feature limit:', error);
    return 0;
  }
};

export const trackFeatureUsage = async (feature) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    const { data, error } = await supabase
      .from('subscription_usage')
      .upsert({
        user_id: session.user.id,
        feature,
        usage_count: supabase.raw('usage_count + 1'),
        last_used_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error tracking feature usage:', error);
    throw error;
  }
};