import { GoogleGenerativeAI } from "@google/generative-ai";
import { supabase } from './supabase';

// Initialize the Gemini API
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const genAI = new GoogleGenerativeAI(API_KEY);

// Response feedback types
export const FEEDBACK_TYPES = {
  HEART: 'heart',   // Save to hot cache
  LIKE: 'like',     // Save to cold cache
  DISLIKE: 'dislike' // Don't save
};

// Main fusion function
export const generateFusionResponse = async (input) => {
  try {
    // First show thinking state
    const thinkingResponse = {
      content: "🤔 Analyzing your question...",
      category: 'thinking',
      language: 'english'
    };
    
    // Try to get response from database first
    const dbResponse = await getResponseFromDatabase(input);
    if (dbResponse) {
      // Format the response
      const formattedContent = formatResponse(dbResponse.content);
      return {
        content: formattedContent,
        category: dbResponse.category || 'technical',
        language: 'english',
        cached: true
      };
    }
    
    // Generate fallback response when offline
    const response = {
      content: `I understand you're asking about "${input}". This is a locally generated response since we're working offline. In a production environment, I would connect to the Gemini API to provide a more detailed answer.`,
      category: 'technical',
      language: 'english',
      cached: false
    };
    
    // Save to database
    await saveResponseToDatabase(input, response);
    
    return response;
  } catch (error) {
    console.error('Error in fusion response generation:', error);
    return {
      content: "I apologize, but I encountered an error. Could you please rephrase your question about cybersecurity?",
      category: "error",
      language: "english"
    };
  }
};

// Save chat message
export const saveFusionChatMessage = async (userId, message) => {
  try {
    const { error } = await supabase
      .from('chat_messages')
      .insert({
        user_id: userId,
        type: message.type,
        content: message.content,
        category: message.category || null
      });

    if (error) {
      console.error("Error saving chat message:", error);
      throw error;
    }
  } catch (error) {
    console.error('Error saving chat message:', error);
  }
};

// Helper functions
async function getResponseFromDatabase(input) {
  try {
    // Try exact match first
    const { data: exactMatch } = await supabase
      .from('ai_responses')
      .select('*')
      .eq('keyword', input.toLowerCase())
      .single();
      
    if (exactMatch) return exactMatch;
    
    // Try fuzzy match
    const { data: fuzzyMatch } = await supabase
      .from('ai_responses')
      .select('*')
      .ilike('keyword', `%${input.toLowerCase()}%`)
      .limit(1)
      .single();
      
    return fuzzyMatch || null;
  } catch (error) {
    console.error('Error querying database:', error);
    return null;
  }
}

async function saveResponseToDatabase(input, response) {
  try {
    await supabase.from('ai_responses').insert({
      keyword: input.toLowerCase(),
      content: response.content,
      category: response.category
    });
  } catch (error) {
    console.error('Error saving to database:', error);
  }
}

// Format response with proper markdown
function formatResponse(content) {
  if (!content.includes('##')) {
    // Add formatting if not already present
    return `## Response
${content}

### Key Points
* Important point 1
* Important point 2
* Important point 3

### Additional Resources
* Official documentation
* Security best practices
* Related topics`;
  }
  return content;
}

// Save response feedback
export const saveFeedback = async (query, response, feedbackType) => {
  try {
    const { error } = await supabase
      .from('ai_response_feedback')
      .insert({
        query: query.toLowerCase(),
        response_content: response.content,
        feedback_type: feedbackType
      });

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error saving feedback:', error);
    return false;
  }
};