import { GoogleGenerativeAI } from "@google/generative-ai";
import { supabase } from '../../supabase';

// Initialize Gemini API
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const genAI = new GoogleGenerativeAI(API_KEY);

// Risk Management
class RiskManagement {
  constructor() {
    this.model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
  }

  async assessRisk(scenario) {
    const prompt = `Perform a risk assessment for:
${JSON.stringify(scenario, null, 2)}

Include:
1. Threat identification
2. Vulnerability analysis
3. Impact assessment
4. Likelihood evaluation
5. Risk rating
6. Control recommendations`;

    try {
      const result = await this.model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Error assessing risk:', error);
      throw error;
    }
  }

  async generateRiskRegister(risks) {
    const prompt = `Generate a risk register for:
${JSON.stringify(risks, null, 2)}

Include:
1. Risk descriptions
2. Risk owners
3. Current controls
4. Risk ratings
5. Treatment plans
6. Review schedule`;

    try {
      const result = await this.model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Error generating risk register:', error);
      throw error;
    }
  }
}

// Compliance Management
class ComplianceManagement {
  constructor() {
    this.model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
  }

  async assessCompliance(framework, evidence) {
    const prompt = `Assess compliance with ${framework}:
${JSON.stringify(evidence, null, 2)}

Provide:
1. Control assessment
2. Gap analysis
3. Remediation plan
4. Evidence requirements`;

    try {
      const result = await this.model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Error assessing compliance:', error);
      throw error;
    }
  }

  async generatePolicies(requirements) {
    const prompt = `Generate security policies for:
${JSON.stringify(requirements, null, 2)}

Include:
1. Policy statements
2. Scope and applicability
3. Roles and responsibilities
4. Compliance requirements
5. Review procedures`;

    try {
      const result = await this.model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Error generating policies:', error);
      throw error;
    }
  }
}

// Audit Management
class AuditManagement {
  constructor() {
    this.model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
  }

  async generateAuditPlan(scope) {
    const prompt = `Generate an audit plan for:
${JSON.stringify(scope, null, 2)}

Include:
1. Audit objectives
2. Scope definition
3. Methodology
4. Timeline
5. Resource requirements
6. Evidence collection`;

    try {
      const result = await this.model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Error generating audit plan:', error);
      throw error;
    }
  }

  async analyzeAuditFindings(findings) {
    const prompt = `Analyze these audit findings:
${JSON.stringify(findings, null, 2)}

Provide:
1. Finding classification
2. Root cause analysis
3. Impact assessment
4. Remediation recommendations
5. Follow-up procedures`;

    try {
      const result = await this.model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Error analyzing findings:', error);
      throw error;
    }
  }
}

export {
  RiskManagement,
  ComplianceManagement,
  AuditManagement
};