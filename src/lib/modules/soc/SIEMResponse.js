import { GoogleGenerativeAI } from "@google/generative-ai";
import { supabase } from '../../supabase';

const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const genAI = new GoogleGenerativeAI(API_KEY);

export const generateSIEMResponse = async (query) => {
  try {
    // First try to get response from database
    const { data: dbResponse, error } = await supabase.rpc(
      'search_ai_responses',
      { 
        search_term: query.toLowerCase(),
        search_domain: 'TECHNICAL',
        search_language: 'english'
      }
    );

    if (!error && dbResponse && dbResponse.length > 0) {
      return {
        content: dbResponse[0].content,
        category: dbResponse[0].category,
        language: dbResponse[0].language,
        cached: true
      };
    }

    // If no database match, generate with AI
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    
    const prompt = `As a cybersecurity expert, provide a detailed explanation about SIEM (Security Information and Event Management) based on this query: ${query}

Please cover:
1. Definition and core concepts
2. Key components and features
3. Use cases and benefits
4. Implementation considerations
5. Best practices and tools
6. Common challenges and solutions

Focus on practical, technical details that would be useful for security professionals.`;

    const result = await model.generateContent(prompt);
    const response = {
      content: result.response.text(),
      category: 'technical',
      language: 'english',
      cached: false
    };

    // Save new response to database
    try {
      await supabase.from('ai_responses').insert({
        keyword: query.toLowerCase(),
        content: response.content,
        category: response.category,
        domain: 'TECHNICAL',
        language: response.language
      });
    } catch (saveError) {
      console.error('Error saving response:', saveError);
    }

    return response;
  } catch (error) {
    console.error('Error generating SIEM response:', error);
    return {
      content: "I apologize, but I encountered an error. Could you please rephrase your question about SIEM?",
      category: 'error',
      language: 'english',
      cached: false
    };
  }
};