@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Add custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-400 {
  scrollbar-color: #9ca3af transparent;
}

.scrollbar-track-gray-100 {
  scrollbar-track-color: #f3f4f6;
}

/* Webkit scrollbar styles */

/* Custom animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out forwards;
}

/* Rotation Utilities */
.rotate-1 {
  transform: rotate(1deg);
}

.rotate-2 {
  transform: rotate(2deg);
}

.rotate-3 {
  transform: rotate(3deg);
}

.-rotate-1 {
  transform: rotate(-1deg);
}

.-rotate-2 {
  transform: rotate(-2deg);
}

.-rotate-3 {
  transform: rotate(-3deg);
}

/* Animation for dashed lines */
@keyframes dash {
  to {
    stroke-dashoffset: -30;
  }
}

.animate-dash-slow {
  animation: dash 15s linear infinite;
}

.animate-dash-medium {
  animation: dash 10s linear infinite;
}

.animate-dash-fast {
  animation: dash 5s linear infinite;
}

/* Animation for pulsing */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

/* Animation for floating particles */
@keyframes float-particle {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-15px) translateX(10px);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-25px) translateX(0);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-15px) translateX(-10px);
    opacity: 0.6;
  }
  100% {
    transform: translateY(0) translateX(0);
    opacity: 0.4;
  }
}

/* Animation for spinning */
@keyframes spin-forward {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

.animate-spin-forward {
  animation: spin-forward linear infinite;
}

.animate-spin-reverse {
  animation: spin-reverse linear infinite;
}

.animate-spin-slow {
  animation-duration: 20s;
}

/* 3D perspective */
.perspective-800 {
  perspective: 800px;
}

.perspective-1200 {
  perspective: 1200px;
}

/* Advanced animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes float-subtle {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(74, 92, 186, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(74, 92, 186, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(74, 92, 186, 0.3);
  }
}

@keyframes glow-gold {
  0% {
    box-shadow: 0 0 5px rgba(245, 185, 63, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(245, 185, 63, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(245, 185, 63, 0.3);
  }
}

@keyframes slide-in-left {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  0% {
    transform: translateX(100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-bottom {
  0% {
    transform: translateY(100px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotate-in {
  0% {
    transform: rotate(-10deg) scale(0.9);
    opacity: 0;
  }
  100% {
    transform: rotate(0) scale(1);
    opacity: 1;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Animation classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-subtle {
  animation: float-subtle 5s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-glow-gold {
  animation: glow-gold 3s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 5s ease infinite;
}

/* Glass morphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}

.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.glass-card:hover {
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
}

.glass-card-dark {
  background: rgba(26, 31, 53, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.glass-card-dark:hover {
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
}

/* Gradient backgrounds */
.bg-gradient-blue {
  background: linear-gradient(135deg, #4A5CBA 0%, #3B82F6 100%);
}

.bg-gradient-gold {
  background: linear-gradient(135deg, #F5B93F 0%, #F59E0B 100%);
}

.bg-gradient-cyber {
  background: linear-gradient(135deg, #4A5CBA 0%, #10B981 100%);
}

.bg-gradient-dark {
  background: linear-gradient(135deg, #1A1F35 0%, #0B1120 100%);
}

/* Neon effects */
.neon-blue {
  text-shadow: 0 0 5px rgba(74, 92, 186, 0.7), 0 0 10px rgba(74, 92, 186, 0.5);
}

.neon-gold {
  text-shadow: 0 0 5px rgba(245, 185, 63, 0.7), 0 0 10px rgba(245, 185, 63, 0.5);
}

.neon-box-blue {
  box-shadow: 0 0 5px rgba(74, 92, 186, 0.7), 0 0 10px rgba(74, 92, 186, 0.5);
}

.neon-box-gold {
  box-shadow: 0 0 5px rgba(245, 185, 63, 0.7), 0 0 10px rgba(245, 185, 63, 0.5);
}

.animate-pulse-slow {
  animation: pulse 3s infinite;
}

.animate-pulse-medium {
  animation: pulse 2s infinite;
}

.animate-pulse-fast {
  animation: pulse 1s infinite;
}
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f3f4f6;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #9ca3af;
  border-radius: 3px;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  font-family: 'JetBrains Mono', monospace;
  @apply bg-gray-50 text-gray-900;
}

/* Interactive Elements */
.interactive-hover {
  @apply transition-all duration-300 ease-in-out transform hover:scale-[1.03] hover:shadow-lg hover:shadow-primary/30;
}

.button-hover {
  @apply transition-all duration-300 ease-in-out transform hover:scale-[1.05] hover:shadow-md active:scale-95;
}

.link-hover {
  @apply transition-all duration-300 relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary hover:after:w-full after:transition-all after:duration-300;
}

/* Card Styles */
.card {
  @apply bg-white rounded-lg border border-gray-200 p-6 transition-all duration-300 hover:border-primary hover:shadow-lg hover:shadow-primary/30;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgb(45, 212, 191));
  transition: 0.5s;
}

.card:hover::before {
  left: 100%;
}

.card-hover {
  @apply hover:border-primary hover:shadow-lg hover:shadow-primary/30 transition-all duration-300 transform hover:scale-[1.02];
  position: relative;
  overflow: hidden;
}

.card-hover::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 2px solid transparent;
  border-radius: 0.5rem;
  transition: 0.3s;
  pointer-events: none;
}

.card-hover:hover::after {
  border-color: rgb(45, 212, 191);
  box-shadow: 0 0 15px rgba(45, 212, 191, 0.5);
}

/* Button Styles */
.primary-button {
  @apply bg-primary text-black font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:bg-primary-hover hover:scale-[1.05] hover:shadow-lg hover:shadow-primary/40 active:scale-95;
  position: relative;
  overflow: hidden;
}

.primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
}

.primary-button:hover::before {
  left: 100%;
}

.secondary-button {
  @apply bg-white text-gray-800 border border-gray-200 font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:border-primary hover:text-primary hover:scale-[1.05] hover:shadow-lg hover:shadow-primary/20 active:scale-95;
}

/* Input Styles */
.input-field {
  @apply w-full px-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300;
}

/* Navigation Styles */
.nav-link {
  @apply relative text-gray-900 font-medium transition-all duration-300;
}

.nav-link::after {
  content: '';
  @apply absolute left-0 bottom-[-4px] w-0 h-0.5 bg-primary transition-all duration-300;
}

.nav-link:hover {
  @apply text-primary;
}

.nav-link:hover::after {
  @apply w-full;
}

/* Challenge Card Styles */
.challenge-card {
  @apply bg-white border border-gray-200 rounded-lg p-6 transition-all duration-300;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.challenge-card:hover {
  @apply border-primary transform scale-[1.03];
  box-shadow: 0 8px 20px rgba(45, 212, 191, 0.2);
}

.challenge-card::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 2px solid transparent;
  border-radius: 0.5rem;
  transition: 0.3s;
  pointer-events: none;
}

.challenge-card:hover::after {
  border-color: rgb(45, 212, 191);
  box-shadow: 0 0 15px rgba(45, 212, 191, 0.5);
}

/* Terminal Styles */
.terminal {
  @apply bg-[#1c1c1c] border border-[#2d2d2d] rounded-lg overflow-hidden;
  height: 500px;
}

.terminal input {
  caret-color: rgb(45, 212, 191);
  font-family: 'JetBrains Mono', monospace;
}

.terminal input::selection {
  background-color: rgba(45, 212, 191, 0.3);
}

.terminal-header {
  @apply bg-[#252525] px-4 py-2 flex items-center gap-2 border-b border-gray-800;
}

.terminal-container {
  @apply relative;
  height: 500px;
}

/* Icon Hover Effects */
.icon-hover {
  @apply transition-all duration-300 transform hover:scale-125 hover:text-primary;
}

/* List Item Hover */
.list-item-hover {
  @apply transition-all duration-300 hover:bg-primary/10 hover:text-primary rounded-lg;
}

/* Section Transitions */
.section-transition {
  @apply transition-all duration-500 ease-in-out;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
  .container {
    @apply px-4;
  }

  .terminal {
    height: 400px;
    font-size: 14px;
  }

  .terminal input {
    font-size: 14px;
  }

  .terminal-header {
    padding: 8px 12px;
  }

  .terminal .typed-text {
    font-size: 14px;
  }

  .terminal-content {
    padding: 12px;
  }

  .terminal-input {
    padding: 8px 12px;
  }

  .terminal-cursor {
    height: 13px;
  }

  .challenge-card {
    @apply p-4;
  }

  .primary-button,
  .secondary-button {
    @apply w-full flex justify-center;
  }

  .grid {
    @apply gap-4;
  }

  .prose {
    @apply text-sm;
  }

  .prose pre {
    @apply text-xs;
  }

  input,
  select,
  textarea {
    @apply text-base;
  }

  .form-grid {
    @apply grid-cols-1;
  }
}

/* Table Responsiveness */
@media (max-width: 768px) {
  .table-container {
    @apply -mx-4 px-4 overflow-x-auto;
  }

  table {
    @apply min-w-full;
  }

  td, th {
    @apply whitespace-nowrap;
  }
}

/* Footer Styles */
.footer-section {
  @apply py-8;
}

.footer-title {
  @apply font-bold text-gray-900 mb-3 text-sm;
}

.footer-link {
  @apply text-gray-600 hover:text-primary transition-colors text-sm;
}

/* Prose Styles */
.prose {
  @apply text-gray-600 leading-relaxed;
}

.prose p {
  @apply mb-4;
}

.prose ul {
  @apply list-disc list-inside mb-4 space-y-2;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4 {
  @apply text-gray-900 font-bold mb-4;
}

.prose pre {
  @apply bg-gray-900 text-white p-4 rounded-lg mb-4 overflow-x-auto;
}

.prose code {
  @apply bg-gray-100 text-gray-900 px-1 py-0.5 rounded;
}

/* Loading States */
.loading-pulse {
  @apply animate-pulse bg-gray-200 rounded;
}

/* Success States */
.success-animation {
  @apply transition-all duration-300 transform;
  animation: successPop 0.3s ease-out;
}

@keyframes successPop {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Error States */
.error-shake {
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Section spacing improvements */
section {
  @apply py-16 md:py-24;
}

.section-title {
  @apply text-3xl md:text-4xl font-bold mb-4 md:mb-6;
}

.section-description {
  @apply text-gray-600 max-w-3xl mx-auto mb-10 md:mb-16;
}

/* Card improvements */
.feature-card {
  @apply p-6 md:p-8 rounded-xl border border-gray-200 bg-white transition-all duration-300;
  position: relative;
  overflow: hidden;
}

.feature-card:hover {
  @apply border-primary shadow-lg shadow-primary/20 transform scale-[1.02];
}

.feature-card::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 2px solid transparent;
  border-radius: 0.75rem;
  transition: 0.3s;
  pointer-events: none;
}

.feature-card:hover::after {
  border-color: rgb(45, 212, 191);
  box-shadow: 0 0 15px rgba(45, 212, 191, 0.3);
}

/* Consistent spacing for stats */
.stat-card {
  @apply p-4 md:p-5 rounded-lg bg-gray-50 border border-gray-100 hover:shadow-md transition-all duration-300;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  @apply border-primary shadow-md shadow-primary/20 transform scale-[1.03];
}

.stat-card::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 2px solid transparent;
  border-radius: 0.5rem;
  transition: 0.3s;
  pointer-events: none;
}

.stat-card:hover::after {
  border-color: rgb(45, 212, 191);
  box-shadow: 0 0 10px rgba(45, 212, 191, 0.3);
}

/* FAQ item styling */
.faq-item {
  @apply p-5 md:p-6 rounded-lg bg-white shadow-sm hover:shadow-md transition-all duration-300;
  position: relative;
  overflow: hidden;
}

.faq-item:hover {
  @apply border-primary shadow-md shadow-primary/15 transform scale-[1.01];
}

.faq-item::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  transition: 0.3s;
  pointer-events: none;
}

.faq-item:hover::after {
  border-color: rgb(45, 212, 191);
  box-shadow: 0 0 10px rgba(45, 212, 191, 0.2);
}

/* Improved mobile spacing */
@media (max-width: 768px) {
  .hero-title {
    @apply text-4xl leading-tight;
  }

  .hero-description {
    @apply text-lg;
  }

  .section-gap {
    @apply gap-8;
  }

  .card-grid {
    @apply gap-6;
  }
}

/* Force correct order on mobile */
@media (max-width: 1023px) {
  .order-first {
    order: 1 !important;
  }

  .order-last {
    order: 2 !important;
  }
}

/* Fix for mobile layout */
@media (max-width: 1023px) {
  .grid > [style*="order: 1"] {
    order: 1 !important;
  }

  .grid > [style*="order: 2"] {
    order: 2 !important;
  }
}

/* Ensure hero section content is always first on mobile */
@media (max-width: 1023px) {
  .grid > div:first-of-type {
    order: 1 !important;
  }

  .grid > div:last-of-type {
    order: 2 !important;
  }

  /* Additional selectors to ensure ordering */
  .grid > .order-first {
    order: 1 !important;
  }

  .grid > .order-last {
    order: 2 !important;
  }
}

/* Critical mobile layout fix */
.grid > .order-first {
  order: 1 !important;
}

.grid > .order-last {
  order: 2 !important;
}

/* Hide terminal on mobile */
@media (max-width: 767px) {
  .terminal-container {
    display: none !important;
  }
}

/* Neon glow effects */
.neon-glow {
  box-shadow: 0 0 5px rgb(45, 212, 191), 0 0 10px rgb(45, 212, 191), 0 0 15px rgb(45, 212, 191);
  border: 1px solid rgb(45, 212, 191);
}

.neon-text {
  text-shadow: 0 0 5px rgb(45, 212, 191), 0 0 10px rgb(45, 212, 191);
  color: rgb(45, 212, 191);
}

/* Animated border effect */
.animated-border {
  position: relative;
  overflow: hidden;
}

.animated-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgb(45, 212, 191), transparent);
  animation: borderSlide 3s linear infinite;
}

@keyframes borderSlide {
  0% { left: -100%; }
  50%, 100% { left: 100%; }
}

/* Pulsing effect */
.pulse-effect {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Floating animation */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-float-slow {
  animation: float 6s ease-in-out infinite;
}

.animate-float-medium {
  animation: float 4s ease-in-out infinite;
}

.animate-float-fast {
  animation: float 2s ease-in-out infinite;
}

/* Fade in animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 1s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
}

/* Animation delays */
.animation-delay-100 {
  animation-delay: 0.1s;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-300 {
  animation-delay: 0.3s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-500 {
  animation-delay: 0.5s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-700 {
  animation-delay: 0.7s;
}

.animation-delay-800 {
  animation-delay: 0.8s;
}

/* Hover animations */
.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Globe rotation animation */
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin-slow {
  animation: spin-slow 120s linear infinite;
}

/* Satellite orbit animation */
@keyframes orbit {
  0% { transform: translate(-200px, 0) rotate(0deg); }
  100% { transform: translate(-200px, 0) rotate(360deg); }
}

.animate-orbit {
  animation: orbit 20s linear infinite;
  transform-origin: 200px 0;
}

/* Connection pulse animation */
@keyframes connection-pulse {
  0% { opacity: 0.3; stroke-width: 1px; }
  50% { opacity: 1; stroke-width: 2px; }
  100% { opacity: 0.3; stroke-width: 1px; }
}

.animate-connection-pulse {
  animation: connection-pulse 2s ease-in-out infinite;
}

/* Knowledge particle animations */
@keyframes particle1 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(0, -25%); opacity: 0; }
}

.animate-particle1 {
  animation: particle1 4s linear infinite;
}

@keyframes particle2 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(25%, 0); opacity: 0; }
}

.animate-particle2 {
  animation: particle2 5s linear infinite;
}

@keyframes particle3 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(0, 25%); opacity: 0; }
}

.animate-particle3 {
  animation: particle3 4.5s linear infinite;
}

@keyframes particle4 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(-25%, 0); opacity: 0; }
}

.animate-particle4 {
  animation: particle4 5.5s linear infinite;
}

/* Secondary particle animations */
@keyframes particle5 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(-7.5%, -2.5%); opacity: 0; }
}

.animate-particle5 {
  animation: particle5 3s linear infinite;
}

@keyframes particle6 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(7.5%, -2.5%); opacity: 0; }
}

.animate-particle6 {
  animation: particle6 3.2s linear infinite;
}

@keyframes particle7 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(5%, -10%); opacity: 0; }
}

.animate-particle7 {
  animation: particle7 3.4s linear infinite;
}

@keyframes particle8 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(5%, 10%); opacity: 0; }
}

.animate-particle8 {
  animation: particle8 3.6s linear infinite;
}

@keyframes particle9 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(7.5%, 2.5%); opacity: 0; }
}

.animate-particle9 {
  animation: particle9 3.8s linear infinite;
}

@keyframes particle10 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(-7.5%, 2.5%); opacity: 0; }
}

.animate-particle10 {
  animation: particle10 4s linear infinite;
}

@keyframes particle11 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(-5%, 10%); opacity: 0; }
}

.animate-particle11 {
  animation: particle11 4.2s linear infinite;
}

@keyframes particle12 {
  0% { transform: translate(0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate(-5%, -10%); opacity: 0; }
}

.animate-particle12 {
  animation: particle12 4.4s linear infinite;
}

/* Pricing card specific styles */
.pricing-card {
  @apply bg-white rounded-2xl shadow-lg p-6 md:p-8 border border-gray-100 relative transition-all duration-500;
  overflow: hidden;
}

.pricing-card:hover {
  @apply border-primary shadow-xl shadow-primary/20 transform scale-[1.02];
}

.pricing-card::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 2px solid transparent;
  border-radius: 1rem;
  transition: 0.5s;
  pointer-events: none;
}

.pricing-card:hover::after {
  border-color: rgb(45, 212, 191);
  box-shadow: 0 0 20px rgba(45, 212, 191, 0.3);
}

.pricing-card .feature-item {
  @apply flex items-start;
}

.pricing-card .feature-icon {
  @apply w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0;
  transition: all 0.3s ease;
}

.pricing-card:hover .feature-icon {
  @apply bg-primary/30;
}

.pricing-card .feature-text {
  @apply text-gray-600;
}

.pricing-popular {
  @apply border-2 border-primary transform scale-105 hover:shadow-xl hover:shadow-primary/30;
  z-index: 10;
}

.pricing-popular::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 5px;
  background: linear-gradient(90deg, transparent, rgb(45, 212, 191), transparent);
  border-radius: 5px;
}