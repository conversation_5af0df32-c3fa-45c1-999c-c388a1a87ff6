import { supabase } from '../lib/supabase';

class LogService {
  constructor() {
    this.queue = [];
    this.processing = false;
    this.maxQueueSize = 20;
    this.flushInterval = 10000; // 10 seconds
    
    // Set up interval to flush logs
    setInterval(() => this.flushLogs(), this.flushInterval);
    
    // Listen for unload event to flush logs before page close
    window.addEventListener('beforeunload', () => this.flushLogs(true));
  }
  
  /**
   * Log an error
   * @param {string} userId - User ID (can be null for anonymous users)
   * @param {string} errorType - Type of error
   * @param {string} errorMessage - Error message
   * @param {string} errorStack - Error stack trace
   * @param {string} component - Component where error occurred
   * @param {Object} additionalData - Additional data about the error
   */
  logError(userId, errorType, errorMessage, errorStack = null, component = null, additionalData = {}) {
    console.error(`[${errorType}] ${errorMessage}`, { component, additionalData, errorStack });
    
    const logEntry = {
      type: 'error',
      userId: userId || 'anonymous',
      errorType,
      errorMessage,
      errorStack,
      component,
      pageUrl: window.location.href,
      additionalData,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };
    
    this.addToQueue(logEntry);
  }
  
  /**
   * Log user activity
   * @param {string} userId - User ID
   * @param {string} activityType - Type of activity
   * @param {Object} activityData - Additional data about the activity
   */
  logActivity(userId, activityType, activityData = {}) {
    if (!userId) return;
    
    const logEntry = {
      type: 'activity',
      userId,
      activityType,
      activityData,
      pageUrl: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };
    
    this.addToQueue(logEntry);
  }
  
  /**
   * Add log entry to queue
   * @param {Object} logEntry - Log entry to add
   */
  addToQueue(logEntry) {
    this.queue.push(logEntry);
    
    // If queue is getting too large, flush it
    if (this.queue.length >= this.maxQueueSize) {
      this.flushLogs();
    }
  }
  
  /**
   * Flush logs to server
   * @param {boolean} immediate - Whether to flush immediately (bypass debounce)
   */
  async flushLogs(immediate = false) {
    // If already processing or queue is empty, skip
    if ((this.processing && !immediate) || this.queue.length === 0) return;
    
    this.processing = true;
    const logsToProcess = [...this.queue];
    this.queue = [];
    
    try {
      // Process error logs
      const errorLogs = logsToProcess.filter(log => log.type === 'error');
      if (errorLogs.length > 0) {
        await supabase.from('error_logs').insert(errorLogs.map(log => ({
          user_id: log.userId === 'anonymous' ? null : log.userId,
          error_type: log.errorType,
          error_message: log.errorMessage,
          error_stack: log.errorStack,
          component: log.component,
          page_url: log.pageUrl,
          additional_data: log.additionalData,
          ip_address: null, // We don't collect IP addresses on the client
          user_agent: log.userAgent,
          created_at: log.timestamp
        })));
      }
      
      // Process activity logs
      const activityLogs = logsToProcess.filter(log => log.type === 'activity');
      if (activityLogs.length > 0) {
        await supabase.from('user_activity_logs').insert(activityLogs.map(log => ({
          user_id: log.userId,
          activity_type: log.activityType,
          activity_data: log.activityData,
          ip_address: null, // We don't collect IP addresses on the client
          user_agent: log.userAgent,
          created_at: log.timestamp
        })));
      }
    } catch (error) {
      console.error('Error flushing logs:', error);
      
      // Add logs back to queue if they failed to process
      this.queue = [...logsToProcess, ...this.queue];
    } finally {
      this.processing = false;
    }
  }
}

// Create singleton instance
const logService = new LogService();

export default logService;
