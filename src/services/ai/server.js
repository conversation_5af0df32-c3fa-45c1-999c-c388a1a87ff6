import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { GoogleGenerativeAI } from "@google/generative-ai";
import { createClient } from '@supabase/supabase-js';
import morgan from 'morgan';
import dotenv from 'dotenv';
import createLogger from '../logger.js';

dotenv.config();

const logger = createLogger('ai');

// Initialize Gemini API
const genAI = new GoogleGenerativeAI(process.env.VITE_GEMINI_API_KEY);

// Initialize Supabase
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

const app = express();

// Middleware
app.use(cors());
app.use(helmet());
app.use(compression());
app.use(express.json());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Routes
app.post('/api/generate', async (req, res) => {
  try {
    const { prompt, category } = req.body;

    // Check cache first
    const { data: cachedResponse } = await supabase
      .from('ai_responses')
      .select('*')
      .eq('keyword', prompt.toLowerCase())
      .single();

    if (cachedResponse) {
      return res.json({ ...cachedResponse, cached: true });
    }

    // Generate new response
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    const result = await model.generateContent(prompt);
    
    const response = {
      content: result.response.text(),
      category: category || 'general',
      cached: false
    };

    // Cache the response
    await supabase.from('ai_responses').insert({
      keyword: prompt.toLowerCase(),
      content: response.content,
      category: response.category
    });

    res.json(response);
  } catch (error) {
    logger.error('Error generating response:', error);
    res.status(500).json({ error: 'Failed to generate response' });
  }
});

const PORT = process.env.AI_SERVICE_PORT || 3001;

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (err) => {
  logger.error('Unhandled rejection:', err);
});

app.listen(PORT, () => {
  logger.info(`AI service running on port ${PORT}`);
});