/**
 * RealTimeThreatService.js
 * 
 * This service provides real-time threat intelligence data for visualization.
 * In a production environment, this would connect to actual threat intelligence APIs.
 * For demonstration purposes, it generates realistic mock data.
 */

// Major countries for source of attacks
const SOURCE_COUNTRIES = [
  { name: 'Russia', lat: 55.7558, lng: 37.6173, weight: 25 },
  { name: 'China', lat: 39.9042, lng: 116.4074, weight: 25 },
  { name: 'North Korea', lat: 39.0392, lng: 125.7625, weight: 15 },
  { name: 'Iran', lat: 35.6892, lng: 51.3890, weight: 15 },
  { name: 'United States', lat: 37.0902, lng: -95.7129, weight: 10 },
  { name: 'Brazil', lat: -14.2350, lng: -51.9253, weight: 5 },
  { name: 'Ukraine', lat: 48.3794, lng: 31.1656, weight: 5 }
];

// Target countries (more diverse)
const TARGET_COUNTRIES = [
  { name: 'United States', lat: 37.0902, lng: -95.7129, weight: 30 },
  { name: 'United Kingdom', lat: 51.5074, lng: -0.1278, weight: 10 },
  { name: 'Germany', lat: 51.1657, lng: 10.4515, weight: 10 },
  { name: 'France', lat: 46.2276, lng: 2.2137, weight: 8 },
  { name: 'Japan', lat: 36.2048, lng: 138.2529, weight: 8 },
  { name: 'South Korea', lat: 35.9078, lng: 127.7669, weight: 7 },
  { name: 'Australia', lat: -25.2744, lng: 133.7751, weight: 7 },
  { name: 'India', lat: 20.5937, lng: 78.9629, weight: 7 },
  { name: 'Canada', lat: 56.1304, lng: -106.3468, weight: 5 },
  { name: 'Italy', lat: 41.8719, lng: 12.5674, weight: 4 },
  { name: 'Spain', lat: 40.4637, lng: -3.7492, weight: 4 }
];

// Attack types with weights
const ATTACK_TYPES = [
  { name: 'Ransomware', weight: 25, color: '#ff0000' },
  { name: 'DDoS', weight: 20, color: '#ff3300' },
  { name: 'Phishing', weight: 20, color: '#ff6600' },
  { name: 'Malware', weight: 15, color: '#ff9900' },
  { name: 'SQL Injection', weight: 10, color: '#ffcc00' },
  { name: 'XSS', weight: 5, color: '#ffff00' },
  { name: 'Zero-day Exploit', weight: 5, color: '#ff00ff' }
];

// Severity levels with weights
const SEVERITY_LEVELS = [
  { level: 'Critical', weight: 15, color: '#ff0000' },
  { level: 'High', weight: 25, color: '#ff3300' },
  { level: 'Medium', weight: 35, color: '#ffaa00' },
  { level: 'Low', weight: 25, color: '#ffff00' }
];

// Industry sectors with weights
const INDUSTRY_SECTORS = [
  { name: 'Financial Services', weight: 25 },
  { name: 'Healthcare', weight: 20 },
  { name: 'Government', weight: 15 },
  { name: 'Energy', weight: 10 },
  { name: 'Technology', weight: 10 },
  { name: 'Manufacturing', weight: 8 },
  { name: 'Retail', weight: 7 },
  { name: 'Education', weight: 5 }
];

// Helper function to select an item based on weights
const weightedRandom = (items) => {
  const totalWeight = items.reduce((sum, item) => sum + item.weight, 0);
  let random = Math.random() * totalWeight;
  
  for (const item of items) {
    random -= item.weight;
    if (random <= 0) {
      return item;
    }
  }
  
  return items[0]; // Fallback
};

// Add some randomness to coordinates to avoid overlapping arcs
const jitter = () => (Math.random() - 0.5) * 2;

class RealTimeThreatService {
  constructor() {
    this.attacks = [];
    this.statistics = {
      totalAttacks: 0,
      attacksByType: {},
      attacksBySeverity: {},
      attacksBySource: {},
      attacksByTarget: {},
      attacksByIndustry: {}
    };
    this.listeners = [];
  }

  // Initialize the service
  initialize() {
    // Generate initial attacks
    this.generateAttacks(30);
    
    // Update statistics
    this.updateStatistics();
    
    // Return this for chaining
    return this;
  }

  // Generate a batch of attacks
  generateAttacks(count = 10) {
    const newAttacks = [];
    
    for (let i = 0; i < count; i++) {
      const source = weightedRandom(SOURCE_COUNTRIES);
      const target = weightedRandom(TARGET_COUNTRIES);
      
      // Ensure source and target are different
      if (source.name === target.name) continue;
      
      const attackType = weightedRandom(ATTACK_TYPES);
      const severity = weightedRandom(SEVERITY_LEVELS);
      const industry = weightedRandom(INDUSTRY_SECTORS);
      
      const attack = {
        id: `attack-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        startLat: source.lat + jitter() * 0.5,
        startLng: source.lng + jitter() * 0.5,
        endLat: target.lat + jitter() * 0.5,
        endLng: target.lng + jitter() * 0.5,
        color: severity.color,
        type: attackType.name,
        typeColor: attackType.color,
        source: source.name,
        target: target.name,
        severity: severity.level,
        industry: industry.name,
        timestamp: new Date().toISOString(),
        active: true
      };
      
      newAttacks.push(attack);
    }
    
    this.attacks = [...this.attacks, ...newAttacks];
    this.statistics.totalAttacks += newAttacks.length;
    
    // Notify listeners
    this.notifyListeners();
    
    return newAttacks;
  }

  // Update attack statistics
  updateStatistics() {
    // Reset statistics
    this.statistics.attacksByType = {};
    this.statistics.attacksBySeverity = {};
    this.statistics.attacksBySource = {};
    this.statistics.attacksByTarget = {};
    this.statistics.attacksByIndustry = {};
    
    // Count attacks by different dimensions
    this.attacks.forEach(attack => {
      // By type
      this.statistics.attacksByType[attack.type] = (this.statistics.attacksByType[attack.type] || 0) + 1;
      
      // By severity
      this.statistics.attacksBySeverity[attack.severity] = (this.statistics.attacksBySeverity[attack.severity] || 0) + 1;
      
      // By source
      this.statistics.attacksBySource[attack.source] = (this.statistics.attacksBySource[attack.source] || 0) + 1;
      
      // By target
      this.statistics.attacksByTarget[attack.target] = (this.statistics.attacksByTarget[attack.target] || 0) + 1;
      
      // By industry
      this.statistics.attacksByIndustry[attack.industry] = (this.statistics.attacksByIndustry[attack.industry] || 0) + 1;
    });
    
    return this.statistics;
  }

  // Get all active attacks
  getActiveAttacks() {
    return this.attacks.filter(attack => attack.active);
  }

  // Get attack statistics
  getStatistics() {
    return this.statistics;
  }

  // Start real-time updates
  startRealTimeUpdates(interval = 5000) {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    
    this.updateInterval = setInterval(() => {
      // Remove some old attacks
      this.attacks = this.attacks.filter((_, index) => {
        return index >= Math.floor(Math.random() * 5) || index >= this.attacks.length - 30;
      });
      
      // Add new attacks
      this.generateAttacks(Math.floor(Math.random() * 5) + 3);
      
      // Update statistics
      this.updateStatistics();
      
      // Notify listeners
      this.notifyListeners();
    }, interval);
    
    return this;
  }

  // Stop real-time updates
  stopRealTimeUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    return this;
  }

  // Add a listener for updates
  addListener(callback) {
    if (typeof callback === 'function') {
      this.listeners.push(callback);
    }
    
    return this;
  }

  // Remove a listener
  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
    
    return this;
  }

  // Notify all listeners
  notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener({
          attacks: this.getActiveAttacks(),
          statistics: this.getStatistics()
        });
      } catch (error) {
        console.error('Error in threat intelligence listener:', error);
      }
    });
  }

  // Singleton pattern
  static getInstance() {
    if (!RealTimeThreatService.instance) {
      RealTimeThreatService.instance = new RealTimeThreatService();
    }
    
    return RealTimeThreatService.instance;
  }
}

export default RealTimeThreatService;
