/**
 * Threat Correlation Service
 * 
 * This service analyzes and correlates data from multiple threat intelligence sources
 * to identify relationships between different threats, actors, and indicators.
 */

import apiManager from './api/apiManager';

class ThreatCorrelationService {
  constructor() {
    this.initialized = false;
    this.correlationRules = [];
    this.threatGraph = {
      nodes: [],
      edges: []
    };
  }

  /**
   * Initialize the correlation service
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    // Initialize API manager to ensure all services are available
    apiManager.initialize();
    
    // Set up correlation rules
    this.setupCorrelationRules();
    
    this.initialized = true;
  }

  /**
   * Set up the rules used for correlating threats
   */
  setupCorrelationRules() {
    // IP to Malware correlation
    this.correlationRules.push({
      id: 'ip-malware',
      name: 'IP to Malware',
      description: 'Correlates IP addresses with malware samples',
      sources: ['virusTotal', 'abuseIPDB'],
      correlationFunction: this.correlateIPToMalware.bind(this)
    });

    // Vulnerability to Threat Actor correlation
    this.correlationRules.push({
      id: 'vuln-actor',
      name: 'Vulnerability to Threat Actor',
      description: 'Correlates vulnerabilities with known threat actors',
      sources: ['nvd', 'otx'],
      correlationFunction: this.correlateVulnerabilityToActor.bind(this)
    });

    // Domain to IP correlation
    this.correlationRules.push({
      id: 'domain-ip',
      name: 'Domain to IP',
      description: 'Correlates domains with IP addresses',
      sources: ['virusTotal', 'shodan'],
      correlationFunction: this.correlateDomainToIP.bind(this)
    });

    // Malware to Technique correlation
    this.correlationRules.push({
      id: 'malware-technique',
      name: 'Malware to Technique',
      description: 'Correlates malware with MITRE ATT&CK techniques',
      sources: ['virusTotal', 'otx'],
      correlationFunction: this.correlateMalwareToTechnique.bind(this)
    });
  }

  /**
   * Correlate an IP address with malware samples
   * @param {string} ip - IP address to correlate
   * @returns {Promise<Array>} - Array of correlated malware
   */
  async correlateIPToMalware(ip) {
    try {
      const correlations = [];
      
      // Get data from VirusTotal
      const vtService = apiManager.getService('virusTotal');
      if (vtService) {
        const vtData = await vtService.getIpReport(ip);
        if (vtData && vtData.data && vtData.data.attributes && vtData.data.attributes.last_analysis_results) {
          // Extract malware detections
          const detections = Object.values(vtData.data.attributes.last_analysis_results)
            .filter(result => result.category === 'malicious')
            .map(result => ({
              source: 'VirusTotal',
              engineName: result.engine_name,
              malwareName: result.result,
              confidence: 'high',
              timestamp: new Date().toISOString()
            }));
          
          correlations.push(...detections);
        }
      }
      
      // Get data from AbuseIPDB
      const abuseService = apiManager.getService('abuseIPDB');
      if (abuseService) {
        const abuseData = await abuseService.checkIP(ip);
        if (abuseData && abuseData.data && abuseData.data.reports) {
          // Extract malware mentions from reports
          const malwareReports = abuseData.data.reports
            .filter(report => report.comment && report.comment.toLowerCase().includes('malware'))
            .map(report => ({
              source: 'AbuseIPDB',
              reporterId: report.reporterId,
              malwareName: this.extractMalwareName(report.comment),
              confidence: 'medium',
              timestamp: report.reportedAt
            }));
          
          correlations.push(...malwareReports);
        }
      }
      
      return correlations;
    } catch (error) {
      console.error('Error correlating IP to malware:', error);
      return [];
    }
  }

  /**
   * Correlate a vulnerability with threat actors
   * @param {string} cveId - CVE ID to correlate
   * @returns {Promise<Array>} - Array of correlated threat actors
   */
  async correlateVulnerabilityToActor(cveId) {
    try {
      const correlations = [];
      
      // Get data from OTX
      const otxService = apiManager.getService('otx');
      if (otxService) {
        const pulses = await otxService.searchPulses(cveId);
        if (pulses && pulses.results) {
          // Extract threat actor mentions
          const actorMentions = pulses.results
            .filter(pulse => pulse.author_name && pulse.tags)
            .map(pulse => {
              // Look for APT or threat group tags
              const actorTags = pulse.tags.filter(tag => 
                tag.toLowerCase().includes('apt') || 
                tag.toLowerCase().includes('group') ||
                tag.toLowerCase().includes('threat actor')
              );
              
              if (actorTags.length > 0) {
                return {
                  source: 'AlienVault OTX',
                  pulseId: pulse.id,
                  actorNames: actorTags,
                  author: pulse.author_name,
                  confidence: 'medium',
                  timestamp: pulse.created
                };
              }
              return null;
            })
            .filter(Boolean);
          
          correlations.push(...actorMentions);
        }
      }
      
      // Simulate additional data sources
      // In a real implementation, this would come from other intelligence sources
      if (cveId.includes('2021')) {
        correlations.push({
          source: 'Simulated Intelligence',
          actorNames: ['APT29', 'Cozy Bear'],
          confidence: 'high',
          timestamp: new Date().toISOString()
        });
      }
      
      return correlations;
    } catch (error) {
      console.error('Error correlating vulnerability to actor:', error);
      return [];
    }
  }

  /**
   * Correlate a domain with IP addresses
   * @param {string} domain - Domain to correlate
   * @returns {Promise<Array>} - Array of correlated IPs
   */
  async correlateDomainToIP(domain) {
    try {
      const correlations = [];
      
      // Get data from VirusTotal
      const vtService = apiManager.getService('virusTotal');
      if (vtService) {
        const vtData = await vtService.getDomainReport(domain);
        if (vtData && vtData.data && vtData.data.attributes && vtData.data.attributes.last_dns_records) {
          // Extract IP addresses from DNS records
          const ipAddresses = vtData.data.attributes.last_dns_records
            .filter(record => record.type === 'A' || record.type === 'AAAA')
            .map(record => ({
              source: 'VirusTotal',
              ipAddress: record.value,
              recordType: record.type,
              confidence: 'high',
              timestamp: new Date().toISOString()
            }));
          
          correlations.push(...ipAddresses);
        }
      }
      
      // Get data from Shodan
      const shodanService = apiManager.getService('shodan');
      if (shodanService) {
        const shodanData = await shodanService.getDomainInfo(domain);
        if (shodanData && shodanData.data) {
          // Extract IP addresses
          const ipAddresses = shodanData.data
            .filter(record => record.type === 'A' || record.type === 'AAAA')
            .map(record => ({
              source: 'Shodan',
              ipAddress: record.value,
              recordType: record.type,
              confidence: 'high',
              timestamp: new Date().toISOString()
            }));
          
          correlations.push(...ipAddresses);
        }
      }
      
      return correlations;
    } catch (error) {
      console.error('Error correlating domain to IP:', error);
      return [];
    }
  }

  /**
   * Correlate malware with MITRE ATT&CK techniques
   * @param {string} malwareName - Malware name to correlate
   * @returns {Promise<Array>} - Array of correlated techniques
   */
  async correlateMalwareToTechnique(malwareName) {
    try {
      const correlations = [];
      
      // Get data from OTX
      const otxService = apiManager.getService('otx');
      if (otxService) {
        const pulses = await otxService.searchPulses(malwareName);
        if (pulses && pulses.results) {
          // Extract MITRE ATT&CK technique mentions
          const techniqueMentions = pulses.results
            .filter(pulse => pulse.tags)
            .flatMap(pulse => {
              // Look for MITRE ATT&CK technique tags (T1XXX format)
              const techniqueTags = pulse.tags.filter(tag => 
                /T\d{4}(\.\d{3})?/.test(tag)
              );
              
              return techniqueTags.map(technique => ({
                source: 'AlienVault OTX',
                pulseId: pulse.id,
                technique,
                confidence: 'medium',
                timestamp: pulse.created
              }));
            });
          
          correlations.push(...techniqueMentions);
        }
      }
      
      // Simulate MITRE ATT&CK mapping
      // In a real implementation, this would come from a MITRE ATT&CK API or database
      const commonTechniques = {
        'emotet': ['T1566', 'T1204', 'T1027'],
        'trickbot': ['T1055', 'T1083', 'T1082'],
        'ryuk': ['T1486', 'T1490', 'T1489'],
        'wannacry': ['T1210', 'T1486', 'T1083'],
        'cobalt strike': ['T1059', 'T1057', 'T1106']
      };
      
      const lowerMalwareName = malwareName.toLowerCase();
      for (const [knownMalware, techniques] of Object.entries(commonTechniques)) {
        if (lowerMalwareName.includes(knownMalware)) {
          const simulatedTechniques = techniques.map(technique => ({
            source: 'MITRE ATT&CK Database',
            technique,
            confidence: 'high',
            timestamp: new Date().toISOString()
          }));
          
          correlations.push(...simulatedTechniques);
        }
      }
      
      return correlations;
    } catch (error) {
      console.error('Error correlating malware to technique:', error);
      return [];
    }
  }

  /**
   * Build a threat graph based on correlations
   * @param {Object} startNode - Starting node for the graph
   * @param {number} depth - How many levels to explore
   * @returns {Promise<Object>} - Threat graph with nodes and edges
   */
  async buildThreatGraph(startNode, depth = 2) {
    try {
      // Reset the graph
      this.threatGraph = {
        nodes: [],
        edges: []
      };
      
      // Add the starting node
      this.threatGraph.nodes.push({
        id: startNode.id,
        type: startNode.type,
        label: startNode.label,
        data: startNode.data
      });
      
      // Build the graph recursively
      await this.expandNode(startNode, depth);
      
      return this.threatGraph;
    } catch (error) {
      console.error('Error building threat graph:', error);
      return {
        nodes: [],
        edges: []
      };
    }
  }

  /**
   * Expand a node in the threat graph
   * @param {Object} node - Node to expand
   * @param {number} depth - Remaining depth to explore
   */
  async expandNode(node, depth) {
    if (depth <= 0) {
      return;
    }
    
    let correlations = [];
    
    // Apply appropriate correlation rules based on node type
    switch (node.type) {
      case 'ip':
        correlations = await this.correlateIPToMalware(node.id);
        // Add malware nodes
        for (const correlation of correlations) {
          const malwareNode = {
            id: `malware-${correlation.malwareName}`,
            type: 'malware',
            label: correlation.malwareName,
            data: correlation
          };
          
          // Add node if it doesn't exist
          if (!this.threatGraph.nodes.some(n => n.id === malwareNode.id)) {
            this.threatGraph.nodes.push(malwareNode);
          }
          
          // Add edge
          this.threatGraph.edges.push({
            source: node.id,
            target: malwareNode.id,
            label: 'hosts',
            confidence: correlation.confidence
          });
          
          // Recursively expand this node
          await this.expandNode(malwareNode, depth - 1);
        }
        break;
        
      case 'vulnerability':
        correlations = await this.correlateVulnerabilityToActor(node.id);
        // Add actor nodes
        for (const correlation of correlations) {
          for (const actorName of correlation.actorNames) {
            const actorNode = {
              id: `actor-${actorName}`,
              type: 'actor',
              label: actorName,
              data: correlation
            };
            
            // Add node if it doesn't exist
            if (!this.threatGraph.nodes.some(n => n.id === actorNode.id)) {
              this.threatGraph.nodes.push(actorNode);
            }
            
            // Add edge
            this.threatGraph.edges.push({
              source: node.id,
              target: actorNode.id,
              label: 'exploited by',
              confidence: correlation.confidence
            });
            
            // Recursively expand this node
            await this.expandNode(actorNode, depth - 1);
          }
        }
        break;
        
      case 'domain':
        correlations = await this.correlateDomainToIP(node.id);
        // Add IP nodes
        for (const correlation of correlations) {
          const ipNode = {
            id: `ip-${correlation.ipAddress}`,
            type: 'ip',
            label: correlation.ipAddress,
            data: correlation
          };
          
          // Add node if it doesn't exist
          if (!this.threatGraph.nodes.some(n => n.id === ipNode.id)) {
            this.threatGraph.nodes.push(ipNode);
          }
          
          // Add edge
          this.threatGraph.edges.push({
            source: node.id,
            target: ipNode.id,
            label: 'resolves to',
            confidence: correlation.confidence
          });
          
          // Recursively expand this node
          await this.expandNode(ipNode, depth - 1);
        }
        break;
        
      case 'malware':
        correlations = await this.correlateMalwareToTechnique(node.label);
        // Add technique nodes
        for (const correlation of correlations) {
          const techniqueNode = {
            id: `technique-${correlation.technique}`,
            type: 'technique',
            label: correlation.technique,
            data: correlation
          };
          
          // Add node if it doesn't exist
          if (!this.threatGraph.nodes.some(n => n.id === techniqueNode.id)) {
            this.threatGraph.nodes.push(techniqueNode);
          }
          
          // Add edge
          this.threatGraph.edges.push({
            source: node.id,
            target: techniqueNode.id,
            label: 'uses',
            confidence: correlation.confidence
          });
          
          // Recursively expand this node
          await this.expandNode(techniqueNode, depth - 1);
        }
        break;
    }
  }

  /**
   * Extract malware name from a text comment
   * @param {string} comment - Comment text
   * @returns {string} - Extracted malware name or 'Unknown Malware'
   */
  extractMalwareName(comment) {
    if (!comment) return 'Unknown Malware';
    
    // List of common malware names to look for
    const commonMalware = [
      'emotet', 'trickbot', 'ryuk', 'wannacry', 'petya', 'notpetya', 
      'locky', 'cryptolocker', 'gandcrab', 'maze', 'revil', 'darkside',
      'conti', 'qakbot', 'dridex', 'ursnif', 'zloader', 'icedid'
    ];
    
    const lowerComment = comment.toLowerCase();
    
    // Check for common malware names
    for (const malware of commonMalware) {
      if (lowerComment.includes(malware)) {
        return malware.charAt(0).toUpperCase() + malware.slice(1);
      }
    }
    
    // Look for patterns like "malware: XYZ" or "XYZ malware"
    const malwarePattern = /(?:malware[:\s]+)([a-z0-9_\-.]+)|([a-z0-9_\-.]+)(?:\s+malware)/i;
    const match = lowerComment.match(malwarePattern);
    
    if (match && (match[1] || match[2])) {
      const name = match[1] || match[2];
      return name.charAt(0).toUpperCase() + name.slice(1);
    }
    
    return 'Unknown Malware';
  }

  /**
   * Get all available correlation rules
   * @returns {Array} - Array of correlation rules
   */
  getCorrelationRules() {
    return this.correlationRules;
  }

  /**
   * Run a specific correlation rule
   * @param {string} ruleId - ID of the rule to run
   * @param {string} value - Value to correlate
   * @returns {Promise<Array>} - Correlation results
   */
  async runCorrelation(ruleId, value) {
    const rule = this.correlationRules.find(r => r.id === ruleId);
    if (!rule) {
      throw new Error(`Correlation rule ${ruleId} not found`);
    }
    
    return await rule.correlationFunction(value);
  }
}

// Create and export a singleton instance
const threatCorrelationService = new ThreatCorrelationService();
export default threatCorrelationService;
