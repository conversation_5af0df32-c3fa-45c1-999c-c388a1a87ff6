/**
 * ThreatDataService.js
 * 
 * This service handles the generation, management, and analysis of threat data.
 * It provides both mock data for development and can integrate with real threat intelligence APIs.
 */

// Configuration for the threat data service
const THREAT_DATA_CONFIG = {
  updateInterval: 5 * 60 * 1000, // 5 minutes in milliseconds
  historicalDataRetention: 30, // days
  maxCachedAttacks: 1000,
  realTimeUpdates: true
};

// Country codes for generating realistic data
const COUNTRY_CODES = {
  'United States': 'USA',
  'Russia': 'RUS',
  'China': 'CHN',
  'India': 'IND',
  'Brazil': 'BRA',
  'United Kingdom': 'GBR',
  'Germany': 'DEU',
  'France': 'FRA',
  'Japan': 'JPN',
  'South Korea': 'KOR',
  'Australia': 'AUS',
  'Canada': 'CAN',
  'Italy': 'ITA',
  'Spain': 'ESP',
  'Mexico': 'MEX',
  'Indonesia': 'IDN',
  'Turkey': 'TUR',
  'Saudi Arabia': 'SAU',
  'South Africa': 'ZAF',
  'Nigeria': 'NGA',
  'Egypt': 'EGY',
  'Iran': 'IRN',
  'Pakistan': 'PAK',
  'Vietnam': 'VNM',
  'Philippines': 'PHL',
  'Thailand': 'THA',
  'Singapore': 'SGP',
  'Malaysia': 'MYS',
  'Ukraine': 'UKR',
  'Poland': 'POL',
  'Sweden': 'SWE',
  'Norway': 'NOR',
  'Finland': 'FIN',
  'Denmark': 'DNK',
  'Netherlands': 'NLD',
  'Belgium': 'BEL',
  'Switzerland': 'CHE',
  'Austria': 'AUT',
  'Israel': 'ISR',
  'United Arab Emirates': 'ARE',
  'Argentina': 'ARG',
  'Chile': 'CHL',
  'Colombia': 'COL',
  'Peru': 'PER',
  'Venezuela': 'VEN',
  'New Zealand': 'NZL'
};

// Attack types for generating realistic data
const ATTACK_TYPES = [
  'DDoS',
  'Phishing',
  'Ransomware',
  'SQL Injection',
  'XSS',
  'Zero-day Exploit',
  'Malware',
  'Man-in-the-Middle',
  'Brute Force',
  'Social Engineering',
  'Supply Chain',
  'Credential Stuffing',
  'API Abuse',
  'DNS Poisoning',
  'IoT Botnet',
  'Cryptojacking'
];

// Severity levels
const SEVERITY_LEVELS = ['Critical', 'High', 'Medium', 'Low'];

// In-memory cache for threat data
let threatDataCache = {
  attacks: [],
  statistics: {
    totalAttacks: 0,
    attacksByCountry: {},
    attacksByType: {},
    attacksBySeverity: {}
  },
  lastUpdated: null
};

// Function to generate a realistic attack
const generateAttack = (id) => {
  const countryNames = Object.keys(COUNTRY_CODES);
  const sourceCountryName = countryNames[Math.floor(Math.random() * countryNames.length)];
  
  // Ensure target is different from source
  let targetCountryName;
  do {
    targetCountryName = countryNames[Math.floor(Math.random() * countryNames.length)];
  } while (targetCountryName === sourceCountryName);
  
  const attackType = ATTACK_TYPES[Math.floor(Math.random() * ATTACK_TYPES.length)];
  const severity = SEVERITY_LEVELS[Math.floor(Math.random() * SEVERITY_LEVELS.length)];
  
  // More likely to be active if severity is higher
  const severityIndex = SEVERITY_LEVELS.indexOf(severity);
  const activeChance = 0.2 + (severityIndex * 0.2); // 0.2 for Low, 0.8 for Critical
  const active = Math.random() < activeChance;
  
  return {
    id: id || `attack-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    source: {
      name: sourceCountryName,
      code: COUNTRY_CODES[sourceCountryName]
    },
    target: {
      name: targetCountryName,
      code: COUNTRY_CODES[targetCountryName]
    },
    type: attackType,
    severity: severity,
    active: active,
    timestamp: new Date().toISOString(),
    details: {
      ipAddress: `${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`,
      port: Math.floor(Math.random() * 65536),
      protocol: Math.random() > 0.5 ? 'TCP' : 'UDP',
      payload: Math.random() > 0.7 ? 'Encrypted' : 'Plaintext',
      success: Math.random() > 0.3
    }
  };
};

// Function to generate initial mock data
const generateInitialMockData = (count = 50) => {
  const attacks = [];
  for (let i = 0; i < count; i++) {
    attacks.push(generateAttack());
  }
  return attacks;
};

// Function to update statistics based on attacks
const updateStatistics = (attacks) => {
  const statistics = {
    totalAttacks: attacks.length,
    attacksByCountry: {},
    attacksByType: {},
    attacksBySeverity: {},
    topSourceCountries: [],
    topTargetCountries: [],
    topAttackTypes: []
  };
  
  // Process each attack
  attacks.forEach(attack => {
    // By source country
    const sourceCountry = attack.source.name;
    statistics.attacksByCountry[sourceCountry] = statistics.attacksByCountry[sourceCountry] || { 
      outgoing: 0, 
      incoming: 0 
    };
    statistics.attacksByCountry[sourceCountry].outgoing++;
    
    // By target country
    const targetCountry = attack.target.name;
    statistics.attacksByCountry[targetCountry] = statistics.attacksByCountry[targetCountry] || { 
      outgoing: 0, 
      incoming: 0 
    };
    statistics.attacksByCountry[targetCountry].incoming++;
    
    // By attack type
    statistics.attacksByType[attack.type] = (statistics.attacksByType[attack.type] || 0) + 1;
    
    // By severity
    statistics.attacksBySeverity[attack.severity] = (statistics.attacksBySeverity[attack.severity] || 0) + 1;
  });
  
  // Calculate top countries and attack types
  statistics.topSourceCountries = Object.entries(statistics.attacksByCountry)
    .map(([country, data]) => ({ country, count: data.outgoing }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
    
  statistics.topTargetCountries = Object.entries(statistics.attacksByCountry)
    .map(([country, data]) => ({ country, count: data.incoming }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
    
  statistics.topAttackTypes = Object.entries(statistics.attacksByType)
    .map(([type, count]) => ({ type, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
  
  return statistics;
};

// Function to initialize the threat data service
const initializeThreatDataService = () => {
  console.log('Initializing Threat Data Service...');
  
  // Generate initial mock data
  threatDataCache.attacks = generateInitialMockData(100);
  
  // Update statistics
  threatDataCache.statistics = updateStatistics(threatDataCache.attacks);
  
  // Set last updated timestamp
  threatDataCache.lastUpdated = new Date().toISOString();
  
  console.log('Threat Data Service initialized with mock data.');
  
  // Start real-time updates if enabled
  if (THREAT_DATA_CONFIG.realTimeUpdates) {
    startRealTimeUpdates();
  }
  
  return {
    getThreatData,
    getStatistics,
    getAttackById,
    getAttacksByCountry,
    getAttacksByType,
    getAttacksBySeverity,
    addAttack,
    updateAttack,
    startRealTimeUpdates,
    stopRealTimeUpdates
  };
};

// Function to get all threat data
const getThreatData = () => {
  return {
    attacks: [...threatDataCache.attacks],
    statistics: { ...threatDataCache.statistics },
    lastUpdated: threatDataCache.lastUpdated
  };
};

// Function to get statistics only
const getStatistics = () => {
  return { ...threatDataCache.statistics };
};

// Function to get a specific attack by ID
const getAttackById = (id) => {
  return threatDataCache.attacks.find(attack => attack.id === id);
};

// Function to get attacks by country (as source or target)
const getAttacksByCountry = (countryName, asSource = true) => {
  return threatDataCache.attacks.filter(attack => 
    asSource 
      ? attack.source.name === countryName 
      : attack.target.name === countryName
  );
};

// Function to get attacks by type
const getAttacksByType = (type) => {
  return threatDataCache.attacks.filter(attack => attack.type === type);
};

// Function to get attacks by severity
const getAttacksBySeverity = (severity) => {
  return threatDataCache.attacks.filter(attack => attack.severity === severity);
};

// Function to add a new attack
const addAttack = (attack) => {
  const newAttack = {
    ...attack,
    id: attack.id || `attack-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    timestamp: attack.timestamp || new Date().toISOString()
  };
  
  // Add to cache
  threatDataCache.attacks.push(newAttack);
  
  // Limit cache size
  if (threatDataCache.attacks.length > THREAT_DATA_CONFIG.maxCachedAttacks) {
    threatDataCache.attacks = threatDataCache.attacks.slice(-THREAT_DATA_CONFIG.maxCachedAttacks);
  }
  
  // Update statistics
  threatDataCache.statistics = updateStatistics(threatDataCache.attacks);
  
  // Update last updated timestamp
  threatDataCache.lastUpdated = new Date().toISOString();
  
  return newAttack;
};

// Function to update an existing attack
const updateAttack = (id, updates) => {
  const index = threatDataCache.attacks.findIndex(attack => attack.id === id);
  
  if (index === -1) {
    return null;
  }
  
  // Update the attack
  threatDataCache.attacks[index] = {
    ...threatDataCache.attacks[index],
    ...updates,
    lastUpdated: new Date().toISOString()
  };
  
  // Update statistics
  threatDataCache.statistics = updateStatistics(threatDataCache.attacks);
  
  // Update last updated timestamp
  threatDataCache.lastUpdated = new Date().toISOString();
  
  return threatDataCache.attacks[index];
};

// Variables for real-time updates
let realTimeUpdateInterval = null;

// Function to start real-time updates
const startRealTimeUpdates = () => {
  if (realTimeUpdateInterval) {
    return;
  }
  
  console.log('Starting real-time threat data updates...');
  
  // Function to update threat data
  const updateThreatData = () => {
    // Randomly decide how many attacks to add (0-3)
    const attacksToAdd = Math.floor(Math.random() * 4);
    
    for (let i = 0; i < attacksToAdd; i++) {
      addAttack(generateAttack());
    }
    
    // Randomly update some existing attacks
    const attacksToUpdate = Math.floor(Math.random() * 5);
    
    for (let i = 0; i < attacksToUpdate; i++) {
      if (threatDataCache.attacks.length > 0) {
        const randomIndex = Math.floor(Math.random() * threatDataCache.attacks.length);
        const attackToUpdate = threatDataCache.attacks[randomIndex];
        
        // Randomly decide what to update
        const updateType = Math.random();
        
        if (updateType < 0.7) {
          // Update active status (most common update)
          updateAttack(attackToUpdate.id, { active: !attackToUpdate.active });
        } else if (updateType < 0.9) {
          // Update severity (less common)
          const newSeverity = SEVERITY_LEVELS[Math.floor(Math.random() * SEVERITY_LEVELS.length)];
          updateAttack(attackToUpdate.id, { severity: newSeverity });
        } else {
          // Update details (rare)
          updateAttack(attackToUpdate.id, { 
            details: {
              ...attackToUpdate.details,
              success: Math.random() > 0.5,
              payload: Math.random() > 0.7 ? 'Encrypted' : 'Plaintext'
            }
          });
        }
      }
    }
    
    console.log(`Real-time update: Added ${attacksToAdd} attacks, updated ${attacksToUpdate} attacks.`);
  };
  
  // Initial update
  updateThreatData();
  
  // Set interval for regular updates
  realTimeUpdateInterval = setInterval(updateThreatData, THREAT_DATA_CONFIG.updateInterval);
};

// Function to stop real-time updates
const stopRealTimeUpdates = () => {
  if (realTimeUpdateInterval) {
    clearInterval(realTimeUpdateInterval);
    realTimeUpdateInterval = null;
    console.log('Real-time threat data updates stopped.');
  }
};

export default {
  initializeThreatDataService,
  COUNTRY_CODES,
  ATTACK_TYPES,
  SEVERITY_LEVELS
};
