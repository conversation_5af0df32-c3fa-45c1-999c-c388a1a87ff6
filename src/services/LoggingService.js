import { supabase } from '../lib/supabase';

class LoggingService {
  constructor() {
    this.queue = [];
    this.processing = false;
    this.maxQueueSize = 50;
    this.flushInterval = 10000; // 10 seconds
    
    // Set up interval to flush logs
    setInterval(() => this.flushLogs(), this.flushInterval);
    
    // Listen for unload event to flush logs before page close
    window.addEventListener('beforeunload', () => this.flushLogs(true));
  }
  
  /**
   * Log user activity
   * @param {string} userId - User ID
   * @param {string} activityType - Type of activity
   * @param {Object} activityData - Additional data about the activity
   */
  async logActivity(userId, activityType, activityData = {}) {
    if (!userId || !activityType) return;
    
    const logEntry = {
      type: 'activity',
      userId,
      activityType,
      activityData,
      ipAddress: await this.getIpAddress(),
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };
    
    this.addToQueue(logEntry);
  }
  
  /**
   * Log an error
   * @param {string} userId - User ID (can be null for anonymous users)
   * @param {string} errorType - Type of error
   * @param {string} errorMessage - Error message
   * @param {string} errorStack - Error stack trace
   * @param {string} component - Component where error occurred
   * @param {string} pageUrl - URL where error occurred
   * @param {Object} additionalData - Additional data about the error
   */
  async logError(userId, errorType, errorMessage, errorStack = null, component = null, additionalData = {}) {
    const logEntry = {
      type: 'error',
      userId: userId || '00000000-0000-0000-0000-000000000000', // Anonymous user ID
      errorType,
      errorMessage,
      errorStack,
      component,
      pageUrl: window.location.href,
      additionalData,
      ipAddress: await this.getIpAddress(),
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };
    
    this.addToQueue(logEntry);
    
    // For critical errors, flush immediately
    if (errorType === 'critical') {
      this.flushLogs(true);
    }
  }
  
  /**
   * Update daily user stats
   * @param {string} userId - User ID
   * @param {Object} stats - Stats to update
   */
  async updateUserStats(userId, stats = {}) {
    if (!userId) return;
    
    try {
      await supabase.rpc('update_daily_user_stats', {
        user_id: userId,
        login_count: stats.loginCount || 0,
        challenge_attempts: stats.challengeAttempts || 0,
        challenge_completions: stats.challengeCompletions || 0,
        module_progress: stats.moduleProgress || 0,
        coins_earned: stats.coinsEarned || 0,
        coins_spent: stats.coinsSpent || 0,
        time_spent: stats.timeSpent || 0
      });
    } catch (error) {
      console.error('Error updating user stats:', error);
      
      // Log the error but don't add to queue to avoid infinite loop
      console.error('Error updating user stats:', error);
    }
  }
  
  /**
   * Add log entry to queue
   * @param {Object} logEntry - Log entry to add
   */
  addToQueue(logEntry) {
    this.queue.push(logEntry);
    
    // If queue is getting too large, flush it
    if (this.queue.length >= this.maxQueueSize) {
      this.flushLogs();
    }
  }
  
  /**
   * Flush logs to server
   * @param {boolean} immediate - Whether to flush immediately (bypass debounce)
   */
  async flushLogs(immediate = false) {
    // If already processing or queue is empty, skip
    if ((this.processing && !immediate) || this.queue.length === 0) return;
    
    this.processing = true;
    const logsToProcess = [...this.queue];
    this.queue = [];
    
    try {
      // Process activity logs
      const activityLogs = logsToProcess.filter(log => log.type === 'activity');
      if (activityLogs.length > 0) {
        await Promise.all(activityLogs.map(log => 
          supabase.rpc('log_user_activity', {
            user_id: log.userId,
            activity_type: log.activityType,
            activity_data: log.activityData,
            ip_address: log.ipAddress,
            user_agent: log.userAgent
          })
        ));
      }
      
      // Process error logs
      const errorLogs = logsToProcess.filter(log => log.type === 'error');
      if (errorLogs.length > 0) {
        await Promise.all(errorLogs.map(log => 
          supabase.rpc('log_error', {
            user_id: log.userId,
            error_type: log.errorType,
            error_message: log.errorMessage,
            error_stack: log.errorStack,
            component: log.component,
            page_url: log.pageUrl,
            additional_data: log.additionalData,
            ip_address: log.ipAddress,
            user_agent: log.userAgent
          })
        ));
      }
    } catch (error) {
      console.error('Error flushing logs:', error);
      
      // Add logs back to queue if they failed to process
      this.queue = [...logsToProcess, ...this.queue];
    } finally {
      this.processing = false;
    }
  }
  
  /**
   * Get user's IP address
   * @returns {Promise<string>} IP address
   */
  async getIpAddress() {
    try {
      // Use a service to get IP address
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      return 'unknown';
    }
  }
}

// Create singleton instance
const loggingService = new LoggingService();

export default loggingService;
