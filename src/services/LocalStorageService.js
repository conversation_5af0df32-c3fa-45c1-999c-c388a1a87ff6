/**
 * Service to handle all localStorage operations
 * This centralizes our storage logic and provides a consistent API
 */
const LocalStorageService = {
  // Prefix for all keys to avoid conflicts with other applications
  PREFIX: 'xcerberus_',
  
  /**
   * Set a value in localStorage
   * @param {string} key - The key to store the value under
   * @param {any} value - The value to store (will be JSON stringified)
   */
  set: (key, value) => {
    try {
      const prefixedKey = `${LocalStorageService.PREFIX}${key}`;
      localStorage.setItem(prefixedKey, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Error storing data in localStorage:', error);
      return false;
    }
  },
  
  /**
   * Get a value from localStorage
   * @param {string} key - The key to retrieve
   * @param {any} defaultValue - Default value if key doesn't exist
   * @returns {any} The stored value or defaultValue if not found
   */
  get: (key, defaultValue = null) => {
    try {
      const prefixedKey = `${LocalStorageService.PREFIX}${key}`;
      const value = localStorage.getItem(prefixedKey);
      return value ? JSON.parse(value) : defaultValue;
    } catch (error) {
      console.error('Error retrieving data from localStorage:', error);
      return defaultValue;
    }
  },
  
  /**
   * Remove a value from localStorage
   * @param {string} key - The key to remove
   */
  remove: (key) => {
    try {
      const prefixedKey = `${LocalStorageService.PREFIX}${key}`;
      localStorage.removeItem(prefixedKey);
      return true;
    } catch (error) {
      console.error('Error removing data from localStorage:', error);
      return false;
    }
  },
  
  /**
   * Check if a key exists in localStorage
   * @param {string} key - The key to check
   * @returns {boolean} True if the key exists
   */
  exists: (key) => {
    const prefixedKey = `${LocalStorageService.PREFIX}${key}`;
    return localStorage.getItem(prefixedKey) !== null;
  },
  
  /**
   * Clear all xcerberus-related items from localStorage
   */
  clearAll: () => {
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(LocalStorageService.PREFIX)) {
          localStorage.removeItem(key);
        }
      });
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  },
  
  /**
   * Transfer guest data to user account
   * @param {string} userId - The user ID to transfer data to
   */
  transferGuestData: (userId) => {
    try {
      // Get all guest progress data
      const guestProgress = LocalStorageService.get('guest_progress', {});
      const guestAchievements = LocalStorageService.get('guest_achievements', []);
      const guestLastViewed = LocalStorageService.get('guest_last_viewed', {});
      
      // Store as user-specific data
      LocalStorageService.set(`user_${userId}_progress`, guestProgress);
      LocalStorageService.set(`user_${userId}_achievements`, guestAchievements);
      LocalStorageService.set(`user_${userId}_last_viewed`, guestLastViewed);
      
      // Clear guest data
      LocalStorageService.remove('guest_progress');
      LocalStorageService.remove('guest_achievements');
      LocalStorageService.remove('guest_last_viewed');
      
      return true;
    } catch (error) {
      console.error('Error transferring guest data:', error);
      return false;
    }
  }
};

export default LocalStorageService;
