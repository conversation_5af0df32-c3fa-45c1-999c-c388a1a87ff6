const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const winston = require('winston');
const dotenv = require('dotenv');
const { EventEmitter } = require('events');

// Increase max event listeners
EventEmitter.defaultMaxListeners = 20;

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  defaultMeta: { service: 'threat-service' },
  transports: [
    new winston.transports.Console({
      format: winston.format.simple(),
    }),
    new winston.transports.File({ filename: 'logs/threat-service-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/threat-service.log' }),
  ],
});

// Constants for optimization
const MAX_ACTIVE_THREATS = 5;
const PROCESSING_INTERVAL = 5000; // 5 seconds between threats
const MAX_THREATS = 100; // Maximum number of threats to store
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const app = express();

// Apply middleware
app.use(cors());
app.use(helmet());
app.use(compression()); // Add compression for faster data transfer
app.use(express.json());
app.use(morgan('combined'));

// Rate limiting
const apiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // limit each IP to 30 requests per minute
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests from this IP, please try again after a minute'
});

// Apply rate limiting to all threat API endpoints
app.use('/api/threats', apiLimiter);

// In-memory threat cache with optimization
let threatCache = [];
let processedThreats = new Set(); // Track processed threats to avoid duplicates
let lastFetchTime = 0;
let isFetching = false; // Flag to prevent concurrent fetches

// Country mapping for IP geolocation
const countries = [
  { name: 'United States', lat: 37.0902, lng: -95.7129, code: 'US' },
  { name: 'China', lat: 35.8617, lng: 104.1954, code: 'CN' },
  { name: 'Russia', lat: 61.5240, lng: 105.3188, code: 'RU' },
  { name: 'United Kingdom', lat: 55.3781, lng: -3.4360, code: 'GB' },
  { name: 'Germany', lat: 51.1657, lng: 10.4515, code: 'DE' },
  { name: 'Brazil', lat: -14.2350, lng: -51.9253, code: 'BR' },
  { name: 'Australia', lat: -25.2744, lng: 133.7751, code: 'AU' },
  { name: 'India', lat: 20.5937, lng: 78.9629, code: 'IN' },
  { name: 'Japan', lat: 36.2048, lng: 138.2529, code: 'JP' },
  { name: 'South Africa', lat: -30.5595, lng: 22.9375, code: 'ZA' },
  { name: 'Canada', lat: 56.1304, lng: -106.3468, code: 'CA' },
  { name: 'France', lat: 46.2276, lng: 2.2137, code: 'FR' },
  { name: 'Mexico', lat: 23.6345, lng: -102.5528, code: 'MX' },
  { name: 'Indonesia', lat: -0.7893, lng: 113.9213, code: 'ID' },
  { name: 'Nigeria', lat: 9.0820, lng: 8.6753, code: 'NG' },
  { name: 'Spain', lat: 40.4637, lng: -3.7492, code: 'ES' },
  { name: 'Italy', lat: 41.8719, lng: 12.5674, code: 'IT' },
  { name: 'South Korea', lat: 35.9078, lng: 127.7669, code: 'KR' },
  { name: 'Netherlands', lat: 52.1326, lng: 5.2913, code: 'NL' },
  { name: 'Sweden', lat: 60.1282, lng: 18.6435, code: 'SE' },
  { name: 'Singapore', lat: 1.3521, lng: 103.8198, code: 'SG' },
  { name: 'Israel', lat: 31.0461, lng: 34.8516, code: 'IL' },
  { name: 'Ukraine', lat: 48.3794, lng: 31.1656, code: 'UA' },
  { name: 'Turkey', lat: 38.9637, lng: 35.2433, code: 'TR' },
  { name: 'Poland', lat: 51.9194, lng: 19.1451, code: 'PL' },
  { name: 'Unknown', lat: 0, lng: 0, code: 'XX' }
];

// Get geolocation data from AbuseIPDB
const getIPGeolocation = async (ip) => {
  try {
    const ABUSEIPDB_API_KEY = process.env.ABUSEIPDB_API_KEY;
    if (!ABUSEIPDB_API_KEY) {
      logger.error('AbuseIPDB API key not found in environment variables');
      return null;
    }

    const response = await axios.get('https://api.abuseipdb.com/api/v2/check', {
      params: {
        ipAddress: ip,
        maxAgeInDays: 90,
        verbose: true
      },
      headers: {
        'Key': ABUSEIPDB_API_KEY,
        'Accept': 'application/json'
      }
    });

    if (response.data && response.data.data) {
      const data = response.data.data;
      return {
        countryCode: data.countryCode,
        countryName: data.countryName,
        abuseScore: data.abuseConfidenceScore,
        isp: data.isp,
        domain: data.domain,
        usageType: data.usageType,
        totalReports: data.totalReports
      };
    }
    return null;
  } catch (error) {
    logger.error('Error fetching IP geolocation from AbuseIPDB:', error);
    return null;
  }
};

// Fetch recent reports from AbuseIPDB
const fetchAbuseIPDBReports = async () => {
  try {
    const ABUSEIPDB_API_KEY = process.env.ABUSEIPDB_API_KEY;
    if (!ABUSEIPDB_API_KEY) {
      logger.error('AbuseIPDB API key not found in environment variables');
      return [];
    }

    const response = await axios.get('https://api.abuseipdb.com/api/v2/blacklist', {
      params: {
        confidenceMinimum: 90,
        limit: 25
      },
      headers: {
        'Key': ABUSEIPDB_API_KEY,
        'Accept': 'application/json'
      }
    });

    return response.data && response.data.data ? response.data.data : [];
  } catch (error) {
    logger.error('Error fetching AbuseIPDB blacklist:', error);
    return [];
  }
};

// Fetch real threat data combining OTX and AbuseIPDB
const fetchThreatData = async () => {
  try {
    // Get blacklisted IPs from AbuseIPDB
    const blacklistedIPs = await fetchAbuseIPDBReports();
    logger.info('Fetched blacklisted IPs:', blacklistedIPs.length);

    // Process blacklisted IPs into threats
    const threats = [];
    const threatTypes = ['Malware', 'Ransomware', 'Phishing', 'DDoS', 'Data Breach', 'Brute Force', 'Port Scan'];

    // Process up to 20 IPs
    const ipsToProcess = blacklistedIPs.slice(0, 20);

    for (let i = 0; i < ipsToProcess.length; i++) {
      const ip = ipsToProcess[i];

      // Get detailed information about this IP
      const geoData = await getIPGeolocation(ip.ipAddress);

      if (geoData) {
        // Find source country based on country code
        const sourceCountry = countries.find(c => c.code === geoData.countryCode) ||
                             countries[Math.floor(Math.random() * (countries.length - 1))];

        // Get a different random country as the target
        let targetCountry;
        do {
          targetCountry = countries[Math.floor(Math.random() * (countries.length - 1))];
        } while (targetCountry.name === sourceCountry.name);

        // Determine threat type based on abuse score
        let threatType = threatTypes[Math.floor(Math.random() * threatTypes.length)];
        if (geoData.usageType) {
          if (geoData.usageType.includes('Data Center')) threatType = 'DDoS';
          else if (geoData.usageType.includes('Hosting')) threatType = 'Malware';
        }

        // Determine severity based on abuse confidence score
        let severity = 1; // Default: Low
        if (geoData.abuseScore >= 90) severity = 3; // High
        else if (geoData.abuseScore >= 70) severity = 2; // Medium

        threats.push({
          id: `threat-${Date.now()}-${i}`,
          type: threatType,
          severity: severity,
          source: {
            name: sourceCountry.name,
            lat: sourceCountry.lat,
            lng: sourceCountry.lng
          },
          target: {
            name: targetCountry.name,
            lat: targetCountry.lat,
            lng: targetCountry.lng
          },
          timestamp: new Date().getTime(),
          details: {
            name: `${threatType} Attack (Confidence: ${geoData.abuseScore}%)`,
            description: `Reported ${ip.totalReports} times. ${geoData.isp ? 'ISP: ' + geoData.isp : ''}`,
            ip: ip.ipAddress,
            reports: ip.totalReports
          }
        });
      }
    }

    // If we didn't get enough threats, add some fallback ones
    if (threats.length < 10) {
      const additionalThreats = generateFallbackThreats(10 - threats.length);
      threats.push(...additionalThreats);
    }

    return threats;
  } catch (error) {
    logger.error('Error fetching threat data:', error);
    // Fallback to generated threats if API fails
    return generateFallbackThreats(20);
  }
};

// Fallback threat generation if API fails or returns insufficient data
const generateFallbackThreats = (count) => {
  const threats = [];
  const threatTypes = ['Ransomware', 'DDoS', 'Phishing', 'Data Breach', 'Malware'];

  for (let i = 0; i < count; i++) {
    const sourceCountry = countries[Math.floor(Math.random() * (countries.length - 1))];
    let targetCountry;

    // Ensure target is different from source
    do {
      targetCountry = countries[Math.floor(Math.random() * (countries.length - 1))];
    } while (targetCountry.name === sourceCountry.name);

    threats.push({
      id: `fallback-${Date.now()}-${i}`,
      type: threatTypes[Math.floor(Math.random() * threatTypes.length)],
      severity: Math.floor(Math.random() * 3) + 1, // 1-3
      source: {
        name: sourceCountry.name,
        lat: sourceCountry.lat,
        lng: sourceCountry.lng
      },
      target: {
        name: targetCountry.name,
        lat: targetCountry.lat,
        lng: targetCountry.lng
      },
      timestamp: new Date().getTime() - Math.floor(Math.random() * 3600000), // Within the last hour
      details: {
        name: `Fallback Threat ${i + 1}`,
        description: 'Generated threat when API data is insufficient',
        author: 'System',
        references: []
      }
    });
  }

  return threats;
};

// Optimized threat cache refresh with debouncing
const refreshThreatCache = async () => {
  try {
    const now = Date.now();

    // Prevent concurrent fetches and respect cache duration
    if (isFetching || (threatCache.length > 0 && now - lastFetchTime < CACHE_DURATION)) {
      return;
    }

    isFetching = true;
    logger.info('Refreshing threat cache');

    try {
      // Fetch new threats
      const newThreats = await fetchThreatData();

      // Filter out duplicates
      const uniqueNewThreats = newThreats.filter(threat => {
        // Use a combination of properties to identify unique threats
        const threatKey = `${threat.source.name}-${threat.target.name}-${threat.type}-${threat.timestamp}`;
        if (processedThreats.has(threatKey)) {
          return false;
        }
        processedThreats.add(threatKey);
        return true;
      });

      // Store in database for persistence (in background)
      if (uniqueNewThreats.length > 0) {
        supabase.from('threat_data').upsert(
          uniqueNewThreats.map(threat => ({
            threat_id: threat.id,
            threat_data: threat,
            created_at: new Date()
          }))
        ).then(() => {
          logger.info(`Stored ${uniqueNewThreats.length} new threats in database`);
        }).catch(dbError => {
          logger.error('Error storing threats in database:', dbError);
        });
      }

      // Update cache efficiently
      threatCache = [...threatCache, ...uniqueNewThreats];

      // Sort by timestamp (newest first) and limit to MAX_THREATS
      threatCache.sort((a, b) => b.timestamp - a.timestamp);
      threatCache = threatCache.slice(0, MAX_THREATS);

      // Limit the size of processedThreats set to prevent memory leaks
      if (processedThreats.size > MAX_THREATS * 3) {
        // Convert to array, slice, and convert back to set
        const tempArray = Array.from(processedThreats);
        processedThreats = new Set(tempArray.slice(tempArray.length - MAX_THREATS * 2));
      }

      lastFetchTime = now;
    } finally {
      isFetching = false;
    }
  } catch (error) {
    logger.error('Error refreshing threat cache:', error);
    isFetching = false;
  }
};

// Initialize threat cache
refreshThreatCache();

// Schedule regular cache refresh (every 5 minutes)
setInterval(refreshThreatCache, CACHE_DURATION);

// Optimized function to get threats for the frontend
const getThreatsForFrontend = (count = 10, options = {}) => {
  const { filterType, filterSeverity, newestFirst = true } = options;

  // Apply filters if specified
  let filteredThreats = [...threatCache];

  if (filterType && filterType !== 'all') {
    filteredThreats = filteredThreats.filter(threat => threat.type === filterType);
  }

  if (filterSeverity && filterSeverity > 0) {
    filteredThreats = filteredThreats.filter(threat => threat.severity === filterSeverity);
  }

  // Sort by timestamp if needed
  if (newestFirst) {
    filteredThreats.sort((a, b) => b.timestamp - a.timestamp);
  }

  // If we have enough threats, return a selection
  if (filteredThreats.length >= count) {
    // For better performance, just take the first 'count' threats after filtering/sorting
    return filteredThreats.slice(0, count);
  }

  // If we don't have enough threats, return what we have
  return filteredThreats;
};

// Optimized Routes

// Get all threats (limited to MAX_THREATS)
app.get('/api/threats', async (req, res) => {
  try {
    await refreshThreatCache();

    // Apply filters if provided
    const filterType = req.query.type;
    const filterSeverity = parseInt(req.query.severity) || 0;
    const limit = parseInt(req.query.limit) || MAX_THREATS;

    let filteredThreats = [...threatCache];

    if (filterType && filterType !== 'all') {
      filteredThreats = filteredThreats.filter(threat => threat.type === filterType);
    }

    if (filterSeverity > 0) {
      filteredThreats = filteredThreats.filter(threat => threat.severity === filterSeverity);
    }

    // Limit the number of threats returned
    filteredThreats = filteredThreats.slice(0, limit);

    res.json(filteredThreats);
  } catch (error) {
    logger.error('Error fetching threats:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get a batch of threats for the frontend
app.get('/api/threats/batch', async (req, res) => {
  try {
    const count = parseInt(req.query.count) || 10;
    const filterType = req.query.type;
    const filterSeverity = parseInt(req.query.severity) || 0;
    const newestFirst = req.query.newest !== 'false';

    await refreshThreatCache();

    const threats = getThreatsForFrontend(count, {
      filterType,
      filterSeverity,
      newestFirst
    });

    res.json(threats);
  } catch (error) {
    logger.error('Error fetching threat batch:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get a single threat for the frontend
app.get('/api/threats/next', async (req, res) => {
  try {
    await refreshThreatCache();

    if (threatCache.length === 0) {
      return res.json(null);
    }

    const filterType = req.query.type;
    const filterSeverity = parseInt(req.query.severity) || 0;

    let filteredThreats = [...threatCache];

    if (filterType && filterType !== 'all') {
      filteredThreats = filteredThreats.filter(threat => threat.type === filterType);
    }

    if (filterSeverity > 0) {
      filteredThreats = filteredThreats.filter(threat => threat.severity === filterSeverity);
    }

    if (filteredThreats.length === 0) {
      return res.json(null);
    }

    // Get a random threat from filtered list
    const randomIndex = Math.floor(Math.random() * filteredThreats.length);
    const threat = filteredThreats[randomIndex];

    res.json(threat);
  } catch (error) {
    logger.error('Error fetching next threat:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get threat statistics with caching
let cachedStats = null;
let statsLastUpdated = 0;
const STATS_CACHE_DURATION = 60 * 1000; // 1 minute

app.get('/api/threats/stats', async (req, res) => {
  try {
    const now = Date.now();

    // Use cached stats if available and recent
    if (cachedStats && now - statsLastUpdated < STATS_CACHE_DURATION) {
      return res.json(cachedStats);
    }

    await refreshThreatCache();

    // Calculate statistics
    const stats = {
      totalThreats: threatCache.length,
      threatsByType: {},
      threatsByCountry: {},
      threatsBySeverity: {
        1: 0, // Low
        2: 0, // Medium
        3: 0  // High
      },
      topSourceCountries: [],
      topTargetCountries: []
    };

    // Process threats to generate statistics
    threatCache.forEach(threat => {
      // Count by type
      if (!stats.threatsByType[threat.type]) {
        stats.threatsByType[threat.type] = 0;
      }
      stats.threatsByType[threat.type]++;

      // Count by source country
      if (!stats.threatsByCountry[threat.source.name]) {
        stats.threatsByCountry[threat.source.name] = 0;
      }
      stats.threatsByCountry[threat.source.name]++;

      // Count by severity
      stats.threatsBySeverity[threat.severity]++;
    });

    // Get top source countries
    stats.topSourceCountries = Object.entries(stats.threatsByCountry)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([country, count]) => ({ country, count }));

    // Get top threat types
    stats.topThreatTypes = Object.entries(stats.threatsByType)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([type, count]) => ({ type, count }));

    // Cache the stats
    cachedStats = stats;
    statsLastUpdated = now;

    res.json(stats);
  } catch (error) {
    logger.error('Error fetching threat statistics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Implement Server-Sent Events for streaming threats
app.get('/api/threats/stream', (req, res) => {
  // Set headers for SSE
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('Access-Control-Allow-Origin', '*');

  // Send a heartbeat every 30 seconds to keep the connection alive
  const heartbeatInterval = setInterval(() => {
    res.write('event: heartbeat\ndata: {}\n\n');
  }, 30000);

  // Function to send a threat
  const sendThreat = async () => {
    try {
      await refreshThreatCache();

      if (threatCache.length === 0) {
        res.write(`data: ${JSON.stringify(null)}\n\n`);
        return;
      }

      // Get a random threat
      const randomIndex = Math.floor(Math.random() * threatCache.length);
      const threat = threatCache[randomIndex];

      res.write(`data: ${JSON.stringify(threat)}\n\n`);
    } catch (error) {
      logger.error('Error sending threat via SSE:', error);
      res.write(`event: error\ndata: ${JSON.stringify({ message: 'Error fetching threat data' })}\n\n`);
    }
  };

  // Send initial threat
  sendThreat();

  // Send a new threat every 5 seconds
  const threatInterval = setInterval(sendThreat, PROCESSING_INTERVAL);

  // Clean up on close
  req.on('close', () => {
    clearInterval(heartbeatInterval);
    clearInterval(threatInterval);
  });
});

// Start the server
const PORT = process.env.THREATS_SERVICE_PORT || 3005;
app.listen(PORT, () => {
  console.log(`Threats service running on port ${PORT}`);
  logger.info(`Threats service running on port ${PORT}`);
});
