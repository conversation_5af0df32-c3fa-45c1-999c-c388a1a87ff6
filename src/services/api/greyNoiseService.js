/**
 * GreyNoise API Service
 *
 * This service provides methods to interact with the GreyNoise API
 * for internet noise and mass-scanner identification data.
 */

import axios from 'axios';
import proxyService from './proxyService';

// Base URL for GreyNoise API
const GREYNOISE_BASE_URL = 'https://api.greynoise.io/v3';

// Create API service with configurable API key
const createGreyNoiseService = (apiKey) => {
  // Create axios instance with default config
  const greyNoiseClient = axios.create({
    baseURL: GREYNOISE_BASE_URL,
    headers: {
      'key': apiKey,
      'Content-Type': 'application/json'
    }
  });

  return {
    /**
     * Get noise status for an IP address
     * @param {string} ip - IP address to check
     * @returns {Promise} - Promise with noise data
     */
    getNoiseStatus: async (ip) => {
      try {
        // First try with axios client
        try {
          const response = await greyNoiseClient.get(`/noise/quick/${ip}`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct GreyNoise noise status request for ${ip} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${GREYNOISE_BASE_URL}/noise/quick/${ip}`;
          const headers = { 'key': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching noise status for ${ip}:`, error);
        throw error;
      }
    },

    /**
     * Get RIOT status for an IP address (RIOT = Rule It Out)
     * @param {string} ip - IP address to check
     * @returns {Promise} - Promise with RIOT data
     */
    getRiotStatus: async (ip) => {
      try {
        // First try with axios client
        try {
          const response = await greyNoiseClient.get(`/riot/${ip}`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct GreyNoise RIOT status request for ${ip} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${GREYNOISE_BASE_URL}/riot/${ip}`;
          const headers = { 'key': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching RIOT status for ${ip}:`, error);
        throw error;
      }
    },

    /**
     * Get community context for an IP address
     * @param {string} ip - IP address to check
     * @returns {Promise} - Promise with community data
     */
    getCommunityContext: async (ip) => {
      try {
        // First try with axios client
        try {
          const response = await greyNoiseClient.get(`/community/${ip}`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct GreyNoise community context request for ${ip} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${GREYNOISE_BASE_URL}/community/${ip}`;
          const headers = { 'key': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching community context for ${ip}:`, error);
        throw error;
      }
    },

    /**
     * Get context for an IP address (full context)
     * @param {string} ip - IP address to check
     * @returns {Promise} - Promise with context data
     */
    getIPContext: async (ip) => {
      try {
        // First try with axios client
        try {
          const response = await greyNoiseClient.get(`/ip/${ip}`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct GreyNoise IP context request for ${ip} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${GREYNOISE_BASE_URL}/ip/${ip}`;
          const headers = { 'key': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching IP context for ${ip}:`, error);
        throw error;
      }
    },

    /**
     * Query the GNQL (GreyNoise Query Language)
     * @param {string} query - GNQL query string
     * @param {number} size - Number of results to return
     * @param {number} scroll - Scroll ID for pagination
     * @returns {Promise} - Promise with query results
     */
    queryGNQL: async (query, size = 10, scroll = null) => {
      try {
        // Build params
        const params = { query, size };
        if (scroll) params.scroll = scroll;

        // First try with axios client
        try {
          const response = await greyNoiseClient.get('/experimental/gnql', { params });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct GreyNoise GNQL query request failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${GREYNOISE_BASE_URL}/experimental/gnql`;
          const headers = { 'key': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error executing GNQL query:`, error);
        throw error;
      }
    },

    /**
     * Get stats based on a GNQL query
     * @param {string} query - GNQL query string
     * @param {string} stat - Stat type (classification, actor, spoofable, etc.)
     * @returns {Promise} - Promise with stats data
     */
    getStats: async (query, stat = 'classification') => {
      try {
        // First try with axios client
        try {
          const response = await greyNoiseClient.get('/experimental/gnql/stats', { 
            params: { 
              query,
              stat
            } 
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct GreyNoise stats request failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${GREYNOISE_BASE_URL}/experimental/gnql/stats`;
          const headers = { 'key': apiKey, 'Content-Type': 'application/json' };
          const params = { query, stat };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching stats:`, error);
        throw error;
      }
    },

    /**
     * Get timeline data based on a GNQL query
     * @param {string} query - GNQL query string
     * @returns {Promise} - Promise with timeline data
     */
    getTimeline: async (query) => {
      try {
        // First try with axios client
        try {
          const response = await greyNoiseClient.get('/experimental/gnql/timeline', { 
            params: { query } 
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct GreyNoise timeline request failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${GREYNOISE_BASE_URL}/experimental/gnql/timeline`;
          const headers = { 'key': apiKey, 'Content-Type': 'application/json' };
          const params = { query };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching timeline:`, error);
        throw error;
      }
    }
  };
};

export default createGreyNoiseService;
