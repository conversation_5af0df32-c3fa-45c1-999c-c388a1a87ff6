/**
 * API Manager
 *
 * Central service to manage all API integrations with proper key management.
 * This service loads API keys from environment variables and initializes
 * all API services.
 */

import createOTXService from './otxService';
import createAbuseIPDBService from './abuseIPDBService';
import createNVDService from './nvdService';
import createVirusTotalService from './virusTotalService';
import createShodanService from './shodanService';
import createGreyNoiseService from './greyNoiseService';

class APIManager {
  constructor() {
    this.services = {};
    this.initialized = false;
  }

  /**
   * Initialize all API services
   * @returns {Object} - Object containing all initialized services
   */
  initialize() {
    if (this.initialized) {
      return this.services;
    }

    // Get API keys from environment variables
    const otxApiKey = process.env.REACT_APP_OTX_API_KEY || import.meta.env.VITE_OTX_API_KEY;
    const abuseIPDBApiKey = process.env.REACT_APP_ABUSEIPDB_API_KEY || import.meta.env.VITE_ABUSEIPDB_API_KEY;
    const nvdApiKey = process.env.REACT_APP_NVD_API_KEY || import.meta.env.VITE_NVD_API_KEY || '5e3ecbad-e117-4fad-9a19-983e63edfb67';
    const virusTotalApiKey = process.env.REACT_APP_VIRUSTOTAL_API_KEY || import.meta.env.VITE_VIRUSTOTAL_API_KEY || '****************************************************************';
    const shodanApiKey = process.env.REACT_APP_SHODAN_API_KEY || import.meta.env.VITE_SHODAN_API_KEY || '********************************';
    const greyNoiseApiKey = process.env.REACT_APP_GREYNOISE_API_KEY || import.meta.env.VITE_GREYNOISE_API_KEY || 'dX0AK56JO7Vvh1BLbuUI3zxgK6gFIVZ21zHeHVz4wEOTXpZBvtqS8F2TzJDwNy2F';

    // Initialize services with API keys
    if (otxApiKey) {
      this.services.otx = createOTXService(otxApiKey);
    } else {
      console.warn('OTX API key not found. OTX service will not be available.');
    }

    if (abuseIPDBApiKey) {
      this.services.abuseIPDB = createAbuseIPDBService(abuseIPDBApiKey);
    } else {
      console.warn('AbuseIPDB API key not found. AbuseIPDB service will not be available.');
    }

    if (nvdApiKey) {
      this.services.nvd = createNVDService(nvdApiKey);
    } else {
      console.warn('NVD API key not found. NVD service will not be available.');
    }

    if (virusTotalApiKey) {
      this.services.virusTotal = createVirusTotalService(virusTotalApiKey);
    } else {
      console.warn('VirusTotal API key not found. VirusTotal service will not be available.');
    }

    if (shodanApiKey) {
      this.services.shodan = createShodanService(shodanApiKey);
    } else {
      console.warn('Shodan API key not found. Shodan service will not be available.');
    }

    if (greyNoiseApiKey) {
      this.services.greyNoise = createGreyNoiseService(greyNoiseApiKey);
    } else {
      console.warn('GreyNoise API key not found. GreyNoise service will not be available.');
    }

    this.initialized = true;
    return this.services;
  }

  /**
   * Get a specific API service
   * @param {string} serviceName - Name of the service to get
   * @returns {Object|null} - The requested service or null if not available
   */
  getService(serviceName) {
    if (!this.initialized) {
      this.initialize();
    }

    return this.services[serviceName] || null;
  }

  /**
   * Get all available API services
   * @returns {Object} - Object containing all available services
   */
  getAllServices() {
    if (!this.initialized) {
      this.initialize();
    }

    return this.services;
  }

  /**
   * Check if a specific service is available
   * @param {string} serviceName - Name of the service to check
   * @returns {boolean} - True if service is available, false otherwise
   */
  isServiceAvailable(serviceName) {
    if (!this.initialized) {
      this.initialize();
    }

    return !!this.services[serviceName];
  }
}

// Create and export a singleton instance
const apiManager = new APIManager();
export default apiManager;
