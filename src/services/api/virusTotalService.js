/**
 * VirusTotal API Service
 *
 * This service provides methods to interact with the VirusTotal API
 * for file, URL, domain, and IP reputation data.
 */

import axios from 'axios';
import proxyService from './proxyService';

// Base URL for VirusTotal API
const VT_BASE_URL = 'https://www.virustotal.com/api/v3';

// Create API service with configurable API key
const createVirusTotalService = (apiKey) => {
  // Create axios instance with default config
  const vtClient = axios.create({
    baseURL: VT_BASE_URL,
    headers: {
      'x-apikey': apiKey,
      'Content-Type': 'application/json'
    }
  });

  return {
    /**
     * Get file report by hash
     * @param {string} hash - File hash (MD5, SHA-1, or SHA-256)
     * @returns {Promise} - Promise with file analysis data
     */
    getFileReport: async (hash) => {
      try {
        // First try with axios client
        try {
          const response = await vtClient.get(`/files/${hash}`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct VirusTotal file report request for ${hash} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${VT_BASE_URL}/files/${hash}`;
          const headers = { 'x-apikey': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching file report for ${hash}:`, error);
        throw error;
      }
    },

    /**
     * Get URL analysis report
     * @param {string} url - URL to analyze
     * @returns {Promise} - Promise with URL analysis data
     */
    getUrlReport: async (url) => {
      try {
        // URL needs to be encoded and converted to base64
        const encodedUrl = Buffer.from(url).toString('base64').replace(/\\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
        
        // First try with axios client
        try {
          const response = await vtClient.get(`/urls/${encodedUrl}`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct VirusTotal URL report request failed, trying proxy...`);

          // If direct request fails, try with proxy
          const proxyUrl = `${VT_BASE_URL}/urls/${encodedUrl}`;
          const headers = { 'x-apikey': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(proxyUrl, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching URL report:`, error);
        throw error;
      }
    },

    /**
     * Get domain report
     * @param {string} domain - Domain to analyze
     * @returns {Promise} - Promise with domain analysis data
     */
    getDomainReport: async (domain) => {
      try {
        // First try with axios client
        try {
          const response = await vtClient.get(`/domains/${domain}`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct VirusTotal domain report request for ${domain} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${VT_BASE_URL}/domains/${domain}`;
          const headers = { 'x-apikey': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching domain report for ${domain}:`, error);
        throw error;
      }
    },

    /**
     * Get IP address report
     * @param {string} ip - IP address to analyze
     * @returns {Promise} - Promise with IP analysis data
     */
    getIpReport: async (ip) => {
      try {
        // First try with axios client
        try {
          const response = await vtClient.get(`/ip_addresses/${ip}`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct VirusTotal IP report request for ${ip} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${VT_BASE_URL}/ip_addresses/${ip}`;
          const headers = { 'x-apikey': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching IP report for ${ip}:`, error);
        throw error;
      }
    },

    /**
     * Submit URL for scanning
     * @param {string} url - URL to scan
     * @returns {Promise} - Promise with scan submission result
     */
    scanUrl: async (url) => {
      try {
        const response = await vtClient.post('/urls', `url=${encodeURIComponent(url)}`, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });
        return response.data;
      } catch (error) {
        console.error(`Error submitting URL for scanning:`, error);
        throw error;
      }
    },

    /**
     * Get comments for a resource (file, URL, domain, IP)
     * @param {string} resource - Resource ID or hash
     * @param {string} type - Resource type (file, url, domain, ip_address)
     * @returns {Promise} - Promise with comments data
     */
    getComments: async (resource, type) => {
      try {
        let endpoint;
        switch (type) {
          case 'file':
            endpoint = `/files/${resource}/comments`;
            break;
          case 'url':
            // URL needs to be encoded and converted to base64
            const encodedUrl = Buffer.from(resource).toString('base64').replace(/\\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
            endpoint = `/urls/${encodedUrl}/comments`;
            break;
          case 'domain':
            endpoint = `/domains/${resource}/comments`;
            break;
          case 'ip_address':
            endpoint = `/ip_addresses/${resource}/comments`;
            break;
          default:
            throw new Error('Invalid resource type');
        }

        const response = await vtClient.get(endpoint);
        return response.data;
      } catch (error) {
        console.error(`Error fetching comments:`, error);
        throw error;
      }
    }
  };
};

export default createVirusTotalService;
