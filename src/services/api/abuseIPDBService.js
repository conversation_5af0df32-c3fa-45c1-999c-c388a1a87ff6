/**
 * AbuseIPDB API Service
 *
 * This service provides methods to interact with the AbuseIPDB API
 * for IP reputation and abuse data.
 */

import axios from 'axios';
import proxyService from './proxyService';

// Base URL for AbuseIPDB API
const ABUSEIPDB_BASE_URL = 'https://api.abuseipdb.com/api/v2';

// Create API service with configurable API key
const createAbuseIPDBService = (apiKey) => {
  // Create axios instance with default config
  const abuseClient = axios.create({
    baseURL: ABUSEIPDB_BASE_URL,
    headers: {
      'Key': apiKey,
      'Accept': 'application/json'
    }
  });

  return {
    /**
     * Check an IP address for abuse reports
     * @param {string} ip - The IP address to check
     * @param {number} maxAgeInDays - Maximum age of reports in days
     * @returns {Promise} - Promise with IP check data
     */
    checkIP: async (ip, maxAgeInDays = 90) => {
      try {
        // First try with axios client
        try {
          const response = await abuseClient.get('/check', {
            params: {
              ipAddress: ip,
              maxAgeInDays,
              verbose: true
            }
          });
          return response.data.data;
        } catch (axiosError) {
          console.log('Direct AbuseIPDB request failed, trying proxy...');

          // If direct request fails, try with proxy
          const url = `${ABUSEIPDB_BASE_URL}/check`;
          const headers = { 'Key': apiKey, 'Accept': 'application/json' };
          const params = { ipAddress: ip, maxAgeInDays, verbose: true };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse.data;
        }
      } catch (error) {
        console.error(`Error checking IP ${ip}:`, error);
        throw error;
      }
    },

    /**
     * Get blacklisted IPs
     * @param {number} confidenceMinimum - Minimum confidence score (0-100)
     * @param {number} limit - Maximum number of results
     * @returns {Promise} - Promise with blacklist data
     */
    getBlacklist: async (confidenceMinimum = 90, limit = 25) => {
      try {
        // First try with axios client
        try {
          const response = await abuseClient.get('/blacklist', {
            params: {
              confidenceMinimum,
              limit
            }
          });
          return response.data.data;
        } catch (axiosError) {
          console.log('Direct AbuseIPDB blacklist request failed, trying proxy...');

          // If direct request fails, try with proxy
          const url = `${ABUSEIPDB_BASE_URL}/blacklist`;
          const headers = { 'Key': apiKey, 'Accept': 'application/json' };
          const params = { confidenceMinimum, limit };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse.data;
        }
      } catch (error) {
        console.error('Error fetching blacklist:', error);
        throw error;
      }
    },

    /**
     * Report an abusive IP address
     * @param {string} ip - The IP address to report
     * @param {Array} categories - Array of category IDs
     * @param {string} comment - Comment about the abuse
     * @returns {Promise} - Promise with report submission result
     */
    reportIP: async (ip, categories, comment) => {
      try {
        const response = await abuseClient.post('/report', {
          ip,
          categories,
          comment
        });
        return response.data;
      } catch (error) {
        console.error(`Error reporting IP ${ip}:`, error);
        throw error;
      }
    },

    /**
     * Check multiple IPs in bulk
     * @param {Array} ips - Array of IP addresses to check
     * @param {number} maxAgeInDays - Maximum age of reports in days
     * @returns {Promise} - Promise with bulk check results
     */
    checkBulk: async (ips, maxAgeInDays = 90) => {
      try {
        const response = await abuseClient.post('/check-block', {
          ipAddress: ips.join(','),
          maxAgeInDays
        });
        return response.data;
      } catch (error) {
        console.error('Error performing bulk IP check:', error);
        throw error;
      }
    }
  };
};

export default createAbuseIPDBService;
