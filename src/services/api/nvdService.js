/**
 * NVD (National Vulnerability Database) API Service
 *
 * This service provides methods to interact with the NVD API
 * for vulnerability data and CVE information.
 */

import axios from 'axios';
import proxyService from './proxyService';

// Base URL for NVD API
const NVD_BASE_URL = 'https://services.nvd.nist.gov/rest/json/cves/2.0';

// Create API service with configurable API key
const createNVDService = (apiKey) => {
  // Create axios instance with default config
  const nvdClient = axios.create({
    baseURL: NVD_BASE_URL,
    headers: {
      'apiKey': apiKey,
      'Content-Type': 'application/json'
    }
  });

  return {
    /**
     * Search for vulnerabilities with various filters
     * @param {Object} params - Search parameters
     * @returns {Promise} - Promise with vulnerability data
     */
    searchVulnerabilities: async (params = {}) => {
      try {
        // First try with axios client
        try {
          const response = await nvdClient.get('', { params });
          return response.data;
        } catch (axiosError) {
          console.log('Direct NVD request failed, trying proxy...');

          // If direct request fails, try with proxy
          const url = NVD_BASE_URL;
          const headers = { 'apiKey': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error('Error fetching vulnerability data:', error);
        throw error;
      }
    },

    /**
     * Get details for a specific CVE
     * @param {string} cveId - The CVE ID (e.g., CVE-2021-44228)
     * @returns {Promise} - Promise with CVE details
     */
    getCVEDetails: async (cveId) => {
      try {
        // First try with axios client
        try {
          const response = await nvdClient.get('', { 
            params: { 
              cveId 
            } 
          });
          return response.data.vulnerabilities?.[0] || null;
        } catch (axiosError) {
          console.log(`Direct NVD request for ${cveId} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = NVD_BASE_URL;
          const headers = { 'apiKey': apiKey, 'Content-Type': 'application/json' };
          const params = { cveId };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse.vulnerabilities?.[0] || null;
        }
      } catch (error) {
        console.error(`Error fetching details for ${cveId}:`, error);
        throw error;
      }
    },

    /**
     * Search for vulnerabilities by keyword
     * @param {string} keyword - Keyword to search for
     * @param {number} resultsPerPage - Number of results per page
     * @param {number} startIndex - Starting index for pagination
     * @returns {Promise} - Promise with search results
     */
    searchByKeyword: async (keyword, resultsPerPage = 10, startIndex = 0) => {
      try {
        // First try with axios client
        try {
          const response = await nvdClient.get('', { 
            params: { 
              keywordSearch: keyword,
              resultsPerPage,
              startIndex
            } 
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct NVD keyword search for "${keyword}" failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = NVD_BASE_URL;
          const headers = { 'apiKey': apiKey, 'Content-Type': 'application/json' };
          const params = { 
            keywordSearch: keyword,
            resultsPerPage,
            startIndex
          };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error searching for "${keyword}":`, error);
        throw error;
      }
    },

    /**
     * Get recently added vulnerabilities
     * @param {number} days - Number of days to look back
     * @param {number} resultsPerPage - Number of results per page
     * @returns {Promise} - Promise with recent vulnerabilities
     */
    getRecentVulnerabilities: async (days = 30, resultsPerPage = 20) => {
      try {
        const pubStartDate = new Date();
        pubStartDate.setDate(pubStartDate.getDate() - days);
        
        // Format date as YYYY-MM-DDT00:00:00.000
        const formattedDate = pubStartDate.toISOString().split('T')[0] + 'T00:00:00.000';
        
        // First try with axios client
        try {
          const response = await nvdClient.get('', { 
            params: { 
              pubStartDate: formattedDate,
              resultsPerPage
            } 
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct NVD recent vulnerabilities request failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = NVD_BASE_URL;
          const headers = { 'apiKey': apiKey, 'Content-Type': 'application/json' };
          const params = { 
            pubStartDate: formattedDate,
            resultsPerPage
          };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error('Error fetching recent vulnerabilities:', error);
        throw error;
      }
    },

    /**
     * Get vulnerabilities by CVSS score range
     * @param {number} minScore - Minimum CVSS score
     * @param {number} maxScore - Maximum CVSS score
     * @param {number} resultsPerPage - Number of results per page
     * @returns {Promise} - Promise with vulnerabilities in the score range
     */
    getVulnerabilitiesByScore: async (minScore = 7.0, maxScore = 10.0, resultsPerPage = 20) => {
      try {
        // First try with axios client
        try {
          const response = await nvdClient.get('', { 
            params: { 
              cvssV3Severity: getSeverityFromScore(minScore),
              resultsPerPage
            } 
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct NVD score-based request failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = NVD_BASE_URL;
          const headers = { 'apiKey': apiKey, 'Content-Type': 'application/json' };
          const params = { 
            cvssV3Severity: getSeverityFromScore(minScore),
            resultsPerPage
          };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching vulnerabilities by score range:`, error);
        throw error;
      }
    }
  };
};

// Helper function to convert CVSS score to severity string
const getSeverityFromScore = (score) => {
  if (score >= 9.0) return 'CRITICAL';
  if (score >= 7.0) return 'HIGH';
  if (score >= 4.0) return 'MEDIUM';
  return 'LOW';
};

export default createNVDService;
