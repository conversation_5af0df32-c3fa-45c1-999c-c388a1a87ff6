/**
 * AlienVault OTX API Service
 *
 * This service provides methods to interact with the AlienVault OTX API
 * for threat intelligence data.
 */

import axios from 'axios';
import proxyService from './proxyService';

// Base URL for AlienVault OTX API
const OTX_BASE_URL = 'https://otx.alienvault.com/api/v1';

// Create API service with configurable API key
const createOTXService = (apiKey) => {
  // Create axios instance with default config
  const otxClient = axios.create({
    baseURL: OTX_BASE_URL,
    headers: {
      'X-OTX-API-KEY': apiKey,
      'Content-Type': 'application/json'
    }
  });

  return {
    /**
     * Get general pulse information
     * @returns {Promise} - Promise with pulse data
     */
    getPulses: async (limit = 10) => {
      try {
        // First try with axios client
        try {
          const response = await otxClient.get(`/pulses/subscribed?limit=${limit}`);
          return response.data.results;
        } catch (axiosError) {
          console.log('Direct OTX pulses request failed, trying proxy...');

          // If direct request fails, try with proxy
          const url = `${OTX_BASE_URL}/pulses/subscribed`;
          const headers = { 'X-OTX-API-KEY': apiKey, 'Content-Type': 'application/json' };
          const params = { limit };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse.results;
        }
      } catch (error) {
        console.error('Error fetching OTX pulses:', error);
        throw error;
      }
    },

    /**
     * Get indicators for a specific pulse
     * @param {string} pulseId - The ID of the pulse
     * @returns {Promise} - Promise with indicator data
     */
    getPulseIndicators: async (pulseId) => {
      try {
        // First try with axios client
        try {
          const response = await otxClient.get(`/pulses/${pulseId}/indicators`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct OTX pulse indicators request for ${pulseId} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${OTX_BASE_URL}/pulses/${pulseId}/indicators`;
          const headers = { 'X-OTX-API-KEY': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching indicators for pulse ${pulseId}:`, error);
        throw error;
      }
    },

    /**
     * Get information about an IP address
     * @param {string} ip - The IP address to look up
     * @returns {Promise} - Promise with IP data
     */
    getIPReputation: async (ip) => {
      try {
        // First try with axios client
        try {
          const response = await otxClient.get(`/indicators/IPv4/${ip}/general`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct OTX IP reputation request for ${ip} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${OTX_BASE_URL}/indicators/IPv4/${ip}/general`;
          const headers = { 'X-OTX-API-KEY': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching IP reputation for ${ip}:`, error);
        throw error;
      }
    },

    /**
     * Get information about a domain
     * @param {string} domain - The domain to look up
     * @returns {Promise} - Promise with domain data
     */
    getDomainReputation: async (domain) => {
      try {
        // First try with axios client
        try {
          const response = await otxClient.get(`/indicators/domain/${domain}/general`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct OTX domain reputation request for ${domain} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${OTX_BASE_URL}/indicators/domain/${domain}/general`;
          const headers = { 'X-OTX-API-KEY': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching domain reputation for ${domain}:`, error);
        throw error;
      }
    },

    /**
     * Get information about a URL
     * @param {string} url - The URL to look up
     * @returns {Promise} - Promise with URL data
     */
    getURLReputation: async (url) => {
      try {
        // URL needs to be encoded
        const encodedUrl = encodeURIComponent(url);

        // First try with axios client
        try {
          const response = await otxClient.get(`/indicators/url/${encodedUrl}/general`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct OTX URL reputation request for ${url} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const proxyUrl = `${OTX_BASE_URL}/indicators/url/${encodedUrl}/general`;
          const headers = { 'X-OTX-API-KEY': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(proxyUrl, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching URL reputation for ${url}:`, error);
        throw error;
      }
    },

    /**
     * Get information about a file hash
     * @param {string} hash - The file hash to look up
     * @param {string} type - The hash type (md5, sha1, sha256)
     * @returns {Promise} - Promise with hash data
     */
    getFileReputation: async (hash, type = 'sha256') => {
      try {
        // First try with axios client
        try {
          const response = await otxClient.get(`/indicators/file/${hash}/${type}`);
          return response.data;
        } catch (axiosError) {
          console.log(`Direct OTX file reputation request for ${hash} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${OTX_BASE_URL}/indicators/file/${hash}/${type}`;
          const headers = { 'X-OTX-API-KEY': apiKey, 'Content-Type': 'application/json' };

          const proxyResponse = await proxyService.get(url, headers);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching file reputation for ${hash}:`, error);
        throw error;
      }
    },

    /**
     * Search for indicators across OTX
     * @param {string} query - The search query
     * @returns {Promise} - Promise with search results
     */
    searchIndicators: async (query) => {
      try {
        const encodedQuery = encodeURIComponent(query);

        // First try with axios client
        try {
          const response = await otxClient.get(`/search/indicators?q=${encodedQuery}`);
          return response.data.results;
        } catch (axiosError) {
          console.log(`Direct OTX search request for ${query} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${OTX_BASE_URL}/search/indicators`;
          const headers = { 'X-OTX-API-KEY': apiKey, 'Content-Type': 'application/json' };
          const params = { q: query };

          const proxyResponse = await proxyService.get(url, headers, params);
          return proxyResponse.results;
        }
      } catch (error) {
        console.error(`Error searching indicators for ${query}:`, error);
        throw error;
      }
    }
  };
};

export default createOTXService;
