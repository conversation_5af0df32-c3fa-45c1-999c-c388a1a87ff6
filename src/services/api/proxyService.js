/**
 * API Proxy Service
 * 
 * This service provides a proxy for making API calls to external services
 * that may have CORS restrictions. It uses a combination of techniques to
 * ensure reliable data retrieval.
 */

import axios from 'axios';

class ProxyService {
  constructor() {
    this.proxyUrls = [
      'https://corsproxy.io/?',
      'https://cors-anywhere.herokuapp.com/',
      'https://api.allorigins.win/raw?url='
    ];
    this.currentProxyIndex = 0;
  }

  /**
   * Get the next proxy URL in rotation
   * @returns {string} - The proxy URL
   */
  getNextProxy() {
    const proxy = this.proxyUrls[this.currentProxyIndex];
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxyUrls.length;
    return proxy;
  }

  /**
   * Make a proxied GET request
   * @param {string} url - The URL to request
   * @param {Object} headers - Headers to include in the request
   * @param {Object} params - Query parameters
   * @returns {Promise} - Promise with response data
   */
  async get(url, headers = {}, params = {}) {
    // Try direct request first
    try {
      const response = await axios.get(url, { headers, params });
      return response.data;
    } catch (directError) {
      console.log('Direct request failed, trying proxy...', directError.message);
      
      // If direct request fails, try with proxy
      let lastError = directError;
      
      // Try each proxy in sequence
      for (let i = 0; i < this.proxyUrls.length; i++) {
        try {
          const proxy = this.getNextProxy();
          const proxyUrl = proxy + encodeURIComponent(url);
          
          const response = await axios.get(proxyUrl, { 
            headers,
            params
          });
          
          return response.data;
        } catch (proxyError) {
          lastError = proxyError;
          console.log(`Proxy ${i+1} failed, trying next...`, proxyError.message);
        }
      }
      
      // If all proxies fail, throw the last error
      throw lastError;
    }
  }

  /**
   * Make a proxied POST request
   * @param {string} url - The URL to request
   * @param {Object} data - Data to send in the request body
   * @param {Object} headers - Headers to include in the request
   * @returns {Promise} - Promise with response data
   */
  async post(url, data = {}, headers = {}) {
    // Try direct request first
    try {
      const response = await axios.post(url, data, { headers });
      return response.data;
    } catch (directError) {
      console.log('Direct request failed, trying proxy...', directError.message);
      
      // If direct request fails, try with proxy
      let lastError = directError;
      
      // Try each proxy in sequence
      for (let i = 0; i < this.proxyUrls.length; i++) {
        try {
          const proxy = this.getNextProxy();
          const proxyUrl = proxy + encodeURIComponent(url);
          
          const response = await axios.post(proxyUrl, data, { headers });
          return response.data;
        } catch (proxyError) {
          lastError = proxyError;
          console.log(`Proxy ${i+1} failed, trying next...`, proxyError.message);
        }
      }
      
      // If all proxies fail, throw the last error
      throw lastError;
    }
  }
}

// Create and export a singleton instance
const proxyService = new ProxyService();
export default proxyService;
