/**
 * Shodan API Service
 *
 * This service provides methods to interact with the Shodan API
 * for internet-wide scanning data and exposed device information.
 */

import axios from 'axios';
import proxyService from './proxyService';

// Base URL for Shodan API
const SHODAN_BASE_URL = 'https://api.shodan.io';

// Create API service with configurable API key
const createShodanService = (apiKey) => {
  return {
    /**
     * Search for hosts with specific criteria
     * @param {string} query - Shodan search query
     * @param {number} page - Page number for results
     * @returns {Promise} - Promise with search results
     */
    search: async (query, page = 1) => {
      try {
        // First try with axios client
        try {
          const response = await axios.get(`${SHODAN_BASE_URL}/shodan/host/search`, {
            params: {
              key: apiKey,
              query,
              page
            }
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct Shodan search request failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${SHODAN_BASE_URL}/shodan/host/search`;
          const params = { key: apiKey, query, page };

          const proxyResponse = await proxyService.get(url, {}, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error searching Shodan:`, error);
        throw error;
      }
    },

    /**
     * Get information about a specific IP address
     * @param {string} ip - IP address to look up
     * @returns {Promise} - Promise with host information
     */
    getHostInfo: async (ip) => {
      try {
        // First try with axios client
        try {
          const response = await axios.get(`${SHODAN_BASE_URL}/shodan/host/${ip}`, {
            params: {
              key: apiKey
            }
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct Shodan host info request for ${ip} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${SHODAN_BASE_URL}/shodan/host/${ip}`;
          const params = { key: apiKey };

          const proxyResponse = await proxyService.get(url, {}, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching host info for ${ip}:`, error);
        throw error;
      }
    },

    /**
     * Get summary information about the current API key
     * @returns {Promise} - Promise with API key information
     */
    getApiInfo: async () => {
      try {
        const response = await axios.get(`${SHODAN_BASE_URL}/api-info`, {
          params: {
            key: apiKey
          }
        });
        return response.data;
      } catch (error) {
        console.error('Error fetching API info:', error);
        throw error;
      }
    },

    /**
     * Get list of services Shodan crawls
     * @returns {Promise} - Promise with services data
     */
    getServices: async () => {
      try {
        const response = await axios.get(`${SHODAN_BASE_URL}/shodan/services`, {
          params: {
            key: apiKey
          }
        });
        return response.data;
      } catch (error) {
        console.error('Error fetching Shodan services:', error);
        throw error;
      }
    },

    /**
     * Get summary information about a search query
     * @param {string} query - Shodan search query
     * @returns {Promise} - Promise with query summary
     */
    getQuerySummary: async (query) => {
      try {
        // First try with axios client
        try {
          const response = await axios.get(`${SHODAN_BASE_URL}/shodan/host/count`, {
            params: {
              key: apiKey,
              query
            }
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct Shodan query summary request failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${SHODAN_BASE_URL}/shodan/host/count`;
          const params = { key: apiKey, query };

          const proxyResponse = await proxyService.get(url, {}, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching query summary:`, error);
        throw error;
      }
    },

    /**
     * Search for SSL/TLS certificates
     * @param {string} query - Certificate search query
     * @param {number} page - Page number for results
     * @returns {Promise} - Promise with certificate data
     */
    searchCertificates: async (query, page = 1) => {
      try {
        // First try with axios client
        try {
          const response = await axios.get(`${SHODAN_BASE_URL}/shodan/certs/search`, {
            params: {
              key: apiKey,
              query,
              page
            }
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct Shodan certificate search request failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${SHODAN_BASE_URL}/shodan/certs/search`;
          const params = { key: apiKey, query, page };

          const proxyResponse = await proxyService.get(url, {}, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error searching certificates:`, error);
        throw error;
      }
    },

    /**
     * Get list of ports Shodan crawls
     * @returns {Promise} - Promise with ports data
     */
    getPorts: async () => {
      try {
        const response = await axios.get(`${SHODAN_BASE_URL}/shodan/ports`, {
          params: {
            key: apiKey
          }
        });
        return response.data;
      } catch (error) {
        console.error('Error fetching Shodan ports:', error);
        throw error;
      }
    },

    /**
     * Get data from the Shodan Internet Database (SIDB)
     * @param {string} domain - Domain to look up
     * @returns {Promise} - Promise with SIDB data
     */
    getDomainInfo: async (domain) => {
      try {
        // First try with axios client
        try {
          const response = await axios.get(`${SHODAN_BASE_URL}/dns/domain/${domain}`, {
            params: {
              key: apiKey
            }
          });
          return response.data;
        } catch (axiosError) {
          console.log(`Direct Shodan domain info request for ${domain} failed, trying proxy...`);

          // If direct request fails, try with proxy
          const url = `${SHODAN_BASE_URL}/dns/domain/${domain}`;
          const params = { key: apiKey };

          const proxyResponse = await proxyService.get(url, {}, params);
          return proxyResponse;
        }
      } catch (error) {
        console.error(`Error fetching domain info for ${domain}:`, error);
        throw error;
      }
    }
  };
};

export default createShodanService;
