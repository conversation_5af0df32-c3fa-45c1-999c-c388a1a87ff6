# API Services

This directory contains services for interacting with external APIs used in the XCerberus platform.

## Available API Services

### 1. AlienVault OTX API

The Open Threat Exchange (OTX) API provides access to threat intelligence data including:
- Pulses (collections of indicators)
- IP reputation
- Domain reputation
- URL reputation
- File hash reputation

### 2. AbuseIPDB API

The AbuseIPDB API provides information about potentially malicious IP addresses:
- IP reputation checks
- Blacklist of known malicious IPs
- Ability to report abusive IPs

## Setup Instructions

1. Create a `.env` file in the root of your project (copy from `.env.example`)
2. Add your API keys to the `.env` file:
   ```
   VITE_OTX_API_KEY=your_otx_api_key_here
   VITE_ABUSEIPDB_API_KEY=your_abuseipdb_api_key_here
   ```

## Usage Examples

### Using the API Manager

The API Manager provides a centralized way to access all API services:

```javascript
import apiManager from '../../services/api/apiManager';

// Initialize all API services
apiManager.initialize();

// Check if a service is available
if (apiManager.isServiceAvailable('otx')) {
  // Get the OTX service
  const otxService = apiManager.getService('otx');
  
  // Use the service
  const pulses = await otxService.getPulses();
}

// Get the AbuseIPDB service
const abuseIPDBService = apiManager.getService('abuseIPDB');

// Check an IP
const ipDetails = await abuseIPDBService.checkIP('*******');
```

### Direct Service Usage

You can also create and use services directly:

```javascript
import createOTXService from '../../services/api/otxService';
import createAbuseIPDBService from '../../services/api/abuseIPDBService';

// Create OTX service with API key
const otxService = createOTXService('your_api_key_here');

// Get pulses
const pulses = await otxService.getPulses();

// Create AbuseIPDB service with API key
const abuseIPDBService = createAbuseIPDBService('your_api_key_here');

// Get blacklist
const blacklist = await abuseIPDBService.getBlacklist();
```

## Security Best Practices

1. **Never hardcode API keys** in your source code
2. **Always use environment variables** for sensitive credentials
3. **Implement rate limiting** to avoid exceeding API quotas
4. **Add error handling** for API failures
5. **Cache responses** when appropriate to reduce API calls

## Additional Open-Source APIs to Consider

1. **VirusTotal API** - For file and URL scanning
2. **Shodan API** - For internet-wide device scanning
3. **MISP API** - For threat intelligence sharing
4. **PhishTank API** - For phishing URL detection
5. **URLhaus API** - For malware URL tracking
6. **Censys API** - For internet asset discovery
7. **GreyNoise API** - For internet background noise filtering
8. **Hybrid Analysis API** - For malware analysis
9. **MalwareBazaar API** - For malware sample sharing
10. **ThreatFox API** - For IOC sharing
