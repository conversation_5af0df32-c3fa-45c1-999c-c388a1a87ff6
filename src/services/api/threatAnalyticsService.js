/**
 * Threat Analytics Service
 * 
 * This service provides advanced analytics capabilities for threat intelligence data
 * by aggregating, correlating, and analyzing data from multiple sources.
 */

import apiManager from './apiManager';

class ThreatAnalyticsService {
  constructor() {
    this.initialized = false;
    this.services = {};
  }

  /**
   * Initialize the analytics service
   */
  async initialize() {
    if (this.initialized) return;

    // Initialize API manager and get available services
    this.services = apiManager.initialize();
    this.initialized = true;
  }

  /**
   * Correlate IP data from multiple sources
   * @param {string} ip - IP address to analyze
   * @returns {Object} - Correlated data from all available sources
   */
  async correlateIPData(ip) {
    if (!this.initialized) await this.initialize();
    
    const result = {
      ip,
      sources: [],
      riskScore: 0,
      riskFactors: [],
      geoData: null,
      relatedIndicators: [],
      analysisTimestamp: new Date().toISOString()
    };

    try {
      // Get data from AbuseIPDB if available
      if (this.services.abuseIPDB) {
        const abuseData = await this.services.abuseIPDB.checkIP(ip);
        if (abuseData) {
          result.sources.push('AbuseIPDB');
          
          // Add geo data
          result.geoData = {
            countryCode: abuseData.countryCode,
            countryName: abuseData.countryName,
            isp: abuseData.isp,
            domain: abuseData.domain,
            usageType: abuseData.usageType
          };
          
          // Calculate risk based on abuse confidence score
          const abuseScore = abuseData.abuseConfidenceScore || 0;
          result.riskScore += abuseScore * 0.7; // Weight AbuseIPDB score at 70%
          
          if (abuseScore > 80) {
            result.riskFactors.push('High abuse confidence score');
          } else if (abuseScore > 50) {
            result.riskFactors.push('Medium abuse confidence score');
          }
          
          if (abuseData.totalReports > 10) {
            result.riskFactors.push('Multiple abuse reports');
            result.riskScore += 10;
          }
        }
      }
      
      // Get data from OTX if available
      if (this.services.otx) {
        const otxData = await this.services.otx.getIPReputation(ip);
        if (otxData) {
          result.sources.push('AlienVault OTX');
          
          // Add pulse data
          if (otxData.pulse_info && otxData.pulse_info.count > 0) {
            const pulseCount = otxData.pulse_info.count;
            result.riskScore += Math.min(pulseCount * 5, 30); // Max 30 points from pulse count
            
            if (pulseCount > 5) {
              result.riskFactors.push('Associated with multiple threat intelligence reports');
            }
            
            // Extract related indicators
            if (otxData.pulse_info.pulses) {
              otxData.pulse_info.pulses.forEach(pulse => {
                if (pulse.indicators) {
                  pulse.indicators.forEach(indicator => {
                    if (indicator.type !== 'IPv4' && indicator.type !== 'IPv6') {
                      result.relatedIndicators.push({
                        type: indicator.type,
                        indicator: indicator.indicator,
                        title: pulse.name,
                        created: pulse.created
                      });
                    }
                  });
                }
              });
            }
          }
          
          // Add reputation data
          if (otxData.reputation && otxData.reputation.reputation_score) {
            const repScore = otxData.reputation.reputation_score;
            result.riskScore += (100 - repScore) * 0.3; // Weight OTX reputation at 30%
            
            if (repScore < 20) {
              result.riskFactors.push('Poor reputation score');
            }
          }
        }
      }
      
      // Cap risk score at 100
      result.riskScore = Math.min(Math.round(result.riskScore), 100);
      
      // Add risk level based on score
      if (result.riskScore >= 75) {
        result.riskLevel = 'Critical';
      } else if (result.riskScore >= 50) {
        result.riskLevel = 'High';
      } else if (result.riskScore >= 25) {
        result.riskLevel = 'Medium';
      } else {
        result.riskLevel = 'Low';
      }
      
      return result;
    } catch (error) {
      console.error(`Error correlating data for IP ${ip}:`, error);
      throw error;
    }
  }

  /**
   * Analyze a batch of IPs and identify patterns
   * @param {Array} ips - Array of IP addresses to analyze
   * @returns {Object} - Analysis results including patterns and high-risk IPs
   */
  async analyzeThreatCluster(ips) {
    if (!this.initialized) await this.initialize();
    
    const results = {
      analyzedCount: ips.length,
      highRiskCount: 0,
      countryClusters: {},
      ispClusters: {},
      highRiskIPs: [],
      analysisTimestamp: new Date().toISOString()
    };
    
    try {
      // Analyze each IP
      const analysisPromises = ips.map(ip => this.correlateIPData(ip));
      const ipAnalyses = await Promise.all(analysisPromises);
      
      // Process results to find patterns
      ipAnalyses.forEach(analysis => {
        // Track high risk IPs
        if (analysis.riskScore >= 50) {
          results.highRiskCount++;
          results.highRiskIPs.push({
            ip: analysis.ip,
            riskScore: analysis.riskScore,
            riskLevel: analysis.riskLevel,
            countryName: analysis.geoData?.countryName || 'Unknown'
          });
        }
        
        // Group by country
        if (analysis.geoData && analysis.geoData.countryName) {
          const country = analysis.geoData.countryName;
          if (!results.countryClusters[country]) {
            results.countryClusters[country] = {
              count: 0,
              avgRiskScore: 0,
              ips: []
            };
          }
          
          results.countryClusters[country].count++;
          results.countryClusters[country].avgRiskScore = 
            (results.countryClusters[country].avgRiskScore * 
             (results.countryClusters[country].count - 1) + 
             analysis.riskScore) / results.countryClusters[country].count;
          
          results.countryClusters[country].ips.push(analysis.ip);
        }
        
        // Group by ISP
        if (analysis.geoData && analysis.geoData.isp) {
          const isp = analysis.geoData.isp;
          if (!results.ispClusters[isp]) {
            results.ispClusters[isp] = {
              count: 0,
              avgRiskScore: 0,
              ips: []
            };
          }
          
          results.ispClusters[isp].count++;
          results.ispClusters[isp].avgRiskScore = 
            (results.ispClusters[isp].avgRiskScore * 
             (results.ispClusters[isp].count - 1) + 
             analysis.riskScore) / results.ispClusters[isp].count;
          
          results.ispClusters[isp].ips.push(analysis.ip);
        }
      });
      
      // Sort clusters by count
      results.topCountries = Object.entries(results.countryClusters)
        .sort((a, b) => b[1].count - a[1].count)
        .slice(0, 5)
        .map(([country, data]) => ({
          country,
          count: data.count,
          avgRiskScore: Math.round(data.avgRiskScore)
        }));
      
      results.topISPs = Object.entries(results.ispClusters)
        .sort((a, b) => b[1].count - a[1].count)
        .slice(0, 5)
        .map(([isp, data]) => ({
          isp,
          count: data.count,
          avgRiskScore: Math.round(data.avgRiskScore)
        }));
      
      return results;
    } catch (error) {
      console.error('Error analyzing threat cluster:', error);
      throw error;
    }
  }

  /**
   * Generate a threat intelligence report based on recent data
   * @param {Object} options - Report options
   * @returns {Object} - Threat intelligence report
   */
  async generateThreatReport(options = {}) {
    if (!this.initialized) await this.initialize();
    
    const report = {
      title: options.title || 'Threat Intelligence Report',
      generatedAt: new Date().toISOString(),
      summary: {
        totalThreats: 0,
        criticalThreats: 0,
        highThreats: 0,
        mediumThreats: 0,
        lowThreats: 0
      },
      topThreats: [],
      geographicDistribution: {},
      attackVectors: {},
      recommendations: []
    };
    
    try {
      // Get blacklisted IPs from AbuseIPDB if available
      if (this.services.abuseIPDB) {
        const blacklist = await this.services.abuseIPDB.getBlacklist(70, 50);
        if (blacklist && blacklist.length > 0) {
          // Analyze the blacklisted IPs
          const clusterAnalysis = await this.analyzeThreatCluster(
            blacklist.map(item => item.ipAddress)
          );
          
          // Update report with analysis results
          report.summary.totalThreats = blacklist.length;
          report.summary.criticalThreats = clusterAnalysis.highRiskIPs.filter(ip => ip.riskLevel === 'Critical').length;
          report.summary.highThreats = clusterAnalysis.highRiskIPs.filter(ip => ip.riskLevel === 'High').length;
          
          // Add top threats
          report.topThreats = clusterAnalysis.highRiskIPs
            .sort((a, b) => b.riskScore - a.riskScore)
            .slice(0, 10);
          
          // Add geographic distribution
          report.geographicDistribution = clusterAnalysis.topCountries.reduce((acc, item) => {
            acc[item.country] = item.count;
            return acc;
          }, {});
        }
      }
      
      // Get recent pulses from OTX if available
      if (this.services.otx) {
        const pulses = await this.services.otx.getPulses(20);
        if (pulses && pulses.length > 0) {
          // Extract attack vectors from pulse tags
          const attackVectors = {};
          
          pulses.forEach(pulse => {
            if (pulse.tags) {
              pulse.tags.forEach(tag => {
                // Normalize tags to common attack vectors
                let vector = tag.toLowerCase();
                
                if (vector.includes('ransomware')) vector = 'Ransomware';
                else if (vector.includes('phish')) vector = 'Phishing';
                else if (vector.includes('malware')) vector = 'Malware';
                else if (vector.includes('ddos')) vector = 'DDoS';
                else if (vector.includes('exploit')) vector = 'Exploit';
                else if (vector.includes('backdoor')) vector = 'Backdoor';
                else if (vector.includes('trojan')) vector = 'Trojan';
                else if (vector.includes('botnet')) vector = 'Botnet';
                else return; // Skip non-attack vector tags
                
                attackVectors[vector] = (attackVectors[vector] || 0) + 1;
              });
            }
          });
          
          // Add to report
          report.attackVectors = Object.entries(attackVectors)
            .sort((a, b) => b[1] - a[1])
            .reduce((acc, [key, value]) => {
              acc[key] = value;
              return acc;
            }, {});
          
          // Generate recommendations based on top attack vectors
          const topVectors = Object.keys(report.attackVectors).slice(0, 3);
          
          if (topVectors.includes('Ransomware')) {
            report.recommendations.push(
              'Implement regular backup procedures and test recovery processes',
              'Deploy endpoint protection with anti-ransomware capabilities',
              'Segment networks to limit lateral movement'
            );
          }
          
          if (topVectors.includes('Phishing')) {
            report.recommendations.push(
              'Conduct regular phishing awareness training',
              'Implement email filtering solutions',
              'Deploy multi-factor authentication'
            );
          }
          
          if (topVectors.includes('Exploit')) {
            report.recommendations.push(
              'Establish a robust patch management process',
              'Conduct regular vulnerability scanning',
              'Implement a web application firewall'
            );
          }
          
          if (topVectors.includes('Malware') || topVectors.includes('Trojan')) {
            report.recommendations.push(
              'Deploy advanced endpoint protection',
              'Implement application whitelisting',
              'Conduct regular system scans'
            );
          }
          
          if (topVectors.includes('DDoS') || topVectors.includes('Botnet')) {
            report.recommendations.push(
              'Implement DDoS protection services',
              'Configure network rate limiting',
              'Develop a DDoS response plan'
            );
          }
        }
      }
      
      return report;
    } catch (error) {
      console.error('Error generating threat report:', error);
      throw error;
    }
  }

  /**
   * Analyze a specific domain for threats
   * @param {string} domain - Domain to analyze
   * @returns {Object} - Domain analysis results
   */
  async analyzeDomain(domain) {
    if (!this.initialized) await this.initialize();
    
    const result = {
      domain,
      sources: [],
      riskScore: 0,
      riskFactors: [],
      relatedIndicators: [],
      analysisTimestamp: new Date().toISOString()
    };
    
    try {
      // Get data from OTX if available
      if (this.services.otx) {
        const otxData = await this.services.otx.getDomainReputation(domain);
        if (otxData) {
          result.sources.push('AlienVault OTX');
          
          // Add pulse data
          if (otxData.pulse_info && otxData.pulse_info.count > 0) {
            const pulseCount = otxData.pulse_info.count;
            result.riskScore += Math.min(pulseCount * 5, 30); // Max 30 points from pulse count
            
            if (pulseCount > 5) {
              result.riskFactors.push('Associated with multiple threat intelligence reports');
            }
            
            // Extract related indicators
            if (otxData.pulse_info.pulses) {
              otxData.pulse_info.pulses.forEach(pulse => {
                if (pulse.indicators) {
                  pulse.indicators.forEach(indicator => {
                    if (indicator.type !== 'domain' && indicator.indicator !== domain) {
                      result.relatedIndicators.push({
                        type: indicator.type,
                        indicator: indicator.indicator,
                        title: pulse.name,
                        created: pulse.created
                      });
                    }
                  });
                }
              });
            }
          }
          
          // Add passive DNS data
          if (otxData.passive_dns && otxData.passive_dns.length > 0) {
            result.passiveDNS = otxData.passive_dns.map(record => ({
              address: record.address,
              firstSeen: record.first,
              lastSeen: record.last
            }));
            
            // Check for recently created records (potential DGA)
            const recentRecords = otxData.passive_dns.filter(record => {
              const firstSeen = new Date(record.first);
              const now = new Date();
              const daysDiff = (now - firstSeen) / (1000 * 60 * 60 * 24);
              return daysDiff < 30;
            });
            
            if (recentRecords.length > 5) {
              result.riskFactors.push('Multiple recent DNS records (potential DGA)');
              result.riskScore += 15;
            }
          }
          
          // Add WHOIS data if available
          if (otxData.whois) {
            result.whois = {
              creationDate: otxData.whois.creation_date,
              expirationDate: otxData.whois.expiration_date,
              updatedDate: otxData.whois.updated_date,
              registrar: otxData.whois.registrar,
              nameServers: otxData.whois.nameservers
            };
            
            // Check for recently registered domains
            if (otxData.whois.creation_date) {
              const creationDate = new Date(otxData.whois.creation_date);
              const now = new Date();
              const daysDiff = (now - creationDate) / (1000 * 60 * 60 * 24);
              
              if (daysDiff < 30) {
                result.riskFactors.push('Recently registered domain');
                result.riskScore += 20;
              }
            }
          }
          
          // Add malware data if available
          if (otxData.malware && otxData.malware.count > 0) {
            result.riskFactors.push('Associated with malware');
            result.riskScore += 25;
            
            result.malware = {
              count: otxData.malware.count,
              samples: otxData.malware.samples?.map(sample => ({
                hash: sample.hash,
                date: sample.date,
                name: sample.name
              })) || []
            };
          }
          
          // Add URL data if available
          if (otxData.url_list && otxData.url_list.url_list && otxData.url_list.url_list.length > 0) {
            result.urls = otxData.url_list.url_list.map(url => ({
              url: url.url,
              date: url.date,
              domain: url.domain
            }));
          }
        }
      }
      
      // Cap risk score at 100
      result.riskScore = Math.min(Math.round(result.riskScore), 100);
      
      // Add risk level based on score
      if (result.riskScore >= 75) {
        result.riskLevel = 'Critical';
      } else if (result.riskScore >= 50) {
        result.riskLevel = 'High';
      } else if (result.riskScore >= 25) {
        result.riskLevel = 'Medium';
      } else {
        result.riskLevel = 'Low';
      }
      
      return result;
    } catch (error) {
      console.error(`Error analyzing domain ${domain}:`, error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const threatAnalytics = new ThreatAnalyticsService();
export default threatAnalytics;
