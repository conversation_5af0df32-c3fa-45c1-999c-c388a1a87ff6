import winston from 'winston';
import 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs-extra';

const createLogger = (service) => {
  const logDir = path.join('logs', service);

  // Ensure log directory exists with proper permissions
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true, mode: 0o755 });
  }

  const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
    transports: [
      new winston.transports.DailyRotateFile({
        filename: path.join(logDir, '%DATE%-error.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        maxSize: '20m',
        maxFiles: '14d',
        handleExceptions: true,
        zippedArchive: true,
        // Add proper file permissions
        options: { mode: 0o644 }
      }),
      new winston.transports.DailyRotateFile({
        filename: path.join(logDir, '%DATE%-combined.log'),
        datePattern: 'YYYY-MM-DD',
        maxSize: '20m',
        maxFiles: '14d',
        zippedArchive: true,
        // Add proper file permissions
        options: { mode: 0o644 }
      })
    ],
    // Handle uncaught exceptions and unhandled rejections
    exceptionHandlers: [
      new winston.transports.DailyRotateFile({
        filename: path.join(logDir, '%DATE%-exceptions.log'),
        datePattern: 'YYYY-MM-DD',
        maxSize: '20m',
        maxFiles: '14d',
        handleExceptions: true,
        zippedArchive: true,
        options: { mode: 0o644 }
      })
    ],
    rejectionHandlers: [
      new winston.transports.DailyRotateFile({
        filename: path.join(logDir, '%DATE%-rejections.log'),
        datePattern: 'YYYY-MM-DD',
        maxSize: '20m',
        maxFiles: '14d',
        handleRejections: true,
        zippedArchive: true,
        options: { mode: 0o644 }
      })
    ],
    exitOnError: false
  });

  // Add console transport in development
  if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
      handleExceptions: true,
      handleRejections: true
    }));
  }

  return logger;
};

export default createLogger;