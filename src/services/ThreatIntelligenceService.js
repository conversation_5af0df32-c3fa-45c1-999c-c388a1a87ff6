/**
 * Threat Intelligence Service
 * 
 * This service provides a unified interface to access threat intelligence data
 * from multiple sources including AbuseIPDB and AlienVault OTX.
 */

import axios from 'axios';

// API configuration
const API_CONFIG = {
  abuseIPDB: {
    baseUrl: 'https://api.abuseipdb.com/api/v2',
    apiKey: import.meta.env.VITE_ABUSEIPDB_API_KEY || '********************************************************************************'
  },
  otx: {
    baseUrl: 'https://otx.alienvault.com/api/v1',
    apiKey: import.meta.env.VITE_OTX_API_KEY || '437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0'
  }
};

// Create axios instances for each API
const abuseIPDBClient = axios.create({
  baseURL: API_CONFIG.abuseIPDB.baseUrl,
  headers: {
    'Key': API_CONFIG.abuseIPDB.apiKey,
    'Accept': 'application/json'
  }
});

const otxClient = axios.create({
  baseURL: API_CONFIG.otx.baseUrl,
  headers: {
    'X-OTX-API-KEY': API_CONFIG.otx.apiKey,
    'Content-Type': 'application/json'
  }
});

// Cache mechanism to reduce API calls
const cache = {
  data: {},
  timestamp: {},
  maxAge: 15 * 60 * 1000, // 15 minutes in milliseconds
  
  get(key) {
    const now = Date.now();
    if (this.data[key] && (now - this.timestamp[key] < this.maxAge)) {
      return this.data[key];
    }
    return null;
  },
  
  set(key, data) {
    this.data[key] = data;
    this.timestamp[key] = Date.now();
  }
};

/**
 * Threat Intelligence Service
 */
class ThreatIntelligenceService {
  /**
   * Get blacklisted IPs from AbuseIPDB
   * @param {number} confidenceMinimum - Minimum confidence score (0-100)
   * @param {number} limit - Maximum number of results
   * @returns {Promise<Array>} - Promise with blacklist data
   */
  async getBlacklistedIPs(confidenceMinimum = 90, limit = 25) {
    const cacheKey = `blacklist_${confidenceMinimum}_${limit}`;
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }
    
    try {
      const response = await abuseIPDBClient.get('/blacklist', {
        params: {
          confidenceMinimum,
          limit
        }
      });
      
      const data = response.data.data;
      cache.set(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching blacklisted IPs:', error);
      throw error;
    }
  }
  
  /**
   * Check an IP address for abuse reports
   * @param {string} ip - The IP address to check
   * @returns {Promise<Object>} - Promise with IP check data
   */
  async checkIP(ip) {
    const cacheKey = `ip_${ip}`;
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }
    
    try {
      const response = await abuseIPDBClient.get('/check', {
        params: {
          ipAddress: ip,
          maxAgeInDays: 90,
          verbose: true
        }
      });
      
      const data = response.data.data;
      cache.set(cacheKey, data);
      return data;
    } catch (error) {
      console.error(`Error checking IP ${ip}:`, error);
      throw error;
    }
  }
  
  /**
   * Get recent pulses from OTX
   * @param {number} limit - Maximum number of pulses to retrieve
   * @returns {Promise<Array>} - Promise with pulse data
   */
  async getPulses(limit = 10) {
    const cacheKey = `pulses_${limit}`;
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }
    
    try {
      const response = await otxClient.get(`/pulses/subscribed?limit=${limit}`);
      const data = response.data.results;
      cache.set(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching OTX pulses:', error);
      throw error;
    }
  }
  
  /**
   * Get indicators for a specific pulse
   * @param {string} pulseId - The ID of the pulse
   * @returns {Promise<Array>} - Promise with indicator data
   */
  async getPulseIndicators(pulseId) {
    const cacheKey = `pulse_indicators_${pulseId}`;
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }
    
    try {
      const response = await otxClient.get(`/pulses/${pulseId}/indicators`);
      const data = response.data;
      cache.set(cacheKey, data);
      return data;
    } catch (error) {
      console.error(`Error fetching indicators for pulse ${pulseId}:`, error);
      throw error;
    }
  }
  
  /**
   * Get information about a domain
   * @param {string} domain - The domain to look up
   * @returns {Promise<Object>} - Promise with domain data
   */
  async getDomainInfo(domain) {
    const cacheKey = `domain_${domain}`;
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }
    
    try {
      const response = await otxClient.get(`/indicators/domain/${domain}/general`);
      const data = response.data;
      cache.set(cacheKey, data);
      return data;
    } catch (error) {
      console.error(`Error fetching domain info for ${domain}:`, error);
      throw error;
    }
  }
  
  /**
   * Search for indicators across OTX
   * @param {string} query - The search query
   * @returns {Promise<Array>} - Promise with search results
   */
  async searchIndicators(query) {
    const cacheKey = `search_${query}`;
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }
    
    try {
      const response = await otxClient.get(`/search/pulses?q=${encodeURIComponent(query)}`);
      const data = response.data.results;
      cache.set(cacheKey, data);
      return data;
    } catch (error) {
      console.error(`Error searching indicators for ${query}:`, error);
      throw error;
    }
  }
  
  /**
   * Get the latest threat of the week
   * This combines data from multiple sources to create a comprehensive threat profile
   * @returns {Promise<Object>} - Promise with threat of the week data
   */
  async getThreatOfTheWeek() {
    try {
      // Get recent pulses from OTX
      const pulses = await this.getPulses(20);
      
      if (!pulses || pulses.length === 0) {
        throw new Error('No pulses found');
      }
      
      // Find the most significant pulse based on indicators count and references
      const significantPulses = pulses
        .filter(pulse => pulse.indicator_count > 3)
        .sort((a, b) => {
          // Score based on indicator count, references, and recency
          const scoreA = (a.indicator_count || 0) + (a.references?.length || 0) * 2;
          const scoreB = (b.indicator_count || 0) + (b.references?.length || 0) * 2;
          return scoreB - scoreA;
        });
      
      if (significantPulses.length === 0) {
        throw new Error('No significant pulses found');
      }
      
      const selectedPulse = significantPulses[0];
      
      // Get indicators for the selected pulse
      const indicators = await this.getPulseIndicators(selectedPulse.id);
      
      // Group indicators by type
      const groupedIndicators = indicators.reduce((acc, indicator) => {
        if (!acc[indicator.type]) {
          acc[indicator.type] = [];
        }
        acc[indicator.type].push(indicator);
        return acc;
      }, {});
      
      // Extract attack vectors from tags
      const attackVectors = selectedPulse.tags
        .filter(tag => 
          tag.toLowerCase().includes('malware') || 
          tag.toLowerCase().includes('ransomware') || 
          tag.toLowerCase().includes('exploit') || 
          tag.toLowerCase().includes('phishing') ||
          tag.toLowerCase().includes('backdoor') ||
          tag.toLowerCase().includes('trojan')
        );
      
      // Create a timeline based on pulse created date and modified date
      const timeline = [
        { date: new Date(selectedPulse.created).toLocaleDateString(), event: 'Initial discovery' }
      ];
      
      if (selectedPulse.modified !== selectedPulse.created) {
        timeline.push({ 
          date: new Date(selectedPulse.modified).toLocaleDateString(), 
          event: 'Latest update with new indicators' 
        });
      }
      
      // Add references to timeline if available
      if (selectedPulse.references && selectedPulse.references.length > 0) {
        timeline.push({ 
          date: new Date(selectedPulse.created).toLocaleDateString(), 
          event: `Referenced in ${selectedPulse.references.length} external sources` 
        });
      }
      
      // Create detection methods based on indicator types
      const detectionMethods = [];
      
      if (groupedIndicators['IPv4'] || groupedIndicators['IPv6']) {
        detectionMethods.push('Monitor for connections to malicious IP addresses');
      }
      
      if (groupedIndicators['domain'] || groupedIndicators['hostname']) {
        detectionMethods.push('Implement DNS monitoring for suspicious domains');
      }
      
      if (groupedIndicators['FileHash-MD5'] || groupedIndicators['FileHash-SHA1'] || groupedIndicators['FileHash-SHA256']) {
        detectionMethods.push('Deploy hash-based detection for malicious files');
      }
      
      if (groupedIndicators['URL']) {
        detectionMethods.push('Filter web traffic for malicious URLs');
      }
      
      // Create mitigation steps based on attack vectors
      const mitigationSteps = [];
      
      if (attackVectors.some(v => v.toLowerCase().includes('ransomware'))) {
        mitigationSteps.push('Implement regular backup procedures and test recovery processes');
        mitigationSteps.push('Deploy endpoint protection with anti-ransomware capabilities');
      }
      
      if (attackVectors.some(v => v.toLowerCase().includes('phishing'))) {
        mitigationSteps.push('Conduct regular phishing awareness training');
        mitigationSteps.push('Implement email filtering solutions');
      }
      
      if (attackVectors.some(v => v.toLowerCase().includes('exploit'))) {
        mitigationSteps.push('Establish a robust patch management process');
        mitigationSteps.push('Conduct regular vulnerability scanning');
      }
      
      // Construct the threat of the week object
      const threatOfTheWeek = {
        title: selectedPulse.name,
        summary: selectedPulse.description,
        technicalDetails: {
          attackVectors: attackVectors.length > 0 ? attackVectors : ['Unknown attack vector'],
          indicators: Object.entries(groupedIndicators).flatMap(([type, items]) => 
            items.slice(0, 5).map(item => ({
              type,
              value: item.indicator
            }))
          ).slice(0, 10),
          techniques: selectedPulse.tags
            .filter(tag => tag.match(/^T\d{4}(\.\d{3})?$/))
            .map(technique => {
              // Map MITRE ATT&CK technique IDs to names (simplified mapping)
              const techniqueMap = {
                'T1566': { name: 'Phishing', description: 'Adversaries may send phishing messages to gain access to victim systems.' },
                'T1566.001': { name: 'Phishing: Spearphishing Attachment', description: 'Adversaries may send spearphishing emails with a malicious attachment in an attempt to gain access to victim systems.' },
                'T1566.002': { name: 'Phishing: Spearphishing Link', description: 'Adversaries may send spearphishing emails with a malicious link in an attempt to gain access to victim systems.' },
                'T1027': { name: 'Obfuscated Files or Information', description: 'Adversaries may attempt to make an executable or file difficult to discover or analyze by encrypting, encoding, or otherwise obfuscating its contents on the system.' },
                'T1027.002': { name: 'Software Packing', description: 'Adversaries may use software packing to conceal their code.' },
                'T1027.005': { name: 'Indicator Removal from Tools', description: 'Adversaries may remove indicators from tools if they believe their malicious tool was detected.' },
                'T1059': { name: 'Command and Scripting Interpreter', description: 'Adversaries may abuse command and script interpreters to execute commands, scripts, or binaries.' },
                'T1059.001': { name: 'PowerShell', description: 'Adversaries may abuse PowerShell commands and scripts for execution.' },
                'T1059.003': { name: 'Windows Command Shell', description: 'Adversaries may abuse the Windows command shell for execution.' },
                'T1204': { name: 'User Execution', description: 'Adversaries may rely upon specific actions by a user in order to gain execution.' },
                'T1204.001': { name: 'Malicious Link', description: 'Adversaries may rely upon a user clicking a malicious link in order to gain execution.' },
                'T1204.002': { name: 'Malicious File', description: 'Adversaries may rely upon a user opening a malicious file in order to gain execution.' }
              };
              
              const baseId = technique.split('.')[0];
              const details = techniqueMap[technique] || techniqueMap[baseId] || { 
                name: `Unknown Technique (${technique})`, 
                description: 'No description available for this technique.' 
              };
              
              return {
                id: technique,
                name: details.name,
                description: details.description
              };
            })
        },
        timeline,
        detectionMethods: detectionMethods.length > 0 ? detectionMethods : ['No specific detection methods available'],
        mitigationSteps: mitigationSteps.length > 0 ? mitigationSteps : ['No specific mitigation steps available'],
        learningObjectives: [
          `Understand ${selectedPulse.name} attack techniques`,
          'Identify indicators of compromise associated with this threat',
          'Learn effective detection strategies',
          'Practice incident response for this type of attack'
        ],
        challengeScenario: {
          description: `You are a security analyst who has detected potential indicators related to ${selectedPulse.name}. Your task is to analyze the provided evidence, confirm the infection, and recommend containment steps.`,
          evidenceFiles: ['network_capture.pcap', 'suspicious_process_logs.txt', 'email_headers.txt'],
          questions: [
            'Identify the initial infection vector based on the provided evidence',
            'Determine which systems have been compromised',
            'Recommend immediate containment actions',
            'Create a timeline of the attack based on log evidence'
          ]
        }
      };
      
      return threatOfTheWeek;
    } catch (error) {
      console.error('Error generating threat of the week:', error);
      throw error;
    }
  }
  
  /**
   * Get hunting challenges based on real threat data
   * @returns {Promise<Array>} - Promise with hunting challenges
   */
  async getHuntingChallenges() {
    try {
      // Get recent pulses from OTX
      const pulses = await this.getPulses(30);
      
      if (!pulses || pulses.length === 0) {
        throw new Error('No pulses found');
      }
      
      // Get blacklisted IPs from AbuseIPDB
      const blacklistedIPs = await this.getBlacklistedIPs(80, 20);
      
      // Create hunting challenges based on real threat data
      const challenges = [];
      
      // Categorize pulses by type
      const ransomwarePulses = pulses.filter(pulse => 
        pulse.tags.some(tag => tag.toLowerCase().includes('ransomware'))
      );
      
      const aptPulses = pulses.filter(pulse => 
        pulse.tags.some(tag => tag.toLowerCase().includes('apt'))
      );
      
      const malwarePulses = pulses.filter(pulse => 
        pulse.tags.some(tag => tag.toLowerCase().includes('malware')) &&
        !pulse.tags.some(tag => tag.toLowerCase().includes('ransomware'))
      );
      
      // Create ransomware challenge if available
      if (ransomwarePulses.length > 0) {
        const pulse = ransomwarePulses[0];
        challenges.push({
          id: `hunt-ransomware-${Date.now()}`,
          title: `${pulse.name} Ransomware Detection`,
          difficulty: 'Beginner',
          category: 'Endpoint',
          description: `Learn to identify ${pulse.name} ransomware activity by analyzing endpoint logs and system behavior.`,
          objectives: [
            'Identify suspicious process execution patterns',
            'Detect file encryption activities',
            'Recognize ransomware persistence mechanisms'
          ],
          points: 100,
          estimatedTime: '30 minutes',
          prerequisites: [],
          unlocked: true,
          realData: {
            pulseId: pulse.id,
            tags: pulse.tags,
            references: pulse.references
          }
        });
      }
      
      // Create C2 communication challenge
      if (blacklistedIPs.length > 0) {
        challenges.push({
          id: `hunt-c2-${Date.now()}`,
          title: 'C2 Communication Hunt',
          difficulty: 'Intermediate',
          category: 'Network',
          description: 'Hunt for command and control (C2) communications by analyzing network traffic patterns.',
          objectives: [
            'Identify beaconing patterns in network traffic',
            'Detect domain generation algorithms (DGA)',
            'Recognize encrypted C2 channels'
          ],
          points: 200,
          estimatedTime: '45 minutes',
          prerequisites: challenges.length > 0 ? [challenges[0].id] : [],
          unlocked: true,
          realData: {
            ipAddresses: blacklistedIPs.slice(0, 5).map(ip => ip.ipAddress),
            countries: [...new Set(blacklistedIPs.slice(0, 5).map(ip => ip.countryCode))],
            confidenceScores: blacklistedIPs.slice(0, 5).map(ip => ip.abuseConfidenceScore)
          }
        });
      }
      
      // Create APT challenge if available
      if (aptPulses.length > 0) {
        const pulse = aptPulses[0];
        challenges.push({
          id: `hunt-apt-${Date.now()}`,
          title: `${pulse.name} APT Hunt`,
          difficulty: 'Advanced',
          category: 'Multi-source',
          description: `Learn to track sophisticated ${pulse.name} APT activities across multiple data sources.`,
          objectives: [
            'Correlate indicators across different log sources',
            'Identify lateral movement techniques',
            'Detect data exfiltration attempts'
          ],
          points: 350,
          estimatedTime: '60 minutes',
          prerequisites: challenges.length > 0 ? [challenges[0].id] : [],
          unlocked: false,
          realData: {
            pulseId: pulse.id,
            tags: pulse.tags,
            references: pulse.references
          }
        });
      }
      
      // Create malware analysis challenge if available
      if (malwarePulses.length > 0) {
        const pulse = malwarePulses[0];
        challenges.push({
          id: `hunt-malware-${Date.now()}`,
          title: `${pulse.name} Analysis`,
          difficulty: 'Intermediate',
          category: 'Malware',
          description: `Analyze ${pulse.name} malware behavior and identify its capabilities.`,
          objectives: [
            'Identify malware infection vectors',
            'Analyze malware behavior and capabilities',
            'Develop detection strategies'
          ],
          points: 250,
          estimatedTime: '50 minutes',
          prerequisites: challenges.length > 0 ? [challenges[0].id] : [],
          unlocked: false,
          realData: {
            pulseId: pulse.id,
            tags: pulse.tags,
            references: pulse.references
          }
        });
      }
      
      return challenges;
    } catch (error) {
      console.error('Error generating hunting challenges:', error);
      throw error;
    }
  }
  
  /**
   * Get AI assistant data based on real threat intelligence
   * @returns {Promise<Object>} - Promise with AI assistant data
   */
  async getAIAssistantData() {
    try {
      // Get recent pulses from OTX
      const pulses = await this.getPulses(10);
      
      if (!pulses || pulses.length === 0) {
        throw new Error('No pulses found');
      }
      
      // Extract suggested questions based on pulse data
      const suggestedQuestions = [];
      
      // Add questions about specific threats
      if (pulses.length > 0) {
        const recentThreat = pulses[0];
        suggestedQuestions.push(`What is ${recentThreat.name}?`);
      }
      
      // Add questions about attack techniques
      const techniques = pulses.flatMap(pulse => 
        pulse.tags.filter(tag => tag.match(/^T\d{4}(\.\d{3})?$/))
      );
      
      if (techniques.length > 0) {
        const uniqueTechniques = [...new Set(techniques)];
        if (uniqueTechniques.length > 0) {
          suggestedQuestions.push(`Explain the ${uniqueTechniques[0]} technique`);
        }
      }
      
      // Add general questions
      suggestedQuestions.push(
        "What is a command and control (C2) server?",
        "Explain the MITRE ATT&CK framework",
        "How do I detect lateral movement in a network?",
        "What are common indicators of a ransomware attack?",
        "Explain the difference between signature and behavior-based detection"
      );
      
      // Extract related learning resources
      const relatedLearning = [];
      
      // Add resources based on pulse tags
      const allTags = pulses.flatMap(pulse => pulse.tags);
      const uniqueTags = [...new Set(allTags)];
      
      if (uniqueTags.some(tag => tag.toLowerCase().includes('ransomware'))) {
        relatedLearning.push({
          title: 'Ransomware Defense',
          description: 'Strategies for preventing and responding to ransomware attacks',
          module: 'ransomware-defense'
        });
      }
      
      if (uniqueTags.some(tag => tag.toLowerCase().includes('apt'))) {
        relatedLearning.push({
          title: 'APT Detection Techniques',
          description: 'Methods for identifying advanced persistent threats',
          module: 'apt-detection'
        });
      }
      
      if (uniqueTags.some(tag => tag.toLowerCase().includes('phishing'))) {
        relatedLearning.push({
          title: 'Phishing Analysis',
          description: 'Techniques for analyzing and detecting phishing attempts',
          module: 'phishing-analysis'
        });
      }
      
      // Add default resources
      relatedLearning.push(
        {
          title: 'MITRE ATT&CK Framework',
          description: 'Comprehensive threat model and knowledge base',
          module: 'mitre-attack'
        },
        {
          title: 'Threat Hunting Fundamentals',
          description: 'Learn proactive threat detection techniques',
          module: 'threat-hunting'
        }
      );
      
      return {
        suggestedQuestions: suggestedQuestions.slice(0, 5),
        relatedLearning: relatedLearning.slice(0, 3),
        recentThreats: pulses.slice(0, 3).map(pulse => ({
          name: pulse.name,
          description: pulse.description,
          created: pulse.created,
          tags: pulse.tags
        }))
      };
    } catch (error) {
      console.error('Error generating AI assistant data:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const threatIntelligenceService = new ThreatIntelligenceService();
export default threatIntelligenceService;
