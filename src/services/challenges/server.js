import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createClient } from '@supabase/supabase-js';
import winston from 'winston';
import morgan from 'morgan';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'challenges-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'challenges-combined.log' })
  ]
});

const app = express();

app.use(cors());
app.use(helmet());
app.use(express.json());
app.use(morgan('combined'));

// Challenge routes
app.get('/api/challenges', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('challenges')
      .select('*')
      .eq('is_open', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    res.json(data);
  } catch (error) {
    logger.error('Error fetching challenges:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/challenges/submit', async (req, res) => {
  try {
    const { challengeId, userId, status, pointsEarned } = req.body;
    
    const { data, error } = await supabase
      .from('challenge_submissions')
      .insert([{
        user_id: userId,
        challenge_id: challengeId,
        status,
        points_earned: pointsEarned,
        completion_time: status === 'completed' ? new Date() : null
      }])
      .select();

    if (error) throw error;
    res.json(data);
  } catch (error) {
    logger.error('Error submitting challenge:', error);
    res.status(500).json({ error: error.message });
  }
});

const PORT = process.env.CHALLENGES_SERVICE_PORT || 3003;
app.listen(PORT, () => {
  console.log(`Challenges service running on port ${PORT}`);
});