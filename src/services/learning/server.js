import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createClient } from '@supabase/supabase-js';
import winston from 'winston';
import morgan from 'morgan';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'learning-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'learning-combined.log' })
  ]
});

const app = express();

app.use(cors());
app.use(helmet());
app.use(express.json());
app.use(morgan('combined'));

// Learning paths routes
app.get('/api/learning/paths', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('learning_paths')
      .select('*')
      .order('created_at', { ascending: true });

    if (error) throw error;
    res.json(data);
  } catch (error) {
    logger.error('Error fetching learning paths:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/learning/progress/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const { data, error } = await supabase
      .from('user_progress')
      .select('*')
      .eq('user_id', userId);

    if (error) throw error;
    res.json(data);
  } catch (error) {
    logger.error('Error fetching user progress:', error);
    res.status(500).json({ error: error.message });
  }
});

const PORT = process.env.LEARNING_SERVICE_PORT || 3004;
app.listen(PORT, () => {
  console.log(`Learning service running on port ${PORT}`);
});