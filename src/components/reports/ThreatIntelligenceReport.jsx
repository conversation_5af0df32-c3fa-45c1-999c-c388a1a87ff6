import React, { useState, useEffect, useRef } from 'react';
import { FaExclamationTriangle, FaDownload, Fa<PERSON><PERSON><PERSON>, FaCheck } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import threatAnalytics from '../../services/api/threatAnalyticsService';

const ThreatIntelligenceReport = () => {
  const { darkMode } = useGlobalTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [report, setReport] = useState(null);
  const [generating, setGenerating] = useState(false);
  const [reportTitle, setReportTitle] = useState('Threat Intelligence Report');
  const reportRef = useRef(null);

  // Generate report
  const generateReport = async () => {
    try {
      setGenerating(true);
      setError(null);
      
      // Initialize threat analytics service
      await threatAnalytics.initialize();
      
      // Generate threat report
      const threatReport = await threatAnalytics.generateThreatReport({
        title: reportTitle
      });
      
      setReport(threatReport);
    } catch (err) {
      console.error('Error generating threat report:', err);
      setError('Failed to generate threat report. Please try again later.');
    } finally {
      setGenerating(false);
    }
  };

  // Export report as PDF
  const exportAsPDF = () => {
    // This is a placeholder - in a real implementation, you would use a library like jsPDF
    // to generate a PDF from the report content
    alert('PDF export functionality would be implemented here');
  };

  // Export report as CSV
  const exportAsCSV = () => {
    if (!report) return;
    
    // Create CSV content
    let csvContent = 'data:text/csv;charset=utf-8,';
    
    // Add header
    csvContent += `${report.title} - Generated on ${new Date(report.generatedAt).toLocaleString()}\n\n`;
    
    // Add summary
    csvContent += 'Summary\n';
    csvContent += `Total Threats,${report.summary.totalThreats}\n`;
    csvContent += `Critical Threats,${report.summary.criticalThreats}\n`;
    csvContent += `High Threats,${report.summary.highThreats}\n`;
    csvContent += `Medium Threats,${report.summary.mediumThreats}\n`;
    csvContent += `Low Threats,${report.summary.lowThreats}\n\n`;
    
    // Add geographic distribution
    csvContent += 'Geographic Distribution\n';
    csvContent += 'Country,Count\n';
    Object.entries(report.geographicDistribution).forEach(([country, count]) => {
      csvContent += `${country},${count}\n`;
    });
    csvContent += '\n';
    
    // Add attack vectors
    csvContent += 'Attack Vectors\n';
    csvContent += 'Vector,Count\n';
    Object.entries(report.attackVectors).forEach(([vector, count]) => {
      csvContent += `${vector},${count}\n`;
    });
    csvContent += '\n';
    
    // Add top threats
    csvContent += 'Top Threats\n';
    csvContent += 'IP Address,Risk Score,Risk Level,Country\n';
    report.topThreats.forEach(threat => {
      csvContent += `${threat.ip},${threat.riskScore},${threat.riskLevel},${threat.countryName}\n`;
    });
    csvContent += '\n';
    
    // Add recommendations
    csvContent += 'Recommendations\n';
    report.recommendations.forEach(recommendation => {
      csvContent += `${recommendation}\n`;
    });
    
    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `${report.title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    
    // Trigger download
    link.click();
    
    // Clean up
    document.body.removeChild(link);
  };

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Threat Intelligence Report Generator</h2>
        
        {report && (
          <div className="flex space-x-2">
            <button
              onClick={exportAsPDF}
              className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm flex items-center"
            >
              <FaDownload className="mr-1" /> PDF
            </button>
            <button
              onClick={exportAsCSV}
              className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm flex items-center"
            >
              <FaDownload className="mr-1" /> CSV
            </button>
          </div>
        )}
      </div>
      
      {/* Report Generator Form */}
      <div className="bg-gray-700 rounded-lg p-4 mb-6">
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">Report Title</label>
          <input
            type="text"
            value={reportTitle}
            onChange={(e) => setReportTitle(e.target.value)}
            className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter report title"
          />
        </div>
        
        <button
          onClick={generateReport}
          disabled={generating}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center justify-center w-full"
        >
          {generating ? (
            <>
              <FaSpinner className="animate-spin mr-2" /> Generating Report...
            </>
          ) : (
            <>
              <FaCheck className="mr-2" /> Generate Report
            </>
          )}
        </button>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="bg-red-900 bg-opacity-20 border border-red-800 rounded-lg p-4 text-red-400 mb-6">
          <FaExclamationTriangle className="inline-block mr-2" />
          {error}
        </div>
      )}
      
      {/* Report Content */}
      {report && (
        <div ref={reportRef} className="bg-white text-black rounded-lg p-6 print:p-0">
          {/* Report Header */}
          <div className="border-b border-gray-300 pb-4 mb-6">
            <h1 className="text-2xl font-bold mb-2">{report.title}</h1>
            <p className="text-gray-600">
              Generated on {new Date(report.generatedAt).toLocaleString()}
            </p>
          </div>
          
          {/* Executive Summary */}
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-3">Executive Summary</h2>
            <p className="mb-4">
              This report provides an overview of the current threat landscape based on data from multiple threat intelligence sources.
              It highlights the most significant threats, their geographic distribution, and common attack vectors observed during the analysis period.
            </p>
            
            <div className="grid grid-cols-5 gap-4 mb-4">
              <div className="border rounded p-3 text-center">
                <div className="text-sm text-gray-600">Total Threats</div>
                <div className="text-xl font-bold">{report.summary.totalThreats}</div>
              </div>
              <div className="border rounded p-3 text-center">
                <div className="text-sm text-gray-600">Critical</div>
                <div className="text-xl font-bold text-red-600">{report.summary.criticalThreats}</div>
              </div>
              <div className="border rounded p-3 text-center">
                <div className="text-sm text-gray-600">High</div>
                <div className="text-xl font-bold text-orange-600">{report.summary.highThreats}</div>
              </div>
              <div className="border rounded p-3 text-center">
                <div className="text-sm text-gray-600">Medium</div>
                <div className="text-xl font-bold text-yellow-600">{report.summary.mediumThreats}</div>
              </div>
              <div className="border rounded p-3 text-center">
                <div className="text-sm text-gray-600">Low</div>
                <div className="text-xl font-bold text-green-600">{report.summary.lowThreats}</div>
              </div>
            </div>
          </div>
          
          {/* Key Findings */}
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-3">Key Findings</h2>
            
            {/* Geographic Distribution */}
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Geographic Distribution</h3>
              <p className="mb-3">
                The following countries were identified as the primary sources of malicious activity:
              </p>
              
              <table className="w-full border-collapse mb-3">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border p-2 text-left">Country</th>
                    <th className="border p-2 text-left">Count</th>
                    <th className="border p-2 text-left">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(report.geographicDistribution).map(([country, count], idx) => {
                    const total = Object.values(report.geographicDistribution).reduce((sum, val) => sum + val, 0);
                    const percentage = Math.round((count / total) * 100);
                    
                    return (
                      <tr key={idx} className={idx % 2 === 0 ? 'bg-gray-50' : ''}>
                        <td className="border p-2">{country}</td>
                        <td className="border p-2">{count}</td>
                        <td className="border p-2">{percentage}%</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
            
            {/* Attack Vectors */}
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Attack Vectors</h3>
              <p className="mb-3">
                The following attack vectors were most commonly observed:
              </p>
              
              <table className="w-full border-collapse mb-3">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border p-2 text-left">Attack Vector</th>
                    <th className="border p-2 text-left">Count</th>
                    <th className="border p-2 text-left">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(report.attackVectors).map(([vector, count], idx) => {
                    const total = Object.values(report.attackVectors).reduce((sum, val) => sum + val, 0);
                    const percentage = Math.round((count / total) * 100);
                    
                    return (
                      <tr key={idx} className={idx % 2 === 0 ? 'bg-gray-50' : ''}>
                        <td className="border p-2">{vector}</td>
                        <td className="border p-2">{count}</td>
                        <td className="border p-2">{percentage}%</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Top Threats */}
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-3">Top Threats</h2>
            <p className="mb-3">
              The following IP addresses were identified as the most significant threats:
            </p>
            
            <table className="w-full border-collapse mb-3">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border p-2 text-left">IP Address</th>
                  <th className="border p-2 text-left">Risk Score</th>
                  <th className="border p-2 text-left">Risk Level</th>
                  <th className="border p-2 text-left">Country</th>
                </tr>
              </thead>
              <tbody>
                {report.topThreats.map((threat, idx) => (
                  <tr key={idx} className={idx % 2 === 0 ? 'bg-gray-50' : ''}>
                    <td className="border p-2 font-mono">{threat.ip}</td>
                    <td className="border p-2">{threat.riskScore}/100</td>
                    <td className="border p-2">
                      <span 
                        className={`px-2 py-1 rounded text-xs ${
                          threat.riskLevel === 'Critical' ? 'bg-red-100 text-red-800' :
                          threat.riskLevel === 'High' ? 'bg-orange-100 text-orange-800' :
                          threat.riskLevel === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}
                      >
                        {threat.riskLevel}
                      </span>
                    </td>
                    <td className="border p-2">{threat.countryName}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Recommendations */}
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-3">Recommendations</h2>
            <p className="mb-3">
              Based on the analysis of the current threat landscape, the following security recommendations are provided:
            </p>
            
            <ul className="list-disc pl-6 space-y-2">
              {report.recommendations.map((recommendation, idx) => (
                <li key={idx}>{recommendation}</li>
              ))}
            </ul>
          </div>
          
          {/* Conclusion */}
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-3">Conclusion</h2>
            <p className="mb-3">
              This threat intelligence report provides an overview of the current threat landscape based on data from multiple sources.
              Organizations should use this information to enhance their security posture and implement the recommended security controls
              to mitigate the identified threats.
            </p>
            <p>
              Regular monitoring of threat intelligence feeds and updating security controls accordingly is essential to maintain
              an effective defense against evolving cyber threats.
            </p>
          </div>
          
          {/* Footer */}
          <div className="border-t border-gray-300 pt-4 text-center text-gray-600">
            <p>Generated by XCerberus Threat Intelligence Platform</p>
            <p className="text-sm">Confidential - For internal use only</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThreatIntelligenceReport;
