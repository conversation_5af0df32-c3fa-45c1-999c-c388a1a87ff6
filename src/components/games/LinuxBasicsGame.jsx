import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebglAddon } from 'xterm-addon-webgl';
import { FaChevronLeft, FaLightbulb, FaVolumeUp, FaVolumeMute, FaExpand, FaCompress, FaTerminal, FaCheck, FaServer, FaUserSecret, FaNetworkWired } from 'react-icons/fa';
import * as Tone from 'tone';
import 'xterm/css/xterm.css';

const LinuxBasicsGame = ({ mission, onComplete, onBack }) => {
  const [currentTask, setCurrentTask] = useState(0);
  const [score, setScore] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [synth] = useState(new Tone.Synth().toDestination());
  const [completedTasks, setCompletedTasks] = useState(new Set());

  const containerRef = useRef(null);
  const terminalRef = useRef(null);
  const terminalContainerRef = useRef(null);
  const terminalInstance = useRef(null);
  const fitAddonRef = useRef(null);
  const webglAddonRef = useRef(null);
  const commandBuffer = useRef('');

  // Initialize terminal
  useEffect(() => {
    if (!terminalContainerRef.current || terminalInstance.current) return;

    const term = new Terminal({
      cursorBlink: true,
      fontSize: 16,
      fontFamily: 'JetBrains Mono, monospace',
      theme: {
        background: '#000000',
        foreground: '#00ff00',
        cursor: '#00ff00',
        selection: 'rgba(0, 255, 0, 0.3)',
        black: '#000000',
        red: '#ff0000',
        green: '#00ff00',
        yellow: '#ffff00',
        blue: '#0000ff',
        magenta: '#ff00ff',
        cyan: '#00ffff',
        white: '#ffffff',
      },
      allowTransparency: true,
      rendererType: 'webgl'
    });

    // Initialize addons
    fitAddonRef.current = new FitAddon();
    term.loadAddon(fitAddonRef.current);

    try {
      webglAddonRef.current = new WebglAddon();
      term.loadAddon(webglAddonRef.current);
    } catch (e) {
      console.warn('WebGL addon not supported:', e);
    }

    term.open(terminalContainerRef.current);
    fitAddonRef.current.fit();

    // Write welcome message with ASCII art
    term.writeln('\x1b[1;32m');
    term.writeln('╔══════════════════════════════════════════╗');
    term.writeln('║     XCerberus Hacking Terminal v2.0      ║');
    term.writeln('╚══════════════════════════════════════════╝\x1b[0m');
    term.writeln('');
    term.writeln('\x1b[36mMission: \x1b[0m' + (mission?.title || 'Unknown'));
    term.writeln('\x1b[36mObjective: \x1b[0m' + (mission?.tasks[currentTask]?.description || 'N/A'));
    term.writeln('');
    term.write('\x1b[32mxcerberus@terminal:~$ \x1b[0m');

    // Handle key input
    term.onKey(({ key, domEvent }) => {
      const printable = !domEvent.altKey && !domEvent.ctrlKey && !domEvent.metaKey;

      if (domEvent.keyCode === 13) { // Enter
        const command = commandBuffer.current.trim();
        handleCommand(command);
        commandBuffer.current = '';
        term.write('\r\n\x1b[32mxcerberus@terminal:~$ \x1b[0m');
      } else if (domEvent.keyCode === 8) { // Backspace
        if (commandBuffer.current.length > 0) {
          commandBuffer.current = commandBuffer.current.slice(0, -1);
          term.write('\b \b');
        }
      } else if (printable) {
        commandBuffer.current += key;
        term.write(key);
      }
    });

    terminalInstance.current = term;

    // Handle window resize
    const handleResize = () => {
      if (fitAddonRef.current) {
        fitAddonRef.current.fit();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (webglAddonRef.current) {
        webglAddonRef.current.dispose();
      }
      if (fitAddonRef.current) {
        fitAddonRef.current.dispose();
      }
      if (terminalInstance.current) {
        terminalInstance.current.dispose();
      }
    };
  }, [mission, currentTask]);

  const handleCommand = (command) => {
    if (!mission?.tasks[currentTask]) return;

    const term = terminalInstance.current;
    const task = mission.tasks[currentTask];
    const isCorrect = command === task.command;

    term.writeln('');
    if (isCorrect) {
      if (!isMuted) {
        synth.triggerAttackRelease("C4", "8n");
      }
      
      setScore(prev => prev + task.points);
      setCompletedTasks(prev => new Set([...prev, currentTask]));
      
      // Success animation and message
      term.writeln('\x1b[32m✓ Command successful!\x1b[0m');
      term.writeln('\x1b[32m' + task.expectedOutput + '\x1b[0m');
      term.writeln('\x1b[33m[+] ' + task.points + ' points earned!\x1b[0m');
      
      if (currentTask < mission.tasks.length - 1) {
        setTimeout(() => {
          setCurrentTask(prev => prev + 1);
          term.writeln('\r\n\x1b[34m[*] Loading next objective...\x1b[0m');
          term.writeln('\x1b[36mObjective: \x1b[0m' + mission.tasks[currentTask + 1].description);
        }, 1500);
      } else {
        if (!isMuted) {
          synth.triggerAttackRelease("G4", "8n");
        }
        term.writeln('\r\n\x1b[33m🎉 Mission Complete! Congratulations!\x1b[0m');
        setTimeout(() => onComplete?.(), 2000);
      }
    } else {
      if (!isMuted) {
        synth.triggerAttackRelease("E3", "8n");
      }
      term.writeln('\x1b[31m✗ Command failed. Try again or use hint.\x1b[0m');
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  return (
    <div 
      ref={containerRef}
      className="min-h-screen bg-[#0d1117] text-white relative"
    >
      {/* Mission Header */}
      <div className="bg-black/50 backdrop-blur-sm border-b border-gray-800 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <FaChevronLeft />
            </button>
            <div>
              <h2 className="text-xl font-bold flex items-center gap-2">
                <FaUserSecret className="text-[#88cc14]" />
                {mission?.title || 'Mission'}
              </h2>
              <p className="text-sm text-gray-400">
                Task {currentTask + 1} of {mission?.tasks?.length || 0}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="text-[#88cc14] font-bold">
              Score: {score}
            </div>
            <button
              onClick={() => setIsMuted(!isMuted)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
            </button>
            <button
              onClick={toggleFullscreen}
              className="text-gray-400 hover:text-white transition-colors"
            >
              {isFullscreen ? <FaCompress /> : <FaExpand />}
            </button>
          </div>
        </div>
      </div>

      {/* Mission Progress */}
      <div className="p-4">
        <div className="flex items-center gap-2 mb-4">
          {mission?.tasks?.map((_, index) => (
            <div
              key={index}
              className={`w-3 h-3 rounded-full transition-colors ${
                completedTasks.has(index)
                  ? 'bg-[#88cc14]'
                  : index === currentTask
                  ? 'bg-[#88cc14] animate-pulse'
                  : 'bg-gray-800'
              }`}
            />
          ))}
        </div>

        {/* Current Objective */}
        <div className="bg-black/30 rounded-lg p-4 mb-4 border border-gray-800">
          <div className="flex items-center gap-2 mb-2">
            <FaTerminal className="text-[#88cc14]" />
            <h3 className="text-lg font-bold">Current Objective</h3>
          </div>
          <p className="text-gray-300">
            {mission?.tasks[currentTask]?.description}
          </p>
        </div>

        {/* Terminal */}
        <div className="bg-black rounded-lg overflow-hidden border border-gray-800">
          <div 
            ref={terminalContainerRef}
            className="h-[400px] w-full"
            style={{ padding: '12px' }}
          />
        </div>

        {/* Hint Section */}
        <div className="mt-4">
          <button
            onClick={() => setShowHint(!showHint)}
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
          >
            <FaLightbulb />
            <span>{showHint ? 'Hide Hint' : 'Show Hint'}</span>
          </button>

          <AnimatePresence>
            {showHint && mission?.tasks[currentTask]?.hint && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="mt-4 bg-black/30 rounded-lg p-4 border border-gray-800"
              >
                <p className="text-gray-300">{mission.tasks[currentTask].hint}</p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default LinuxBasicsGame;