import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaFolder, FaFile, FaArrowLeft, FaPlus, FaTrash } from 'react-icons/fa';

const LinuxFileSystemGame = ({ onComplete }) => {
  const [fileSystem, setFileSystem] = useState({
    '/': {
      type: 'dir',
      children: {
        'home': {
          type: 'dir',
          children: {}
        },
        'etc': {
          type: 'dir',
          children: {}
        },
        'var': {
          type: 'dir',
          children: {}
        }
      }
    }
  });
  const [currentPath, setCurrentPath] = useState('/');
  const [score, setScore] = useState(0);

  const getCurrentDirectory = () => {
    const path = currentPath.split('/').filter(Boolean);
    let current = fileSystem['/'];
    
    for (const dir of path) {
      current = current.children[dir];
    }
    
    return current;
  };

  const createItem = (name, type = 'file') => {
    const current = getCurrentDirectory();
    
    if (current.children[name]) return;

    current.children[name] = {
      type,
      children: type === 'dir' ? {} : undefined
    };

    setFileSystem({ ...fileSystem });
    setScore(prev => prev + 10);

    if (score >= 100) {
      onComplete?.();
    }
  };

  const deleteItem = (name) => {
    const current = getCurrentDirectory();
    delete current.children[name];
    setFileSystem({ ...fileSystem });
  };

  const navigateTo = (path) => {
    setCurrentPath(path);
  };

  return (
    <div className="bg-black rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <FaFolder className="text-[#88cc14]" />
            Linux File System
          </h3>
          <div className="text-[#88cc14] font-bold">
            Score: {score}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Path Navigation */}
        <div className="flex items-center gap-2 bg-gray-900 p-2 rounded-lg mb-4">
          {currentPath !== '/' && (
            <button
              onClick={() => {
                const parts = currentPath.split('/').filter(Boolean);
                parts.pop();
                navigateTo('/' + parts.join('/'));
              }}
              className="text-[#88cc14] hover:text-[#7ab811] transition-colors"
            >
              <FaArrowLeft />
            </button>
          )}
          <span className="text-white font-mono">{currentPath}</span>
        </div>

        {/* File System View */}
        <div className="grid grid-cols-4 gap-4 mb-4">
          {Object.entries(getCurrentDirectory().children).map(([name, item]) => (
            <motion.div
              key={name}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-gray-900 p-4 rounded-lg relative group"
            >
              {item.type === 'dir' ? (
                <FaFolder className="text-[#88cc14] text-2xl mb-2" />
              ) : (
                <FaFile className="text-[#88cc14] text-2xl mb-2" />
              )}
              <span className="text-white">{name}</span>

              <button
                onClick={() => deleteItem(name)}
                className="absolute top-2 right-2 text-red-400 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <FaTrash />
              </button>

              {item.type === 'dir' && (
                <div
                  className="absolute inset-0 cursor-pointer"
                  onClick={() => navigateTo(`${currentPath}${currentPath.endsWith('/') ? '' : '/'}${name}`)}
                />
              )}
            </motion.div>
          ))}
        </div>

        {/* Controls */}
        <div className="flex gap-2">
          <button
            onClick={() => createItem(`file${Date.now()}`, 'file')}
            className="bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors flex items-center gap-2"
          >
            <FaPlus />
            <span>New File</span>
          </button>

          <button
            onClick={() => createItem(`dir${Date.now()}`, 'dir')}
            className="bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors flex items-center gap-2"
          >
            <FaPlus />
            <span>New Directory</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default LinuxFileSystemGame;