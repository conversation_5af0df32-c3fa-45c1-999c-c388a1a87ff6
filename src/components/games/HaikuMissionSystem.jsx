import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaLock, FaUnlock, FaTrophy, FaMap, FaBook, FaGamepad } from 'react-icons/fa';
import * as Tone from 'tone';

const HaikuMissionSystem = ({ missions, onMissionSelect }) => {
  const [unlockedMissions, setUnlockedMissions] = useState([]);
  const [currentMission, setCurrentMission] = useState(null);
  const [showMap, setShowMap] = useState(true);
  const [synth, setSynth] = useState(null);

  useEffect(() => {
    // Initialize audio synthesizer
    const newSynth = new Tone.Synth().toDestination();
    setSynth(newSynth);

    // Load unlocked missions from progress
    setUnlockedMissions(['mission1']); // Start with first mission unlocked
  }, []);

  const playSound = (note) => {
    if (synth) {
      synth.triggerAttackRelease(note, "8n");
    }
  };

  const handleMissionClick = (mission) => {
    if (unlockedMissions.includes(mission.id)) {
      playSound("C4"); // Success sound
      setCurrentMission(mission);
      onMissionSelect(mission);
    } else {
      playSound("G3"); // Locked sound
    }
  };

  const MissionNode = ({ mission, position }) => (
    <motion.div
      className={`absolute ${position} cursor-pointer`}
      whileHover={{ scale: 1.1 }}
      onClick={() => handleMissionClick(mission)}
    >
      <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
        unlockedMissions.includes(mission.id)
          ? 'bg-[#88cc14] text-black'
          : 'bg-gray-800 text-gray-500'
      }`}>
        {unlockedMissions.includes(mission.id) ? <FaUnlock /> : <FaLock />}
      </div>
      <div className="absolute top-full mt-2 text-center w-32 -left-8">
        <p className={unlockedMissions.includes(mission.id) ? 'text-white' : 'text-gray-500'}>
          {mission.title}
        </p>
      </div>
    </motion.div>
  );

  return (
    <div className="relative min-h-screen bg-black text-white">
      {/* Mission Map */}
      {showMap && (
        <div className="relative w-full h-screen">
          <div className="absolute inset-0 bg-[url('/path/to/grid-bg.png')] opacity-10" />
          
          {/* Mission Nodes */}
          {missions.map((mission, index) => (
            <MissionNode
              key={mission.id}
              mission={mission}
              position={`top-${50 + index * 100}px left-${50 + index * 100}px`}
            />
          ))}

          {/* Connection Lines */}
          <svg className="absolute inset-0 pointer-events-none">
            {missions.map((mission, index) => {
              if (index === 0) return null;
              return (
                <line
                  key={`line-${index}`}
                  x1={50 + (index - 1) * 100}
                  y1={50 + (index - 1) * 100}
                  x2={50 + index * 100}
                  y2={50 + index * 100}
                  stroke={unlockedMissions.includes(mission.id) ? "#88cc14" : "#333"}
                  strokeWidth="2"
                  strokeDasharray="5,5"
                />
              );
            })}
          </svg>
        </div>
      )}

      {/* Mission Details */}
      {currentMission && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute inset-0 bg-black/90 p-8"
        >
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold text-[#88cc14] mb-4">
              {currentMission.title}
            </h2>
            <p className="text-gray-400 mb-6">{currentMission.description}</p>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-gray-900 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <FaTrophy className="text-[#88cc14]" />
                  <span>Rewards</span>
                </div>
                <ul className="text-gray-400">
                  {currentMission.rewards.map((reward, i) => (
                    <li key={i}>{reward}</li>
                  ))}
                </ul>
              </div>
              
              <div className="bg-gray-900 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <FaBook className="text-[#88cc14]" />
                  <span>Requirements</span>
                </div>
                <ul className="text-gray-400">
                  {currentMission.requirements.map((req, i) => (
                    <li key={i}>{req}</li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => setShowMap(true)}
                className="flex items-center gap-2 bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                <FaMap />
                <span>Back to Map</span>
              </button>
              
              <button
                onClick={() => onMissionSelect(currentMission)}
                className="flex-1 bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors flex items-center justify-center gap-2"
              >
                <FaGamepad />
                <span>Start Mission</span>
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default HaikuMissionSystem;