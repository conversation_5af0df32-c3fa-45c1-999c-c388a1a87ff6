import React, { useEffect, useRef } from 'react';
import { Terminal as XTerm } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebglAddon } from 'xterm-addon-webgl';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { SearchAddon } from 'xterm-addon-search';
import { Unicode11Addon } from 'xterm-addon-unicode11';
import 'xterm/css/xterm.css';

const Terminal = ({ onCommand, initialText, theme = 'matrix' }) => {
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);
  const commandBufferRef = useRef('');

  const themes = {
    matrix: {
      background: '#000000',
      foreground: '#88cc14',
      cursor: '#88cc14',
      selection: 'rgba(136, 204, 20, 0.3)',
      black: '#000000',
      red: '#ff0000',
      green: '#88cc14',
      yellow: '#ffff00',
      blue: '#0000ff',
      magenta: '#ff00ff',
      cyan: '#00ffff',
      white: '#ffffff'
    }
  };

  useEffect(() => {
    if (!terminalRef.current) return;

    // Initialize terminal
    const term = new XTerm({
      cursorBlink: true,
      fontSize: 16,
      fontFamily: 'JetBrains Mono, monospace',
      theme: themes[theme],
      allowTransparency: true,
      rendererType: 'webgl'
    });

    // Initialize addons
    const fitAddon = new FitAddon();
    const webglAddon = new WebglAddon();
    const webLinksAddon = new WebLinksAddon();
    const searchAddon = new SearchAddon();
    const unicode11Addon = new Unicode11Addon();

    term.loadAddon(fitAddon);
    term.loadAddon(webglAddon);
    term.loadAddon(webLinksAddon);
    term.loadAddon(searchAddon);
    term.loadAddon(unicode11Addon);

    term.open(terminalRef.current);
    fitAddon.fit();

    // Write initial text if provided
    if (initialText) {
      term.writeln(initialText);
    }

    // Write prompt
    term.write('\r\n$ ');

    // Handle input
    term.onKey(({ key, domEvent }) => {
      const printable = !domEvent.altKey && !domEvent.ctrlKey && !domEvent.metaKey;

      if (domEvent.keyCode === 13) { // Enter
        const command = commandBufferRef.current.trim();
        if (command) {
          onCommand?.(command);
          term.writeln('');
          term.write('$ ');
        }
        commandBufferRef.current = '';
      } else if (domEvent.keyCode === 8) { // Backspace
        if (commandBufferRef.current.length > 0) {
          commandBufferRef.current = commandBufferRef.current.slice(0, -1);
          term.write('\b \b');
        }
      } else if (printable) {
        commandBufferRef.current += key;
        term.write(key);
      }
    });

    // Store references
    xtermRef.current = term;
    fitAddonRef.current = fitAddon;

    // Handle resize
    const handleResize = () => fitAddon.fit();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      webglAddon.dispose();
      term.dispose();
    };
  }, [theme, initialText, onCommand]);

  return (
    <div 
      ref={terminalRef}
      className="w-full h-full min-h-[400px] bg-black rounded-lg overflow-hidden"
    />
  );
};

export default Terminal;