import React from 'react';
import { motion } from 'framer-motion';
import { FaStar, FaClock, FaTrophy, FaHeart } from 'react-icons/fa';

const GameHUD = ({ score, time, lives, level }) => {
  return (
    <motion.div
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="absolute top-0 left-0 right-0 p-4 flex justify-between items-center bg-black/50 backdrop-blur-sm"
    >
      {/* Score */}
      <div className="flex items-center gap-2">
        <FaStar className="text-yellow-400" />
        <span className="text-white font-bold">{score}</span>
      </div>

      {/* Time */}
      <div className="flex items-center gap-2">
        <FaClock className="text-[#88cc14]" />
        <span className="text-white font-bold">{time}s</span>
      </div>

      {/* Level */}
      <div className="flex items-center gap-2">
        <FaTrophy className="text-[#88cc14]" />
        <span className="text-white font-bold">Level {level}</span>
      </div>

      {/* Lives */}
      <div className="flex items-center gap-1">
        {Array.from({ length: lives }).map((_, i) => (
          <FaHeart key={i} className="text-red-500" />
        ))}
      </div>
    </motion.div>
  );
};

export default GameHUD;