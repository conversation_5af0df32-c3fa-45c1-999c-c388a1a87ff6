import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaM<PERSON>ory, FaPlus, FaMinus, FaExclamationTriangle } from 'react-icons/fa';

const MemoryGame = ({ onComplete }) => {
  const [memory, setMemory] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [score, setScore] = useState(0);
  const [gameState, setGameState] = useState('ready');
  const [error, setError] = useState(null);

  const MEMORY_SIZE = 1024; // 1024 MB total memory
  const PROCESS_SIZES = [64, 128, 256, 512]; // Available process sizes in MB

  useEffect(() => {
    // Initialize empty memory blocks
    setMemory(Array(16).fill({ allocated: false, process: null }));
  }, []);

  const allocateMemory = (processSize) => {
    // Find contiguous free blocks
    let startBlock = -1;
    let contiguousBlocks = 0;
    const blocksNeeded = processSize / 64; // Each block is 64MB

    for (let i = 0; i < memory.length; i++) {
      if (!memory[i].allocated) {
        if (startBlock === -1) startBlock = i;
        contiguousBlocks++;
        
        if (contiguousBlocks === blocksNeeded) {
          // Allocate memory
          const newMemory = [...memory];
          const processId = Date.now();
          
          for (let j = startBlock; j < startBlock + blocksNeeded; j++) {
            newMemory[j] = { allocated: true, process: processId };
          }
          
          setMemory(newMemory);
          setProcesses(prev => [...prev, { id: processId, size: processSize }]);
          setScore(prev => prev + processSize);
          
          if (score + processSize >= 1000) {
            setGameState('complete');
            onComplete?.();
          }
          
          return true;
        }
      } else {
        startBlock = -1;
        contiguousBlocks = 0;
      }
    }
    
    setError('Not enough contiguous memory!');
    return false;
  };

  const deallocateMemory = (processId) => {
    const newMemory = memory.map(block => 
      block.process === processId ? { allocated: false, process: null } : block
    );
    setMemory(newMemory);
    setProcesses(prev => prev.filter(p => p.id !== processId));
  };

  return (
    <div className="bg-black rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <FaMemory className="text-[#88cc14]" />
            Memory Manager
          </h3>
          <div className="text-[#88cc14] font-bold">
            Score: {score}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Memory Visualization */}
        <div className="grid grid-cols-8 gap-2 mb-6">
          {memory.map((block, index) => (
            <motion.div
              key={index}
              className={`h-12 rounded-lg ${
                block.allocated 
                  ? 'bg-[#88cc14]' 
                  : 'bg-gray-800'
              }`}
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.2 }}
            />
          ))}
        </div>

        {/* Process List */}
        <div className="mb-6">
          <h4 className="text-white font-bold mb-2">Running Processes</h4>
          <div className="space-y-2">
            {processes.map(process => (
              <div
                key={process.id}
                className="flex items-center justify-between bg-gray-800 p-3 rounded-lg"
              >
                <div className="flex items-center gap-2">
                  <FaMemory className="text-[#88cc14]" />
                  <span className="text-white">Process {process.id.toString().slice(-4)}</span>
                  <span className="text-gray-400">({process.size} MB)</span>
                </div>
                <button
                  onClick={() => deallocateMemory(process.id)}
                  className="text-red-400 hover:text-red-300 transition-colors"
                >
                  <FaMinus />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Controls */}
        <div className="flex gap-2">
          {PROCESS_SIZES.map(size => (
            <button
              key={size}
              onClick={() => allocateMemory(size)}
              className="bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors flex items-center gap-2"
            >
              <FaPlus />
              <span>{size}MB</span>
            </button>
          ))}
        </div>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mt-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-400 flex items-center gap-2"
          >
            <FaExclamationTriangle />
            <span>{error}</span>
          </motion.div>
        )}

        {/* Game Complete */}
        {gameState === 'complete' && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-[#88cc14] mb-4">
                Challenge Complete!
              </h3>
              <p className="text-gray-400 mb-6">
                You've mastered memory management!
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
              >
                Play Again
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="p-4 bg-gray-900 border-t border-gray-800">
        <div className="flex items-center gap-2 mb-2">
          <FaExclamationTriangle className="text-[#88cc14]" />
          <span className="text-white font-bold">How to Play</span>
        </div>
        <p className="text-gray-400">
          Allocate memory to processes by clicking the size buttons. Manage memory efficiently to avoid fragmentation. 
          Score 1000 points to complete the challenge!
        </p>
      </div>
    </div>
  );
};

export default MemoryGame;