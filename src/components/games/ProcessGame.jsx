import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Matter from 'matter-js';
import { FaMicrochip, FaMemory, FaClock, FaExclamationTriangle } from 'react-icons/fa';

const ProcessGame = ({ onComplete }) => {
  const [engine, setEngine] = useState(null);
  const [score, setScore] = useState(0);
  const [processes, setProcesses] = useState([]);
  const [gameState, setGameState] = useState('ready');

  useEffect(() => {
    // Initialize Matter.js physics engine
    const eng = Matter.Engine.create();
    const world = eng.world;

    // Create game objects
    const cpu = Matter.Bodies.rectangle(400, 300, 60, 60, { isStatic: true });
    Matter.World.add(world, cpu);

    setEngine(eng);

    // Game loop
    const gameLoop = setInterval(() => {
      Matter.Engine.update(eng);
      
      // Update process positions
      setProcesses(prev => prev.map(process => ({
        ...process,
        x: process.body.position.x,
        y: process.body.position.y
      })));
    }, 1000 / 60);

    return () => {
      clearInterval(gameLoop);
      Matter.Engine.clear(eng);
    };
  }, []);

  const spawnProcess = () => {
    if (!engine) return;

    const process = {
      id: Date.now(),
      priority: Math.floor(Math.random() * 10) + 1,
      size: Math.floor(Math.random() * 50) + 20,
      body: Matter.Bodies.circle(
        Math.random() * 800,
        0,
        20,
        { restitution: 0.6 }
      )
    };

    Matter.World.add(engine.world, process.body);
    setProcesses(prev => [...prev, process]);
  };

  const handleProcessClick = (process) => {
    // Schedule process
    setScore(prev => prev + process.priority);
    Matter.World.remove(engine.world, process.body);
    setProcesses(prev => prev.filter(p => p.id !== process.id));

    if (score >= 100) {
      setGameState('complete');
      onComplete?.();
    }
  };

  return (
    <div className="bg-black rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <FaMicrochip className="text-[#88cc14]" />
            Process Scheduler
          </h3>
          <div className="text-[#88cc14] font-bold">
            Score: {score}
          </div>
        </div>
      </div>

      <div className="relative h-[500px] bg-gray-900">
        {/* Game Canvas */}
        <div id="game-canvas" className="absolute inset-0">
          {processes.map(process => (
            <motion.div
              key={process.id}
              className="absolute"
              style={{
                left: process.x - 20,
                top: process.y - 20,
                width: 40,
                height: 40
              }}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              onClick={() => handleProcessClick(process)}
            >
              <div className="w-full h-full rounded-full bg-[#88cc14] flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                <span className="text-black font-bold">P{process.priority}</span>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CPU */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="w-16 h-16 bg-[#88cc14]/20 rounded-lg border-2 border-[#88cc14] flex items-center justify-center">
            <FaMicrochip className="text-[#88cc14] text-2xl" />
          </div>
        </div>

        {/* Controls */}
        <div className="absolute top-4 right-4">
          <button
            onClick={spawnProcess}
            className="bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
          >
            Spawn Process
          </button>
        </div>

        {/* Game Over */}
        {gameState === 'complete' && (
          <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-[#88cc14] mb-4">
                Challenge Complete!
              </h3>
              <p className="text-gray-400 mb-6">
                You've mastered process scheduling!
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
              >
                Play Again
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="p-4 bg-gray-900 border-t border-gray-800">
        <div className="flex items-center gap-2 mb-2">
          <FaExclamationTriangle className="text-[#88cc14]" />
          <span className="text-white font-bold">How to Play</span>
        </div>
        <p className="text-gray-400">
          Click on processes to schedule them. Higher priority processes (higher numbers) give more points. 
          Reach 100 points to complete the challenge!
        </p>
      </div>
    </div>
  );
};

export default ProcessGame;