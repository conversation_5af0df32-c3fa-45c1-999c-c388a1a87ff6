import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaFolder, FaFile, FaTrash, FaPlus, FaExclamationTriangle } from 'react-icons/fa';

const FileSystemGame = ({ onComplete }) => {
  const [fileSystem, setFileSystem] = useState({
    '/': { type: 'dir', children: {} }
  });
  const [currentPath, setCurrentPath] = useState('/');
  const [score, setScore] = useState(0);
  const [gameState, setGameState] = useState('ready');

  const createFile = (name, type = 'file') => {
    const path = currentPath.split('/').filter(Boolean);
    let current = fileSystem;
    
    // Navigate to current directory
    path.forEach(dir => {
      current = current[dir].children;
    });

    // Create new file/directory
    if (current[name]) {
      return false; // Already exists
    }

    current[name] = {
      type,
      ...(type === 'dir' ? { children: {} } : {})
    };

    setFileSystem({ ...fileSystem });
    setScore(prev => prev + 10);

    if (score >= 100) {
      setGameState('complete');
      onComplete?.();
    }

    return true;
  };

  const deleteItem = (name) => {
    const path = currentPath.split('/').filter(Boolean);
    let current = fileSystem;
    
    // Navigate to current directory
    path.forEach(dir => {
      current = current[dir].children;
    });

    delete current[name];
    setFileSystem({ ...fileSystem });
  };

  const navigateToDirectory = (name) => {
    if (name === '..') {
      // Go up one level
      const path = currentPath.split('/').filter(Boolean);
      path.pop();
      setCurrentPath('/' + path.join('/'));
    } else {
      // Go into directory
      setCurrentPath(currentPath + (currentPath.endsWith('/') ? '' : '/') + name);
    }
  };

  const getCurrentDirectory = () => {
    const path = currentPath.split('/').filter(Boolean);
    let current = fileSystem['/'].children;
    
    path.forEach(dir => {
      current = current[dir]?.children || {};
    });

    return current;
  };

  return (
    <div className="bg-black rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <FaFolder className="text-[#88cc14]" />
            File System Explorer
          </h3>
          <div className="text-[#88cc14] font-bold">
            Score: {score}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Path Navigation */}
        <div className="bg-gray-900 p-2 rounded-lg mb-4 flex items-center gap-2">
          <FaFolder className="text-[#88cc14]" />
          <span className="text-white font-mono">{currentPath}</span>
        </div>

        {/* File System View */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          {currentPath !== '/' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="bg-gray-800 p-4 rounded-lg cursor-pointer hover:bg-gray-700 transition-colors"
              onClick={() => navigateToDirectory('..')}
            >
              <FaFolder className="text-[#88cc14] text-2xl mb-2" />
              <span className="text-white">..</span>
            </motion.div>
          )}
          
          {Object.entries(getCurrentDirectory()).map(([name, item]) => (
            <motion.div
              key={name}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-gray-800 p-4 rounded-lg relative group"
            >
              {item.type === 'dir' ? (
                <FaFolder className="text-[#88cc14] text-2xl mb-2" />
              ) : (
                <FaFile className="text-[#88cc14] text-2xl mb-2" />
              )}
              <span className="text-white">{name}</span>
              
              {/* Delete Button */}
              <button
                onClick={() => deleteItem(name)}
                className="absolute top-2 right-2 text-red-400 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <FaTrash />
              </button>
              
              {/* Click handler for directories */}
              {item.type === 'dir' && (
                <div
                  className="absolute inset-0 cursor-pointer"
                  onClick={() => navigateToDirectory(name)}
                />
              )}
            </motion.div>
          ))}
        </div>

        {/* Controls */}
        <div className="flex gap-2">
          <button
            onClick={() => createFile('new_file_' + Date.now(), 'file')}
            className="bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors flex items-center gap-2"
          >
            <FaPlus />
            <span>New File</span>
          </button>
          
          <button
            onClick={() => createFile('new_dir_' + Date.now(), 'dir')}
            className="bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors flex items-center gap-2"
          >
            <FaPlus />
            <span>New Directory</span>
          </button>
        </div>

        {/* Game Complete */}
        {gameState === 'complete' && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-[#88cc14] mb-4">
                Challenge Complete!
              </h3>
              <p className="text-gray-400 mb-6">
                You've mastered file system operations!
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
              >
                Play Again
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="p-4 bg-gray-900 border-t border-gray-800">
        <div className="flex items-center gap-2 mb-2">
          <FaExclamationTriangle className="text-[#88cc14]" />
          <span className="text-white font-bold">How to Play</span>
        </div>
        <p className="text-gray-400">
          Create files and directories to organize your file system. Navigate through directories and manage your files.
          Score 100 points to complete the challenge!
        </p>
      </div>
    </div>
  );
};

export default FileSystemGame;