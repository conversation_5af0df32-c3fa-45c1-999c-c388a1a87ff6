import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaTerminal, FaFolder, FaCog, FaMemory, FaNetworkWired, FaLock } from 'react-icons/fa';

const OSSimulator = ({ topic, onComplete }) => {
  const [state, setState] = useState({
    processes: [],
    memory: [],
    files: [],
    devices: [],
    output: [],
    input: '',
    currentTask: null,
    score: 0,
    sessionData: null
  });

  const [loading, setLoading] = useState(false);
  const [aiResponse, setAiResponse] = useState(null);

  useEffect(() => {
    // Load saved session data from localStorage
    const loadSession = async () => {
      try {
        const savedSession = localStorage.getItem(`os_simulator_${topic}`);
        if (savedSession) {
          setState(prev => ({ ...prev, sessionData: JSON.parse(savedSession) }));
        }
      } catch (error) {
        console.error('Error loading session:', error);
      }
    };

    loadSession();
  }, [topic]);

  // Save session data to localStorage
  const saveSession = async () => {
    try {
      localStorage.setItem(`os_simulator_${topic}`, JSON.stringify(state));
    } catch (error) {
      console.error('Error saving session:', error);
    }
  };

  // Get AI assistance
  const getAIHelp = async (query) => {
    setLoading(true);
    try {
      // Generate a response based on the query and topic
      let response = '';
      
      if (topic === 'process-management') {
        response = `Process management involves creating, scheduling, and terminating processes.
        
Key commands:
- create [name] [priority] - Create a new process
- list - List all processes
- kill [id] - Terminate a process
- schedule - Run the scheduler

Try creating a process with: create myProcess 5`;
      } else if (topic === 'memory-management') {
        response = `Memory management involves allocating and deallocating memory.
        
Key commands:
- allocate [size] [process] - Allocate memory
- free [id] - Free memory
- status - Show memory status

Try allocating memory with: allocate 1024 myProcess`;
      } else if (topic === 'file-systems') {
        response = `File system operations involve creating, reading, and managing files.
        
Key commands:
- touch [name] - Create a file
- mkdir [name] - Create a directory
- ls - List files
- rm [name] - Remove a file

Try creating a file with: touch myfile.txt`;
      } else {
        response = `This is a simulation of ${topic}. Try using basic commands to interact with the system.`;
      }
      
      setAiResponse(response);
    } catch (error) {
      console.error('Error getting AI help:', error);
      setAiResponse('Sorry, I encountered an error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Simulate process management
  const simulateProcesses = (command) => {
    const [action, ...args] = command.split(' ');
    
    switch (action) {
      case 'create':
        setState(prev => {
          const newProcesses = [...prev.processes, {
            id: Date.now(),
            name: args[0] || `process-${prev.processes.length + 1}`,
            state: 'ready',
            priority: parseInt(args[1]) || 1
          }];
          
          return {
            ...prev,
            processes: newProcesses,
            output: [...prev.output, 
              { type: 'system', text: `Process ${args[0] || `process-${prev.processes.length + 1}`} created with priority ${args[1] || 1}` }
            ],
            score: prev.score + 10
          };
        });
        break;
      
      case 'list':
        setState(prev => {
          const processOutput = prev.processes.map(p => 
            `ID: ${p.id}, Name: ${p.name}, State: ${p.state}, Priority: ${p.priority}`
          );
          
          return {
            ...prev,
            output: [...prev.output, 
              { type: 'system', text: 'Process List:' },
              ...processOutput.map(text => ({ type: 'output', text }))
            ]
          };
        });
        break;
        
      case 'kill':
        const processId = parseInt(args[0]);
        setState(prev => {
          const processIndex = prev.processes.findIndex(p => p.id === processId);
          
          if (processIndex === -1) {
            return {
              ...prev,
              output: [...prev.output, { type: 'error', text: `Process with ID ${processId} not found` }]
            };
          }
          
          const newProcesses = [...prev.processes];
          newProcesses.splice(processIndex, 1);
          
          return {
            ...prev,
            processes: newProcesses,
            output: [...prev.output, { type: 'system', text: `Process with ID ${processId} terminated` }],
            score: prev.score + 5
          };
        });
        break;
      
      default:
        setState(prev => ({
          ...prev,
          output: [...prev.output, { type: 'error', text: `Unknown command: ${action}` }]
        }));
    }
  };

  // Simulate memory management
  const simulateMemory = (command) => {
    const [action, ...args] = command.split(' ');
    
    switch (action) {
      case 'allocate':
        setState(prev => {
          const size = parseInt(args[0]) || 1024;
          const process = args[1] || 'unknown';
          
          const newMemory = [...prev.memory, {
            id: Date.now(),
            size,
            process,
            allocated: true
          }];
          
          return {
            ...prev,
            memory: newMemory,
            output: [...prev.output, 
              { type: 'system', text: `Allocated ${size} bytes to process ${process}` }
            ],
            score: prev.score + 10
          };
        });
        break;
        
      case 'free':
        const memoryId = parseInt(args[0]);
        setState(prev => {
          const memoryIndex = prev.memory.findIndex(m => m.id === memoryId);
          
          if (memoryIndex === -1) {
            return {
              ...prev,
              output: [...prev.output, { type: 'error', text: `Memory block with ID ${memoryId} not found` }]
            };
          }
          
          const newMemory = [...prev.memory];
          newMemory.splice(memoryIndex, 1);
          
          return {
            ...prev,
            memory: newMemory,
            output: [...prev.output, { type: 'system', text: `Memory block with ID ${memoryId} freed` }],
            score: prev.score + 5
          };
        });
        break;
        
      case 'status':
        setState(prev => {
          const memoryOutput = prev.memory.map(m => 
            `ID: ${m.id}, Size: ${m.size} bytes, Process: ${m.process}, Allocated: ${m.allocated}`
          );
          
          const totalAllocated = prev.memory.reduce((sum, m) => sum + m.size, 0);
          
          return {
            ...prev,
            output: [...prev.output, 
              { type: 'system', text: 'Memory Status:' },
              { type: 'output', text: `Total Allocated: ${totalAllocated} bytes` },
              ...memoryOutput.map(text => ({ type: 'output', text }))
            ]
          };
        });
        break;
      
      default:
        setState(prev => ({
          ...prev,
          output: [...prev.output, { type: 'error', text: `Unknown command: ${action}` }]
        }));
    }
  };

  // Simulate file system operations
  const simulateFileSystem = (command) => {
    const [action, ...args] = command.split(' ');
    
    switch (action) {
      case 'touch':
        setState(prev => {
          const fileName = args[0] || `file-${prev.files.length + 1}.txt`;
          
          const newFiles = [...prev.files, {
            id: Date.now(),
            name: fileName,
            type: 'file',
            size: 0,
            content: ''
          }];
          
          return {
            ...prev,
            files: newFiles,
            output: [...prev.output, 
              { type: 'system', text: `Created file: ${fileName}` }
            ],
            score: prev.score + 5
          };
        });
        break;
        
      case 'mkdir':
        setState(prev => {
          const dirName = args[0] || `dir-${prev.files.length + 1}`;
          
          const newFiles = [...prev.files, {
            id: Date.now(),
            name: dirName,
            type: 'directory',
            children: []
          }];
          
          return {
            ...prev,
            files: newFiles,
            output: [...prev.output, 
              { type: 'system', text: `Created directory: ${dirName}` }
            ],
            score: prev.score + 5
          };
        });
        break;
        
      case 'ls':
        setState(prev => {
          const fileOutput = prev.files.map(f => 
            `${f.type === 'directory' ? 'd' : '-'}rw-r--r-- 1 user user ${f.size || 0} ${new Date().toLocaleDateString()} ${f.name}`
          );
          
          return {
            ...prev,
            output: [...prev.output, 
              { type: 'system', text: 'File Listing:' },
              ...fileOutput.map(text => ({ type: 'output', text }))
            ]
          };
        });
        break;
        
      case 'rm':
        const fileName = args[0];
        setState(prev => {
          const fileIndex = prev.files.findIndex(f => f.name === fileName);
          
          if (fileIndex === -1) {
            return {
              ...prev,
              output: [...prev.output, { type: 'error', text: `File ${fileName} not found` }]
            };
          }
          
          const newFiles = [...prev.files];
          newFiles.splice(fileIndex, 1);
          
          return {
            ...prev,
            files: newFiles,
            output: [...prev.output, { type: 'system', text: `Removed: ${fileName}` }],
            score: prev.score + 5
          };
        });
        break;
      
      default:
        setState(prev => ({
          ...prev,
          output: [...prev.output, { type: 'error', text: `Unknown command: ${action}` }]
        }));
    }
  };

  // Handle command input
  const handleCommand = async (e) => {
    e.preventDefault();
    
    const command = state.input.trim().toLowerCase();
    if (!command) return;

    setState(prev => ({
      ...prev,
      output: [...prev.output, { type: 'input', text: command }],
      input: ''
    }));

    // Process commands based on topic
    switch (topic) {
      case 'process-management':
        simulateProcesses(command);
        break;
      
      case 'memory-management':
        simulateMemory(command);
        break;
      
      case 'file-systems':
        simulateFileSystem(command);
        break;
      
      case 'help':
        getAIHelp(command);
        break;
        
      default:
        setState(prev => ({
          ...prev,
          output: [...prev.output, { 
            type: 'system', 
            text: `Command processed: ${command}. This is a simulation for ${topic}.` 
          }]
        }));
    }

    // Save session after each command
    await saveSession();
    
    // Check if score is high enough to complete
    if (state.score >= 50) {
      onComplete?.();
    }
  };

  return (
    <div className="bg-black rounded-lg overflow-hidden">
      {/* System Monitor */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-900">
        <div className="bg-black/50 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <FaTerminal className="text-[#88cc14]" />
            <span className="text-white">Processes</span>
          </div>
          <div className="text-2xl font-bold text-[#88cc14]">
            {state.processes.length}
          </div>
        </div>

        <div className="bg-black/50 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <FaMemory className="text-[#88cc14]" />
            <span className="text-white">Memory</span>
          </div>
          <div className="text-2xl font-bold text-[#88cc14]">
            {state.memory.length}
          </div>
        </div>

        <div className="bg-black/50 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <FaFolder className="text-[#88cc14]" />
            <span className="text-white">Files</span>
          </div>
          <div className="text-2xl font-bold text-[#88cc14]">
            {state.files.length}
          </div>
        </div>

        <div className="bg-black/50 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <FaCog className="text-[#88cc14]" />
            <span className="text-white">Score</span>
          </div>
          <div className="text-2xl font-bold text-[#88cc14]">
            {state.score}
          </div>
        </div>
      </div>

      {/* Terminal */}
      <div className="p-4">
        <div className="h-64 overflow-y-auto mb-4 font-mono bg-black p-4 rounded-lg border border-gray-800">
          {state.output.map((line, i) => (
            <div
              key={i}
              className={`mb-2 ${
                line.type === 'input' ? 'text-[#88cc14]' :
                line.type === 'error' ? 'text-red-400' :
                line.type === 'success' ? 'text-green-400' :
                line.type === 'output' ? 'text-blue-300' :
                'text-gray-300'
              }`}
            >
              {line.type === 'input' ? '$ ' : ''}{line.text}
            </div>
          ))}
        </div>

        <form onSubmit={handleCommand} className="flex gap-2">
          <input
            type="text"
            value={state.input}
            onChange={(e) => setState(prev => ({ ...prev, input: e.target.value }))}
            className="flex-1 bg-gray-900 text-white px-4 py-2 rounded-lg border border-gray-800 focus:outline-none focus:border-[#88cc14]"
            placeholder="Enter command..."
          />
          <button
            type="submit"
            className="bg-[#88cc14] text-black px-6 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
          >
            Execute
          </button>
        </form>
      </div>

      {/* AI Assistant */}
      {aiResponse && (
        <div className="p-4 border-t border-gray-800">
          <div className="flex items-center gap-2 mb-2">
            <FaLock className="text-[#88cc14]" />
            <span className="text-white font-bold">AI Assistant</span>
          </div>
          <div className="bg-gray-900 p-4 rounded-lg text-gray-300">
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-[#88cc14] border-t-transparent"></div>
                <span>Thinking...</span>
              </div>
            ) : (
              aiResponse
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default OSSimulator;