import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaServer, FaUserSecret, FaRobot, FaExclamationTriangle, FaFlag, FaLock, FaUnlock, FaCheck, FaArrowRight, FaTerminal, FaSkull, FaShieldAlt } from 'react-icons/fa';
import LabTerminal from '../LabTerminal';
import GameAI from './GameAI';

const ServerBreachGame = ({ onBack }) => {
  const [phase, setPhase] = useState(0);
  const [showBriefing, setShowBriefing] = useState(true);
  const [completedCommands, setCompletedCommands] = useState([]);
  const [aiVisible, setAiVisible] = useState(true);
  const [currentCommand, setCurrentCommand] = useState(null);
  const [flags, setFlags] = useState([]);
  const [showHint, setShowHint] = useState(false);
  const [showFlagSuccess, setShowFlagSuccess] = useState(false);
  const [showPhaseComplete, setShowPhaseComplete] = useState(false);
  const [gameComplete, setGameComplete] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  const phases = [
    {
      id: 'recon',
      name: 'Reconnaissance',
      description: 'Gather intelligence about the target system',
      flag: 'FLAG{SSH_MISCONFIGURED}',
      hint: 'Look for unusual service configurations and hidden ports. The flag is related to SSH.',
      commands: [
        {
          command: "nmap -sV *************",
          description: "Scan target for open ports and services",
          expectedOutput: `Starting Nmap 7.94 ( https://nmap.org )
Scanning *************
PORT     STATE  SERVICE        VERSION
22/tcp   open   ssh           OpenSSH 8.2p1
80/tcp   open   http          nginx 1.18.0
443/tcp  open   https         nginx 1.18.0
3306/tcp open   mysql         MySQL 8.0.28
8080/tcp open   http-proxy    [FILTERED]

Service detection performed. Please wait...
[!] Interesting: Non-standard SSH configuration detected
[!] Port 8080 appears to be running custom software`,
          hint: `Try these Nmap commands:
• nmap -sV *************    (Service version detection)
• nmap -p- *************    (All ports scan)
• nmap -A *************     (Aggressive scan)

The SSH service on port 22 looks misconfigured - this could be our way in.
The custom software on port 8080 might be related to the AI system.`
        },
        {
          command: "nmap -p- *************",
          description: "Scan all ports on target",
          expectedOutput: `Starting Nmap 7.94 ( https://nmap.org )
Scanning *************
PORT     STATE  SERVICE
22/tcp   open   ssh
80/tcp   open   http
443/tcp  open   https
3306/tcp open   mysql
8080/tcp open   http-proxy
9001/tcp open   unknown

[!] Hidden service detected on port 9001
[!] Found flag: FLAG{SSH_MISCONFIGURED}`,
          hint: 'The "-p-" flag scans all 65535 ports. Look for unusual or non-standard ports that might be running custom services.'
        }
      ]
    },
    {
      id: 'initial-access',
      name: 'Initial Access',
      description: 'Exploit identified vulnerabilities to gain access',
      flag: 'FLAG{WEAK_SSH_CREDS}',
      hint: 'The SSH service has a known vulnerability. Try common credentials.',
      commands: [
        {
          command: "ssh admin@*************",
          description: "Attempt SSH connection as admin",
          expectedOutput: `SSH connection attempt...
Permission denied (publickey,password).
[!] Note: Server allows password authentication despite config
[!] Default credentials might work`,
          hint: "The SSH server is misconfigured to allow password authentication. Try common credentials."
        },
        {
          command: "hydra -l admin -P /usr/share/wordlists/common.txt ssh://*************",
          description: "Brute force SSH password",
          expectedOutput: `Hydra v9.1 (c) 2020 by van Hauser/THC & David Maciejak
Hydra (https://github.com/vanhauser-thc/thc-hydra)
[DATA] max 16 tasks per 1 server
[DATA] attacking ssh://*************:22/
[22][ssh] host: *************   login: admin   password: admin123
[STATUS] attack finished for ************* (valid pair found)

[!] Found flag: FLAG{WEAK_SSH_CREDS}`,
          hint: "Hydra is a fast network logon cracker that supports many protocols. The -l flag specifies the username and -P specifies the password list."
        }
      ]
    },
    {
      id: 'privilege-escalation',
      name: 'Privilege Escalation',
      description: 'Escalate privileges to gain administrative access',
      flag: 'FLAG{SUDO_MISCONFIGURED}',
      hint: 'Check for misconfigured sudo permissions and SUID binaries.',
      commands: [
        {
          command: "sudo -l",
          description: "List sudo privileges",
          expectedOutput: `Matching Defaults entries for admin on cybercorp:
    env_reset, mail_badpass,
    secure_path=/usr/local/sbin\\:/usr/local/bin\\:/usr/sbin\\:/usr/bin\\:/sbin\\:/bin\\:/snap/bin

User admin may run the following commands on cybercorp:
    (ALL : ALL) NOPASSWD: /usr/bin/find
    (ALL : ALL) NOPASSWD: /usr/bin/python3
    (ALL : ALL) NOPASSWD: /usr/bin/env

[!] Unusual sudo permissions detected`,
          hint: "The sudo -l command shows which commands you can run with sudo. Look for commands that can be used for privilege escalation."
        },
        {
          command: "sudo find . -exec /bin/sh \\;",
          description: "Exploit sudo find permission",
          expectedOutput: `[+] Shell spawned with elevated privileges
[+] Current user: root
[+] Current privileges: ALL

[!] Found flag: FLAG{SUDO_MISCONFIGURED}`,
          hint: "The find command's -exec option can be used to execute commands. Since we can run find with sudo, we can use it to spawn a root shell."
        }
      ]
    },
    {
      id: 'ai-shutdown',
      name: 'AI Shutdown',
      description: 'Locate and terminate the rogue AI process',
      flag: 'FLAG{AI_TERMINATED}',
      hint: 'The AI process is running with a specific signature. Look for unusual system processes.',
      commands: [
        {
          command: "ps aux | grep AI",
          description: "Search for AI processes",
          expectedOutput: `root      1234  99.9  5.0 1234567 89012 ?   R    10:00   0:00 [ROGUE_AI]
root      1235   0.0  0.0   4567   890 pts/0    S+   10:00   0:00 grep AI

[!] High CPU usage detected from ROGUE_AI process`,
          hint: "The ps command shows running processes. Look for suspicious processes with high CPU usage."
        },
        {
          command: "netstat -tuln",
          description: "Check network connections",
          expectedOutput: `Active Internet connections (only servers)
Proto Recv-Q Send-Q Local Address           Foreign Address         State
tcp        0      0 0.0.0.0:22              0.0.0.0:*               LISTEN
tcp        0      0 0.0.0.0:80              0.0.0.0:*               LISTEN
tcp        0      0 0.0.0.0:9001            0.0.0.0:*               LISTEN

[!] AI control port detected on 9001`,
          hint: "The netstat command shows network connections. Port 9001 was discovered earlier during reconnaissance."
        },
        {
          command: "kill -9 1234",
          description: "Terminate the AI process",
          expectedOutput: `[!] WARNING: AI defensive measures activated
[*] Attempting to terminate process...
[+] Process successfully terminated
[+] AI system shutting down...
[+] Control restored to authorized personnel

[!] Found flag: FLAG{AI_TERMINATED}

Mission Complete! All systems secured.`,
          hint: "The kill -9 command forcefully terminates a process. Use the PID found from the ps command."
        }
      ]
    }
  ];

  const handleCommandComplete = (command) => {
    setCurrentCommand(command);
    
    // Check for flags in command output
    const flagMatch = command.expectedOutput.match(/FLAG{[^}]+}/);
    if (flagMatch && !flags.includes(flagMatch[0])) {
      const newFlag = flagMatch[0];
      
      // Add the flag to the collection
      setFlags(prevFlags => [...prevFlags, newFlag]);
      
      // Show flag capture success message
      setShowFlagSuccess(true);
      
      // Add command to completed commands
      setCompletedCommands(prevCompleted => [...prevCompleted, command]);
      
      // Check if this was the last command in the phase
      const currentPhaseCommands = phases[phase].commands;
      const isLastCommand = currentPhaseCommands.indexOf(command) === currentPhaseCommands.length - 1;
      
      if (isLastCommand) {
        // Wait for flag success message to show before showing phase complete
        setTimeout(() => {
          setShowFlagSuccess(false);
          setShowPhaseComplete(true);
          
          // Move to next phase after showing phase complete message
          setTimeout(() => {
            setShowPhaseComplete(false);
            if (phase < phases.length - 1) {
              setPhase(phase + 1);
              setCompletedCommands([]);
              setCurrentCommand(null);
            } else {
              setGameComplete(true);
            }
          }, 3000);
        }, 2000);
      } else {
        // If not the last command, just hide the flag success message after a delay
        setTimeout(() => {
          setShowFlagSuccess(false);
        }, 2000);
      }
    } else {
      // If no flag found, just add to completed commands
      setCompletedCommands(prevCompleted => [...prevCompleted, command]);
    }
  };

  const PhaseTracker = () => (
    <div className="bg-black/50 backdrop-blur-sm sticky top-0 z-10 p-4">
      <div className="flex items-center gap-4 max-w-4xl mx-auto">
        {phases.map((p, index) => (
          <div
            key={p.id}
            className={`flex-1 ${index === phase ? 'text-[#88cc14]' : index < phase ? 'text-gray-400' : 'text-gray-600'}`}
          >
            <div className="flex items-center gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                index === phase ? 'bg-[#88cc14]/20 text-[#88cc14]' :
                index < phase ? 'bg-gray-800 text-gray-400' :
                'bg-gray-800/50 text-gray-600'
              }`}>
                {index + 1}
              </div>
              <span className="font-medium hidden sm:inline">{p.name}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const FlagDisplay = () => (
    <div className="bg-black rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <FaFlag className="text-[#88cc14]" />
          <h3 className="text-white font-bold">Captured Flags</h3>
        </div>
        <button
          onClick={() => setShowHint(!showHint)}
          className="text-gray-400 hover:text-[#88cc14] transition-colors"
        >
          {showHint ? <FaUnlock /> : <FaLock />}
        </button>
      </div>
      {flags.length > 0 ? (
        <div className="space-y-2">
          {flags.map((flag, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-[#88cc14]/10 text-[#88cc14] p-2 rounded font-mono"
            >
              {flag}
            </motion.div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500">No flags captured yet</p>
      )}
      {showHint && phases[phase].hint && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-4 bg-yellow-500/10 border border-yellow-500/20 rounded p-3"
        >
          <div className="flex items-center gap-2 text-yellow-500 mb-2">
            <FaExclamationTriangle />
            <span className="font-bold">Flag Hint</span>
          </div>
          <p className="text-gray-300">{phases[phase].hint}</p>
        </motion.div>
      )}
    </div>
  );

  const MissionBriefing = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-black rounded-lg p-6 mb-6"
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
            <FaUserSecret className="text-[#88cc14] text-2xl" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">Operation: Shadow Breach</h3>
            <p className="text-gray-400">Top Secret Clearance Required</p>
          </div>
        </div>
        <button
          onClick={() => setShowBriefing(false)}
          className="bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
        >
          Accept Mission
        </button>
      </div>
      
      <div className="prose prose-invert max-w-none">
        <pre className="text-gray-300 font-mono text-sm whitespace-pre-wrap">
          {`Mission Briefing: A rogue AI has taken control of CyberCorp's main server infrastructure. Your mission is to infiltrate the system, bypass security measures, and regain control before critical data is compromised.

Target: CyberCorp Main Server (*************)
Security Level: High
Mission Status: Critical

Objectives:
1. Perform reconnaissance
2. Identify vulnerabilities
3. Gain initial access
4. Escalate privileges
5. Locate and stop the rogue AI

Each phase has a hidden flag to capture. Use the available tools and watch for flag patterns: FLAG{SOME_TEXT}

Remember: Stealth is crucial. Excessive failed attempts will trigger enhanced security measures.`}
        </pre>
      </div>
    </motion.div>
  );

  const FlagCaptureSuccess = () => (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none"
    >
      <div className="bg-[#88cc14]/90 text-black p-6 rounded-lg shadow-lg flex items-center gap-4">
        <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center">
          <FaFlag className="text-[#88cc14] text-2xl" />
        </div>
        <div>
          <h3 className="text-xl font-bold">Flag Captured!</h3>
          <p>Great work! You've found a hidden flag.</p>
        </div>
      </div>
    </motion.div>
  );

  const PhaseCompleteSuccess = () => (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none"
    >
      <div className="bg-black/90 text-white p-6 rounded-lg shadow-lg">
        <div className="text-center">
          <div className="w-16 h-16 rounded-full bg-[#88cc14] mx-auto flex items-center justify-center mb-4">
            <FaCheck className="text-black text-3xl" />
          </div>
          <h3 className="text-2xl font-bold mb-2">Phase Complete!</h3>
          {phase < phases.length - 1 ? (
            <>
              <p className="text-gray-400 mb-4">Moving to next phase...</p>
              <div className="flex items-center justify-center gap-2 text-[#88cc14]">
                <span>{phases[phase].name}</span>
                <FaArrowRight />
                <span>{phases[phase + 1].name}</span>
              </div>
            </>
          ) : (
            <p className="text-[#88cc14]">Mission Accomplished!</p>
          )}
        </div>
      </div>
    </motion.div>
  );

  const MissionComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="fixed inset-0 flex items-center justify-center z-50 bg-black/90"
    >
      <div className="bg-black p-8 rounded-lg border border-[#88cc14] max-w-2xl w-full mx-4">
        <div className="text-center">
          <div className="w-20 h-20 rounded-full bg-[#88cc14] mx-auto flex items-center justify-center mb-6">
            <FaShieldAlt className="text-black text-4xl" />
          </div>
          <h2 className="text-3xl font-bold text-white mb-4">Mission Accomplished!</h2>
          <p className="text-gray-400 mb-6">
            You've successfully infiltrated the system, gained control, and terminated the rogue AI.
            All systems have been restored to normal operation.
          </p>
          
          <div className="bg-gray-900 rounded-lg p-4 mb-6">
            <h3 className="text-[#88cc14] font-bold mb-2">Captured Flags:</h3>
            <div className="space-y-2">
              {flags.map((flag, index) => (
                <div key={index} className="font-mono text-white bg-[#88cc14]/10 p-2 rounded">
                  {flag}
                </div>
              ))}
            </div>
          </div>

          <button
            onClick={onBack}
            className="bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
          >
            Return to Missions
          </button>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <button
          onClick={onBack}
          className="text-gray-600 hover:text-gray-900"
        >
          ← Back to Missions
        </button>
        <button
          onClick={() => setAiVisible(!aiVisible)}
          className="bg-[#88cc14]/10 text-[#88cc14] p-2 rounded-lg hover:bg-[#88cc14]/20 transition-colors"
        >
          <FaRobot className="text-xl" />
        </button>
      </div>

      {showBriefing ? (
        <MissionBriefing />
      ) : (
        <>
          <PhaseTracker />
          <FlagDisplay />
          {aiVisible && !isMobile && (
            <GameAI 
              phase={phases[phase].name}
              command={currentCommand?.command}
            />
          )}

          <div className="bg-black rounded-lg overflow-hidden">
            <div className="p-4 border-b border-gray-800">
              <h2 className="text-xl font-bold text-white flex items-center gap-2">
                <FaServer />
                {phases[phase].name}
              </h2>
              <p className="text-gray-400 mt-1">{phases[phase].description}</p>
            </div>
            <LabTerminal 
              commands={phases[phase].commands}
              onCommandComplete={handleCommandComplete}
            />
          </div>
        </>
      )}

      <AnimatePresence>
        {showFlagSuccess && <FlagCaptureSuccess />}
        {showPhaseComplete && <PhaseCompleteSuccess />}
        {gameComplete && <MissionComplete />}
      </AnimatePresence>
    </div>
  );
};

export default ServerBreachGame;