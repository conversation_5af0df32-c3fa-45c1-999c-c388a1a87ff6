import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaRobot, Fa<PERSON><PERSON>bulb, FaExclamationTriangle, FaTerminal } from 'react-icons/fa';

const GameAI = ({ phase, command, onHint }) => {
  const [analysis, setAnalysis] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // AI responses for different commands
  const aiResponses = {
    'nmap -sV': {
      analysis: [
        "I've detected several open ports and services:",
        "• SSH (22) - OpenSSH 8.2p1 with non-standard configuration",
        "• Web servers (80, 443) - nginx 1.18.0",
        "• MySQL (3306) - Version 8.0.28",
        "• Custom service (8080) - Possibly related to target system",
        "\nThe SSH misconfiguration could be our entry point."
      ],
      hint: "Try scanning for all ports with 'nmap -p-' to find any hidden services."
    },
    'nmap -p-': {
      analysis: [
        "Full port scan reveals additional services:",
        "• All previously discovered ports confirmed",
        "• New port discovered: 9001 (unknown service)",
        "\nThe unknown service on port 9001 could be significant."
      ],
      hint: "Use 'nmap -A' for deeper analysis of the unknown service."
    },
    'nmap -A': {
      analysis: [
        "Aggressive scan results show:",
        "• Linux 5.4.0-generic OS detected",
        "• SSH authentication methods exposed",
        "• SSL certificate reveals domain: cybercorp.local",
        "• Critical: SSH vulnerability CVE-2020-14145 detected",
        "\nWe can potentially exploit the SSH vulnerability."
      ],
      hint: "Research CVE-2020-14145 for potential exploitation methods."
    },
    'ssh admin': {
      analysis: [
        "SSH connection attempt failed, but revealed important information:",
        "• Server allows password authentication despite config",
        "• This suggests weak security practices",
        "• Default credentials might work",
        "\nWe should try brute forcing with common credentials."
      ],
      hint: "Use Hydra to attempt brute forcing with common credentials."
    },
    'hydra': {
      analysis: [
        "Brute force attack successful!",
        "• Credentials found: admin:admin123",
        "• This is a common default credential pair",
        "• The server should have had these credentials changed",
        "\nWe now have initial access to the system."
      ],
      hint: "Now that we have access, check what privileges we have with 'sudo -l'."
    },
    'sudo -l': {
      analysis: [
        "Privilege check reveals dangerous permissions:",
        "• User can run several commands with sudo WITHOUT password",
        "• Critical: /usr/bin/find has known privilege escalation vectors",
        "• This is a serious misconfiguration",
        "\nWe can use these permissions to escalate to root."
      ],
      hint: "The 'find' command can be used to execute arbitrary commands with '-exec'."
    },
    'sudo find': {
      analysis: [
        "Privilege escalation successful!",
        "• We now have root access",
        "• This gives us complete control of the system",
        "• We can now hunt for the rogue AI process",
        "\nNext step is to locate the AI process."
      ],
      hint: "Use 'ps aux | grep AI' to search for suspicious processes."
    },
    'ps aux': {
      analysis: [
        "Process analysis reveals:",
        "• Suspicious process: [ROGUE_AI]",
        "• Extremely high CPU usage (99.9%)",
        "• Running as root user",
        "• Process ID: 1234",
        "\nThis is likely our target process."
      ],
      hint: "Check network connections with 'netstat -tuln' to see what ports this process might be using."
    },
    'netstat': {
      analysis: [
        "Network analysis shows:",
        "• Port 9001 is listening for connections",
        "• This matches the hidden port we found earlier",
        "• This is likely the AI control port",
        "\nWe can now terminate the AI process."
      ],
      hint: "Use 'kill -9 1234' to forcefully terminate the process."
    },
    'kill': {
      analysis: [
        "AI termination successful!",
        "• Process has been killed",
        "• System control restored",
        "• Mission accomplished",
        "\nAll objectives completed successfully."
      ],
      hint: "Mission complete! Return to base."
    }
  };

  useEffect(() => {
    if (command) {
      setIsAnalyzing(true);
      // Simulate AI analysis
      setTimeout(() => {
        const baseCommand = Object.keys(aiResponses).find(cmd => command.includes(cmd));
        if (baseCommand) {
          setAnalysis(aiResponses[baseCommand].analysis.join('\n'));
        } else {
          setAnalysis("I don't have specific insights for this command. Try one of the suggested commands.");
        }
        setIsAnalyzing(false);
      }, 1000);
    }
  }, [command]);

  const getHint = () => {
    const baseCommand = Object.keys(aiResponses).find(cmd => command?.includes(cmd));
    return baseCommand ? aiResponses[baseCommand].hint : null;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-black rounded-lg p-4 mb-6"
    >
      <div className="flex items-center gap-3 mb-4">
        <div className="w-10 h-10 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
          <FaRobot className="text-[#88cc14] text-xl" />
        </div>
        <div>
          <h3 className="text-white font-bold">AI Analysis</h3>
          <p className="text-gray-400 text-sm">Phase: {phase}</p>
        </div>
      </div>

      {isAnalyzing ? (
        <div className="flex items-center gap-2 text-gray-400">
          <div className="animate-spin">
            <FaTerminal />
          </div>
          <span>Analyzing command output...</span>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Analysis Output */}
          <div className="font-mono text-sm whitespace-pre-wrap text-gray-300">
            {analysis}
          </div>

          {/* Hint Section */}
          {getHint() && (
            <div className="mt-4 bg-[#88cc14]/10 border border-[#88cc14]/20 rounded-lg p-3">
              <div className="flex items-center gap-2 text-[#88cc14] mb-2">
                <FaLightbulb />
                <span className="font-bold">Suggestion</span>
              </div>
              <p className="text-gray-300">{getHint()}</p>
            </div>
          )}

          {/* Warning/Tips */}
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
            <div className="flex items-center gap-2 text-red-400 mb-2">
              <FaExclamationTriangle />
              <span className="font-bold">Security Alert</span>
            </div>
            <p className="text-gray-300">
              Remember to document all findings. Each discovered service and vulnerability could be crucial for later phases.
            </p>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default GameAI;