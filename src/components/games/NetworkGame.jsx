import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaNetworkWired, FaServer, FaDesktop, FaExclamationTriangle } from 'react-icons/fa';

const NetworkGame = ({ onComplete }) => {
  const [nodes, setNodes] = useState([]);
  const [packets, setPackets] = useState([]);
  const [score, setScore] = useState(0);
  const [gameState, setGameState] = useState('ready');

  useEffect(() => {
    // Initialize network nodes
    setNodes([
      { id: 1, type: 'server', x: 400, y: 100 },
      { id: 2, type: 'client', x: 200, y: 300 },
      { id: 3, type: 'client', x: 600, y: 300 }
    ]);
  }, []);

  const sendPacket = (from, to) => {
    const newPacket = {
      id: Date.now(),
      from,
      to,
      x: nodes[from - 1].x,
      y: nodes[from - 1].y,
      delivered: false
    };

    setPackets(prev => [...prev, newPacket]);
    setScore(prev => prev + 10);

    if (score >= 100) {
      setGameState('complete');
      onComplete?.();
    }
  };

  useEffect(() => {
    // Packet animation loop
    const interval = setInterval(() => {
      setPackets(prev => prev.map(packet => {
        if (packet.delivered) return packet;

        const toNode = nodes[packet.to - 1];
        const dx = toNode.x - packet.x;
        const dy = toNode.y - packet.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 5) {
          return { ...packet, delivered: true };
        }

        const speed = 5;
        const vx = (dx / distance) * speed;
        const vy = (dy / distance) * speed;

        return {
          ...packet,
          x: packet.x + vx,
          y: packet.y + vy
        };
      }).filter(packet => !packet.delivered || packet.delivered));
    }, 1000 / 60);

    return () => clearInterval(interval);
  }, [nodes]);

  return (
    <div className="bg-black rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <FaNetworkWired className="text-[#88cc14]" />
            Network Simulator
          </h3>
          <div className="text-[#88cc14] font-bold">
            Score: {score}
          </div>
        </div>
      </div>

      <div className="relative h-[500px] bg-gray-900">
        {/* Network Nodes */}
        {nodes.map(node => (
          <motion.div
            key={node.id}
            className="absolute"
            style={{ left: node.x - 25, top: node.y - 25 }}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
          >
            <div 
              className="w-12 h-12 rounded-lg bg-[#88cc14]/20 border-2 border-[#88cc14] flex items-center justify-center cursor-pointer hover:bg-[#88cc14]/30 transition-colors"
              onClick={() => {
                const otherNodes = nodes.filter(n => n.id !== node.id);
                const randomNode = otherNodes[Math.floor(Math.random() * otherNodes.length)];
                sendPacket(node.id, randomNode.id);
              }}
            >
              {node.type === 'server' ? (
                <FaServer className="text-[#88cc14] text-xl" />
              ) : (
                <FaDesktop className="text-[#88cc14] text-xl" />
              )}
            </div>
          </motion.div>
        ))}

        {/* Network Packets */}
        {packets.map(packet => (
          <motion.div
            key={packet.id}
            className="absolute w-3 h-3 bg-[#88cc14] rounded-full"
            style={{ left: packet.x - 6, top: packet.y - 6 }}
          />
        ))}

        {/* Game Complete */}
        {gameState === 'complete' && (
          <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-[#88cc14] mb-4">
                Challenge Complete!
              </h3>
              <p className="text-gray-400 mb-6">
                You've mastered network communication!
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
              >
                Play Again
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="p-4 bg-gray-900 border-t border-gray-800">
        <div className="flex items-center gap-2 mb-2">
          <FaExclamationTriangle className="text-[#88cc14]" />
          <span className="text-white font-bold">How to Play</span>
        </div>
        <p className="text-gray-400">
          Click on network nodes to send packets between them. Create successful network communications to score points.
          Score 100 points to complete the challenge!
        </p>
      </div>
    </div>
  );
};

export default NetworkGame;