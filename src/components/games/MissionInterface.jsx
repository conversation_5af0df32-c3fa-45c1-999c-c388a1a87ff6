import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaChevronLeft, FaLightbulb, FaVolumeUp, FaVolumeMute, FaExpand, FaCompress, FaTerminal, FaCheck, FaServer, FaUserSecret } from 'react-icons/fa';
import * as Tone from 'tone';
import Terminal from './Terminal';

const MissionInterface = ({ mission, onComplete, onBack }) => {
  const [currentTask, setCurrentTask] = useState(0);
  const [score, setScore] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [synth] = useState(new Tone.Synth().toDestination());
  const [completedTasks, setCompletedTasks] = useState(new Set());

  const containerRef = useRef(null);

  // Early return if no mission is provided
  if (!mission || !mission.tasks || !mission.tasks.length) {
    return (
      <div className="bg-black rounded-lg p-6 text-white">
        <h3 className="text-xl font-bold mb-4">Mission Not Available</h3>
        <p className="text-gray-400">This mission is currently unavailable or being updated.</p>
      </div>
    );
  }

  const handleCommand = (command) => {
    if (!mission.tasks[currentTask]) return;

    const task = mission.tasks[currentTask];
    const isCorrect = command === task.command;

    if (isCorrect) {
      if (!isMuted) {
        synth.triggerAttackRelease("C4", "8n");
      }
      
      setScore(prev => prev + task.points);
      setCompletedTasks(prev => new Set([...prev, currentTask]));
      
      if (currentTask < mission.tasks.length - 1) {
        setTimeout(() => setCurrentTask(prev => prev + 1), 1500);
      } else {
        if (!isMuted) {
          synth.triggerAttackRelease("G4", "8n");
        }
        setTimeout(() => onComplete?.(), 2000);
      }
    } else {
      if (!isMuted) {
        synth.triggerAttackRelease("E3", "8n");
      }
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  return (
    <div 
      ref={containerRef}
      className="min-h-screen bg-cyber-black text-white relative"
    >
      {/* Mission Header */}
      <div className="bg-black/50 backdrop-blur-sm border-b border-[#88cc14]/20 p-4 relative z-10">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="text-[#88cc14] hover:text-white transition-colors"
            >
              <FaChevronLeft />
            </button>
            <div>
              <h2 className="text-xl font-bold flex items-center gap-2">
                <FaUserSecret className="text-[#88cc14]" />
                {mission.title}
              </h2>
              <p className="text-sm text-gray-400">
                Task {currentTask + 1} of {mission.tasks.length}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="text-[#88cc14] font-bold">
              Score: {score}
            </div>
            <button
              onClick={() => setIsMuted(!isMuted)}
              className="text-[#88cc14] hover:text-white transition-colors"
            >
              {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
            </button>
            <button
              onClick={toggleFullscreen}
              className="text-[#88cc14] hover:text-white transition-colors"
            >
              {isFullscreen ? <FaCompress /> : <FaExpand />}
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-4 relative z-10">
        {/* Progress Indicators */}
        <div className="flex items-center gap-2 mb-4">
          {mission.tasks.map((_, index) => (
            <div
              key={index}
              className={`w-3 h-3 rounded-full transition-colors ${
                completedTasks.has(index)
                  ? 'bg-[#88cc14] shadow-[0_0_10px_rgba(136,204,20,0.5)]'
                  : index === currentTask
                  ? 'bg-[#88cc14] animate-pulse shadow-[0_0_10px_rgba(136,204,20,0.5)]'
                  : 'bg-gray-800'
              }`}
            />
          ))}
        </div>

        <div className="grid grid-cols-12 gap-6">
          {/* Mission Info */}
          <div className="col-span-3">
            <div className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-4">
              <h3 className="text-lg font-bold text-[#88cc14] mb-4">Mission Intel</h3>
              <div className="space-y-4">
                <div>
                  <label className="text-gray-400 text-sm">Target</label>
                  <p className="font-mono text-white">192.168.1.100</p>
                </div>
                <div>
                  <label className="text-gray-400 text-sm">Access Level</label>
                  <p className="font-mono text-white">Restricted</p>
                </div>
                <div>
                  <label className="text-gray-400 text-sm">Security Level</label>
                  <p className="font-mono text-[#ff0000]">High</p>
                </div>
              </div>
            </div>
          </div>

          {/* Terminal Section */}
          <div className="col-span-6">
            {/* Current Objective */}
            <div className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-4 mb-4">
              <div className="flex items-center gap-2 mb-2">
                <FaTerminal className="text-[#88cc14]" />
                <h3 className="text-lg font-bold">Current Objective</h3>
              </div>
              <p className="text-gray-300">
                {mission.tasks[currentTask].description}
              </p>
            </div>

            {/* Terminal */}
            <div className="bg-black rounded-lg overflow-hidden border border-[#88cc14]/20">
              <Terminal
                onCommand={handleCommand}
                theme="matrix"
                initialText={`
╔══════════════════════════════════════════╗
║     XCerberus Hacking Terminal v2.0      ║
╚══════════════════════════════════════════╝

Mission: ${mission.title}
Objective: ${mission.tasks[currentTask].description}

Type your commands below:
`}
              />
            </div>
          </div>

          {/* Mission Progress */}
          <div className="col-span-3">
            <div className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-4">
              <h3 className="text-lg font-bold text-[#88cc14] mb-4">Mission Progress</h3>
              
              {/* Hint Section */}
              <div className="mb-4">
                <button
                  onClick={() => setShowHint(!showHint)}
                  className="flex items-center gap-2 text-[#88cc14] hover:text-white transition-colors"
                >
                  <FaLightbulb />
                  <span>{showHint ? 'Hide Hint' : 'Show Hint'}</span>
                </button>

                <AnimatePresence>
                  {showHint && mission.tasks[currentTask].hint && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="mt-4 bg-black/30 rounded-lg p-4 border border-[#88cc14]/20"
                    >
                      <p className="text-gray-300">{mission.tasks[currentTask].hint}</p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Task List */}
              <div className="space-y-2">
                {mission.tasks.map((task, index) => (
                  <div
                    key={index}
                    className={`p-2 rounded ${
                      completedTasks.has(index)
                        ? 'bg-[#88cc14]/10 text-[#88cc14]'
                        : index === currentTask
                        ? 'bg-[#88cc14]/5 text-white'
                        : 'text-gray-500'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {completedTasks.has(index) ? (
                        <FaCheck className="text-[#88cc14]" />
                      ) : (
                        <div className={`w-2 h-2 rounded-full ${
                          index === currentTask ? 'bg-[#88cc14]' : 'bg-gray-600'
                        }`} />
                      )}
                      <span className="text-sm">Task {index + 1}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MissionInterface;