import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>a<PERSON><PERSON>, <PERSON>aU<PERSON>lock, FaShieldAlt, FaExclamationTriangle } from 'react-icons/fa';

const SecurityGame = ({ onComplete }) => {
  const [permissions, setPermissions] = useState({
    read: false,
    write: false,
    execute: false
  });
  const [score, setScore] = useState(0);
  const [gameState, setGameState] = useState('ready');
  const [currentChallenge, setCurrentChallenge] = useState(0);

  const challenges = [
    {
      description: "Set permissions to allow reading but prevent writing and execution",
      solution: { read: true, write: false, execute: false }
    },
    {
      description: "Configure permissions for a script file that needs to be executed",
      solution: { read: true, write: false, execute: true }
    },
    {
      description: "Set full permissions for an administrator",
      solution: { read: true, write: true, execute: true }
    }
  ];

  const togglePermission = (permission) => {
    setPermissions(prev => ({
      ...prev,
      [permission]: !prev[permission]
    }));
  };

  const checkSolution = () => {
    const challenge = challenges[currentChallenge];
    const isCorrect = Object.entries(challenge.solution).every(
      ([key, value]) => permissions[key] === value
    );

    if (isCorrect) {
      setScore(prev => prev + 10);
      if (currentChallenge < challenges.length - 1) {
        setCurrentChallenge(prev => prev + 1);
        setPermissions({ read: false, write: false, execute: false });
      } else {
        setGameState('complete');
        onComplete?.();
      }
    }
  };

  return (
    <div className="bg-black rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <FaShieldAlt className="text-[#88cc14]" />
            Security Manager
          </h3>
          <div className="text-[#88cc14] font-bold">
            Score: {score}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Current Challenge */}
        <div className="bg-gray-900 p-4 rounded-lg mb-6">
          <h4 className="text-white font-bold mb-2">Challenge {currentChallenge + 1}</h4>
          <p className="text-gray-400">
            {challenges[currentChallenge].description}
          </p>
        </div>

        {/* Permission Controls */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          {Object.entries(permissions).map(([permission, enabled]) => (
            <motion.button
              key={permission}
              onClick={() => togglePermission(permission)}
              className={`p-4 rounded-lg flex flex-col items-center gap-2 transition-colors ${
                enabled
                  ? 'bg-[#88cc14] text-black'
                  : 'bg-gray-800 text-gray-400'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {enabled ? <FaUnlock className="text-2xl" /> : <FaLock className="text-2xl" />}
              <span className="font-bold capitalize">{permission}</span>
            </motion.button>
          ))}
        </div>

        {/* Submit Button */}
        <button
          onClick={checkSolution}
          className="w-full bg-[#88cc14] text-black py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
        >
          Check Solution
        </button>

        {/* Game Complete */}
        {gameState === 'complete' && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-[#88cc14] mb-4">
                Challenge Complete!
              </h3>
              <p className="text-gray-400 mb-6">
                You've mastered file permissions!
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
              >
                Play Again
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="p-4 bg-gray-900 border-t border-gray-800">
        <div className="flex items-center gap-2 mb-2">
          <FaExclamationTriangle className="text-[#88cc14]" />
          <span className="text-white font-bold">How to Play</span>
        </div>
        <p className="text-gray-400">
          Toggle permissions to match the required security configuration for each challenge.
          Complete all challenges to win!
        </p>
      </div>
    </div>
  );
};

export default SecurityGame;