import React, { useEffect, useRef } from 'react';
import * as PIXI from 'pixi.js';
import { Howl } from 'howler';
import { motion } from 'framer-motion';
import * as Matter from 'matter-js';
import * as Tone from 'tone';

// Sound effects
const sounds = {
  success: new Howl({ src: ['/sounds/success.mp3'] }),
  error: new Howl({ src: ['/sounds/error.mp3'] }),
  click: new Howl({ src: ['/sounds/click.mp3'] }),
  complete: new Howl({ src: ['/sounds/complete.mp3'] }),
  background: new Howl({
    src: ['/sounds/background.mp3'],
    loop: true,
    volume: 0.3
  })
};

// Synth for interactive feedback
const synth = new Tone.Synth().toDestination();

const GameEngine = ({ width = 800, height = 600, onInit }) => {
  const canvasRef = useRef(null);
  const appRef = useRef(null);
  const engineRef = useRef(null);

  useEffect(() => {
    // Initialize PIXI Application
    const app = new PIXI.Application({
      width,
      height,
      backgroundColor: 0x000000,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
    });

    // Initialize Matter.js
    const engine = Matter.Engine.create();
    const world = engine.world;

    // Add to refs
    canvasRef.current.appendChild(app.view);
    appRef.current = app;
    engineRef.current = engine;

    // Start background music
    sounds.background.play();

    // Initialize game
    if (onInit) {
      onInit(app, engine, sounds, synth);
    }

    // Game loop
    let lastTime = 0;
    const gameLoop = (timestamp) => {
      const deltaTime = timestamp - lastTime;
      lastTime = timestamp;

      // Update physics
      Matter.Engine.update(engine, deltaTime);

      // Request next frame
      requestAnimationFrame(gameLoop);
    };

    requestAnimationFrame(gameLoop);

    return () => {
      // Cleanup
      app.destroy(true);
      Matter.Engine.clear(engine);
      sounds.background.stop();
    };
  }, [width, height, onInit]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="game-container relative"
      ref={canvasRef}
    >
      {/* Game overlay elements can be added here */}
      <div className="absolute top-0 left-0 right-0 pointer-events-none">
        <div className="scanline"></div>
      </div>
    </motion.div>
  );
};

export default GameEngine;