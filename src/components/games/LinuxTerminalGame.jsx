import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTerminal, FaLightbulb, FaTrophy, FaCheck, FaGamepad } from 'react-icons/fa';
import { Howl } from 'howler';
import * as Tone from 'tone';

// Sound effects
const sounds = {
  success: new Howl({ src: ['https://assets.mixkit.co/active_storage/sfx/2000/2000-preview.mp3'] }),
  error: new Howl({ src: ['https://assets.mixkit.co/active_storage/sfx/2003/2003-preview.mp3'] }),
  type: new Howl({ src: ['https://assets.mixkit.co/active_storage/sfx/2004/2004-preview.mp3'], volume: 0.2 }),
  complete: new Howl({ src: ['https://assets.mixkit.co/active_storage/sfx/2005/2005-preview.mp3'] }),
  background: new Howl({
    src: ['https://assets.mixkit.co/active_storage/sfx/2006/2006-preview.mp3'],
    loop: true,
    volume: 0.3
  })
};

// Synth for interactive feedback
const synth = new Tone.Synth().toDestination();

const LinuxTerminalGame = ({ mission, onComplete }) => {
  const [input, setInput] = useState('');
  const [history, setHistory] = useState([]);
  const [currentTask, setCurrentTask] = useState(0);
  const [score, setScore] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [particles, setParticles] = useState([]);

  // Early return if no mission is provided
  if (!mission || !mission.tasks || !mission.tasks.length) {
    return (
      <div className="bg-black rounded-lg p-6 text-white">
        <h3 className="text-xl font-bold mb-4">Mission Not Available</h3>
        <p className="text-gray-400">This mission is currently unavailable or being updated.</p>
      </div>
    );
  }

  useEffect(() => {
    // Start background music
    sounds.background.play();
    return () => sounds.background.stop();
  }, []);

  // Particle effect on success
  const createParticles = () => {
    const newParticles = Array.from({ length: 20 }, () => ({
      id: Math.random(),
      x: Math.random() * window.innerWidth,
      y: window.innerHeight,
      vx: (Math.random() - 0.5) * 10,
      vy: -Math.random() * 15,
      life: 1
    }));
    setParticles(prev => [...prev, ...newParticles]);
  };

  useEffect(() => {
    // Animate particles
    const interval = setInterval(() => {
      setParticles(prev => 
        prev
          .map(p => ({
            ...p,
            x: p.x + p.vx,
            y: p.y + p.vy,
            vy: p.vy + 0.5,
            life: p.life - 0.02
          }))
          .filter(p => p.life > 0)
      );
    }, 1000 / 60);

    return () => clearInterval(interval);
  }, []);

  const handleCommand = (e) => {
    e.preventDefault();
    if (!input.trim()) return;

    const task = mission.tasks[currentTask];
    const isCorrect = input.trim() === task.command;

    sounds.type.play();

    setHistory(prev => [...prev, {
      command: input,
      output: isCorrect ? 'Command successful! Mission objective completed.' : 'Incorrect command. Try again.',
      correct: isCorrect
    }]);

    if (isCorrect) {
      sounds.success.play();
      synth.triggerAttackRelease("C4", "8n");
      createParticles();
      setScore(prev => prev + task.points);
      
      if (currentTask < mission.tasks.length - 1) {
        setCurrentTask(prev => prev + 1);
      } else {
        sounds.complete.play();
        onComplete?.();
      }
    } else {
      sounds.error.play();
      synth.triggerAttackRelease("G3", "8n");
    }

    setInput('');
  };

  return (
    <div className="bg-black rounded-lg overflow-hidden relative">
      {/* Particle Effects */}
      <div className="absolute inset-0 pointer-events-none">
        <AnimatePresence>
          {particles.map(particle => (
            <motion.div
              key={particle.id}
              className="absolute w-2 h-2 bg-[#88cc14] rounded-full"
              style={{
                left: particle.x,
                top: particle.y,
                opacity: particle.life
              }}
              exit={{ opacity: 0 }}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Mission Header */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-white flex items-center gap-2">
              <FaTerminal className="text-[#88cc14]" />
              {mission.title}
            </h3>
            <p className="text-gray-400 text-sm">{mission.description}</p>
          </div>
          <div className="text-[#88cc14] font-bold">
            Score: {score}/{mission.points}
          </div>
        </div>
      </div>

      {/* Mission Progress */}
      <div className="p-4 bg-gray-900">
        <div className="flex items-center gap-4">
          {mission.tasks.map((task, index) => (
            <motion.div
              key={index}
              className={`w-3 h-3 rounded-full ${
                index < currentTask ? 'bg-[#88cc14]' :
                index === currentTask ? 'bg-[#88cc14] animate-pulse' :
                'bg-gray-700'
              }`}
              whileHover={{ scale: 1.2 }}
            />
          ))}
        </div>
      </div>

      {/* Current Task */}
      <div className="p-4">
        <div className="bg-gray-900 p-4 rounded-lg mb-4">
          <h4 className="text-white font-bold mb-2">Current Objective:</h4>
          <p className="text-gray-400">{mission.tasks[currentTask].description}</p>
        </div>

        {/* Terminal Output */}
        <div className="bg-gray-900 p-4 rounded-lg mb-4 h-64 overflow-y-auto font-mono">
          {history.map((entry, i) => (
            <div key={i} className="mb-2">
              <div className="flex items-center gap-2 text-[#88cc14]">
                <span>$</span>
                <span>{entry.command}</span>
              </div>
              <div className={`ml-4 ${
                entry.correct ? 'text-green-400' : 'text-red-400'
              }`}>
                {entry.output}
              </div>
            </div>
          ))}
        </div>

        {/* Command Input */}
        <form onSubmit={handleCommand}>
          <div className="flex items-center gap-2 bg-gray-900 p-2 rounded-lg">
            <span className="text-[#88cc14]">$</span>
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="flex-1 bg-transparent border-none outline-none text-white font-mono"
              placeholder="Enter command..."
            />
          </div>
        </form>

        {/* Hint Button */}
        <button
          onClick={() => setShowHint(!showHint)}
          className="mt-4 text-gray-400 hover:text-[#88cc14] transition-colors flex items-center gap-2"
        >
          <FaLightbulb />
          <span>{showHint ? 'Hide Hint' : 'Show Hint'}</span>
        </button>

        {/* Hint Display */}
        {showHint && mission.tasks[currentTask].hint && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2 p-4 bg-gray-900 rounded-lg"
          >
            <p className="text-gray-400">{mission.tasks[currentTask].hint}</p>
          </motion.div>
        )}
      </div>

      {/* Mission Complete */}
      {currentTask === mission.tasks.length && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/80 flex items-center justify-center"
        >
          <div className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="w-24 h-24 bg-[#88cc14] rounded-full mx-auto mb-6 flex items-center justify-center"
            >
              <FaTrophy className="text-4xl text-black" />
            </motion.div>
            <h3 className="text-2xl font-bold text-[#88cc14] mb-4">
              Mission Complete!
            </h3>
            <div className="space-y-2 mb-6">
              {mission.rewards && mission.rewards.map((reward, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: i * 0.2 }}
                  className="flex items-center gap-2 justify-center text-white"
                >
                  <FaCheck className="text-[#88cc14]" />
                  <span>{reward}</span>
                </motion.div>
              ))}
            </div>
            <button
              onClick={() => window.location.reload()}
              className="bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
            >
              Continue Training
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default LinuxTerminalGame;