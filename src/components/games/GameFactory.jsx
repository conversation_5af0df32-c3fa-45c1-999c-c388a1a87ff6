import React from 'react';
import { motion } from 'framer-motion';
import { FaGamepad, FaLock } from 'react-icons/fa';
import OSSimulator from './OSSimulator';
import ProcessGame from './ProcessGame';
import MemoryGame from './MemoryGame';
import FileSystemGame from './FileSystemGame';
import NetworkGame from './NetworkGame';
import SecurityGame from './SecurityGame';
import LinuxTerminalGame from './LinuxTerminalGame';
import LinuxBasicsGame from './LinuxBasicsGame';

const gameComponents = {
  // OS Games
  'os-introduction': OSSimulator,
  'process-management': ProcessGame,
  'memory-management': MemoryGame,
  'file-systems': FileSystemGame,
  'io-management': NetworkGame,
  'protection-security': SecurityGame,

  // Linux Games
  'linux-basics': LinuxBasicsGame,
  'linux-introduction': LinuxTerminalGame,
  'shell-scripting': LinuxTerminalGame,
  'file-system-navigation': FileSystemGame,
  'linux-permissions': SecurityGame
};

const GameFactory = ({ topic, onComplete }) => {
  const GameComponent = gameComponents[topic];
  
  if (!GameComponent) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-black/5 rounded-lg p-8 text-center"
      >
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
          <FaLock className="text-[#88cc14] text-2xl" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">Mission Locked</h3>
        <p className="text-gray-600 mb-4">
          This mission is currently in development. Check back soon for new challenges!
        </p>
        <div className="inline-flex items-center gap-2 text-[#88cc14]">
          <FaGamepad />
          <span>More missions coming soon</span>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="game-container"
    >
      <GameComponent topic={topic} onComplete={onComplete} />
    </motion.div>
  );
};

export default GameFactory;