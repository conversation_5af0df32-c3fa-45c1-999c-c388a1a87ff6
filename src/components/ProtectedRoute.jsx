import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { getSession, refreshSession } from '../lib/auth';

const ProtectedRoute = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const location = useLocation();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Try to get session
        let session = await getSession();

        // If no session, try to refresh
        if (!session) {
          session = await refreshSession();
        }

        if (session?.user) {
          // Get user subscription using RPC function
          const { data: subscriptionData, error: subError } = await supabase
            .rpc('get_user_subscription', { p_user_id: session.user.id });

          if (subError) {
            console.error('Error fetching subscription:', subError);
          }

          setUser({ ...session.user, subscription: subscriptionData });
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        try {
          // Get user subscription on auth change
          const { data: subscriptionData, error: subError } = await supabase
            .rpc('get_user_subscription', { p_user_id: session.user.id });

          if (subError) {
            console.error('Error fetching subscription:', subError);
          }

          setUser({ ...session.user, subscription: subscriptionData });
        } catch (error) {
          console.error('Error updating user data:', error);
          setUser(session.user);
        }
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0B1120] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  // For development purposes, allow access to protected routes without authentication
  // Remove this in production
  const isDevelopment = import.meta.env.DEV;

  // Create a mock user for development
  if (isDevelopment && !user) {
    const mockUser = {
      id: 'mock-user-id',
      email: '<EMAIL>',
      user_metadata: {
        username: 'dev_user',
        full_name: 'Development User',
        subscription: 'Premium'
      },
      subscription: 'Premium'
    };
    setUser(mockUser);
    return (
      <div className="min-h-screen bg-[#0B1120] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    // Save the attempted URL for redirecting after login
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }

  return children;
};

export default ProtectedRoute;