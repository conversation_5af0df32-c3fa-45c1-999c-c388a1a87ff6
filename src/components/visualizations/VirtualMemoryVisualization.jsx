import React, { useState } from 'react';
import { motion } from 'framer-motion';

const VirtualMemoryVisualization = ({ data }) => {
  const [selectedPage, setSelectedPage] = useState(null);

  const handlePageClick = (page) => {
    setSelectedPage(page);
  };

  return (
    <div className="space-y-6">
      <div className="bg-gray-900 p-4 rounded-lg">
        <h3 className="text-white font-bold mb-4">Virtual Address Translation</h3>
        <div className="flex items-center gap-4">
          <div className="bg-[#88cc14]/20 p-3 rounded border border-[#88cc14]">
            <p className="text-[#88cc14] font-mono">{data.virtualAddress}</p>
            <p className="text-gray-400 text-sm">Virtual Address</p>
          </div>
          <div className="text-[#88cc14]">→</div>
          <div className="bg-gray-800 p-3 rounded">
            <p className="text-white font-mono">Physical Address</p>
            <p className="text-gray-400 text-sm">After translation</p>
          </div>
        </div>
      </div>

      <div className="bg-gray-900 p-4 rounded-lg">
        <h3 className="text-white font-bold mb-4">Page Table</h3>
        <div className="space-y-2">
          {data.pageTable.map((entry, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => handlePageClick(entry)}
              className={`p-3 rounded cursor-pointer transition-colors ${
                selectedPage === entry
                  ? 'bg-[#88cc14]/20 border border-[#88cc14]'
                  : 'bg-gray-800 hover:bg-gray-700'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white">Virtual Page: {entry.virtualPage}</p>
                  <p className="text-gray-400 text-sm">
                    Physical Frame: {entry.physicalPage}
                  </p>
                </div>
                <div className={`px-2 py-1 rounded text-sm ${
                  entry.valid
                    ? 'bg-green-500/20 text-green-400'
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {entry.valid ? 'Valid' : 'Invalid'}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VirtualMemoryVisualization;