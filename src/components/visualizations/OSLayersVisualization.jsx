import React from 'react';
import { motion } from 'framer-motion';

const OSLayersVisualization = ({ data }) => {
  return (
    <div className="space-y-4 p-4 bg-gray-900 rounded-lg">
      {data.layers.map((layer, index) => (
        <motion.div
          key={index}
          initial={{ x: -100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: index * 0.2 }}
          className="p-4 rounded-lg flex items-center gap-3"
          style={{ backgroundColor: `${layer.color}20`, border: `1px solid ${layer.color}` }}
        >
          <div className="w-10 h-10 rounded-lg flex items-center justify-center" 
            style={{ backgroundColor: `${layer.color}40` }}>
            <span className="text-white text-xl">{layer.icon}</span>
          </div>
          <span className="text-white font-medium">{layer.name}</span>
        </motion.div>
      ))}
    </div>
  );
};

export default OSLayersVisualization;