import React, { useState } from 'react';
import { motion } from 'framer-motion';

const PageReplacementVisualization = ({ data }) => {
  const [selectedAlgorithm, setSelectedAlgorithm] = useState(data.algorithms[0]);
  const [currentStep, setCurrentStep] = useState(0);

  const stepForward = () => {
    if (currentStep < data.pageReferences.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const stepBackward = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-gray-900 p-4 rounded-lg">
        <h3 className="text-white font-bold mb-4">Page Replacement Simulation</h3>
        
        {/* Algorithm Selection */}
        <div className="flex gap-2 mb-4">
          {data.algorithms.map((algorithm, index) => (
            <button
              key={index}
              onClick={() => setSelectedAlgorithm(algorithm)}
              className={`px-3 py-1 rounded transition-colors ${
                selectedAlgorithm === algorithm
                  ? 'bg-[#88cc14] text-black'
                  : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
              }`}
            >
              {algorithm}
            </button>
          ))}
        </div>

        {/* Page Reference String */}
        <div className="mb-4">
          <h4 className="text-gray-400 mb-2">Reference String:</h4>
          <div className="flex gap-2">
            {data.pageReferences.map((page, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className={`w-8 h-8 flex items-center justify-center rounded ${
                  index === currentStep
                    ? 'bg-[#88cc14] text-black'
                    : 'bg-gray-800 text-white'
                }`}
              >
                {page}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Controls */}
        <div className="flex justify-center gap-4">
          <button
            onClick={stepBackward}
            disabled={currentStep === 0}
            className="px-4 py-2 rounded bg-gray-800 text-white disabled:opacity-50"
          >
            Previous
          </button>
          <button
            onClick={stepForward}
            disabled={currentStep === data.pageReferences.length - 1}
            className="px-4 py-2 rounded bg-gray-800 text-white disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default PageReplacementVisualization;