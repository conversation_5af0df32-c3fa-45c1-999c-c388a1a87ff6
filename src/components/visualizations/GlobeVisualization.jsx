import React, { useRef, useState, useEffect, Suspense } from 'react';
import { <PERSON><PERSON>, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, Stars } from '@react-three/drei';
import * as THREE from 'three';
import { FaExclamationTriangle, FaSkull, FaLock, FaServer, FaWifi } from 'react-icons/fa';

// Mock attack data for demonstration
const MOCK_ATTACKS = [
  { from: 'US', to: 'RU', type: 'Ransomware' },
  { from: 'CN', to: 'US', type: 'DDoS' },
  { from: 'RU', to: 'DE', type: 'Malware' },
  { from: 'IR', to: 'US', type: 'Phishing' },
  { from: 'KP', to: 'JP', type: 'Ransomware' },
  { from: 'RU', to: 'UA', type: 'Infrastructure' },
];

// Attack types with colors
const attackTypes = {
  'Ransomware': { color: '#ef4444', icon: FaExclamationTriangle },
  'DDoS': { color: '#f97316', icon: FaServer },
  'Malware': { color: '#8b5cf6', icon: FaSkull },
  'Phishing': { color: '#3b82f6', icon: FaWifi },
  'Infrastructure': { color: '#10b981', icon: FaLock },
};

// Flickering fire light component
function FireLight({ position, color }) {
  const lightRef = useRef();

  useFrame(({ clock }) => {
    if (lightRef.current) {
      // Create a natural flickering effect
      const time = clock.getElapsedTime();
      const flicker1 = Math.sin(time * 2.5) * 0.1;
      const flicker2 = Math.sin(time * 3.5) * 0.05;
      const flicker3 = Math.sin(time * 5.0) * 0.025;

      // Combine multiple frequencies for a more natural flicker
      const intensity = 0.3 + flicker1 + flicker2 + flicker3;
      lightRef.current.intensity = Math.max(0.1, intensity);
    }
  });

  return <pointLight ref={lightRef} position={position} intensity={0.3} color={color} />;
}

// Country positions on the globe (spherical coordinates)
const countries = [
  { name: 'United States', code: 'US', lat: 37.0902, lng: -95.7129, attacks: 0, color: '#3b82f6' },
  { name: 'Russia', code: 'RU', lat: 61.5240, lng: 105.3188, attacks: 0, color: '#ef4444' },
  { name: 'China', code: 'CN', lat: 35.8617, lng: 104.1954, attacks: 0, color: '#f97316' },
  { name: 'North Korea', code: 'KP', lat: 40.3399, lng: 127.5101, attacks: 0, color: '#ec4899' },
  { name: 'Iran', code: 'IR', lat: 32.4279, lng: 53.6880, attacks: 0, color: '#f97316' },
  { name: 'United Kingdom', code: 'GB', lat: 55.3781, lng: -3.4360, attacks: 0, color: '#6366f1' },
  { name: 'Germany', code: 'DE', lat: 51.1657, lng: 10.4515, attacks: 0, color: '#6366f1' },
  { name: 'Ukraine', code: 'UA', lat: 48.3794, lng: 31.1656, attacks: 0, color: '#6366f1' },
  { name: 'Japan', code: 'JP', lat: 36.2048, lng: 138.2529, attacks: 0, color: '#ec4899' },
  { name: 'India', code: 'IN', lat: 20.5937, lng: 78.9629, attacks: 0, color: '#8b5cf6' },
  { name: 'Brazil', code: 'BR', lat: -14.2350, lng: -51.9253, attacks: 0, color: '#10b981' },
  { name: 'Australia', code: 'AU', lat: -25.2744, lng: 133.7751, attacks: 0, color: '#f43f5e' },
  { name: 'Poland', code: 'PL', lat: 51.9194, lng: 19.1451, attacks: 0, color: '#6366f1' },
  { name: 'Spain', code: 'ES', lat: 40.4637, lng: -3.7492, attacks: 0, color: '#6366f1' },
];

// Convert lat/lng to 3D coordinates on a sphere
const latLngToVector3 = (lat, lng, radius = 1) => {
  const phi = (90 - lat) * (Math.PI / 180);
  const theta = (lng + 180) * (Math.PI / 180);

  const x = -(radius * Math.sin(phi) * Math.cos(theta));
  const z = radius * Math.sin(phi) * Math.sin(theta);
  const y = radius * Math.cos(phi);

  return new THREE.Vector3(x, y, z);
};

// Earth component with realistic appearance and dragon-fire theme
function Earth(props) {
  const earthRef = useRef();
  const atmosphereRef = useRef();
  const landmassRef = useRef();

  useFrame(() => {
    earthRef.current.rotation.y += 0.0005;
    if (atmosphereRef.current) {
      atmosphereRef.current.rotation.y = earthRef.current.rotation.y;
    }
    if (landmassRef.current) {
      landmassRef.current.rotation.y = earthRef.current.rotation.y;
    }
  });

  // Create landmasses for major continents and countries
  const createLandmass = (points, color) => {
    const normalizedPoints = points.map(v => v.clone().normalize());

    return (
      <line>
        <bufferGeometry attach="geometry">
          <bufferAttribute
            attach="attributes-position"
            array={new Float32Array(normalizedPoints.flatMap(v => [v.x, v.y, v.z]))}
            count={normalizedPoints.length}
            itemSize={3}
          />
        </bufferGeometry>
        <lineBasicMaterial attach="material" color={color} linewidth={3} />
      </line>
    );
  };

  // Define landmasses with more detailed country outlines
  const landmasses = [
    // North America
    {
      points: [
        new THREE.Vector3(-0.5, 0.4, -0.5),
        new THREE.Vector3(-0.7, 0.3, -0.2),
        new THREE.Vector3(-0.6, 0.1, -0.1),
        new THREE.Vector3(-0.3, 0.1, -0.2),
        new THREE.Vector3(-0.3, 0.4, -0.6),
        new THREE.Vector3(-0.5, 0.4, -0.5),
      ],
      color: "#caf0f8"
    },
    // United States
    {
      points: [
        new THREE.Vector3(-0.5, 0.3, -0.4),
        new THREE.Vector3(-0.6, 0.25, -0.3),
        new THREE.Vector3(-0.5, 0.2, -0.2),
        new THREE.Vector3(-0.4, 0.25, -0.3),
        new THREE.Vector3(-0.5, 0.3, -0.4),
      ],
      color: "#90e0ef"
    },
    // Canada
    {
      points: [
        new THREE.Vector3(-0.5, 0.4, -0.5),
        new THREE.Vector3(-0.6, 0.35, -0.4),
        new THREE.Vector3(-0.5, 0.3, -0.3),
        new THREE.Vector3(-0.4, 0.35, -0.4),
        new THREE.Vector3(-0.5, 0.4, -0.5),
      ],
      color: "#48cae4"
    },
    // South America
    {
      points: [
        new THREE.Vector3(-0.3, -0.1, -0.1),
        new THREE.Vector3(-0.2, -0.4, 0.1),
        new THREE.Vector3(-0.1, -0.5, 0.1),
        new THREE.Vector3(0, -0.2, 0),
        new THREE.Vector3(-0.3, -0.1, -0.1),
      ],
      color: "#caf0f8"
    },
    // Brazil
    {
      points: [
        new THREE.Vector3(-0.25, -0.2, 0),
        new THREE.Vector3(-0.2, -0.3, 0.05),
        new THREE.Vector3(-0.15, -0.35, 0.05),
        new THREE.Vector3(-0.1, -0.25, 0),
        new THREE.Vector3(-0.25, -0.2, 0),
      ],
      color: "#90e0ef"
    },
    // Europe
    {
      points: [
        new THREE.Vector3(0.2, 0.5, 0.5),
        new THREE.Vector3(0.1, 0.6, 0.4),
        new THREE.Vector3(0.3, 0.5, 0.3),
        new THREE.Vector3(0.4, 0.4, 0.4),
        new THREE.Vector3(0.2, 0.5, 0.5),
      ],
      color: "#caf0f8"
    },
    // UK
    {
      points: [
        new THREE.Vector3(0.15, 0.55, 0.45),
        new THREE.Vector3(0.13, 0.57, 0.43),
        new THREE.Vector3(0.17, 0.56, 0.42),
        new THREE.Vector3(0.18, 0.54, 0.44),
        new THREE.Vector3(0.15, 0.55, 0.45),
      ],
      color: "#00b4d8"
    },
    // Germany
    {
      points: [
        new THREE.Vector3(0.25, 0.5, 0.4),
        new THREE.Vector3(0.23, 0.52, 0.38),
        new THREE.Vector3(0.27, 0.51, 0.37),
        new THREE.Vector3(0.28, 0.49, 0.39),
        new THREE.Vector3(0.25, 0.5, 0.4),
      ],
      color: "#48cae4"
    },
    // Africa
    {
      points: [
        new THREE.Vector3(0.1, 0.3, 0.7),
        new THREE.Vector3(0.3, 0, 0.7),
        new THREE.Vector3(0.1, -0.3, 0.6),
        new THREE.Vector3(-0.1, -0.1, 0.5),
        new THREE.Vector3(0.1, 0.3, 0.7),
      ],
      color: "#caf0f8"
    },
    // South Africa
    {
      points: [
        new THREE.Vector3(0.15, -0.2, 0.65),
        new THREE.Vector3(0.2, -0.25, 0.63),
        new THREE.Vector3(0.15, -0.3, 0.62),
        new THREE.Vector3(0.1, -0.25, 0.64),
        new THREE.Vector3(0.15, -0.2, 0.65),
      ],
      color: "#90e0ef"
    },
    // Asia
    {
      points: [
        new THREE.Vector3(0.5, 0.5, 0.4),
        new THREE.Vector3(0.7, 0.3, 0.2),
        new THREE.Vector3(0.7, 0.1, 0.1),
        new THREE.Vector3(0.5, 0, 0.5),
        new THREE.Vector3(0.4, 0.4, 0.6),
        new THREE.Vector3(0.5, 0.5, 0.4),
      ],
      color: "#caf0f8"
    },
    // China
    {
      points: [
        new THREE.Vector3(0.6, 0.3, 0.3),
        new THREE.Vector3(0.65, 0.25, 0.25),
        new THREE.Vector3(0.6, 0.2, 0.2),
        new THREE.Vector3(0.55, 0.25, 0.25),
        new THREE.Vector3(0.6, 0.3, 0.3),
      ],
      color: "#00b4d8"
    },
    // India - more detailed outline
    {
      points: [
        // Northern border
        new THREE.Vector3(0.52, 0.25, 0.52),
        new THREE.Vector3(0.55, 0.22, 0.5),
        // Eastern border
        new THREE.Vector3(0.57, 0.18, 0.47),
        new THREE.Vector3(0.56, 0.15, 0.45),
        // Southern tip
        new THREE.Vector3(0.53, 0.1, 0.42),
        // Western border
        new THREE.Vector3(0.48, 0.12, 0.45),
        new THREE.Vector3(0.47, 0.17, 0.48),
        new THREE.Vector3(0.49, 0.22, 0.51),
        // Back to start
        new THREE.Vector3(0.52, 0.25, 0.52),
      ],
      color: "#0096c7" // Brighter blue to make India stand out
    },
    // Russia
    {
      points: [
        new THREE.Vector3(0.5, 0.6, 0.3),
        new THREE.Vector3(0.7, 0.5, 0.1),
        new THREE.Vector3(0.6, 0.4, 0.2),
        new THREE.Vector3(0.4, 0.5, 0.4),
        new THREE.Vector3(0.5, 0.6, 0.3),
      ],
      color: "#90e0ef"
    },
    // Australia
    {
      points: [
        new THREE.Vector3(0.6, -0.3, 0.1),
        new THREE.Vector3(0.7, -0.4, 0.2),
        new THREE.Vector3(0.8, -0.3, 0.3),
        new THREE.Vector3(0.7, -0.2, 0.2),
        new THREE.Vector3(0.6, -0.3, 0.1),
      ],
      color: "#caf0f8"
    },
    // Japan
    {
      points: [
        new THREE.Vector3(0.75, 0.25, 0.15),
        new THREE.Vector3(0.77, 0.23, 0.13),
        new THREE.Vector3(0.79, 0.21, 0.11),
        new THREE.Vector3(0.77, 0.19, 0.13),
        new THREE.Vector3(0.75, 0.25, 0.15),
      ],
      color: "#00b4d8"
    },
    // Mexico
    {
      points: [
        new THREE.Vector3(-0.4, 0.15, -0.3),
        new THREE.Vector3(-0.45, 0.1, -0.25),
        new THREE.Vector3(-0.4, 0.05, -0.2),
        new THREE.Vector3(-0.35, 0.1, -0.25),
        new THREE.Vector3(-0.4, 0.15, -0.3),
      ],
      color: "#48cae4"
    },
    // France
    {
      points: [
        new THREE.Vector3(0.2, 0.45, 0.45),
        new THREE.Vector3(0.18, 0.47, 0.43),
        new THREE.Vector3(0.22, 0.46, 0.42),
        new THREE.Vector3(0.23, 0.44, 0.44),
        new THREE.Vector3(0.2, 0.45, 0.45),
      ],
      color: "#00b4d8"
    },
    // Italy
    {
      points: [
        new THREE.Vector3(0.3, 0.4, 0.45),
        new THREE.Vector3(0.28, 0.42, 0.43),
        new THREE.Vector3(0.32, 0.41, 0.42),
        new THREE.Vector3(0.33, 0.39, 0.44),
        new THREE.Vector3(0.3, 0.4, 0.45),
      ],
      color: "#48cae4"
    },
    // Spain
    {
      points: [
        new THREE.Vector3(0.15, 0.4, 0.5),
        new THREE.Vector3(0.13, 0.42, 0.48),
        new THREE.Vector3(0.17, 0.41, 0.47),
        new THREE.Vector3(0.18, 0.39, 0.49),
        new THREE.Vector3(0.15, 0.4, 0.5),
      ],
      color: "#00b4d8"
    },
    // Saudi Arabia
    {
      points: [
        new THREE.Vector3(0.4, 0.2, 0.6),
        new THREE.Vector3(0.45, 0.15, 0.55),
        new THREE.Vector3(0.4, 0.1, 0.5),
        new THREE.Vector3(0.35, 0.15, 0.55),
        new THREE.Vector3(0.4, 0.2, 0.6),
      ],
      color: "#48cae4"
    },
    // Egypt
    {
      points: [
        new THREE.Vector3(0.35, 0.25, 0.65),
        new THREE.Vector3(0.4, 0.2, 0.6),
        new THREE.Vector3(0.35, 0.15, 0.55),
        new THREE.Vector3(0.3, 0.2, 0.6),
        new THREE.Vector3(0.35, 0.25, 0.65),
      ],
      color: "#00b4d8"
    },
    // Indonesia
    {
      points: [
        new THREE.Vector3(0.65, -0.1, 0.3),
        new THREE.Vector3(0.7, -0.15, 0.25),
        new THREE.Vector3(0.65, -0.2, 0.2),
        new THREE.Vector3(0.6, -0.15, 0.25),
        new THREE.Vector3(0.65, -0.1, 0.3),
      ],
      color: "#48cae4"
    },
    // South Korea
    {
      points: [
        new THREE.Vector3(0.7, 0.2, 0.2),
        new THREE.Vector3(0.72, 0.18, 0.18),
        new THREE.Vector3(0.7, 0.16, 0.16),
        new THREE.Vector3(0.68, 0.18, 0.18),
        new THREE.Vector3(0.7, 0.2, 0.2),
      ],
      color: "#00b4d8"
    },
    // North Korea
    {
      points: [
        new THREE.Vector3(0.7, 0.25, 0.2),
        new THREE.Vector3(0.72, 0.23, 0.18),
        new THREE.Vector3(0.7, 0.21, 0.16),
        new THREE.Vector3(0.68, 0.23, 0.18),
        new THREE.Vector3(0.7, 0.25, 0.2),
      ],
      color: "#48cae4"
    },
    // Iran
    {
      points: [
        new THREE.Vector3(0.45, 0.3, 0.5),
        new THREE.Vector3(0.5, 0.25, 0.45),
        new THREE.Vector3(0.45, 0.2, 0.4),
        new THREE.Vector3(0.4, 0.25, 0.45),
        new THREE.Vector3(0.45, 0.3, 0.5),
      ],
      color: "#00b4d8"
    },
    // Ukraine
    {
      points: [
        new THREE.Vector3(0.4, 0.45, 0.5),
        new THREE.Vector3(0.45, 0.4, 0.45),
        new THREE.Vector3(0.4, 0.35, 0.4),
        new THREE.Vector3(0.35, 0.4, 0.45),
        new THREE.Vector3(0.4, 0.45, 0.5),
      ],
      color: "#48cae4"
    },
    // Poland
    {
      points: [
        new THREE.Vector3(0.35, 0.45, 0.45),
        new THREE.Vector3(0.4, 0.4, 0.4),
        new THREE.Vector3(0.35, 0.35, 0.35),
        new THREE.Vector3(0.3, 0.4, 0.4),
        new THREE.Vector3(0.35, 0.45, 0.45),
      ],
      color: "#00b4d8"
    },
    // Canada - more detailed
    {
      points: [
        new THREE.Vector3(-0.4, 0.6, -0.4),
        new THREE.Vector3(-0.5, 0.55, -0.3),
        new THREE.Vector3(-0.6, 0.5, -0.2),
        new THREE.Vector3(-0.7, 0.45, -0.1),
        new THREE.Vector3(-0.65, 0.4, -0.15),
        new THREE.Vector3(-0.6, 0.35, -0.2),
        new THREE.Vector3(-0.5, 0.4, -0.3),
        new THREE.Vector3(-0.4, 0.5, -0.4),
        new THREE.Vector3(-0.4, 0.6, -0.4),
      ],
      color: "#48cae4"
    },
    // Brazil - more detailed
    {
      points: [
        new THREE.Vector3(-0.2, -0.1, 0),
        new THREE.Vector3(-0.25, -0.2, 0.05),
        new THREE.Vector3(-0.3, -0.3, 0.1),
        new THREE.Vector3(-0.25, -0.4, 0.15),
        new THREE.Vector3(-0.2, -0.35, 0.1),
        new THREE.Vector3(-0.15, -0.3, 0.05),
        new THREE.Vector3(-0.1, -0.2, 0),
        new THREE.Vector3(-0.15, -0.15, -0.05),
        new THREE.Vector3(-0.2, -0.1, 0),
      ],
      color: "#0096c7"
    },
    // Turkey
    {
      points: [
        new THREE.Vector3(0.4, 0.35, 0.55),
        new THREE.Vector3(0.45, 0.33, 0.52),
        new THREE.Vector3(0.5, 0.31, 0.49),
        new THREE.Vector3(0.45, 0.29, 0.51),
        new THREE.Vector3(0.4, 0.31, 0.53),
        new THREE.Vector3(0.4, 0.35, 0.55),
      ],
      color: "#00b4d8"
    },
    // Pakistan
    {
      points: [
        new THREE.Vector3(0.45, 0.3, 0.5),
        new THREE.Vector3(0.5, 0.28, 0.47),
        new THREE.Vector3(0.48, 0.25, 0.45),
        new THREE.Vector3(0.45, 0.27, 0.48),
        new THREE.Vector3(0.45, 0.3, 0.5),
      ],
      color: "#48cae4"
    },
    // Bangladesh
    {
      points: [
        new THREE.Vector3(0.57, 0.2, 0.47),
        new THREE.Vector3(0.58, 0.18, 0.45),
        new THREE.Vector3(0.56, 0.16, 0.44),
        new THREE.Vector3(0.55, 0.18, 0.46),
        new THREE.Vector3(0.57, 0.2, 0.47),
      ],
      color: "#00b4d8"
    },
    // Thailand
    {
      points: [
        new THREE.Vector3(0.6, 0.15, 0.4),
        new THREE.Vector3(0.62, 0.12, 0.38),
        new THREE.Vector3(0.6, 0.09, 0.36),
        new THREE.Vector3(0.58, 0.12, 0.38),
        new THREE.Vector3(0.6, 0.15, 0.4),
      ],
      color: "#48cae4"
    },
    // Vietnam
    {
      points: [
        new THREE.Vector3(0.63, 0.15, 0.35),
        new THREE.Vector3(0.65, 0.12, 0.33),
        new THREE.Vector3(0.63, 0.09, 0.31),
        new THREE.Vector3(0.61, 0.12, 0.33),
        new THREE.Vector3(0.63, 0.15, 0.35),
      ],
      color: "#00b4d8"
    },
    // Philippines
    {
      points: [
        new THREE.Vector3(0.7, 0.1, 0.25),
        new THREE.Vector3(0.72, 0.08, 0.23),
        new THREE.Vector3(0.7, 0.06, 0.21),
        new THREE.Vector3(0.68, 0.08, 0.23),
        new THREE.Vector3(0.7, 0.1, 0.25),
      ],
      color: "#48cae4"
    },
    // Malaysia
    {
      points: [
        new THREE.Vector3(0.62, 0.05, 0.32),
        new THREE.Vector3(0.64, 0.03, 0.3),
        new THREE.Vector3(0.62, 0.01, 0.28),
        new THREE.Vector3(0.6, 0.03, 0.3),
        new THREE.Vector3(0.62, 0.05, 0.32),
      ],
      color: "#00b4d8"
    },
    // New Zealand
    {
      points: [
        new THREE.Vector3(0.85, -0.35, 0.25),
        new THREE.Vector3(0.87, -0.37, 0.23),
        new THREE.Vector3(0.85, -0.39, 0.21),
        new THREE.Vector3(0.83, -0.37, 0.23),
        new THREE.Vector3(0.85, -0.35, 0.25),
      ],
      color: "#48cae4"
    },
  ];

  return (
    <>
      {/* Main globe */}
      <mesh ref={earthRef} {...props}>
        <sphereGeometry args={[1, 64, 64]} />
        <meshPhongMaterial
          color="#0a1128" // Dark blue base color
          emissive="#001845" // Subtle blue glow
          shininess={15}
          specular="#90e0ef" // Blue specular highlights
        />
      </mesh>

      {/* Atmosphere glow */}
      <mesh ref={atmosphereRef} scale={[1.02, 1.02, 1.02]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#0077b6"
          transparent={true}
          opacity={0.1}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Landmasses */}
      <group ref={landmassRef}>
        {landmasses.map((landmass, index) => (
          <React.Fragment key={index}>
            {createLandmass(landmass.points, landmass.color)}
          </React.Fragment>
        ))}
      </group>

      {/* Subtle blue glow effect */}
      <mesh ref={atmosphereRef} scale={[1.005, 1.005, 1.005]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#48cae4"
          transparent={true}
          opacity={0.05}
          blending={THREE.AdditiveBlending}
        />
      </mesh>
    </>
  );
}

// Enhanced marker for countries with glow effect
function CountryMarker({ position, color, size = 0.02 }) {
  const markerRef = useRef();

  // Pulse animation
  useFrame(({ clock }) => {
    if (markerRef.current) {
      const pulse = Math.sin(clock.getElapsedTime() * 2) * 0.05 + 1;
      markerRef.current.scale.set(pulse, pulse, pulse);
    }
  });

  return (
    <group position={position}>
      {/* Main marker */}
      <mesh ref={markerRef}>
        <sphereGeometry args={[size, 16, 16]} />
        <meshBasicMaterial color={color} />
      </mesh>

      {/* Glow effect */}
      <mesh scale={[1.5, 1.5, 1.5]}>
        <sphereGeometry args={[size, 16, 16]} />
        <meshBasicMaterial
          color={color}
          transparent={true}
          opacity={0.15}
          blending={THREE.AdditiveBlending}
        />
      </mesh>
    </group>
  );
}

// Dragon-fire attack line between countries with enhanced animation
function AttackLine({ startPos, endPos, color }) {
  const ref = useRef();
  const [progress, setProgress] = useState(0);

  const startVec = new THREE.Vector3().copy(startPos);
  const endVec = new THREE.Vector3().copy(endPos);

  // Create a curved line by adding a control point
  const midPoint = new THREE.Vector3().addVectors(startVec, endVec).multiplyScalar(0.5);
  // Push the midpoint outward
  midPoint.normalize().multiplyScalar(1.5);

  // Create a curve with these points
  const curve = new THREE.QuadraticBezierCurve3(startVec, midPoint, endVec);

  // Animation effect
  useEffect(() => {
    let animationFrame;
    let startTime = Date.now();
    const duration = 2000; // 2 seconds for full animation

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min(elapsed / duration, 1);
      setProgress(newProgress);

      if (newProgress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      cancelAnimationFrame(animationFrame);
    };
  }, []);

  // Sample points along the curve based on current progress
  const points = [];
  const numPoints = 50;
  const visiblePoints = Math.ceil(numPoints * progress);

  for (let i = 0; i <= visiblePoints; i++) {
    const point = curve.getPoint(i / numPoints);
    points.push(point);
  }

  const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);

  // Add a moving particle along the attack line
  const particlePosition = curve.getPoint(progress);

  // Generate multiple particles for fire effect
  const fireParticles = [];
  const particleCount = 5;

  for (let i = 0; i < particleCount; i++) {
    if (progress > 0.1) {
      // Calculate position slightly behind the main particle
      const offset = Math.max(0, progress - (i * 0.05));
      const pos = curve.getPoint(offset);

      // Add some randomness to position
      const jitter = new THREE.Vector3(
        (Math.random() - 0.5) * 0.03,
        (Math.random() - 0.5) * 0.03,
        (Math.random() - 0.5) * 0.03
      );

      pos.add(jitter);

      // Calculate size based on position in trail
      const size = 0.03 - (i * 0.005);

      // Calculate color based on position in trail
      let particleColor;
      if (i === 0) {
        particleColor = '#ffffff'; // White hot center
      } else if (i === 1) {
        particleColor = '#caf0f8'; // Light blue
      } else if (i < 3) {
        particleColor = color; // Original color
      } else {
        // Darker version of the color for the tail
        particleColor = '#0077b6';
      }

      fireParticles.push({ position: pos, size, color: particleColor });
    }
  }

  return (
    <group>
      {/* Main attack line */}
      <line ref={ref} geometry={lineGeometry}>
        <lineBasicMaterial
          color={color}
          linewidth={2}
          transparent={true}
          opacity={0.7}
        />
      </line>

      {/* Glow effect around the line */}
      <line geometry={lineGeometry}>
        <lineBasicMaterial
          color={color}
          linewidth={6}
          transparent={true}
          opacity={0.2}
          blending={THREE.AdditiveBlending}
        />
      </line>

      {/* Fire particles following the line */}
      {fireParticles.map((particle, index) => (
        <mesh key={index} position={particle.position} scale={[particle.size, particle.size, particle.size]}>
          <sphereGeometry args={[1, 8, 8]} />
          <meshBasicMaterial
            color={particle.color}
            transparent={true}
            opacity={0.8}
            blending={THREE.AdditiveBlending}
          />
        </mesh>
      ))}
    </group>
  );
}

// Main scene component
function Scene({ attacks }) {
  const [activeAttacks, setActiveAttacks] = useState([]);

  useEffect(() => {
    // Simulate new attacks appearing over time
    const interval = setInterval(() => {
      if (attacks.length > 0) {
        const randomIndex = Math.floor(Math.random() * attacks.length);
        const newAttack = attacks[randomIndex];

        const fromCountry = countries.find(c => c.code === newAttack.from);
        const toCountry = countries.find(c => c.code === newAttack.to);

        if (fromCountry && toCountry) {
          const fromPos = latLngToVector3(fromCountry.lat, fromCountry.lng);
          const toPos = latLngToVector3(toCountry.lat, toCountry.lng);

          setActiveAttacks(prev => [
            ...prev,
            {
              id: Date.now(),
              from: fromPos,
              to: toPos,
              type: newAttack.type,
              fromCountry: fromCountry.name,
              toCountry: toCountry.name,
              color: attackTypes[newAttack.type]?.color || '#ffffff'
            }
          ]);

          // Remove attack after animation completes
          setTimeout(() => {
            setActiveAttacks(prev => prev.filter(a => a.id !== Date.now()));
          }, 5000);
        }
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [attacks]);

  return (
    <>
      <color attach="background" args={['#000']} />
      <fog attach="fog" args={['#000', 3, 15]} />
      <ambientLight intensity={0.2} />

      {/* Main light - blue */}
      <pointLight position={[10, 10, 10]} intensity={1.0} color="#90e0ef" />

      {/* Secondary light - cyan */}
      <pointLight position={[-10, -10, -10]} intensity={0.7} color="#00b4d8" />

      {/* Accent light - subtle dark blue */}
      <pointLight position={[0, -15, 0]} intensity={0.3} color="#0a1128" />

      {/* Flickering blue light effect */}
      <FireLight position={[5, 0, 5]} color="#48cae4" />

      <Earth position={[0, 0, 0]} />

      {/* Country markers with enhanced visibility */}
      {countries.map((country, i) => (
        <CountryMarker
          key={i}
          position={latLngToVector3(country.lat, country.lng)}
          color={country.color === '#3b82f6' ? '#00b4d8' :
                 country.color === '#ef4444' ? '#0077b6' :
                 country.color === '#f97316' ? '#48cae4' :
                 country.color === '#ec4899' ? '#90e0ef' :
                 country.color === '#8b5cf6' ? '#ade8f4' :
                 country.color === '#10b981' ? '#48cae4' :
                 country.color === '#f43f5e' ? '#0077b6' :
                 country.color === '#60a5fa' ? '#00b4d8' :
                 country.color === '#f59e0b' ? '#48cae4' :
                 country.color === '#a855f7' ? '#ade8f4' : '#00b4d8'}
          size={0.025} // Slightly larger markers
        />
      ))}

      {/* Attack lines with blue-themed colors */}
      {activeAttacks.map((attack) => (
        <AttackLine
          key={attack.id}
          startPos={attack.from}
          endPos={attack.to}
          color={attack.color === '#3b82f6' ? '#00b4d8' :
                 attack.color === '#ef4444' ? '#0077b6' :
                 attack.color === '#f97316' ? '#48cae4' :
                 attack.color === '#ec4899' ? '#90e0ef' :
                 attack.color === '#8b5cf6' ? '#ade8f4' :
                 attack.color === '#10b981' ? '#48cae4' :
                 attack.color === '#f43f5e' ? '#0077b6' :
                 attack.color === '#60a5fa' ? '#00b4d8' :
                 attack.color === '#f59e0b' ? '#48cae4' :
                 attack.color === '#a855f7' ? '#ade8f4' : '#00b4d8'}
        />
      ))}

      {/* Enhanced stars with blue tint */}
      <Stars radius={100} depth={50} count={7000} factor={4} saturation={0.3} fade color="#caf0f8" />

      <OrbitControls
        enableZoom={true}
        enablePan={false}
        enableRotate={true}
        minDistance={1.5}
        maxDistance={4}
        autoRotate
        autoRotateSpeed={0.3}
      />
    </>
  );
}

// UI Components
const ThreatPanel = ({ attack }) => {
  if (!attack) return null;

  const attackType = attack.type || 'Unknown';
  const Icon = attackTypes[attackType]?.icon || FaExclamationTriangle;

  return (
    <div className="absolute bottom-4 left-0 right-0 mx-auto w-11/12 max-w-4xl bg-black bg-opacity-80 text-white p-4 rounded-lg border border-gray-700 backdrop-blur-sm">
      <div className="flex items-center space-x-2 mb-2">
        <div className="w-10 h-10 rounded-full flex items-center justify-center" style={{ backgroundColor: `${attack.color}30` }}>
          <Icon className="text-xl" style={{ color: attack.color }} />
        </div>
        <h3 className="text-xl font-bold">{attackType} threat detected</h3>
        <div className="ml-auto flex items-center">
          <span className="font-bold text-sm mr-2">{attack.fromCountry}</span>
          <div className="h-0.5 w-6 bg-gray-500"></div>
          <div className="w-3 h-3 rounded-full animate-pulse mx-1" style={{ backgroundColor: attack.color }}></div>
          <div className="h-0.5 w-6 bg-gray-500"></div>
          <span className="font-bold text-sm ml-2">{attack.toCountry}</span>
        </div>
      </div>

      <div className="flex items-center text-gray-400 mb-2">
        <div className="px-2 py-0.5 rounded bg-gray-800 text-xs mr-2">Severity: High</div>
        <div className="px-2 py-0.5 rounded bg-gray-800 text-xs">ID: TH-{Math.floor(Math.random() * 10000)}</div>
      </div>

      <p className="mb-4 border-l-2 pl-3" style={{ borderColor: attack.color }}>
        {attackType === 'Ransomware' && 'This attack encrypts files and demands payment for decryption keys. Attackers typically gain access through phishing emails or exploiting vulnerabilities.'}
        {attackType === 'DDoS' && 'This attack floods servers with traffic to disrupt services. Attackers use botnets of compromised devices to generate massive traffic volumes.'}
        {attackType === 'Malware' && 'This attack uses malicious software to compromise systems. Modern malware can evade detection and establish persistent access to networks.'}
        {attackType === 'Phishing' && 'This attack tricks users into revealing sensitive information through deception. Sophisticated phishing campaigns can mimic legitimate organizations with high accuracy.'}
        {attackType === 'Infrastructure' && 'This attack targets critical infrastructure systems. These attacks can disrupt essential services and potentially cause physical damage.'}
      </p>

      <div className="bg-blue-900 bg-opacity-30 p-3 rounded flex items-center border border-blue-800">
        <div className="font-bold text-blue-300 mr-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          Did you know:
        </div>
        <div>
          {attackType === 'Ransomware' && 'Ransomware attacks increased by 150% in 2023, with an average ransom payment of $1.5 million. The healthcare sector is particularly vulnerable.'}
          {attackType === 'DDoS' && 'The largest DDoS attack ever recorded reached 3.4 Tbps in bandwidth. Modern attacks often target application layer vulnerabilities rather than just overwhelming bandwidth.'}
          {attackType === 'Malware' && '560,000 new pieces of malware are detected every day. Fileless malware that operates only in memory is becoming increasingly common.'}
          {attackType === 'Phishing' && '90% of data breaches start with a phishing email, making it the most common attack vector. AI-generated phishing content has increased success rates by 40%.'}
          {attackType === 'Infrastructure' && 'Critical infrastructure attacks increased by 2000% from 2019 to 2023. Many industrial control systems were designed without security in mind.'}
        </div>
      </div>
    </div>
  );
};

const StatsPanel = () => {
  // Animated counter hook
  const useCounter = (end, duration = 2000, start = 0) => {
    const [count, setCount] = useState(start);

    useEffect(() => {
      let startTime = null;
      const step = timestamp => {
        if (!startTime) startTime = timestamp;
        const progress = Math.min((timestamp - startTime) / duration, 1);
        setCount(Math.floor(progress * (end - start) + start));
        if (progress < 1) {
          window.requestAnimationFrame(step);
        }
      };
      window.requestAnimationFrame(step);
    }, [end, duration, start]);

    return count;
  };

  const threatCount = useCounter(32320);
  const uniqueThreats = useCounter(5, 1000);

  return (
    <div className="absolute top-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg border border-gray-700 backdrop-blur-sm">
      <div className="flex space-x-2 mb-4">
        <button className="bg-green-800 px-3 py-1 rounded font-bold text-sm flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
          </svg>
          Observe
        </button>
        <button className="bg-gray-800 px-3 py-1 rounded text-sm flex items-center hover:bg-gray-700 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
          Defend
        </button>
        <button className="bg-gray-800 px-3 py-1 rounded text-sm flex items-center hover:bg-gray-700 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path d="M9 9a2 2 0 114 0 2 2 0 01-4 0z" />
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a4 4 0 00-3.446 6.032l-2.261 2.26a1 1 0 101.414 1.415l2.261-2.261A4 4 0 1011 5z" clipRule="evenodd" />
          </svg>
          Analyze
        </button>
      </div>

      <div className="space-y-3">
        <div>
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm text-gray-400">Security Index</span>
            <span className="text-sm font-bold">50%</span>
          </div>
          <div className="w-full bg-gray-700 h-2 rounded-full overflow-hidden">
            <div className="bg-gradient-to-r from-red-500 to-yellow-500 h-full" style={{ width: '50%' }}></div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gray-800 bg-opacity-50 p-2 rounded border border-gray-700">
            <div className="text-xs text-gray-400">Threats Detected</div>
            <div className="text-xl font-mono font-bold">{threatCount.toLocaleString()}</div>
          </div>

          <div className="bg-gray-800 bg-opacity-50 p-2 rounded border border-gray-700">
            <div className="text-xs text-gray-400">Unique Threat Types</div>
            <div className="text-xl font-mono font-bold">{uniqueThreats}</div>
          </div>

          <div className="bg-gray-800 bg-opacity-50 p-2 rounded border border-gray-700 col-span-2">
            <div className="text-xs text-gray-400">Active Defense Systems</div>
            <div className="flex items-center">
              <div className="text-xl font-mono font-bold mr-2">3/5</div>
              <div className="flex space-x-1">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                <div className="w-2 h-2 rounded-full bg-gray-500"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="flex items-center justify-center h-full w-full">
      <div className="text-white text-xl">Loading 3D Globe...</div>
    </div>
  );
}

// Main component
const GlobeVisualization = () => {
  const [currentAttack, setCurrentAttack] = useState(null);

  useEffect(() => {
    // Simulate changing the current attack for the panel
    const interval = setInterval(() => {
      if (MOCK_ATTACKS.length > 0) {
        const randomIndex = Math.floor(Math.random() * MOCK_ATTACKS.length);
        const attack = MOCK_ATTACKS[randomIndex];

        const fromCountry = countries.find(c => c.code === attack.from);
        const toCountry = countries.find(c => c.code === attack.to);

        setCurrentAttack({
          type: attack.type,
          fromCountry: fromCountry?.name || attack.from,
          toCountry: toCountry?.name || attack.to,
          color: attackTypes[attack.type]?.color || '#ffffff'
        });
      }
    }, 8000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative w-full h-[600px] bg-black rounded-lg overflow-hidden">
      <Suspense fallback={<LoadingFallback />}>
        <Canvas camera={{ position: [0, 0, 2.5], fov: 45 }}>
          <Scene attacks={MOCK_ATTACKS} />
        </Canvas>
      </Suspense>

      <StatsPanel />
      <ThreatPanel attack={currentAttack} />

      <div className="absolute bottom-2 left-0 right-0 mx-auto text-center text-gray-500">
        Powered by real-time threat intelligence
      </div>

      <button className="absolute bottom-2 right-4 bg-green-800 text-white px-4 py-2 rounded">
        Test Your Knowledge
      </button>
    </div>
  );
};

export default GlobeVisualization;
