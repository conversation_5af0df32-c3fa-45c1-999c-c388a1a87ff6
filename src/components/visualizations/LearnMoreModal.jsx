import React from 'react';
import threatEducation from '../../utils/threatEducation';

const LearnMoreModal = ({ threatType, onClose }) => {
  const content = threatEducation.getContent(threatType);
  
  return (
    <div className="fixed inset-0 flex items-center justify-center z-30 bg-black/70 backdrop-blur-sm">
      <div className="bg-[#0B1120] border border-gray-700 rounded-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <div className="sticky top-0 bg-[#0B1120] p-4 border-b border-gray-700 flex justify-between items-center z-10">
          <h2 className="text-xl font-bold text-[#88cc14]">{threatType}</h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="p-6 text-gray-200">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2 text-[#88cc14]/80">What is {threatType}?</h3>
            <p className="leading-relaxed">{content.description}</p>
          </div>
          
          {content.examples.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2 text-[#88cc14]/80">Notable Examples</h3>
              <ul className="list-disc pl-5 space-y-1">
                {content.examples.map((example, i) => (
                  <li key={i}>{example}</li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h3 className="text-lg font-semibold mb-2 text-[#88cc14]/80">Potential Impacts</h3>
              <ul className="list-disc pl-5 space-y-1">
                {content.impacts.map((impact, i) => (
                  <li key={i}>{impact}</li>
                ))}
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-2 text-[#88cc14]/80">Defense Strategies</h3>
              <ul className="list-disc pl-5 space-y-1">
                {content.defenses.map((defense, i) => (
                  <li key={i}>{defense}</li>
                ))}
              </ul>
            </div>
          </div>
          
          {content.technicalDetails && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2 text-[#88cc14]/80">Technical Details</h3>
              <div className="bg-gray-900 p-4 rounded-lg text-gray-300 whitespace-pre-line">
                {content.technicalDetails}
              </div>
            </div>
          )}
          
          {content.resources.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-2 text-[#88cc14]/80">Additional Resources</h3>
              <ul className="list-disc pl-5 space-y-1">
                {content.resources.map((resource, i) => (
                  <li key={i}>
                    <a 
                      href={resource.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-[#88cc14] hover:underline"
                    >
                      {resource.title}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LearnMoreModal;
