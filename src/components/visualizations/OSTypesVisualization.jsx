import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, Fa<PERSON>lock, FaNetworkWired } from 'react-icons/fa';

const iconMap = {
  FaUser,
  FaUsers,
  <PERSON>a<PERSON>lock,
  FaNetwork: FaNetworkWired
};

const OSTypesVisualization = ({ data }) => {
  return (
    <div className="grid grid-cols-2 gap-4 p-4 bg-gray-900 rounded-lg">
      {data.types.map((type, index) => {
        const Icon = iconMap[type.icon];
        return (
          <motion.div
            key={index}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: index * 0.1 }}
            className="bg-gray-800 p-4 rounded-lg flex items-center gap-3"
          >
            {Icon && <Icon className="text-[#88cc14] text-xl" />}
            <span className="text-white font-medium">{type.name}</span>
          </motion.div>
        );
      })}
    </div>
  );
};

export default OSTypesVisualization;