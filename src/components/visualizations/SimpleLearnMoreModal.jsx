import React, { useState, useEffect } from 'react';

const threatInfo = {
  'Ransomware': {
    description: 'Ransomware is a type of malicious software that encrypts a victim\'s files. The attackers then demand a ransom from the victim to restore access to the data upon payment.',
    impact: 'Ransomware can lead to temporary or permanent loss of sensitive data, disruption to regular operations, financial losses, and potential damage to the organization\'s reputation.',
    examples: ['WannaCry', 'NotPetya', 'Ryuk', 'REvil/Sodinokibi'],
    prevention: [
      'Maintain offline, encrypted backups of data',
      'Keep systems and software updated',
      'Use strong spam filters and authenticate inbound emails',
      'Train users to identify suspicious emails',
      'Implement application whitelisting'
    ]
  },
  'DDoS': {
    description: 'A Distributed Denial of Service (DDoS) attack attempts to make an online service unavailable by overwhelming it with traffic from multiple sources.',
    impact: 'DDoS attacks can cause websites and services to be slow or unavailable, leading to lost business, damaged reputation, and costs associated with mitigation and recovery.',
    examples: ['Mirai <PERSON>', 'AWS Shield 2.3 Tbps attack', 'GitHub 1.35 Tbps attack', 'Memcached reflection attacks'],
    prevention: [
      'Use anti-DDoS services and technology',
      'Implement rate limiting',
      'Configure network hardware against common attacks',
      'Deploy web application firewalls',
      'Have a DDoS response plan'
    ]
  },
  'Phishing': {
    description: 'Phishing is a cybercrime where targets are contacted by email, telephone or text message by someone posing as a legitimate institution to lure individuals into providing sensitive data.',
    impact: 'Phishing can lead to identity theft, unauthorized purchases, stealing of funds, and unauthorized access to personal accounts or sensitive organizational data.',
    examples: ['Spear phishing', 'Whaling', 'Clone phishing', 'Voice phishing (vishing)'],
    prevention: [
      'Implement email authentication protocols (SPF, DKIM, DMARC)',
      'Use anti-phishing toolbars in web browsers',
      'Train employees to identify phishing attempts',
      'Keep systems and browsers updated',
      'Use multi-factor authentication'
    ]
  },
  'Malware': {
    description: 'Malware is any software intentionally designed to cause damage to a computer, server, client, or computer network.',
    impact: 'Malware can steal, encrypt, or delete sensitive data, alter or hijack core computing functions, and monitor users\' computer activity without their permission.',
    examples: ['Viruses', 'Worms', 'Trojans', 'Spyware', 'Adware', 'Rootkits'],
    prevention: [
      'Use and maintain anti-virus software',
      'Keep operating systems and software updated',
      'Use a firewall',
      'Be cautious about downloads and attachments',
      'Use strong passwords and multi-factor authentication'
    ]
  },
  'Data Breach': {
    description: 'A data breach is a security incident in which information is accessed without authorization. It can be the result of a cyberattack or simply due to a vulnerability in the system.',
    impact: 'Data breaches can expose sensitive personal and financial information, leading to identity theft, financial fraud, and significant costs for the affected organization.',
    examples: ['Equifax breach', 'Marriott International breach', 'Yahoo breach', 'Capital One breach'],
    prevention: [
      'Encrypt sensitive data',
      'Implement strong access controls',
      'Regularly update and patch systems',
      'Train employees on security best practices',
      'Have an incident response plan'
    ]
  },
  'Brute Force': {
    description: 'A brute force attack is a trial-and-error method used to obtain information such as a user password or personal identification number (PIN).',
    impact: 'Successful brute force attacks can lead to unauthorized access to accounts, systems, or encrypted data, potentially resulting in data theft or system compromise.',
    examples: ['Password cracking', 'Hash cracking', 'Dictionary attacks', 'Credential stuffing'],
    prevention: [
      'Use strong, complex passwords',
      'Implement account lockout policies',
      'Use CAPTCHA',
      'Implement multi-factor authentication',
      'Use rate limiting for login attempts'
    ]
  },
  'Port Scan': {
    description: 'A port scan is a process that sends client requests to a range of server port addresses on a host, with the goal of finding an active port and exploiting a known vulnerability.',
    impact: 'While port scanning itself is not an attack, it is often a precursor to attacks, allowing attackers to identify potential vulnerabilities in systems.',
    examples: ['TCP SYN scan', 'TCP connect scan', 'UDP scan', 'FIN scan', 'XMAS scan'],
    prevention: [
      'Configure firewalls to block unauthorized access',
      'Use intrusion detection systems',
      'Keep systems updated with security patches',
      'Disable unnecessary services and ports',
      'Monitor network traffic for suspicious activity'
    ]
  }
};

// Default info for any threat type not specifically defined
const defaultInfo = {
  description: 'This is a type of cyber threat that can compromise systems, networks, or data.',
  impact: 'Cyber threats can lead to data loss, financial damage, operational disruption, and reputational harm.',
  examples: ['Various attack vectors', 'Multiple techniques', 'Different tools and methods'],
  prevention: [
    'Keep systems and software updated',
    'Use strong authentication methods',
    'Implement defense-in-depth strategies',
    'Train users on security awareness',
    'Have incident response plans'
  ]
};

const SimpleLearnMoreModal = ({ threatType, onClose }) => {
  // Get info for the specific threat type, or use default if not found
  const info = threatInfo[threatType] || defaultInfo;
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Simulate loading for better UX
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center z-30 bg-black/70 backdrop-blur-sm">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#88cc14]"></div>
        <p className="text-white ml-3">Loading educational content...</p>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center z-30">
      <div className="absolute inset-0 bg-black/70 backdrop-blur-sm" onClick={onClose}></div>

      <div className="bg-[#0B1120] border border-gray-700 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden relative z-10">
        {/* Header */}
        <div className="sticky top-0 bg-[#0B1120] p-4 border-b border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-bold text-[#88cc14]">{threatType} Threat</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700 bg-gray-800/50">
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'overview' ? 'text-[#88cc14] border-b-2 border-[#88cc14]' : 'text-gray-400 hover:text-gray-200'}`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'impact' ? 'text-[#88cc14] border-b-2 border-[#88cc14]' : 'text-gray-400 hover:text-gray-200'}`}
            onClick={() => setActiveTab('impact')}
          >
            Impact
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'prevention' ? 'text-[#88cc14] border-b-2 border-[#88cc14]' : 'text-gray-400 hover:text-gray-200'}`}
            onClick={() => setActiveTab('prevention')}
          >
            Prevention
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'examples' ? 'text-[#88cc14] border-b-2 border-[#88cc14]' : 'text-gray-400 hover:text-gray-200'}`}
            onClick={() => setActiveTab('examples')}
          >
            Examples
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'overview' && (
            <div>
              <h3 className="text-[#88cc14] font-medium mb-3">What is {threatType}?</h3>
              <p className="text-gray-300">{info.description}</p>

              <div className="mt-6 bg-gray-800/30 p-4 rounded border border-gray-700">
                <h4 className="text-white font-medium mb-2">Key Characteristics</h4>
                <ul className="list-disc pl-5 space-y-2 text-gray-300">
                  <li>Typically targets: {threatType === 'Ransomware' ? 'File systems and critical data' :
                                         threatType === 'DDoS' ? 'Network infrastructure and services' :
                                         threatType === 'Phishing' ? 'Human users and authentication systems' :
                                         threatType === 'Malware' ? 'Operating systems and applications' :
                                         threatType === 'Data Breach' ? 'Databases and data storage systems' :
                                         threatType === 'Brute Force' ? 'Authentication mechanisms' :
                                         threatType === 'Port Scan' ? 'Network services and open ports' :
                                         'Various systems and infrastructure'}</li>
                  <li>Attack vector: {threatType === 'Ransomware' ? 'Email attachments, exploit kits, RDP' :
                                     threatType === 'DDoS' ? 'Botnets, amplification techniques' :
                                     threatType === 'Phishing' ? 'Fraudulent emails, fake websites, SMS' :
                                     threatType === 'Malware' ? 'Downloads, attachments, exploits' :
                                     threatType === 'Data Breach' ? 'Exploits, stolen credentials, insider threats' :
                                     threatType === 'Brute Force' ? 'Automated password guessing tools' :
                                     threatType === 'Port Scan' ? 'Network scanning tools, reconnaissance' :
                                     'Various entry points and vulnerabilities'}</li>
                  <li>Detection difficulty: {threatType === 'Ransomware' ? 'Medium (visible impact)' :
                                           threatType === 'DDoS' ? 'Low (obvious impact)' :
                                           threatType === 'Phishing' ? 'High (requires user awareness)' :
                                           threatType === 'Malware' ? 'Medium to High (can be stealthy)' :
                                           threatType === 'Data Breach' ? 'Very High (often undetected for months)' :
                                           threatType === 'Brute Force' ? 'Low (generates many logs)' :
                                           threatType === 'Port Scan' ? 'Low (easily detected but common)' :
                                           'Varies by sophistication'}</li>
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'impact' && (
            <div>
              <h3 className="text-[#88cc14] font-medium mb-3">Potential Impact</h3>
              <p className="text-gray-300 mb-4">{info.impact}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div className="bg-red-900/20 p-4 rounded border border-red-800/30">
                  <h4 className="text-red-400 font-medium mb-2">Business Impact</h4>
                  <p className="text-gray-300 text-sm">
                    {threatType === 'Ransomware' ? 'Operational downtime, financial losses from ransom payments, recovery costs' :
                     threatType === 'DDoS' ? 'Service unavailability, lost revenue, customer dissatisfaction' :
                     threatType === 'Phishing' ? 'Financial fraud, unauthorized access to systems, data theft' :
                     threatType === 'Malware' ? 'System damage, data theft, additional attack vectors' :
                     threatType === 'Data Breach' ? 'Regulatory fines, legal liability, remediation costs' :
                     threatType === 'Brute Force' ? 'Unauthorized access, account takeover, data compromise' :
                     threatType === 'Port Scan' ? 'Precursor to more serious attacks, vulnerability exposure' :
                     'Financial losses, operational disruption, and reputational damage'}
                  </p>
                </div>

                <div className="bg-blue-900/20 p-4 rounded border border-blue-800/30">
                  <h4 className="text-blue-400 font-medium mb-2">Technical Impact</h4>
                  <p className="text-gray-300 text-sm">
                    {threatType === 'Ransomware' ? 'File encryption, system corruption, backup destruction' :
                     threatType === 'DDoS' ? 'Network saturation, resource exhaustion, service disruption' :
                     threatType === 'Phishing' ? 'Credential theft, malware installation, session hijacking' :
                     threatType === 'Malware' ? 'System infection, backdoor installation, data corruption' :
                     threatType === 'Data Breach' ? 'Data exfiltration, unauthorized access, integrity loss' :
                     threatType === 'Brute Force' ? 'Password compromise, access control bypass, privilege escalation' :
                     threatType === 'Port Scan' ? 'Service enumeration, vulnerability identification' :
                     'System compromise, data loss, and infrastructure damage'}
                  </p>
                </div>
              </div>

              <div className="mt-6 bg-yellow-900/20 p-4 rounded border border-yellow-800/30">
                <h4 className="text-yellow-500 font-medium mb-2">Did You Know?</h4>
                <p className="text-gray-300 text-sm">
                  {threatType === 'Ransomware' ? 'The average downtime after a ransomware attack is 16 days, and the average ransom payment increased to $570,000 in 2021.' :
                   threatType === 'DDoS' ? 'The largest DDoS attack ever recorded reached 3.47 Tbps, targeting Azure servers in 2022.' :
                   threatType === 'Phishing' ? '97% of users cannot identify a sophisticated phishing email, and phishing accounts for more than 80% of reported security incidents.' :
                   threatType === 'Malware' ? 'Over 450,000 new malware samples are detected every day, with most targeting Windows systems.' :
                   threatType === 'Data Breach' ? 'The average cost of a data breach reached $4.35 million in 2022, and it takes an average of 277 days to identify and contain a breach.' :
                   threatType === 'Brute Force' ? 'A complex 8-character password can be cracked in about 8 hours with modern hardware.' :
                   threatType === 'Port Scan' ? 'An average Internet-connected device is scanned 2,000 times per day.' :
                   'Cybercrime damages are projected to reach $10.5 trillion annually by 2025.'}
                </p>
              </div>
            </div>
          )}

          {activeTab === 'prevention' && (
            <div>
              <h3 className="text-[#88cc14] font-medium mb-3">Prevention Measures</h3>
              <div className="bg-[#88cc14]/10 p-4 rounded border border-[#88cc14]/30 mb-6">
                <p className="text-gray-300 text-sm italic">
                  Implementing these measures can significantly reduce the risk of {threatType.toLowerCase()} attacks and minimize potential damage.
                </p>
              </div>

              <ul className="space-y-3">
                {info.prevention.map((measure, index) => (
                  <li key={index} className="flex items-start bg-gray-800/30 p-3 rounded border border-gray-700">
                    <span className="text-[#88cc14] mr-2 mt-0.5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </span>
                    <span className="text-gray-300">{measure}</span>
                  </li>
                ))}
              </ul>

              <div className="mt-6 bg-blue-900/20 p-4 rounded border border-blue-800/30">
                <h4 className="text-blue-400 font-medium mb-2">Best Practice</h4>
                <p className="text-gray-300">
                  {threatType === 'Ransomware' ? 'Maintain offline backups and test restoration procedures regularly.' :
                   threatType === 'DDoS' ? 'Use a DDoS protection service and distribute your infrastructure.' :
                   threatType === 'Phishing' ? 'Implement email authentication (SPF, DKIM, DMARC) and train users regularly.' :
                   threatType === 'Malware' ? 'Use application whitelisting and keep all software updated.' :
                   threatType === 'Data Breach' ? 'Encrypt sensitive data and implement least privilege access controls.' :
                   threatType === 'Brute Force' ? 'Use multi-factor authentication and implement account lockout policies.' :
                   threatType === 'Port Scan' ? 'Close unnecessary ports and implement intrusion detection systems.' :
                   'Implement defense in depth with multiple layers of security controls.'}
                </p>
              </div>
            </div>
          )}

          {activeTab === 'examples' && (
            <div>
              <h3 className="text-[#88cc14] font-medium mb-3">Notable Examples</h3>
              <div className="grid grid-cols-1 gap-3 mb-6">
                {info.examples.map((example, index) => (
                  <div key={index} className="bg-gray-800/50 p-3 rounded border border-gray-700">
                    <div className="flex items-start">
                      <span className="text-yellow-500 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </span>
                      <span className="text-gray-300">{example}</span>
                    </div>
                  </div>
                ))}
              </div>

              <h3 className="text-[#88cc14] font-medium mb-3">Global Statistics</h3>
              <div className="bg-gray-800/30 p-4 rounded border border-gray-700">
                <p className="text-gray-300 mb-2">
                  {threatType === 'Ransomware' ? 'Ransomware attacks increased by 150% in 2021, with an average ransom payment of $220,000.' :
                   threatType === 'DDoS' ? 'DDoS attacks increased by 55% from 2020 to 2021, with the average attack size being 5.5 Gbps.' :
                   threatType === 'Phishing' ? '90% of data breaches start with a phishing email, making it the most common attack vector.' :
                   threatType === 'Malware' ? 'Malware attacks increased by 358% in 2020, with 92% of malware being delivered via email.' :
                   threatType === 'Data Breach' ? 'The average cost of a data breach reached $4.35 million in 2022, a 13% increase since 2020.' :
                   threatType === 'Brute Force' ? '80% of data breaches involve brute force or stolen credentials.' :
                   threatType === 'Port Scan' ? 'Port scanning is often the first step in 70% of targeted cyber attacks.' :
                   'Organizations with strong security awareness training report 70% fewer successful cyber attacks.'}
                </p>
                <p className="text-gray-300">
                  {threatType === 'Ransomware' ? 'The healthcare and financial sectors are the most targeted industries for ransomware attacks.' :
                   threatType === 'DDoS' ? 'Gaming and financial services are the most targeted industries for DDoS attacks.' :
                   threatType === 'Phishing' ? 'Business Email Compromise (BEC) scams cost $1.8 billion annually.' :
                   threatType === 'Malware' ? 'The average cost of a malware attack on a company is $2.6 million.' :
                   threatType === 'Data Breach' ? 'Healthcare has the highest average cost per breach at $10.10 million.' :
                   threatType === 'Brute Force' ? 'SSH servers experience an average of 2,814 brute force attempts per day.' :
                   threatType === 'Port Scan' ? 'The most commonly scanned ports are 22 (SSH), 80 (HTTP), and 443 (HTTPS).' :
                   'The average time to identify a breach is 207 days.'}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-900/50 p-4 border-t border-gray-700 flex justify-between items-center">
          <div className="text-xs text-gray-500">
            Educational content provided by XCerberus Security
          </div>
          <button
            onClick={onClose}
            className="bg-[#88cc14]/20 hover:bg-[#88cc14]/30 text-[#88cc14] px-4 py-2 rounded border border-[#88cc14]/30 transition-colors text-sm"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleLearnMoreModal;
