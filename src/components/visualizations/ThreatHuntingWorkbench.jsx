import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaInfoCircle, FaExclamationTriangle, FaPlay, FaSave, FaTrash, FaFlask, FaBriefcase, FaGraduationCap, FaLaptopCode } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import apiManager from '../../services/api/apiManager';

/**
 * ThreatHuntingWorkbench Component
 *
 * Provides an educational and practical interface for threat hunting with
 * learning resources, real API integration, and career insights.
 */
const ThreatHuntingWorkbench = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('learn');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showInfoPanel, setShowInfoPanel] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('ip');
  const [searchResults, setSearchResults] = useState(null);
  const [apiInitialized, setApiInitialized] = useState(false);
  const [careerStats, setCareerStats] = useState({
    averageSalary: '$110,140',
    jobGrowth: '35%',
    openPositions: '3,500+',
    requiredSkills: ['Threat Intelligence', 'SIEM Tools', 'Network Analysis', 'MITRE ATT&CK']
  });

  // Initialize API manager
  useEffect(() => {
    const initializeApis = async () => {
      try {
        setLoading(true);
        await apiManager.initialize();
        setApiInitialized(true);
        setLoading(false);
      } catch (err) {
        console.error('Failed to initialize APIs:', err);
        setError('Failed to initialize threat intelligence APIs. Some features may be limited.');
        setLoading(false);
      }
    };

    initializeApis();
  }, []);

  // Tabs for different workbench features
  const tabs = [
    { id: 'learn', label: 'Learn Threat Hunting', icon: <FaGraduationCap className="mr-2" /> },
    { id: 'hunt', label: 'Hunt Threats', icon: <FaSearch className="mr-2" /> },
    { id: 'career', label: 'Career Insights', icon: <FaBriefcase className="mr-2" /> }
  ];

  // Handle search query submission
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setError('Please enter a search query');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSearchResults(null);

      let results = {};

      // Perform search based on search type
      switch (searchType) {
        case 'ip':
          // Search IP using VirusTotal and Shodan
          if (apiManager.services.virusTotal) {
            const vtResult = await apiManager.services.virusTotal.getIPReport(searchQuery);
            results.virusTotal = vtResult;
          }
          
          if (apiManager.services.shodan) {
            const shodanResult = await apiManager.services.shodan.getHostInfo(searchQuery);
            results.shodan = shodanResult;
          }
          break;

        case 'domain':
          // Search domain using VirusTotal
          if (apiManager.services.virusTotal) {
            const vtResult = await apiManager.services.virusTotal.getDomainReport(searchQuery);
            results.virusTotal = vtResult;
          }
          break;

        case 'cve':
          // Search CVE using NVD
          if (apiManager.services.nvd) {
            const nvdResult = await apiManager.services.nvd.searchCVE(searchQuery);
            results.nvd = nvdResult;
          }
          break;

        default:
          setError('Invalid search type');
      }

      setSearchResults(results);
      setLoading(false);
    } catch (err) {
      console.error('Search error:', err);
      setError(`Error performing search: ${err.message || 'Unknown error'}`);
      setLoading(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold flex items-center">
          <FaSearch className="mr-2 text-blue-400" /> Threat Hunting Workbench
          <button
            className="ml-2 text-gray-400 hover:text-gray-300"
            onClick={() => setShowInfoPanel(!showInfoPanel)}
            title="Information about threat hunting"
          >
            <FaInfoCircle size={14} />
          </button>
        </h3>
      </div>

      {/* Information Panel */}
      {showInfoPanel && (
        <div className="mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm">
          <div className="flex justify-between items-start">
            <h4 className="font-semibold mb-2 flex items-center">
              <FaSearch className="mr-1 text-blue-400" /> About Threat Hunting
            </h4>
            <button
              className="text-gray-400 hover:text-gray-300"
              onClick={() => setShowInfoPanel(false)}
            >
              ×
            </button>
          </div>
          <p className="text-gray-300 mb-2">
            Threat hunting is a proactive cybersecurity approach that focuses on searching for malicious actors
            and activities in your network that may have evaded existing security solutions. This workbench
            provides tools to learn about threat hunting, practice with real threat intelligence, and explore career opportunities.
          </p>
        </div>
      )}

      {/* Tabs */}
      <div className="flex border-b border-gray-700 mb-4">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`px-4 py-2 text-sm font-medium flex items-center ${
              activeTab === tab.id
                ? 'border-b-2 border-blue-500 text-blue-400'
                : 'text-gray-400 hover:text-gray-300'
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content based on active tab */}
      <div className="flex-1 overflow-auto">
        {activeTab === 'learn' && (
          <div className="h-full">
            <div className="bg-gray-800 rounded-lg p-4 mb-4">
              <h4 className="text-lg font-semibold mb-4 flex items-center">
                <FaGraduationCap className="mr-2 text-green-400" /> Introduction to Threat Hunting
              </h4>
              
              <p className="mb-4">
                Threat hunting is a proactive cybersecurity approach where security professionals actively search for threats that have evaded existing security controls. Unlike traditional security monitoring that relies on alerts, threat hunting assumes that threats may already be present in the environment.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h5 className="font-medium mb-2">Why Hunt?</h5>
                  <ul className="list-disc pl-5 text-sm space-y-1">
                    <li>Reduce dwell time of attackers</li>
                    <li>Find threats that evaded detection</li>
                    <li>Validate security controls</li>
                    <li>Improve detection capabilities</li>
                  </ul>
                </div>
                
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h5 className="font-medium mb-2">Key Skills</h5>
                  <ul className="list-disc pl-5 text-sm space-y-1">
                    <li>Data analysis</li>
                    <li>Threat intelligence</li>
                    <li>System & network knowledge</li>
                    <li>Investigative mindset</li>
                  </ul>
                </div>
                
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h5 className="font-medium mb-2">Hunting Process</h5>
                  <ul className="list-disc pl-5 text-sm space-y-1">
                    <li>Create hypothesis</li>
                    <li>Gather & analyze data</li>
                    <li>Identify patterns</li>
                    <li>Investigate & document</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4 mb-4">
              <h4 className="text-lg font-semibold mb-4">Guided Hunt: Finding Suspicious IPs</h4>
              
              <p className="mb-4">
                In this guided hunt, we'll learn how to identify potentially malicious IP addresses using threat intelligence. Follow these steps to conduct your first threat hunt.
              </p>
              
              <div className="mb-6 bg-gray-700 p-4 rounded-lg">
                <h5 className="font-medium mb-2">Step 1: Form a Hypothesis</h5>
                <p className="text-sm mb-2">
                  Every hunt starts with a hypothesis. For this hunt, our hypothesis is:
                </p>
                <div className="bg-gray-600 p-3 rounded text-sm italic mb-2">
                  "There may be suspicious IP addresses in our environment that have been flagged by threat intelligence sources."
                </div>
                <p className="text-sm">
                  This hypothesis is testable by checking IP addresses against reputation databases like VirusTotal and Shodan.
                </p>
              </div>
              
              <div className="mb-6 bg-gray-700 p-4 rounded-lg">
                <h5 className="font-medium mb-2">Step 2: Try the Hunt</h5>
                <p className="text-sm mb-3">
                  Let's try hunting for a known malicious IP address. Enter one of these example IPs in the Hunt tab:
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3">
                  <div className="bg-gray-600 p-2 rounded text-sm font-mono">**************</div>
                  <div className="bg-gray-600 p-2 rounded text-sm font-mono">**************</div>
                  <div className="bg-gray-600 p-2 rounded text-sm font-mono">**************</div>
                  <div className="bg-gray-600 p-2 rounded text-sm font-mono">**************</div>
                </div>
                <button 
                  className="bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2 text-sm flex items-center"
                  onClick={() => {
                    setActiveTab('hunt');
                    setSearchType('ip');
                    setSearchQuery('**************');
                  }}
                >
                  <FaPlay className="mr-2" /> Try This Hunt
                </button>
              </div>
              
              <div className="mb-6 bg-gray-700 p-4 rounded-lg">
                <h5 className="font-medium mb-2">Step 3: Analyze the Results</h5>
                <p className="text-sm mb-3">
                  When analyzing the results, look for these indicators of suspicious activity:
                </p>
                <ul className="list-disc pl-5 text-sm space-y-1 mb-3">
                  <li>High detection rates in VirusTotal</li>
                  <li>Open suspicious ports in Shodan</li>
                  <li>Association with known malicious infrastructure</li>
                  <li>Unusual geographic locations</li>
                </ul>
                <p className="text-sm">
                  Document your findings and determine if further investigation is needed. In a real environment, you would check if these IPs appear in your logs or network traffic.
                </p>
              </div>
              
              <div className="bg-gray-700 p-4 rounded-lg">
                <h5 className="font-medium mb-2">Next Steps</h5>
                <p className="text-sm mb-3">
                  After completing this guided hunt, you can:
                </p>
                <ul className="list-disc pl-5 text-sm space-y-1">
                  <li>Try hunting for suspicious domains</li>
                  <li>Search for known vulnerabilities (CVEs)</li>
                  <li>Create your own hunting hypotheses</li>
                  <li>Learn about the MITRE ATT&CK framework for more structured hunting</li>
                </ul>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'hunt' && (
          <div className="h-full">
            <div className="bg-gray-800 rounded-lg p-4 mb-4">
              <h4 className="text-lg font-semibold mb-4 flex items-center">
                <FaSearch className="mr-2 text-blue-400" /> Threat Intelligence Search
              </h4>
              
              <div className="mb-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="md:col-span-3">
                    <label className="block text-sm font-medium text-gray-400 mb-1">Search Query</label>
                    <input
                      type="text"
                      className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="Enter IP, domain, or CVE ID..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Search Type</label>
                    <select
                      className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      value={searchType}
                      onChange={(e) => setSearchType(e.target.value)}
                    >
                      <option value="ip">IP Address</option>
                      <option value="domain">Domain</option>
                      <option value="cve">CVE ID</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <div className="mb-4">
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2 text-sm flex items-center"
                  onClick={handleSearch}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <span className="animate-pulse mr-2">⏳</span> Searching...
                    </>
                  ) : (
                    <>
                      <FaSearch className="mr-2" /> Search
                    </>
                  )}
                </button>
              </div>
              
              {error && (
                <div className="mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm">
                  <FaExclamationTriangle className="inline-block mr-2" />
                  {error}
                </div>
              )}
              
              {searchResults && (
                <div className="bg-gray-700 rounded-lg p-4 overflow-auto">
                  <h5 className="font-medium mb-3">Search Results</h5>
                  
                  {searchResults.virusTotal && (
                    <div className="mb-4">
                      <h6 className="text-sm font-medium mb-2 flex items-center">
                        <img src="https://www.virustotal.com/gui/images/favicon.png" alt="VirusTotal" className="w-4 h-4 mr-2" />
                        VirusTotal Results
                      </h6>
                      
                      <div className="bg-gray-800 p-3 rounded mb-2">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">Malicious Detections</span>
                          <span className="text-sm bg-red-900/30 text-red-400 px-2 py-1 rounded">
                            {searchResults.virusTotal.data?.attributes?.last_analysis_stats?.malicious || 0} / 
                            {(searchResults.virusTotal.data?.attributes?.last_analysis_stats?.malicious || 0) + 
                             (searchResults.virusTotal.data?.attributes?.last_analysis_stats?.harmless || 0)}
                          </span>
                        </div>
                        
                        {searchResults.virusTotal.data?.attributes?.last_analysis_results && (
                          <div className="text-xs">
                            <div className="grid grid-cols-2 gap-2">
                              {Object.entries(searchResults.virusTotal.data.attributes.last_analysis_results)
                                .filter(([_, result]) => result.category === 'malicious')
                                .slice(0, 6)
                                .map(([engine, result], index) => (
                                  <div key={index} className="bg-gray-700 p-2 rounded">
                                    <div className="font-medium">{engine}</div>
                                    <div className="text-red-400">{result.result}</div>
                                  </div>
                                ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {searchResults.shodan && (
                    <div className="mb-4">
                      <h6 className="text-sm font-medium mb-2 flex items-center">
                        <img src="https://www.shodan.io/static/img/favicon.png" alt="Shodan" className="w-4 h-4 mr-2" />
                        Shodan Results
                      </h6>
                      
                      <div className="bg-gray-800 p-3 rounded mb-2">
                        {searchResults.shodan.country_name && (
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm">Location</span>
                            <span className="text-sm">
                              {searchResults.shodan.country_name}, {searchResults.shodan.city || 'Unknown City'}
                            </span>
                          </div>
                        )}
                        
                        {searchResults.shodan.ports && searchResults.shodan.ports.length > 0 && (
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm">Open Ports</span>
                            <span className="text-sm">
                              {searchResults.shodan.ports.slice(0, 10).join(', ')}
                              {searchResults.shodan.ports.length > 10 ? '...' : ''}
                            </span>
                          </div>
                        )}
                        
                        {searchResults.shodan.tags && searchResults.shodan.tags.length > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Tags</span>
                            <span className="text-sm">
                              {searchResults.shodan.tags.join(', ')}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {searchResults.nvd && (
                    <div className="mb-4">
                      <h6 className="text-sm font-medium mb-2 flex items-center">
                        <img src="https://nvd.nist.gov/favicon.ico" alt="NVD" className="w-4 h-4 mr-2" />
                        NVD Results
                      </h6>
                      
                      {searchResults.nvd.vulnerabilities && searchResults.nvd.vulnerabilities.map((vuln, index) => (
                        <div key={index} className="bg-gray-800 p-3 rounded mb-2">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium">{vuln.cve.id}</span>
                            <span className={`text-sm px-2 py-1 rounded ${
                              vuln.cve.metrics?.cvssMetricV31?.[0]?.cvssData?.baseScore >= 9 ? 'bg-red-900/30 text-red-400' :
                              vuln.cve.metrics?.cvssMetricV31?.[0]?.cvssData?.baseScore >= 7 ? 'bg-orange-900/30 text-orange-400' :
                              vuln.cve.metrics?.cvssMetricV31?.[0]?.cvssData?.baseScore >= 4 ? 'bg-yellow-900/30 text-yellow-400' :
                              'bg-green-900/30 text-green-400'
                            }`}>
                              CVSS: {vuln.cve.metrics?.cvssMetricV31?.[0]?.cvssData?.baseScore || 'N/A'}
                            </span>
                          </div>
                          
                          <div className="text-sm mb-2">
                            {vuln.cve.descriptions?.[0]?.value}
                          </div>
                          
                          <div className="text-xs text-gray-400">
                            Published: {new Date(vuln.cve.published).toLocaleDateString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
        
        {activeTab === 'career' && (
          <div className="h-full">
            <div className="bg-gray-800 rounded-lg p-4 mb-4">
              <h4 className="text-lg font-semibold mb-4 flex items-center">
                <FaBriefcase className="mr-2 text-green-400" /> Cybersecurity Career Insights
              </h4>
              
              <p className="mb-4">
                Threat hunting is one of the most in-demand skills in cybersecurity today. Organizations are increasingly recognizing the value of proactive threat detection and are willing to pay a premium for skilled threat hunters.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h5 className="font-medium mb-2 flex items-center">
                    <span className="text-green-400 mr-2">$</span> Average Salary
                  </h5>
                  <div className="text-2xl font-bold mb-1">{careerStats.averageSalary}</div>
                  <p className="text-sm text-gray-400">For Threat Hunting Specialists in the US</p>
                </div>
                
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h5 className="font-medium mb-2 flex items-center">
                    <span className="text-green-400 mr-2">↗</span> Job Growth
                  </h5>
                  <div className="text-2xl font-bold mb-1">{careerStats.jobGrowth}</div>
                  <p className="text-sm text-gray-400">Projected growth over the next 5 years</p>
                </div>
                
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h5 className="font-medium mb-2 flex items-center">
                    <span className="text-green-400 mr-2">🔍</span> Open Positions
                  </h5>
                  <div className="text-2xl font-bold mb-1">{careerStats.openPositions}</div>
                  <p className="text-sm text-gray-400">Current job openings nationwide</p>
                </div>
              </div>
              
              <h5 className="font-medium mb-3">Required Skills for Threat Hunters</h5>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
                {careerStats.requiredSkills.map((skill, index) => (
                  <div key={index} className="bg-gray-700 p-2 rounded-lg text-center">
                    {skill}
                  </div>
                ))}
              </div>
              
              <div className="bg-blue-900/20 border border-blue-800 rounded-lg p-4">
                <h5 className="font-medium mb-2 flex items-center">
                  <FaGraduationCap className="mr-2" /> Learning Path
                </h5>
                <p className="text-sm mb-3">
                  To become a threat hunter, follow this learning path:
                </p>
                <ol className="list-decimal pl-5 text-sm space-y-1">
                  <li>Learn network and system fundamentals</li>
                  <li>Master security monitoring tools and techniques</li>
                  <li>Study threat intelligence and IOC identification</li>
                  <li>Practice with real-world hunting scenarios</li>
                  <li>Get certified (SANS FOR508, Certified Threat Hunter)</li>
                </ol>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThreatHuntingWorkbench;
