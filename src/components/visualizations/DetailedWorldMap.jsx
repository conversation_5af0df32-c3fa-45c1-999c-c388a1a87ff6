import React from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

const DetailedWorldMap = () => {
  const { darkMode } = useGlobalTheme();

  return (
    <div className="absolute inset-0">
      {/* Use our colorful world map */}
      <div
        className="absolute inset-0 bg-no-repeat bg-center"
        style={{
          backgroundImage: `url('/images/final-accurate-map.svg')`,
          backgroundSize: 'cover',
          opacity: 1,
        }}
      />
    </div>
  );
};

export default DetailedWorldMap;
