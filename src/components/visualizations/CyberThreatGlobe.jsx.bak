import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import axios from 'axios';

// Country mapping for IP geolocation
const countries = [
  { name: 'United States', lat: 37.0902, lng: -95.7129, code: 'US' },
  { name: 'China', lat: 35.8617, lng: 104.1954, code: 'CN' },
  { name: 'Russia', lat: 61.5240, lng: 105.3188, code: 'RU' },
  { name: 'United Kingdom', lat: 55.3781, lng: -3.4360, code: 'GB' },
  { name: 'Germany', lat: 51.1657, lng: 10.4515, code: 'DE' },
  { name: 'Brazil', lat: -14.2350, lng: -51.9253, code: 'BR' },
  { name: 'Australia', lat: -25.2744, lng: 133.7751, code: 'AU' },
  { name: 'India', lat: 20.5937, lng: 78.9629, code: 'IN' },
  { name: 'Japan', lat: 36.2048, lng: 138.2529, code: 'JP' },
  { name: 'South Africa', lat: -30.5595, lng: 22.9375, code: 'ZA' },
  { name: 'Canada', lat: 56.1304, lng: -106.3468, code: 'CA' },
  { name: 'France', lat: 46.2276, lng: 2.2137, code: 'FR' },
  { name: 'Mexico', lat: 23.6345, lng: -102.5528, code: 'MX' },
  { name: 'Indonesia', lat: -0.7893, lng: 113.9213, code: 'ID' },
  { name: 'Nigeria', lat: 9.0820, lng: 8.6753, code: 'NG' },
  { name: 'Spain', lat: 40.4637, lng: -3.7492, code: 'ES' },
  { name: 'Italy', lat: 41.8719, lng: 12.5674, code: 'IT' },
  { name: 'South Korea', lat: 35.9078, lng: 127.7669, code: 'KR' },
  { name: 'Netherlands', lat: 52.1326, lng: 5.2913, code: 'NL' },
  { name: 'Sweden', lat: 60.1282, lng: 18.6435, code: 'SE' },
  { name: 'Singapore', lat: 1.3521, lng: 103.8198, code: 'SG' },
  { name: 'Israel', lat: 31.0461, lng: 34.8516, code: 'IL' },
  { name: 'Ukraine', lat: 48.3794, lng: 31.1656, code: 'UA' },
  { name: 'Turkey', lat: 38.9637, lng: 35.2433, code: 'TR' },
  { name: 'Poland', lat: 51.9194, lng: 19.1451, code: 'PL' },
  { name: 'Unknown', lat: 0, lng: 0, code: 'XX' }
];

// Get geolocation data from AbuseIPDB
const getIPGeolocation = async (ip) => {
  try {
    const ABUSEIPDB_API_KEY = '********************************************************************************';
    const response = await axios.get('https://api.abuseipdb.com/api/v2/check', {
      params: {
        ipAddress: ip,
        maxAgeInDays: 90,
        verbose: true
      },
      headers: {
        'Key': ABUSEIPDB_API_KEY,
        'Accept': 'application/json'
      }
    });

    if (response.data && response.data.data) {
      const data = response.data.data;
      return {
        countryCode: data.countryCode,
        countryName: data.countryName,
        abuseScore: data.abuseConfidenceScore,
        isp: data.isp,
        domain: data.domain,
        usageType: data.usageType,
        totalReports: data.totalReports
      };
    }
    return null;
  } catch (error) {
    console.error('Error fetching IP geolocation from AbuseIPDB:', error);
    return null;
  }
};

// Fetch recent reports from AbuseIPDB
const fetchAbuseIPDBReports = async () => {
  try {
    const ABUSEIPDB_API_KEY = '********************************************************************************';
    const response = await axios.get('https://api.abuseipdb.com/api/v2/blacklist', {
      params: {
        confidenceMinimum: 90,
        limit: 25
      },
      headers: {
        'Key': ABUSEIPDB_API_KEY,
        'Accept': 'application/json'
      }
    });

    return response.data && response.data.data ? response.data.data : [];
  } catch (error) {
    console.error('Error fetching AbuseIPDB blacklist:', error);
    return [];
  }
};

// Fetch real threat data combining OTX and AbuseIPDB
const fetchThreatData = async () => {
  try {
    // Get blacklisted IPs from AbuseIPDB
    const blacklistedIPs = await fetchAbuseIPDBReports();
    console.log('Fetched blacklisted IPs:', blacklistedIPs.length);

    // Process blacklisted IPs into threats
    const threats = [];
    const threatTypes = ['Malware', 'Ransomware', 'Phishing', 'DDoS', 'Data Breach', 'Brute Force', 'Port Scan'];

    // Process up to 20 IPs
    const ipsToProcess = blacklistedIPs.slice(0, 20);

    for (let i = 0; i < ipsToProcess.length; i++) {
      const ip = ipsToProcess[i];

      // Get detailed information about this IP
      const geoData = await getIPGeolocation(ip.ipAddress);

      if (geoData) {
        // Find source country based on country code
        const sourceCountry = countries.find(c => c.code === geoData.countryCode) ||
                             countries[Math.floor(Math.random() * (countries.length - 1))];

        // Get a different random country as the target
        let targetCountry;
        do {
          targetCountry = countries[Math.floor(Math.random() * (countries.length - 1))];
        } while (targetCountry.name === sourceCountry.name);

        // Determine threat type based on abuse score
        let threatType = threatTypes[Math.floor(Math.random() * threatTypes.length)];
        if (geoData.usageType) {
          if (geoData.usageType.includes('Data Center')) threatType = 'DDoS';
          else if (geoData.usageType.includes('Hosting')) threatType = 'Malware';
        }

        // Determine severity based on abuse confidence score
        let severity = 1; // Default: Low
        if (geoData.abuseScore >= 90) severity = 3; // High
        else if (geoData.abuseScore >= 70) severity = 2; // Medium

        threats.push({
          id: i,
          type: threatType,
          severity: severity,
          source: {
            name: sourceCountry.name,
            lat: sourceCountry.lat,
            lng: sourceCountry.lng
          },
          target: {
            name: targetCountry.name,
            lat: targetCountry.lat,
            lng: targetCountry.lng
          },
          timestamp: new Date().getTime(),
          details: {
            name: `${threatType} Attack (Confidence: ${geoData.abuseScore}%)`,
            description: `Reported ${ip.totalReports} times. ${geoData.isp ? 'ISP: ' + geoData.isp : ''}`,
            ip: ip.ipAddress,
            reports: ip.totalReports
          }
        });
      }
    }

    // If we didn't get enough threats, add some fallback ones
    if (threats.length < 10) {
      const additionalThreats = generateFallbackThreats(10 - threats.length);
      threats.push(...additionalThreats);
    }

    return threats;
  } catch (error) {
    console.error('Error fetching threat data:', error);
    // Fallback to generated threats if API fails
    return generateFallbackThreats(20);
  }
};

// Fallback threat generation if API fails or returns insufficient data
const generateFallbackThreats = (count) => {
  const threats = [];
  const threatTypes = ['Ransomware', 'DDoS', 'Phishing', 'Data Breach', 'Malware'];

  for (let i = 0; i < count; i++) {
    const sourceCountry = countries[Math.floor(Math.random() * (countries.length - 1))];
    let targetCountry;

    // Ensure target is different from source
    do {
      targetCountry = countries[Math.floor(Math.random() * (countries.length - 1))];
    } while (targetCountry.name === sourceCountry.name);

    threats.push({
      id: i + 1000, // Offset to avoid ID conflicts
      type: threatTypes[Math.floor(Math.random() * threatTypes.length)],
      severity: Math.floor(Math.random() * 3) + 1, // 1-3
      source: {
        name: sourceCountry.name,
        lat: sourceCountry.lat,
        lng: sourceCountry.lng
      },
      target: {
        name: targetCountry.name,
        lat: targetCountry.lat,
        lng: targetCountry.lng
      },
      timestamp: new Date().getTime() - Math.floor(Math.random() * 3600000), // Within the last hour
      details: {
        name: `Fallback Threat ${i + 1}`,
        description: 'Generated threat when API data is insufficient',
        author: 'System',
        references: []
      }
    });
  }

  return threats;
};

// Convert lat/lng to 3D coordinates on a sphere
const latLngToVector3 = (lat, lng, radius) => {
  const phi = (90 - lat) * (Math.PI / 180);
  const theta = (lng + 180) * (Math.PI / 180);

  const x = -radius * Math.sin(phi) * Math.cos(theta);
  const y = radius * Math.cos(phi);
  const z = radius * Math.sin(phi) * Math.sin(theta);

  return new THREE.Vector3(x, y, z);
};

const CyberThreatGlobe = ({ width = '100%', height = '500px' }) => {
  const { darkMode } = useGlobalTheme();
  const containerRef = useRef(null);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const globeRef = useRef(null);
  const attackLinesRef = useRef([]);
  const defenderIconsRef = useRef([]);
  const [threats, setThreats] = useState([]);
  const [activeThreat, setActiveThreat] = useState(null);
  const [stats, setStats] = useState({
    totalThreatsDetected: 0,
    threatsByType: {},
    threatsByCountry: {},
    securityIndex: 65 // Starting security index (0-100)
  });
  const [showAchievement, setShowAchievement] = useState(false);
  const [achievementText, setAchievementText] = useState('');
  const [interactionMode, setInteractionMode] = useState('observe'); // 'observe', 'defend', 'analyze'
  const [selectedThreat, setSelectedThreat] = useState(null);
  const [userScore, setUserScore] = useState(0);
  const [showQuiz, setShowQuiz] = useState(false);
  const [currentQuiz, setCurrentQuiz] = useState(null);

  // Quiz questions
  const quizQuestions = [
    {
      question: "Which attack encrypts files and demands payment?",
      options: ["DDoS", "Phishing", "Ransomware", "SQL Injection"],
      correctAnswer: "Ransomware"
    },
    {
      question: "What type of attack floods servers with traffic?",
      options: ["Malware", "DDoS", "Phishing", "Man-in-the-Middle"],
      correctAnswer: "DDoS"
    },
    {
      question: "Which attack tricks users into revealing sensitive information?",
      options: ["Phishing", "Brute Force", "SQL Injection", "Cross-site Scripting"],
      correctAnswer: "Phishing"
    },
    {
      question: "What's the most common initial attack vector for ransomware?",
      options: ["USB Drives", "Email Attachments", "Software Vulnerabilities", "Weak Passwords"],
      correctAnswer: "Email Attachments"
    }
  ];

  // Listen for quiz trigger events
  useEffect(() => {
    const handleTriggerQuiz = () => {
      const randomQuiz = quizQuestions[Math.floor(Math.random() * quizQuestions.length)];
      setCurrentQuiz(randomQuiz);
      setShowQuiz(true);
    };

    document.addEventListener('triggerThreatQuiz', handleTriggerQuiz);

    return () => {
      document.removeEventListener('triggerThreatQuiz', handleTriggerQuiz);
    };
  }, [quizQuestions]);

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(45, containerRef.current.clientWidth / containerRef.current.clientHeight, 0.1, 1000);
    camera.position.set(0, 30, 250); // Position slightly above to see more northern hemisphere
    cameraRef.current = camera;

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.5;
    controls.enableZoom = true;
    controls.enablePan = false;
    controls.autoRotate = true;
    controls.autoRotateSpeed = 0.5;

    // Cinematic camera movements
    let cinematicMode = true;
    let cameraTarget = new THREE.Vector3(0, 30, 250);
    let cameraLookAt = new THREE.Vector3(0, 0, 0);
    let cameraMoveSpeed = 0.01;
    let cameraPhase = 0;

    // Function to update camera position cinematically
    const updateCameraPosition = () => {
      if (cinematicMode && cameraRef.current) {
        // Create smooth orbital movement with slight variations
        cameraPhase += 0.001;

        // Calculate new camera position
        const radius = 250 + Math.sin(cameraPhase * 2) * 30;
        const height = 30 + Math.sin(cameraPhase * 3) * 20;

        cameraTarget.x = Math.sin(cameraPhase) * radius;
        cameraTarget.y = height;
        cameraTarget.z = Math.cos(cameraPhase) * radius;

        // Smoothly move camera towards target
        cameraRef.current.position.lerp(cameraTarget, cameraMoveSpeed);

        // Smooth camera movement without dramatic zooms
        // Always gradually look at the center
        cameraLookAt.lerp(new THREE.Vector3(0, 0, 0), 0.01);
        cameraRef.current.lookAt(cameraLookAt);
      }
    };

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    // Add a subtle blue light from the opposite side
    const blueLight = new THREE.DirectionalLight(0x0077ff, 0.5);
    blueLight.position.set(-1, -1, -1);
    scene.add(blueLight);

    // Create Earth globe
    const radius = 100;
    const segments = 64;
    const textureLoader = new THREE.TextureLoader();

    // Load Earth texture based on dark/light mode
    const earthTexture = textureLoader.load(
      darkMode
        ? '/earth-dark.jpg'
        : '/earth-blue.jpg'
    );

    const earthGeometry = new THREE.SphereGeometry(radius, segments, segments);
    const earthMaterial = new THREE.MeshPhongMaterial({
      map: earthTexture,
      bumpScale: 0.5,
      specular: new THREE.Color(0x333333),
      shininess: 5
    });

    // Add a subtle glow effect
    const glowGeometry = new THREE.SphereGeometry(radius * 1.01, segments, segments);
    const glowMaterial = new THREE.MeshPhongMaterial({
      color: darkMode ? 0x0a4da8 : 0x0a6da8,
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide
    });

    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    scene.add(glowMesh);

    const earthMesh = new THREE.Mesh(earthGeometry, earthMaterial);
    scene.add(earthMesh);
    globeRef.current = earthMesh;

    // Add stars background
    if (darkMode) {
      const starGeometry = new THREE.BufferGeometry();
      const starMaterial = new THREE.PointsMaterial({
        color: 0xffffff,
        size: 0.7,
        transparent: true
      });

      const starVertices = [];
      for (let i = 0; i < 1000; i++) {
        const x = (Math.random() - 0.5) * 2000;
        const y = (Math.random() - 0.5) * 2000;
        const z = (Math.random() - 0.5) * 2000;
        starVertices.push(x, y, z);
      }

      starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
      const stars = new THREE.Points(starGeometry, starMaterial);
      scene.add(stars);
    }

    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate);
      controls.update();

      // Update cinematic camera movements
      if (Math.random() < 0.005) { // Occasionally switch camera modes
        cinematicMode = !cinematicMode;
      }

      if (cinematicMode) {
        updateCameraPosition();
        controls.enabled = false; // Disable manual controls during cinematic mode
      } else {
        controls.enabled = true; // Enable manual controls when not in cinematic mode
      }

      renderer.render(scene, camera);
    };

    animate();

    // Generate initial threats
    setThreats(generateFallbackThreats(3)); // Minimal initial threats for better performance

    // Handle window resize
    const handleResize = () => {
      if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;

      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;

      cameraRef.current.aspect = width / height;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (rendererRef.current && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [darkMode]);

  // Update attack lines and defenders when threats change
  useEffect(() => {
    if (!sceneRef.current || !globeRef.current || threats.length === 0) return;

    // Remove old attack lines and defender icons
    attackLinesRef.current.forEach(line => {
      if (line && sceneRef.current) {
        sceneRef.current.remove(line);
      }
    });
    attackLinesRef.current = [];

    defenderIconsRef.current.forEach(defender => {
      if (defender && sceneRef.current) {
        sceneRef.current.remove(defender);
      }
    });
    defenderIconsRef.current = [];

    // Create new attack lines
    const radius = 100; // Same as globe radius

    // Update stats
    const newStats = { ...stats };
    newStats.totalThreatsDetected += threats.length;

    // Track unique threat types and countries
    threats.forEach(threat => {
      // Count threats by type
      if (!newStats.threatsByType[threat.type]) {
        newStats.threatsByType[threat.type] = 0;
      }
      newStats.threatsByType[threat.type]++;

      // Count threats by country
      if (!newStats.threatsByCountry[threat.source.name]) {
        newStats.threatsByCountry[threat.source.name] = 0;
      }
      newStats.threatsByCountry[threat.source.name]++;

      // Check for achievements
      if (threat.type === 'APT' || threat.type === 'Zero-day') {
        setAchievementText(`Rare Threat Detected: ${threat.type}`);
        setShowAchievement(true);
        setTimeout(() => setShowAchievement(false), 3000);
      }

      if (Object.keys(newStats.threatsByType).length >= 5) {
        // Achievement for detecting 5 different threat types
        if (!newStats.achievementDiverseThreatTypes) {
          setAchievementText('Achievement: Threat Diversity Expert');
          setShowAchievement(true);
          setTimeout(() => setShowAchievement(false), 3000);
          newStats.achievementDiverseThreatTypes = true;
        }
      }
    });

    // Update security index based on threats and mitigations
    const threatImpact = Math.min(threats.length * 2, 15); // Max 15 points reduction
    newStats.securityIndex = Math.max(50, Math.min(95, newStats.securityIndex - threatImpact + 5)); // Slowly improve, but threats reduce it

    setStats(newStats);

    threats.forEach(threat => {
      const sourceVector = latLngToVector3(threat.source.lat, threat.source.lng, radius);
      const targetVector = latLngToVector3(threat.target.lat, threat.target.lng, radius);

      // Create a curved line between source and target
      const curvePoints = [];
      const midPoint = new THREE.Vector3().addVectors(sourceVector, targetVector).multiplyScalar(0.5);
      const distance = sourceVector.distanceTo(targetVector);

      // Make the curve higher based on distance
      midPoint.normalize().multiplyScalar(radius + distance * 0.3);

      // Create a quadratic bezier curve
      const curve = new THREE.QuadraticBezierCurve3(
        sourceVector,
        midPoint,
        targetVector
      );

      // Sample points along the curve
      const points = curve.getPoints(50);
      const geometry = new THREE.BufferGeometry().setFromPoints(points);

      // Set color based on threat severity
      let color, glowColor;
      switch (threat.severity) {
        case 3: // High
          color = 0xff3333; // Red
          glowColor = 0xff0000;
          break;
        case 2: // Medium
          color = 0xffaa00; // Orange
          glowColor = 0xff7700;
          break;
        case 1: // Low
        default:
          color = 0xffff00; // Yellow
          glowColor = 0xdddd00;
          break;
      }

      // Create the main line
      const material = new THREE.LineBasicMaterial({
        color: color,
        transparent: true,
        opacity: 0.8,
        linewidth: 2
      });

      const line = new THREE.Line(geometry, material);
      sceneRef.current.add(line);
      attackLinesRef.current.push(line);

      // Add a subtle pulse animation to the attack line
      const pulseAnimation = () => {
        if (line.material) {
          line.material.opacity = 0.5 + Math.sin(Date.now() * 0.005) * 0.3;
        }
        requestAnimationFrame(pulseAnimation);
      };
      pulseAnimation();

      // Add enhanced markers at source and target points
      // Source marker (smaller)
      const sourceGeometry = new THREE.SphereGeometry(1.2, 16, 16);
      const sourceMaterial = new THREE.MeshBasicMaterial({
        color: color,
        transparent: true,
        opacity: 0.9
      });

      const sourceSphere = new THREE.Mesh(sourceGeometry, sourceMaterial);
      sourceSphere.position.copy(sourceVector);
      sceneRef.current.add(sourceSphere);
      attackLinesRef.current.push(sourceSphere);

      // Add glow effect to source
      const sourceGlowGeometry = new THREE.SphereGeometry(1.8, 16, 16);
      const sourceGlowMaterial = new THREE.MeshBasicMaterial({
        color: glowColor,
        transparent: true,
        opacity: 0.4
      });
      const sourceGlow = new THREE.Mesh(sourceGlowGeometry, sourceGlowMaterial);
      sourceGlow.position.copy(sourceVector);
      sceneRef.current.add(sourceGlow);
      attackLinesRef.current.push(sourceGlow);

      // Target marker (larger to indicate destination)
      const targetGeometry = new THREE.SphereGeometry(1.5, 16, 16);
      const targetMaterial = new THREE.MeshBasicMaterial({
        color: color,
        transparent: true,
        opacity: 0.9
      });

      const targetSphere = new THREE.Mesh(targetGeometry, targetMaterial);
      targetSphere.position.copy(targetVector);
      sceneRef.current.add(targetSphere);
      attackLinesRef.current.push(targetSphere);

      // Add glow effect to target
      const targetGlowGeometry = new THREE.SphereGeometry(2.2, 16, 16);
      const targetGlowMaterial = new THREE.MeshBasicMaterial({
        color: glowColor,
        transparent: true,
        opacity: 0.4
      });
      const targetGlow = new THREE.Mesh(targetGlowGeometry, targetGlowMaterial);
      targetGlow.position.copy(targetVector);
      sceneRef.current.add(targetGlow);
      attackLinesRef.current.push(targetGlow);

      // Add pulse animation to the target glow
      const targetPulseAnimation = () => {
        if (targetGlow.material) {
          targetGlow.material.opacity = 0.2 + Math.sin(Date.now() * 0.003) * 0.2;
          targetGlow.scale.setScalar(1 + Math.sin(Date.now() * 0.003) * 0.1);
        }
        requestAnimationFrame(targetPulseAnimation);
      };
      targetPulseAnimation();

      // Randomly add defender icons (30% chance per threat)
      if (Math.random() < 0.3) {
        // Create a defender icon near the target
        const defenderOffset = new THREE.Vector3(
          (Math.random() - 0.5) * 5,
          (Math.random() - 0.5) * 5,
          (Math.random() - 0.5) * 5
        );

        const defenderPosition = new THREE.Vector3().copy(targetVector).add(defenderOffset).normalize().multiplyScalar(radius + 2);

        // Create defender icon (shield shape)
        const shieldGeometry = new THREE.ConeGeometry(1.5, 3, 8);
        const shieldMaterial = new THREE.MeshBasicMaterial({
          color: 0x00ffff,
          transparent: true,
          opacity: 0.8
        });

        const shield = new THREE.Mesh(shieldGeometry, shieldMaterial);
        shield.position.copy(defenderPosition);

        // Orient shield to face outward from globe center
        shield.lookAt(0, 0, 0);
        shield.rotateX(Math.PI); // Flip to point outward

        sceneRef.current.add(shield);
        defenderIconsRef.current.push(shield);

        // Add shield effect
        const shieldEffectGeometry = new THREE.SphereGeometry(3, 16, 16);
        const shieldEffectMaterial = new THREE.MeshBasicMaterial({
          color: 0x00ffff,
          transparent: true,
          opacity: 0.2,
          side: THREE.DoubleSide
        });

        const shieldEffect = new THREE.Mesh(shieldEffectGeometry, shieldEffectMaterial);
        shieldEffect.position.copy(defenderPosition);
        sceneRef.current.add(shieldEffect);
        defenderIconsRef.current.push(shieldEffect);

        // Add pulse animation to shield effect
        const shieldPulseAnimation = () => {
          if (shieldEffect.material) {
            shieldEffect.material.opacity = 0.1 + Math.sin(Date.now() * 0.002) * 0.1;
            shieldEffect.scale.setScalar(1 + Math.sin(Date.now() * 0.002) * 0.3);
          }
          requestAnimationFrame(shieldPulseAnimation);
        };
        shieldPulseAnimation();
      }
    });

    // Set a random threat as active
    setActiveThreat(threats[Math.floor(Math.random() * threats.length)]);

  }, [threats]);

  // Fetch threat data on component mount
  useEffect(() => {
    const loadThreatData = async () => {
      const realThreats = await fetchThreatData();
      setThreats(realThreats);

      // Set a random threat as active
      if (realThreats.length > 0) {
        setActiveThreat(realThreats[Math.floor(Math.random() * realThreats.length)]);
      }
    };

    loadThreatData();

    // Backend-like data management with very slow updates
    // This simulates having a backend that pushes data to the frontend
    const threatQueue = [];
    let isProcessingQueue = false;

    // Pre-generate a minimal number of threats and queue them
    for (let i = 0; i < 5; i++) { // Reduced to 5 for better performance
      threatQueue.push(generateFallbackThreats(1)[0]);
    }

    // Process one threat at a time with a delay between each
    const processQueue = () => {
      if (threatQueue.length === 0 || isProcessingQueue) return;

      isProcessingQueue = true;

      setTimeout(() => {
        setThreats(prevThreats => {
          // Keep a very small number of threats for maximum performance
          let newThreats = [...prevThreats];

          // If we have too many threats, remove the oldest one
          if (newThreats.length >= renderLimit) {
            // Sort by timestamp and remove oldest
            newThreats.sort((a, b) => b.timestamp - a.timestamp);
            newThreats = newThreats.slice(0, renderLimit - 1);
          }

          // Add one threat from the queue
          const newThreat = threatQueue.shift();
          const updatedThreats = [...newThreats, newThreat];

          // Only update active threat very occasionally (5% chance)
          if (Math.random() < 0.05 && updatedThreats.length > 0) {
            // Use the newest threat as active to create a sense of focus
            setActiveThreat(newThreat);
          }

          // Add a new threat to the queue to replace the one we used
          setTimeout(() => {
            threatQueue.push(generateFallbackThreats(1)[0]);
            isProcessingQueue = false;
            processQueue(); // Process next item in queue
          }, 1000); // 1 second delay before adding new threats to queue

          return updatedThreats;
        });
      }, dataUpdateSpeed);
    };

    // Start the queue processing
    const queueInterval = setInterval(processQueue, dataUpdateSpeed);

    // Very rarely show quiz (1% chance per update)
    const quizInterval = setInterval(() => {
      if (Math.random() < 0.01 && !showQuiz) {
        const randomQuiz = quizQuestions[Math.floor(Math.random() * quizQuestions.length)];
        setCurrentQuiz(randomQuiz);
        setShowQuiz(true);
      }
    }, dataUpdateSpeed * 2); // Even slower quiz frequency

    // Full refresh every 3 minutes
    const refreshInterval = setInterval(() => {
      loadThreatData();
    }, 180000); // 3 minutes

    return () => {
      clearInterval(queueInterval);
      clearInterval(quizInterval);
      clearInterval(refreshInterval);
    };
  }, [showQuiz, quizQuestions, dataUpdateSpeed, renderLimit]);

  return (
    <div className="relative">
      {/* Achievement notification */}
      {showAchievement && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10 bg-yellow-500/90 text-black px-4 py-2 rounded-lg shadow-lg animate-bounce">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zm7-10a1 1 0 01.707.293l.707.707L15.414 4a1 1 0 111.414 1.414l-1.414 1.414.707.707a1 1 0 01-1.414 1.414L14 7.242l-1.414 1.414A1 1 0 1111.172 7.24L12.586 5.83l-.707-.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            <span className="font-bold">{achievementText}</span>
          </div>
        </div>
      )}

      {/* Interactive control panel */}
      <div className="absolute top-4 right-4 z-10 bg-black/70 backdrop-blur-sm text-white px-3 py-2 rounded-lg border border-gray-700/50 text-xs">
        <div className="flex flex-col gap-2">
          {/* Mode selection tabs */}
          <div className="flex border border-gray-700 rounded-lg overflow-hidden">
            <button
              onClick={() => setInteractionMode('observe')}
              className={`px-3 py-1.5 flex-1 ${interactionMode === 'observe' ? 'bg-[#88cc14]/20 text-[#88cc14]' : 'bg-gray-800/50 text-gray-400 hover:bg-gray-800'}`}
            >
              Observe
            </button>
            <button
              onClick={() => setInteractionMode('defend')}
              className={`px-3 py-1.5 flex-1 ${interactionMode === 'defend' ? 'bg-[#88cc14]/20 text-[#88cc14]' : 'bg-gray-800/50 text-gray-400 hover:bg-gray-800'}`}
            >
              Defend
            </button>
            <button
              onClick={() => setInteractionMode('analyze')}
              className={`px-3 py-1.5 flex-1 ${interactionMode === 'analyze' ? 'bg-[#88cc14]/20 text-[#88cc14]' : 'bg-gray-800/50 text-gray-400 hover:bg-gray-800'}`}
            >
              Analyze
            </button>
          </div>

          {/* Mode-specific content */}
          {interactionMode === 'observe' && (
            <div className="flex flex-col gap-1">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Security Index:</span>
                <div className="flex items-center">
                  <div className="w-24 h-2 bg-gray-800 rounded-full overflow-hidden mr-2">
                    <div
                      className={`h-full rounded-full ${stats.securityIndex > 75 ? 'bg-green-500' : stats.securityIndex > 50 ? 'bg-yellow-500' : 'bg-red-500'}`}
                      style={{ width: `${stats.securityIndex}%` }}
                    ></div>
                  </div>
                  <span className="font-medium">{stats.securityIndex}%</span>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Threats Detected:</span>
                <span className="font-medium">{stats.totalThreatsDetected}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Unique Threat Types:</span>
                <span className="font-medium">{Object.keys(stats.threatsByType).length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Your Score:</span>
                <span className="font-medium">{userScore}</span>
              </div>
            </div>
          )}

          {interactionMode === 'defend' && (
            <div className="flex flex-col gap-2">
              <p className="text-center text-gray-300">Select a threat to defend against</p>
              <div className="grid grid-cols-2 gap-1">
                {threats.slice(0, 4).map((threat, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setSelectedThreat(threat);
                      // Simulate successful defense
                      setUserScore(prev => prev + (threat.severity * 10));
                      setShowAchievement(true);
                      setAchievementText(`Defended against ${threat.type}!`);
                      setTimeout(() => setShowAchievement(false), 2000);
                    }}
                    className="px-2 py-1 bg-gray-800/80 hover:bg-gray-700/80 rounded border border-gray-700 text-center"
                  >
                    <div className="flex flex-col items-center">
                      <span className={`inline-block w-2 h-2 rounded-full mb-1 ${
                        threat.severity === 3 ? 'bg-red-500' :
                        threat.severity === 2 ? 'bg-orange-500' : 'bg-yellow-500'
                      }`}></span>
                      <span>{threat.type}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {interactionMode === 'analyze' && (
            <div className="flex flex-col gap-2">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Top Threat Types:</span>
                <span className="font-medium">{Object.keys(stats.threatsByType)[0] || 'None'}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Top Source:</span>
                <span className="font-medium">
                  {Object.entries(stats.threatsByCountry).sort((a, b) => b[1] - a[1])[0]?.[0] || 'Unknown'}
                </span>
              </div>
              <div className="mt-1 p-1 bg-gray-800/50 rounded border border-gray-700">
                <div className="text-center mb-1 text-gray-300">Threat Prediction</div>
                <div className="text-center text-[#88cc14] font-medium">
                  {Math.random() > 0.5 ? 'DDoS' : 'Ransomware'} likely in next 24h
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div
        ref={containerRef}
        style={{ width, height }}
        className="cyber-threat-globe"
      />

      {/* Quiz popup */}
      {showQuiz && currentQuiz && (
        <div className="absolute inset-0 flex items-center justify-center z-20">
          <div className="bg-black/90 backdrop-blur-md p-6 rounded-xl border border-gray-700 max-w-md w-full shadow-2xl">
            <h3 className="text-[#88cc14] text-lg font-bold mb-4">Cyber Threat Quiz</h3>
            <p className="text-white mb-6">{currentQuiz.question}</p>

            <div className="grid grid-cols-1 gap-2 mb-4">
              {currentQuiz.options.map((option, index) => (
                <button
                  key={index}
                  onClick={() => {
                    if (option === currentQuiz.correctAnswer) {
                      setUserScore(prev => prev + 25);
                      setShowAchievement(true);
                      setAchievementText('Correct Answer! +25 points');
                      setTimeout(() => setShowAchievement(false), 2000);
                    } else {
                      setShowAchievement(true);
                      setAchievementText(`Incorrect! The answer was ${currentQuiz.correctAnswer}`);
                      setTimeout(() => setShowAchievement(false), 2000);
                    }
                    setShowQuiz(false);
                  }}
                  className="bg-gray-800 hover:bg-gray-700 text-white py-2 px-4 rounded border border-gray-700 transition-colors"
                >
                  {option}
                </button>
              ))}
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => setShowQuiz(false)}
                className="text-gray-400 hover:text-white text-sm"
              >
                Skip
              </button>
            </div>
          </div>
        </div>
      )}

      {activeThreat && (
        <div className="absolute bottom-4 left-4 right-4 bg-black/85 backdrop-blur-sm text-white p-4 rounded-lg text-sm border border-gray-700/50">
          <div className="flex flex-col gap-2">
            {/* Header with threat type and route */}
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                {/* Threat type icon based on threat type */}
                {activeThreat.type === 'Ransomware' && (
                  <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-500/20 mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </span>
                )}
                {activeThreat.type === 'DDoS' && (
                  <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-500/20 mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                  </span>
                )}
                {activeThreat.type === 'Phishing' && (
                  <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-500/20 mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </span>
                )}
                {activeThreat.type === 'Malware' && (
                  <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-500/20 mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                    </svg>
                  </span>
                )}
                {(activeThreat.type !== 'Ransomware' && activeThreat.type !== 'DDoS' &&
                  activeThreat.type !== 'Phishing' && activeThreat.type !== 'Malware') && (
                  <span className={`inline-block w-6 h-6 rounded-full mr-2 flex items-center justify-center ${
                    activeThreat.severity === 3 ? 'bg-red-500/20' :
                    activeThreat.severity === 2 ? 'bg-orange-500/20' : 'bg-yellow-500/20'
                  }`}>
                    <span className={`inline-block w-3 h-3 rounded-full ${
                      activeThreat.severity === 3 ? 'bg-red-500 animate-pulse' :
                      activeThreat.severity === 2 ? 'bg-orange-500' : 'bg-yellow-500'
                    }`}></span>
                  </span>
                )}
                <span className="font-bold">{activeThreat.type}</span> threat detected

                {/* Achievement notification for rare threats */}
                {(activeThreat.type === 'APT' || activeThreat.type === 'Zero-day' ||
                 (activeThreat.details && activeThreat.details.reports && activeThreat.details.reports > 1000)) && (
                  <span className="ml-2 bg-yellow-500/20 text-yellow-400 text-xs px-2 py-0.5 rounded-full">
                    Rare Find!
                  </span>
                )}
              </div>
              <div className="flex items-center">
                <span className="font-medium text-gray-300">{activeThreat.source.name}</span>
                <span className="mx-2 text-gray-500">→</span>
                <span className="font-medium text-gray-300">{activeThreat.target.name}</span>
              </div>
            </div>

            {/* Threat details with narrative */}
            {activeThreat.details && (
              <div className="flex flex-col gap-2 border-t border-gray-700/50 pt-2">
                <div className="text-xs text-gray-300 font-medium">
                  {activeThreat.details.name || 'Unknown threat'}
                </div>

                {/* Threat narrative based on type */}
                <div className="text-xs text-gray-400 italic">
                  {activeThreat.type === 'Ransomware' && 'This attack encrypts victim data and demands payment for decryption keys.'}
                  {activeThreat.type === 'DDoS' && 'This attack floods servers with traffic to disrupt services and cause outages.'}
                  {activeThreat.type === 'Phishing' && 'This attack tricks users into revealing sensitive information through deception.'}
                  {activeThreat.type === 'Malware' && 'This attack uses malicious software to compromise systems and steal data.'}
                  {activeThreat.type === 'Data Breach' && 'This attack extracts sensitive data from compromised systems.'}
                  {activeThreat.type === 'Brute Force' && 'This attack attempts to crack passwords through repeated login attempts.'}
                  {activeThreat.type === 'Port Scan' && 'This reconnaissance technique identifies vulnerable services on target systems.'}
                </div>

                {/* Technical details */}
                <div className="flex justify-between items-center text-xs">
                  {activeThreat.details.ip && (
                    <span className="text-gray-400">IP: {activeThreat.details.ip}</span>
                  )}
                  {activeThreat.details.reports && (
                    <span className="text-gray-400 bg-gray-800 px-2 py-0.5 rounded">
                      {activeThreat.details.reports} reports
                    </span>
                  )}
                </div>

                {/* Did you know fact */}
                <div className="mt-1 text-xs bg-blue-900/30 border border-blue-800/30 rounded p-2 flex items-start">
                  <span className="text-blue-400 font-bold mr-1">Did you know:</span>
                  <span className="text-gray-300">
                    {activeThreat.type === 'Ransomware' && 'Ransomware attacks increased by 150% in 2021, with an average ransom payment of $220,000.'}
                    {activeThreat.type === 'DDoS' && 'The largest DDoS attack ever recorded reached 3.47 Tbps, targeting Azure servers in 2022.'}
                    {activeThreat.type === 'Phishing' && '90% of data breaches start with a phishing email, making it the most common attack vector.'}
                    {activeThreat.type === 'Malware' && 'Over 450,000 new malware samples are detected every day, with most targeting Windows systems.'}
                    {activeThreat.type === 'Data Breach' && 'The average cost of a data breach reached $4.35 million in 2022, a 13% increase since 2020.'}
                    {activeThreat.type === 'Brute Force' && '80% of data breaches involve brute force or stolen credentials.'}
                    {activeThreat.type === 'Port Scan' && 'Port scanning is often the first step in 70% of targeted cyber attacks.'}
                    {(activeThreat.type !== 'Ransomware' && activeThreat.type !== 'DDoS' &&
                      activeThreat.type !== 'Phishing' && activeThreat.type !== 'Malware' &&
                      activeThreat.type !== 'Data Breach' && activeThreat.type !== 'Brute Force' &&
                      activeThreat.type !== 'Port Scan') &&
                      'Organizations with strong security awareness training report 70% fewer successful cyber attacks.'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CyberThreatGlobe;
