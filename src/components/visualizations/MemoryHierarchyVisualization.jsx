import React from 'react';
import { motion } from 'framer-motion';

const MemoryHierarchyVisualization = ({ data }) => {
  return (
    <div className="space-y-4">
      {data.levels.map((level, index) => (
        <motion.div
          key={index}
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: index * 0.2 }}
          className="bg-gray-900 p-4 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-2 h-12 bg-[#88cc14] rounded" 
                style={{ opacity: 1 - (index * 0.2) }}
              />
              <div>
                <h3 className="text-white font-bold">{level.name}</h3>
                <p className="text-gray-400 text-sm">Speed: {level.speed}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-[#88cc14]">{level.size}</p>
              <p className="text-gray-400 text-sm">{level.accessTime}</p>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default MemoryHierarchyVisualization;