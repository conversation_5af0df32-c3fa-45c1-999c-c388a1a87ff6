import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FaChartBar, FaChartLine, FaChart<PERSON>ie, FaExclamationTriangle } from 'react-icons/fa';

/**
 * AnalyticsChart Component
 * 
 * A versatile chart component for analytics visualizations.
 * Supports line, bar, and pie charts with customizable options.
 * 
 * Note: In a real application, this would use a charting library like Chart.js,
 * D3.js, or Recharts. This is a simplified version for demonstration purposes.
 */
const AnalyticsChart = ({
  type = 'line',
  data = [],
  labels = [],
  title = '',
  height = 200,
  colors = ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444'],
  showLegend = true,
  showGrid = true,
  animate = true,
  loading = false,
  error = null
}) => {
  const canvasRef = useRef(null);
  
  // Draw chart on canvas
  useEffect(() => {
    if (loading || error || !data.length) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw based on chart type
    switch (type) {
      case 'line':
        drawLineChart(ctx, data, labels, width, height, colors, showGrid);
        break;
      case 'bar':
        drawBarChart(ctx, data, labels, width, height, colors, showGrid);
        break;
      case 'pie':
        drawPieChart(ctx, data, labels, width, height, colors);
        break;
      default:
        drawLineChart(ctx, data, labels, width, height, colors, showGrid);
    }
  }, [type, data, labels, colors, showGrid, loading, error]);
  
  // Draw line chart
  const drawLineChart = (ctx, data, labels, width, height, colors, showGrid) => {
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    // Draw grid
    if (showGrid) {
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 0.5;
      
      // Horizontal grid lines
      for (let i = 0; i <= 5; i++) {
        const y = padding + (chartHeight / 5) * i;
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(width - padding, y);
        ctx.stroke();
      }
      
      // Vertical grid lines
      for (let i = 0; i < labels.length; i++) {
        const x = padding + (chartWidth / (labels.length - 1)) * i;
        ctx.beginPath();
        ctx.moveTo(x, padding);
        ctx.lineTo(x, height - padding);
        ctx.stroke();
      }
    }
    
    // Draw axes
    ctx.strokeStyle = '#9ca3af';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();
    
    // Draw labels
    ctx.fillStyle = '#6b7280';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    
    for (let i = 0; i < labels.length; i++) {
      const x = padding + (chartWidth / (labels.length - 1)) * i;
      ctx.fillText(labels[i], x, height - padding + 15);
    }
    
    // Draw data series
    const maxValue = Math.max(...data.flat());
    
    for (let seriesIndex = 0; seriesIndex < data.length; seriesIndex++) {
      const seriesData = data[seriesIndex];
      const color = colors[seriesIndex % colors.length];
      
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.beginPath();
      
      for (let i = 0; i < seriesData.length; i++) {
        const x = padding + (chartWidth / (seriesData.length - 1)) * i;
        const y = height - padding - (seriesData[i] / maxValue) * chartHeight;
        
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      
      ctx.stroke();
      
      // Draw points
      ctx.fillStyle = color;
      for (let i = 0; i < seriesData.length; i++) {
        const x = padding + (chartWidth / (seriesData.length - 1)) * i;
        const y = height - padding - (seriesData[i] / maxValue) * chartHeight;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  };
  
  // Draw bar chart
  const drawBarChart = (ctx, data, labels, width, height, colors, showGrid) => {
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    // Draw grid
    if (showGrid) {
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 0.5;
      
      // Horizontal grid lines
      for (let i = 0; i <= 5; i++) {
        const y = padding + (chartHeight / 5) * i;
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(width - padding, y);
        ctx.stroke();
      }
    }
    
    // Draw axes
    ctx.strokeStyle = '#9ca3af';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();
    
    // Draw labels
    ctx.fillStyle = '#6b7280';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    
    const barWidth = chartWidth / labels.length / (data.length + 0.5);
    
    for (let i = 0; i < labels.length; i++) {
      const x = padding + (chartWidth / labels.length) * (i + 0.5);
      ctx.fillText(labels[i], x, height - padding + 15);
    }
    
    // Draw data series
    const maxValue = Math.max(...data.flat());
    
    for (let seriesIndex = 0; seriesIndex < data.length; seriesIndex++) {
      const seriesData = data[seriesIndex];
      const color = colors[seriesIndex % colors.length];
      
      ctx.fillStyle = color;
      
      for (let i = 0; i < seriesData.length; i++) {
        const barHeight = (seriesData[i] / maxValue) * chartHeight;
        const x = padding + (chartWidth / labels.length) * (i + 0.5) - (barWidth * data.length) / 2 + barWidth * seriesIndex;
        const y = height - padding - barHeight;
        
        ctx.fillRect(x, y, barWidth, barHeight);
      }
    }
  };
  
  // Draw pie chart
  const drawPieChart = (ctx, data, labels, width, height, colors) => {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) - 40;
    
    const total = data.reduce((sum, value) => sum + value, 0);
    let startAngle = 0;
    
    for (let i = 0; i < data.length; i++) {
      const sliceAngle = (2 * Math.PI * data[i]) / total;
      const endAngle = startAngle + sliceAngle;
      const color = colors[i % colors.length];
      
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();
      ctx.fill();
      
      // Draw label
      if (labels[i]) {
        const labelAngle = startAngle + sliceAngle / 2;
        const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
        const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(labels[i], labelX, labelY);
      }
      
      startAngle = endAngle;
    }
  };
  
  // Get chart icon based on type
  const getChartIcon = () => {
    switch (type) {
      case 'line':
        return <FaChartLine className="text-blue-500" />;
      case 'bar':
        return <FaChartBar className="text-green-500" />;
      case 'pie':
        return <FaChartPie className="text-purple-500" />;
      default:
        return <FaChartLine className="text-blue-500" />;
    }
  };
  
  // Render loading state
  if (loading) {
    return (
      <div 
        className="bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center"
        style={{ height: `${height}px` }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading chart data...</p>
        </div>
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <div 
        className="bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center"
        style={{ height: `${height}px` }}
      >
        <div className="text-center text-red-500 dark:text-red-400">
          <FaExclamationTriangle className="mx-auto text-2xl mb-2" />
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }
  
  // Render empty state
  if (!data.length) {
    return (
      <div 
        className="bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center"
        style={{ height: `${height}px` }}
      >
        <div className="text-center">
          <div className="text-gray-400 dark:text-gray-500 mb-2">
            {getChartIcon()}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">No data available</p>
        </div>
      </div>
    );
  }
  
  return (
    <motion.div 
      initial={animate ? { opacity: 0, y: 20 } : false}
      animate={animate ? { opacity: 1, y: 0 } : false}
      className="bg-white dark:bg-gray-800 rounded-lg p-4"
    >
      {title && (
        <div className="flex items-center mb-4">
          <div className="mr-2">
            {getChartIcon()}
          </div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">{title}</h4>
        </div>
      )}
      
      <div className="relative">
        <canvas 
          ref={canvasRef} 
          width={500} 
          height={height}
          className="w-full h-auto"
        />
      </div>
      
      {showLegend && data.length > 1 && (
        <div className="mt-4 flex flex-wrap justify-center gap-4">
          {data.map((_, index) => (
            <div key={index} className="flex items-center">
              <div 
                className="w-3 h-3 rounded-full mr-1"
                style={{ backgroundColor: colors[index % colors.length] }}
              />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {labels[index] || `Series ${index + 1}`}
              </span>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default AnalyticsChart;
