import React, { useState } from 'react';
import { motion } from 'framer-motion';

const PagingVisualization = ({ data }) => {
  const [selectedPage, setSelectedPage] = useState(null);

  return (
    <div className="space-y-6">
      <div className="bg-gray-900 p-4 rounded-lg">
        <h3 className="text-white font-bold mb-2">Page Size: {data.pageSize} bytes</h3>
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div>
            <h4 className="text-gray-400 mb-2">Virtual Memory Pages</h4>
            <div className="space-y-2">
              {data.pages.map((page, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => setSelectedPage(page)}
                  className={`p-3 rounded cursor-pointer transition-colors ${
                    selectedPage === page
                      ? 'bg-[#88cc14]/20 border border-[#88cc14]'
                      : 'bg-gray-800 hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-white">Page {page.id}</span>
                    <span className={`px-2 py-1 rounded text-sm ${
                      page.status === 'mapped'
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-yellow-500/20 text-yellow-400'
                    }`}>
                      {page.status}
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
          <div>
            <h4 className="text-gray-400 mb-2">Physical Memory Frames</h4>
            <div className="space-y-2">
              {data.pages
                .filter(page => page.status === 'mapped')
                .map((page, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-gray-800 p-3 rounded"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-white">Frame {page.frame}</span>
                      <span className="text-[#88cc14]">← Page {page.id}</span>
                    </div>
                  </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PagingVisualization;