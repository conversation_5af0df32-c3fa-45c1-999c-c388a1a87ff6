import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import RealTimeThreatService from '../../services/RealTimeThreatService';
import { FaInfoCircle, FaExclamationTriangle, FaServer, FaExclamation } from 'react-icons/fa';

// Enhanced 2D map visualization with more detailed country outlines and interactive features
const EnhancedThreatMap = ({ 
  width = '100%', 
  height = '500px',
  filterType = 'all',
  filterSeverity = 'all'
}) => {
  const { darkMode } = useGlobalTheme();
  const [threats, setThreats] = useState([]);
  const [activeThreat, setActiveThreat] = useState(null);
  const [showThreatDetails, setShowThreatDetails] = useState(false);
  const [hoveredCountry, setHoveredCountry] = useState(null);
  const [mapScale, setMapScale] = useState(1);
  const [mapPosition, setMapPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const mapRef = useRef(null);
  
  // More detailed country data with coordinates for better visualization
  const countries = [
    { name: 'United States', code: 'US', x: 100, y: 120, width: 80, height: 50, hotspot: true },
    { name: 'China', code: 'CN', x: 350, y: 130, width: 60, height: 40, hotspot: true },
    { name: 'Russia', code: 'RU', x: 300, y: 80, width: 100, height: 40, hotspot: true },
    { name: 'Brazil', code: 'BR', x: 150, y: 220, width: 40, height: 30, hotspot: true },
    { name: 'India', code: 'IN', x: 320, y: 150, width: 30, height: 30, hotspot: true },
    { name: 'United Kingdom', code: 'GB', x: 200, y: 100, width: 15, height: 15, hotspot: true },
    { name: 'Germany', code: 'DE', x: 220, y: 110, width: 20, height: 15, hotspot: true },
    { name: 'France', code: 'FR', x: 210, y: 115, width: 20, height: 15, hotspot: false },
    { name: 'Australia', code: 'AU', x: 380, y: 250, width: 40, height: 30, hotspot: true },
    { name: 'Canada', code: 'CA', x: 100, y: 90, width: 70, height: 30, hotspot: false },
    { name: 'Japan', code: 'JP', x: 400, y: 130, width: 20, height: 20, hotspot: true },
    { name: 'South Korea', code: 'KR', x: 380, y: 130, width: 15, height: 15, hotspot: false },
    { name: 'Iran', code: 'IR', x: 290, y: 140, width: 25, height: 20, hotspot: true },
    { name: 'North Korea', code: 'KP', x: 375, y: 125, width: 15, height: 10, hotspot: true },
    { name: 'Italy', code: 'IT', x: 230, y: 125, width: 15, height: 20, hotspot: false },
    { name: 'Spain', code: 'ES', x: 200, y: 130, width: 20, height: 15, hotspot: false },
    { name: 'Ukraine', code: 'UA', x: 250, y: 110, width: 25, height: 15, hotspot: true },
    { name: 'South Africa', code: 'ZA', x: 240, y: 250, width: 25, height: 20, hotspot: false },
    { name: 'Mexico', code: 'MX', x: 80, y: 150, width: 30, height: 20, hotspot: false },
    { name: 'Israel', code: 'IL', x: 270, y: 140, width: 10, height: 10, hotspot: false }
  ];
  
  // Initialize threat service and get real-time data
  useEffect(() => {
    const threatService = RealTimeThreatService.getInstance().initialize();
    
    // Generate initial threats
    const generateThreats = () => {
      const attacks = threatService.getActiveAttacks();
      
      // Convert attacks to the format our map component expects
      const mappedThreats = attacks.map(attack => {
        // Find source and target countries
        const sourceCountry = countries.find(c => c.name === attack.source);
        const targetCountry = countries.find(c => c.name === attack.target);
        
        if (!sourceCountry || !targetCountry) return null;
        
        return {
          id: attack.id,
          type: attack.type,
          severity: attack.severity === 'Critical' ? 3 : 
                   attack.severity === 'High' ? 2 : 1,
          source: {
            name: sourceCountry.name,
            code: sourceCountry.code,
            x: sourceCountry.x,
            y: sourceCountry.y
          },
          target: {
            name: targetCountry.name,
            code: targetCountry.code,
            x: targetCountry.x,
            y: targetCountry.y
          },
          color: attack.color,
          timestamp: new Date(attack.timestamp).getTime(),
          details: {
            name: `${attack.type} Attack`,
            description: `${attack.severity} severity attack from ${attack.source} targeting ${attack.target}`,
            reports: Math.floor(Math.random() * 100) + 1
          }
        };
      }).filter(Boolean);
      
      setThreats(mappedThreats);
    };
    
    // Initial generation
    generateThreats();
    
    // Add listener for updates
    threatService.addListener(() => {
      generateThreats();
    });
    
    // Start real-time updates
    threatService.startRealTimeUpdates();
    
    // Cleanup
    return () => {
      threatService.removeListener(generateThreats);
      threatService.stopRealTimeUpdates();
    };
  }, []);
  
  // Filter threats based on selected filters
  const filteredThreats = threats.filter(threat => {
    // Filter by type
    if (filterType !== 'all' && threat.type !== filterType) {
      return false;
    }
    
    // Filter by severity
    if (filterSeverity !== 'all') {
      const severityMap = {
        'Critical': 3,
        'High': 2,
        'Medium': 1,
        'Low': 1
      };
      
      if (threat.severity !== severityMap[filterSeverity]) {
        return false;
      }
    }
    
    return true;
  });
  
  // Handle map zooming
  const handleWheel = (e) => {
    e.preventDefault();
    const delta = e.deltaY * -0.01;
    const newScale = Math.min(Math.max(mapScale + delta, 0.5), 3);
    setMapScale(newScale);
  };
  
  // Handle map dragging
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
  };
  
  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    const dx = e.clientX - dragStart.x;
    const dy = e.clientY - dragStart.y;
    
    setMapPosition({
      x: mapPosition.x + dx,
      y: mapPosition.y + dy
    });
    
    setDragStart({ x: e.clientX, y: e.clientY });
  };
  
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  // Handle country hover
  const handleCountryHover = (country) => {
    setHoveredCountry(country);
  };
  
  // Handle country click
  const handleCountryClick = (country) => {
    // Find threats related to this country
    const relatedThreats = filteredThreats.filter(
      threat => threat.source.name === country.name || threat.target.name === country.name
    );
    
    if (relatedThreats.length > 0) {
      setActiveThreat(relatedThreats[0]);
      setShowThreatDetails(true);
    }
  };
  
  // Handle threat click
  const handleThreatClick = (threat) => {
    setActiveThreat(threat);
    setShowThreatDetails(true);
  };
  
  // Get color for threat severity
  const getThreatColor = (severity) => {
    switch (severity) {
      case 3: return '#ff0000'; // Critical
      case 2: return '#ff6600'; // High
      case 1: return '#ffcc00'; // Medium/Low
      default: return '#ffcc00';
    }
  };
  
  // Get icon for threat type
  const getThreatIcon = (type) => {
    switch (type) {
      case 'Ransomware': return <FaExclamationTriangle />;
      case 'DDoS': return <FaServer />;
      case 'Phishing': return <FaExclamation />;
      case 'Malware': return <FaExclamationTriangle />;
      case 'Data Breach': return <FaExclamation />;
      case 'Brute Force': return <FaServer />;
      case 'Port Scan': return <FaServer />;
      default: return <FaExclamationTriangle />;
    }
  };
  
  // Format time ago
  const formatTimeAgo = (timestamp) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    
    if (seconds < 60) return `${seconds} seconds ago`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)} minutes ago`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)} hours ago`;
    return `${Math.floor(seconds / 86400)} days ago`;
  };
  
  // Reset map position and scale
  const resetMap = () => {
    setMapScale(1);
    setMapPosition({ x: 0, y: 0 });
  };
  
  return (
    <div className="relative" style={{ width, height }}>
      {/* Map Controls */}
      <div className="absolute top-4 right-4 z-20 flex gap-2">
        <button 
          onClick={() => setMapScale(prev => Math.min(prev + 0.2, 3))}
          className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-gray-700"
        >
          +
        </button>
        <button 
          onClick={() => setMapScale(prev => Math.max(prev - 0.2, 0.5))}
          className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-gray-700"
        >
          -
        </button>
        <button 
          onClick={resetMap}
          className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-gray-700"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>
      
      {/* Map Container */}
      <div 
        ref={mapRef}
        className="relative w-full h-full bg-gray-900 rounded-lg overflow-hidden border border-gray-800"
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
      >
        {/* World Map Background */}
        <div className="absolute inset-0 opacity-20">
          <img 
            src="/world-map-simple.png" 
            alt="World Map" 
            className="w-full h-full object-cover"
            style={{ 
              transform: `scale(${mapScale}) translate(${mapPosition.x / mapScale}px, ${mapPosition.y / mapScale}px)`,
              transformOrigin: 'center',
              transition: isDragging ? 'none' : 'transform 0.2s ease-out'
            }}
          />
        </div>
        
        {/* Country Outlines */}
        <div 
          className="absolute inset-0 pointer-events-none"
          style={{ 
            transform: `scale(${mapScale}) translate(${mapPosition.x / mapScale}px, ${mapPosition.y / mapScale}px)`,
            transformOrigin: 'center',
            transition: isDragging ? 'none' : 'transform 0.2s ease-out'
          }}
        >
          {countries.map(country => (
            <div 
              key={country.code}
              className={`absolute border ${
                hoveredCountry === country 
                  ? 'border-blue-500' 
                  : darkMode ? 'border-gray-700' : 'border-gray-400'
              } rounded-sm pointer-events-auto`}
              style={{ 
                left: `${country.x}px`, 
                top: `${country.y}px`,
                width: `${country.width}px`,
                height: `${country.height}px`,
                backgroundColor: hoveredCountry === country 
                  ? 'rgba(59, 130, 246, 0.2)' 
                  : 'transparent'
              }}
              onMouseEnter={() => handleCountryHover(country)}
              onMouseLeave={() => handleCountryHover(null)}
              onClick={() => handleCountryClick(country)}
            />
          ))}
        </div>
        
        {/* Threat Indicators */}
        <div 
          className="absolute inset-0 pointer-events-none"
          style={{ 
            transform: `scale(${mapScale}) translate(${mapPosition.x / mapScale}px, ${mapPosition.y / mapScale}px)`,
            transformOrigin: 'center',
            transition: isDragging ? 'none' : 'transform 0.2s ease-out'
          }}
        >
          {filteredThreats.map(threat => (
            <React.Fragment key={threat.id}>
              {/* Source Point */}
              <div 
                className="absolute w-2 h-2 rounded-full bg-red-500 pointer-events-auto cursor-pointer"
                style={{ 
                  left: `${threat.source.x}px`, 
                  top: `${threat.source.y}px`,
                  boxShadow: '0 0 8px #ff0000',
                  zIndex: 10
                }}
                onClick={() => handleThreatClick(threat)}
              />
              
              {/* Target Point */}
              <div 
                className="absolute w-3 h-3 rounded-full pointer-events-auto cursor-pointer"
                style={{ 
                  left: `${threat.target.x}px`, 
                  top: `${threat.target.y}px`,
                  backgroundColor: getThreatColor(threat.severity),
                  boxShadow: `0 0 10px ${getThreatColor(threat.severity)}`,
                  zIndex: 10
                }}
                onClick={() => handleThreatClick(threat)}
              >
                <div className="absolute w-full h-full rounded-full animate-ping" style={{ backgroundColor: getThreatColor(threat.severity), opacity: 0.5 }} />
              </div>
              
              {/* Connection Line */}
              <svg 
                className="absolute top-0 left-0 w-full h-full pointer-events-none"
                style={{ zIndex: 5 }}
              >
                <line 
                  x1={threat.source.x} 
                  y1={threat.source.y} 
                  x2={threat.target.x} 
                  y2={threat.target.y} 
                  stroke={getThreatColor(threat.severity)}
                  strokeWidth={threat.severity}
                  strokeDasharray="4"
                  strokeOpacity="0.6"
                />
              </svg>
            </React.Fragment>
          ))}
        </div>
        
        {/* Country Hotspots */}
        <div 
          className="absolute inset-0 pointer-events-none"
          style={{ 
            transform: `scale(${mapScale}) translate(${mapPosition.x / mapScale}px, ${mapPosition.y / mapScale}px)`,
            transformOrigin: 'center',
            transition: isDragging ? 'none' : 'transform 0.2s ease-out'
          }}
        >
          {countries.filter(country => country.hotspot).map(country => {
            // Count threats related to this country
            const relatedThreats = filteredThreats.filter(
              threat => threat.source.name === country.name || threat.target.name === country.name
            );
            
            if (relatedThreats.length === 0) return null;
            
            return (
              <div 
                key={`hotspot-${country.code}`}
                className="absolute pointer-events-auto cursor-pointer"
                style={{ 
                  left: `${country.x + country.width/2}px`, 
                  top: `${country.y + country.height/2}px`,
                  transform: 'translate(-50%, -50%)',
                  zIndex: 15
                }}
                onClick={() => handleCountryClick(country)}
              >
                <div className="relative">
                  <div className="absolute -inset-2 bg-red-500 rounded-full opacity-20 animate-pulse"></div>
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                  <div className="absolute top-3 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                    <div className="text-xs font-bold text-white bg-gray-800 bg-opacity-70 px-1.5 py-0.5 rounded">
                      {country.name} ({relatedThreats.length})
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Hover Country Info */}
      {hoveredCountry && (
        <div className="absolute bottom-4 left-4 bg-gray-800 bg-opacity-90 text-white p-2 rounded text-xs border border-gray-700 z-20">
          <div className="font-bold">{hoveredCountry.name}</div>
          <div className="text-gray-300">
            {filteredThreats.filter(t => 
              t.source.name === hoveredCountry.name || t.target.name === hoveredCountry.name
            ).length} active threats
          </div>
        </div>
      )}
      
      {/* Active Threat Info Panel */}
      {activeThreat && showThreatDetails && (
        <div className="absolute bottom-4 left-4 right-4 bg-black/85 backdrop-blur-sm text-white p-4 rounded-lg text-sm border border-gray-700/50 z-30">
          <div className="flex justify-between items-start">
            <div className="flex flex-col gap-2">
              {/* Header with threat type and route */}
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full mr-2 ${
                    activeThreat.severity === 3 ? 'bg-red-500/20' : 
                    activeThreat.severity === 2 ? 'bg-orange-500/20' : 
                    'bg-yellow-500/20'
                  }`}>
                    {getThreatIcon(activeThreat.type)}
                  </span>
                  <span className="font-medium">{activeThreat.type}</span>
                </div>
                <div className="text-xs text-gray-400 ml-4">
                  {activeThreat.source.name} → {activeThreat.target.name}
                </div>
              </div>

              {/* Threat details */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-gray-400">Severity:</span>
                  <div className="flex">
                    {Array.from({ length: activeThreat.severity }).map((_, i) => (
                      <span key={i} className="text-red-500">●</span>
                    ))}
                    {Array.from({ length: 3 - activeThreat.severity }).map((_, i) => (
                      <span key={i} className="text-gray-600">●</span>
                    ))}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Detected:</span>
                  <span>
                    {formatTimeAgo(activeThreat.timestamp)}
                  </span>
                </div>
                {activeThreat.details.reports && (
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-gray-400">Reports:</span>
                    <span className="text-gray-300 bg-gray-800 px-2 py-0.5 rounded">
                      {activeThreat.details.reports}
                    </span>
                  </div>
                )}
              </div>
            </div>
            
            <button 
              onClick={() => setShowThreatDetails(false)}
              className="text-gray-400 hover:text-white"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
          
          {/* Did you know fact */}
          <div className="mt-3 text-xs bg-blue-900/30 border border-blue-800/30 rounded p-2 flex items-start">
            <span className="text-blue-400 font-bold mr-1">Did you know:</span>
            <span className="text-gray-300">
              {activeThreat.type === 'Ransomware' && 'Ransomware attacks increased by 150% in 2021, with an average ransom payment of $220,000.'}
              {activeThreat.type === 'DDoS' && 'The largest DDoS attack ever recorded reached 3.47 Tbps, targeting Azure servers in 2022.'}
              {activeThreat.type === 'Phishing' && '90% of data breaches start with a phishing email, making it the most common attack vector.'}
              {activeThreat.type === 'Malware' && 'Over 450,000 new malware samples are detected every day, with most targeting Windows systems.'}
              {activeThreat.type === 'Data Breach' && 'The average cost of a data breach reached $4.35 million in 2022, a 13% increase since 2020.'}
              {activeThreat.type === 'Brute Force' && '80% of data breaches involve brute force or stolen credentials.'}
              {activeThreat.type === 'Port Scan' && 'Port scanning is often the first step in 70% of targeted cyber attacks.'}
              {(activeThreat.type !== 'Ransomware' && activeThreat.type !== 'DDoS' &&
                activeThreat.type !== 'Phishing' && activeThreat.type !== 'Malware' &&
                activeThreat.type !== 'Data Breach' && activeThreat.type !== 'Brute Force' &&
                activeThreat.type !== 'Port Scan') &&
                'Organizations with strong security awareness training report 70% fewer successful cyber attacks.'}
            </span>
          </div>
        </div>
      )}
      
      {/* Legend */}
      <div className="absolute top-4 left-4 bg-gray-800 bg-opacity-80 p-2 rounded-lg z-20">
        <div className="text-xs font-bold mb-1 text-white">Threat Severity</div>
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1.5">
            <div className="w-2 h-2 rounded-full" style={{ backgroundColor: '#ff0000' }}></div>
            <div className="text-xs text-white">Critical</div>
          </div>
          <div className="flex items-center gap-1.5">
            <div className="w-2 h-2 rounded-full" style={{ backgroundColor: '#ff6600' }}></div>
            <div className="text-xs text-white">High</div>
          </div>
          <div className="flex items-center gap-1.5">
            <div className="w-2 h-2 rounded-full" style={{ backgroundColor: '#ffcc00' }}></div>
            <div className="text-xs text-white">Medium/Low</div>
          </div>
        </div>
      </div>
      
      {/* Live attack counter */}
      <div className="absolute top-4 right-24 bg-gray-800 bg-opacity-80 p-2 rounded-lg z-20">
        <div className="text-xs font-bold mb-1 text-white">LIVE ATTACKS</div>
        <div className="text-xl font-bold flex items-center gap-2 text-white">
          <span className="text-red-500">{filteredThreats.length}</span>
          <span className="w-2 h-2 bg-red-500 rounded-full animate-ping"></span>
        </div>
      </div>
    </div>
  );
};

export default EnhancedThreatMap;
