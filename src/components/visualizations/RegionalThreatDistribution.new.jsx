import React, { useState } from 'react';
import { FaExclamationTriangle, FaGlobe, FaInfoCircle, FaShieldAlt, FaExternalLinkAlt } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

/**
 * RegionalThreatDistribution Component - Simplified Version
 *
 * Displays a visualization of threat distribution across different regions
 * with detailed breakdowns by attack type and severity.
 */
const RegionalThreatDistribution = () => {
  const { darkMode } = useGlobalTheme();
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [showInfoPanel, setShowInfoPanel] = useState(false);

  // Sample data for regional threat distribution
  const regionData = [
    {
      name: 'Asia Pacific',
      totalThreats: 1245,
      attackTypes: [
        { type: 'Ransomware', count: 320, color: '#ff3b30' },
        { type: 'Phishing', count: 280, color: '#ff9500' },
        { type: 'DDoS', count: 210, color: '#ffcc00' },
        { type: 'Data Breach', count: 175, color: '#007aff' },
        { type: 'Supply Chain', count: 95, color: '#5856d6' },
        { type: 'Zero-day Exploit', count: 85, color: '#ff2d55' },
        { type: 'Malware', count: 65, color: '#34c759' },
        { type: 'Other', count: 15, color: '#8e8e93' }
      ]
    },
    {
      name: 'Europe',
      totalThreats: 980,
      attackTypes: [
        { type: 'Ransomware', count: 250, color: '#ff3b30' },
        { type: 'Phishing', count: 220, color: '#ff9500' },
        { type: 'DDoS', count: 170, color: '#ffcc00' },
        { type: 'Data Breach', count: 140, color: '#007aff' },
        { type: 'Supply Chain', count: 75, color: '#5856d6' },
        { type: 'Zero-day Exploit', count: 65, color: '#ff2d55' },
        { type: 'Malware', count: 50, color: '#34c759' },
        { type: 'Other', count: 10, color: '#8e8e93' }
      ]
    },
    {
      name: 'North America',
      totalThreats: 760,
      attackTypes: [
        { type: 'Ransomware', count: 210, color: '#ff3b30' },
        { type: 'Phishing', count: 180, color: '#ff9500' },
        { type: 'DDoS', count: 120, color: '#ffcc00' },
        { type: 'Data Breach', count: 95, color: '#007aff' },
        { type: 'Supply Chain', count: 65, color: '#5856d6' },
        { type: 'Zero-day Exploit', count: 45, color: '#ff2d55' },
        { type: 'Malware', count: 35, color: '#34c759' },
        { type: 'Other', count: 10, color: '#8e8e93' }
      ]
    },
    {
      name: 'Middle East',
      totalThreats: 620,
      attackTypes: [
        { type: 'Ransomware', count: 180, color: '#ff3b30' },
        { type: 'Phishing', count: 140, color: '#ff9500' },
        { type: 'DDoS', count: 95, color: '#ffcc00' },
        { type: 'Data Breach', count: 75, color: '#007aff' },
        { type: 'Supply Chain', count: 45, color: '#5856d6' },
        { type: 'Zero-day Exploit', count: 40, color: '#ff2d55' },
        { type: 'Malware', count: 35, color: '#34c759' },
        { type: 'Other', count: 10, color: '#8e8e93' }
      ]
    }
  ];

  // Handle region selection
  const handleRegionSelect = (region) => {
    setSelectedRegion(region === selectedRegion ? null : region);
  };

  // Toggle info panel
  const toggleInfoPanel = () => {
    setShowInfoPanel(!showInfoPanel);
  };

  // Render the visualization using HTML/CSS instead of Canvas
  const renderVisualization = () => {
    return regionData.map((region, index) => {
      // Calculate total width for the bar (100% minus some padding)
      const totalWidth = 100;
      
      return (
        <div key={index} className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <div className="text-lg font-semibold">{region.name}</div>
            <div className="text-sm">{region.totalThreats.toLocaleString()} threats</div>
          </div>
          
          <div className="flex h-10 rounded-md overflow-hidden" style={{ width: '100%' }}>
            {region.attackTypes.map((attack, i) => {
              // Calculate width percentage based on count
              const widthPercentage = (attack.count / region.totalThreats) * 100;
              
              return (
                <div 
                  key={i}
                  style={{ 
                    width: `${widthPercentage}%`, 
                    backgroundColor: attack.color,
                    transition: 'width 0.3s ease'
                  }}
                  title={`${attack.type}: ${attack.count} (${Math.round(widthPercentage)}%)`}
                />
              );
            })}
          </div>
        </div>
      );
    });
  };
  
  // Render the legend
  const renderLegend = () => {
    // Get unique attack types from all regions
    const attackTypes = [];
    regionData.forEach(region => {
      region.attackTypes.forEach(attack => {
        if (!attackTypes.some(a => a.type === attack.type)) {
          attackTypes.push(attack);
        }
      });
    });
    
    return (
      <div className="flex flex-wrap gap-4 mt-4">
        {attackTypes.map((attack, index) => (
          <div key={index} className="flex items-center">
            <div 
              className="w-3 h-3 mr-2" 
              style={{ backgroundColor: attack.color }}
            />
            <span className="text-sm">{attack.type}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold flex items-center">
          <FaGlobe className="mr-2 text-green-400" /> Regional Threat Distribution
          <button 
            className="ml-2 text-gray-400 hover:text-gray-300"
            onClick={toggleInfoPanel}
            title="Information about regional threats"
          >
            <FaInfoCircle size={14} />
          </button>
        </h3>
        
        <div className="text-xs text-gray-400">
          Updated {new Date().toLocaleString()}
        </div>
      </div>

      {/* Information Panel */}
      {showInfoPanel && (
        <div className="mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm">
          <div className="flex justify-between items-start">
            <h4 className="font-semibold mb-2 flex items-center">
              <FaShieldAlt className="mr-1 text-green-400" /> Understanding Regional Threats
            </h4>
            <button 
              className="text-gray-400 hover:text-gray-300"
              onClick={toggleInfoPanel}
            >
              ×
            </button>
          </div>
          <p className="text-gray-300 mb-2">
            Different regions face unique cybersecurity threats based on geopolitical factors, 
            economic conditions, and the presence of state-sponsored threat actors. Understanding
            these regional patterns can help organizations implement targeted security controls.
          </p>
          <div className="flex items-center text-xs">
            <a
              href="https://www.cisa.gov/topics/cyber-threats-and-advisories"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 flex items-center"
            >
              Learn more about regional threats <FaExternalLinkAlt className="ml-1" size={10} />
            </a>
          </div>
        </div>
      )}

      {/* Error message */}
      <div className="mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm">
        <FaExclamationTriangle className="inline-block mr-2" />
        Unable to load regional threat data. Using sample data instead.
      </div>

      {/* Visualization */}
      <div className="mb-4 bg-gray-800 rounded-lg p-4">
        {renderVisualization()}
      </div>

      {/* Legend */}
      <div className="bg-gray-800 rounded-lg p-4">
        <h4 className="font-medium mb-2">Attack Types</h4>
        {renderLegend()}
      </div>
    </div>
  );
};

export default RegionalThreatDistribution;
