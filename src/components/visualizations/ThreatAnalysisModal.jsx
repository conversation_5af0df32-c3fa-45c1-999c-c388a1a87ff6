import React, { useState, useEffect } from 'react';
import threatAnalyzer from '../../utils/threatAnalyzer';

const ThreatAnalysisModal = ({ threats, onClose }) => {
  const [analysis, setAnalysis] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('summary');
  
  useEffect(() => {
    const performAnalysis = async () => {
      setLoading(true);
      try {
        const result = await threatAnalyzer.analyzeThreats(threats);
        setAnalysis(result);
      } catch (error) {
        console.error('Error analyzing threats:', error);
      } finally {
        setLoading(false);
      }
    };
    
    performAnalysis();
  }, [threats]);
  
  const renderSummaryTab = () => (
    <div className="space-y-4">
      <div className="bg-gray-900/50 p-4 rounded-lg">
        <p className="text-gray-200">{analysis?.summary}</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-gray-900/50 p-4 rounded-lg">
          <h4 className="text-[#88cc14] font-medium mb-2">Top Threat Types</h4>
          {analysis?.topThreatTypes.map((item, index) => (
            <div key={index} className="flex justify-between items-center mb-1">
              <span className="text-gray-300">{item.type}</span>
              <span className="text-gray-400 bg-gray-800 px-2 py-0.5 rounded">{item.count}</span>
            </div>
          ))}
        </div>
        
        <div className="bg-gray-900/50 p-4 rounded-lg">
          <h4 className="text-[#88cc14] font-medium mb-2">Risk Assessment</h4>
          <div className="flex items-center mb-2">
            <span className="text-gray-300 mr-2">Level:</span>
            <span className={`px-2 py-0.5 rounded font-medium ${
              analysis?.riskAssessment.level === 'High' ? 'bg-red-900/50 text-red-300' :
              analysis?.riskAssessment.level === 'Medium' ? 'bg-orange-900/50 text-orange-300' :
              'bg-green-900/50 text-green-300'
            }`}>
              {analysis?.riskAssessment.level}
            </span>
          </div>
          <div className="text-sm text-gray-400">
            {analysis?.riskAssessment.factors.map((factor, index) => (
              <div key={index} className="flex items-start mb-1">
                <span className="mr-2">•</span>
                <span>{factor}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="bg-gray-900/50 p-4 rounded-lg">
        <h4 className="text-[#88cc14] font-medium mb-2">Recommendations</h4>
        <ul className="list-disc pl-5 space-y-1 text-gray-300">
          {analysis?.recommendations.map((recommendation, index) => (
            <li key={index}>{recommendation}</li>
          ))}
        </ul>
      </div>
      
      <div className="bg-gray-900/50 p-4 rounded-lg">
        <h4 className="text-[#88cc14] font-medium mb-2">Predictions</h4>
        <ul className="list-disc pl-5 space-y-1 text-gray-300">
          {analysis?.predictions.map((prediction, index) => (
            <li key={index}>{prediction}</li>
          ))}
        </ul>
      </div>
    </div>
  );
  
  const renderDetailsTab = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-gray-900/50 p-4 rounded-lg">
          <h4 className="text-[#88cc14] font-medium mb-2">Top Source Countries</h4>
          {analysis?.topSourceCountries.map((item, index) => (
            <div key={index} className="flex justify-between items-center mb-1">
              <span className="text-gray-300">{item.country}</span>
              <span className="text-gray-400 bg-gray-800 px-2 py-0.5 rounded">{item.count}</span>
            </div>
          ))}
        </div>
        
        <div className="bg-gray-900/50 p-4 rounded-lg">
          <h4 className="text-[#88cc14] font-medium mb-2">Top Target Countries</h4>
          {analysis?.topTargetCountries.map((item, index) => (
            <div key={index} className="flex justify-between items-center mb-1">
              <span className="text-gray-300">{item.country}</span>
              <span className="text-gray-400 bg-gray-800 px-2 py-0.5 rounded">{item.count}</span>
            </div>
          ))}
        </div>
      </div>
      
      <div className="bg-gray-900/50 p-4 rounded-lg">
        <h4 className="text-[#88cc14] font-medium mb-2">Severity Distribution</h4>
        <div className="flex items-center space-x-4 mt-2">
          <div className="flex items-center">
            <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-2"></span>
            <span className="text-gray-300">Critical: {analysis?.severityDistribution[3] || 0}</span>
          </div>
          <div className="flex items-center">
            <span className="inline-block w-3 h-3 rounded-full bg-orange-500 mr-2"></span>
            <span className="text-gray-300">Warning: {analysis?.severityDistribution[2] || 0}</span>
          </div>
          <div className="flex items-center">
            <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
            <span className="text-gray-300">Alert: {analysis?.severityDistribution[1] || 0}</span>
          </div>
        </div>
      </div>
      
      {analysis?.potentialCampaigns.length > 0 && (
        <div className="bg-gray-900/50 p-4 rounded-lg">
          <h4 className="text-[#88cc14] font-medium mb-2">Potential Attack Campaigns</h4>
          {analysis.potentialCampaigns.map((campaign, index) => (
            <div key={index} className="mb-3 pb-3 border-b border-gray-800 last:border-0 last:mb-0 last:pb-0">
              <div className="flex justify-between items-center mb-1">
                <span className="text-gray-300 font-medium">{campaign.type} Campaign from {campaign.source}</span>
                <span className={`px-2 py-0.5 rounded text-xs ${
                  campaign.severity === 3 ? 'bg-red-900/50 text-red-300' :
                  campaign.severity === 2 ? 'bg-orange-900/50 text-orange-300' :
                  'bg-yellow-900/50 text-yellow-300'
                }`}>
                  {campaign.severity === 3 ? 'Critical' : campaign.severity === 2 ? 'Warning' : 'Alert'}
                </span>
              </div>
              <div className="text-sm text-gray-400">
                <div>{campaign.count} attacks targeting {campaign.targets.length} countries</div>
                <div className="mt-1">Targets: {campaign.targets.join(', ')}</div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
  
  return (
    <div className="fixed inset-0 flex items-center justify-center z-30 bg-black/70 backdrop-blur-sm">
      <div className="bg-[#0B1120] border border-gray-700 rounded-xl max-w-3xl w-full max-h-[80vh] overflow-hidden flex flex-col">
        <div className="sticky top-0 bg-[#0B1120] p-4 border-b border-gray-700 flex justify-between items-center z-10">
          <h2 className="text-xl font-bold text-[#88cc14]">Threat Intelligence Analysis</h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="border-b border-gray-700">
          <div className="flex">
            <button
              className={`px-4 py-2 font-medium ${activeTab === 'summary' ? 'text-[#88cc14] border-b-2 border-[#88cc14]' : 'text-gray-400 hover:text-gray-200'}`}
              onClick={() => setActiveTab('summary')}
            >
              Summary
            </button>
            <button
              className={`px-4 py-2 font-medium ${activeTab === 'details' ? 'text-[#88cc14] border-b-2 border-[#88cc14]' : 'text-gray-400 hover:text-gray-200'}`}
              onClick={() => setActiveTab('details')}
            >
              Details
            </button>
          </div>
        </div>
        
        <div className="p-6 overflow-y-auto flex-grow">
          {loading ? (
            <div className="flex flex-col items-center justify-center h-64">
              <div className="w-12 h-12 border-4 border-gray-600 border-t-[#88cc14] rounded-full animate-spin mb-4"></div>
              <p className="text-gray-400">Analyzing threat data with AI...</p>
            </div>
          ) : analysis ? (
            activeTab === 'summary' ? renderSummaryTab() : renderDetailsTab()
          ) : (
            <div className="text-center text-gray-400 py-8">
              No analysis available. Please try again later.
            </div>
          )}
        </div>
        
        <div className="bg-gray-900/50 p-3 border-t border-gray-700 text-xs text-gray-500 text-center">
          Analysis powered by Gemini AI | Data from the last 24 hours
        </div>
      </div>
    </div>
  );
};

export default ThreatAnalysisModal;
