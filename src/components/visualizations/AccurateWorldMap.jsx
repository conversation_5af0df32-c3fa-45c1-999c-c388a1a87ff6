import React from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

const AccurateWorldMap = () => {
  const { darkMode } = useGlobalTheme();
  
  // Colors for the map
  const backgroundColor = darkMode ? '#0f172a' : '#f1f5f9';
  const landColor = darkMode ? '#334155' : '#94a3b8';
  const borderColor = darkMode ? '#475569' : '#64748b';
  const gridColor = darkMode ? '#1e293b' : '#e2e8f0';
  
  return (
    <div className="absolute inset-0">
      {/* Background color */}
      <div className="absolute inset-0" style={{ backgroundColor }}></div>
      
      {/* Grid lines for reference */}
      <svg 
        width="100%" 
        height="100%" 
        viewBox="0 0 1000 500" 
        preserveAspectRatio="xMidYMid meet"
        className="absolute inset-0"
      >
        <g className="grid-lines" opacity="0.15">
          {Array.from({ length: 20 }).map((_, i) => (
            <line 
              key={`h-${i}`} 
              x1="0" 
              y1={i * 25} 
              x2="1000" 
              y2={i * 25} 
              stroke={gridColor} 
              strokeWidth="1" 
            />
          ))}
          {Array.from({ length: 40 }).map((_, i) => (
            <line 
              key={`v-${i}`} 
              x1={i * 25} 
              y1="0" 
              x2={i * 25} 
              y2="500" 
              stroke={gridColor} 
              strokeWidth="1" 
            />
          ))}
        </g>
      </svg>
      
      {/* World Map SVG */}
      <svg 
        width="100%" 
        height="100%" 
        viewBox="0 0 1000 500" 
        preserveAspectRatio="xMidYMid meet"
        className="absolute inset-0"
      >
        <g fill={landColor} stroke={borderColor} strokeWidth="1">
          {/* North America */}
          <path d="M170,80 L220,60 L280,70 L330,110 L280,150 L260,190 L220,210 L180,230 L150,200 L130,160 L120,130 L170,80" />
          
          {/* Central America */}
          <path d="M180,230 L200,250 L170,270 L190,290 L220,270 L240,250" />
          
          {/* South America */}
          <path d="M240,250 L270,290 L290,330 L280,370 L250,410 L220,390 L210,350 L230,310 L240,250" />
          
          {/* Europe */}
          <path d="M450,100 L490,80 L530,90 L560,110 L540,130 L510,150 L480,160 L450,140 L430,120 L450,100" />
          
          {/* Africa */}
          <path d="M450,170 L490,160 L530,170 L550,200 L540,250 L520,300 L490,330 L450,340 L420,310 L410,270 L430,230 L450,170" />
          
          {/* Middle East */}
          <path d="M530,170 L570,160 L590,180 L580,210 L550,200 L530,170" />
          
          {/* Russia */}
          <path d="M530,90 L600,70 L700,80 L750,100 L730,130 L680,150 L630,140 L580,130 L560,110 L530,90" />
          
          {/* India - Accurate shape */}
          <path d="M600,160 C600,160 605,155 610,150 C615,145 620,140 625,145 C630,150 635,155 640,160 C645,165 650,170 650,175 C650,180 645,185 640,190 C635,195 630,200 625,205 C620,210 615,215 610,220 C605,225 600,230 595,225 C590,220 585,215 580,210 C575,205 570,200 570,195 C570,190 575,185 580,180 C585,175 590,170 595,165 C600,160 600,160 600,160" />
          
          {/* Sri Lanka */}
          <path d="M610,220 C610,220 615,215 620,215 C625,215 625,220 625,225 C625,230 620,230 615,230 C610,230 610,225 610,220" />
          
          {/* China */}
          <path d="M650,130 L670,120 L690,130 L710,150 L690,170 L670,180 L650,170 L630,150 L650,130" />
          
          {/* Southeast Asia */}
          <path d="M650,180 L660,170 L670,180 L680,190 L670,200 L660,190 L650,180" />
          
          {/* Japan */}
          <path d="M740,140 L750,130 L760,140 L750,150 L740,140" />
          
          {/* Australia */}
          <path d="M720,280 L750,270 L780,280 L790,300 L770,320 L740,330 L720,310 L710,290 L720,280" />
          
          {/* Indonesia */}
          <path d="M670,230 L690,220 L710,230 L700,240 L680,250 L670,230" />
          
          {/* UK */}
          <path d="M410,100 L420,90 L430,100 L420,110 L410,100" />
          
          {/* Greenland */}
          <path d="M350,60 L380,50 L410,60 L390,70 L360,80 L350,60" />
          
          {/* New Zealand */}
          <path d="M820,320 L830,310 L840,320 L830,330 L820,320" />
          
          {/* Madagascar */}
          <path d="M530,240 L540,230 L550,240 L540,250 L530,240" />
          
          {/* Philippines */}
          <path d="M700,190 L710,180 L720,190 L710,200 L700,190" />
          
          {/* Caribbean Islands */}
          <path d="M220,190 L230,180 L240,190 L230,200 L220,190" />
          
          {/* Iceland */}
          <path d="M380,80 L390,70 L400,80 L390,90 L380,80" />
          
          {/* Korea */}
          <path d="M710,150 L720,140 L730,150 L720,160 L710,150" />
          
          {/* Scandinavia */}
          <path d="M470,90 L480,80 L490,90 L480,100 L470,90" />
          
          {/* Italy */}
          <path d="M470,130 L480,120 L490,130 L480,140 L470,130" />
          
          {/* Spain */}
          <path d="M410,130 L420,120 L430,130 L420,140 L410,130" />
          
          {/* Turkey */}
          <path d="M510,130 L520,120 L530,130 L520,140 L510,130" />
          
          {/* Iran */}
          <path d="M550,150 L560,140 L570,150 L560,160 L550,150" />
          
          {/* Saudi Arabia */}
          <path d="M530,170 L540,160 L550,170 L540,180 L530,170" />
          
          {/* Egypt */}
          <path d="M510,170 L520,160 L530,170 L520,180 L510,170" />
          
          {/* South Africa */}
          <path d="M490,270 L500,260 L510,270 L500,280 L490,270" />
          
          {/* Argentina */}
          <path d="M230,340 L240,330 L250,340 L240,350 L230,340" />
          
          {/* Mexico */}
          <path d="M170,190 L180,180 L190,190 L180,200 L170,190" />
          
          {/* Canada */}
          <path d="M170,100 L180,90 L190,100 L180,110 L170,100" />
          
          {/* Alaska */}
          <path d="M120,100 L130,90 L140,100 L130,110 L120,100" />
          
          {/* Thailand */}
          <path d="M650,200 L660,190 L670,200 L660,210 L650,200" />
          
          {/* Vietnam */}
          <path d="M670,210 L680,200 L690,210 L680,220 L670,210" />
          
          {/* Malaysia */}
          <path d="M660,220 L670,210 L680,220 L670,230 L660,220" />
          
          {/* Pakistan */}
          <path d="M580,160 L590,150 L600,160 L590,170 L580,160" />
          
          {/* Bangladesh */}
          <path d="M620,180 L630,170 L640,180 L630,190 L620,180" />
        </g>
      </svg>
    </div>
  );
};

export default AccurateWorldMap;
