import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FaExclamationTriangle } from 'react-icons/fa';

/**
 * SkillsRadarChart Component
 * 
 * A radar chart visualization for displaying skill levels.
 * Supports multiple data series for comparison (e.g., user vs. team average).
 * 
 * Note: In a real application, this would use a charting library like Chart.js,
 * D3.js, or Recharts. This is a simplified version for demonstration purposes.
 */
const SkillsRadarChart = ({
  skills = [],
  dataSeries = [],
  labels = ['Your Skills', 'Team Average'],
  colors = ['rgba(59, 130, 246, 0.7)', 'rgba(16, 185, 129, 0.7)'],
  height = 300,
  animate = true,
  loading = false,
  error = null
}) => {
  const canvasRef = useRef(null);
  
  // Draw chart on canvas
  useEffect(() => {
    if (loading || error || !skills.length || !dataSeries.length) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw radar chart
    drawRadarChart(ctx, skills, dataSeries, width, height, colors);
  }, [skills, dataSeries, colors, loading, error]);
  
  // Draw radar chart
  const drawRadarChart = (ctx, skills, dataSeries, width, height, colors) => {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) - 40;
    
    const numSkills = skills.length;
    const angleStep = (Math.PI * 2) / numSkills;
    
    // Draw axes
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;
    
    for (let i = 0; i < numSkills; i++) {
      const angle = i * angleStep - Math.PI / 2;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(x, y);
      ctx.stroke();
    }
    
    // Draw concentric circles
    for (let i = 1; i <= 5; i++) {
      const circleRadius = (radius / 5) * i;
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, circleRadius, 0, Math.PI * 2);
      ctx.stroke();
    }
    
    // Draw skill labels
    ctx.fillStyle = '#6b7280';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    for (let i = 0; i < numSkills; i++) {
      const angle = i * angleStep - Math.PI / 2;
      const x = centerX + Math.cos(angle) * (radius + 20);
      const y = centerY + Math.sin(angle) * (radius + 20);
      
      ctx.fillText(skills[i], x, y);
    }
    
    // Draw data series
    for (let seriesIndex = 0; seriesIndex < dataSeries.length; seriesIndex++) {
      const seriesData = dataSeries[seriesIndex];
      const color = colors[seriesIndex % colors.length];
      
      ctx.fillStyle = color;
      ctx.strokeStyle = color.replace('0.7', '1');
      ctx.lineWidth = 2;
      
      ctx.beginPath();
      
      for (let i = 0; i < numSkills; i++) {
        const angle = i * angleStep - Math.PI / 2;
        const value = seriesData[i] / 100; // Normalize to 0-1
        const x = centerX + Math.cos(angle) * radius * value;
        const y = centerY + Math.sin(angle) * radius * value;
        
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      
      // Close the path
      const firstAngle = -Math.PI / 2;
      const firstValue = seriesData[0] / 100;
      const firstX = centerX + Math.cos(firstAngle) * radius * firstValue;
      const firstY = centerY + Math.sin(firstAngle) * radius * firstValue;
      ctx.lineTo(firstX, firstY);
      
      ctx.fill();
      ctx.stroke();
      
      // Draw points
      for (let i = 0; i < numSkills; i++) {
        const angle = i * angleStep - Math.PI / 2;
        const value = seriesData[i] / 100;
        const x = centerX + Math.cos(angle) * radius * value;
        const y = centerY + Math.sin(angle) * radius * value;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  };
  
  // Render loading state
  if (loading) {
    return (
      <div 
        className="bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center"
        style={{ height: `${height}px` }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading skills data...</p>
        </div>
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <div 
        className="bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center"
        style={{ height: `${height}px` }}
      >
        <div className="text-center text-red-500 dark:text-red-400">
          <FaExclamationTriangle className="mx-auto text-2xl mb-2" />
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }
  
  // Render empty state
  if (!skills.length || !dataSeries.length) {
    return (
      <div 
        className="bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center"
        style={{ height: `${height}px` }}
      >
        <div className="text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">No skills data available</p>
        </div>
      </div>
    );
  }
  
  return (
    <motion.div 
      initial={animate ? { opacity: 0, y: 20 } : false}
      animate={animate ? { opacity: 1, y: 0 } : false}
      className="bg-white dark:bg-gray-800 rounded-lg p-4"
    >
      <div className="relative">
        <canvas 
          ref={canvasRef} 
          width={500} 
          height={height}
          className="w-full h-auto"
        />
      </div>
      
      {/* Legend */}
      <div className="mt-4 flex flex-wrap justify-center gap-4">
        {dataSeries.map((_, index) => (
          <div key={index} className="flex items-center">
            <div 
              className="w-3 h-3 rounded-full mr-1"
              style={{ backgroundColor: colors[index % colors.length] }}
            />
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {labels[index] || `Series ${index + 1}`}
            </span>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

export default SkillsRadarChart;
