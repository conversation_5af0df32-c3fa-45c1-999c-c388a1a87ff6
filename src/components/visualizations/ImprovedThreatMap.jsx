import React, { useState, useEffect, useRef } from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import RealTimeThreatService from '../../services/RealTimeThreatService';
import { FaExclamationTriangle, FaServer, FaExclamation, FaGlobe, FaShieldAlt } from 'react-icons/fa';
import AccurateWorldMap from './AccurateWorldMap';

// Improved threat map with fixed country positions and solid attack lines
const ImprovedThreatMap = ({
  height = '500px',
  filterType = 'all',
  filterSeverity = 'all'
}) => {
  const { darkMode } = useGlobalTheme();
  const [threats, setThreats] = useState([]);
  const [activeThreat, setActiveThreat] = useState(null);
  const [showThreatDetails, setShowThreatDetails] = useState(false);
  const [hoveredCountry, setHoveredCountry] = useState(null);
  const [attackStats, setAttackStats] = useState({
    total: 0,
    blocked: 0,
    byCountry: {},
    byType: {}
  });
  const mapRef = useRef(null);

  // Country positions aligned with the accurate world map
  const countries = [
    { name: 'United States', code: 'US', x: 220, y: 140, attacks: 0, color: '#3b82f6' },
    { name: 'Russia', code: 'RU', x: 630, y: 110, attacks: 0, color: '#ef4444' },
    { name: 'China', code: 'CN', x: 680, y: 150, attacks: 0, color: '#f97316' },
    { name: 'North Korea', code: 'KP', x: 720, y: 150, attacks: 0, color: '#ec4899' },
    { name: 'Iran', code: 'IR', x: 560, y: 150, attacks: 0, color: '#f97316' },
    { name: 'United Kingdom', code: 'GB', x: 420, y: 100, attacks: 0, color: '#6366f1' },
    { name: 'Germany', code: 'DE', x: 480, y: 130, attacks: 0, color: '#6366f1' },
    { name: 'Ukraine', code: 'UA', x: 520, y: 130, attacks: 0, color: '#6366f1' },
    { name: 'Japan', code: 'JP', x: 750, y: 140, attacks: 0, color: '#ec4899' },
    { name: 'India', code: 'IN', x: 610, y: 180, attacks: 0, color: '#8b5cf6' },
    { name: 'Brazil', code: 'BR', x: 260, y: 320, attacks: 0, color: '#10b981' },
    { name: 'Australia', code: 'AU', x: 750, y: 300, attacks: 0, color: '#f43f5e' },
    { name: 'Canada', code: 'CA', x: 180, y: 100, attacks: 0, color: '#3b82f6' },
    { name: 'South Africa', code: 'ZA', x: 500, y: 270, attacks: 0, color: '#f59e0b' },
    { name: 'Israel', code: 'IL', x: 530, y: 170, attacks: 0, color: '#f97316' },
    { name: 'France', code: 'FR', x: 450, y: 130, attacks: 0, color: '#6366f1' },
    { name: 'Italy', code: 'IT', x: 480, y: 130, attacks: 0, color: '#6366f1' },
    { name: 'South Korea', code: 'KR', x: 720, y: 150, attacks: 0, color: '#ec4899' },
    { name: 'Mexico', code: 'MX', x: 180, y: 190, attacks: 0, color: '#60a5fa' },
    { name: 'Saudi Arabia', code: 'SA', x: 540, y: 170, attacks: 0, color: '#f97316' },
    { name: 'Pakistan', code: 'PK', x: 590, y: 160, attacks: 0, color: '#8b5cf6' },
    { name: 'Bangladesh', code: 'BD', x: 630, y: 180, attacks: 0, color: '#8b5cf6' },
    { name: 'Thailand', code: 'TH', x: 660, y: 200, attacks: 0, color: '#a855f7' },
    { name: 'Vietnam', code: 'VN', x: 680, y: 210, attacks: 0, color: '#a855f7' },
    { name: 'Indonesia', code: 'ID', x: 680, y: 240, attacks: 0, color: '#a855f7' },
    { name: 'Egypt', code: 'EG', x: 520, y: 170, attacks: 0, color: '#f59e0b' },
    { name: 'Turkey', code: 'TR', x: 520, y: 130, attacks: 0, color: '#f97316' },
    { name: 'Spain', code: 'ES', x: 420, y: 130, attacks: 0, color: '#6366f1' },
    { name: 'Argentina', code: 'AR', x: 240, y: 340, attacks: 0, color: '#10b981' },
    { name: 'Philippines', code: 'PH', x: 710, y: 190, attacks: 0, color: '#a855f7' },
    { name: 'Sri Lanka', code: 'LK', x: 620, y: 220, attacks: 0, color: '#a855f7' }
  ];

  // Attack types with colors
  const attackTypes = {
    'Ransomware': { color: '#ef4444', icon: FaExclamationTriangle },
    'DDoS': { color: '#f97316', icon: FaServer },
    'Phishing': { color: '#f59e0b', icon: FaExclamation },
    'Malware': { color: '#dc2626', icon: FaExclamationTriangle },
    'SQL Injection': { color: '#a855f7', icon: FaServer },
    'XSS': { color: '#ec4899', icon: FaExclamation },
    'Zero-day Exploit': { color: '#8b5cf6', icon: FaExclamationTriangle }
  };

  // Initialize threat service and get real-time data
  useEffect(() => {
    const threatService = RealTimeThreatService.getInstance().initialize();

    // Generate initial threats
    const processAttacks = () => {
      const attacks = threatService.getActiveAttacks();
      const stats = threatService.getStatistics();

      // Reset country attack counts
      countries.forEach(country => {
        country.attacks = 0;
      });

      // Convert attacks to the format our map component expects
      const mappedThreats = attacks.map(attack => {
        // Find source and target countries
        const sourceCountry = countries.find(c => c.name === attack.source);
        const targetCountry = countries.find(c => c.name === attack.target);

        if (!sourceCountry || !targetCountry) return null;

        // Increment attack counts for countries
        sourceCountry.attacks++;
        targetCountry.attacks++;

        return {
          id: attack.id,
          type: attack.type,
          severity: attack.severity === 'Critical' ? 3 :
                   attack.severity === 'High' ? 2 : 1,
          source: sourceCountry,
          target: targetCountry,
          color: attack.color || (attackTypes[attack.type]?.color || '#f59e0b'),
          timestamp: new Date(attack.timestamp).getTime(),
          details: {
            name: `${attack.type} Attack`,
            description: `${attack.severity} severity attack from ${attack.source} targeting ${attack.target}`,
            industry: attack.industry || 'Unknown',
            method: attack.type,
            status: Math.random() > 0.3 ? 'Active' : 'Blocked'
          }
        };
      }).filter(Boolean);

      // Update attack statistics
      const countryStats = {};
      const typeStats = {};

      countries.forEach(country => {
        countryStats[country.name] = country.attacks;
      });

      Object.keys(stats.attacksByType || {}).forEach(type => {
        typeStats[type] = stats.attacksByType[type];
      });

      setAttackStats({
        total: stats.totalAttacks || 0,
        blocked: Math.floor((stats.totalAttacks || 0) * 0.76),
        byCountry: countryStats,
        byType: typeStats
      });

      setThreats(mappedThreats);
    };

    // Initial processing
    processAttacks();

    // Add listener for updates
    threatService.addListener(() => {
      processAttacks();
    });

    // Start real-time updates
    threatService.startRealTimeUpdates();

    // Cleanup
    return () => {
      threatService.removeListener(processAttacks);
      threatService.stopRealTimeUpdates();
    };
  }, []);

  // Filter threats based on selected filters
  const filteredThreats = threats.filter(threat => {
    // Filter by type
    if (filterType !== 'all' && threat.type !== filterType) {
      return false;
    }

    // Filter by severity
    if (filterSeverity !== 'all') {
      const severityMap = {
        'Critical': 3,
        'High': 2,
        'Medium': 1,
        'Low': 1
      };

      if (threat.severity !== severityMap[filterSeverity]) {
        return false;
      }
    }

    return true;
  });

  // Handle country hover
  const handleCountryHover = (country) => {
    setHoveredCountry(country);
  };

  // Handle country click
  const handleCountryClick = (country) => {
    // Find threats related to this country
    const relatedThreats = filteredThreats.filter(
      threat => threat.source.name === country.name || threat.target.name === country.name
    );

    if (relatedThreats.length > 0) {
      setActiveThreat(relatedThreats[0]);
      setShowThreatDetails(true);
    }
  };

  // Handle threat click
  const handleThreatClick = (threat) => {
    setActiveThreat(threat);
    setShowThreatDetails(true);
  };

  // Get color for threat severity
  const getThreatColor = (severity) => {
    switch (severity) {
      case 3: return '#ef4444'; // Critical
      case 2: return '#f97316'; // High
      case 1: return '#f59e0b'; // Medium/Low
      default: return '#f59e0b';
    }
  };

  // Get icon for threat type
  const getThreatIcon = (type) => {
    return attackTypes[type]?.icon || FaExclamationTriangle;
  };

  // Format time ago
  const formatTimeAgo = (timestamp) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);

    if (seconds < 60) return `${seconds} seconds ago`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)} minutes ago`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)} hours ago`;
    return `${Math.floor(seconds / 86400)} days ago`;
  };

  // Get attack path with curve
  const getAttackPath = (source, target) => {
    // Calculate control point for curved line
    const dx = target.x - source.x;
    const dy = target.y - source.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // Adjust curve based on distance
    const curveOffset = Math.min(distance * 0.2, 50);

    // Calculate control point perpendicular to the line
    const mx = source.x + dx * 0.5;
    const my = source.y + dy * 0.5;

    // Perpendicular vector
    const nx = -dy / distance;
    const ny = dx / distance;

    // Control point
    const cx = mx + nx * curveOffset;
    const cy = my + ny * curveOffset;

    return `M${source.x},${source.y} Q${cx},${cy} ${target.x},${target.y}`;
  };

  return (
    <div className="relative" style={{ width: '100%', height }}>
      {/* Map Container */}
      <div
        ref={mapRef}
        className="relative w-full h-full bg-gray-900 rounded-lg overflow-hidden border border-gray-800"
      >
        {/* World Map Background */}
        <AccurateWorldMap />

        {/* Attack Vectors - Solid lines with animations */}
        <svg width="100%" height="100%" viewBox="0 0 1000 500" preserveAspectRatio="xMidYMid meet" className="absolute inset-0 z-10">
          <defs>
            <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
              <feGaussianBlur stdDeviation="3" result="blur" />
              <feComposite in="SourceGraphic" in2="blur" operator="over" />
            </filter>
            <filter id="strong-glow" x="-30%" y="-30%" width="160%" height="160%">
              <feGaussianBlur stdDeviation="5" result="blur" />
              <feComposite in="SourceGraphic" in2="blur" operator="over" />
            </filter>
          </defs>

          {filteredThreats.map(threat => {
            const pathId = `path-${threat.id}`;
            const attackColor = threat.color;

            return (
              <g key={threat.id} className="attack-vector">
                {/* Attack path */}
                <path
                  id={pathId}
                  d={getAttackPath(threat.source, threat.target)}
                  stroke={attackColor}
                  strokeWidth={threat.severity + 1.5}
                  fill="none"
                  opacity="1"
                  filter={threat.severity > 3 ? "url(#strong-glow)" : "url(#glow)"}
                  strokeDasharray={threat.type === 'DDoS' ? '5,3' : threat.type === 'Phishing' ? '1,1' : 'none'}
                />

                {/* Animated particles along the path */}
                <circle r="5" fill={attackColor} filter={threat.severity > 3 ? "url(#strong-glow)" : "url(#glow)"}>
                  <animateMotion
                    dur={`${2 + Math.random() * 1.5}s`}
                    repeatCount="indefinite"
                    path={getAttackPath(threat.source, threat.target)}
                  />
                  <animate
                    attributeName="opacity"
                    values="1;0.7;1"
                    dur={`${1 + Math.random()}s`}
                    repeatCount="indefinite"
                  />
                </circle>

                {/* Additional particles for severe attacks */}
                {threat.severity > 3 && (
                  <>
                    <circle r="4" fill={attackColor} filter="url(#glow)">
                      <animateMotion
                        dur={`${2.5 + Math.random() * 1.5}s`}
                        repeatCount="indefinite"
                        path={getAttackPath(threat.source, threat.target)}
                      />
                      <animate
                        attributeName="opacity"
                        values="0.7;1;0.7"
                        dur={`${1.2 + Math.random()}s`}
                        repeatCount="indefinite"
                      />
                    </circle>

                    <circle r="3" fill={attackColor} filter="url(#glow)">
                      <animateMotion
                        dur={`${3 + Math.random() * 1.5}s`}
                        repeatCount="indefinite"
                        path={getAttackPath(threat.source, threat.target)}
                      />
                      <animate
                        attributeName="opacity"
                        values="0.5;0.8;0.5"
                        dur={`${1.5 + Math.random()}s`}
                        repeatCount="indefinite"
                      />
                    </circle>
                  </>
                )}
              </g>
            );
          })}
        </svg>

        {/* Countries */}
        <div className="absolute inset-0 z-20">
          {countries.map(country => {
            // Count threats related to this country
            const relatedThreats = filteredThreats.filter(
              threat => threat.source.name === country.name || threat.target.name === country.name
            );

            const isHotspot = relatedThreats.length > 0;
            const size = Math.max(30, Math.min(60, 30 + relatedThreats.length * 2));

            return (
              <div
                key={country.code}
                className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                style={{
                  left: `${country.x}px`,
                  top: `${country.y}px`,
                }}
                onMouseEnter={() => handleCountryHover(country)}
                onMouseLeave={() => handleCountryHover(null)}
                onClick={() => handleCountryClick(country)}
              >
                {/* Country hotspot */}
                <div
                  className={`relative rounded-full flex items-center justify-center transition-all duration-300 ${
                    hoveredCountry === country ? 'scale-125' : ''
                  }`}
                  style={{
                    width: `${size}px`,
                    height: `${size}px`,
                    backgroundColor: isHotspot ? `${country.color}50` : 'transparent',
                    border: isHotspot ? `3px solid ${country.color}` : '1px solid rgba(255,255,255,0.3)',
                    boxShadow: isHotspot ? `0 0 15px ${country.color}` : 'none',
                    transform: `scale(${isHotspot ? 1.1 : 1})`,
                  }}
                >
                  {isHotspot && (
                    <div
                      className="absolute inset-0 rounded-full animate-ping"
                      style={{
                        backgroundColor: country.color,
                        opacity: 0.5,
                        animationDuration: `${2 + Math.random()}s`
                      }}
                    />
                  )}

                  {/* Country label */}
                  <div
                    className={`absolute whitespace-nowrap text-center transition-opacity duration-300 ${
                      hoveredCountry === country || relatedThreats.length > 2 ? 'opacity-100' : 'opacity-70'
                    }`}
                    style={{
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                    }}
                  >
                    <div
                      className="font-mono text-xs font-bold px-2 py-1 rounded"
                      style={{
                        backgroundColor: isHotspot ? `${country.color}30` : 'transparent',
                        color: isHotspot ? country.color : '#94a3b8',
                        textShadow: '0 0 10px rgba(0,0,0,0.8)',
                      }}
                    >
                      {country.name} {isHotspot && `(${relatedThreats.length})`}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Live attack counter */}
        <div className="absolute top-4 left-4 bg-gray-800/80 backdrop-blur-sm p-4 rounded-lg z-30 border border-gray-700/50 shadow-lg shadow-black/30">
          <div className="flex items-center gap-2">
            <div className="text-xs font-bold text-gray-300 uppercase tracking-wider">Live Attacks</div>
            <div className="px-2 py-0.5 bg-red-500/20 rounded-full flex items-center">
              <span className="w-2 h-2 bg-red-500 rounded-full animate-ping absolute"></span>
              <span className="w-2 h-2 bg-red-500 rounded-full relative"></span>
              <span className="text-red-500 text-xs font-mono font-bold ml-2">LIVE</span>
            </div>
          </div>
          <div className="text-4xl font-mono font-bold text-white flex items-center gap-2 mt-2">
            {filteredThreats.length}
            <span className="text-xs text-gray-400 font-normal">active threats</span>
          </div>
          <div className="flex items-center gap-2 mt-3 text-xs text-gray-300">
            <FaShieldAlt className="text-green-500" />
            <span>{attackStats.blocked} blocked attacks</span>
          </div>
          <div className="h-1 w-full bg-gray-700/50 rounded-full mt-3 overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-green-500 to-red-500 rounded-full"
              style={{ width: `${Math.min(100, filteredThreats.length * 5)}%` }}
            ></div>
          </div>
        </div>

        {/* Attack type distribution */}
        <div className="absolute top-4 right-4 bg-gray-800/80 backdrop-blur-sm p-4 rounded-lg z-30 border border-gray-700/50 max-w-[250px] shadow-lg shadow-black/30">
          <div className="text-xs font-bold text-gray-300 uppercase tracking-wider mb-3">Attack Types</div>
          <div className="space-y-3">
            {Object.entries(attackStats.byType)
              .sort((a, b) => b[1] - a[1])
              .slice(0, 4)
              .map(([type, count]) => {
                const percentage = Math.round((count / (attackStats.total || 1)) * 100);
                const TypeIcon = attackTypes[type]?.icon || FaExclamationTriangle;
                const color = attackTypes[type]?.color || '#f59e0b';

                return (
                  <div key={type} className="flex items-center gap-3">
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center"
                      style={{
                        backgroundColor: `${color}20`,
                        boxShadow: `0 0 10px ${color}40`
                      }}
                    >
                      <TypeIcon style={{ color }} size={14} />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between text-xs">
                        <span className="text-white font-medium">{type}</span>
                        <span className="text-gray-300 font-mono font-bold">{percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-700/50 rounded-full h-1.5 mt-1.5 overflow-hidden">
                        <div
                          className="h-full rounded-full"
                          style={{ width: `${percentage}%`, backgroundColor: color }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>

        {/* Top attacked countries */}
        <div className="absolute bottom-4 left-4 bg-gray-800/80 backdrop-blur-sm p-4 rounded-lg z-30 border border-gray-700/50 max-w-[250px] shadow-lg shadow-black/30">
          <div className="text-xs font-bold text-gray-300 uppercase tracking-wider mb-3">Top Targets</div>
          <div className="space-y-3">
            {Object.entries(attackStats.byCountry)
              .sort((a, b) => b[1] - a[1])
              .filter(([_, count]) => count > 0)
              .slice(0, 4)
              .map(([countryName, count]) => {
                const country = countries.find(c => c.name === countryName);
                if (!country) return null;

                const percentage = Math.round((count / (attackStats.total || 1)) * 100);

                return (
                  <div key={countryName} className="flex items-center gap-3">
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center"
                      style={{
                        backgroundColor: `${country.color}20`,
                        boxShadow: `0 0 10px ${country.color}40`
                      }}
                    >
                      <FaGlobe style={{ color: country.color }} size={14} />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between text-xs">
                        <span className="text-white font-medium">{countryName}</span>
                        <span className="text-gray-300 font-mono font-bold">{count} attacks</span>
                      </div>
                      <div className="w-full bg-gray-700/50 rounded-full h-1.5 mt-1.5 overflow-hidden">
                        <div
                          className="h-full rounded-full"
                          style={{ width: `${percentage}%`, backgroundColor: country.color }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      </div>

      {/* Active Threat Info Panel */}
      {activeThreat && showThreatDetails && (
        <div className="absolute bottom-4 right-4 w-80 bg-gray-800/90 backdrop-blur-sm text-white p-4 rounded-lg text-sm border border-gray-700/50 z-40">
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-2">
              <div
                className="w-8 h-8 rounded-full flex items-center justify-center"
                style={{ backgroundColor: `${activeThreat.color}20` }}
              >
                {React.createElement(getThreatIcon(activeThreat.type), {
                  style: { color: activeThreat.color }
                })}
              </div>
              <div>
                <div className="font-bold">{activeThreat.type} Attack</div>
                <div className="text-xs text-gray-400">
                  {activeThreat.source.name} → {activeThreat.target.name}
                </div>
              </div>
            </div>

            <button
              onClick={() => setShowThreatDetails(false)}
              className="text-gray-400 hover:text-white"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>

          <div className="mt-4 space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Severity:</span>
              <div className="flex">
                {Array.from({ length: activeThreat.severity }).map((_, i) => (
                  <span key={i} className="text-red-500">●</span>
                ))}
                {Array.from({ length: 3 - activeThreat.severity }).map((_, i) => (
                  <span key={i} className="text-gray-600">●</span>
                ))}
              </div>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-400">Status:</span>
              <span className={activeThreat.details.status === 'Blocked' ? 'text-green-500' : 'text-red-500'}>
                {activeThreat.details.status}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-400">Target Industry:</span>
              <span>{activeThreat.details.industry}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-400">Detected:</span>
              <span>{formatTimeAgo(activeThreat.timestamp)}</span>
            </div>
          </div>

          <div className="mt-4 p-3 bg-gray-900/50 rounded border border-gray-700/50">
            <div className="text-xs text-gray-400 mb-1">ATTACK DETAILS</div>
            <div className="text-sm">
              {activeThreat.type === 'Ransomware' && 'Encrypting files and demanding payment for decryption keys.'}
              {activeThreat.type === 'DDoS' && 'Overwhelming target servers with a flood of internet traffic.'}
              {activeThreat.type === 'Phishing' && 'Attempting to steal sensitive information through deceptive emails.'}
              {activeThreat.type === 'Malware' && 'Deploying malicious software to damage systems or steal data.'}
              {activeThreat.type === 'SQL Injection' && 'Exploiting database vulnerabilities to extract sensitive data.'}
              {activeThreat.type === 'XSS' && 'Injecting malicious scripts into web applications.'}
              {activeThreat.type === 'Zero-day Exploit' && 'Exploiting previously unknown software vulnerabilities.'}
              {!['Ransomware', 'DDoS', 'Phishing', 'Malware', 'SQL Injection', 'XSS', 'Zero-day Exploit'].includes(activeThreat.type) &&
                'Attempting to compromise target systems using advanced techniques.'}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImprovedThreatMap;
