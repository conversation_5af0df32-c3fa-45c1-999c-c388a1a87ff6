import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

// Simple 2D map visualization that's much more performant than the 3D globe
const CyberThreatMap = ({ width = '100%', height = '500px' }) => {
  const [threats, setThreats] = useState([]);
  const [activeThreat, setActiveThreat] = useState(null);
  const [showLearnMore, setShowLearnMore] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch threat data
  const fetchThreatData = async () => {
    try {
      // In a real implementation, this would call your API
      // For now, we'll generate some sample data
      return generateSampleThreats(5);
    } catch (error) {
      console.error('Error fetching threat data:', error);
      return generateSampleThreats(5); // Fallback
    }
  };

  // Generate sample threat data
  const generateSampleThreats = (count) => {
    const threatTypes = ['Ransomware', 'DDoS', 'Phishing', 'Malware', 'Data Breach', 'Brute Force', 'Port Scan'];
    const countries = [
      { name: 'United States', code: 'US', x: 100, y: 120 },
      { name: 'China', code: 'CN', x: 350, y: 130 },
      { name: 'Russia', code: 'RU', x: 300, y: 80 },
      { name: 'Brazil', code: 'BR', x: 150, y: 220 },
      { name: 'India', code: 'IN', x: 320, y: 150 },
      { name: 'United Kingdom', code: 'GB', x: 200, y: 100 },
      { name: 'Germany', code: 'DE', x: 220, y: 110 },
      { name: 'Australia', code: 'AU', x: 380, y: 250 }
    ];

    return Array.from({ length: count }, (_, i) => {
      const sourceCountry = countries[Math.floor(Math.random() * countries.length)];
      let targetCountry;
      do {
        targetCountry = countries[Math.floor(Math.random() * countries.length)];
      } while (targetCountry.name === sourceCountry.name);

      return {
        id: `threat-${Date.now()}-${i}`,
        type: threatTypes[Math.floor(Math.random() * threatTypes.length)],
        severity: Math.floor(Math.random() * 3) + 1, // 1-3
        source: {
          name: sourceCountry.name,
          code: sourceCountry.code,
          x: sourceCountry.x,
          y: sourceCountry.y
        },
        target: {
          name: targetCountry.name,
          code: targetCountry.code,
          x: targetCountry.x,
          y: targetCountry.y
        },
        timestamp: Date.now() - Math.floor(Math.random() * 3600000), // Within the last hour
        details: {
          name: `Sample Threat ${i + 1}`,
          description: 'Sample threat for demonstration',
          reports: Math.floor(Math.random() * 100) + 1
        }
      };
    });
  };

  // Handle Learn More button click
  const handleLearnMore = useCallback(() => {
    setShowLearnMore(true);
  }, []);

  // Load and update threat data
  useEffect(() => {
    const loadThreatData = async () => {
      setIsLoading(true);
      const data = await fetchThreatData();
      setThreats(data);
      setActiveThreat(data[0]); // Set first threat as active
      setIsLoading(false);
    };

    loadThreatData();

    // Update threats every 15 seconds
    const interval = setInterval(async () => {
      const newThreat = generateSampleThreats(1)[0];
      
      setThreats(prev => {
        // Keep only the 5 most recent threats
        const updated = [newThreat, ...prev.slice(0, 4)];
        
        // Only update active threat every 30 seconds
        const now = Date.now();
        if (!window.lastThreatChangeTime || now - window.lastThreatChangeTime > 30000) {
          setActiveThreat(newThreat);
          window.lastThreatChangeTime = now;
        }
        
        return updated;
      });
    }, 15000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative" style={{ width, height }}>
      {/* Simple 2D Map */}
      <div className="relative w-full h-full bg-gray-900 rounded-lg overflow-hidden border border-gray-800">
        {/* World Map Background */}
        <div className="absolute inset-0 opacity-20">
          <img 
            src="/world-map-simple.png" 
            alt="World Map" 
            className="w-full h-full object-cover"
          />
        </div>

        {/* Threat Indicators */}
        {threats.map(threat => (
          <React.Fragment key={threat.id}>
            {/* Source Point */}
            <div 
              className="absolute w-2 h-2 rounded-full bg-red-500"
              style={{ 
                left: `${threat.source.x}px`, 
                top: `${threat.source.y}px`,
                boxShadow: '0 0 8px #ff0000'
              }}
            />
            
            {/* Target Point */}
            <div 
              className="absolute w-3 h-3 rounded-full bg-yellow-500"
              style={{ 
                left: `${threat.target.x}px`, 
                top: `${threat.target.y}px`,
                boxShadow: '0 0 10px #ffff00'
              }}
            />
            
            {/* Connection Line */}
            <svg 
              className="absolute top-0 left-0 w-full h-full pointer-events-none"
              style={{ zIndex: 1 }}
            >
              <line 
                x1={threat.source.x} 
                y1={threat.source.y} 
                x2={threat.target.x} 
                y2={threat.target.y} 
                stroke={
                  threat.severity === 3 ? "#ff0000" : 
                  threat.severity === 2 ? "#ff9900" : 
                  "#ffff00"
                }
                strokeWidth="1"
                strokeDasharray="4"
                strokeOpacity="0.6"
              />
            </svg>
          </React.Fragment>
        ))}
      </div>

      {/* Active Threat Info Panel */}
      {activeThreat && (
        <div className="absolute bottom-4 left-4 right-4 bg-black/85 backdrop-blur-sm text-white p-4 rounded-lg text-sm border border-gray-700/50">
          <div className="flex flex-col gap-2">
            {/* Header with threat type and route */}
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full mr-2 ${
                  activeThreat.severity === 3 ? 'bg-red-500/20' : 
                  activeThreat.severity === 2 ? 'bg-orange-500/20' : 
                  'bg-yellow-500/20'
                }`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </span>
                <span className="font-medium">{activeThreat.type}</span>
              </div>
              <div className="text-xs text-gray-400">
                {activeThreat.source.name} → {activeThreat.target.name}
              </div>
            </div>

            {/* Threat details */}
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-gray-400">Severity:</span>
                <div className="flex">
                  {Array.from({ length: activeThreat.severity }).map((_, i) => (
                    <span key={i} className="text-red-500">●</span>
                  ))}
                  {Array.from({ length: 3 - activeThreat.severity }).map((_, i) => (
                    <span key={i} className="text-gray-600">●</span>
                  ))}
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Detected:</span>
                <span>
                  {new Date(activeThreat.timestamp).toLocaleTimeString()}
                </span>
              </div>
              {activeThreat.details.reports && (
                <div className="flex justify-between items-center mt-1">
                  <span className="text-gray-400">Reports:</span>
                  <span className="text-gray-300 bg-gray-800 px-2 py-0.5 rounded">
                    {activeThreat.details.reports}
                  </span>
                </div>
              )}
            </div>

            {/* Did you know fact */}
            <div className="mt-1 text-xs bg-blue-900/30 border border-blue-800/30 rounded p-2 flex items-start">
              <span className="text-blue-400 font-bold mr-1">Did you know:</span>
              <span className="text-gray-300">
                {activeThreat.type === 'Ransomware' && 'Ransomware attacks increased by 150% in 2021, with an average ransom payment of $220,000.'}
                {activeThreat.type === 'DDoS' && 'The largest DDoS attack ever recorded reached 3.47 Tbps, targeting Azure servers in 2022.'}
                {activeThreat.type === 'Phishing' && '90% of data breaches start with a phishing email, making it the most common attack vector.'}
                {activeThreat.type === 'Malware' && 'Over 450,000 new malware samples are detected every day, with most targeting Windows systems.'}
                {activeThreat.type === 'Data Breach' && 'The average cost of a data breach reached $4.35 million in 2022, a 13% increase since 2020.'}
                {activeThreat.type === 'Brute Force' && '80% of data breaches involve brute force or stolen credentials.'}
                {activeThreat.type === 'Port Scan' && 'Port scanning is often the first step in 70% of targeted cyber attacks.'}
                {(activeThreat.type !== 'Ransomware' && activeThreat.type !== 'DDoS' &&
                  activeThreat.type !== 'Phishing' && activeThreat.type !== 'Malware' &&
                  activeThreat.type !== 'Data Breach' && activeThreat.type !== 'Brute Force' &&
                  activeThreat.type !== 'Port Scan') &&
                  'Organizations with strong security awareness training report 70% fewer successful cyber attacks.'}
              </span>
            </div>

            {/* Learn More button */}
            <button
              onClick={handleLearnMore}
              className="mt-2 w-full bg-[#88cc14]/20 hover:bg-[#88cc14]/30 text-[#88cc14] text-xs py-1.5 rounded border border-[#88cc14]/30 transition-colors flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Learn More About {activeThreat.type}
            </button>
          </div>
        </div>
      )}

      {/* Learn More Modal */}
      {showLearnMore && activeThreat && (
        <div className="fixed inset-0 flex items-center justify-center z-30">
          <div className="absolute inset-0 bg-black/70 backdrop-blur-sm" onClick={() => setShowLearnMore(false)}></div>
          
          <div className="bg-[#0B1120] border border-gray-700 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden relative z-10">
            {/* Header */}
            <div className="sticky top-0 bg-[#0B1120] p-4 border-b border-gray-700 flex justify-between items-center">
              <h2 className="text-xl font-bold text-[#88cc14]">{activeThreat.type} Threat</h2>
              <button 
                onClick={() => setShowLearnMore(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              <div>
                <h3 className="text-[#88cc14] font-medium mb-3">What is {activeThreat.type}?</h3>
                <p className="text-gray-300">
                  {activeThreat.type === 'Ransomware' && 'Ransomware is a type of malicious software that encrypts a victim\'s files. The attackers then demand a ransom from the victim to restore access to the data upon payment.'}
                  {activeThreat.type === 'DDoS' && 'A DDoS attack attempts to make an online service unavailable by overwhelming it with traffic from multiple sources. Unlike a simple DoS attack, which uses a single computer and internet connection, a DDoS attack uses multiple computers and connections, often distributed around the world.'}
                  {activeThreat.type === 'Phishing' && 'Phishing is a cybercrime where attackers disguise themselves as trustworthy entities to trick victims into revealing sensitive information such as passwords, credit card numbers, or personal data.'}
                  {activeThreat.type === 'Malware' && 'Malware (malicious software) is any program or file that is harmful to a computer user. Types of malware include viruses, worms, Trojans, spyware, adware, and ransomware.'}
                  {activeThreat.type === 'Data Breach' && 'A data breach is a security incident where sensitive, protected, or confidential data is copied, transmitted, viewed, stolen, or used by an unauthorized individual.'}
                  {activeThreat.type === 'Brute Force' && 'A brute force attack is a trial-and-error method used to obtain information such as passwords or PINs by systematically trying all possible combinations until the correct one is found.'}
                  {activeThreat.type === 'Port Scan' && 'Port scanning is a technique used to identify open ports and services on a network host. While it has legitimate uses in network management, it\'s often used by attackers to discover vulnerable services.'}
                  {(activeThreat.type !== 'Ransomware' && activeThreat.type !== 'DDoS' &&
                    activeThreat.type !== 'Phishing' && activeThreat.type !== 'Malware' &&
                    activeThreat.type !== 'Data Breach' && activeThreat.type !== 'Brute Force' &&
                    activeThreat.type !== 'Port Scan') &&
                    'This is a type of cyber attack that targets computer systems, networks, or data to disrupt operations, steal information, or gain unauthorized access.'}
                </p>
              </div>
              
              <div className="mt-6">
                <h3 className="text-[#88cc14] font-medium mb-3">Potential Impact</h3>
                <p className="text-gray-300">
                  {activeThreat.type === 'Ransomware' && 'Ransomware can cause significant financial losses, operational disruption, and reputational damage. Organizations may face costs from ransom payments, system recovery, and downtime.'}
                  {activeThreat.type === 'DDoS' && 'DDoS attacks can lead to slow website performance, website crashes, service unavailability, financial losses from downtime, and damage to reputation and customer trust.'}
                  {activeThreat.type === 'Phishing' && 'Phishing can lead to identity theft, financial fraud, unauthorized purchases, data breaches, and installation of malware on victims\' devices or networks.'}
                  {activeThreat.type === 'Malware' && 'Malware can cause data theft, financial loss, system damage, privacy violations, and can be used as a foothold for more sophisticated attacks.'}
                  {activeThreat.type === 'Data Breach' && 'Data breaches can result in identity theft, financial fraud, reputational damage, legal consequences, regulatory fines, and loss of customer trust.'}
                  {activeThreat.type === 'Brute Force' && 'Successful brute force attacks can lead to unauthorized access to accounts, data theft, financial loss, and can serve as an entry point for more damaging attacks.'}
                  {activeThreat.type === 'Port Scan' && 'Port scanning itself doesn\'t cause damage but provides attackers with information about potential vulnerabilities that can be exploited in subsequent attacks.'}
                  {(activeThreat.type !== 'Ransomware' && activeThreat.type !== 'DDoS' &&
                    activeThreat.type !== 'Phishing' && activeThreat.type !== 'Malware' &&
                    activeThreat.type !== 'Data Breach' && activeThreat.type !== 'Brute Force' &&
                    activeThreat.type !== 'Port Scan') &&
                    'This type of attack can result in data theft, financial loss, operational disruption, reputational damage, and in some cases, physical harm through compromised infrastructure.'}
                </p>
              </div>
              
              <div className="mt-6">
                <h3 className="text-[#88cc14] font-medium mb-3">Prevention Measures</h3>
                <ul className="space-y-2">
                  {activeThreat.type === 'Ransomware' && (
                    <>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Regularly back up important data and store backups offline</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Keep all software and operating systems updated with the latest patches</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Use reputable antivirus and anti-malware software</span>
                      </li>
                    </>
                  )}
                  {activeThreat.type === 'DDoS' && (
                    <>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Implement DDoS protection services or anti-DDoS hardware</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Increase bandwidth capacity (bandwidth overprovisioning)</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Use Content Delivery Networks (CDNs) to distribute traffic</span>
                      </li>
                    </>
                  )}
                  {activeThreat.type === 'Phishing' && (
                    <>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Implement email filtering and authentication (SPF, DKIM, DMARC)</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Conduct regular phishing awareness training for all users</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Enable multi-factor authentication on all accounts</span>
                      </li>
                    </>
                  )}
                  {activeThreat.type === 'Malware' && (
                    <>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Install and maintain reputable antivirus/anti-malware software</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Keep all software and operating systems updated</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Be cautious when downloading files or clicking on links</span>
                      </li>
                    </>
                  )}
                  {activeThreat.type === 'Data Breach' && (
                    <>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Encrypt sensitive data both in transit and at rest</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Implement strong access controls and authentication</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Regularly update and patch all systems</span>
                      </li>
                    </>
                  )}
                  {activeThreat.type === 'Brute Force' && (
                    <>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Implement strong password policies (length, complexity, regular changes)</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Use multi-factor authentication</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Implement account lockout policies after failed attempts</span>
                      </li>
                    </>
                  )}
                  {activeThreat.type === 'Port Scan' && (
                    <>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Configure firewalls to block unauthorized port scans</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Use intrusion detection/prevention systems</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Close unnecessary ports and disable unused services</span>
                      </li>
                    </>
                  )}
                  {(activeThreat.type !== 'Ransomware' && activeThreat.type !== 'DDoS' &&
                    activeThreat.type !== 'Phishing' && activeThreat.type !== 'Malware' &&
                    activeThreat.type !== 'Data Breach' && activeThreat.type !== 'Brute Force' &&
                    activeThreat.type !== 'Port Scan') && (
                    <>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Keep all software and systems updated with security patches</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Use strong, unique passwords and multi-factor authentication</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-[#88cc14] mr-2">•</span>
                        <span className="text-gray-300">Implement comprehensive security controls including firewalls and antivirus</span>
                      </li>
                    </>
                  )}
                </ul>
              </div>
            </div>
            
            {/* Footer */}
            <div className="bg-gray-900/50 p-4 border-t border-gray-700 flex justify-between items-center">
              <div className="text-xs text-gray-500">
                Educational content provided by XCerberus Security
              </div>
              <button 
                onClick={() => setShowLearnMore(false)}
                className="bg-[#88cc14]/20 hover:bg-[#88cc14]/30 text-[#88cc14] px-4 py-2 rounded border border-[#88cc14]/30 transition-colors text-sm"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#88cc14]"></div>
          <p className="text-white ml-3">Loading threat data...</p>
        </div>
      )}
    </div>
  );
};

export default CyberThreatMap;
