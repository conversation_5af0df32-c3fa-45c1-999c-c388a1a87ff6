import React, { useState, useEffect, useRef } from 'react';
import { FaExclamationTriangle, FaGlobe, FaServer, FaShieldAlt, FaSearch, FaChartBar } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import threatAnalytics from '../../services/api/threatAnalyticsService';

const ThreatAnalyticsDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [report, setReport] = useState(null);
  const [selectedIP, setSelectedIP] = useState(null);
  const [ipDetails, setIPDetails] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [searchType, setSearchType] = useState('ip'); // 'ip' or 'domain'
  const chartRef = useRef(null);
  const geoChartRef = useRef(null);

  // Initialize and load data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Initialize threat analytics service
        await threatAnalytics.initialize();

        // Generate threat report
        const threatReport = await threatAnalytics.generateThreatReport({
          title: 'Current Threat Landscape'
        });

        setReport(threatReport);
      } catch (err) {
        console.error('Error loading threat analytics data:', err);

        // Provide more specific error message
        if (err.message && err.message.includes('Network Error')) {
          setError('Network error connecting to threat intelligence APIs. Using proxy to retry connection...');
        } else if (err.response && err.response.status === 403) {
          setError('API access forbidden. Please check your API keys and try again.');
        } else if (err.response && err.response.status === 429) {
          setError('API rate limit exceeded. Please wait a moment and try again.');
        } else {
          setError('Unable to connect to threat analytics API. Retrying connection...');
        }

        // Schedule a retry after 5 seconds
        setTimeout(() => {
          if (!report) {
            setError('Retrying connection to threat intelligence APIs...');
            loadData();
          }
        }, 5000);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Draw charts when report data is available
  useEffect(() => {
    if (report && chartRef.current) {
      drawAttackVectorsChart();
    }

    if (report && geoChartRef.current) {
      drawGeoDistributionChart();
    }
  }, [report, darkMode]);

  // Draw attack vectors chart
  const drawAttackVectorsChart = () => {
    const canvas = chartRef.current;
    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set dimensions
    const width = canvas.width;
    const height = canvas.height;
    const barWidth = 40;
    const barSpacing = 20;
    const maxBarHeight = height - 60;

    // Get attack vectors data
    const vectors = Object.entries(report.attackVectors)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);

    if (vectors.length === 0) return;

    // Find maximum value for scaling
    const maxValue = Math.max(...vectors.map(([_, value]) => value));

    // Draw bars
    vectors.forEach(([vector, value], index) => {
      const x = 60 + index * (barWidth + barSpacing);
      const barHeight = (value / maxValue) * maxBarHeight;
      const y = height - 40 - barHeight;

      // Draw bar
      const gradient = ctx.createLinearGradient(x, y, x, height - 40);

      // Set colors based on vector type
      let startColor, endColor;

      switch(vector) {
        case 'Ransomware':
          startColor = '#ff0066';
          endColor = '#990033';
          break;
        case 'Phishing':
          startColor = '#3366ff';
          endColor = '#003399';
          break;
        case 'Malware':
          startColor = '#ff9900';
          endColor = '#cc6600';
          break;
        case 'DDoS':
          startColor = '#00cc99';
          endColor = '#006633';
          break;
        case 'Exploit':
          startColor = '#cc33ff';
          endColor = '#660099';
          break;
        default:
          startColor = '#999999';
          endColor = '#666666';
      }

      gradient.addColorStop(0, startColor);
      gradient.addColorStop(1, endColor);

      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.roundRect(x, y, barWidth, barHeight, 5);
      ctx.fill();

      // Draw value on top of bar
      ctx.fillStyle = darkMode ? '#ffffff' : '#000000';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(value, x + barWidth / 2, y - 5);

      // Draw vector name below bar
      ctx.fillStyle = darkMode ? '#cccccc' : '#333333';
      ctx.font = '10px Arial';
      ctx.fillText(vector, x + barWidth / 2, height - 20);
    });

    // Draw title
    ctx.fillStyle = darkMode ? '#ffffff' : '#000000';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Top Attack Vectors', width / 2, 20);
  };

  // Draw geographic distribution chart
  const drawGeoDistributionChart = () => {
    const canvas = geoChartRef.current;
    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set dimensions
    const width = canvas.width;
    const height = canvas.height;
    const pieRadius = Math.min(width, height) / 2 - 40;
    const centerX = width / 2;
    const centerY = height / 2;

    // Get geo distribution data
    const geoData = Object.entries(report.geographicDistribution)
      .sort((a, b) => b[1] - a[1]);

    if (geoData.length === 0) return;

    // Calculate total
    const total = geoData.reduce((sum, [_, value]) => sum + value, 0);

    // Define colors
    const colors = [
      '#ff3366', '#3366ff', '#33cc33', '#ff9900', '#9966ff',
      '#ff6666', '#66ccff', '#99cc00', '#ffcc00', '#cc66ff'
    ];

    // Draw pie chart
    let startAngle = 0;
    const legend = [];

    geoData.forEach(([country, value], index) => {
      const sliceAngle = (value / total) * 2 * Math.PI;
      const endAngle = startAngle + sliceAngle;
      const midAngle = startAngle + sliceAngle / 2;

      // Draw slice
      ctx.fillStyle = colors[index % colors.length];
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, pieRadius, startAngle, endAngle);
      ctx.closePath();
      ctx.fill();

      // Draw percentage if slice is big enough
      if (sliceAngle > 0.2) {
        const percent = Math.round((value / total) * 100);
        const labelRadius = pieRadius * 0.7;
        const labelX = centerX + Math.cos(midAngle) * labelRadius;
        const labelY = centerY + Math.sin(midAngle) * labelRadius;

        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`${percent}%`, labelX, labelY);
      }

      // Add to legend
      legend.push({
        country,
        value,
        percent: Math.round((value / total) * 100),
        color: colors[index % colors.length]
      });

      startAngle = endAngle;
    });

    // Draw legend
    const legendX = 20;
    let legendY = height - 20 - (legend.length * 20);

    legend.forEach(item => {
      // Draw color box
      ctx.fillStyle = item.color;
      ctx.fillRect(legendX, legendY, 15, 15);

      // Draw text
      ctx.fillStyle = darkMode ? '#ffffff' : '#000000';
      ctx.font = '12px Arial';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'middle';
      ctx.fillText(`${item.country} (${item.percent}%)`, legendX + 20, legendY + 7);

      legendY += 20;
    });

    // Draw title
    ctx.fillStyle = darkMode ? '#ffffff' : '#000000';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Geographic Distribution', width / 2, 20);
  };

  // Handle IP selection
  const handleIPSelect = async (ip) => {
    try {
      setSelectedIP(ip);
      setLoading(true);
      setError(null);

      // Get detailed IP analysis
      const analysis = await threatAnalytics.correlateIPData(ip);
      setIPDetails(analysis);
    } catch (err) {
      console.error(`Error analyzing IP ${ip}:`, err);
      setError(`Failed to analyze IP ${ip}.`);
      setIPDetails(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    try {
      setLoading(true);
      setError(null);

      if (searchType === 'ip') {
        // Analyze IP
        const analysis = await threatAnalytics.correlateIPData(searchQuery);
        setSearchResults({ type: 'ip', data: analysis });
      } else {
        // Analyze domain
        const analysis = await threatAnalytics.analyzeDomain(searchQuery);
        setSearchResults({ type: 'domain', data: analysis });
      }
    } catch (err) {
      console.error(`Error searching for "${searchQuery}":`, err);
      setError(`Failed to analyze ${searchType === 'ip' ? 'IP' : 'domain'} "${searchQuery}".`);
      setSearchResults(null);
    } finally {
      setLoading(false);
    }
  };

  // Render loading state
  if (loading && !report) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error && !report) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-xl font-bold mb-4">Advanced Threat Analytics</h2>
        <p className="text-gray-400 mb-4">
          In-depth analysis of threat data from multiple intelligence sources.
        </p>

        <div className="bg-blue-900 bg-opacity-20 border border-blue-800 rounded-lg p-4 mb-4 text-blue-400">
          <FaExclamationTriangle className="inline-block mr-2" />
          {error}
        </div>

        <div className="flex justify-center mt-6">
          <div className="animate-pulse flex space-x-4 items-center">
            <div className="rounded-full bg-blue-700 h-3 w-3"></div>
            <div className="rounded-full bg-blue-700 h-3 w-3 animate-animation-delay-200"></div>
            <div className="rounded-full bg-blue-700 h-3 w-3 animate-animation-delay-400"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Threat Analytics Dashboard</h2>
        <form onSubmit={handleSearch} className="flex">
          <div className="mr-2">
            <select
              className="bg-gray-700 border border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchType}
              onChange={(e) => setSearchType(e.target.value)}
            >
              <option value="ip">IP</option>
              <option value="domain">Domain</option>
            </select>
          </div>
          <input
            type="text"
            placeholder={`Search ${searchType === 'ip' ? 'IP address' : 'domain'}...`}
            className="bg-gray-700 border border-gray-600 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 rounded-r px-4 py-2 flex items-center"
            disabled={loading}
          >
            <FaSearch className="mr-2" />
            Analyze
          </button>
        </form>
      </div>

      {/* Search Results */}
      {searchResults && (
        <div className="mb-6 bg-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">
            Analysis Results: {searchResults.type === 'ip' ? 'IP Address' : 'Domain'} {searchQuery}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-800 p-3 rounded">
              <div className="text-sm text-gray-400">Risk Score</div>
              <div className="text-2xl font-bold">{searchResults.data.riskScore}/100</div>
              <div className={`text-sm ${
                searchResults.data.riskLevel === 'Critical' ? 'text-red-400' :
                searchResults.data.riskLevel === 'High' ? 'text-orange-400' :
                searchResults.data.riskLevel === 'Medium' ? 'text-yellow-400' :
                'text-green-400'
              }`}>
                {searchResults.data.riskLevel} Risk
              </div>
            </div>

            <div className="bg-gray-800 p-3 rounded">
              <div className="text-sm text-gray-400">Data Sources</div>
              <div className="flex flex-wrap gap-2 mt-1">
                {searchResults.data.sources.map((source, idx) => (
                  <span key={idx} className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    {source}
                  </span>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 p-3 rounded">
              <div className="text-sm text-gray-400">Analysis Time</div>
              <div>{new Date(searchResults.data.analysisTimestamp).toLocaleString()}</div>
            </div>
          </div>

          {/* Risk Factors */}
          {searchResults.data.riskFactors.length > 0 && (
            <div className="mb-4">
              <h4 className="font-semibold mb-2">Risk Factors</h4>
              <ul className="list-disc list-inside space-y-1 pl-2">
                {searchResults.data.riskFactors.map((factor, idx) => (
                  <li key={idx} className="text-yellow-300">{factor}</li>
                ))}
              </ul>
            </div>
          )}

          {/* IP-specific data */}
          {searchResults.type === 'ip' && searchResults.data.geoData && (
            <div className="mb-4">
              <h4 className="font-semibold mb-2">Geolocation Data</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">Country</div>
                  <div>{searchResults.data.geoData.countryName || 'Unknown'}</div>
                </div>
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">ISP</div>
                  <div>{searchResults.data.geoData.isp || 'Unknown'}</div>
                </div>
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">Domain</div>
                  <div>{searchResults.data.geoData.domain || 'Unknown'}</div>
                </div>
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">Usage Type</div>
                  <div>{searchResults.data.geoData.usageType || 'Unknown'}</div>
                </div>
              </div>
            </div>
          )}

          {/* Domain-specific data */}
          {searchResults.type === 'domain' && searchResults.data.whois && (
            <div className="mb-4">
              <h4 className="font-semibold mb-2">WHOIS Data</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">Creation Date</div>
                  <div>{searchResults.data.whois.creationDate ? new Date(searchResults.data.whois.creationDate).toLocaleDateString() : 'Unknown'}</div>
                </div>
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">Expiration Date</div>
                  <div>{searchResults.data.whois.expirationDate ? new Date(searchResults.data.whois.expirationDate).toLocaleDateString() : 'Unknown'}</div>
                </div>
                <div className="bg-gray-800 p-2 rounded">
                  <div className="text-xs text-gray-400">Registrar</div>
                  <div>{searchResults.data.whois.registrar || 'Unknown'}</div>
                </div>
              </div>
            </div>
          )}

          {/* Related Indicators */}
          {searchResults.data.relatedIndicators && searchResults.data.relatedIndicators.length > 0 && (
            <div>
              <h4 className="font-semibold mb-2">Related Indicators</h4>
              <div className="max-h-40 overflow-y-auto">
                <table className="w-full">
                  <thead className="bg-gray-800">
                    <tr>
                      <th className="px-2 py-1 text-left text-xs">Type</th>
                      <th className="px-2 py-1 text-left text-xs">Indicator</th>
                      <th className="px-2 py-1 text-left text-xs">Source</th>
                    </tr>
                  </thead>
                  <tbody>
                    {searchResults.data.relatedIndicators.slice(0, 10).map((indicator, idx) => (
                      <tr key={idx} className="border-t border-gray-700">
                        <td className="px-2 py-1 text-xs">{indicator.type}</td>
                        <td className="px-2 py-1 text-xs font-mono">{indicator.indicator}</td>
                        <td className="px-2 py-1 text-xs">{indicator.title}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Threat Summary */}
      {report && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">{report.title}</h3>
          <p className="text-sm text-gray-400 mb-4">
            Generated on {new Date(report.generatedAt).toLocaleString()}
          </p>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="bg-gray-700 p-3 rounded text-center">
              <div className="text-sm text-gray-400">Total Threats</div>
              <div className="text-2xl font-bold">{report.summary.totalThreats}</div>
            </div>
            <div className="bg-gray-700 p-3 rounded text-center">
              <div className="text-sm text-gray-400">Critical</div>
              <div className="text-2xl font-bold text-red-400">{report.summary.criticalThreats}</div>
            </div>
            <div className="bg-gray-700 p-3 rounded text-center">
              <div className="text-sm text-gray-400">High</div>
              <div className="text-2xl font-bold text-orange-400">{report.summary.highThreats}</div>
            </div>
            <div className="bg-gray-700 p-3 rounded text-center">
              <div className="text-sm text-gray-400">Medium</div>
              <div className="text-2xl font-bold text-yellow-400">{report.summary.mediumThreats}</div>
            </div>
            <div className="bg-gray-700 p-3 rounded text-center">
              <div className="text-sm text-gray-400">Low</div>
              <div className="text-2xl font-bold text-green-400">{report.summary.lowThreats}</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Attack Vectors Chart */}
            <div className="bg-gray-700 p-4 rounded-lg">
              <canvas
                ref={chartRef}
                width="400"
                height="250"
                className="w-full"
              />
            </div>

            {/* Geographic Distribution Chart */}
            <div className="bg-gray-700 p-4 rounded-lg">
              <canvas
                ref={geoChartRef}
                width="400"
                height="250"
                className="w-full"
              />
            </div>
          </div>
        </div>
      )}

      {/* Top Threats */}
      {report && report.topThreats && report.topThreats.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Top Threats</h3>
          <div className="bg-gray-700 rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-800">
                <tr>
                  <th className="px-4 py-2 text-left">IP Address</th>
                  <th className="px-4 py-2 text-left">Risk Score</th>
                  <th className="px-4 py-2 text-left">Risk Level</th>
                  <th className="px-4 py-2 text-left">Country</th>
                  <th className="px-4 py-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {report.topThreats.map((threat, index) => (
                  <tr
                    key={index}
                    className={`border-t border-gray-600 hover:bg-gray-600 cursor-pointer ${
                      selectedIP === threat.ip ? 'bg-gray-600' : ''
                    }`}
                  >
                    <td className="px-4 py-2 font-mono">{threat.ip}</td>
                    <td className="px-4 py-2">{threat.riskScore}/100</td>
                    <td className="px-4 py-2">
                      <span
                        className={`px-2 py-1 rounded text-xs ${
                          threat.riskLevel === 'Critical' ? 'bg-red-900 text-red-200' :
                          threat.riskLevel === 'High' ? 'bg-orange-900 text-orange-200' :
                          threat.riskLevel === 'Medium' ? 'bg-yellow-900 text-yellow-200' :
                          'bg-green-900 text-green-200'
                        }`}
                      >
                        {threat.riskLevel}
                      </span>
                    </td>
                    <td className="px-4 py-2">{threat.countryName}</td>
                    <td className="px-4 py-2">
                      <button
                        onClick={() => handleIPSelect(threat.ip)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs"
                      >
                        Analyze
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Recommendations */}
      {report && report.recommendations && report.recommendations.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Security Recommendations</h3>
          <div className="bg-gray-700 p-4 rounded-lg">
            <ul className="list-disc list-inside space-y-2 pl-2">
              {report.recommendations.map((recommendation, idx) => (
                <li key={idx} className="text-blue-300">{recommendation}</li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThreatAnalyticsDashboard;
