import React, { useState, useEffect, useRef } from 'react';
import { FaSearch, FaNetworkWired, FaInfoCircle, FaExclamationTriangle, FaGlobe, FaServer, FaShieldAlt, FaUserSecret, FaVirus } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import threatCorrelationService from '../../services/threatCorrelationService';

/**
 * ThreatCorrelationGraph Component
 *
 * Visualizes relationships between different threat entities using a force-directed graph.
 * Allows users to explore connections between IPs, domains, malware, vulnerabilities, and threat actors.
 */
const ThreatCorrelationGraph = () => {
  const { darkMode } = useGlobalTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('ip');
  const [graph, setGraph] = useState({ nodes: [], edges: [] });
  const [selectedNode, setSelectedNode] = useState(null);
  const [showInfoPanel, setShowInfoPanel] = useState(false);

  const canvasRef = useRef(null);
  const simulationRef = useRef(null);

  // Initialize the correlation service
  useEffect(() => {
    const initService = async () => {
      try {
        // Try to initialize the service, but handle errors gracefully
        if (threatCorrelationService && typeof threatCorrelationService.initialize === 'function') {
          await threatCorrelationService.initialize();
        } else {
          console.warn('Threat correlation service not properly initialized');
          // Don't set error here to allow the component to still function with sample data
        }
      } catch (err) {
        console.error('Error initializing threat correlation service:', err);
        setError('Failed to initialize threat correlation service. Using sample data instead.');
      }
    };

    initService();
  }, []);

  // Handle search
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setLoading(true);
      setError(null);
      setSelectedNode(null);

      // Create starting node based on search type
      const startNode = {
        id: `${searchType}-${searchQuery}`,
        type: searchType,
        label: searchQuery,
        data: { source: 'User Search' }
      };

      // Build the threat graph
      let threatGraph;

      if (threatCorrelationService && typeof threatCorrelationService.buildThreatGraph === 'function') {
        // Try to use the service
        threatGraph = await threatCorrelationService.buildThreatGraph(startNode, 2);
      } else {
        // Fallback to sample data if service is not available
        console.warn('Using sample threat graph data');
        threatGraph = generateSampleThreatGraph(startNode);
      }

      setGraph(threatGraph);
      setLoading(false);
    } catch (err) {
      console.error('Error building threat graph:', err);
      setError('Failed to build threat graph. Using sample data instead.');

      // Fallback to sample data on error
      const sampleGraph = generateSampleThreatGraph({
        id: `${searchType}-${searchQuery}`,
        type: searchType,
        label: searchQuery,
        data: { source: 'User Search' }
      });

      setGraph(sampleGraph);
      setLoading(false);
    }
  };

  // Generate sample threat graph data
  const generateSampleThreatGraph = (startNode) => {
    const graph = {
      nodes: [
        {
          id: startNode.id,
          type: startNode.type,
          label: startNode.label,
          data: startNode.data,
          x: 400,
          y: 300
        }
      ],
      edges: []
    };

    // Add sample related nodes based on node type
    if (startNode.type === 'ip') {
      // Add malware nodes
      graph.nodes.push(
        {
          id: 'malware-emotet',
          type: 'malware',
          label: 'Emotet',
          data: { source: 'VirusTotal' },
          x: 250,
          y: 200
        },
        {
          id: 'malware-trickbot',
          type: 'malware',
          label: 'TrickBot',
          data: { source: 'VirusTotal' },
          x: 550,
          y: 200
        }
      );

      // Add edges
      graph.edges.push(
        {
          source: startNode.id,
          target: 'malware-emotet',
          label: 'hosts',
          confidence: 'high'
        },
        {
          source: startNode.id,
          target: 'malware-trickbot',
          label: 'hosts',
          confidence: 'medium'
        }
      );
    } else if (startNode.type === 'domain') {
      // Add IP nodes
      graph.nodes.push(
        {
          id: 'ip-***********',
          type: 'ip',
          label: '***********',
          data: { source: 'DNS Records' },
          x: 250,
          y: 200
        },
        {
          id: 'ip-***********',
          type: 'ip',
          label: '***********',
          data: { source: 'DNS Records' },
          x: 550,
          y: 200
        }
      );

      // Add edges
      graph.edges.push(
        {
          source: startNode.id,
          target: 'ip-***********',
          label: 'resolves to',
          confidence: 'high'
        },
        {
          source: startNode.id,
          target: 'ip-***********',
          label: 'resolves to',
          confidence: 'high'
        }
      );
    } else if (startNode.type === 'vulnerability') {
      // Add actor nodes
      graph.nodes.push(
        {
          id: 'actor-apt29',
          type: 'actor',
          label: 'APT29',
          data: { source: 'Threat Intelligence' },
          x: 250,
          y: 200
        },
        {
          id: 'actor-apt28',
          type: 'actor',
          label: 'APT28',
          data: { source: 'Threat Intelligence' },
          x: 550,
          y: 200
        }
      );

      // Add edges
      graph.edges.push(
        {
          source: startNode.id,
          target: 'actor-apt29',
          label: 'exploited by',
          confidence: 'medium'
        },
        {
          source: startNode.id,
          target: 'actor-apt28',
          label: 'exploited by',
          confidence: 'low'
        }
      );
    } else if (startNode.type === 'malware') {
      // Add technique nodes
      graph.nodes.push(
        {
          id: 'technique-T1566',
          type: 'technique',
          label: 'T1566',
          data: { source: 'MITRE ATT&CK' },
          x: 250,
          y: 200
        },
        {
          id: 'technique-T1204',
          type: 'technique',
          label: 'T1204',
          data: { source: 'MITRE ATT&CK' },
          x: 550,
          y: 200
        }
      );

      // Add edges
      graph.edges.push(
        {
          source: startNode.id,
          target: 'technique-T1566',
          label: 'uses',
          confidence: 'high'
        },
        {
          source: startNode.id,
          target: 'technique-T1204',
          label: 'uses',
          confidence: 'high'
        }
      );
    }

    return graph;
  };

  // Draw the graph when it changes
  useEffect(() => {
    if (graph.nodes.length > 0 && canvasRef.current) {
      drawGraph();
    }
  }, [graph, darkMode]);

  // Clean up simulation on unmount
  useEffect(() => {
    return () => {
      if (simulationRef.current) {
        simulationRef.current.stop();
      }
    };
  }, []);

  // Draw the force-directed graph
  const drawGraph = () => {
    // This is a simplified version - in a real implementation,
    // you would use D3.js or a similar library for force-directed graphs
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Set background
    ctx.fillStyle = darkMode ? '#1f2937' : '#f3f4f6';
    ctx.fillRect(0, 0, width, height);

    // Simple force-directed layout simulation
    // In a real implementation, this would be D3's force simulation
    const nodes = graph.nodes.map(node => ({
      ...node,
      x: node.x || Math.random() * width,
      y: node.y || Math.random() * height,
      radius: getNodeRadius(node.type)
    }));

    const edges = graph.edges.map(edge => ({
      ...edge,
      source: nodes.find(n => n.id === edge.source),
      target: nodes.find(n => n.id === edge.target)
    }));

    // Draw edges
    ctx.strokeStyle = darkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)';
    ctx.lineWidth = 1;

    edges.forEach(edge => {
      if (edge.source && edge.target) {
        ctx.beginPath();
        ctx.moveTo(edge.source.x, edge.source.y);
        ctx.lineTo(edge.target.x, edge.target.y);
        ctx.stroke();

        // Draw edge label
        const midX = (edge.source.x + edge.target.x) / 2;
        const midY = (edge.source.y + edge.target.y) / 2;

        ctx.fillStyle = darkMode ? '#d1d5db' : '#374151';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(edge.label, midX, midY - 5);
      }
    });

    // Draw nodes
    nodes.forEach(node => {
      // Draw circle
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
      ctx.fillStyle = getNodeColor(node.type);
      ctx.fill();

      // Draw border
      ctx.strokeStyle = darkMode ? '#374151' : '#d1d5db';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Draw label
      ctx.fillStyle = darkMode ? '#ffffff' : '#111827';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(node.label, node.x, node.y + node.radius + 15);
    });

    // Add click handler to canvas
    canvas.onclick = (event) => {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Check if a node was clicked
      const clickedNode = nodes.find(node => {
        const dx = node.x - x;
        const dy = node.y - y;
        return Math.sqrt(dx * dx + dy * dy) <= node.radius;
      });

      if (clickedNode) {
        setSelectedNode(clickedNode);
      } else {
        setSelectedNode(null);
      }
    };
  };

  // Get node radius based on type
  const getNodeRadius = (type) => {
    switch (type) {
      case 'ip':
        return 15;
      case 'domain':
        return 18;
      case 'malware':
        return 20;
      case 'vulnerability':
        return 17;
      case 'actor':
        return 22;
      case 'technique':
        return 16;
      default:
        return 15;
    }
  };

  // Get node color based on type
  const getNodeColor = (type) => {
    switch (type) {
      case 'ip':
        return '#3b82f6'; // blue
      case 'domain':
        return '#10b981'; // green
      case 'malware':
        return '#ef4444'; // red
      case 'vulnerability':
        return '#f59e0b'; // amber
      case 'actor':
        return '#8b5cf6'; // purple
      case 'technique':
        return '#ec4899'; // pink
      default:
        return '#6b7280'; // gray
    }
  };

  // Get icon based on node type
  const getNodeIcon = (type) => {
    switch (type) {
      case 'ip':
        return <FaServer className="text-blue-500" />;
      case 'domain':
        return <FaGlobe className="text-green-500" />;
      case 'malware':
        return <FaVirus className="text-red-500" />;
      case 'vulnerability':
        return <FaExclamationTriangle className="text-amber-500" />;
      case 'actor':
        return <FaUserSecret className="text-purple-500" />;
      case 'technique':
        return <FaShieldAlt className="text-pink-500" />;
      default:
        return <FaInfoCircle className="text-gray-500" />;
    }
  };

  // Get node type label
  const getNodeTypeLabel = (type) => {
    switch (type) {
      case 'ip':
        return 'IP Address';
      case 'domain':
        return 'Domain';
      case 'malware':
        return 'Malware';
      case 'vulnerability':
        return 'Vulnerability';
      case 'actor':
        return 'Threat Actor';
      case 'technique':
        return 'ATT&CK Technique';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold flex items-center">
          <FaNetworkWired className="mr-2 text-blue-400" /> Advanced Threat Correlation
          <button
            className="ml-2 text-gray-400 hover:text-gray-300"
            onClick={() => setShowInfoPanel(!showInfoPanel)}
            title="Information about threat correlation"
          >
            <FaInfoCircle size={14} />
          </button>
        </h3>

        <div className="text-xs text-gray-400">
          Updated {new Date().toLocaleString()}
        </div>
      </div>

      {/* Information Panel */}
      {showInfoPanel && (
        <div className="mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm">
          <div className="flex justify-between items-start">
            <h4 className="font-semibold mb-2 flex items-center">
              <FaNetworkWired className="mr-1 text-blue-400" /> Understanding Threat Correlation
            </h4>
            <button
              className="text-gray-400 hover:text-gray-300"
              onClick={() => setShowInfoPanel(false)}
            >
              ×
            </button>
          </div>
          <p className="text-gray-300 mb-2">
            Threat correlation analyzes relationships between different threat indicators to identify
            connections that might not be apparent when looking at individual data points. This visualization
            shows how IPs, domains, malware, vulnerabilities, and threat actors are interconnected.
          </p>
          <div className="grid grid-cols-3 gap-2 mb-2">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-blue-500 mr-1"></div>
              <span>IP Address</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-green-500 mr-1"></div>
              <span>Domain</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
              <span>Malware</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-amber-500 mr-1"></div>
              <span>Vulnerability</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-purple-500 mr-1"></div>
              <span>Threat Actor</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-pink-500 mr-1"></div>
              <span>ATT&CK Technique</span>
            </div>
          </div>
        </div>
      )}

      {/* Search bar */}
      <div className="mb-4">
        <div className="flex">
          <select
            className="bg-gray-700 border border-gray-600 rounded-l-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
            value={searchType}
            onChange={(e) => setSearchType(e.target.value)}
          >
            <option value="ip">IP Address</option>
            <option value="domain">Domain</option>
            <option value="malware">Malware</option>
            <option value="vulnerability">Vulnerability (CVE)</option>
          </select>
          <input
            type="text"
            className="flex-1 bg-gray-700 border-t border-b border-gray-600 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder={`Enter ${searchType}...`}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-r-lg px-4 py-2 text-sm"
            onClick={handleSearch}
            disabled={loading}
          >
            {loading ? 'Searching...' : <FaSearch />}
          </button>
        </div>
        <div className="mt-1 text-xs text-gray-400">
          Example searches: ******* (IP), example.com (Domain), Emotet (Malware), CVE-2021-44228 (Vulnerability)
        </div>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm">
          <FaExclamationTriangle className="inline-block mr-2" />
          {error}
        </div>
      )}

      {/* Main content area - split into two columns when a node is selected */}
      <div className="flex-1 flex flex-col md:flex-row gap-4 overflow-hidden">
        {/* Graph visualization */}
        <div className={`bg-gray-800 rounded-lg p-4 ${selectedNode ? 'md:w-2/3' : 'w-full'}`}>
          {graph.nodes.length === 0 ? (
            <div className="h-full flex flex-col items-center justify-center text-gray-400">
              <FaNetworkWired className="text-4xl mb-4" />
              <p>Search for an indicator to visualize threat correlations</p>
            </div>
          ) : (
            <canvas
              ref={canvasRef}
              width={800}
              height={600}
              className="w-full h-full rounded-lg"
            />
          )}
        </div>

        {/* Node details panel - only visible when a node is selected */}
        {selectedNode && (
          <div className="md:w-1/3 bg-gray-800 rounded-lg p-4 overflow-y-auto">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold flex items-center">
                {getNodeIcon(selectedNode.type)}
                <span className="ml-2">{getNodeTypeLabel(selectedNode.type)} Details</span>
              </h3>
              <button
                className="text-gray-400 hover:text-gray-300"
                onClick={() => setSelectedNode(null)}
              >
                ×
              </button>
            </div>

            {/* Node details */}
            <div className="mb-4">
              <div className="bg-gray-700 p-3 rounded-lg mb-4">
                <div className="mb-2">
                  <div className="text-xs text-gray-400">Identifier</div>
                  <div className="font-medium">{selectedNode.label}</div>
                </div>
                <div className="mb-2">
                  <div className="text-xs text-gray-400">Type</div>
                  <div className="font-medium">{getNodeTypeLabel(selectedNode.type)}</div>
                </div>
                {selectedNode.data && selectedNode.data.source && (
                  <div>
                    <div className="text-xs text-gray-400">Source</div>
                    <div className="font-medium">{selectedNode.data.source}</div>
                  </div>
                )}
              </div>

              {/* Connected nodes */}
              <div className="mb-4">
                <h4 className="font-medium mb-2">Connected Entities</h4>
                <div className="space-y-2">
                  {graph.edges
                    .filter(edge => edge.source === selectedNode.id || edge.target === selectedNode.id)
                    .map((edge, index) => {
                      const connectedNodeId = edge.source === selectedNode.id ? edge.target : edge.source;
                      const connectedNode = graph.nodes.find(node => node.id === connectedNodeId);
                      const relationship = edge.source === selectedNode.id ? edge.label : `is ${edge.label} by`;

                      return connectedNode ? (
                        <div key={index} className="bg-gray-700 p-2 rounded-lg flex items-center">
                          {getNodeIcon(connectedNode.type)}
                          <div className="ml-2">
                            <div className="text-sm font-medium">{connectedNode.label}</div>
                            <div className="text-xs text-gray-400 flex items-center">
                              <span className="mr-1">{getNodeTypeLabel(connectedNode.type)}</span> •
                              <span className="mx-1">{relationship}</span>
                            </div>
                          </div>
                        </div>
                      ) : null;
                    })}
                </div>
              </div>

              {/* Type-specific information */}
              {selectedNode.type === 'ip' && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">IP Intelligence</h4>
                  <div className="bg-gray-700 p-3 rounded-lg">
                    <p className="text-sm mb-2">
                      This IP address may be associated with malicious activity. Consider blocking it in your firewall
                      or monitoring for connections to/from this address.
                    </p>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <div className="text-xs text-gray-400">Reputation</div>
                        <div className="font-medium text-red-400">Malicious</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-400">Confidence</div>
                        <div className="font-medium">High</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {selectedNode.type === 'malware' && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Malware Analysis</h4>
                  <div className="bg-gray-700 p-3 rounded-lg">
                    <p className="text-sm mb-2">
                      This malware is known to be used in targeted attacks. It typically gains initial access through
                      phishing emails and establishes persistence using scheduled tasks.
                    </p>
                    <div className="text-xs text-gray-400 mb-1">Common Techniques</div>
                    <div className="flex flex-wrap gap-1 mb-2">
                      <span className="bg-gray-600 px-2 py-0.5 rounded text-xs">Data Exfiltration</span>
                      <span className="bg-gray-600 px-2 py-0.5 rounded text-xs">Credential Theft</span>
                      <span className="bg-gray-600 px-2 py-0.5 rounded text-xs">Persistence</span>
                    </div>
                  </div>
                </div>
              )}

              {selectedNode.type === 'vulnerability' && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Vulnerability Details</h4>
                  <div className="bg-gray-700 p-3 rounded-lg">
                    <p className="text-sm mb-2">
                      This vulnerability is being actively exploited in the wild. Prioritize patching systems
                      that are affected by this vulnerability.
                    </p>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <div className="text-xs text-gray-400">Severity</div>
                        <div className="font-medium text-red-400">Critical</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-400">Exploitation</div>
                        <div className="font-medium">Active</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {selectedNode.type === 'actor' && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Threat Actor Profile</h4>
                  <div className="bg-gray-700 p-3 rounded-lg">
                    <p className="text-sm mb-2">
                      This threat actor is known for targeting organizations in the financial and healthcare sectors.
                      They typically maintain long-term access to compromised networks.
                    </p>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <div className="text-xs text-gray-400">Motivation</div>
                        <div className="font-medium">Financial Gain</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-400">Sophistication</div>
                        <div className="font-medium">High</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Recommended actions */}
            <div>
              <h4 className="font-medium mb-2">Recommended Actions</h4>
              <ul className="list-disc list-inside text-sm space-y-1 text-gray-300">
                <li>Investigate all connected entities in your environment</li>
                <li>Update detection rules to identify this threat pattern</li>
                <li>Share this intelligence with your security team</li>
                <li>Implement specific mitigations based on the threat type</li>
              </ul>
            </div>
          </div>
        )}
      </div>

      <div className="mt-4 text-xs text-gray-400">
        <strong>Data Sources:</strong> Multiple threat intelligence feeds •
        <strong>Analysis:</strong> Advanced correlation algorithms across disparate data sources
      </div>
    </div>
  );
};

export default ThreatCorrelationGraph;
