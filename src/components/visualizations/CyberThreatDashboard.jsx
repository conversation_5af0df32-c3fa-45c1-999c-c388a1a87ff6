import React, { useState, useEffect, useRef } from 'react';
import { FaShieldAlt, FaExclamationTriangle, FaLock, FaServer, FaWifi, FaGlobe, FaChartBar, FaUserShield, FaGlobeAmericas, FaRss } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import RealTimeThreatService from '../../services/RealTimeThreatService';
import LiveThreatFeed from './LiveThreatFeed';

// Attack types with colors and icons
const attackTypes = {
  'Ransomware': { color: '#ef4444', icon: FaExclamationTriangle },
  'DDoS': { color: '#f97316', icon: FaServer },
  'Malware': { color: '#8b5cf6', icon: FaLock },
  'Phishing': { color: '#3b82f6', icon: FaWifi },
  'Infrastructure': { color: '#10b981', icon: FaGlobe },
};

// Country data with regions
const countries = [
  { name: 'United States', code: 'US', region: 'North America', risk: 'High' },
  { name: 'Russia', code: 'RU', region: 'Europe', risk: 'Critical' },
  { name: 'China', code: 'CN', region: 'Asia', risk: 'Critical' },
  { name: 'North Korea', code: 'KP', region: 'Asia', risk: 'Critical' },
  { name: 'Iran', code: 'IR', region: 'Middle East', risk: 'High' },
  { name: 'United Kingdom', code: 'GB', region: 'Europe', risk: 'Medium' },
  { name: 'Germany', code: 'DE', region: 'Europe', risk: 'Medium' },
  { name: 'Ukraine', code: 'UA', region: 'Europe', risk: 'High' },
  { name: 'Japan', code: 'JP', region: 'Asia', risk: 'Medium' },
  { name: 'India', code: 'IN', region: 'Asia', risk: 'Medium' },
  { name: 'Brazil', code: 'BR', region: 'South America', risk: 'Medium' },
  { name: 'Australia', code: 'AU', region: 'Oceania', risk: 'Low' },
  { name: 'Canada', code: 'CA', region: 'North America', risk: 'Medium' },
  { name: 'France', code: 'FR', region: 'Europe', risk: 'Medium' },
  { name: 'Italy', code: 'IT', region: 'Europe', risk: 'Medium' },
  { name: 'Spain', code: 'ES', region: 'Europe', risk: 'Low' },
  { name: 'South Korea', code: 'KR', region: 'Asia', risk: 'Medium' },
  { name: 'Mexico', code: 'MX', region: 'North America', risk: 'Medium' },
  { name: 'Indonesia', code: 'ID', region: 'Asia', risk: 'Low' },
  { name: 'Turkey', code: 'TR', region: 'Middle East', risk: 'Medium' },
  { name: 'Saudi Arabia', code: 'SA', region: 'Middle East', risk: 'Medium' },
  { name: 'South Africa', code: 'ZA', region: 'Africa', risk: 'Low' },
  { name: 'Egypt', code: 'EG', region: 'Africa', risk: 'Medium' },
  { name: 'Pakistan', code: 'PK', region: 'Asia', risk: 'High' },
  { name: 'Bangladesh', code: 'BD', region: 'Asia', risk: 'Low' },
];

// Regions with colors
const regions = {
  'North America': '#0077b6',
  'South America': '#00b4d8',
  'Europe': '#90e0ef',
  'Asia': '#0096c7',
  'Africa': '#48cae4',
  'Middle East': '#ade8f4',
  'Oceania': '#caf0f8',
};

// Risk levels with colors
const riskLevels = {
  'Critical': '#ef4444',
  'High': '#f97316',
  'Medium': '#facc15',
  'Low': '#4ade80',
};

// Animated counter hook
const useCounter = (end, duration = 2000, start = 0) => {
  const [count, setCount] = useState(start);

  useEffect(() => {
    let startTime = null;
    const step = timestamp => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      setCount(Math.floor(progress * (end - start) + start));
      if (progress < 1) {
        window.requestAnimationFrame(step);
      }
    };
    window.requestAnimationFrame(step);
  }, [end, duration, start]);

  return count;
};

// Threat Activity Chart Component
const ThreatActivityChart = ({ data }) => {
  const { darkMode } = useGlobalTheme();
  const canvasRef = useRef(null);

  useEffect(() => {
    if (!canvasRef.current || !data) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Background
    ctx.fillStyle = darkMode ? '#1f2937' : '#f1f5f9';
    ctx.fillRect(0, 0, width, height);

    // Draw grid lines
    ctx.strokeStyle = darkMode ? '#374151' : '#cbd5e1';
    ctx.lineWidth = 1;

    // Horizontal grid lines
    for (let i = 0; i < 5; i++) {
      const y = height - (i * (height / 4));
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // Prepare data points
    const points = Object.entries(data).map(([key, value], index) => ({
      x: (index / (Object.keys(data).length - 1)) * width,
      y: height - (value / 100) * height,
      value
    }));

    // Draw line
    ctx.strokeStyle = darkMode ? '#0ea5e9' : '#0284c7';
    ctx.lineWidth = 3;
    ctx.beginPath();
    points.forEach((point, i) => {
      if (i === 0) {
        ctx.moveTo(point.x, point.y);
      } else {
        ctx.lineTo(point.x, point.y);
      }
    });
    ctx.stroke();

    // Draw points
    points.forEach(point => {
      ctx.fillStyle = darkMode ? '#0ea5e9' : '#0284c7';
      ctx.beginPath();
      ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
      ctx.fill();

      // Draw value labels
      ctx.fillStyle = darkMode ? '#e5e7eb' : '#1f2937';
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(point.value.toString(), point.x, point.y - 10);
    });

    // Draw x-axis labels
    ctx.fillStyle = darkMode ? '#9ca3af' : '#4b5563';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    Object.keys(data).forEach((key, index) => {
      const x = (index / (Object.keys(data).length - 1)) * width;
      ctx.fillText(key, x, height - 5);
    });
  }, [data, darkMode]);

  useEffect(() => {
    // Handle high-DPI displays for crisp rendering
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      const dpr = window.devicePixelRatio || 1;

      // Get the CSS size of the canvas
      const rect = canvas.getBoundingClientRect();

      // Set the canvas dimensions accounting for device pixel ratio
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;

      // Scale the context to ensure correct drawing operations
      ctx.scale(dpr, dpr);

      // Set CSS size explicitly
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;
    }
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="w-full h-full"
      style={{ maxHeight: '300px' }}
    />
  );
};

// Regional Threat Distribution Component
const RegionalThreatDistribution = ({ data }) => {
  const { darkMode } = useGlobalTheme();

  // Calculate total
  const total = Object.values(data).reduce((sum, value) => sum + value, 0);

  // Sort regions by percentage (descending)
  const sortedRegions = Object.entries(data)
    .sort(([, valueA], [, valueB]) => valueB - valueA);

  return (
    <div className="space-y-4">
      {sortedRegions.map(([region, value]) => {
        const percentage = Math.round((value / total) * 100);
        return (
          <div key={region} className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div
                  className="w-4 h-4 rounded-full mr-3"
                  style={{ backgroundColor: regions[region] || '#cbd5e1' }}
                ></div>
                <span className="font-medium">{region}</span>
              </div>
              <div className="flex items-center">
                <span className="text-lg font-bold mr-1">{percentage}%</span>
                <span className="text-sm text-gray-400">({value} attacks)</span>
              </div>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5 overflow-hidden shadow-inner">
              <div
                className="h-full rounded-full transition-all duration-500 ease-out"
                style={{
                  width: `${percentage}%`,
                  backgroundColor: regions[region] || '#cbd5e1',
                  boxShadow: `0 0 8px ${regions[region] || '#cbd5e1'}`
                }}
              ></div>
            </div>
          </div>
        );
      })}

      <div className="mt-6 grid grid-cols-2 gap-3">
        {sortedRegions.slice(0, 2).map(([region, value]) => {
          const percentage = Math.round((value / total) * 100);
          return (
            <div key={`stat-${region}`} className="bg-gray-700 p-3 rounded-lg text-center">
              <div className="text-sm text-gray-400">Highest Threat Region</div>
              <div className="text-xl font-bold">{region}</div>
              <div
                className="text-sm font-medium"
                style={{ color: regions[region] || '#cbd5e1' }}
              >
                {percentage}% of all attacks
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Attack Type Distribution Component
const AttackTypeDistribution = ({ data }) => {
  const { darkMode } = useGlobalTheme();
  const canvasRef = useRef(null);

  useEffect(() => {
    if (!canvasRef.current || !data) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // Fixed dimensions approach to solve the white screen issue
    const width = 500;
    const height = 500;

    // Set canvas dimensions explicitly
    canvas.width = width;
    canvas.height = height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Set up the pie chart dimensions
    const radius = Math.min(width, height) / 2.5; // Larger radius for better visibility
    const centerX = width / 2;
    const centerY = height / 2;

    // Calculate total
    const total = Object.values(data).reduce((sum, value) => sum + value, 0);

    // Draw full pie chart - start from the top
    let startAngle = -Math.PI / 2; // Start from the top (12 o'clock position)

    // Draw all slices
    Object.entries(data).forEach(([type, value]) => {
      const sliceAngle = (value / total) * 2 * Math.PI;
      const endAngle = startAngle + sliceAngle;

      // Draw slice
      const baseColor = attackTypes[type]?.color || '#cbd5e1';

      ctx.fillStyle = baseColor;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();
      ctx.fill();

      // Draw slice border
      ctx.strokeStyle = darkMode ? '#1f2937' : '#ffffff';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();
      ctx.stroke();

      // Calculate percentage
      const percent = Math.round((value / total) * 100);

      // Position label
      const midAngle = startAngle + sliceAngle / 2;
      const labelDistance = radius * 0.7;
      const labelX = centerX + Math.cos(midAngle) * labelDistance;
      const labelY = centerY + Math.sin(midAngle) * labelDistance;

      // Only draw label if slice is big enough
      if (sliceAngle > 0.1) {
        const labelText = `${percent}%`;

        // Draw label background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.beginPath();
        ctx.arc(labelX, labelY, 15, 0, 2 * Math.PI);
        ctx.fill();

        // Draw label text
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(labelText, labelX, labelY);
      }

      startAngle = endAngle;
    });

    // Draw center circle (donut hole)
    ctx.fillStyle = darkMode ? '#1f2937' : '#f1f5f9';
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.3, 0, 2 * Math.PI);
    ctx.fill();

    // Add a subtle border to the center hole
    ctx.strokeStyle = darkMode ? '#374151' : '#e2e8f0';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.3, 0, 2 * Math.PI);
    ctx.stroke();
  }, [data, darkMode]);

  // We're handling the canvas dimensions directly in the main effect now

  return (
    <div className="relative w-full h-full">
      <canvas
        ref={canvasRef}
        width="500"
        height="500"
        className="w-full h-full"
        style={{ display: 'block' }}
      />
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="text-center bg-gray-900 bg-opacity-90 rounded-full p-6 shadow-xl border-2 border-gray-700">
          <div className="text-sm text-gray-300 uppercase tracking-wider font-semibold">TOTAL</div>
          <div className="text-4xl font-bold">{Object.values(data).reduce((sum, value) => sum + value, 0)}</div>
        </div>
      </div>
    </div>
  );
};

// Threat Feed List Component
const ThreatFeedList = ({ threats }) => {
  return (
    <div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
      {threats.map((threat, index) => {
        const Icon = attackTypes[threat.type]?.icon || FaExclamationTriangle;
        return (
          <div
            key={index}
            className="bg-gray-800 bg-opacity-50 p-2 rounded border border-gray-700 flex items-center"
          >
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
              style={{ backgroundColor: `${attackTypes[threat.type]?.color}20` }}
            >
              <Icon style={{ color: attackTypes[threat.type]?.color }} />
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <div className="font-medium">{threat.type}</div>
                <div className="text-xs text-gray-400">{threat.time}</div>
              </div>
              <div className="text-sm text-gray-400 flex items-center">
                <span>{threat.source}</span>
                <span className="mx-2">→</span>
                <span>{threat.target}</span>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

// Country Risk Matrix Component
const CountryRiskMatrix = ({ countries }) => {
  // Group countries by risk level
  const groupedByRisk = countries.reduce((acc, country) => {
    if (!acc[country.risk]) {
      acc[country.risk] = [];
    }
    acc[country.risk].push(country);
    return acc;
  }, {});

  // Risk level order and descriptions
  const riskOrder = { 'Critical': 0, 'High': 1, 'Medium': 2, 'Low': 3 };
  const riskDescriptions = {
    'Critical': 'Immediate action required',
    'High': 'Significant threat activity',
    'Medium': 'Moderate threat activity',
    'Low': 'Minimal threat activity'
  };

  return (
    <div className="space-y-6">
      {Object.entries(groupedByRisk)
        .sort(([riskA], [riskB]) => riskOrder[riskA] - riskOrder[riskB])
        .map(([risk, countriesInRisk]) => (
          <div key={risk} className="space-y-3">
            <div className="flex items-center">
              <div
                className="w-5 h-5 rounded-full mr-3 flex items-center justify-center"
                style={{ backgroundColor: riskLevels[risk] }}
              >
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
              <div>
                <h3 className="font-bold text-lg">{risk} Risk</h3>
                <p className="text-sm text-gray-400">{riskDescriptions[risk]}</p>
              </div>
              <div className="ml-auto bg-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                {countriesInRisk.length} countries
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {countriesInRisk.map(country => (
                <div
                  key={country.code}
                  className="px-3 py-2 rounded-lg flex items-center"
                  style={{
                    backgroundColor: `${riskLevels[risk]}15`,
                    border: `1px solid ${riskLevels[risk]}40`,
                  }}
                >
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: riskLevels[risk] }}
                  ></div>
                  <span
                    className="font-medium"
                    style={{ color: riskLevels[risk] }}
                  >
                    {country.name}
                  </span>
                </div>
              ))}
            </div>

            {risk === 'Critical' && (
              <div
                className="p-3 rounded-lg text-sm"
                style={{ backgroundColor: `${riskLevels[risk]}15`, border: `1px solid ${riskLevels[risk]}40` }}
              >
                <div className="flex items-center mb-1">
                  <FaExclamationTriangle className="mr-2" style={{ color: riskLevels[risk] }} />
                  <span className="font-bold" style={{ color: riskLevels[risk] }}>Critical Alert</span>
                </div>
                <p className="text-gray-300">These countries are currently experiencing significant cyber attack activity and require immediate attention.</p>
              </div>
            )}
          </div>
        ))}
    </div>
  );
};

// Main Dashboard Component
const CyberThreatDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [threatStats, setThreatStats] = useState({
    totalAttacks: 0,
    blockedAttacks: 0,
    criticalVulnerabilities: 0,
    securityScore: 0,
    attacksByType: {},
    attacksBySource: {},
    attacksBySeverity: {},
    attacksByRegion: {},
    recentThreats: []
  });

  // Animated counters
  const totalAttacks = useCounter(threatStats.totalAttacks);
  const blockedAttacks = useCounter(threatStats.blockedAttacks);
  const securityScore = useCounter(threatStats.securityScore);

  // Initialize threat service and get real-time data
  useEffect(() => {
    const threatService = RealTimeThreatService.getInstance().initialize();

    // Get initial statistics
    const stats = threatService.getStatistics();

    // Group attacks by region
    const attacksByRegion = {};
    Object.entries(stats.attacksBySource || {}).forEach(([country, count]) => {
      const foundCountry = countries.find(c => c.name === country);
      if (foundCountry) {
        const region = foundCountry.region;
        if (!attacksByRegion[region]) {
          attacksByRegion[region] = 0;
        }
        attacksByRegion[region] += count;
      }
    });

    // Generate recent threats
    const recentThreats = [];
    for (let i = 0; i < 10; i++) {
      const randomSourceIndex = Math.floor(Math.random() * countries.length);
      let randomTargetIndex = Math.floor(Math.random() * countries.length);
      while (randomTargetIndex === randomSourceIndex) {
        randomTargetIndex = Math.floor(Math.random() * countries.length);
      }

      const attackTypes = ['Ransomware', 'DDoS', 'Malware', 'Phishing', 'Infrastructure'];
      const randomTypeIndex = Math.floor(Math.random() * attackTypes.length);

      const hours = Math.floor(Math.random() * 24);
      const minutes = Math.floor(Math.random() * 60);
      const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

      recentThreats.push({
        source: countries[randomSourceIndex].name,
        target: countries[randomTargetIndex].name,
        type: attackTypes[randomTypeIndex],
        time: timeString
      });
    }

    // Mock threat activity over time
    const threatActivity = {
      '00:00': Math.floor(Math.random() * 50) + 20,
      '04:00': Math.floor(Math.random() * 50) + 20,
      '08:00': Math.floor(Math.random() * 50) + 20,
      '12:00': Math.floor(Math.random() * 50) + 20,
      '16:00': Math.floor(Math.random() * 50) + 20,
      '20:00': Math.floor(Math.random() * 50) + 20,
      '24:00': Math.floor(Math.random() * 50) + 20,
    };

    setThreatStats({
      totalAttacks: stats.totalAttacks || 32320,
      blockedAttacks: Math.floor((stats.totalAttacks || 32320) * 0.76), // 76% blocked
      criticalVulnerabilities: Math.floor(Math.random() * 5) + 1,
      securityScore: 78,
      attacksByType: stats.attacksByType || {
        'Ransomware': 35,
        'DDoS': 25,
        'Malware': 20,
        'Phishing': 15,
        'Infrastructure': 5
      },
      attacksBySource: stats.attacksBySource || {},
      attacksBySeverity: stats.attacksBySeverity || {},
      attacksByRegion: attacksByRegion || {
        'North America': 25,
        'Europe': 30,
        'Asia': 35,
        'Middle East': 5,
        'South America': 3,
        'Africa': 1,
        'Oceania': 1
      },
      recentThreats,
      threatActivity
    });

    // Add listener for updates
    threatService.addListener(data => {
      setThreatStats(prev => ({
        ...prev,
        totalAttacks: data.statistics.totalAttacks || 32320,
        blockedAttacks: Math.floor((data.statistics.totalAttacks || 32320) * 0.76),
        attacksByType: data.statistics.attacksByType || prev.attacksByType,
        attacksBySource: data.statistics.attacksBySource || prev.attacksBySource,
        attacksBySeverity: data.statistics.attacksBySeverity || prev.attacksBySeverity
      }));
    });

    // Start real-time updates
    threatService.startRealTimeUpdates();

    // Cleanup
    return () => {
      threatService.removeListener(setThreatStats);
      threatService.stopRealTimeUpdates();
    };
  }, []);

  return (
    <div className="space-y-8">
      {/* Key metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700">
          <div className="w-16 h-16 rounded-full bg-blue-500/20 flex items-center justify-center mr-5">
            <FaShieldAlt className="text-blue-500 text-2xl" />
          </div>
          <div>
            <div className="text-sm text-gray-400 uppercase tracking-wider">Total Attacks</div>
            <div className="text-3xl font-bold">{totalAttacks.toLocaleString()}</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700">
          <div className="w-16 h-16 rounded-full bg-green-500/20 flex items-center justify-center mr-5">
            <FaUserShield className="text-green-500 text-2xl" />
          </div>
          <div>
            <div className="text-sm text-gray-400 uppercase tracking-wider">Blocked Attacks</div>
            <div className="text-3xl font-bold">{blockedAttacks.toLocaleString()}</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700">
          <div className="w-16 h-16 rounded-full bg-red-500/20 flex items-center justify-center mr-5">
            <FaExclamationTriangle className="text-red-500 text-2xl" />
          </div>
          <div>
            <div className="text-sm text-gray-400 uppercase tracking-wider">Critical Vulnerabilities</div>
            <div className="text-3xl font-bold">{threatStats.criticalVulnerabilities}</div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700">
          <div className="w-16 h-16 rounded-full bg-yellow-500/20 flex items-center justify-center mr-5">
            <FaChartBar className="text-yellow-500 text-2xl" />
          </div>
          <div>
            <div className="text-sm text-gray-400 uppercase tracking-wider">Security Score</div>
            <div className="text-3xl font-bold">{securityScore}%</div>
          </div>
        </div>
      </div>

      {/* Main charts - larger and more prominent */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Live Threat Feed */}
        <div className="bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <span className="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center mr-3">
              <FaRss className="text-blue-500" />
            </span>
            Live Threat Feed
          </h3>
          <div className="h-[350px]">
            <LiveThreatFeed />
          </div>
        </div>

        {/* Attack Type Distribution */}
        <div className="bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <span className="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center mr-3">
              <FaExclamationTriangle className="text-purple-500" />
            </span>
            Attack Type Distribution
          </h3>
          <div className="h-[550px] w-full flex items-center justify-center">
            <div className="w-[500px] h-[500px]">
              <AttackTypeDistribution data={threatStats.attacksByType} />
            </div>
          </div>
          <div className="mt-6 grid grid-cols-2 md:grid-cols-3 gap-3">
            {Object.entries(threatStats.attacksByType || {}).map(([type, count]) => (
              <div key={type} className="flex items-center bg-gray-700 p-2 rounded-lg">
                <div
                  className="w-4 h-4 rounded-full mr-2"
                  style={{ backgroundColor: attackTypes[type]?.color || '#cbd5e1' }}
                ></div>
                <span className="text-sm font-medium">{type}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Secondary visualizations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Regional Threat Distribution */}
        <div className="bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <span className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-3">
              <FaGlobe className="text-green-500" />
            </span>
            Regional Threat Distribution
          </h3>
          <RegionalThreatDistribution data={threatStats.attacksByRegion} />
        </div>

        {/* Country Risk Matrix */}
        <div className="bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <span className="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center mr-3">
              <FaExclamationTriangle className="text-red-500" />
            </span>
            Country Risk Matrix
          </h3>
          <CountryRiskMatrix countries={countries} />
        </div>
      </div>

      {/* Live Threat Feed */}
      <div className="bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700">
        <h3 className="text-xl font-bold mb-4 flex items-center">
          <span className="w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center mr-3">
            <FaServer className="text-yellow-500" />
          </span>
          Live Threat Feed
        </h3>
        <ThreatFeedList threats={threatStats.recentThreats} />
      </div>
    </div>
  );
};

export default CyberThreatDashboard;
