import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaShieldAlt, FaGlobe, FaServer, FaLock, FaWifi, FaSpinner, FaInfoCircle, FaExternalLinkAlt, FaVirus, FaUserSecret, FaDatabase, FaNetworkWired } from 'react-icons/fa';
import createAbuseIPDBService from '../../services/api/abuseIPDBService';
import createOTXService from '../../services/api/otxService';

/**
 * LiveThreatFeed Component
 *
 * Displays a real-time feed of cyber threats with details about each attack.
 * This component connects to real threat intelligence APIs (AbuseIPDB and AlienVault OTX)
 * to display actual threat data.
 */
const LiveThreatFeed = () => {
  const [threats, setThreats] = useState([]);
  const [isLive, setIsLive] = useState(true);
  const [dataSource, setDataSource] = useState('live'); // Always using live data
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // API keys (in a real app, these would be stored securely in environment variables)
  const ABUSEIPDB_API_KEY = import.meta.env.VITE_ABUSEIPDB_API_KEY || '********************************************************************************';
  const OTX_API_KEY = import.meta.env.VITE_OTX_API_KEY || '437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0';

  // Get attack type icon
  const getAttackIcon = (type) => {
    switch (type.toLowerCase()) {
      case 'ransomware':
        return <FaLock className="text-red-500" />;
      case 'ddos':
        return <FaServer className="text-orange-500" />;
      case 'phishing':
        return <FaWifi className="text-yellow-500" />;
      case 'malware':
        return <FaVirus className="text-purple-500" />;
      case 'data breach':
        return <FaDatabase className="text-blue-500" />;
      case 'apt':
      case 'advanced persistent threat':
        return <FaUserSecret className="text-pink-500" />;
      case 'network attack':
        return <FaNetworkWired className="text-green-500" />;
      case 'unknown':
        return <FaExclamationTriangle className="text-gray-500" />;
      default:
        return <FaShieldAlt className="text-blue-500" />;
    }
  };

  // Get severity class
  const getSeverityClass = (severity) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'bg-red-900 text-red-200';
      case 'high':
        return 'bg-orange-900 text-orange-200';
      case 'medium':
        return 'bg-yellow-900 text-yellow-200';
      case 'low':
        return 'bg-green-900 text-green-200';
      default:
        return 'bg-blue-900 text-blue-200';
    }
  };

  // Format timestamp
  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  };

  // Initialize and fetch real threat data
  useEffect(() => {
    // Create API service instances
    const abuseIPDBService = createAbuseIPDBService(ABUSEIPDB_API_KEY);
    const otxService = createOTXService(OTX_API_KEY);

    // Function to fetch and combine threat data
    const fetchThreatData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Use Promise.allSettled to handle partial failures
        const [abuseIPDBResult, otxResult] = await Promise.allSettled([
          abuseIPDBService.getBlacklist(80, 15).catch(err => {
            console.error('AbuseIPDB API error:', err);
            return [];
          }),
          otxService.getPulses(5).catch(err => {
            console.error('OTX API error:', err);
            return [];
          })
        ]);

        // Extract results or use empty arrays for failures
        const blacklistedIPs = abuseIPDBResult.status === 'fulfilled' ? abuseIPDBResult.value : [];
        const pulses = otxResult.status === 'fulfilled' ? otxResult.value : [];

        // Check if we have any data
        if (blacklistedIPs.length === 0 && pulses.length === 0) {
          // If both APIs failed, show a more helpful error
          if (abuseIPDBResult.status === 'rejected' && otxResult.status === 'rejected') {
            throw new Error('Unable to connect to threat intelligence APIs. Using sample data instead.');
          }
        }

        // Convert blacklisted IPs to threat format
        const ipThreats = blacklistedIPs.map(ip => ({
          id: `ip-${ip.ipAddress}`,
          type: 'Malicious IP',
          severity: getSeverityFromScore(ip.abuseConfidenceScore),
          source: ip.countryCode || 'Unknown',
          target: 'Multiple',
          industry: 'Various',
          timestamp: new Date().toISOString(),
          color: getSeverityColor(getSeverityFromScore(ip.abuseConfidenceScore)),
          details: {
            ipAddress: ip.ipAddress,
            confidenceScore: ip.abuseConfidenceScore,
            totalReports: ip.totalReports,
            countryCode: ip.countryCode
          }
        }));

        // Convert OTX pulses to threat format
        const pulseThreats = pulses.map(pulse => {
          // Determine threat type from tags
          let type = 'Unknown';
          if (pulse.tags && Array.isArray(pulse.tags)) {
            if (pulse.tags.some(tag => tag.toLowerCase().includes('ransomware'))) {
              type = 'Ransomware';
            } else if (pulse.tags.some(tag => tag.toLowerCase().includes('malware'))) {
              type = 'Malware';
            } else if (pulse.tags.some(tag => tag.toLowerCase().includes('phishing'))) {
              type = 'Phishing';
            } else if (pulse.tags.some(tag => tag.toLowerCase().includes('ddos'))) {
              type = 'DDoS';
            }
          }

          // Determine severity
          const severity = pulse.adversary ? 'High' : 'Medium';

          return {
            id: `pulse-${pulse.id || Math.random().toString(36).substring(2, 15)}`,
            type,
            severity,
            source: pulse.author_name || 'Threat Actor',
            target: pulse.targeted_countries?.join(', ') || 'Multiple',
            industry: pulse.industries?.join(', ') || 'Various',
            timestamp: pulse.created || new Date().toISOString(),
            color: getSeverityColor(severity),
            details: {
              name: pulse.name || 'Unknown Threat',
              description: pulse.description || 'No description available',
              tags: pulse.tags || [],
              references: pulse.references || []
            }
          };
        });

        // Only use real data
        if (ipThreats.length === 0 && pulseThreats.length === 0) {
          // If no data, show error
          throw new Error('No threat data available. Please try again later.');
        } else {
          // Combine and sort threats
          const combinedThreats = [...ipThreats, ...pulseThreats]
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

          setThreats(combinedThreats);
          setDataSource('live');
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching threat data:', err);

        // Show error message
        setError(err.message || 'Failed to fetch live threat data. Please try again.');
        setLoading(false);

        // Schedule a retry after 10 seconds
        setTimeout(() => {
          if (isLive) {
            setError(null);
            setLoading(true);
            fetchThreatData();
          }
        }, 10000);
      }
    };

    // Initial fetch
    fetchThreatData();

    // Set up interval for live updates if enabled
    let intervalId;
    if (isLive) {
      intervalId = setInterval(fetchThreatData, 30000); // Update every 30 seconds
    }

    // Cleanup
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isLive, ABUSEIPDB_API_KEY, OTX_API_KEY]);

  // Helper function to determine severity from confidence score
  const getSeverityFromScore = (score) => {
    if (score >= 90) return 'Critical';
    if (score >= 80) return 'High';
    if (score >= 60) return 'Medium';
    return 'Low';
  };

  // Helper function to get color based on severity
  const getSeverityColor = (severity) => {
    switch (severity.toLowerCase()) {
      case 'critical': return '#ef4444'; // red
      case 'high': return '#f97316'; // orange
      case 'medium': return '#eab308'; // yellow
      case 'low': return '#22c55e'; // green
      default: return '#3b82f6'; // blue
    }
  };

  // State for selected threat and info panel
  const [selectedThreat, setSelectedThreat] = useState(null);
  const [showInfoPanel, setShowInfoPanel] = useState(false);

  // Get MITRE ATT&CK information for a threat type
  const getMitreInfo = (threatType) => {
    const mitreInfo = {
      'ransomware': {
        tactics: ['Impact', 'Execution'],
        techniques: ['T1486: Data Encrypted for Impact', 'T1490: Inhibit System Recovery'],
        description: 'Ransomware encrypts files and demands payment for decryption keys. It often enters systems through phishing emails or exploiting vulnerabilities.',
        mitigation: 'Maintain offline backups, implement application allowlisting, keep systems patched, and use anti-ransomware solutions.',
        url: 'https://attack.mitre.org/techniques/T1486/'
      },
      'phishing': {
        tactics: ['Initial Access', 'Credential Access'],
        techniques: ['T1566: Phishing', 'T1534: Internal Spearphishing'],
        description: 'Phishing attacks use deceptive emails, messages, or websites to steal credentials or deliver malware by tricking users into taking harmful actions.',
        mitigation: 'Implement email filtering, user awareness training, multi-factor authentication, and disable macros in documents from the internet.',
        url: 'https://attack.mitre.org/techniques/T1566/'
      },
      'ddos': {
        tactics: ['Impact'],
        techniques: ['T1498: Network Denial of Service', 'T1499: Endpoint Denial of Service'],
        description: 'DDoS attacks overwhelm services with excessive traffic or requests, making them unavailable to legitimate users.',
        mitigation: 'Use DDoS protection services, implement rate limiting, and configure network infrastructure to handle traffic surges.',
        url: 'https://attack.mitre.org/techniques/T1498/'
      },
      'malware': {
        tactics: ['Execution', 'Defense Evasion', 'Discovery'],
        techniques: ['T1204: User Execution', 'T1027: Obfuscated Files or Information'],
        description: 'Malware is malicious software designed to damage systems, steal data, or gain unauthorized access to networks.',
        mitigation: 'Use anti-malware solutions, keep systems updated, implement application allowlisting, and practice the principle of least privilege.',
        url: 'https://attack.mitre.org/tactics/TA0002/'
      },
      'data breach': {
        tactics: ['Exfiltration', 'Collection'],
        techniques: ['T1048: Exfiltration Over Alternative Protocol', 'T1114: Email Collection'],
        description: 'Data breaches involve unauthorized access to sensitive data, often followed by data theft or exposure.',
        mitigation: 'Encrypt sensitive data, implement data loss prevention tools, monitor for unusual data access patterns, and use network segmentation.',
        url: 'https://attack.mitre.org/tactics/TA0010/'
      },
      'apt': {
        tactics: ['Persistence', 'Privilege Escalation', 'Defense Evasion'],
        techniques: ['T1078: Valid Accounts', 'T1053: Scheduled Task/Job', 'T1055: Process Injection'],
        description: 'Advanced Persistent Threats are prolonged, targeted attacks by sophisticated threat actors, often nation-states, aiming to maintain long-term access to networks.',
        mitigation: 'Implement defense-in-depth strategies, conduct threat hunting, monitor for unusual behavior, and use advanced endpoint protection.',
        url: 'https://attack.mitre.org/groups/'
      },
      'network attack': {
        tactics: ['Lateral Movement', 'Discovery'],
        techniques: ['T1046: Network Service Scanning', 'T1021: Remote Services'],
        description: 'Network attacks target network infrastructure and services to gain access, disrupt operations, or move laterally within environments.',
        mitigation: 'Implement network segmentation, use intrusion detection systems, monitor network traffic, and secure remote access services.',
        url: 'https://attack.mitre.org/tactics/TA0008/'
      }
    };

    return mitreInfo[threatType.toLowerCase()] || {
      tactics: ['Multiple'],
      techniques: ['Various techniques may be employed'],
      description: 'This threat type encompasses various attack vectors and methods that may target different aspects of systems and networks.',
      mitigation: 'Implement defense-in-depth strategies, keep systems updated, and follow security best practices appropriate to your environment.',
      url: 'https://attack.mitre.org/'
    };
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with controls */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-green-500 animate-pulse' : 'bg-gray-500'} mr-2`}></div>
          <span className="text-sm text-gray-400">
            {isLive ? 'Live Feed' : 'Paused'} • Real-time Threat Data
          </span>
          <button
            className="ml-2 text-gray-400 hover:text-gray-300"
            onClick={() => setShowInfoPanel(!showInfoPanel)}
            title="Information about the threat feed"
          >
            <FaInfoCircle size={14} />
          </button>
        </div>
        <div className="flex space-x-2">
          <button
            className={`px-2 py-1 text-xs rounded ${isLive ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}`}
            onClick={() => setIsLive(!isLive)}
            disabled={loading}
          >
            {isLive ? 'Pause' : 'Resume'}
          </button>
          <button
            className="px-2 py-1 text-xs rounded bg-blue-600 hover:bg-blue-700"
            onClick={() => {
              if (!isLive) {
                setIsLive(true);
              }
            }}
            disabled={loading || isLive}
          >
            Refresh Now
          </button>
        </div>
      </div>

      {/* Information Panel */}
      {showInfoPanel && (
        <div className="mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm">
          <div className="flex justify-between items-start">
            <h4 className="font-semibold mb-2 flex items-center">
              <FaShieldAlt className="mr-1 text-blue-400" /> Live Threat Intelligence Feed
            </h4>
            <button
              className="text-gray-400 hover:text-gray-300"
              onClick={() => setShowInfoPanel(false)}
            >
              ×
            </button>
          </div>
          <p className="text-gray-300 mb-2">
            This feed displays real-time cyber threats detected across the globe. Data is collected from
            multiple threat intelligence sources including AbuseIPDB and AlienVault OTX. Click on any
            threat to see detailed information including MITRE ATT&CK framework references and mitigation strategies.
          </p>
          <div className="flex items-center text-xs">
            <a
              href="https://attack.mitre.org/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 flex items-center"
            >
              Learn more about MITRE ATT&CK <FaExternalLinkAlt className="ml-1" size={10} />
            </a>
          </div>
        </div>
      )}

      {/* Loading state */}
      {loading && (
        <div className="flex items-center justify-center py-4">
          <FaSpinner className="animate-spin text-blue-500 mr-2" />
          <span className="text-gray-300">Fetching live threat data...</span>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="bg-red-900 bg-opacity-20 border border-red-800 rounded-lg p-3 mb-4 text-red-400">
          <FaExclamationTriangle className="inline-block mr-2" />
          {error}
        </div>
      )}

      {/* Main content area - split into two columns when a threat is selected */}
      <div className="flex-1 flex flex-col md:flex-row gap-4 overflow-hidden">
        {/* Threat feed - takes full width when no threat selected, otherwise 60% */}
        <div className={`overflow-y-auto space-y-2 pr-1 ${selectedThreat ? 'md:w-3/5' : 'w-full'}`}>
          {!loading && threats.length === 0 ? (
            <div className="flex items-center justify-center h-full text-gray-500">
              No threat data available
            </div>
          ) : (
            threats.map((threat) => (
              <div
                key={threat.id}
                className={`bg-gray-700 rounded-lg p-3 border-l-4 animate-fadeIn cursor-pointer transition-colors ${selectedThreat === threat ? 'bg-gray-600' : 'hover:bg-gray-650'}`}
                style={{ borderLeftColor: threat.color }}
                onClick={() => setSelectedThreat(selectedThreat === threat ? null : threat)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex items-start">
                    <div className="mt-0.5 mr-2">
                      {getAttackIcon(threat.type)}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{threat.type}</div>
                      <div className="text-xs text-gray-400 flex items-center">
                        <FaGlobe className="mr-1" size={10} />
                        <span className="mr-1">{threat.source}</span> → <span className="ml-1">{threat.target}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <span className={`text-xs px-1.5 py-0.5 rounded-full ${getSeverityClass(threat.severity)}`}>
                      {threat.severity}
                    </span>
                    <span className="text-xs text-gray-400">
                      {formatTime(threat.timestamp)}
                    </span>
                  </div>
                </div>

                {/* Compact details based on threat type */}
                <div className="mt-1 text-xs grid grid-cols-1 gap-0.5">
                  {threat.id.startsWith('ip-') && (
                    <div className="flex flex-wrap">
                      <span className="text-gray-400 mr-1">IP:</span> {threat.details.ipAddress} •
                      <span className="text-gray-400 mx-1">Conf:</span> {threat.details.confidenceScore}% •
                      <span className="text-gray-400 mx-1">Reports:</span> {threat.details.totalReports || 0}
                    </div>
                  )}

                  {threat.id.startsWith('pulse-') && (
                    <>
                      <div><span className="text-gray-400 mr-1">Name:</span> {threat.details.name}</div>
                      {threat.details.tags && threat.details.tags.length > 0 && (
                        <div className="flex flex-wrap gap-0.5">
                          {threat.details.tags.slice(0, 3).map((tag, index) => (
                            <span key={index} className="bg-gray-800 px-1 py-0.5 rounded text-xs">{tag}</span>
                          ))}
                          {threat.details.tags.length > 3 && (
                            <span className="bg-gray-800 px-1 py-0.5 rounded text-xs">+{threat.details.tags.length - 3}</span>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Threat details panel - only visible when a threat is selected */}
        {selectedThreat && (
          <div className="md:w-2/5 bg-gray-800 rounded-lg p-4 overflow-y-auto">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold flex items-center">
                {getAttackIcon(selectedThreat.type)}
                <span className="ml-2">{selectedThreat.type} Details</span>
              </h3>
              <button
                className="text-gray-400 hover:text-gray-300"
                onClick={() => setSelectedThreat(null)}
              >
                ×
              </button>
            </div>

            {/* Threat details */}
            <div className="mb-4">
              <div className="grid grid-cols-2 gap-2 mb-4">
                <div>
                  <div className="text-xs text-gray-400">Source</div>
                  <div className="font-medium">{selectedThreat.source}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-400">Target</div>
                  <div className="font-medium">{selectedThreat.target}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-400">Severity</div>
                  <div className="font-medium">{selectedThreat.severity}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-400">Timestamp</div>
                  <div className="font-medium">{new Date(selectedThreat.timestamp).toLocaleString()}</div>
                </div>
              </div>

              {/* Specific details based on threat type */}
              {selectedThreat.id.startsWith('ip-') && (
                <div className="bg-gray-700 p-3 rounded-lg mb-4">
                  <h4 className="font-medium mb-2">IP Details</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <div className="text-xs text-gray-400">IP Address</div>
                      <div>{selectedThreat.details.ipAddress}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">Country</div>
                      <div>{selectedThreat.details.countryCode || 'Unknown'}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">Confidence Score</div>
                      <div>{selectedThreat.details.confidenceScore}%</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">Total Reports</div>
                      <div>{selectedThreat.details.totalReports || 0}</div>
                    </div>
                  </div>
                </div>
              )}

              {selectedThreat.id.startsWith('pulse-') && (
                <div className="bg-gray-700 p-3 rounded-lg mb-4">
                  <h4 className="font-medium mb-2">Threat Intelligence</h4>
                  <div className="mb-2">
                    <div className="text-xs text-gray-400">Name</div>
                    <div className="text-sm">{selectedThreat.details.name}</div>
                  </div>
                  {selectedThreat.details.description && (
                    <div className="mb-2">
                      <div className="text-xs text-gray-400">Description</div>
                      <div className="text-sm">{selectedThreat.details.description}</div>
                    </div>
                  )}
                  {selectedThreat.details.tags && selectedThreat.details.tags.length > 0 && (
                    <div>
                      <div className="text-xs text-gray-400 mb-1">Tags</div>
                      <div className="flex flex-wrap gap-1">
                        {selectedThreat.details.tags.map((tag, index) => (
                          <span key={index} className="bg-gray-600 px-2 py-0.5 rounded text-xs">{tag}</span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* MITRE ATT&CK Framework Information */}
            <div className="mb-4">
              <h4 className="font-medium mb-2 flex items-center">
                <FaShieldAlt className="mr-1 text-blue-400" /> MITRE ATT&CK Framework
              </h4>

              {/* Get MITRE info based on threat type */}
              {(() => {
                const mitreInfo = getMitreInfo(selectedThreat.type);
                return (
                  <div className="bg-gray-700 p-3 rounded-lg text-sm">
                    <div className="mb-2">
                      <div className="text-xs text-gray-400">Tactics</div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {mitreInfo.tactics.map((tactic, index) => (
                          <span key={index} className="bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full text-xs">
                            {tactic}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="mb-2">
                      <div className="text-xs text-gray-400">Techniques</div>
                      <div className="flex flex-col gap-1 mt-1">
                        {mitreInfo.techniques.map((technique, index) => (
                          <span key={index} className="text-xs">{technique}</span>
                        ))}
                      </div>
                    </div>

                    <div className="mb-2">
                      <div className="text-xs text-gray-400">Description</div>
                      <p className="text-xs mt-1">{mitreInfo.description}</p>
                    </div>

                    <div className="mb-2">
                      <div className="text-xs text-gray-400">Mitigation Strategies</div>
                      <p className="text-xs mt-1">{mitreInfo.mitigation}</p>
                    </div>

                    <div className="text-right">
                      <a
                        href={mitreInfo.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 text-xs flex items-center justify-end"
                      >
                        Learn more <FaExternalLinkAlt className="ml-1" size={10} />
                      </a>
                    </div>
                  </div>
                );
              })()}
            </div>

            {/* Steps to take */}
            <div>
              <h4 className="font-medium mb-2">Recommended Actions</h4>
              <ul className="list-disc list-inside text-sm space-y-1 text-gray-300">
                <li>Monitor for similar {selectedThreat.type.toLowerCase()} activity in your environment</li>
                <li>Review logs for connections to {selectedThreat.source}</li>
                <li>Update security controls based on the MITRE ATT&CK techniques</li>
                <li>Share this intelligence with your security team</li>
              </ul>
            </div>
          </div>
        )}
      </div>

      {/* Data source information */}
      <div className="mt-4 text-xs text-gray-400">
        <strong>Data Sources:</strong> AbuseIPDB API and AlienVault OTX API •
        <strong>Update Frequency:</strong> {isLive ? 'Every 30 seconds' : 'Paused'} •
        <strong>Last Updated:</strong> {new Date().toLocaleTimeString()}
      </div>
    </div>
  );
};

export default LiveThreatFeed;
