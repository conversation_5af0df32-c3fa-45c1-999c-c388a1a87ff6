import React, { useState, lazy, Suspense } from 'react';
import { FaGlobe, FaChartBar, FaExclamationTriangle, FaInfoCircle, FaShieldAlt, FaSearch, FaNetworkWired } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

// Lazy load components to avoid potential circular dependencies
const LiveThreatFeed = lazy(() => import('./LiveThreatFeed'));
const CountryRiskMatrix = lazy(() => import('./CountryRiskMatrix'));
const RegionalThreatDistribution = lazy(() => import('./RegionalThreatDistribution'));
const VulnerabilityInsights = lazy(() => import('./VulnerabilityInsights'));
const ThreatHuntingWorkbench = lazy(() => import('./ThreatHuntingWorkbench'));
const ThreatCorrelationGraph = lazy(() => import('./ThreatCorrelationGraph'));

// Loading spinner
const LoadingSpinner = () => (
  <div className="flex items-center justify-center h-full">
    <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
  </div>
);

/**
 * ThreatIntelligenceDashboard Component
 *
 * A comprehensive dashboard that combines multiple threat intelligence visualizations:
 * - Live Threat Feed
 * - Country Risk Matrix
 * - Regional Threat Distribution
 *
 * This component provides a full-featured threat intelligence experience with
 * educational content and actionable insights.
 */
const ThreatIntelligenceDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('live-feed');

  // Define tabs
  const tabs = [
    { id: 'live-feed', label: 'Live Threat Feed', icon: <FaExclamationTriangle /> },
    { id: 'country-risk', label: 'Country Risk Matrix', icon: <FaGlobe /> },
    { id: 'regional-distribution', label: 'Regional Threat Distribution', icon: <FaChartBar /> },
    { id: 'vulnerability-insights', label: 'Vulnerability Insights', icon: <FaShieldAlt /> },
    { id: 'threat-correlation', label: 'Threat Correlation', icon: <FaNetworkWired /> },
    { id: 'threat-hunting', label: 'Threat Hunting', icon: <FaSearch /> }
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Tabs */}
      <div className="flex border-b border-gray-700 mb-4">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`flex items-center px-4 py-2 text-sm font-medium ${
              activeTab === tab.id
                ? 'border-b-2 border-blue-500 text-blue-400'
                : 'text-gray-400 hover:text-gray-300'
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.label}
          </button>
        ))}

        <div className="ml-auto flex items-center px-4">
          <FaInfoCircle className="text-gray-500 mr-2" />
          <span className="text-xs text-gray-500">
            Data refreshes automatically
          </span>
        </div>
      </div>

      {/* Content based on active tab */}
      <div className="flex-1 overflow-hidden">
        <Suspense fallback={<LoadingSpinner />}>
          {activeTab === 'live-feed' && (
            <div className="h-full">
              <LiveThreatFeed />
            </div>
          )}

          {activeTab === 'country-risk' && (
            <div className="h-full">
              <CountryRiskMatrix />
            </div>
          )}

          {activeTab === 'regional-distribution' && (
            <div className="h-full">
              <RegionalThreatDistribution />
            </div>
          )}

          {activeTab === 'vulnerability-insights' && (
            <div className="h-full">
              <VulnerabilityInsights />
            </div>
          )}

          {activeTab === 'threat-correlation' && (
            <div className="h-full">
              <ThreatCorrelationGraph />
            </div>
          )}

          {activeTab === 'threat-hunting' && (
            <div className="h-full">
              <ThreatHuntingWorkbench />
            </div>
          )}
        </Suspense>
      </div>
    </div>
  );
};

export default ThreatIntelligenceDashboard;
