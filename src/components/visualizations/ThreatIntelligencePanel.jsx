import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaGlobe, FaServer, FaShieldAlt, FaSearch } from 'react-icons/fa';
import apiManager from '../../services/api/apiManager';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

const ThreatIntelligencePanel = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('pulses');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState({
    pulses: [],
    blacklist: [],
    searchResults: []
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIP, setSelectedIP] = useState(null);
  const [ipDetails, setIPDetails] = useState(null);

  // Initialize API services
  useEffect(() => {
    apiManager.initialize();
  }, []);

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load OTX pulses if service is available
        if (apiManager.isServiceAvailable('otx')) {
          const otxService = apiManager.getService('otx');
          const pulses = await otxService.getPulses(10);
          setData(prev => ({ ...prev, pulses }));
        }

        // Load AbuseIPDB blacklist if service is available
        if (apiManager.isServiceAvailable('abuseIPDB')) {
          const abuseIPDBService = apiManager.getService('abuseIPDB');
          const blacklist = await abuseIPDBService.getBlacklist(90, 20);
          setData(prev => ({ ...prev, blacklist }));
        }
      } catch (err) {
        console.error('Error loading threat intelligence data:', err);
        setError('Failed to load threat intelligence data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Handle IP selection for details
  const handleIPSelect = async (ip) => {
    try {
      setSelectedIP(ip);
      setLoading(true);
      setError(null);

      // Get IP details from AbuseIPDB if available
      if (apiManager.isServiceAvailable('abuseIPDB')) {
        const abuseIPDBService = apiManager.getService('abuseIPDB');
        const details = await abuseIPDBService.checkIP(ip);
        setIPDetails(details);
      }
    } catch (err) {
      console.error(`Error fetching details for IP ${ip}:`, err);
      setError(`Failed to load details for IP ${ip}.`);
      setIPDetails(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    try {
      setLoading(true);
      setError(null);
      
      // Search in OTX if available
      if (apiManager.isServiceAvailable('otx')) {
        const otxService = apiManager.getService('otx');
        const results = await otxService.searchIndicators(searchQuery);
        setData(prev => ({ ...prev, searchResults: results }));
        setActiveTab('search');
      } else {
        setError('OTX service is not available for searching.');
      }
    } catch (err) {
      console.error(`Error searching for "${searchQuery}":`, err);
      setError(`Failed to search for "${searchQuery}".`);
    } finally {
      setLoading(false);
    }
  };

  // Render tabs
  const renderTabs = () => {
    const tabs = [
      { id: 'pulses', label: 'OTX Pulses', icon: FaGlobe, available: apiManager.isServiceAvailable('otx') },
      { id: 'blacklist', label: 'IP Blacklist', icon: FaServer, available: apiManager.isServiceAvailable('abuseIPDB') },
      { id: 'search', label: 'Search Results', icon: FaSearch, available: data.searchResults.length > 0 }
    ];

    return (
      <div className="flex border-b border-gray-700 mb-4">
        {tabs.filter(tab => tab.available).map(tab => (
          <button
            key={tab.id}
            className={`flex items-center px-4 py-2 ${
              activeTab === tab.id
                ? 'border-b-2 border-blue-500 text-blue-500'
                : 'text-gray-400 hover:text-gray-200'
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            <tab.icon className="mr-2" />
            {tab.label}
          </button>
        ))}
      </div>
    );
  };

  // Render content based on active tab
  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="bg-red-900 bg-opacity-20 border border-red-800 rounded-lg p-4 text-red-400">
          <FaExclamationTriangle className="inline-block mr-2" />
          {error}
        </div>
      );
    }

    switch (activeTab) {
      case 'pulses':
        return renderPulses();
      case 'blacklist':
        return renderBlacklist();
      case 'search':
        return renderSearchResults();
      default:
        return null;
    }
  };

  // Render OTX pulses
  const renderPulses = () => {
    if (!data.pulses.length) {
      return (
        <div className="text-gray-400 text-center py-8">
          No threat intelligence pulses available.
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {data.pulses.map((pulse, index) => (
          <div key={index} className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold">{pulse.name}</h3>
            <div className="text-sm text-gray-400 mb-2">
              {new Date(pulse.created).toLocaleDateString()} | Author: {pulse.author_name}
            </div>
            <p className="text-gray-300 mb-2">{pulse.description?.substring(0, 150)}...</p>
            <div className="flex flex-wrap gap-2 mt-2">
              {pulse.tags?.slice(0, 5).map((tag, idx) => (
                <span key={idx} className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                  {tag}
                </span>
              ))}
              {pulse.tags?.length > 5 && (
                <span className="bg-gray-600 text-gray-300 text-xs px-2 py-1 rounded">
                  +{pulse.tags.length - 5} more
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render AbuseIPDB blacklist
  const renderBlacklist = () => {
    if (!data.blacklist.length) {
      return (
        <div className="text-gray-400 text-center py-8">
          No blacklisted IPs available.
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4">Blacklisted IPs</h3>
          <div className="h-96 overflow-y-auto">
            <table className="w-full">
              <thead className="bg-gray-800">
                <tr>
                  <th className="px-4 py-2 text-left">IP Address</th>
                  <th className="px-4 py-2 text-left">Confidence</th>
                  <th className="px-4 py-2 text-left">Reports</th>
                </tr>
              </thead>
              <tbody>
                {data.blacklist.map((ip, index) => (
                  <tr 
                    key={index} 
                    className={`border-t border-gray-600 hover:bg-gray-600 cursor-pointer ${
                      selectedIP === ip.ipAddress ? 'bg-gray-600' : ''
                    }`}
                    onClick={() => handleIPSelect(ip.ipAddress)}
                  >
                    <td className="px-4 py-2">{ip.ipAddress}</td>
                    <td className="px-4 py-2">
                      <span 
                        className={`px-2 py-1 rounded text-xs ${
                          ip.abuseConfidenceScore > 80 ? 'bg-red-900 text-red-200' :
                          ip.abuseConfidenceScore > 50 ? 'bg-yellow-900 text-yellow-200' :
                          'bg-green-900 text-green-200'
                        }`}
                      >
                        {ip.abuseConfidenceScore}%
                      </span>
                    </td>
                    <td className="px-4 py-2">{ip.totalReports || 'N/A'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4">IP Details</h3>
          {selectedIP ? (
            ipDetails ? (
              <div>
                <div className="grid grid-cols-2 gap-2 mb-4">
                  <div className="bg-gray-800 p-3 rounded">
                    <div className="text-sm text-gray-400">IP Address</div>
                    <div>{ipDetails.ipAddress}</div>
                  </div>
                  <div className="bg-gray-800 p-3 rounded">
                    <div className="text-sm text-gray-400">Confidence Score</div>
                    <div>{ipDetails.abuseConfidenceScore}%</div>
                  </div>
                  <div className="bg-gray-800 p-3 rounded">
                    <div className="text-sm text-gray-400">Country</div>
                    <div>{ipDetails.countryName || 'Unknown'}</div>
                  </div>
                  <div className="bg-gray-800 p-3 rounded">
                    <div className="text-sm text-gray-400">ISP</div>
                    <div>{ipDetails.isp || 'Unknown'}</div>
                  </div>
                  <div className="bg-gray-800 p-3 rounded">
                    <div className="text-sm text-gray-400">Domain</div>
                    <div>{ipDetails.domain || 'Unknown'}</div>
                  </div>
                  <div className="bg-gray-800 p-3 rounded">
                    <div className="text-sm text-gray-400">Usage Type</div>
                    <div>{ipDetails.usageType || 'Unknown'}</div>
                  </div>
                </div>
                
                <div className="bg-gray-800 p-3 rounded mb-4">
                  <div className="text-sm text-gray-400 mb-2">Last Reported</div>
                  <div>{ipDetails.lastReportedAt ? new Date(ipDetails.lastReportedAt).toLocaleString() : 'Never'}</div>
                </div>
                
                {ipDetails.reports && ipDetails.reports.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2">Recent Reports</h4>
                    <div className="h-40 overflow-y-auto">
                      {ipDetails.reports.map((report, idx) => (
                        <div key={idx} className="border-t border-gray-600 py-2">
                          <div className="text-sm">
                            <span className="text-gray-400">Reported on: </span>
                            {new Date(report.reportedAt).toLocaleString()}
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-400">Comment: </span>
                            {report.comment || 'No comment'}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                {loading ? (
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
                ) : (
                  <div className="text-gray-400">No details available for this IP.</div>
                )}
              </div>
            )
          ) : (
            <div className="text-gray-400 text-center py-8">
              Select an IP from the list to view details.
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render search results
  const renderSearchResults = () => {
    if (!data.searchResults.length) {
      return (
        <div className="text-gray-400 text-center py-8">
          No search results found. Try a different query.
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Search Results for "{searchQuery}"</h3>
        {data.searchResults.map((result, index) => (
          <div key={index} className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-start">
              <div className="bg-gray-800 p-2 rounded mr-3">
                <FaShieldAlt className="text-blue-400" />
              </div>
              <div>
                <h4 className="font-semibold">{result.indicator}</h4>
                <div className="text-sm text-gray-400 mb-1">
                  Type: {result.type} | Pulse Count: {result.pulse_count}
                </div>
                {result.description && (
                  <p className="text-gray-300 text-sm mb-2">{result.description}</p>
                )}
                <div className="flex flex-wrap gap-2 mt-2">
                  {result.tags?.slice(0, 3).map((tag, idx) => (
                    <span key={idx} className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                      {tag}
                    </span>
                  ))}
                  {result.tags?.length > 3 && (
                    <span className="bg-gray-600 text-gray-300 text-xs px-2 py-1 rounded">
                      +{result.tags.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={`bg-gray-800 rounded-lg p-6 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Threat Intelligence</h2>
        <form onSubmit={handleSearch} className="flex">
          <input
            type="text"
            placeholder="Search indicators..."
            className="bg-gray-700 border border-gray-600 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 rounded-r px-4 py-2 flex items-center"
            disabled={loading}
          >
            <FaSearch className="mr-2" />
            Search
          </button>
        </form>
      </div>

      {renderTabs()}
      {renderContent()}
    </div>
  );
};

export default ThreatIntelligencePanel;
