import React, { useState } from 'react';
import { FaPlus, FaMinus, FaPlay, FaSave, FaTrash, FaExclamationTriangle } from 'react-icons/fa';

/**
 * QueryBuilder Component
 * 
 * Advanced query builder for threat hunting that allows users to create
 * complex queries with multiple conditions and operators.
 */
const QueryBuilder = ({ onRunQuery, onSaveQuery, savedQueries = [] }) => {
  const [queryName, setQueryName] = useState('');
  const [queryDescription, setQueryDescription] = useState('');
  const [conditions, setConditions] = useState([
    { id: 1, field: 'ip_address', operator: 'equals', value: '', logicalOperator: 'AND' }
  ]);
  const [dataSource, setDataSource] = useState('all');
  const [timeRange, setTimeRange] = useState('24h');
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Available fields for querying
  const availableFields = [
    { id: 'ip_address', label: 'IP Address', type: 'text' },
    { id: 'domain', label: 'Domain', type: 'text' },
    { id: 'file_hash', label: 'File Hash', type: 'text' },
    { id: 'user', label: 'Username', type: 'text' },
    { id: 'process_name', label: 'Process Name', type: 'text' },
    { id: 'event_type', label: 'Event Type', type: 'select', options: [
      'login', 'logout', 'file_access', 'network_connection', 'process_execution'
    ]},
    { id: 'severity', label: 'Severity', type: 'select', options: [
      'low', 'medium', 'high', 'critical'
    ]},
    { id: 'timestamp', label: 'Timestamp', type: 'datetime' }
  ];

  // Available operators
  const operators = [
    { id: 'equals', label: 'Equals (=)' },
    { id: 'not_equals', label: 'Not Equals (≠)' },
    { id: 'contains', label: 'Contains' },
    { id: 'starts_with', label: 'Starts With' },
    { id: 'ends_with', label: 'Ends With' },
    { id: 'greater_than', label: 'Greater Than (>)' },
    { id: 'less_than', label: 'Less Than (<)' },
    { id: 'in_list', label: 'In List' },
    { id: 'not_in_list', label: 'Not In List' }
  ];

  // Available data sources
  const dataSources = [
    { id: 'all', label: 'All Sources' },
    { id: 'logs', label: 'System Logs' },
    { id: 'network', label: 'Network Traffic' },
    { id: 'endpoints', label: 'Endpoint Events' },
    { id: 'alerts', label: 'Security Alerts' }
  ];

  // Available time ranges
  const timeRanges = [
    { id: '1h', label: 'Last Hour' },
    { id: '6h', label: 'Last 6 Hours' },
    { id: '24h', label: 'Last 24 Hours' },
    { id: '7d', label: 'Last 7 Days' },
    { id: '30d', label: 'Last 30 Days' },
    { id: 'custom', label: 'Custom Range' }
  ];

  // Add a new condition
  const addCondition = () => {
    const newId = conditions.length > 0 
      ? Math.max(...conditions.map(c => c.id)) + 1 
      : 1;
    
    setConditions([
      ...conditions,
      { id: newId, field: 'ip_address', operator: 'equals', value: '', logicalOperator: 'AND' }
    ]);
  };

  // Remove a condition
  const removeCondition = (id) => {
    if (conditions.length <= 1) {
      setError('Query must have at least one condition');
      return;
    }
    
    setConditions(conditions.filter(condition => condition.id !== id));
    setError(null);
  };

  // Update a condition
  const updateCondition = (id, field, value) => {
    setConditions(conditions.map(condition => {
      if (condition.id === id) {
        return { ...condition, [field]: value };
      }
      return condition;
    }));
  };

  // Build the query string
  const buildQueryString = () => {
    return conditions.map((condition, index) => {
      const field = availableFields.find(f => f.id === condition.field);
      const operator = operators.find(o => o.id === condition.operator);
      
      let queryPart = `${field.label} ${operator.label.split(' ')[0]} "${condition.value}"`;
      
      if (index < conditions.length - 1) {
        queryPart += ` ${condition.logicalOperator} `;
      }
      
      return queryPart;
    }).join('');
  };

  // Run the query
  const runQuery = () => {
    // Validate query
    const invalidConditions = conditions.filter(c => !c.value);
    if (invalidConditions.length > 0) {
      setError('All conditions must have values');
      return;
    }
    
    setError(null);
    
    // Build the query object
    const query = {
      name: queryName || 'Unnamed Query',
      description: queryDescription,
      conditions,
      dataSource,
      timeRange,
      queryString: buildQueryString()
    };
    
    // In a real implementation, this would call an API to execute the query
    // For now, we'll simulate results
    setResults({
      query,
      timestamp: new Date().toISOString(),
      resultCount: Math.floor(Math.random() * 100),
      sampleResults: generateSampleResults(query)
    });
    
    // Call the parent component's handler
    if (onRunQuery) {
      onRunQuery(query);
    }
  };

  // Save the query
  const saveQuery = () => {
    if (!queryName) {
      setError('Query must have a name to save');
      return;
    }
    
    // Validate query
    const invalidConditions = conditions.filter(c => !c.value);
    if (invalidConditions.length > 0) {
      setError('All conditions must have values');
      return;
    }
    
    setError(null);
    
    // Build the query object
    const query = {
      id: Date.now().toString(),
      name: queryName,
      description: queryDescription,
      conditions,
      dataSource,
      timeRange,
      queryString: buildQueryString(),
      created: new Date().toISOString()
    };
    
    // Call the parent component's handler
    if (onSaveQuery) {
      onSaveQuery(query);
    }
  };

  // Clear the query
  const clearQuery = () => {
    setQueryName('');
    setQueryDescription('');
    setConditions([
      { id: 1, field: 'ip_address', operator: 'equals', value: '', logicalOperator: 'AND' }
    ]);
    setDataSource('all');
    setTimeRange('24h');
    setError(null);
    setResults(null);
  };

  // Generate sample results for demonstration
  const generateSampleResults = (query) => {
    const results = [];
    const count = Math.floor(Math.random() * 5) + 1;
    
    for (let i = 0; i < count; i++) {
      results.push({
        id: `result-${i}`,
        timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(),
        source: {
          ip: '192.168.1.' + Math.floor(Math.random() * 255),
          hostname: `host-${Math.floor(Math.random() * 100)}.example.com`
        },
        destination: {
          ip: '10.0.0.' + Math.floor(Math.random() * 255),
          hostname: `server-${Math.floor(Math.random() * 20)}.example.com`
        },
        user: `user${Math.floor(Math.random() * 10)}`,
        event_type: ['login', 'file_access', 'network_connection'][Math.floor(Math.random() * 3)],
        severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)]
      });
    }
    
    return results;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Query Builder Form */}
      <div className="bg-gray-800 rounded-lg p-4 mb-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Query Name</label>
            <input
              type="text"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Enter query name..."
              value={queryName}
              onChange={(e) => setQueryName(e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Description</label>
            <input
              type="text"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Enter description..."
              value={queryDescription}
              onChange={(e) => setQueryDescription(e.target.value)}
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Data Source</label>
            <select
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
              value={dataSource}
              onChange={(e) => setDataSource(e.target.value)}
            >
              {dataSources.map(source => (
                <option key={source.id} value={source.id}>{source.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Time Range</label>
            <select
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
            >
              {timeRanges.map(range => (
                <option key={range.id} value={range.id}>{range.label}</option>
              ))}
            </select>
          </div>
        </div>
        
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium text-gray-400">Conditions</label>
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white rounded px-2 py-1 text-xs flex items-center"
              onClick={addCondition}
            >
              <FaPlus className="mr-1" size={10} /> Add Condition
            </button>
          </div>
          
          <div className="space-y-3">
            {conditions.map((condition, index) => (
              <div key={condition.id} className="flex flex-wrap items-center gap-2 p-2 bg-gray-700 rounded-lg">
                {index > 0 && (
                  <select
                    className="bg-gray-600 border border-gray-500 rounded px-2 py-1 text-sm"
                    value={condition.logicalOperator}
                    onChange={(e) => updateCondition(condition.id, 'logicalOperator', e.target.value)}
                  >
                    <option value="AND">AND</option>
                    <option value="OR">OR</option>
                  </select>
                )}
                
                <select
                  className="bg-gray-600 border border-gray-500 rounded px-2 py-1 text-sm flex-grow"
                  value={condition.field}
                  onChange={(e) => updateCondition(condition.id, 'field', e.target.value)}
                >
                  {availableFields.map(field => (
                    <option key={field.id} value={field.id}>{field.label}</option>
                  ))}
                </select>
                
                <select
                  className="bg-gray-600 border border-gray-500 rounded px-2 py-1 text-sm flex-grow"
                  value={condition.operator}
                  onChange={(e) => updateCondition(condition.id, 'operator', e.target.value)}
                >
                  {operators.map(op => (
                    <option key={op.id} value={op.id}>{op.label}</option>
                  ))}
                </select>
                
                <input
                  type="text"
                  className="bg-gray-600 border border-gray-500 rounded px-2 py-1 text-sm flex-grow"
                  placeholder="Value"
                  value={condition.value}
                  onChange={(e) => updateCondition(condition.id, 'value', e.target.value)}
                />
                
                <button
                  className="bg-red-600 hover:bg-red-700 text-white rounded p-1"
                  onClick={() => removeCondition(condition.id)}
                >
                  <FaMinus size={12} />
                </button>
              </div>
            ))}
          </div>
        </div>
        
        {error && (
          <div className="mb-4 p-2 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm">
            <FaExclamationTriangle className="inline-block mr-2" />
            {error}
          </div>
        )}
        
        <div className="flex justify-between">
          <div>
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2 text-sm flex items-center mr-2"
              onClick={runQuery}
            >
              <FaPlay className="mr-2" /> Run Query
            </button>
          </div>
          <div>
            <button
              className="bg-green-600 hover:bg-green-700 text-white rounded px-4 py-2 text-sm flex items-center mr-2"
              onClick={saveQuery}
            >
              <FaSave className="mr-2" /> Save Query
            </button>
            <button
              className="bg-gray-600 hover:bg-gray-700 text-white rounded px-4 py-2 text-sm flex items-center"
              onClick={clearQuery}
            >
              <FaTrash className="mr-2" /> Clear
            </button>
          </div>
        </div>
      </div>
      
      {/* Query Results */}
      {results && (
        <div className="bg-gray-800 rounded-lg p-4 flex-1 overflow-auto">
          <h4 className="text-lg font-semibold mb-2">Query Results</h4>
          <div className="mb-4 grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Query:</span> {results.query.queryString}
            </div>
            <div>
              <span className="text-gray-400">Time Range:</span> {timeRanges.find(r => r.id === results.query.timeRange)?.label}
            </div>
            <div>
              <span className="text-gray-400">Data Source:</span> {dataSources.find(s => s.id === results.query.dataSource)?.label}
            </div>
            <div>
              <span className="text-gray-400">Results Found:</span> {results.resultCount}
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full bg-gray-700 rounded-lg">
              <thead>
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Timestamp</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Source</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Destination</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">User</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Event Type</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Severity</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-600">
                {results.sampleResults.map(result => (
                  <tr key={result.id} className="hover:bg-gray-650">
                    <td className="px-4 py-2 text-sm">{new Date(result.timestamp).toLocaleString()}</td>
                    <td className="px-4 py-2 text-sm">
                      <div>{result.source.ip}</div>
                      <div className="text-xs text-gray-400">{result.source.hostname}</div>
                    </td>
                    <td className="px-4 py-2 text-sm">
                      <div>{result.destination.ip}</div>
                      <div className="text-xs text-gray-400">{result.destination.hostname}</div>
                    </td>
                    <td className="px-4 py-2 text-sm">{result.user}</td>
                    <td className="px-4 py-2 text-sm">{result.event_type}</td>
                    <td className="px-4 py-2 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        result.severity === 'high' ? 'bg-red-900/30 text-red-400' :
                        result.severity === 'medium' ? 'bg-yellow-900/30 text-yellow-400' :
                        'bg-green-900/30 text-green-400'
                      }`}>
                        {result.severity}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      
      {/* Saved Queries */}
      {savedQueries.length > 0 && !results && (
        <div className="bg-gray-800 rounded-lg p-4 flex-1 overflow-auto">
          <h4 className="text-lg font-semibold mb-2">Saved Queries</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {savedQueries.map(query => (
              <div key={query.id} className="bg-gray-700 p-3 rounded-lg">
                <div className="font-medium mb-1">{query.name}</div>
                {query.description && (
                  <div className="text-sm text-gray-400 mb-2">{query.description}</div>
                )}
                <div className="text-xs text-gray-500 mb-2">
                  Created: {new Date(query.created).toLocaleString()}
                </div>
                <div className="flex justify-end">
                  <button
                    className="bg-blue-600 hover:bg-blue-700 text-white rounded px-2 py-1 text-xs"
                    onClick={() => {
                      setQueryName(query.name);
                      setQueryDescription(query.description);
                      setConditions(query.conditions);
                      setDataSource(query.dataSource);
                      setTimeRange(query.timeRange);
                    }}
                  >
                    Load Query
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default QueryBuilder;
