import React, { useState } from 'react';
import { FaPlay, FaInfoCircle, FaExclamationTriangle, FaCheck, FaChevronRight, FaChevronDown, FaFileAlt } from 'react-icons/fa';

/**
 * HuntingPlaybooks Component
 *
 * Provides structured hunting playbooks with step-by-step guidance
 * for different threat hunting scenarios.
 */
const HuntingPlaybooks = () => {
  const [selectedPlaybook, setSelectedPlaybook] = useState(null);
  const [activeStep, setActiveStep] = useState(0);
  const [expandedSections, setExpandedSections] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Sample playbooks
  const playbooks = [
    {
      id: 'lateral-movement',
      name: 'Lateral Movement Detection',
      description: 'Hunt for signs of lateral movement within the network, which may indicate an attacker moving between systems.',
      difficulty: 'Medium',
      estimatedTime: '45 minutes',
      mitreTactics: ['Lateral Movement', 'Discovery'],
      mitreTechniques: ['T1021', 'T1018', 'T1078'],
      steps: [
        {
          id: 'step1',
          name: 'Identify Authentication Events',
          description: 'Look for unusual authentication patterns that may indicate lateral movement.',
          query: 'event_type = "authentication" AND outcome = "success" | stats count by source_ip, destination_ip, user',
          expectedFindings: 'Multiple successful authentications from a single source to multiple destinations in a short time period.',
          guidance: 'Focus on authentications occurring outside normal business hours or from unusual source systems.'
        },
        {
          id: 'step2',
          name: 'Analyze Remote Service Usage',
          description: 'Examine the use of remote services like RDP, SSH, and WinRM.',
          query: 'event_type IN ("rdp_connection", "ssh_connection", "winrm_connection") | stats count by source_ip, destination_ip, user, process_name',
          expectedFindings: 'Remote service connections between systems that don\'t normally communicate.',
          guidance: 'Compare against baseline of normal remote service usage patterns.'
        },
        {
          id: 'step3',
          name: 'Detect Unusual Process Execution',
          description: 'Look for execution of processes commonly used for lateral movement.',
          query: 'event_type = "process_execution" AND process_name IN ("psexec.exe", "wmic.exe", "sc.exe", "schtasks.exe") | stats count by host, user, process_name, command_line',
          expectedFindings: 'Execution of administrative tools with command-line parameters indicating remote system targeting.',
          guidance: 'Pay special attention to command lines containing IP addresses or hostnames.'
        },
        {
          id: 'step4',
          name: 'Identify Credential Dumping',
          description: 'Look for evidence of credential dumping which often precedes lateral movement.',
          query: 'event_type = "process_execution" AND (process_name = "mimikatz.exe" OR command_line CONTAINS "sekurlsa" OR command_line CONTAINS "lsass.exe") | sort by timestamp',
          expectedFindings: 'Processes accessing lsass.exe memory or known credential dumping tools.',
          guidance: 'These activities are highly suspicious and warrant immediate investigation.'
        }
      ]
    },
    {
      id: 'persistence-mechanisms',
      name: 'Persistence Mechanisms',
      description: 'Hunt for persistence mechanisms that allow attackers to maintain access to compromised systems.',
      difficulty: 'Hard',
      estimatedTime: '60 minutes',
      mitreTactics: ['Persistence', 'Privilege Escalation'],
      mitreTechniques: ['T1053', 'T1547', 'T1543'],
      steps: [
        {
          id: 'step1',
          name: 'Identify Scheduled Task Creation',
          description: 'Look for the creation of scheduled tasks that may be used for persistence.',
          query: 'event_type = "scheduled_task_creation" | sort by timestamp',
          expectedFindings: 'Recently created scheduled tasks, especially those with unusual command lines or created by unusual users.',
          guidance: 'Compare against known legitimate scheduled tasks in your environment.'
        },
        {
          id: 'step2',
          name: 'Examine Registry Run Keys',
          description: 'Check for modifications to registry run keys that execute at startup.',
          query: 'event_type = "registry_modification" AND registry_path CONTAINS "\\\\Run" | sort by timestamp',
          expectedFindings: 'Additions to Run, RunOnce, or other autostart registry locations.',
          guidance: 'Verify the legitimacy of all executables referenced in these registry keys.'
        },
        {
          id: 'step3',
          name: 'Analyze Service Creation',
          description: 'Look for the creation of new services or modifications to existing services.',
          query: 'event_type = "service_creation" OR (event_type = "service_modification" AND field = "start_type") | sort by timestamp',
          expectedFindings: 'New services or changes to service start types, especially to automatic.',
          guidance: 'Investigate services with unusual names, descriptions, or binary paths.'
        },
        {
          id: 'step4',
          name: 'Detect WMI Event Subscriptions',
          description: 'Identify WMI event subscriptions used for persistence.',
          query: 'event_type = "wmi_subscription" | sort by timestamp',
          expectedFindings: 'WMI event subscriptions, especially those with PowerShell or script execution in the consumer.',
          guidance: 'WMI persistence is sophisticated and often indicates an advanced adversary.'
        }
      ]
    },
    {
      id: 'data-exfiltration',
      name: 'Data Exfiltration Detection',
      description: 'Hunt for signs of data being exfiltrated from your environment to external locations.',
      difficulty: 'Medium',
      estimatedTime: '50 minutes',
      mitreTactics: ['Exfiltration', 'Command and Control'],
      mitreTechniques: ['T1048', 'T1041', 'T1567'],
      steps: [
        {
          id: 'step1',
          name: 'Identify Large Outbound Transfers',
          description: 'Look for unusually large data transfers to external destinations.',
          query: 'event_type = "network_connection" AND direction = "outbound" AND bytes_sent > 10000000 | sort by bytes_sent DESC',
          expectedFindings: 'Large outbound data transfers, especially to unusual destinations.',
          guidance: 'Compare against normal data transfer patterns for the source systems.'
        },
        {
          id: 'step2',
          name: 'Detect Unusual Protocol Usage',
          description: 'Identify the use of protocols that may be used for exfiltration.',
          query: 'event_type = "network_connection" AND protocol IN ("ftp", "sftp", "scp", "dns") AND destination_ip NOT IN (internal_subnet) | stats count by source_ip, destination_ip, protocol',
          expectedFindings: 'Use of file transfer or tunneling protocols to external destinations.',
          guidance: "DNS tunneling is particularly concerning as it's often overlooked."
        },
        {
          id: 'step3',
          name: 'Analyze Cloud Storage Access',
          description: 'Look for access to cloud storage services that might be used for exfiltration.',
          query: 'event_type = "http_request" AND (url CONTAINS "dropbox.com" OR url CONTAINS "drive.google.com" OR url CONTAINS "onedrive.com" OR url CONTAINS "amazonaws.com") AND method = "PUT" | sort by timestamp',
          expectedFindings: 'Uploads to cloud storage services, especially from systems that don't normally use these services.',
          guidance: 'Verify if the cloud services are approved for business use.'
        },
        {
          id: 'step4',
          name: 'Examine Email Attachments',
          description: 'Look for suspicious email attachments that may contain exfiltrated data.',
          query: 'event_type = "email" AND direction = "outbound" AND attachment_size > 1000000 | sort by attachment_size DESC',
          expectedFindings: 'Large email attachments sent to external recipients, especially personal email domains.',
          guidance: 'Pay special attention to compressed or encrypted attachments.'
        }
      ]
    }
  ];

  // Toggle section expansion
  const toggleSection = (sectionId) => {
    setExpandedSections({
      ...expandedSections,
      [sectionId]: !expandedSections[sectionId]
    });
  };

  // Start a playbook
  const startPlaybook = (playbook) => {
    setSelectedPlaybook(playbook);
    setActiveStep(0);
    setResults(null);
    setError(null);
  };

  // Execute the current step
  const executeStep = () => {
    if (!selectedPlaybook || activeStep >= selectedPlaybook.steps.length) {
      return;
    }

    setLoading(true);
    setError(null);

    // Simulate query execution
    setTimeout(() => {
      setLoading(false);

      // Generate sample results
      const step = selectedPlaybook.steps[activeStep];
      const sampleResults = generateSampleResults(step);

      setResults({
        step,
        timestamp: new Date().toISOString(),
        data: sampleResults
      });
    }, 1500);
  };

  // Move to the next step
  const nextStep = () => {
    if (activeStep < selectedPlaybook.steps.length - 1) {
      setActiveStep(activeStep + 1);
      setResults(null);
    }
  };

  // Move to the previous step
  const prevStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
      setResults(null);
    }
  };

  // Generate sample results for demonstration
  const generateSampleResults = (step) => {
    const results = [];
    const count = Math.floor(Math.random() * 5) + 2;

    for (let i = 0; i < count; i++) {
      const result = {
        id: `result-${i}`,
        timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString()
      };

      // Add fields based on the step
      if (step.id === 'step1' && selectedPlaybook.id === 'lateral-movement') {
        result.source_ip = `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
        result.destination_ip = `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
        result.user = `user${Math.floor(Math.random() * 10)}`;
        result.count = Math.floor(Math.random() * 20) + 1;
      } else if (step.id === 'step2' && selectedPlaybook.id === 'lateral-movement') {
        result.source_ip = `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
        result.destination_ip = `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
        result.user = `user${Math.floor(Math.random() * 10)}`;
        result.process_name = ['rdp.exe', 'ssh.exe', 'winrm.exe'][Math.floor(Math.random() * 3)];
        result.count = Math.floor(Math.random() * 15) + 1;
      } else if (step.id === 'step1' && selectedPlaybook.id === 'data-exfiltration') {
        result.source_ip = `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
        result.destination_ip = `203.0.113.${Math.floor(Math.random() * 255)}`;
        result.bytes_sent = Math.floor(Math.random() * 100000000) + 10000000;
        result.protocol = ['tcp', 'udp'][Math.floor(Math.random() * 2)];
        result.port = [443, 80, 8080, 22][Math.floor(Math.random() * 4)];
      } else {
        // Generic fields for other steps
        result.host = `host-${Math.floor(Math.random() * 100)}.example.com`;
        result.user = `user${Math.floor(Math.random() * 10)}`;
        result.process_name = `process-${Math.floor(Math.random() * 20)}.exe`;
        result.command_line = `command line arguments ${i}`;
      }

      results.push(result);
    }

    return results;
  };

  // Render the playbook list
  const renderPlaybookList = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {playbooks.map(playbook => (
          <div key={playbook.id} className="bg-gray-700 rounded-lg p-4">
            <h4 className="text-lg font-semibold mb-2">{playbook.name}</h4>
            <p className="text-sm text-gray-300 mb-3">{playbook.description}</p>

            <div className="grid grid-cols-2 gap-2 text-xs text-gray-400 mb-3">
              <div>
                <span className="font-medium">Difficulty:</span> {playbook.difficulty}
              </div>
              <div>
                <span className="font-medium">Est. Time:</span> {playbook.estimatedTime}
              </div>
              <div>
                <span className="font-medium">MITRE Tactics:</span> {playbook.mitreTactics.join(', ')}
              </div>
              <div>
                <span className="font-medium">Steps:</span> {playbook.steps.length}
              </div>
            </div>

            <button
              className="w-full bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2 text-sm flex items-center justify-center"
              onClick={() => startPlaybook(playbook)}
            >
              <FaPlay className="mr-2" /> Start Playbook
            </button>
          </div>
        ))}
      </div>
    );
  };

  // Render the active playbook
  const renderActivePlaybook = () => {
    if (!selectedPlaybook) return null;

    const currentStep = selectedPlaybook.steps[activeStep];

    return (
      <div className="flex flex-col h-full">
        {/* Playbook header */}
        <div className="bg-gray-700 rounded-lg p-4 mb-4">
          <div className="flex justify-between items-start mb-2">
            <h4 className="text-lg font-semibold">{selectedPlaybook.name}</h4>
            <button
              className="text-gray-400 hover:text-gray-300 text-sm"
              onClick={() => setSelectedPlaybook(null)}
            >
              Back to Playbooks
            </button>
          </div>

          <p className="text-sm text-gray-300 mb-3">{selectedPlaybook.description}</p>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-400 mb-3">
            <div>
              <span className="font-medium">Difficulty:</span> {selectedPlaybook.difficulty}
            </div>
            <div>
              <span className="font-medium">Est. Time:</span> {selectedPlaybook.estimatedTime}
            </div>
            <div>
              <span className="font-medium">MITRE Tactics:</span> {selectedPlaybook.mitreTactics.join(', ')}
            </div>
            <div>
              <span className="font-medium">MITRE Techniques:</span> {selectedPlaybook.mitreTechniques.join(', ')}
            </div>
          </div>

          {/* Step progress */}
          <div className="flex items-center mb-2">
            {selectedPlaybook.steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-xs ${
                    index < activeStep
                      ? 'bg-green-600 text-white'
                      : index === activeStep
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-600 text-gray-300'
                  }`}
                >
                  {index < activeStep ? <FaCheck /> : index + 1}
                </div>
                {index < selectedPlaybook.steps.length - 1 && (
                  <div className={`h-1 w-8 ${
                    index < activeStep ? 'bg-green-600' : 'bg-gray-600'
                  }`}></div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Current step */}
        <div className="bg-gray-700 rounded-lg p-4 mb-4">
          <h5 className="text-md font-semibold mb-2">Step {activeStep + 1}: {currentStep.name}</h5>
          <p className="text-sm text-gray-300 mb-3">{currentStep.description}</p>

          <div
            className="bg-gray-800 p-3 rounded-lg mb-3 cursor-pointer"
            onClick={() => toggleSection('query')}
          >
            <div className="flex justify-between items-center">
              <h6 className="font-medium">Query</h6>
              {expandedSections.query ? <FaChevronDown /> : <FaChevronRight />}
            </div>
            {expandedSections.query && (
              <div className="mt-2 bg-gray-900 p-2 rounded font-mono text-sm overflow-x-auto">
                {currentStep.query}
              </div>
            )}
          </div>

          <div
            className="bg-gray-800 p-3 rounded-lg mb-3 cursor-pointer"
            onClick={() => toggleSection('expectedFindings')}
          >
            <div className="flex justify-between items-center">
              <h6 className="font-medium">Expected Findings</h6>
              {expandedSections.expectedFindings ? <FaChevronDown /> : <FaChevronRight />}
            </div>
            {expandedSections.expectedFindings && (
              <div className="mt-2 text-sm">
                {currentStep.expectedFindings}
              </div>
            )}
          </div>

          <div
            className="bg-gray-800 p-3 rounded-lg mb-3 cursor-pointer"
            onClick={() => toggleSection('guidance')}
          >
            <div className="flex justify-between items-center">
              <h6 className="font-medium">Analyst Guidance</h6>
              {expandedSections.guidance ? <FaChevronDown /> : <FaChevronRight />}
            </div>
            {expandedSections.guidance && (
              <div className="mt-2 text-sm">
                {currentStep.guidance}
              </div>
            )}
          </div>

          <div className="flex justify-between mt-4">
            <button
              className="bg-gray-600 hover:bg-gray-500 text-white rounded px-4 py-2 text-sm"
              onClick={prevStep}
              disabled={activeStep === 0}
            >
              Previous Step
            </button>
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2 text-sm flex items-center"
              onClick={executeStep}
              disabled={loading}
            >
              {loading ? 'Executing...' : 'Execute Query'}
            </button>
            <button
              className="bg-gray-600 hover:bg-gray-500 text-white rounded px-4 py-2 text-sm"
              onClick={nextStep}
              disabled={activeStep === selectedPlaybook.steps.length - 1}
            >
              Next Step
            </button>
          </div>
        </div>

        {/* Error state */}
        {error && (
          <div className="mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-sm">
            <FaExclamationTriangle className="inline-block mr-2" />
            {error}
          </div>
        )}

        {/* Results */}
        {results && (
          <div className="bg-gray-700 rounded-lg p-4 flex-1 overflow-auto">
            <div className="flex justify-between items-center mb-3">
              <h5 className="font-semibold">Query Results</h5>
              <div className="text-xs text-gray-400">
                Executed at {new Date(results.timestamp).toLocaleString()}
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full bg-gray-800 rounded-lg">
                <thead>
                  <tr>
                    {Object.keys(results.data[0]).filter(key => key !== 'id').map(key => (
                      <th key={key} className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                        {key.replace(/_/g, ' ')}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {results.data.map(result => (
                    <tr key={result.id} className="hover:bg-gray-750">
                      {Object.entries(result)
                        .filter(([key]) => key !== 'id')
                        .map(([key, value], index) => (
                          <td key={index} className="px-4 py-2 text-sm">
                            {typeof value === 'object'
                              ? JSON.stringify(value)
                              : String(value)
                            }
                          </td>
                        ))
                      }
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-4 p-3 bg-blue-900/20 border border-blue-800 rounded-lg">
              <h6 className="font-medium text-blue-400 mb-1 flex items-center">
                <FaInfoCircle className="mr-1" /> Analysis Guidance
              </h6>
              <p className="text-sm">
                {results.step.guidance}
              </p>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {selectedPlaybook ? renderActivePlaybook() : renderPlaybookList()}
    </div>
  );
};

export default HuntingPlaybooks;
