import React, { useState } from 'react';
import { FaFlask, FaCheck, FaTimes, FaExclamationTriangle, FaPlay, FaSave, FaTrash, FaChartBar } from 'react-icons/fa';

/**
 * HypothesisTesting Component
 * 
 * Provides tools for creating and testing threat hunting hypotheses
 * with statistical analysis and visualization.
 */
const HypothesisTesting = () => {
  const [hypotheses, setHypotheses] = useState([
    {
      id: 'h1',
      title: 'Unusual PowerShell Activity',
      description: 'There is unusual PowerShell activity occurring outside business hours that may indicate malicious behavior.',
      status: 'untested',
      queries: [
        {
          id: 'q1',
          name: 'PowerShell Execution Count',
          query: 'event_type = "process_execution" AND process_name = "powershell.exe" | stats count by hour_of_day, host',
          description: 'Count PowerShell executions by hour of day and host'
        },
        {
          id: 'q2',
          name: 'PowerShell Command Line Analysis',
          query: 'event_type = "process_execution" AND process_name = "powershell.exe" AND hour_of_day NOT IN (9, 10, 11, 12, 13, 14, 15, 16, 17) | table timestamp, host, user, command_line',
          description: 'List PowerShell executions outside business hours with command lines'
        }
      ],
      results: null,
      conclusion: null
    },
    {
      id: 'h2',
      title: 'Data Staging Before Exfiltration',
      description: 'Attackers are staging data in specific directories before exfiltration.',
      status: 'untested',
      queries: [
        {
          id: 'q1',
          name: 'Unusual File Creation',
          query: 'event_type = "file_creation" AND file_path CONTAINS "\\\\Temp\\\\" AND file_size > 10000000 | stats count by host, user, file_extension',
          description: 'Identify large files created in temp directories'
        },
        {
          id: 'q2',
          name: 'Archive Creation',
          query: 'event_type = "process_execution" AND (process_name CONTAINS "zip" OR process_name CONTAINS "rar" OR process_name CONTAINS "7z") | table timestamp, host, user, process_name, command_line',
          description: 'Detect use of archiving tools'
        }
      ],
      results: null,
      conclusion: null
    }
  ]);
  const [newHypothesis, setNewHypothesis] = useState({
    title: '',
    description: '',
    queries: [{ name: '', query: '', description: '' }]
  });
  const [activeHypothesis, setActiveHypothesis] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);

  // Add a new query to the hypothesis being created
  const addQuery = () => {
    setNewHypothesis({
      ...newHypothesis,
      queries: [...newHypothesis.queries, { name: '', query: '', description: '' }]
    });
  };

  // Update a query in the new hypothesis
  const updateQuery = (index, field, value) => {
    const updatedQueries = [...newHypothesis.queries];
    updatedQueries[index] = { ...updatedQueries[index], [field]: value };
    
    setNewHypothesis({
      ...newHypothesis,
      queries: updatedQueries
    });
  };

  // Remove a query from the new hypothesis
  const removeQuery = (index) => {
    if (newHypothesis.queries.length <= 1) {
      return;
    }
    
    const updatedQueries = newHypothesis.queries.filter((_, i) => i !== index);
    
    setNewHypothesis({
      ...newHypothesis,
      queries: updatedQueries
    });
  };

  // Save the new hypothesis
  const saveHypothesis = () => {
    if (!newHypothesis.title || !newHypothesis.description) {
      setError('Hypothesis title and description are required');
      return;
    }
    
    if (newHypothesis.queries.some(q => !q.name || !q.query)) {
      setError('All queries must have a name and query string');
      return;
    }
    
    const newId = `h${hypotheses.length + 1}`;
    const formattedQueries = newHypothesis.queries.map((q, index) => ({
      id: `q${index + 1}`,
      name: q.name,
      query: q.query,
      description: q.description
    }));
    
    const hypothesis = {
      id: newId,
      title: newHypothesis.title,
      description: newHypothesis.description,
      status: 'untested',
      queries: formattedQueries,
      results: null,
      conclusion: null
    };
    
    setHypotheses([...hypotheses, hypothesis]);
    setNewHypothesis({
      title: '',
      description: '',
      queries: [{ name: '', query: '', description: '' }]
    });
    setShowForm(false);
    setError(null);
  };

  // Test a hypothesis
  const testHypothesis = (hypothesis) => {
    setActiveHypothesis(hypothesis);
    setLoading(true);
    setError(null);
    
    // Simulate query execution
    setTimeout(() => {
      const results = hypothesis.queries.map(query => ({
        queryId: query.id,
        queryName: query.name,
        executionTime: new Date().toISOString(),
        data: generateSampleResults(query),
        anomalyScore: Math.random() * 100,
        baselineDeviation: Math.random() * 50
      }));
      
      // Calculate overall anomaly score
      const overallScore = results.reduce((sum, result) => sum + result.anomalyScore, 0) / results.length;
      
      // Determine if hypothesis is supported
      const isSupported = overallScore > 70;
      
      // Update the hypothesis with results
      const updatedHypotheses = hypotheses.map(h => {
        if (h.id === hypothesis.id) {
          return {
            ...h,
            status: isSupported ? 'confirmed' : 'rejected',
            results: results,
            conclusion: isSupported
              ? 'The hypothesis is supported by the data. The observed patterns show significant deviation from baseline behavior.'
              : 'The hypothesis is not supported by the data. The observed patterns are within normal baseline behavior.'
          };
        }
        return h;
      });
      
      setHypotheses(updatedHypotheses);
      setActiveHypothesis(updatedHypotheses.find(h => h.id === hypothesis.id));
      setLoading(false);
    }, 2000);
  };

  // Generate sample results for demonstration
  const generateSampleResults = (query) => {
    const results = [];
    const count = Math.floor(Math.random() * 10) + 5;
    
    if (query.name.includes('PowerShell')) {
      // PowerShell execution data
      for (let i = 0; i < count; i++) {
        const hour = Math.floor(Math.random() * 24);
        const host = `host-${Math.floor(Math.random() * 20)}.example.com`;
        
        results.push({
          hour_of_day: hour,
          host: host,
          count: Math.floor(Math.random() * 50) + 1,
          user: hour >= 9 && hour <= 17 ? `regular-user${i}` : `admin${i}`,
          command_line: hour >= 9 && hour <= 17 
            ? 'powershell.exe -Command "Get-Service | Where-Object {$_.Status -eq \'Running\'}"'
            : 'powershell.exe -e JABjAGwAaQBlAG4AdAAgAD0AIABOAGUAdwAtAE8AYgBqAGUAYwB0ACAAUwB5AHMAdABlAG0ALgBOAGUAdAAuAFMAbwBjAGsAZQB0AHMALgBUAEMAUABDAGwAaQBlAG4AdAAoACIAMQA5ADIALgAxADYAOAAuADEALgAxADAAMAA...'
        });
      }
    } else if (query.name.includes('File Creation') || query.name.includes('Archive')) {
      // File or archive data
      for (let i = 0; i < count; i++) {
        results.push({
          timestamp: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString(),
          host: `host-${Math.floor(Math.random() * 20)}.example.com`,
          user: `user${Math.floor(Math.random() * 10)}`,
          file_path: `C:\\Users\\<USER>\\AppData\\Local\\Temp\\data${i}.zip`,
          file_size: Math.floor(Math.random() * 100000000) + 10000000,
          file_extension: ['.zip', '.rar', '.7z', '.tar', '.gz'][Math.floor(Math.random() * 5)],
          process_name: ['7z.exe', 'winrar.exe', 'zip.exe'][Math.floor(Math.random() * 3)],
          command_line: `7z.exe a archive${i}.zip C:\\Users\\<USER>\\Documents\\*`
        });
      }
    } else {
      // Generic data
      for (let i = 0; i < count; i++) {
        results.push({
          timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(),
          host: `host-${Math.floor(Math.random() * 20)}.example.com`,
          user: `user${Math.floor(Math.random() * 10)}`,
          event_type: ['process_execution', 'file_access', 'network_connection'][Math.floor(Math.random() * 3)],
          severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)]
        });
      }
    }
    
    return results;
  };

  // Render the hypothesis list
  const renderHypothesisList = () => {
    return (
      <div className="space-y-4">
        {hypotheses.map(hypothesis => (
          <div 
            key={hypothesis.id} 
            className={`bg-gray-700 rounded-lg p-4 border-l-4 ${
              hypothesis.status === 'confirmed' ? 'border-green-500' :
              hypothesis.status === 'rejected' ? 'border-red-500' :
              'border-gray-500'
            }`}
          >
            <div className="flex justify-between items-start mb-2">
              <h4 className="text-lg font-semibold flex items-center">
                <FaFlask className="mr-2" />
                {hypothesis.title}
                {hypothesis.status === 'confirmed' && <FaCheck className="ml-2 text-green-500" />}
                {hypothesis.status === 'rejected' && <FaTimes className="ml-2 text-red-500" />}
              </h4>
              <div className="text-xs px-2 py-1 rounded-full bg-gray-600">
                {hypothesis.status === 'untested' ? 'Untested' :
                 hypothesis.status === 'confirmed' ? 'Confirmed' :
                 'Rejected'}
              </div>
            </div>
            
            <p className="text-sm text-gray-300 mb-3">{hypothesis.description}</p>
            
            <div className="text-xs text-gray-400 mb-3">
              <div className="font-medium mb-1">Queries:</div>
              <ul className="list-disc list-inside space-y-1">
                {hypothesis.queries.map(query => (
                  <li key={query.id}>{query.name}</li>
                ))}
              </ul>
            </div>
            
            {hypothesis.conclusion && (
              <div className={`text-sm p-2 rounded mb-3 ${
                hypothesis.status === 'confirmed' ? 'bg-green-900/20 text-green-400' :
                'bg-red-900/20 text-red-400'
              }`}>
                {hypothesis.conclusion}
              </div>
            )}
            
            <div className="flex justify-end">
              {hypothesis.status === 'untested' ? (
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white rounded px-3 py-1 text-sm flex items-center"
                  onClick={() => testHypothesis(hypothesis)}
                >
                  <FaPlay className="mr-1" /> Test Hypothesis
                </button>
              ) : (
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white rounded px-3 py-1 text-sm flex items-center"
                  onClick={() => setActiveHypothesis(hypothesis)}
                >
                  View Results
                </button>
              )}
            </div>
          </div>
        ))}
        
        {!showForm && (
          <button
            className="w-full bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg p-4 text-center"
            onClick={() => setShowForm(true)}
          >
            + Create New Hypothesis
          </button>
        )}
      </div>
    );
  };

  // Render the hypothesis creation form
  const renderHypothesisForm = () => {
    return (
      <div className="bg-gray-700 rounded-lg p-4">
        <h4 className="text-lg font-semibold mb-4">Create New Hypothesis</h4>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-400 mb-1">Hypothesis Title</label>
          <input
            type="text"
            className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter a clear, testable hypothesis..."
            value={newHypothesis.title}
            onChange={(e) => setNewHypothesis({ ...newHypothesis, title: e.target.value })}
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-400 mb-1">Description</label>
          <textarea
            className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Describe what you're looking for and why..."
            rows={3}
            value={newHypothesis.description}
            onChange={(e) => setNewHypothesis({ ...newHypothesis, description: e.target.value })}
          />
        </div>
        
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium text-gray-400">Queries</label>
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white rounded px-2 py-1 text-xs flex items-center"
              onClick={addQuery}
            >
              + Add Query
            </button>
          </div>
          
          <div className="space-y-3">
            {newHypothesis.queries.map((query, index) => (
              <div key={index} className="p-3 bg-gray-600 rounded-lg">
                <div className="mb-2">
                  <label className="block text-xs font-medium text-gray-400 mb-1">Query Name</label>
                  <input
                    type="text"
                    className="w-full bg-gray-700 border border-gray-500 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Name this query..."
                    value={query.name}
                    onChange={(e) => updateQuery(index, 'name', e.target.value)}
                  />
                </div>
                
                <div className="mb-2">
                  <label className="block text-xs font-medium text-gray-400 mb-1">Query String</label>
                  <textarea
                    className="w-full bg-gray-700 border border-gray-500 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 font-mono"
                    placeholder="event_type = 'process_execution' AND ..."
                    rows={2}
                    value={query.query}
                    onChange={(e) => updateQuery(index, 'query', e.target.value)}
                  />
                </div>
                
                <div className="mb-2">
                  <label className="block text-xs font-medium text-gray-400 mb-1">Description</label>
                  <input
                    type="text"
                    className="w-full bg-gray-700 border border-gray-500 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="What does this query look for?"
                    value={query.description}
                    onChange={(e) => updateQuery(index, 'description', e.target.value)}
                  />
                </div>
                
                {newHypothesis.queries.length > 1 && (
                  <button
                    className="text-red-400 hover:text-red-300 text-xs"
                    onClick={() => removeQuery(index)}
                  >
                    Remove Query
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
        
        {error && (
          <div className="mb-4 p-2 bg-red-900/20 border border-red-800 rounded-lg text-red-400 text-xs">
            <FaExclamationTriangle className="inline-block mr-1" />
            {error}
          </div>
        )}
        
        <div className="flex justify-end space-x-2">
          <button
            className="bg-gray-600 hover:bg-gray-500 text-white rounded px-3 py-2 text-sm"
            onClick={() => {
              setShowForm(false);
              setError(null);
            }}
          >
            Cancel
          </button>
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white rounded px-3 py-2 text-sm flex items-center"
            onClick={saveHypothesis}
          >
            <FaSave className="mr-1" /> Save Hypothesis
          </button>
        </div>
      </div>
    );
  };

  // Render the hypothesis results
  const renderHypothesisResults = () => {
    if (!activeHypothesis || !activeHypothesis.results) return null;
    
    return (
      <div className="space-y-4">
        <div className="bg-gray-700 rounded-lg p-4">
          <div className="flex justify-between items-start mb-2">
            <h4 className="text-lg font-semibold flex items-center">
              <FaFlask className="mr-2" />
              {activeHypothesis.title}
              {activeHypothesis.status === 'confirmed' && <FaCheck className="ml-2 text-green-500" />}
              {activeHypothesis.status === 'rejected' && <FaTimes className="ml-2 text-red-500" />}
            </h4>
            <button
              className="text-gray-400 hover:text-gray-300 text-sm"
              onClick={() => setActiveHypothesis(null)}
            >
              Back to Hypotheses
            </button>
          </div>
          
          <p className="text-sm text-gray-300 mb-3">{activeHypothesis.description}</p>
          
          <div className={`text-sm p-3 rounded mb-4 ${
            activeHypothesis.status === 'confirmed' ? 'bg-green-900/20 text-green-400' :
            'bg-red-900/20 text-red-400'
          }`}>
            <div className="font-medium mb-1">Conclusion:</div>
            <p>{activeHypothesis.conclusion}</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-800 p-3 rounded-lg">
              <div className="text-xs text-gray-400 mb-1">Overall Anomaly Score</div>
              <div className="text-2xl font-bold">
                {Math.round(activeHypothesis.results.reduce((sum, r) => sum + r.anomalyScore, 0) / activeHypothesis.results.length)}%
              </div>
              <div className="text-xs text-gray-400 mt-1">
                {activeHypothesis.status === 'confirmed' ? 'Significant anomaly detected' : 'Within normal parameters'}
              </div>
            </div>
            <div className="bg-gray-800 p-3 rounded-lg">
              <div className="text-xs text-gray-400 mb-1">Queries Executed</div>
              <div className="text-2xl font-bold">{activeHypothesis.queries.length}</div>
              <div className="text-xs text-gray-400 mt-1">
                {new Date(activeHypothesis.results[0].executionTime).toLocaleString()}
              </div>
            </div>
            <div className="bg-gray-800 p-3 rounded-lg">
              <div className="text-xs text-gray-400 mb-1">Data Points Analyzed</div>
              <div className="text-2xl font-bold">
                {activeHypothesis.results.reduce((sum, r) => sum + r.data.length, 0)}
              </div>
              <div className="text-xs text-gray-400 mt-1">
                Across {Object.keys(activeHypothesis.results[0].data[0]).length} dimensions
              </div>
            </div>
          </div>
        </div>
        
        {/* Query Results */}
        {activeHypothesis.results.map((result, index) => (
          <div key={index} className="bg-gray-700 rounded-lg p-4">
            <div className="flex justify-between items-start mb-3">
              <h5 className="font-semibold">{result.queryName}</h5>
              <div className="text-xs px-2 py-1 rounded-full bg-gray-600">
                Anomaly Score: {Math.round(result.anomalyScore)}%
              </div>
            </div>
            
            <div className="text-sm text-gray-300 mb-3">
              {activeHypothesis.queries.find(q => q.id === result.queryId).description}
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full bg-gray-800 rounded-lg">
                <thead>
                  <tr>
                    {Object.keys(result.data[0]).map(key => (
                      <th key={key} className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                        {key.replace(/_/g, ' ')}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {result.data.map((item, i) => (
                    <tr key={i} className="hover:bg-gray-750">
                      {Object.values(item).map((value, j) => (
                        <td key={j} className="px-4 py-2 text-sm">
                          {typeof value === 'object' 
                            ? JSON.stringify(value) 
                            : String(value)
                          }
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            <div className="mt-4 p-3 bg-gray-800 rounded-lg">
              <h6 className="font-medium mb-2 flex items-center">
                <FaChartBar className="mr-1 text-blue-400" /> Statistical Analysis
              </h6>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-xs text-gray-400 mb-1">Baseline Deviation</div>
                  <div className="font-medium">{Math.round(result.baselineDeviation)}%</div>
                </div>
                <div>
                  <div className="text-xs text-gray-400 mb-1">Confidence Level</div>
                  <div className="font-medium">
                    {result.anomalyScore > 80 ? 'High' : result.anomalyScore > 50 ? 'Medium' : 'Low'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Loading state */}
      {loading && (
        <div className="absolute inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}
      
      {activeHypothesis && activeHypothesis.results
        ? renderHypothesisResults()
        : showForm
          ? renderHypothesisForm()
          : renderHypothesisList()
      }
    </div>
  );
};

export default HypothesisTesting;
