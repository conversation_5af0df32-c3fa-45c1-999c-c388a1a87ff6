import React from 'react';

const GridOverlay = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      <div
        className="w-full h-full opacity-[0.03]"
        style={{
          backgroundImage: `
            linear-gradient(to right, rgb(136, 204, 20) 1px, transparent 1px),
            linear-gradient(to bottom, rgb(136, 204, 20) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
      />
      <div className="absolute inset-0" style={{
        background: 'radial-gradient(circle at 50% 50%, rgba(0,0,0,0) 0%, rgba(0,0,0,0.5) 100%)'
      }} />
    </div>
  );
};

export default GridOverlay;