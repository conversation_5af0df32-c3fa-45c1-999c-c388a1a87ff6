import React from 'react';
import { motion } from 'framer-motion';

const GlitchText = ({ children, className }) => {
  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        {children}
      </div>
      <motion.div
        className="absolute top-0 left-0 w-full h-full text-[#00f3ff]/30"
        animate={{
          x: [-1, 1, -1],
          transition: {
            repeat: Infinity,
            duration: 2,
            ease: "linear",
          }
        }}
      >
        {children}
      </motion.div>
    </div>
  );
};

export default GlitchText;