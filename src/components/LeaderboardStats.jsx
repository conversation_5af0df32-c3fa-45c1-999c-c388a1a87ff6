import React from 'react';
import { motion } from 'framer-motion';
import { FaTrophy, FaChartLine, FaMedal } from 'react-icons/fa';

const LeaderboardStats = ({ stats }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Weekly Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg p-6"
      >
        <div className="flex items-center gap-4 mb-4">
          <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
            <FaTrophy className="text-[#88cc14] text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-gray-900">Weekly Leaders</h3>
            <p className="text-sm text-gray-500">Top performers this week</p>
          </div>
        </div>

        <div className="space-y-4">
          {stats.weekly.slice(0, 3).map((entry, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-[#88cc14] font-bold">#{index + 1}</span>
                <span className="text-gray-900">{entry.users?.username}</span>
              </div>
              <span className="font-bold">{entry.total_points} pts</span>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Monthly Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-lg p-6"
      >
        <div className="flex items-center gap-4 mb-4">
          <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
            <FaChartLine className="text-[#88cc14] text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-gray-900">Monthly Progress</h3>
            <p className="text-sm text-gray-500">Top performers this month</p>
          </div>
        </div>

        <div className="space-y-4">
          {stats.monthly.slice(0, 3).map((entry, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-[#88cc14] font-bold">#{index + 1}</span>
                <span className="text-gray-900">{entry.users?.username}</span>
              </div>
              <span className="font-bold">{entry.total_points} pts</span>
            </div>
          ))}
        </div>
      </motion.div>

      {/* All-Time Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-lg p-6"
      >
        <div className="flex items-center gap-4 mb-4">
          <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
            <FaMedal className="text-[#88cc14] text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-gray-900">Hall of Fame</h3>
            <p className="text-sm text-gray-500">All-time top performers</p>
          </div>
        </div>

        <div className="space-y-4">
          {stats.allTime.slice(0, 3).map((entry, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-[#88cc14] font-bold">#{index + 1}</span>
                <span className="text-gray-900">{entry.users?.username}</span>
              </div>
              <span className="font-bold">{entry.total_points} pts</span>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default LeaderboardStats;