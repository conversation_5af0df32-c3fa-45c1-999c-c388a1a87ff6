import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaGraduationCap, FaLock, FaArrowRight, FaCheck, FaReg<PERSON>lock, FaChartLine, FaExclamationTriangle } from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { SUBSCRIPTION_TIERS } from '../../config/subscriptionTiers';

/**
 * DynamicLearningModule Component
 * 
 * A component that displays learning modules with adaptive content based on user's skill level,
 * progress, and interests. Provides personalized recommendations and tracks progress.
 */
const DynamicLearningModule = ({ 
  userSkills = {}, 
  userProgress = {}, 
  subscriptionTier = SUBSCRIPTION_TIERS.FREE 
}) => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [recommendedModules, setRecommendedModules] = useState([]);
  const [inProgressModules, setInProgressModules] = useState([]);
  const [skillAssessment, setSkillAssessment] = useState(null);
  const [showAssessment, setShowAssessment] = useState(false);
  
  // Mock data for learning paths
  const learningPaths = [
    {
      id: 'defensive',
      name: 'Defensive Security',
      description: 'Learn to protect systems and networks from attacks',
      modules: [
        {
          id: 'network-security',
          title: 'Network Security Fundamentals',
          description: 'Learn the basics of securing networks against common threats',
          difficulty: 'Beginner',
          estimatedTime: 60,
          progress: 0,
          free: true
        },
        {
          id: 'web-app-security',
          title: 'Web Application Security',
          description: 'Understand common web vulnerabilities and how to protect against them',
          difficulty: 'Intermediate',
          estimatedTime: 90,
          progress: 0,
          free: true
        },
        {
          id: 'incident-response',
          title: 'Incident Response',
          description: 'Learn how to effectively respond to security incidents',
          difficulty: 'Advanced',
          estimatedTime: 120,
          progress: 0,
          free: false
        }
      ]
    },
    {
      id: 'offensive',
      name: 'Offensive Security',
      description: 'Master ethical hacking and penetration testing techniques',
      modules: [
        {
          id: 'recon-techniques',
          title: 'Reconnaissance Techniques',
          description: 'Learn effective information gathering methods',
          difficulty: 'Beginner',
          estimatedTime: 60,
          progress: 0,
          free: true
        },
        {
          id: 'vulnerability-scanning',
          title: 'Vulnerability Scanning',
          description: 'Identify vulnerabilities in systems and networks',
          difficulty: 'Intermediate',
          estimatedTime: 90,
          progress: 0,
          free: true
        },
        {
          id: 'exploit-development',
          title: 'Exploit Development',
          description: 'Learn how to develop and use exploits ethically',
          difficulty: 'Advanced',
          estimatedTime: 150,
          progress: 0,
          free: false
        }
      ]
    }
  ];
  
  // Mock assessment questions
  const assessmentQuestions = [
    {
      id: 'q1',
      question: 'What is your experience level with cybersecurity?',
      options: [
        { id: 'a1', text: 'Beginner - Just starting out', value: 'beginner' },
        { id: 'a2', text: 'Intermediate - Some experience', value: 'intermediate' },
        { id: 'a3', text: 'Advanced - Experienced professional', value: 'advanced' }
      ]
    },
    {
      id: 'q2',
      question: 'Which area of cybersecurity interests you most?',
      options: [
        { id: 'a1', text: 'Defensive Security', value: 'defensive' },
        { id: 'a2', text: 'Offensive Security', value: 'offensive' },
        { id: 'a3', text: 'Both equally', value: 'both' }
      ]
    },
    {
      id: 'q3',
      question: 'What is your primary goal for learning cybersecurity?',
      options: [
        { id: 'a1', text: 'Career advancement', value: 'career' },
        { id: 'a2', text: 'Personal interest', value: 'personal' },
        { id: 'a3', text: 'Specific certification', value: 'certification' }
      ]
    }
  ];
  
  // Initialize user progress from stored data or props
  useEffect(() => {
    // In a real implementation, this would fetch from an API
    // For now, we'll use mock data and localStorage
    
    // Check if we have stored progress
    const storedProgress = localStorage.getItem('learning_progress');
    const storedSkills = localStorage.getItem('user_skills');
    
    if (storedProgress) {
      try {
        const parsedProgress = JSON.parse(storedProgress);
        
        // Find modules that are in progress
        const inProgress = [];
        
        learningPaths.forEach(path => {
          path.modules.forEach(module => {
            const moduleProgress = parsedProgress[module.id];
            if (moduleProgress && moduleProgress > 0 && moduleProgress < 100) {
              inProgress.push({
                ...module,
                progress: moduleProgress,
                pathName: path.name
              });
            }
          });
        });
        
        setInProgressModules(inProgress);
      } catch (error) {
        console.error('Error parsing stored progress:', error);
      }
    }
    
    // Check if user has completed the skill assessment
    if (storedSkills) {
      try {
        const parsedSkills = JSON.parse(storedSkills);
        setSkillAssessment(parsedSkills);
        
        // Generate recommendations based on skills
        generateRecommendations(parsedSkills);
      } catch (error) {
        console.error('Error parsing stored skills:', error);
      }
    } else {
      // If no skills assessment, show the assessment prompt
      setShowAssessment(true);
    }
  }, []);
  
  // Generate module recommendations based on user skills and interests
  const generateRecommendations = (skills) => {
    // In a real implementation, this would use a more sophisticated algorithm
    // For now, we'll use a simple approach based on the mock skills
    
    const recommendations = [];
    
    // Determine preferred path
    const preferredPath = skills?.interests === 'defensive' ? 'defensive' : 
                          skills?.interests === 'offensive' ? 'offensive' : 'both';
    
    // Determine appropriate difficulty
    const appropriateDifficulty = skills?.experience === 'beginner' ? 'Beginner' :
                                 skills?.experience === 'intermediate' ? 'Intermediate' : 'Advanced';
    
    // Find modules that match the user's interests and experience level
    learningPaths.forEach(path => {
      // Skip paths that don't match user's interests if they have a preference
      if (preferredPath !== 'both' && path.id !== preferredPath) {
        return;
      }
      
      path.modules.forEach(module => {
        // For beginners, recommend beginner modules
        // For intermediate, recommend beginner and intermediate
        // For advanced, recommend all levels but prioritize advanced
        
        let isRecommended = false;
        
        if (skills?.experience === 'beginner' && module.difficulty === 'Beginner') {
          isRecommended = true;
        } else if (skills?.experience === 'intermediate' && 
                  (module.difficulty === 'Beginner' || module.difficulty === 'Intermediate')) {
          isRecommended = true;
        } else if (skills?.experience === 'advanced') {
          isRecommended = true;
        }
        
        // Only include modules that are free or match the user's subscription
        const isAccessible = module.free || subscriptionTier !== SUBSCRIPTION_TIERS.FREE;
        
        if (isRecommended && isAccessible) {
          recommendations.push({
            ...module,
            pathName: path.name,
            pathId: path.id
          });
        }
      });
    });
    
    // Sort recommendations: first by matching difficulty, then by free status
    recommendations.sort((a, b) => {
      // First sort by matching the user's experience level
      if (a.difficulty === appropriateDifficulty && b.difficulty !== appropriateDifficulty) {
        return -1;
      }
      if (a.difficulty !== appropriateDifficulty && b.difficulty === appropriateDifficulty) {
        return 1;
      }
      
      // Then sort by free status (free modules first for free users)
      if (subscriptionTier === SUBSCRIPTION_TIERS.FREE) {
        if (a.free && !b.free) return -1;
        if (!a.free && b.free) return 1;
      }
      
      return 0;
    });
    
    setRecommendedModules(recommendations.slice(0, 3)); // Limit to top 3 recommendations
  };
  
  // Handle skill assessment submission
  const handleAssessmentSubmit = (answers) => {
    // Process the answers to determine user skills and interests
    const skills = {
      experience: answers.q1 || 'beginner',
      interests: answers.q2 || 'both',
      goals: answers.q3 || 'personal'
    };
    
    // Save to localStorage
    localStorage.setItem('user_skills', JSON.stringify(skills));
    
    // Update state
    setSkillAssessment(skills);
    setShowAssessment(false);
    
    // Generate recommendations based on the assessment
    generateRecommendations(skills);
  };
  
  // Handle starting a module
  const handleStartModule = (moduleId) => {
    // In a real implementation, this would update the database
    // For now, we'll just update localStorage
    
    const storedProgress = localStorage.getItem('learning_progress');
    let progress = storedProgress ? JSON.parse(storedProgress) : {};
    
    // Set initial progress to 0 if not started
    if (!progress[moduleId]) {
      progress[moduleId] = 0;
    }
    
    localStorage.setItem('learning_progress', JSON.stringify(progress));
    
    // Navigate to the module
    navigate(`/learn/${moduleId}`);
  };
  
  // Render the skill assessment component
  const renderSkillAssessment = () => {
    const [currentQuestion, setCurrentQuestion] = useState(0);
    const [answers, setAnswers] = useState({});
    
    const handleAnswer = (questionId, answer) => {
      setAnswers({
        ...answers,
        [questionId]: answer
      });
      
      // Move to next question or submit if last question
      if (currentQuestion < assessmentQuestions.length - 1) {
        setCurrentQuestion(currentQuestion + 1);
      } else {
        handleAssessmentSubmit(answers);
      }
    };
    
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
            <FaChartLine className="text-[#88cc14]" />
          </div>
          <h2 className="text-xl font-bold">Skill Assessment</h2>
        </div>
        
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
          Help us personalize your learning experience by answering a few questions.
        </p>
        
        <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6`}>
          <h3 className="text-lg font-semibold mb-4">
            {assessmentQuestions[currentQuestion].question}
          </h3>
          
          <div className="space-y-3">
            {assessmentQuestions[currentQuestion].options.map(option => (
              <button
                key={option.id}
                onClick={() => handleAnswer(assessmentQuestions[currentQuestion].id, option.value)}
                className={`w-full text-left p-3 rounded-lg border ${
                  darkMode 
                    ? 'border-gray-700 hover:bg-gray-700' 
                    : 'border-gray-300 hover:bg-gray-200'
                } transition-colors`}
              >
                {option.text}
              </button>
            ))}
          </div>
          
          <div className="mt-6 flex justify-between items-center">
            <div className="text-sm">
              Question {currentQuestion + 1} of {assessmentQuestions.length}
            </div>
            
            <button
              onClick={() => setShowAssessment(false)}
              className={`text-sm ${
                darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'
              }`}
            >
              Skip for now
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  // Render learning paths
  const renderLearningPaths = () => {
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <h2 className="text-xl font-bold mb-4">Current Learning Paths</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {learningPaths.map(path => (
            <div 
              key={path.id}
              className={`${
                darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'
              } border rounded-lg p-6 h-full`}
            >
              <h3 className="text-lg font-semibold mb-2">{path.name}</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 text-sm`}>
                {path.description}
              </p>
              
              <div className="space-y-3">
                {path.modules.filter(module => module.free || subscriptionTier !== SUBSCRIPTION_TIERS.FREE).slice(0, 3).map(module => {
                  // Get progress from localStorage
                  const storedProgress = localStorage.getItem('learning_progress');
                  const progress = storedProgress ? JSON.parse(storedProgress) : {};
                  const moduleProgress = progress[module.id] || 0;
                  
                  return (
                    <div 
                      key={module.id}
                      className={`p-3 rounded-lg border ${
                        darkMode ? 'border-gray-700' : 'border-gray-300'
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium">{module.title}</h4>
                        <span className={`text-xs px-2 py-1 rounded ${
                          module.difficulty === 'Beginner'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            : module.difficulty === 'Intermediate'
                              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                              : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {module.difficulty}
                        </span>
                      </div>
                      
                      {/* Progress bar */}
                      <div className="mb-2">
                        <div className="flex justify-between text-xs mb-1">
                          <span>{moduleProgress > 0 ? 'In Progress' : 'Not Started'}</span>
                          <span>{moduleProgress}%</span>
                        </div>
                        <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                          <div
                            className="h-2 rounded-full bg-[#88cc14]"
                            style={{ width: `${moduleProgress}%` }}
                          ></div>
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center mt-3">
                        <div className="flex items-center text-xs">
                          <FaRegClock className="mr-1" />
                          <span>{module.estimatedTime} min</span>
                        </div>
                        
                        <button
                          onClick={() => handleStartModule(module.id)}
                          className={`px-3 py-1 rounded text-sm ${
                            moduleProgress > 0
                              ? 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                              : 'bg-blue-500 hover:bg-blue-600 text-white'
                          }`}
                        >
                          {moduleProgress > 0 ? 'Continue' : 'Start'}
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
              
              <Link
                to={`/learn/path/${path.id}`}
                className="mt-4 flex items-center text-[#88cc14] hover:underline text-sm"
              >
                View all modules <FaArrowRight className="ml-1" />
              </Link>
            </div>
          ))}
        </div>
      </div>
    );
  };
  
  // Render personalized recommendations
  const renderRecommendations = () => {
    if (recommendedModules.length === 0) {
      return null;
    }
    
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <h2 className="text-xl font-bold mb-4">Recommended for You</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {recommendedModules.map(module => {
            // Get progress from localStorage
            const storedProgress = localStorage.getItem('learning_progress');
            const progress = storedProgress ? JSON.parse(storedProgress) : {};
            const moduleProgress = progress[module.id] || 0;
            
            return (
              <div 
                key={module.id}
                className={`${
                  darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'
                } border rounded-lg p-4 h-full flex flex-col`}
              >
                <div className="mb-2">
                  <span className={`text-xs px-2 py-1 rounded ${
                    module.pathId === 'defensive'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      : 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
                  }`}>
                    {module.pathName}
                  </span>
                </div>
                
                <h3 className="text-lg font-semibold mb-2">{module.title}</h3>
                
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 text-sm flex-grow`}>
                  {module.description}
                </p>
                
                <div className="mt-auto">
                  <div className="flex justify-between items-center text-xs mb-2">
                    <span className={`px-2 py-1 rounded ${
                      module.difficulty === 'Beginner'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        : module.difficulty === 'Intermediate'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                    }`}>
                      {module.difficulty}
                    </span>
                    
                    <div className="flex items-center">
                      <FaRegClock className="mr-1" />
                      <span>{module.estimatedTime} min</span>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => handleStartModule(module.id)}
                    className="w-full py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors"
                  >
                    {moduleProgress > 0 ? 'Continue Learning' : 'Start Module'}
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };
  
  // Render in-progress modules
  const renderInProgressModules = () => {
    if (inProgressModules.length === 0) {
      return null;
    }
    
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Continue Learning</h2>
          <Link
            to="/learn/in-progress"
            className="text-[#88cc14] hover:underline text-sm"
          >
            View all
          </Link>
        </div>
        
        <div className="space-y-4">
          {inProgressModules.map(module => (
            <div 
              key={module.id}
              className={`${
                darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'
              } border rounded-lg p-4 flex flex-col md:flex-row md:items-center gap-4`}
            >
              <div className="flex-grow">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold">{module.title}</h3>
                  <span className="text-xs text-gray-500">({module.pathName})</span>
                </div>
                
                <div className="mb-2">
                  <div className="flex justify-between text-xs mb-1">
                    <span>In Progress</span>
                    <span>{module.progress}%</span>
                  </div>
                  <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                    <div
                      className="h-2 rounded-full bg-[#88cc14]"
                      style={{ width: `${module.progress}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              
              <button
                onClick={() => handleStartModule(module.id)}
                className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors whitespace-nowrap"
              >
                Continue
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  };
  
  return (
    <div>
      {/* Welcome message */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <h2 className="text-2xl font-bold mb-2">Welcome to Your Learning Dashboard</h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
          Track your progress, discover new modules, and continue your cybersecurity journey.
        </p>
        
        {subscriptionTier === SUBSCRIPTION_TIERS.FREE && (
          <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4 flex flex-col md:flex-row items-center justify-between gap-4`}>
            <div>
              <h3 className="font-semibold mb-1">Free Account</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>
                You have access to beginner modules. Upgrade to unlock all content.
              </p>
            </div>
            
            <Link
              to="/pricing"
              className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors whitespace-nowrap"
            >
              Upgrade Now
            </Link>
          </div>
        )}
      </div>
      
      {/* Skill Assessment (if not completed) */}
      {showAssessment && renderSkillAssessment()}
      
      {/* In-progress modules */}
      {renderInProgressModules()}
      
      {/* Personalized recommendations */}
      {renderRecommendations()}
      
      {/* Learning paths */}
      {renderLearningPaths()}
    </div>
  );
};

export default DynamicLearningModule;
