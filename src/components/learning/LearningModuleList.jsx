import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaCheckCircle, Fa<PERSON>lock, <PERSON>a<PERSON><PERSON> } from 'react-icons/fa';
import { useLearningModule } from '../../contexts/LearningModuleContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import SimpleUpgradeBanner from '../access/SimpleUpgradeBanner';

const LearningModuleList = () => {
  const { darkMode } = useGlobalTheme();
  const { user, profile } = useAuth();
  const { 
    modules, 
    categories, 
    difficulties, 
    loading, 
    error, 
    getFilteredModules,
    isModuleCompleted,
    getModuleProgress
  } = useLearningModule();
  
  const [filteredModules, setFilteredModules] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    category: '',
    difficulty: '',
    completed: ''
  });

  // Apply filters when modules or filters change
  useEffect(() => {
    const filtered = getFilteredModules({
      category: filters.category,
      difficulty: filters.difficulty,
      completed: filters.completed === 'completed' ? true : 
                filters.completed === 'incomplete' ? false : undefined,
      search: searchQuery
    });
    
    setFilteredModules(filtered);
  }, [modules, filters, searchQuery, getFilteredModules]);

  // Handle filter change
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      category: '',
      difficulty: '',
      completed: ''
    });
    setSearchQuery('');
  };

  // Format time (minutes to hours and minutes)
  const formatTime = (minutes) => {
    if (!minutes) return 'N/A';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours === 0) return `${mins} min`;
    if (mins === 0) return `${hours} hr`;
    return `${hours} hr ${mins} min`;
  };

  // Render loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <FaSpinner className="animate-spin text-4xl text-[#88cc14]" />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`${darkMode ? 'bg-red-900/20 border-red-900/30' : 'bg-red-100 border-red-200'} border rounded-lg p-6 text-center`}>
        <h2 className="text-2xl font-bold text-red-500 mb-4">Error</h2>
        <p className={`${darkMode ? 'text-red-300' : 'text-red-700'}`}>{error}</p>
        <button 
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div>
      {/* Upgrade Banner for Free Users */}
      {!profile || profile.subscription_tier === 'free' ? (
        <SimpleUpgradeBanner 
          title="Upgrade to Premium"
          description="Get access to all learning modules and features with a premium subscription."
          buttonText="View Plans"
          buttonLink="/pricing"
          className="mb-6"
        />
      ) : null}
      
      {/* Filters and Search */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                className={`w-full pl-10 pr-4 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                placeholder="Search modules..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <FaSearch className="absolute left-3 top-3 text-gray-400" />
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            {/* Category Filter */}
            <select
              className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.name}>
                  {category.name}
                </option>
              ))}
            </select>

            {/* Difficulty Filter */}
            <select
              className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
              value={filters.difficulty}
              onChange={(e) => handleFilterChange('difficulty', e.target.value)}
            >
              <option value="">All Difficulties</option>
              {difficulties.map(difficulty => (
                <option key={difficulty.id} value={difficulty.name}>
                  {difficulty.name}
                </option>
              ))}
            </select>

            {/* Completion Filter */}
            <select
              className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
              value={filters.completed}
              onChange={(e) => handleFilterChange('completed', e.target.value)}
            >
              <option value="">All Status</option>
              <option value="completed">Completed</option>
              <option value="incomplete">Incomplete</option>
            </select>

            {/* Reset Filters */}
            <button
              onClick={resetFilters}
              className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] hover:bg-[#313e6a] text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-800'} border border-transparent`}
            >
              Reset
            </button>
          </div>
        </div>
      </div>

      {/* Module Count */}
      <div className="mb-4">
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Showing {filteredModules.length} of {modules.length} modules
        </p>
      </div>

      {/* Module Grid */}
      {filteredModules.length === 0 ? (
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
          <FaBook className="mx-auto text-4xl mb-4 text-gray-500" />
          <h2 className="text-xl font-bold mb-2">No Modules Found</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Try adjusting your filters or search query.
          </p>
          <button
            onClick={resetFilters}
            className="mt-4 px-4 py-2 theme-button-primary rounded-lg"
          >
            Reset Filters
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredModules.map(module => {
            const isCompleted = isModuleCompleted(module.id);
            const progress = getModuleProgress(module.id);
            const hasAccess = !module.is_premium || (profile?.subscription_tier === 'premium' || profile?.subscription_tier === 'business');
            
            return (
              <div 
                key={module.id}
                className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden transition-transform hover:transform hover:scale-[1.02]`}
              >
                <div className={`p-4 ${
                  isCompleted
                    ? darkMode ? 'bg-[#88cc14]/10' : 'bg-[#88cc14]/5'
                    : ''
                }`}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-bold">{module.title}</h3>
                    <div className="flex items-center">
                      {module.is_business && (
                        <span className={`ml-2 px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                          Business
                        </span>
                      )}
                      {module.is_premium && !module.is_business && (
                        <span className={`ml-2 px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                          Premium
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-3">
                    <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                      {module.category.name}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                      {module.difficulty.name}
                    </span>
                    {module.estimated_time && (
                      <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
                        <FaClock className="mr-1" /> {formatTime(module.estimated_time)}
                      </span>
                    )}
                  </div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 line-clamp-2`}>
                    {module.description}
                  </p>
                  
                  {/* Progress Bar */}
                  {progress > 0 && (
                    <div className="mb-4">
                      <div className="flex justify-between text-xs mb-1">
                        <span>{isCompleted ? 'Completed' : 'In Progress'}</span>
                        <span>{Math.round(progress)}%</span>
                      </div>
                      <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
                        <div 
                          className="h-2 rounded-full bg-[#88cc14]" 
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex justify-between items-center">
                    {isCompleted && (
                      <span className="flex items-center text-[#88cc14]">
                        <FaCheckCircle className="mr-1" /> Completed
                      </span>
                    )}
                    {!isCompleted && progress > 0 && (
                      <span className="flex items-center text-blue-500">
                        {Math.round(progress)}% Complete
                      </span>
                    )}
                    {!isCompleted && progress === 0 && (
                      <span className="invisible">Placeholder</span>
                    )}
                    
                    {hasAccess ? (
                      <Link
                        to={`/learn/${module.slug || module.id}`}
                        className="px-4 py-2 theme-button-primary rounded-lg"
                      >
                        {progress > 0 ? 'Continue' : 'Start Learning'}
                      </Link>
                    ) : (
                      <Link
                        to="/pricing"
                        className={`px-4 py-2 rounded-lg flex items-center ${darkMode ? 'bg-[#252D4A] text-gray-300' : 'bg-gray-200 text-gray-700'}`}
                      >
                        <FaLock className="mr-2" /> Upgrade
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default LearningModuleList;
