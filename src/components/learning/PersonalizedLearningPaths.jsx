import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaGraduationCap, FaArrowRight, FaRegClock, FaChartLine, FaLock, FaUnlock } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useLearningProgress } from '../../contexts/LearningProgressContext';
import { SUBSCRIPTION_TIERS } from '../../config/subscriptionTiers';
import { supabase } from '../../lib/supabase';

const PersonalizedLearningPaths = () => {
  const { darkMode } = useGlobalTheme();
  const { user, profile } = useAuth();
  const { 
    progress, 
    recommendations, 
    userSkills, 
    userInterests,
    updateUserSkills,
    updateUserInterests,
    getModuleProgress,
    loading 
  } = useLearningProgress();
  
  const navigate = useNavigate();
  
  const [showSkillAssessment, setShowSkillAssessment] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [learningPaths, setLearningPaths] = useState([]);
  const [loadingPaths, setLoadingPaths] = useState(true);
  
  // Assessment questions
  const assessmentQuestions = [
    {
      id: 'experience',
      question: 'What is your experience level with cybersecurity?',
      options: [
        { id: 'beginner', text: 'Beginner - Just starting out' },
        { id: 'intermediate', text: 'Intermediate - Some experience' },
        { id: 'advanced', text: 'Advanced - Experienced professional' },
        { id: 'expert', text: 'Expert - Deep expertise' }
      ]
    },
    {
      id: 'interests',
      question: 'Which areas of cybersecurity interest you most? (Select all that apply)',
      multiSelect: true,
      options: [
        { id: 'web_security', text: 'Web Security' },
        { id: 'network_security', text: 'Network Security' },
        { id: 'cryptography', text: 'Cryptography' },
        { id: 'malware_analysis', text: 'Malware Analysis' },
        { id: 'penetration_testing', text: 'Penetration Testing' },
        { id: 'incident_response', text: 'Incident Response' }
      ]
    },
    {
      id: 'goals',
      question: 'What are your primary learning goals?',
      options: [
        { id: 'career', text: 'Career advancement or job preparation' },
        { id: 'certification', text: 'Preparing for certifications' },
        { id: 'skills', text: 'Building practical skills' },
        { id: 'knowledge', text: 'General knowledge and interest' }
      ]
    },
    {
      id: 'time',
      question: 'How much time can you dedicate to learning each week?',
      options: [
        { id: 'minimal', text: 'Less than 2 hours' },
        { id: 'moderate', text: '2-5 hours' },
        { id: 'significant', text: '5-10 hours' },
        { id: 'extensive', text: 'More than 10 hours' }
      ]
    }
  ];
  
  // Load learning paths on component mount
  useEffect(() => {
    fetchLearningPaths();
  }, []);
  
  // Check if skill assessment is needed
  useEffect(() => {
    if (!loading && !userSkills && !userInterests) {
      setShowSkillAssessment(true);
    }
  }, [loading, userSkills, userInterests]);
  
  // Fetch learning paths from database
  const fetchLearningPaths = async () => {
    try {
      setLoadingPaths(true);
      
      const { data, error } = await supabase
        .from('learning_paths')
        .select(`
          id,
          title,
          description,
          category_id,
          is_premium,
          is_business,
          category:learning_module_categories(name),
          modules:learning_path_modules(
            module_id,
            display_order,
            modules:learning_modules(
              id,
              title,
              slug,
              description,
              is_premium,
              is_business,
              estimated_time,
              difficulty:learning_module_difficulty_levels(name)
            )
          )
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Process data to make it easier to work with
      const processedPaths = data.map(path => {
        // Sort modules by display order
        const sortedModules = [...path.modules]
          .sort((a, b) => a.display_order - b.display_order)
          .map(module => module.modules);
        
        // Calculate path progress
        let totalModules = sortedModules.length;
        let completedModules = 0;
        
        sortedModules.forEach(module => {
          if (progress[module.id] >= 100) {
            completedModules++;
          }
        });
        
        const pathProgress = totalModules > 0 
          ? Math.floor((completedModules / totalModules) * 100) 
          : 0;
        
        // Calculate estimated time
        const totalTime = sortedModules.reduce((sum, module) => sum + (module.estimated_time || 0), 0);
        
        return {
          ...path,
          modules: sortedModules,
          progress: pathProgress,
          completedModules,
          totalModules,
          estimatedTime: totalTime
        };
      });
      
      setLearningPaths(processedPaths);
    } catch (error) {
      console.error('Error fetching learning paths:', error);
    } finally {
      setLoadingPaths(false);
    }
  };
  
  // Handle skill assessment submission
  const handleAssessmentSubmit = async () => {
    // Process answers into skills and interests
    const skills = {
      experience: answers.experience || 'beginner',
      goals: answers.goals || 'knowledge',
      timeCommitment: answers.time || 'moderate'
    };
    
    const interests = {
      categories: answers.interests || [],
      focusAreas: []
    };
    
    // Map selected interests to category names
    const categoryMap = {
      'web_security': 'Web Security',
      'network_security': 'Network Security',
      'cryptography': 'Cryptography',
      'malware_analysis': 'Malware Analysis',
      'penetration_testing': 'Penetration Testing',
      'incident_response': 'Incident Response'
    };
    
    interests.categories = interests.categories.map(id => categoryMap[id] || id);
    
    // Update user skills and interests
    await updateUserSkills(skills);
    await updateUserInterests(interests);
    
    // Hide assessment
    setShowSkillAssessment(false);
  };
  
  // Handle answer selection
  const handleAnswerSelect = (questionId, answerId, isMultiSelect = false) => {
    if (isMultiSelect) {
      // For multi-select questions, toggle the selected option
      const currentSelections = answers[questionId] || [];
      const newSelections = currentSelections.includes(answerId)
        ? currentSelections.filter(id => id !== answerId)
        : [...currentSelections, answerId];
      
      setAnswers({
        ...answers,
        [questionId]: newSelections
      });
    } else {
      // For single-select questions, set the selected option
      setAnswers({
        ...answers,
        [questionId]: answerId
      });
      
      // Move to next question if not the last one
      if (currentQuestion < assessmentQuestions.length - 1) {
        setCurrentQuestion(currentQuestion + 1);
      }
    }
  };
  
  // Render skill assessment
  const renderSkillAssessment = () => {
    const question = assessmentQuestions[currentQuestion];
    const isMultiSelect = question.multiSelect || false;
    const selectedAnswers = answers[question.id] || (isMultiSelect ? [] : null);
    
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
            <FaChartLine className="text-[#88cc14]" />
          </div>
          <h2 className="text-xl font-bold">Personalize Your Learning Experience</h2>
        </div>
        
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
          Answer a few questions to help us recommend the best learning paths and modules for you.
        </p>
        
        <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6`}>
          <h3 className="text-lg font-semibold mb-4">
            {question.question}
          </h3>
          
          <div className="space-y-3">
            {question.options.map(option => (
              <button
                key={option.id}
                onClick={() => handleAnswerSelect(question.id, option.id, isMultiSelect)}
                className={`w-full text-left p-3 rounded-lg border ${
                  isMultiSelect
                    ? selectedAnswers.includes(option.id)
                      ? darkMode ? 'border-[#88cc14] bg-[#88cc14]/20' : 'border-[#88cc14] bg-[#88cc14]/10'
                      : darkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-300 hover:bg-gray-200'
                    : selectedAnswers === option.id
                      ? darkMode ? 'border-[#88cc14] bg-[#88cc14]/20' : 'border-[#88cc14] bg-[#88cc14]/10'
                      : darkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-300 hover:bg-gray-200'
                } transition-colors`}
              >
                {option.text}
              </button>
            ))}
          </div>
          
          <div className="mt-6 flex justify-between items-center">
            <div className="text-sm">
              Question {currentQuestion + 1} of {assessmentQuestions.length}
            </div>
            
            <div className="flex gap-3">
              {currentQuestion > 0 && (
                <button
                  onClick={() => setCurrentQuestion(currentQuestion - 1)}
                  className={`px-4 py-2 rounded-lg ${
                    darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
                  } transition-colors`}
                >
                  Previous
                </button>
              )}
              
              {isMultiSelect || currentQuestion === assessmentQuestions.length - 1 ? (
                <button
                  onClick={currentQuestion === assessmentQuestions.length - 1 ? handleAssessmentSubmit : () => setCurrentQuestion(currentQuestion + 1)}
                  className={`px-4 py-2 rounded-lg ${
                    isMultiSelect && (!selectedAnswers || selectedAnswers.length === 0)
                      ? darkMode ? 'bg-gray-700 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                  } transition-colors`}
                  disabled={isMultiSelect && (!selectedAnswers || selectedAnswers.length === 0)}
                >
                  {currentQuestion === assessmentQuestions.length - 1 ? 'Submit' : 'Next'}
                </button>
              ) : null}
            </div>
          </div>
        </div>
        
        <div className="mt-4 text-center">
          <button
            onClick={() => setShowSkillAssessment(false)}
            className={`text-sm ${
              darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'
            }`}
          >
            Skip for now
          </button>
        </div>
      </div>
    );
  };
  
  // Render recommended modules
  const renderRecommendedModules = () => {
    if (recommendations.length === 0) {
      return null;
    }
    
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Recommended for You</h2>
          <Link to="/learn/recommendations" className="text-[#88cc14] hover:underline text-sm flex items-center">
            View all <FaArrowRight className="ml-1" />
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {recommendations.slice(0, 3).map(module => {
            const moduleProgress = getModuleProgress(module.id);
            const isAccessible = !module.is_premium || profile?.subscription_tier === 'premium' || profile?.subscription_tier === 'business';
            
            return (
              <div 
                key={module.id}
                className={`${
                  darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'
                } border rounded-lg p-4 h-full flex flex-col`}
              >
                <div className="mb-2 flex justify-between">
                  <span className={`text-xs px-2 py-1 rounded ${
                    darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'
                  }`}>
                    {module.category.name}
                  </span>
                  
                  <span className={`text-xs px-2 py-1 rounded flex items-center ${
                    module.is_premium
                      ? darkMode ? 'bg-amber-900/30 text-amber-300' : 'bg-amber-100 text-amber-800'
                      : darkMode ? 'bg-green-900/30 text-green-300' : 'bg-green-100 text-green-800'
                  }`}>
                    {module.is_premium ? <FaLock className="mr-1" /> : <FaUnlock className="mr-1" />}
                    {module.is_premium ? 'Premium' : 'Free'}
                  </span>
                </div>
                
                <h3 className="text-lg font-semibold mb-2">{module.title}</h3>
                
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 text-sm flex-grow`}>
                  {module.description.length > 100 
                    ? `${module.description.substring(0, 100)}...` 
                    : module.description}
                </p>
                
                <div className="mt-auto">
                  <div className="flex justify-between items-center text-xs mb-2">
                    <span className={`px-2 py-1 rounded ${
                      darkMode ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-800'
                    }`}>
                      {module.difficulty.name}
                    </span>
                    
                    <div className="flex items-center">
                      <FaRegClock className="mr-1" />
                      <span>{module.estimated_time} min</span>
                    </div>
                  </div>
                  
                  {/* Progress bar if started */}
                  {moduleProgress > 0 && (
                    <div className="mb-3">
                      <div className="flex justify-between text-xs mb-1">
                        <span>{moduleProgress === 100 ? 'Completed' : 'In Progress'}</span>
                        <span>{moduleProgress}%</span>
                      </div>
                      <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                        <div
                          className={`h-2 rounded-full ${moduleProgress === 100 ? 'bg-green-500' : 'bg-[#88cc14]'}`}
                          style={{ width: `${moduleProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                  
                  <Link
                    to={isAccessible ? `/learn/${module.slug || module.id}` : '/pricing'}
                    className={`w-full py-2 text-center block rounded-lg transition-colors ${
                      isAccessible
                        ? 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                        : darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-300 text-gray-700'
                    }`}
                  >
                    {!isAccessible ? (
                      <>Upgrade to Access</>
                    ) : moduleProgress > 0 ? (
                      moduleProgress === 100 ? 'Review Module' : 'Continue Learning'
                    ) : (
                      'Start Learning'
                    )}
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };
  
  // Render learning paths
  const renderLearningPaths = () => {
    if (loadingPaths) {
      return (
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#88cc14]"></div>
          </div>
        </div>
      );
    }
    
    if (learningPaths.length === 0) {
      return null;
    }
    
    // Filter paths based on user interests if available
    let filteredPaths = learningPaths;
    
    if (userInterests && userInterests.categories && userInterests.categories.length > 0) {
      // Prioritize paths matching user interests
      filteredPaths = learningPaths.sort((a, b) => {
        const aMatchesInterest = userInterests.categories.includes(a.category.name);
        const bMatchesInterest = userInterests.categories.includes(b.category.name);
        
        if (aMatchesInterest && !bMatchesInterest) return -1;
        if (!aMatchesInterest && bMatchesInterest) return 1;
        return 0;
      });
    }
    
    // Get paths with some progress first
    const inProgressPaths = filteredPaths.filter(path => path.progress > 0 && path.progress < 100);
    const otherPaths = filteredPaths.filter(path => path.progress === 0 || path.progress === 100);
    
    // Combine with in-progress paths first
    filteredPaths = [...inProgressPaths, ...otherPaths];
    
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Learning Paths</h2>
          <Link to="/learn/paths" className="text-[#88cc14] hover:underline text-sm flex items-center">
            View all <FaArrowRight className="ml-1" />
          </Link>
        </div>
        
        <div className="space-y-4">
          {filteredPaths.slice(0, 3).map(path => {
            const isAccessible = !path.is_premium || profile?.subscription_tier === 'premium' || profile?.subscription_tier === 'business';
            
            return (
              <div 
                key={path.id}
                className={`${
                  darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'
                } border rounded-lg p-4`}
              >
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                  <div className="flex-grow">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold">{path.title}</h3>
                      {path.is_premium && (
                        <span className={`text-xs px-2 py-1 rounded flex items-center ${
                          darkMode ? 'bg-amber-900/30 text-amber-300' : 'bg-amber-100 text-amber-800'
                        }`}>
                          <FaLock className="mr-1" /> Premium
                        </span>
                      )}
                    </div>
                    
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm mb-2`}>
                      {path.description.length > 120 
                        ? `${path.description.substring(0, 120)}...` 
                        : path.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-3 text-xs mb-2">
                      <span className="flex items-center">
                        <FaGraduationCap className="mr-1" /> {path.totalModules} modules
                      </span>
                      <span className="flex items-center">
                        <FaRegClock className="mr-1" /> {Math.ceil(path.estimatedTime / 60)} hours
                      </span>
                      <span className={`px-2 py-1 rounded ${
                        darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'
                      }`}>
                        {path.category.name}
                      </span>
                    </div>
                    
                    {/* Progress bar */}
                    {path.progress > 0 && (
                      <div className="mb-2">
                        <div className="flex justify-between text-xs mb-1">
                          <span>{path.progress === 100 ? 'Completed' : 'In Progress'}</span>
                          <span>{path.completedModules}/{path.totalModules} modules</span>
                        </div>
                        <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                          <div
                            className={`h-2 rounded-full ${path.progress === 100 ? 'bg-green-500' : 'bg-[#88cc14]'}`}
                            style={{ width: `${path.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <Link
                    to={isAccessible ? `/learn/path/${path.id}` : '/pricing'}
                    className={`whitespace-nowrap px-4 py-2 rounded-lg transition-colors ${
                      isAccessible
                        ? 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                        : darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-300 text-gray-700'
                    }`}
                  >
                    {!isAccessible ? 'Upgrade to Access' : (
                      path.progress > 0 ? (
                        path.progress === 100 ? 'Review Path' : 'Continue Path'
                      ) : (
                        'Start Path'
                      )
                    )}
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };
  
  // Render next steps
  const renderNextSteps = () => {
    // Find the next recommended module or path
    const nextModule = recommendations[0];
    
    // Find in-progress modules and paths
    const inProgressModuleIds = Object.entries(progress)
      .filter(([_, value]) => value > 0 && value < 100)
      .map(([key, _]) => key);
    
    // Find a module that's in progress
    const inProgressModule = recommendations.find(module => inProgressModuleIds.includes(module.id));
    
    // Find an in-progress path
    const inProgressPath = learningPaths.find(path => path.progress > 0 && path.progress < 100);
    
    // Determine what to show as next step
    let nextStep = null;
    
    if (inProgressModule) {
      // Prioritize in-progress module
      nextStep = {
        type: 'module',
        data: inProgressModule,
        progress: progress[inProgressModule.id] || 0
      };
    } else if (inProgressPath) {
      // Then in-progress path
      nextStep = {
        type: 'path',
        data: inProgressPath,
        progress: inProgressPath.progress
      };
    } else if (nextModule) {
      // Then recommended module
      nextStep = {
        type: 'module',
        data: nextModule,
        progress: 0
      };
    }
    
    if (!nextStep) {
      return null;
    }
    
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <h2 className="text-xl font-bold mb-4">Continue Your Learning Journey</h2>
        
        <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6`}>
          <div className="flex flex-col md:flex-row items-center gap-6">
            <div className="w-16 h-16 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
              <FaGraduationCap className="text-[#88cc14] text-2xl" />
            </div>
            
            <div className="flex-grow">
              <h3 className="text-lg font-bold mb-1">
                {nextStep.progress > 0 ? 'Continue where you left off' : 'Recommended next step'}
              </h3>
              
              <p className="text-lg mb-2">
                {nextStep.type === 'module' ? nextStep.data.title : nextStep.data.title}
              </p>
              
              {nextStep.progress > 0 && (
                <div className="mb-3">
                  <div className="flex justify-between text-xs mb-1">
                    <span>In Progress</span>
                    <span>{nextStep.progress}%</span>
                  </div>
                  <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                    <div
                      className="h-2 rounded-full bg-[#88cc14]"
                      style={{ width: `${nextStep.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
            
            <Link
              to={nextStep.type === 'module' 
                ? `/learn/${nextStep.data.slug || nextStep.data.id}` 
                : `/learn/path/${nextStep.data.id}`}
              className="px-6 py-3 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors whitespace-nowrap"
            >
              {nextStep.progress > 0 ? 'Continue Learning' : 'Start Learning'}
            </Link>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div>
      {/* Skill Assessment */}
      {showSkillAssessment && renderSkillAssessment()}
      
      {/* Next Steps */}
      {!showSkillAssessment && renderNextSteps()}
      
      {/* Recommended Modules */}
      {!showSkillAssessment && renderRecommendedModules()}
      
      {/* Learning Paths */}
      {!showSkillAssessment && renderLearningPaths()}
    </div>
  );
};

export default PersonalizedLearningPaths;
