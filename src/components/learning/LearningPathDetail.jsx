import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaGraduationCap, FaRegClock, FaCheckCircle, FaLock, FaUnlock, FaExclamationTriangle } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useLearningProgress } from '../../contexts/LearningProgressContext';
import { supabase } from '../../lib/supabase';

const LearningPathDetail = () => {
  const { darkMode } = useGlobalTheme();
  const { user, profile } = useAuth();
  const { getModuleProgress, isModuleCompleted } = useLearningProgress();
  const { pathId } = useParams();
  const navigate = useNavigate();
  
  const [path, setPath] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pathProgress, setPathProgress] = useState(0);
  
  // Fetch path data on component mount
  useEffect(() => {
    fetchPathData();
  }, [pathId]);
  
  // Calculate path progress whenever module progress changes
  useEffect(() => {
    if (path) {
      calculatePathProgress();
    }
  }, [path]);
  
  // Fetch path data from database
  const fetchPathData = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('learning_paths')
        .select(`
          id,
          title,
          description,
          category_id,
          is_premium,
          is_business,
          category:learning_module_categories(name),
          modules:learning_path_modules(
            module_id,
            display_order,
            modules:learning_modules(
              id,
              title,
              slug,
              description,
              is_premium,
              is_business,
              estimated_time,
              difficulty:learning_module_difficulty_levels(name)
            )
          )
        `)
        .eq('id', pathId)
        .single();
      
      if (error) throw error;
      
      // Process modules
      const sortedModules = [...data.modules]
        .sort((a, b) => a.display_order - b.display_order)
        .map(module => module.modules);
      
      setPath({
        ...data,
        modules: sortedModules
      });
      
      calculatePathProgress(sortedModules);
    } catch (error) {
      console.error('Error fetching path data:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };
  
  // Calculate path progress
  const calculatePathProgress = (modules = path?.modules) => {
    if (!modules) return;
    
    let totalModules = modules.length;
    let completedModules = 0;
    let totalProgress = 0;
    
    modules.forEach(module => {
      const moduleProgress = getModuleProgress(module.id);
      totalProgress += moduleProgress;
      
      if (moduleProgress >= 100) {
        completedModules++;
      }
    });
    
    const avgProgress = totalModules > 0 ? Math.floor(totalProgress / totalModules) : 0;
    
    setPathProgress({
      percentage: avgProgress,
      completedModules,
      totalModules
    });
  };
  
  // Check if user has access to the path
  const hasAccess = () => {
    if (!path) return false;
    
    if (!path.is_premium && !path.is_business) {
      return true;
    }
    
    if (path.is_premium && profile?.subscription_tier === 'free') {
      return false;
    }
    
    if (path.is_business && profile?.subscription_tier !== 'business') {
      return false;
    }
    
    return true;
  };
  
  // Render loading state
  if (loading) {
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 flex justify-center items-center h-64`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"></div>
      </div>
    );
  }
  
  // Render error state
  if (error || !path) {
    return (
      <div>
        <div className="mb-6">
          <Link to="/learn" className="flex items-center text-[#88cc14] hover:underline">
            <FaArrowLeft className="mr-2" /> Back to Learning
          </Link>
        </div>
        
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
          <FaExclamationTriangle className="mx-auto text-4xl mb-4 text-yellow-500" />
          <h2 className="text-xl font-bold mb-2">Learning Path Not Found</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            The learning path you're looking for doesn't exist or you don't have access to it.
          </p>
          <Link
            to="/learn"
            className="mt-4 px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors inline-block"
          >
            Browse Learning Content
          </Link>
        </div>
      </div>
    );
  }
  
  // Render premium content banner if user doesn't have access
  if (!hasAccess()) {
    return (
      <div>
        <div className="mb-6">
          <Link to="/learn" className="flex items-center text-[#88cc14] hover:underline">
            <FaArrowLeft className="mr-2" /> Back to Learning
          </Link>
        </div>
        
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
          <h1 className="text-2xl font-bold mb-4">{path.title}</h1>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
            {path.description}
          </p>
          
          <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6 text-center mb-6`}>
            <FaLock className="mx-auto text-4xl mb-4 text-[#88cc14]" />
            <h2 className="text-xl font-bold mb-2">Premium Content</h2>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
              This learning path is available to {path.is_business ? 'Business' : 'Premium'} subscribers only.
            </p>
            <Link
              to="/pricing"
              className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors inline-block"
            >
              Upgrade Now
            </Link>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div>
      <div className="mb-6">
        <Link to="/learn" className="flex items-center text-[#88cc14] hover:underline">
          <FaArrowLeft className="mr-2" /> Back to Learning
        </Link>
      </div>
      
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <div className="flex justify-between items-start mb-4">
          <h1 className="text-2xl font-bold">{path.title}</h1>
          <span className={`px-2 py-1 rounded text-xs flex items-center ${
            path.is_premium
              ? darkMode ? 'bg-amber-900/30 text-amber-300' : 'bg-amber-100 text-amber-800'
              : darkMode ? 'bg-green-900/30 text-green-300' : 'bg-green-100 text-green-800'
          }`}>
            {path.is_premium ? <FaLock className="mr-1" /> : <FaUnlock className="mr-1" />}
            {path.is_premium ? 'Premium' : 'Free'}
          </span>
        </div>
        
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
          {path.description}
        </p>
        
        <div className="flex flex-wrap gap-3 mb-6">
          <span className={`px-2 py-1 rounded text-xs flex items-center ${
            darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'
          }`}>
            {path.category.name}
          </span>
          
          <span className="flex items-center text-sm">
            <FaGraduationCap className="mr-1" /> {path.modules.length} modules
          </span>
          
          <span className="flex items-center text-sm">
            <FaRegClock className="mr-1" /> {Math.ceil(path.modules.reduce((sum, module) => sum + (module.estimated_time || 0), 0) / 60)} hours
          </span>
        </div>
        
        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm mb-1">
            <span>
              {pathProgress.percentage === 0 ? 'Not Started' : 
               pathProgress.percentage === 100 ? 'Completed' : 'In Progress'}
            </span>
            <span>{pathProgress.completedModules}/{pathProgress.totalModules} modules completed</span>
          </div>
          <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
            <div
              className={`h-2 rounded-full ${pathProgress.percentage === 100 ? 'bg-green-500' : 'bg-[#88cc14]'}`}
              style={{ width: `${pathProgress.percentage}%` }}
            ></div>
          </div>
        </div>
        
        {/* Module List */}
        <h2 className="text-xl font-semibold mb-4">Modules in this Path</h2>
        
        <div className="space-y-4">
          {path.modules.map((module, index) => {
            const moduleProgress = getModuleProgress(module.id);
            const isCompleted = isModuleCompleted(module.id);
            
            return (
              <div 
                key={module.id}
                className={`${
                  darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'
                } border rounded-lg p-4 ${
                  isCompleted ? darkMode ? 'border-green-500/50' : 'border-green-500/30' : ''
                }`}
              >
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                  <div className="flex-grow">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`w-8 h-8 flex items-center justify-center rounded-full ${
                        isCompleted
                          ? 'bg-green-500 text-white'
                          : darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-300 text-gray-700'
                      }`}>
                        {isCompleted ? <FaCheckCircle /> : index + 1}
                      </span>
                      
                      <h3 className="font-semibold">{module.title}</h3>
                      
                      {module.is_premium && !path.is_premium && (
                        <span className={`text-xs px-2 py-1 rounded flex items-center ${
                          darkMode ? 'bg-amber-900/30 text-amber-300' : 'bg-amber-100 text-amber-800'
                        }`}>
                          <FaLock className="mr-1" /> Premium
                        </span>
                      )}
                    </div>
                    
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm mb-2 ml-10`}>
                      {module.description.length > 100 
                        ? `${module.description.substring(0, 100)}...` 
                        : module.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-3 text-xs mb-2 ml-10">
                      <span className={`px-2 py-1 rounded ${
                        darkMode ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-800'
                      }`}>
                        {module.difficulty.name}
                      </span>
                      
                      <span className="flex items-center">
                        <FaRegClock className="mr-1" /> {module.estimated_time} min
                      </span>
                    </div>
                    
                    {/* Progress bar */}
                    {moduleProgress > 0 && (
                      <div className="mb-2 ml-10">
                        <div className="flex justify-between text-xs mb-1">
                          <span>{moduleProgress === 100 ? 'Completed' : 'In Progress'}</span>
                          <span>{moduleProgress}%</span>
                        </div>
                        <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                          <div
                            className={`h-2 rounded-full ${moduleProgress === 100 ? 'bg-green-500' : 'bg-[#88cc14]'}`}
                            style={{ width: `${moduleProgress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <Link
                    to={`/learn/${module.slug || module.id}`}
                    className={`whitespace-nowrap px-4 py-2 rounded-lg transition-colors ${
                      isCompleted
                        ? darkMode ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-green-500 hover:bg-green-600 text-white'
                        : moduleProgress > 0
                          ? 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                          : darkMode ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-500 hover:bg-blue-600 text-white'
                    }`}
                  >
                    {isCompleted ? 'Review' : moduleProgress > 0 ? 'Continue' : 'Start'}
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default LearningPathDetail;
