import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaLock, FaUnlock, FaCrown, FaBuilding, FaChevronRight, FaChevronDown, FaChevronLeft, FaBars, FaTimes } from 'react-icons/fa';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useAuth } from '../../contexts/AuthContext';
import { SUBSCRIPTION_TIERS } from '../../config/subscriptionTiers';
import UpgradeBanner from '../access/UpgradeBanner';
import LockedItem from '../access/LockedItem';
import { learningPaths } from '../../data/learningPaths';
import { getContent } from '../../utils/contentLoader';

// Import visualization components
import OSLayersVisualization from '../visualizations/OSLayersVisualization';
import OSTypesVisualization from '../visualizations/OSTypesVisualization';
import LabTerminal from '../LabTerminal';
import KnowledgeCheck from '../KnowledgeCheck';

// Map of visualization components
const visualizationComponents = {
  OSLayersVisualization,
  OSTypesVisualization
};

/**
 * GlobalLearningModule Component
 *
 * A unified learning interface that adapts based on user subscription level.
 * Provides appropriate access controls and upgrade prompts for different content tiers.
 */
const GlobalLearningModule = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { subscriptionLevel, hasAccess, getRemainingContent } = useSubscription();

  // State for learning navigation
  const [selectedPath, setSelectedPath] = useState('fundamentals');
  const [selectedModule, setSelectedModule] = useState('os-concepts');
  const [selectedTopic, setSelectedTopic] = useState('os-introduction');
  const [expandedPaths, setExpandedPaths] = useState(['fundamentals']);
  const [expandedModules, setExpandedModules] = useState(['os-concepts']);
  const [isSidebarOpen, setSidebarOpen] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [labStarted, setLabStarted] = useState(false);

  // Check if user is logged in
  const isLoggedIn = !!user;

  // Get content for the selected topic
  const content = getContent(selectedTopic);

  // Get current path, module and topic information
  const currentPath = learningPaths.find(p => p.id === selectedPath);
  const currentModule = currentPath?.modules.find(m => m.id === selectedModule);
  const currentTopic = currentModule?.topics.find(t => t.id === selectedTopic);

  // Check if the current module is accessible based on subscription
  const [isModuleAccessible, setIsModuleAccessible] = useState(true);
  const [remainingModules, setRemainingModules] = useState(0);

  useEffect(() => {
    // Check if the current module is accessible
    if (currentModule) {
      // If user is not logged in, only free content is accessible
      if (!isLoggedIn) {
        setIsModuleAccessible(currentModule.tier === SUBSCRIPTION_TIERS.FREE);
        setRemainingModules(0);
      } else {
        // For logged in users, check access based on subscription
        const moduleAccess = hasAccess('learnModules', currentModule.tier || 'free');
        setIsModuleAccessible(moduleAccess);

        // Get remaining accessible modules
        const { remaining } = getRemainingContent('learnModules');
        setRemainingModules(remaining);
      }
    }
  }, [currentModule, subscriptionLevel, hasAccess, getRemainingContent, isLoggedIn]);

  // Toggle path expansion
  const togglePath = (pathId) => {
    setExpandedPaths(prev =>
      prev.includes(pathId)
        ? prev.filter(id => id !== pathId)
        : [...prev, pathId]
    );
  };

  // Toggle module expansion
  const toggleModule = (moduleId) => {
    setExpandedModules(prev =>
      prev.includes(moduleId)
        ? prev.filter(id => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  // Select a topic
  const selectTopic = (pathId, moduleId, topicId) => {
    setSelectedPath(pathId);
    setSelectedModule(moduleId);
    setSelectedTopic(topicId);

    // Ensure path and module are expanded
    if (!expandedPaths.includes(pathId)) {
      setExpandedPaths([...expandedPaths, pathId]);
    }

    if (!expandedModules.includes(moduleId)) {
      setExpandedModules([...expandedModules, moduleId]);
    }

    // Close mobile menu if open
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Get subscription tier icon
  const getTierIcon = (tier) => {
    switch(tier) {
      case SUBSCRIPTION_TIERS.PREMIUM:
        return <FaCrown className="text-yellow-400" />;
      case SUBSCRIPTION_TIERS.BUSINESS:
        return <FaBuilding className="text-blue-400" />;
      default:
        return null;
    }
  };

  // Render tier badge for modules and topics
  const renderTierBadge = (tier) => {
    if (!tier || tier === SUBSCRIPTION_TIERS.FREE) return null;

    const icon = getTierIcon(tier);
    const isAccessible = hasAccess('learnModules', tier);

    return (
      <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
        isAccessible
          ? tier === SUBSCRIPTION_TIERS.PREMIUM
            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
          : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
      }`}>
        {icon}
        <span className="ml-1">{tier.charAt(0).toUpperCase() + tier.slice(1)}</span>
        {!isAccessible && <FaLock className="ml-1 text-xs" />}
      </span>
    );
  };

  // Render the navigation sidebar
  const renderSidebar = () => (
    <div className={`bg-gray-100 dark:bg-gray-800 overflow-y-auto ${
      isSidebarOpen ? 'w-64 flex-shrink-0' : 'w-0'
    } transition-all duration-300 ease-in-out`}>
      {isSidebarOpen && (
        <div className="p-4">
          <h2 className="text-xl font-bold mb-4 text-gray-800 dark:text-white">Learning Paths</h2>

          <div className="space-y-2">
            {learningPaths.map(path => (
              <div key={path.id} className="rounded-lg overflow-hidden">
                <button
                  className={`w-full flex items-center justify-between p-3 text-left font-medium rounded-lg ${
                    selectedPath === path.id
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                      : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'
                  }`}
                  onClick={() => togglePath(path.id)}
                >
                  <span className="truncate">{path.title}</span>
                  {expandedPaths.includes(path.id) ? (
                    <FaChevronDown className="flex-shrink-0" />
                  ) : (
                    <FaChevronRight className="flex-shrink-0" />
                  )}
                </button>

                {expandedPaths.includes(path.id) && (
                  <div className="mt-1 ml-2 pl-2 border-l-2 border-gray-300 dark:border-gray-600">
                    {path.modules.map(module => (
                      <div key={module.id} className="mt-1">
                        <button
                          className={`w-full flex items-center justify-between p-2 text-left text-sm font-medium rounded-lg ${
                            selectedModule === module.id
                              ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-200'
                              : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700/50'
                          }`}
                          onClick={() => toggleModule(module.id)}
                        >
                          <span className="flex items-center truncate">
                            {module.title}
                            {renderTierBadge(module.tier)}
                          </span>
                          {expandedModules.includes(module.id) ? (
                            <FaChevronDown className="flex-shrink-0" />
                          ) : (
                            <FaChevronRight className="flex-shrink-0" />
                          )}
                        </button>

                        {expandedModules.includes(module.id) && (
                          <div className="mt-1 ml-2 pl-2 border-l-2 border-gray-200 dark:border-gray-700">
                            {module.topics.map(topic => (
                              <button
                                key={topic.id}
                                className={`w-full flex items-center justify-between p-2 text-left text-xs rounded-lg ${
                                  selectedTopic === topic.id
                                    ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-200'
                                    : 'text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-700/30'
                                }`}
                                onClick={() => selectTopic(path.id, module.id, topic.id)}
                              >
                                <span className="flex items-center truncate">
                                  {topic.title}
                                  {renderTierBadge(topic.tier)}
                                </span>
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  // Render content area
  const renderContent = () => {
    if (!isModuleAccessible) {
      return (
        <LockedItem
          title={currentModule?.title || 'Premium Module'}
          description={isLoggedIn ?
            "This module is only available with a higher subscription tier." :
            "Sign in to access this premium module."}
          tier={currentModule?.tier || SUBSCRIPTION_TIERS.PREMIUM}
          actionText={isLoggedIn ? "Upgrade to Access" : "Sign In"}
          onAction={() => navigate(isLoggedIn ? '/pricing' : '/login')}
        />
      );
    }

    if (!content) {
      return (
        <div className="flex items-center justify-center h-full">
          <p className="text-gray-500 dark:text-gray-400">Select a topic to start learning</p>
        </div>
      );
    }

    return (
      <div className="prose prose-lg dark:prose-invert max-w-none">
        <h1>{content.title}</h1>

        {content.sections.map((section, index) => (
          <div key={index} className="mb-8">
            {section.title && <h2>{section.title}</h2>}

            {section.content.map((item, i) => {
              if (item.type === 'text') {
                return <p key={i} dangerouslySetInnerHTML={{ __html: item.value }} />;
              } else if (item.type === 'code') {
                return (
                  <pre key={i} className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto">
                    <code>{item.value}</code>
                  </pre>
                );
              } else if (item.type === 'image') {
                return (
                  <div key={i} className="my-4">
                    <img
                      src={item.src}
                      alt={item.alt || 'Learning content image'}
                      className="rounded-lg max-w-full h-auto"
                    />
                    {item.caption && (
                      <p className="text-sm text-center text-gray-500 dark:text-gray-400 mt-2">
                        {item.caption}
                      </p>
                    )}
                  </div>
                );
              } else if (item.type === 'visualization') {
                const VisualizationComponent = visualizationComponents[item.component];
                return VisualizationComponent ? (
                  <div key={i} className="my-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <VisualizationComponent {...item.props} />
                  </div>
                ) : null;
              } else if (item.type === 'lab') {
                return (
                  <div key={i} className="my-6">
                    {!labStarted ? (
                      <button
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        onClick={() => setLabStarted(true)}
                      >
                        Start Lab Exercise
                      </button>
                    ) : (
                      <div className="border rounded-lg overflow-hidden">
                        <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 flex justify-between items-center">
                          <h3 className="text-lg font-medium">Lab: {item.title}</h3>
                          <button
                            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                            onClick={() => setLabStarted(false)}
                          >
                            Close Lab
                          </button>
                        </div>
                        <div className="p-4">
                          <LabTerminal commands={item.commands} />
                        </div>
                      </div>
                    )}
                  </div>
                );
              } else if (item.type === 'quiz') {
                return (
                  <div key={i} className="my-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <KnowledgeCheck questions={item.questions} />
                  </div>
                );
              }
              return null;
            })}
          </div>
        ))}

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
          {renderPreviousButton()}
          {renderNextButton()}
        </div>
      </div>
    );
  };

  // Render previous button
  const renderPreviousButton = () => {
    if (!currentModule || !currentTopic) return <div></div>;

    const topicIndex = currentModule.topics.findIndex(t => t.id === selectedTopic);

    if (topicIndex > 0) {
      // Previous topic in same module
      const prevTopic = currentModule.topics[topicIndex - 1];
      return (
        <button
          className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          onClick={() => selectTopic(selectedPath, selectedModule, prevTopic.id)}
        >
          <FaChevronLeft className="mr-2" />
          Previous: {prevTopic.title}
        </button>
      );
    }

    // Check for previous module
    const moduleIndex = currentPath.modules.findIndex(m => m.id === selectedModule);
    if (moduleIndex > 0) {
      const prevModule = currentPath.modules[moduleIndex - 1];
      const lastTopic = prevModule.topics[prevModule.topics.length - 1];

      // Check if previous module is accessible
      const isPrevModuleAccessible = hasAccess('learnModules', prevModule.tier || 'free');

      return (
        <button
          className={`flex items-center ${
            isPrevModuleAccessible
              ? 'text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
              : 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
          }`}
          onClick={() => {
            if (isPrevModuleAccessible) {
              selectTopic(selectedPath, prevModule.id, lastTopic.id);
            }
          }}
        >
          <FaChevronLeft className="mr-2" />
          Previous: {prevModule.title}
          {!isPrevModuleAccessible && <FaLock className="ml-2" />}
        </button>
      );
    }

    return <div></div>;
  };

  // Render next button
  const renderNextButton = () => {
    if (!currentModule || !currentTopic) return <div></div>;

    const topicIndex = currentModule.topics.findIndex(t => t.id === selectedTopic);

    if (topicIndex < currentModule.topics.length - 1) {
      // Next topic in same module
      const nextTopic = currentModule.topics[topicIndex + 1];
      return (
        <button
          className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          onClick={() => selectTopic(selectedPath, selectedModule, nextTopic.id)}
        >
          Next: {nextTopic.title}
          <FaChevronRight className="ml-2" />
        </button>
      );
    }

    // Check for next module
    const moduleIndex = currentPath.modules.findIndex(m => m.id === selectedModule);
    if (moduleIndex < currentPath.modules.length - 1) {
      const nextModule = currentPath.modules[moduleIndex + 1];
      const firstTopic = nextModule.topics[0];

      // Check if next module is accessible
      const isNextModuleAccessible = hasAccess('learnModules', nextModule.tier || 'free');

      return (
        <button
          className={`flex items-center ${
            isNextModuleAccessible
              ? 'text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
              : 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
          }`}
          onClick={() => {
            if (isNextModuleAccessible) {
              selectTopic(selectedPath, nextModule.id, firstTopic.id);
            }
          }}
        >
          Next: {nextModule.title}
          <FaChevronRight className="ml-2" />
          {!isNextModuleAccessible && <FaLock className="ml-2" />}
        </button>
      );
    }

    return <div></div>;
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-900">
      {/* Mobile header */}
      <div className="md:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold text-gray-800 dark:text-white">Learning</h1>
          <button
            className="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700"
            onClick={toggleMobileMenu}
          >
            {isMobileMenuOpen ? <FaTimes /> : <FaBars />}
          </button>
        </div>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-4 overflow-hidden"
          >
            {renderSidebar()}
          </motion.div>
        )}
      </div>

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - hidden on mobile */}
        <div className="hidden md:block">
          {renderSidebar()}
        </div>

        {/* Toggle sidebar button */}
        <button
          className="hidden md:flex items-center justify-center w-6 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors"
          onClick={toggleSidebar}
        >
          {isSidebarOpen ? <FaChevronLeft /> : <FaChevronRight />}
        </button>

        {/* Content area */}
        <div className="flex-1 overflow-auto p-4 md:p-8">
          {/* Subscription banner */}
          {subscriptionLevel === SUBSCRIPTION_TIERS.FREE && (
            <div className="mb-6">
              <UpgradeBanner
                title="Upgrade to Access All Learning Modules"
                description={`You have access to ${remainingModules} more modules with your current plan.`}
                buttonText="Upgrade Now"
                onButtonClick={() => navigate('/pricing')}
              />
            </div>
          )}

          {/* Content */}
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default GlobalLearningModule;
