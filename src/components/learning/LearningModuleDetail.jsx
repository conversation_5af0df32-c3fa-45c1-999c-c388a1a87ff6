import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { FaArrow<PERSON>eft, FaSpinner, FaClock, FaStar, FaLock, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';
import { useLearningModule } from '../../contexts/LearningModuleContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import SimpleUpgradeBanner from '../access/SimpleUpgradeBanner';

const LearningModuleDetail = () => {
  const { darkMode } = useGlobalTheme();
  const { user, profile } = useAuth();
  const { 
    getModuleById, 
    updateModuleProgress, 
    rateModule,
    isModuleCompleted,
    getModuleProgress,
    loading: contextLoading, 
    error: contextError 
  } = useLearningModule();
  const { moduleId } = useParams();
  const navigate = useNavigate();
  
  const [module, setModule] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userRating, setUserRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [submittingRating, setSubmittingRating] = useState(false);
  const [currentSection, setCurrentSection] = useState(0);
  const [progress, setProgress] = useState(0);
  const [completed, setCompleted] = useState(false);

  // Fetch module data
  useEffect(() => {
    const fetchModule = async () => {
      try {
        setLoading(true);
        const moduleData = await getModuleById(moduleId);
        setModule(moduleData);
        
        // Set progress and completion status
        setProgress(getModuleProgress(moduleData.id));
        setCompleted(isModuleCompleted(moduleData.id));
      } catch (error) {
        console.error('Error fetching module:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchModule();
  }, [moduleId, getModuleById, getModuleProgress, isModuleCompleted]);

  // Handle module progress update
  const handleProgressUpdate = async (newProgress, isCompleted = false) => {
    try {
      if (!user) {
        navigate('/login', { state: { from: `/learn/${moduleId}` } });
        return;
      }
      
      await updateModuleProgress(module.id, newProgress, isCompleted);
      setProgress(newProgress);
      setCompleted(isCompleted);
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  // Handle rating submission
  const handleRatingSubmit = async () => {
    try {
      if (!user) {
        navigate('/login', { state: { from: `/learn/${moduleId}` } });
        return;
      }
      
      if (userRating === 0) return;
      
      setSubmittingRating(true);
      await rateModule(module.id, userRating, feedback);
      setSubmittingRating(false);
    } catch (error) {
      console.error('Error submitting rating:', error);
      setSubmittingRating(false);
    }
  };

  // Format time (minutes to hours and minutes)
  const formatTime = (minutes) => {
    if (!minutes) return 'N/A';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours === 0) return `${mins} min`;
    if (mins === 0) return `${hours} hr`;
    return `${hours} hr ${mins} min`;
  };

  // Check if user has access to this module
  const hasAccess = () => {
    if (!module) return false;
    if (!module.is_premium && !module.is_business) return true;
    if (module.is_premium && !module.is_business && profile?.subscription_tier === 'premium') return true;
    if (profile?.subscription_tier === 'business') return true;
    return false;
  };

  // Render loading state
  if (loading || contextLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <FaSpinner className="animate-spin text-4xl text-[#88cc14]" />
      </div>
    );
  }

  // Render error state
  if (error || contextError) {
    return (
      <div className={`${darkMode ? 'bg-red-900/20 border-red-900/30' : 'bg-red-100 border-red-200'} border rounded-lg p-6 text-center`}>
        <h2 className="text-2xl font-bold text-red-500 mb-4">Error</h2>
        <p className={`${darkMode ? 'text-red-300' : 'text-red-700'}`}>{error || contextError}</p>
        <button 
          onClick={() => navigate('/learn')}
          className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
        >
          Back to Modules
        </button>
      </div>
    );
  }

  // Render module not found
  if (!module) {
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
        <FaExclamationTriangle className="mx-auto text-4xl mb-4 text-yellow-500" />
        <h2 className="text-xl font-bold mb-2">Module Not Found</h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          The module you're looking for doesn't exist or you don't have access to it.
        </p>
        <Link
          to="/learn"
          className="mt-4 px-4 py-2 theme-button-primary rounded-lg inline-block"
        >
          Browse Modules
        </Link>
      </div>
    );
  }

  // Render premium content banner if user doesn't have access
  if (!hasAccess()) {
    return (
      <div>
        <div className="mb-6">
          <Link to="/learn" className="flex items-center text-[#88cc14] hover:underline">
            <FaArrowLeft className="mr-2" /> Back to Modules
          </Link>
        </div>
        
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
          <h1 className="text-2xl font-bold mb-4">{module.title}</h1>
          
          <div className="flex flex-wrap gap-2 mb-4">
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
              {module.category.name}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
              {module.difficulty.name}
            </span>
            {module.estimated_time && (
              <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
                <FaClock className="mr-1" /> {formatTime(module.estimated_time)}
              </span>
            )}
            {module.is_business && (
              <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                Business
              </span>
            )}
            {module.is_premium && !module.is_business && (
              <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                Premium
              </span>
            )}
          </div>
          
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
            {module.description}
          </p>
          
          <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6 text-center mb-6`}>
            <FaLock className="mx-auto text-4xl mb-4 text-[#88cc14]" />
            <h2 className="text-xl font-bold mb-2">Premium Content</h2>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
              This module is available to {module.is_business ? 'Business' : 'Premium'} subscribers only.
            </p>
            <Link
              to="/pricing"
              className="px-4 py-2 theme-button-primary rounded-lg inline-block"
            >
              Upgrade Now
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Parse content
  const content = module.content?.[0]?.content || {};
  
  // Placeholder for module content
  // In a real implementation, this would render the actual content from the database
  return (
    <div>
      <div className="mb-6">
        <Link to="/learn" className="flex items-center text-[#88cc14] hover:underline">
          <FaArrowLeft className="mr-2" /> Back to Modules
        </Link>
      </div>
      
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
        <h1 className="text-2xl font-bold mb-4">{module.title}</h1>
        
        <div className="flex flex-wrap gap-2 mb-4">
          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
            {module.category.name}
          </span>
          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
            {module.difficulty.name}
          </span>
          {module.estimated_time && (
            <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
              <FaClock className="mr-1" /> {formatTime(module.estimated_time)}
            </span>
          )}
          {module.is_business && (
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
              Business
            </span>
          )}
          {module.is_premium && !module.is_business && (
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
              Premium
            </span>
          )}
        </div>
        
        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm mb-1">
            <span>{completed ? 'Completed' : progress > 0 ? 'In Progress' : 'Not Started'}</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
            <div 
              className="h-2 rounded-full bg-[#88cc14]" 
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
        
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
          {module.description}
        </p>
        
        {/* Module Content */}
        <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6 mb-6`}>
          <h2 className="text-xl font-bold mb-4">Module Content</h2>
          
          {/* Introduction */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Introduction</h3>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {content.introduction || 'Introduction content will be displayed here.'}
            </p>
          </div>
          
          {/* Sections */}
          {content.sections && content.sections.length > 0 ? (
            <div>
              <h3 className="text-lg font-semibold mb-2">Sections</h3>
              <ul className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg divide-y ${darkMode ? 'divide-gray-800' : 'divide-gray-200'}`}>
                {content.sections.map((section, index) => (
                  <li key={index} className="p-4">
                    <div className="flex items-center">
                      <span className={`w-8 h-8 flex items-center justify-center rounded-full mr-3 ${
                        index < currentSection || completed
                          ? 'bg-[#88cc14] text-white'
                          : index === currentSection
                            ? darkMode ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white'
                            : darkMode ? 'bg-gray-800 text-gray-400' : 'bg-gray-200 text-gray-600'
                      }`}>
                        {index + 1}
                      </span>
                      <span className="font-medium">{section}</span>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              No sections available for this module.
            </p>
          )}
        </div>
        
        {/* Module Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
          {!completed ? (
            <button
              onClick={() => handleProgressUpdate(100, true)}
              className="w-full sm:w-auto px-6 py-3 theme-button-primary rounded-lg flex items-center justify-center"
            >
              <FaCheckCircle className="mr-2" /> Mark as Completed
            </button>
          ) : (
            <div className="flex items-center text-[#88cc14]">
              <FaCheckCircle className="mr-2" /> Module Completed
            </div>
          )}
          
          {/* Rating */}
          <div className="flex items-center">
            <span className="mr-2">Rate:</span>
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setUserRating(star)}
                  onMouseEnter={() => setHoverRating(star)}
                  onMouseLeave={() => setHoverRating(0)}
                  className="text-2xl focus:outline-none"
                >
                  <FaStar
                    className={`${
                      (hoverRating || userRating) >= star
                        ? 'text-yellow-400'
                        : darkMode ? 'text-gray-700' : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* Feedback textarea (shows when rating is selected) */}
        {userRating > 0 && (
          <div className="mt-4">
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Share your feedback about this module (optional)"
              className={`w-full p-3 rounded-lg ${
                darkMode
                  ? 'bg-[#252D4A] border-gray-700 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } border`}
              rows={3}
            ></textarea>
            <button
              onClick={handleRatingSubmit}
              disabled={submittingRating}
              className="mt-2 px-4 py-2 theme-button-primary rounded-lg"
            >
              {submittingRating ? (
                <>
                  <FaSpinner className="inline animate-spin mr-2" /> Submitting...
                </>
              ) : (
                'Submit Rating'
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default LearningModuleDetail;
