import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaLock, FaCrown } from 'react-icons/fa';
import { useSubscription, SUBSCRIPTION_LEVELS } from '../contexts/SubscriptionContext';

const SubscriptionRequired = ({ 
  requiredLevel = SUBSCRIPTION_LEVELS.BASIC,
  feature,
  children,
  showUpgrade = true
}) => {
  const { subscriptionLevel, loading } = useSubscription();
  
  // Define subscription level hierarchy for comparison
  const levelHierarchy = {
    [SUBSCRIPTION_LEVELS.NONE]: 0,
    [SUBSCRIPTION_LEVELS.BASIC]: 1,
    [SUBSCRIPTION_LEVELS.PREMIUM]: 2,
    [SUBSCRIPTION_LEVELS.BUSINESS]: 3
  };
  
  // Check if user's subscription level is sufficient
  const hasAccess = levelHierarchy[subscriptionLevel] >= levelHierarchy[requiredLevel];
  
  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#88cc14]"></div>
      </div>
    );
  }
  
  if (hasAccess) {
    return children;
  }
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="bg-black/5 rounded-lg p-6 text-center"
    >
      <div className="w-16 h-16 bg-[#88cc14]/10 rounded-full flex items-center justify-center mx-auto mb-4">
        <FaLock className="text-[#88cc14] text-2xl" />
      </div>
      
      <h3 className="text-xl font-bold text-gray-900 mb-2">
        {requiredLevel} Subscription Required
      </h3>
      
      <p className="text-gray-600 mb-6">
        {feature 
          ? `Access to ${feature} requires a ${requiredLevel} subscription or higher.`
          : `This content requires a ${requiredLevel} subscription or higher.`
        }
      </p>
      
      {showUpgrade && (
        <Link 
          to="/pricing" 
          className="inline-flex items-center gap-2 bg-[#88cc14] text-black px-6 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
        >
          <FaCrown />
          <span>Upgrade Now</span>
        </Link>
      )}
    </motion.div>
  );
};

export default SubscriptionRequired;