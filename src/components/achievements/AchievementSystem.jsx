import React, { useState, useEffect } from 'react';
import { FaTrophy, FaMedal, FaGraduationCap, FaBook, FaCompass, FaTimes } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useGuestProgress } from '../../contexts/GuestProgressContext';

const AchievementSystem = () => {
  const { darkMode } = useGlobalTheme();
  const { achievements } = useGuestProgress();
  const [newAchievement, setNewAchievement] = useState(null);
  const [showNotification, setShowNotification] = useState(false);
  
  // Check for new achievements
  useEffect(() => {
    if (achievements.length > 0) {
      // Get the most recent achievement
      const latestAchievement = [...achievements].sort((a, b) => 
        new Date(b.timestamp) - new Date(a.timestamp)
      )[0];
      
      // Check if it's a new achievement (within the last 5 seconds)
      const isNew = new Date() - new Date(latestAchievement.timestamp) < 5000;
      
      if (isNew) {
        setNewAchievement(latestAchievement);
        setShowNotification(true);
        
        // Hide notification after 5 seconds
        const timer = setTimeout(() => {
          setShowNotification(false);
        }, 5000);
        
        return () => clearTimeout(timer);
      }
    }
  }, [achievements]);
  
  // Get icon component based on icon name
  const getIconComponent = (iconName) => {
    switch (iconName) {
      case 'FaTrophy':
        return <FaTrophy />;
      case 'FaMedal':
        return <FaMedal />;
      case 'FaGraduationCap':
        return <FaGraduationCap />;
      case 'FaBook':
        return <FaBook />;
      case 'FaCompass':
        return <FaCompass />;
      default:
        return <FaTrophy />;
    }
  };
  
  if (!showNotification || !newAchievement) {
    return null;
  }
  
  return (
    <div className="fixed top-20 right-4 z-50 w-80 animate-slide-in-right">
      <div className={`${
        darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'
      } border rounded-lg shadow-lg overflow-hidden`}>
        <div className="p-4">
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-yellow-500/20 rounded-full flex items-center justify-center mr-3">
                <span className="text-yellow-500">
                  {getIconComponent(newAchievement.icon)}
                </span>
              </div>
              <div>
                <h3 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Achievement Unlocked!
                </h3>
                <p className="text-yellow-500 font-medium">
                  {newAchievement.title}
                </p>
              </div>
            </div>
            <button 
              onClick={() => setShowNotification(false)}
              className={`p-1 rounded-full ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
            >
              <FaTimes className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
            </button>
          </div>
          
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
            {newAchievement.description}
          </p>
          
          <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'} text-right`}>
            Sign up to track all your achievements!
          </div>
        </div>
      </div>
    </div>
  );
};

export default AchievementSystem;
