import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaExclamationTriangle, FaCrown } from 'react-icons/fa';
import { useSubscription, SUBSCRIPTION_LEVELS } from '../contexts/SubscriptionContext';

const FeatureLimit = ({ 
  featureName,
  featureLabel,
  currentUsage = 0,
  children,
  showUpgrade = true
}) => {
  const { getFeatureLimit, subscriptionLevel } = useSubscription();
  
  const limit = getFeatureLimit(featureName);
  const isUnlimited = limit < 0;
  const hasReachedLimit = !isUnlimited && currentUsage >= limit;
  
  // Determine next subscription level for upgrade message
  const getNextLevel = () => {
    if (subscriptionLevel === SUBSCRIPTION_LEVELS.NONE) return SUBSCRIPTION_LEVELS.BASIC;
    if (subscriptionLevel === SUBSCRIPTION_LEVELS.BASIC) return SUBSCRIPTION_LEVELS.PREMIUM;
    return SUBSCRIPTION_LEVELS.BUSINESS;
  };
  
  if (isUnlimited || !hasReachedLimit) {
    return children;
  }
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center"
    >
      <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <FaExclamationTriangle className="text-yellow-500 text-2xl" />
      </div>
      
      <h3 className="text-xl font-bold text-gray-900 mb-2">
        {featureLabel} Limit Reached
      </h3>
      
      <p className="text-gray-600 mb-6">
        You've used {currentUsage} of {limit} available {featureLabel.toLowerCase()}.
        Upgrade to {getNextLevel()} for {
          getNextLevel() === SUBSCRIPTION_LEVELS.PREMIUM ? 'unlimited' : 'more'
        } access.
      </p>
      
      {showUpgrade && (
        <Link 
          to="/pricing" 
          className="inline-flex items-center gap-2 bg-[#88cc14] text-black px-6 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
        >
          <FaCrown />
          <span>Upgrade Now</span>
        </Link>
      )}
    </motion.div>
  );
};

export default FeatureLimit;