import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import DashboardHeader from './modules/DashboardHeader';
import DashboardSidebar from './modules/DashboardSidebar';
import DashboardContent from './modules/DashboardContent';
import LearningAnalyticsWidget from './widgets/LearningAnalyticsWidget';
import ChallengeAnalyticsWidget from './widgets/ChallengeAnalyticsWidget';
import RecommendationsWidget from './widgets/RecommendationsWidget';
import AnalyticsChart from '../visualizations/AnalyticsChart';
import SkillsRadarChart from '../visualizations/SkillsRadarChart';
import UpgradePrompt from '../subscription/UpgradePrompt';
import { useDashboard } from '../../contexts/DashboardContext';
import { useSubscription, SUBSCRIPTION_LEVELS } from '../../contexts/SubscriptionContext';
import { signOut } from '../../lib/auth';

/**
 * UnifiedDashboard Component
 * 
 * A single dashboard that adapts based on the user's subscription level.
 * Shows appropriate content and features based on subscription tier.
 */
function UnifiedDashboard({ profile, subscription, coins, challenges, completedChallenges, totalPoints }) {
  const [selectedSection, setSelectedSection] = useState('dashboard');
  const navigate = useNavigate();
  
  // Get subscription information
  const { 
    subscriptionLevel, 
    hasAccess, 
    isFree, 
    isPremium, 
    isBusiness 
  } = useSubscription();

  // Use dashboard context for data and state management
  const {
    // Data
    learningStats,
    challengeStats,
    recommendations,
    userSkills,
    teamMembers,
    teamStats,

    // State
    timeRange,
    isLoading,
    error,
    showLearningDetails,
    showChallengeDetails,
    showTeamDetails,

    // Actions
    handleTimeRangeChange,
    toggleLearningDetails,
    toggleChallengeDetails,
    toggleTeamDetails,
    fetchAllData
  } = useDashboard();

  // Sample data for charts (would be replaced with real data)
  const learningProgressLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
  
  const learningProgressData = [
    [30, 45, 60, 70, 75, 85] // Last 6 months progress
  ];
  
  const challengeCompletionData = [
    [2, 3, 5, 4, 6, 8] // Challenges completed by month
  ];
  
  // Sample skills data (would be replaced with real data)
  const skillNames = ['Web Security', 'Network Security', 'Cryptography', 'OSINT', 'Reverse Engineering'];
  const skillValues = [70, 60, 40, 80, 50];
  const teamSkillValues = [65, 70, 55, 60, 75];

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Handle upgrade click
  const handleUpgrade = () => {
    navigate('/pricing');
  };

  // Handle refresh data
  const handleRefreshData = () => {
    fetchAllData();
  };

  // Determine dashboard title based on subscription level
  const getDashboardTitle = () => {
    if (isBusiness) return 'Business Dashboard';
    if (isPremium) return 'Premium Dashboard';
    return 'Dashboard';
  };

  return (
    <div className="min-h-screen bg-[#0B1120] text-white">
      <div className="flex">
        {/* Sidebar */}
        <DashboardSidebar
          profile={profile}
          selectedSection={selectedSection}
          setSelectedSection={setSelectedSection}
          onSignOut={handleSignOut}
          subscriptionLevel={subscriptionLevel}
        />

        {/* Main Content */}
        <div className="flex-1">
          {/* Header */}
          <DashboardHeader
            profile={profile}
            subscription={subscription}
            coins={coins}
            onSignOut={handleSignOut}
          />

          {/* Dashboard Content */}
          {selectedSection === 'dashboard' ? (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">{getDashboardTitle()}</h2>
                <div className="flex gap-2">
                  {isFree && (
                    <button
                      onClick={handleUpgrade}
                      className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center"
                    >
                      Upgrade Now
                    </button>
                  )}
                  <button
                    onClick={handleRefreshData}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center"
                  >
                    Refresh Data
                  </button>
                </div>
              </div>

              {/* Analytics Widgets - Shown to all users but with different data */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <LearningAnalyticsWidget
                  learningStats={learningStats}
                  timeRange={timeRange}
                  onTimeRangeChange={handleTimeRangeChange}
                  showDetails={showLearningDetails}
                  onToggleDetails={toggleLearningDetails}
                  loading={isLoading.learning}
                  error={error.learning}
                  subscriptionLevel={subscriptionLevel}
                />

                <ChallengeAnalyticsWidget
                  challengeStats={challengeStats}
                  timeRange={timeRange}
                  onTimeRangeChange={handleTimeRangeChange}
                  showDetails={showChallengeDetails}
                  onToggleDetails={toggleChallengeDetails}
                  loading={isLoading.challenges}
                  error={error.challenges}
                  subscriptionLevel={subscriptionLevel}
                />
              </div>

              {/* Visualizations - Basic for free, detailed for premium/business */}
              {(isPremium || isBusiness) ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  <AnalyticsChart
                    type="line"
                    data={learningProgressData}
                    labels={learningProgressLabels}
                    title="Learning Progress Over Time"
                    height={200}
                  />

                  <AnalyticsChart
                    type="bar"
                    data={challengeCompletionData}
                    labels={learningProgressLabels}
                    title="Challenges Completed by Month"
                    height={200}
                  />
                </div>
              ) : (
                <div className="mb-6">
                  <UpgradePrompt
                    title="Unlock Detailed Analytics"
                    description="Upgrade to Premium to access detailed learning and challenge analytics, progress tracking, and personalized recommendations."
                    buttonText="Upgrade to Premium"
                    onUpgrade={handleUpgrade}
                  />
                </div>
              )}

              {/* Skills Radar Chart - Only for premium/business */}
              {(isPremium || isBusiness) && skillNames.length > 0 && (
                <div className="mb-6">
                  <SkillsRadarChart
                    skills={skillNames}
                    dataSeries={[skillValues, teamSkillValues]}
                    labels={['Your Skills', 'Team Average']}
                    height={300}
                  />
                </div>
              )}

              {/* Team Analytics - Only for business */}
              {isBusiness && (
                <div className="mb-6">
                  <div className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-xl font-bold text-white">Team Performance</h3>
                      <button
                        onClick={toggleTeamDetails}
                        className="text-[#88cc14] hover:text-[#7ab811]"
                      >
                        {showTeamDetails ? 'Show Less' : 'Show More'}
                      </button>
                    </div>
                    
                    {isLoading.team ? (
                      <div className="flex justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#88cc14]"></div>
                      </div>
                    ) : error.team ? (
                      <div className="text-red-400 py-4">{error.team}</div>
                    ) : (
                      <div>
                        <p className="text-gray-300 mb-4">
                          Track your team's progress and performance across all learning modules and challenges.
                        </p>
                        
                        {/* Team members list - simplified version */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                          {teamMembers.slice(0, 3).map((member, index) => (
                            <div key={index} className="bg-[#1A1F35] p-4 rounded-lg">
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center">
                                  {member.avatar_url ? (
                                    <img src={member.avatar_url} alt={member.username} className="w-full h-full rounded-full" />
                                  ) : (
                                    <span className="text-[#88cc14]">{member.username.charAt(0).toUpperCase()}</span>
                                  )}
                                </div>
                                <div>
                                  <p className="font-medium text-white">{member.username}</p>
                                  <p className="text-sm text-gray-400">{member.role || 'Team Member'}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                        
                        {showTeamDetails && (
                          <div className="mt-4">
                            <p className="text-white font-medium mb-2">Team Statistics</p>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                              <div className="bg-[#1A1F35] p-3 rounded-lg">
                                <p className="text-gray-400 text-sm">Completed Challenges</p>
                                <p className="text-xl font-bold text-white">{teamStats?.completedChallenges || 0}</p>
                              </div>
                              <div className="bg-[#1A1F35] p-3 rounded-lg">
                                <p className="text-gray-400 text-sm">Completed Modules</p>
                                <p className="text-xl font-bold text-white">{teamStats?.completedModules || 0}</p>
                              </div>
                              <div className="bg-[#1A1F35] p-3 rounded-lg">
                                <p className="text-gray-400 text-sm">Average Score</p>
                                <p className="text-xl font-bold text-white">{teamStats?.averageScore || 0}%</p>
                              </div>
                              <div className="bg-[#1A1F35] p-3 rounded-lg">
                                <p className="text-gray-400 text-sm">Total Points</p>
                                <p className="text-xl font-bold text-white">{teamStats?.totalPoints || 0}</p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Recommendations Widget - Different recommendations based on tier */}
              <div className="mb-6">
                <RecommendationsWidget
                  recommendations={recommendations}
                  userSkills={userSkills}
                  subscriptionLevel={subscriptionLevel}
                  onUpgrade={handleUpgrade}
                  loading={isLoading.recommendations}
                  error={error.recommendations}
                />
              </div>

              {/* Original Dashboard Content - Common for all users */}
              <DashboardContent
                selectedSection={selectedSection}
                profile={profile}
                subscription={subscription}
                coins={coins}
                challenges={challenges}
                completedChallenges={completedChallenges}
                totalPoints={totalPoints}
              />
            </div>
          ) : (
            <DashboardContent
              selectedSection={selectedSection}
              profile={profile}
              subscription={subscription}
              coins={coins}
              challenges={challenges}
              completedChallenges={completedChallenges}
              totalPoints={totalPoints}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default UnifiedDashboard;
