import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  FaHome, FaGraduationCap, FaTrophy, FaUsers, FaStore, FaChartLine, 
  FaUserTie, FaSignOutAlt, FaRegBell, FaRegUser, FaArrowRight, FaCoins
} from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { SUBSCRIPTION_TIERS } from '../../config/subscriptionTiers';
import DynamicLearningModule from '../learning/DynamicLearningModule';
import { signOut } from '../../lib/auth';

/**
 * FreeDashboard Component
 * 
 * A dashboard specifically designed for free tier users with dynamic content
 * and personalized learning experiences.
 */
const FreeDashboard = ({ 
  profile = {}, 
  coins = { balance: 0 }, 
  challenges = [], 
  completedChallenges = 0, 
  totalPoints = 0 
}) => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState('overview');
  const [notifications, setNotifications] = useState([]);
  const [userStats, setUserStats] = useState({
    completedModules: 0,
    inProgressModules: 0,
    completedChallenges: completedChallenges || 0,
    totalPoints: totalPoints || 0,
    coins: coins?.balance || 0
  });
  
  // Sidebar links
  const sidebarLinks = [
    { name: 'Overview', icon: <FaHome />, id: 'overview', locked: false },
    { name: 'Learning', icon: <FaGraduationCap />, id: 'learning', locked: false },
    { name: 'Challenges', icon: <FaTrophy />, id: 'challenges', locked: false },
    { name: 'Leaderboard', icon: <FaUsers />, id: 'leaderboard', locked: false },
    { name: 'Store', icon: <FaStore />, id: 'store', locked: false },
    { name: 'Analytics', icon: <FaChartLine />, id: 'analytics', locked: true }
  ];
  
  // Mock notifications
  const mockNotifications = [
    {
      id: 'n1',
      title: 'New Module Available',
      message: 'Check out the new Network Security Fundamentals module',
      time: '2 hours ago',
      read: false
    },
    {
      id: 'n2',
      title: 'Weekly Challenge',
      message: 'This week\'s challenge: SQL Injection Basics is now available',
      time: '1 day ago',
      read: true
    }
  ];
  
  // Initialize user stats and notifications
  useEffect(() => {
    // In a real implementation, this would fetch from an API
    // For now, we'll use mock data and localStorage
    
    // Get learning progress from localStorage
    const storedProgress = localStorage.getItem('learning_progress');
    if (storedProgress) {
      try {
        const progress = JSON.parse(storedProgress);
        
        // Count completed and in-progress modules
        let completed = 0;
        let inProgress = 0;
        
        Object.values(progress).forEach(value => {
          if (value === 100) {
            completed++;
          } else if (value > 0) {
            inProgress++;
          }
        });
        
        setUserStats(prev => ({
          ...prev,
          completedModules: completed,
          inProgressModules: inProgress
        }));
      } catch (error) {
        console.error('Error parsing stored progress:', error);
      }
    }
    
    // Set notifications
    setNotifications(mockNotifications);
  }, []);
  
  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };
  
  // Render stats cards
  const renderStatsCards = () => {
    const stats = [
      {
        title: 'Learning Modules',
        value: userStats.completedModules,
        subtext: 'completed',
        icon: <FaGraduationCap className="text-blue-500" />,
        color: 'blue',
        inProgress: userStats.inProgressModules
      },
      {
        title: 'Challenges',
        value: userStats.completedChallenges,
        subtext: 'completed',
        icon: <FaTrophy className="text-yellow-500" />,
        color: 'yellow'
      },
      {
        title: 'XCerberus Coins',
        value: userStats.coins,
        subtext: 'available',
        icon: <FaCoins className="text-[#88cc14]" />,
        color: 'green'
      },
      {
        title: 'Total Points',
        value: userStats.totalPoints,
        subtext: 'earned',
        icon: <FaChartLine className="text-purple-500" />,
        color: 'purple'
      }
    ];
    
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {stats.map((stat, index) => (
          <div 
            key={index}
            className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{stat.title}</h3>
              {stat.icon}
            </div>
            <p className="text-3xl font-bold">{stat.value}</p>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{stat.subtext}</p>
            
            {stat.inProgress > 0 && (
              <div className="mt-2 text-xs text-blue-500">
                {stat.inProgress} in progress
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };
  
  // Render upgrade banner
  const renderUpgradeBanner = () => {
    return (
      <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6 mb-6`}>
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-[#88cc14] rounded-full flex items-center justify-center">
              <FaGraduationCap className="text-black text-xl" />
            </div>
            <div>
              <h3 className="text-lg font-bold mb-1">Upgrade to Premium</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>
                Get unlimited access to all learning modules and advanced challenges
              </p>
            </div>
          </div>
          
          <Link
            to="/pricing"
            className="px-6 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors flex items-center whitespace-nowrap"
          >
            Upgrade Now <FaArrowRight className="ml-2" />
          </Link>
        </div>
      </div>
    );
  };
  
  // Render notifications panel
  const renderNotifications = () => {
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold">Notifications</h3>
          <button className="text-sm text-[#88cc14] hover:underline">
            Mark all as read
          </button>
        </div>
        
        {notifications.length === 0 ? (
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-center py-4`}>
            No notifications
          </p>
        ) : (
          <div className="space-y-4">
            {notifications.map(notification => (
              <div 
                key={notification.id}
                className={`p-4 rounded-lg ${
                  notification.read
                    ? darkMode ? 'bg-[#252D4A]' : 'bg-gray-100'
                    : darkMode ? 'bg-[#252D4A] border-l-4 border-[#88cc14]' : 'bg-gray-100 border-l-4 border-[#88cc14]'
                }`}
              >
                <h4 className="font-semibold mb-1">{notification.title}</h4>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm mb-2`}>
                  {notification.message}
                </p>
                <div className="text-xs text-gray-500">
                  {notification.time}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <header className={`fixed top-0 left-0 right-0 z-10 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-b`}>
        <div className="container mx-auto px-4 py-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Link to="/" className="text-xl font-bold mr-8">
                <span className="text-[#88cc14]">X</span>Cerberus
              </Link>
              
              <nav className="hidden md:flex space-x-6">
                {sidebarLinks.filter(link => !link.locked).map(link => (
                  <button
                    key={link.id}
                    onClick={() => setActiveTab(link.id)}
                    className={`flex items-center ${
                      activeTab === link.id
                        ? 'text-[#88cc14]'
                        : darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-black'
                    }`}
                  >
                    <span className="mr-2">{link.icon}</span>
                    <span>{link.name}</span>
                  </button>
                ))}
              </nav>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="relative">
                <button className="p-2 rounded-full hover:bg-gray-800 transition-colors">
                  <FaRegBell />
                  {notifications.some(n => !n.read) && (
                    <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                  )}
                </button>
              </div>
              
              <div className="relative group">
                <button className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
                    <FaRegUser />
                  </div>
                  <span className="hidden md:inline">{user?.email || 'User'}</span>
                </button>
                
                <div className={`absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 ${
                  darkMode ? 'bg-[#252D4A] border border-gray-700' : 'bg-white border border-gray-200'
                } hidden group-hover:block`}>
                  <Link
                    to="/profile"
                    className={`block px-4 py-2 text-sm ${
                      darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    Your Profile
                  </Link>
                  <Link
                    to="/settings"
                    className={`block px-4 py-2 text-sm ${
                      darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    Settings
                  </Link>
                  <button
                    onClick={handleSignOut}
                    className={`block w-full text-left px-4 py-2 text-sm ${
                      darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      
      {/* Mobile Navigation */}
      <div className={`fixed bottom-0 left-0 right-0 md:hidden z-10 ${
        darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'
      } border-t`}>
        <div className="flex justify-around">
          {sidebarLinks.filter(link => !link.locked).slice(0, 5).map(link => (
            <button
              key={link.id}
              onClick={() => setActiveTab(link.id)}
              className={`flex flex-col items-center py-3 ${
                activeTab === link.id
                  ? 'text-[#88cc14]'
                  : darkMode ? 'text-gray-400' : 'text-gray-600'
              }`}
            >
              <span>{link.icon}</span>
              <span className="text-xs mt-1">{link.name}</span>
            </button>
          ))}
        </div>
      </div>
      
      {/* Main Content */}
      <main className="container mx-auto px-4 pt-24 pb-20">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div>
            <h1 className="text-2xl font-bold mb-6">Dashboard Overview</h1>
            
            {/* Stats Cards */}
            {renderStatsCards()}
            
            {/* Upgrade Banner */}
            {renderUpgradeBanner()}
            
            {/* Notifications */}
            {renderNotifications()}
            
            {/* Quick Access */}
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
              <h2 className="text-xl font-bold mb-4">Quick Access</h2>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <Link
                  to="/learn"
                  className={`p-4 rounded-lg border ${
                    darkMode ? 'border-gray-700 hover:bg-gray-800' : 'border-gray-300 hover:bg-gray-100'
                  } transition-colors flex items-center gap-3`}
                >
                  <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                    <FaGraduationCap className="text-blue-500" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Learning Modules</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Continue your learning journey
                    </p>
                  </div>
                </Link>
                
                <Link
                  to="/challenges"
                  className={`p-4 rounded-lg border ${
                    darkMode ? 'border-gray-700 hover:bg-gray-800' : 'border-gray-300 hover:bg-gray-100'
                  } transition-colors flex items-center gap-3`}
                >
                  <div className="w-10 h-10 bg-yellow-500/20 rounded-full flex items-center justify-center">
                    <FaTrophy className="text-yellow-500" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Challenges</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Test your skills with hands-on challenges
                    </p>
                  </div>
                </Link>
                
                <Link
                  to="/leaderboard"
                  className={`p-4 rounded-lg border ${
                    darkMode ? 'border-gray-700 hover:bg-gray-800' : 'border-gray-300 hover:bg-gray-100'
                  } transition-colors flex items-center gap-3`}
                >
                  <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center">
                    <FaUsers className="text-purple-500" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Leaderboard</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      See how you rank against others
                    </p>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        )}
        
        {/* Learning Tab */}
        {activeTab === 'learning' && (
          <div>
            <h1 className="text-2xl font-bold mb-6">Learning Center</h1>
            
            {/* Dynamic Learning Module */}
            <DynamicLearningModule 
              subscriptionTier={SUBSCRIPTION_TIERS.FREE}
            />
          </div>
        )}
        
        {/* Challenges Tab */}
        {activeTab === 'challenges' && (
          <div>
            <h1 className="text-2xl font-bold mb-6">Challenges</h1>
            
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
              <p className="mb-4">Challenges are fully dynamic and implemented separately.</p>
              <Link
                to="/challenges"
                className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors inline-block"
              >
                Go to Challenges
              </Link>
            </div>
          </div>
        )}
        
        {/* Leaderboard Tab */}
        {activeTab === 'leaderboard' && (
          <div>
            <h1 className="text-2xl font-bold mb-6">Leaderboard</h1>
            
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
              <p className="mb-4">Leaderboard is implemented separately.</p>
              <Link
                to="/leaderboard"
                className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors inline-block"
              >
                Go to Leaderboard
              </Link>
            </div>
          </div>
        )}
        
        {/* Store Tab */}
        {activeTab === 'store' && (
          <div>
            <h1 className="text-2xl font-bold mb-6">Store</h1>
            
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
              <p className="mb-4">Store is implemented separately.</p>
              <Link
                to="/store"
                className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors inline-block"
              >
                Go to Store
              </Link>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default FreeDashboard;
