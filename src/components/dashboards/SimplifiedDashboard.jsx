import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaBook, 
  FaCode, 
  FaChevronDown, 
  FaChevronRight, 
  FaTrophy, 
  FaNetworkWired, 
  FaCloud, 
  FaWifi, 
  FaShieldAlt, 
  FaLock, 
  FaUserSecret, 
  FaVirus, 
  FaBug, 
  FaTerminal,
  FaUserGraduate,
  FaRocket,
  FaFlag
} from 'react-icons/fa';
import { useSubscription } from '../../contexts/SubscriptionContext';

const SimplifiedDashboard = ({ userData }) => {
  const navigate = useNavigate();
  const { subscriptionLevel, hasFeature } = useSubscription();
  const [expandedSections, setExpandedSections] = useState({
    learn: true,
    attack: false,
    defence: false,
    malware: false
  });

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Check if user has access to premium features
  const hasPremiumAccess = subscriptionLevel === 'Premium' || subscriptionLevel === 'Business';

  return (
    <div className="flex h-screen bg-[#0B1120]">
      {/* Sidebar */}
      <div className="w-64 bg-[#0B1120] border-r border-gray-800 overflow-y-auto">
        {/* Logo */}
        <div className="p-4 border-b border-gray-800">
          <div className="flex items-center">
            <span className="text-[#88cc14] font-bold text-xl">X</span>
            <span className="text-white font-bold text-xl">CERBERUS</span>
          </div>
        </div>

        {/* Navigation */}
        <nav className="p-4">
          {/* Learn Section */}
          <div className="mb-6">
            <div className="text-gray-400 uppercase text-xs font-bold mb-2">LEARN</div>
            <div className="space-y-1">
              <Link 
                to="/learn/modules"
                className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                  !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                }`}
              >
                <FaBook className="text-[#88cc14]" />
                <span>Learning Modules</span>
              </Link>
              <Link 
                to="/learn/certifications"
                className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                  !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                }`}
              >
                <FaUserGraduate className="text-[#88cc14]" />
                <span>Certifications</span>
              </Link>
              <Link 
                to="/learn/paths"
                className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                  !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                }`}
              >
                <FaRocket className="text-[#88cc14]" />
                <span>Career Paths</span>
              </Link>
            </div>
          </div>

          {/* StartHack Section */}
          <div className="mb-6">
            <div className="text-gray-400 uppercase text-xs font-bold mb-2">STARTHACK</div>
            
            {/* Attack Subsection */}
            <div className="mb-2">
              <button 
                onClick={() => toggleSection('attack')}
                className="flex items-center justify-between w-full px-3 py-2 text-gray-300 hover:bg-gray-800 hover:text-white rounded-md"
              >
                <div className="flex items-center gap-2">
                  <span>Attack</span>
                </div>
                {expandedSections.attack ? <FaChevronDown size={12} /> : <FaChevronRight size={12} />}
              </button>
              
              {expandedSections.attack && (
                <div className="ml-4 mt-1 space-y-1">
                  <Link 
                    to="/hack/web"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaCode className="text-[#88cc14]" />
                    <span>Web Penetration</span>
                  </Link>
                  <Link 
                    to="/hack/network"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaNetworkWired className="text-[#88cc14]" />
                    <span>Network Penetration</span>
                  </Link>
                  <Link 
                    to="/hack/cloud"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaCloud className="text-[#88cc14]" />
                    <span>Cloud Security</span>
                  </Link>
                  <Link 
                    to="/hack/wireless"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaWifi className="text-[#88cc14]" />
                    <span>Wireless Security</span>
                  </Link>
                </div>
              )}
            </div>
            
            {/* Defence Subsection */}
            <div className="mb-2">
              <button 
                onClick={() => toggleSection('defence')}
                className="flex items-center justify-between w-full px-3 py-2 text-gray-300 hover:bg-gray-800 hover:text-white rounded-md"
              >
                <div className="flex items-center gap-2">
                  <span>Defence</span>
                </div>
                {expandedSections.defence ? <FaChevronDown size={12} /> : <FaChevronRight size={12} />}
              </button>
              
              {expandedSections.defence && (
                <div className="ml-4 mt-1 space-y-1">
                  <Link 
                    to="/hack/siem"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaShieldAlt className="text-[#88cc14]" />
                    <span>SIEM</span>
                  </Link>
                  <Link 
                    to="/hack/incident"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaLock className="text-[#88cc14]" />
                    <span>Incident Response</span>
                  </Link>
                  <Link 
                    to="/hack/threat-hunting"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaUserSecret className="text-[#88cc14]" />
                    <span>Threat Hunting</span>
                  </Link>
                </div>
              )}
            </div>
            
            {/* Malware Subsection */}
            <div>
              <button 
                onClick={() => toggleSection('malware')}
                className="flex items-center justify-between w-full px-3 py-2 text-gray-300 hover:bg-gray-800 hover:text-white rounded-md"
              >
                <div className="flex items-center gap-2">
                  <span>Malware</span>
                </div>
                {expandedSections.malware ? <FaChevronDown size={12} /> : <FaChevronRight size={12} />}
              </button>
              
              {expandedSections.malware && (
                <div className="ml-4 mt-1 space-y-1">
                  <Link 
                    to="/hack/malware"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaVirus className="text-[#88cc14]" />
                    <span>Malware Analysis</span>
                  </Link>
                  <Link 
                    to="/hack/reverse"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaBug className="text-[#88cc14]" />
                    <span>Reverse Engineering</span>
                  </Link>
                  <Link 
                    to="/hack/exploit"
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white ${
                      !hasPremiumAccess ? 'opacity-50 pointer-events-none' : ''
                    }`}
                  >
                    <FaTerminal className="text-[#88cc14]" />
                    <span>Exploit Development</span>
                  </Link>
                </div>
              )}
            </div>
          </div>
          
          {/* Challenges Section */}
          <div>
            <div className="text-gray-400 uppercase text-xs font-bold mb-2">CHALLENGES</div>
            <Link 
              to="/challenges"
              className="flex items-center gap-2 px-3 py-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              <FaFlag className="text-[#88cc14]" />
              <span>All Challenges</span>
            </Link>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-[#0B1120] border-b border-gray-800 p-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 bg-[#1A1F35] px-3 py-1.5 rounded-md">
              <span className="text-[#88cc14] font-bold">{userData?.coins || 100}</span>
              <span className="text-[#88cc14]">XC</span>
            </div>
            <div className="bg-[#1A1F35] px-3 py-1.5 rounded-md text-gray-300">
              Wallet
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="w-8 h-8 bg-[#1A1F35] rounded-full flex items-center justify-center text-gray-300">
              <span>🔔</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-[#88cc14] rounded-full flex items-center justify-center">
                {userData?.avatar || userData?.username?.charAt(0) || 'U'}
              </div>
              <div>
                <div className="text-white text-sm">{userData?.username || 'chitti.gouthamkumar'}</div>
                <div className="text-gray-400 text-xs">Free Tier</div>
              </div>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <div className="flex-1 p-4 bg-[#0B1120] overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* XCerberus Coins */}
            <div className="bg-[#1A1F35] rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="text-gray-400 text-sm">XCerberus Coins</div>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-[#88cc14] text-2xl font-bold">{userData?.coins || 100}</span>
                <span className="text-[#88cc14]">XC</span>
              </div>
            </div>
            
            {/* Challenges Completed */}
            <div className="bg-[#1A1F35] rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="text-gray-400 text-sm">Challenges Completed</div>
                <FaTrophy className="text-[#88cc14]" />
              </div>
              <div className="text-white text-2xl font-bold">{userData?.completedChallenges || 0}</div>
            </div>
            
            {/* Total Points */}
            <div className="bg-[#1A1F35] rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="text-gray-400 text-sm">Total Points</div>
                <FaTrophy className="text-[#88cc14]" />
              </div>
              <div className="text-white text-2xl font-bold">{userData?.totalPoints || 0}</div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            {/* Start Hack */}
            <div className="bg-[#1A1F35] rounded-lg p-4">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 bg-[#0B1120] rounded-full flex items-center justify-center">
                  <FaCode className="text-[#88cc14]" />
                </div>
                <div className="text-white font-bold">Start Hack</div>
              </div>
              <div className="text-gray-400 text-sm mb-4">Begin a new hacking mission</div>
              <button 
                onClick={() => navigate('/hack/start')}
                className={`bg-[#0B1120] text-white px-4 py-2 rounded-md w-full hover:bg-gray-800 ${
                  !hasPremiumAccess ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={!hasPremiumAccess}
              >
                {hasPremiumAccess ? 'Start Hacking' : 'Premium Feature'}
              </button>
            </div>
            
            {/* Learn */}
            <div className="bg-[#1A1F35] rounded-lg p-4">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 bg-[#0B1120] rounded-full flex items-center justify-center">
                  <FaBook className="text-[#88cc14]" />
                </div>
                <div className="text-white font-bold">Learn</div>
              </div>
              <div className="text-gray-400 text-sm mb-4">Access learning resources</div>
              <button 
                onClick={() => navigate('/learn/modules')}
                className={`bg-[#0B1120] text-white px-4 py-2 rounded-md w-full hover:bg-gray-800 ${
                  !hasPremiumAccess ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={!hasPremiumAccess}
              >
                {hasPremiumAccess ? 'Start Learning' : 'Premium Feature'}
              </button>
            </div>
            
            {/* Challenges */}
            <div className="bg-[#1A1F35] rounded-lg p-4">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 bg-[#0B1120] rounded-full flex items-center justify-center">
                  <FaFlag className="text-[#88cc14]" />
                </div>
                <div className="text-white font-bold">Challenges</div>
              </div>
              <div className="text-gray-400 text-sm mb-4">Take on security challenges</div>
              <button 
                onClick={() => navigate('/challenges')}
                className="bg-[#0B1120] text-white px-4 py-2 rounded-md w-full hover:bg-gray-800"
              >
                View Challenges
              </button>
            </div>
          </div>
          
          {/* Recent Activity */}
          <div className="bg-[#1A1F35] rounded-lg p-4">
            <h2 className="text-white font-bold text-lg mb-4">Recent Activity</h2>
            
            {userData?.recentActivity && userData.recentActivity.length > 0 ? (
              <div className="space-y-3">
                {userData.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center justify-between border-b border-gray-800 pb-3">
                    <div>
                      <div className="text-white">{activity.title}</div>
                      <div className="text-gray-400 text-sm">{activity.timestamp}</div>
                    </div>
                    <div className="text-[#88cc14]">+{activity.points} XP</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">No recent activity</div>
                <Link to="/challenges" className="text-[#88cc14] hover:underline">
                  Start a challenge
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedDashboard;
