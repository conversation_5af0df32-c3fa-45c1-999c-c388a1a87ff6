import React from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { FaServer, FaArrowRight, FaLock, FaBuilding, FaUsers, FaNetworkWired, FaShieldAlt } from 'react-icons/fa';
import { SUBSCRIPTION_TIERS } from '../../../config/subscriptionTiers';

/**
 * StartHackWidget Component
 * 
 * Displays advanced simulation scenarios for business users.
 * For non-business users, shows a preview with upgrade prompt.
 */
const StartHackWidget = ({ 
  scenarios = [],
  subscriptionLevel = SUBSCRIPTION_TIERS.FREE,
  onUpgrade
}) => {
  const navigate = useNavigate();
  
  // Determine if user has access to Start Hack
  const hasAccess = subscriptionLevel === SUBSCRIPTION_TIERS.BUSINESS;
  
  // Get scenario icon based on type
  const getScenarioIcon = (type) => {
    switch(type) {
      case 'network':
        return <FaNetworkWired className="text-blue-500" />;
      case 'security':
        return <FaShieldAlt className="text-green-500" />;
      case 'team':
        return <FaUsers className="text-purple-500" />;
      default:
        return <FaServer className="text-gray-500" />;
    }
  };
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaServer className="mr-2 text-blue-600 dark:text-blue-400" />
            Start Hack
            {!hasAccess && (
              <span className="ml-2 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 py-0.5 px-2 rounded-full flex items-center">
                <FaBuilding className="mr-1" /> Business
              </span>
            )}
          </h3>
          <Link 
            to="/global-start-hack" 
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center"
          >
            View All
            <FaArrowRight className="ml-1 text-xs" />
          </Link>
        </div>
        
        {hasAccess ? (
          <>
            {/* Available Scenarios */}
            <div className="space-y-3 mb-4">
              {scenarios.slice(0, 2).map((scenario, index) => (
                <div 
                  key={index}
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                  onClick={() => navigate(`/global-start-hack?scenario=${scenario.id}`)}
                >
                  <div className="flex items-center">
                    <div className="mr-3">
                      {getScenarioIcon(scenario.type)}
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900 dark:text-white">{scenario.title}</h5>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {scenario.difficulty} • {scenario.estimatedTime}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Quick Start Button */}
            <div className="text-center">
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                onClick={() => navigate('/global-start-hack')}
              >
                Launch Simulation
              </button>
            </div>
          </>
        ) : (
          <>
            {/* Preview for non-business users */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
              <div className="flex items-center mb-3">
                <FaBuilding className="text-blue-500 mr-2" />
                <h4 className="font-medium text-gray-900 dark:text-white">Business-Exclusive Feature</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Start Hack provides advanced cybersecurity simulations for enterprise training and team exercises.
              </p>
              <div className="grid grid-cols-2 gap-2 mb-3">
                <div className="bg-white dark:bg-gray-600 p-2 rounded text-xs text-center text-gray-700 dark:text-gray-300">
                  <FaUsers className="mx-auto mb-1" />
                  Team Collaboration
                </div>
                <div className="bg-white dark:bg-gray-600 p-2 rounded text-xs text-center text-gray-700 dark:text-gray-300">
                  <FaNetworkWired className="mx-auto mb-1" />
                  Enterprise Scenarios
                </div>
              </div>
              <button
                className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                onClick={onUpgrade}
              >
                Upgrade to Business
              </button>
            </div>
          </>
        )}
      </div>
    </motion.div>
  );
};

export default StartHackWidget;
