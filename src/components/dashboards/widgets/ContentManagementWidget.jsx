import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { 
  FaBook, FaArrowRight, FaPlus, FaEdit, FaTrash, FaEye, 
  FaGraduationCap, FaGamepad, FaNewspaper, FaFilter, FaSearch 
} from 'react-icons/fa';
import { SUBSCRIPTION_TIERS } from '../../../config/subscriptionTiers';

/**
 * ContentManagementWidget Component
 * 
 * Admin widget for managing learning modules, challenges, and blog content.
 * Provides quick access to content creation and editing.
 */
const ContentManagementWidget = ({ 
  modules = [],
  challenges = [],
  blogPosts = [],
  onAddContent,
  onEditContent,
  onDeleteContent,
  onViewContent
}) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('modules');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Filter content based on search query
  const filteredContent = () => {
    const query = searchQuery.toLowerCase();
    
    switch(activeTab) {
      case 'modules':
        return modules.filter(module => 
          module.title.toLowerCase().includes(query) || 
          module.description.toLowerCase().includes(query)
        );
      case 'challenges':
        return challenges.filter(challenge => 
          challenge.title.toLowerCase().includes(query) || 
          challenge.description.toLowerCase().includes(query)
        );
      case 'blog':
        return blogPosts.filter(post => 
          post.title.toLowerCase().includes(query) || 
          post.excerpt.toLowerCase().includes(query)
        );
      default:
        return [];
    }
  };
  
  // Get content icon based on type
  const getContentIcon = (type) => {
    switch(type) {
      case 'modules':
        return <FaGraduationCap />;
      case 'challenges':
        return <FaGamepad />;
      case 'blog':
        return <FaNewspaper />;
      default:
        return <FaBook />;
    }
  };
  
  // Get subscription tier label
  const getTierLabel = (tier) => {
    switch(tier) {
      case SUBSCRIPTION_TIERS.PREMIUM:
        return <span className="text-xs bg-yellow-100 text-yellow-800 py-0.5 px-1.5 rounded">Premium</span>;
      case SUBSCRIPTION_TIERS.BUSINESS:
        return <span className="text-xs bg-blue-100 text-blue-800 py-0.5 px-1.5 rounded">Business</span>;
      default:
        return <span className="text-xs bg-green-100 text-green-800 py-0.5 px-1.5 rounded">Free</span>;
    }
  };
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaBook className="mr-2 text-blue-600 dark:text-blue-400" />
            Content Management
          </h3>
          <button
            className="text-sm bg-blue-600 text-white px-3 py-1 rounded flex items-center hover:bg-blue-700 transition-colors"
            onClick={() => onAddContent(activeTab)}
          >
            <FaPlus className="mr-1" />
            Add New
          </button>
        </div>
        
        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'modules' 
                ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-400 dark:border-blue-400' 
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('modules')}
          >
            <FaGraduationCap className="inline mr-1" />
            Modules
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'challenges' 
                ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-400 dark:border-blue-400' 
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('challenges')}
          >
            <FaGamepad className="inline mr-1" />
            Challenges
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'blog' 
                ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-400 dark:border-blue-400' 
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('blog')}
          >
            <FaNewspaper className="inline mr-1" />
            Blog
          </button>
        </div>
        
        {/* Search */}
        <div className="relative mb-4">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
            placeholder={`Search ${activeTab}...`}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        {/* Content List */}
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {filteredContent().slice(0, 5).map((item, index) => (
            <div 
              key={index}
              className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 flex items-center justify-between"
            >
              <div className="flex items-center">
                <div className="mr-3 text-blue-600 dark:text-blue-400">
                  {getContentIcon(activeTab)}
                </div>
                <div>
                  <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
                    {item.title}
                    <span className="ml-2">{getTierLabel(item.tier)}</span>
                  </h5>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate max-w-xs">
                    {item.description || item.excerpt}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  className="p-1.5 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
                  onClick={() => onViewContent(activeTab, item.id)}
                  title="View"
                >
                  <FaEye />
                </button>
                <button
                  className="p-1.5 text-gray-500 hover:text-yellow-600 dark:text-gray-400 dark:hover:text-yellow-400"
                  onClick={() => onEditContent(activeTab, item.id)}
                  title="Edit"
                >
                  <FaEdit />
                </button>
                <button
                  className="p-1.5 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400"
                  onClick={() => onDeleteContent(activeTab, item.id)}
                  title="Delete"
                >
                  <FaTrash />
                </button>
              </div>
            </div>
          ))}
          
          {filteredContent().length === 0 && (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400">
              No {activeTab} found matching your search.
            </div>
          )}
        </div>
        
        {/* View All Link */}
        <div className="mt-4 text-center">
          <Link 
            to={`/admin/${activeTab}`}
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center"
          >
            Manage All {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            <FaArrowRight className="ml-1 text-xs" />
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

export default ContentManagementWidget;
