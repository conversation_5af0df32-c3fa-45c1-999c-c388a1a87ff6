import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  FaChartLine, FaArrowRight, FaGraduationCap, FaCalendarAlt, 
  FaClock, FaTrophy, FaChartBar, FaChevronDown, FaChevronUp 
} from 'react-icons/fa';

/**
 * LearningAnalyticsWidget Component
 * 
 * Displays detailed analytics for learning progress and engagement.
 * Provides insights into module completion, time spent, and skill development.
 */
const LearningAnalyticsWidget = ({ 
  learningStats = {},
  timeRange = 'month',
  onTimeRangeChange,
  showDetails = false,
  onToggleDetails
}) => {
  // Default stats if not provided
  const stats = {
    modulesCompleted: learningStats.modulesCompleted || 0,
    totalModules: learningStats.totalModules || 0,
    timeSpent: learningStats.timeSpent || 0, // in minutes
    averageScore: learningStats.averageScore || 0,
    streak: learningStats.streak || 0,
    topSkills: learningStats.topSkills || [],
    recentActivity: learningStats.recentActivity || [],
    progressByCategory: learningStats.progressByCategory || [],
    ...learningStats
  };
  
  // Calculate completion percentage
  const completionPercentage = stats.totalModules > 0 
    ? Math.round((stats.modulesCompleted / stats.totalModules) * 100) 
    : 0;
  
  // Format time spent
  const formatTimeSpent = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };
  
  // Get time range label
  const getTimeRangeLabel = () => {
    switch(timeRange) {
      case 'week':
        return 'This Week';
      case 'month':
        return 'This Month';
      case 'year':
        return 'This Year';
      case 'all':
        return 'All Time';
      default:
        return 'This Month';
    }
  };
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaChartLine className="mr-2 text-blue-600 dark:text-blue-400" />
            Learning Analytics
          </h3>
          
          {/* Time range selector */}
          <div className="relative">
            <select
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              value={timeRange}
              onChange={(e) => onTimeRangeChange && onTimeRangeChange(e.target.value)}
            >
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="year">This Year</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>
        
        {/* Main stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{completionPercentage}%</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Completion</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.modulesCompleted}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Modules Completed</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatTimeSpent(stats.timeSpent)}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Time Spent</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{stats.streak}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Day Streak</div>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Progress</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {stats.modulesCompleted}/{stats.totalModules} Modules
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full" 
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </div>
        
        {/* Top skills */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Top Skills</h4>
          <div className="space-y-2">
            {stats.topSkills.slice(0, 3).map((skill, index) => (
              <div key={index} className="flex items-center">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-2">
                  <div 
                    className={`h-2.5 rounded-full ${
                      index === 0 ? 'bg-blue-600' : index === 1 ? 'bg-green-600' : 'bg-purple-600'
                    }`}
                    style={{ width: `${skill.level}%` }}
                  ></div>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400 min-w-[60px] text-right">
                  {skill.name}
                </span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Recent activity */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Activity</h4>
          <div className="space-y-2">
            {stats.recentActivity.slice(0, 3).map((activity, index) => (
              <div 
                key={index}
                className="flex items-start p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="mr-3 mt-0.5">
                  {activity.type === 'completion' ? (
                    <FaTrophy className="text-yellow-500" />
                  ) : (
                    <FaGraduationCap className="text-blue-500" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-800 dark:text-gray-200">{activity.title}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                    <FaCalendarAlt className="mr-1" />
                    {activity.date}
                    {activity.duration && (
                      <span className="ml-2 flex items-center">
                        <FaClock className="mr-1" />
                        {activity.duration}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Toggle details button */}
        <button
          className="w-full flex items-center justify-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
          onClick={onToggleDetails}
        >
          {showDetails ? (
            <>
              Show Less <FaChevronUp className="ml-1" />
            </>
          ) : (
            <>
              Show More <FaChevronDown className="ml-1" />
            </>
          )}
        </button>
        
        {/* Detailed analytics */}
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
          >
            {/* Progress by category */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Progress by Category</h4>
              <div className="space-y-3">
                {stats.progressByCategory.map((category, index) => (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">{category.name}</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {category.completed}/{category.total} ({Math.round((category.completed / category.total) * 100)}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                      <div 
                        className="bg-blue-600 h-1.5 rounded-full" 
                        style={{ width: `${(category.completed / category.total) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Learning trends chart placeholder */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Learning Trends</h4>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 h-40 flex items-center justify-center">
                <FaChartBar className="text-gray-400 text-4xl" />
              </div>
              <p className="text-xs text-center text-gray-500 dark:text-gray-400 mt-2">
                Learning activity for {getTimeRangeLabel().toLowerCase()}
              </p>
            </div>
            
            {/* View full analytics link */}
            <div className="text-center">
              <Link 
                to="/analytics/learning" 
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center"
              >
                View Full Analytics
                <FaArrowRight className="ml-1 text-xs" />
              </Link>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default LearningAnalyticsWidget;
