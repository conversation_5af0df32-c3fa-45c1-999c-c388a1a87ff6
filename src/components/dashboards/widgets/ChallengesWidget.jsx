import React from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { FaGamepad, FaArrowRight, FaLock, FaCrown, FaTrophy, FaCode, FaNetworkWired, FaShieldAlt } from 'react-icons/fa';
import { SUBSCRIPTION_TIERS } from '../../../config/subscriptionTiers';

/**
 * ChallengesWidget Component
 * 
 * Displays the user's challenge progress and provides quick access to challenges.
 * Adapts based on subscription level to show appropriate content and upgrade prompts.
 */
const ChallengesWidget = ({ 
  completedChallenges = 0,
  totalChallenges = 0,
  recentChallenges = [],
  recommendedChallenges = [],
  subscriptionLevel = SUBSCRIPTION_TIERS.FREE,
  onUpgrade
}) => {
  const navigate = useNavigate();
  
  // Calculate completion percentage
  const completionPercentage = totalChallenges > 0 
    ? Math.round((completedChallenges / totalChallenges) * 100) 
    : 0;
  
  // Determine if user has limited access based on subscription
  const hasLimitedAccess = subscriptionLevel === SUBSCRIPTION_TIERS.FREE;
  
  // Get challenge icon based on category
  const getChallengeIcon = (category) => {
    switch(category) {
      case 'web':
        return <FaCode className="text-purple-500" />;
      case 'network':
        return <FaNetworkWired className="text-blue-500" />;
      case 'security':
        return <FaShieldAlt className="text-green-500" />;
      default:
        return <FaGamepad className="text-gray-500" />;
    }
  };
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaGamepad className="mr-2 text-blue-600 dark:text-blue-400" />
            Challenges
          </h3>
          <Link 
            to="/global-challenges" 
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center"
          >
            View All
            <FaArrowRight className="ml-1 text-xs" />
          </Link>
        </div>
        
        {/* Challenge Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{completedChallenges}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Completed</div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{totalChallenges}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Total Available</div>
          </div>
        </div>
        
        {/* Completion Progress */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Completion Progress</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{completionPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
            <div 
              className="bg-green-600 h-2.5 rounded-full" 
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </div>
        
        {/* Recent Challenges */}
        {recentChallenges && recentChallenges.length > 0 ? (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Challenges</h4>
            <div className="space-y-2">
              {recentChallenges.slice(0, 2).map((challenge, index) => (
                <div 
                  key={index}
                  className="flex items-center p-2 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                  onClick={() => navigate(`/global-challenges?challenge=${challenge.id}`)}
                >
                  <div className="mr-3">
                    {getChallengeIcon(challenge.category)}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-800 dark:text-gray-200">{challenge.title}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{challenge.difficulty}</div>
                  </div>
                  {challenge.completed && (
                    <FaTrophy className="text-yellow-500 ml-2" />
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="mb-4 text-center py-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Start solving challenges today!
            </p>
            <button
              className="mt-2 px-4 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
              onClick={() => navigate('/global-challenges')}
            >
              Browse Challenges
            </button>
          </div>
        )}
        
        {/* Recommended Challenge */}
        {recommendedChallenges && recommendedChallenges.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recommended For You</h4>
            <div 
              className="bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-lg p-3 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors cursor-pointer"
              onClick={() => navigate(`/global-challenges?challenge=${recommendedChallenges[0].id}`)}
            >
              <div className="flex items-center">
                <div className="mr-3">
                  {getChallengeIcon(recommendedChallenges[0].category)}
                </div>
                <div>
                  <h5 className="font-medium text-gray-900 dark:text-white">{recommendedChallenges[0].title}</h5>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {recommendedChallenges[0].difficulty} • {recommendedChallenges[0].estimatedTime}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Upgrade Prompt for Free Users */}
        {hasLimitedAccess && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between">
              <div>
                <span className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                  <FaCrown className="mr-1 text-yellow-500" />
                  Premium Challenges
                </span>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Unlock advanced challenges and competitions
                </p>
              </div>
              <button
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                onClick={onUpgrade}
              >
                Upgrade
              </button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ChallengesWidget;
