import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { 
  FaUsers, FaArrowRight, FaUserGraduate, FaTrophy, FaChartBar, 
  FaChevronDown, FaChevronUp, FaSort, FaSortUp, FaSortDown,
  FaUserPlus, FaFileExport, FaFilter
} from 'react-icons/fa';

/**
 * TeamPerformanceWidget Component
 * 
 * Business dashboard widget for tracking team performance across learning and challenges.
 * Provides insights into team member progress, skill development, and challenge success.
 */
const TeamPerformanceWidget = ({ 
  teamMembers = [],
  teamStats = {},
  onAssignLearning,
  onViewMemberProgress,
  onExportReport,
  showDetails = false,
  onToggleDetails
}) => {
  const navigate = useNavigate();
  const [sortField, setSortField] = useState('progress');
  const [sortDirection, setSortDirection] = useState('desc');
  const [filterRole, setFilterRole] = useState('all');
  
  // Handle sort change
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // Sort team members
  const sortedMembers = [...teamMembers].sort((a, b) => {
    let comparison = 0;
    
    switch(sortField) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'progress':
        comparison = (a.progress || 0) - (b.progress || 0);
        break;
      case 'modules':
        comparison = (a.completedModules || 0) - (b.completedModules || 0);
        break;
      case 'challenges':
        comparison = (a.completedChallenges || 0) - (b.completedChallenges || 0);
        break;
      case 'points':
        comparison = (a.points || 0) - (b.points || 0);
        break;
      default:
        comparison = 0;
    }
    
    return sortDirection === 'asc' ? comparison : -comparison;
  });
  
  // Filter team members by role
  const filteredMembers = filterRole === 'all' 
    ? sortedMembers 
    : sortedMembers.filter(member => member.role === filterRole);
  
  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="ml-1 text-gray-400" />;
    return sortDirection === 'asc' ? <FaSortUp className="ml-1 text-blue-600" /> : <FaSortDown className="ml-1 text-blue-600" />;
  };
  
  // Get unique roles from team members
  const roles = [...new Set(teamMembers.map(member => member.role))];
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaUsers className="mr-2 text-blue-600 dark:text-blue-400" />
            Team Performance
          </h3>
          
          <div className="flex space-x-2">
            <button
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg"
              onClick={onExportReport}
              title="Export Report"
            >
              <FaFileExport />
            </button>
            <button
              className="text-sm bg-blue-600 text-white px-3 py-2 rounded-lg flex items-center hover:bg-blue-700 transition-colors"
              onClick={onAssignLearning}
            >
              <FaUserPlus className="mr-1" />
              Assign
            </button>
          </div>
        </div>
        
        {/* Team Stats Overview */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{teamStats.averageProgress || 0}%</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Avg. Progress</div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{teamStats.totalModulesCompleted || 0}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Modules Completed</div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{teamStats.totalChallengesCompleted || 0}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Challenges Completed</div>
          </div>
        </div>
        
        {/* Filters */}
        <div className="flex items-center justify-between mb-4">
          <div className="relative">
            <select
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 pr-8"
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
            >
              <option value="all">All Roles</option>
              {roles.map((role, index) => (
                <option key={index} value={role}>{role}</option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <FaFilter className="text-gray-400" />
            </div>
          </div>
          
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing {filteredMembers.length} of {teamMembers.length} members
          </div>
        </div>
        
        {/* Team Members Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th 
                  scope="col" 
                  className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center">
                    Member {getSortIcon('name')}
                  </div>
                </th>
                <th 
                  scope="col" 
                  className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('progress')}
                >
                  <div className="flex items-center">
                    Progress {getSortIcon('progress')}
                  </div>
                </th>
                <th 
                  scope="col" 
                  className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('modules')}
                >
                  <div className="flex items-center">
                    Modules {getSortIcon('modules')}
                  </div>
                </th>
                <th 
                  scope="col" 
                  className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('challenges')}
                >
                  <div className="flex items-center">
                    Challenges {getSortIcon('challenges')}
                  </div>
                </th>
                <th 
                  scope="col" 
                  className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('points')}
                >
                  <div className="flex items-center">
                    Points {getSortIcon('points')}
                  </div>
                </th>
                <th scope="col" className="relative px-3 py-2">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredMembers.slice(0, 5).map((member, index) => (
                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-3 py-2 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                        {member.avatar ? (
                          <img src={member.avatar} alt={member.name} className="h-8 w-8 rounded-full" />
                        ) : (
                          <FaUserGraduate className="text-gray-500 dark:text-gray-400" />
                        )}
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">{member.name}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">{member.role}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${member.progress || 0}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">{member.progress || 0}%</span>
                    </div>
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {member.completedModules || 0}/{member.totalModules || 0}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {member.completedChallenges || 0}/{member.totalChallenges || 0}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap">
                    <div className="flex items-center">
                      <FaTrophy className="text-yellow-500 mr-1" />
                      <span className="text-sm text-gray-500 dark:text-gray-400">{member.points || 0}</span>
                    </div>
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      onClick={() => onViewMemberProgress(member.id)}
                    >
                      View
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredMembers.length === 0 && (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
            No team members found matching your filter.
          </div>
        )}
        
        {/* Toggle details button */}
        <button
          className="w-full flex items-center justify-center text-sm text-blue-600 dark:text-blue-400 hover:underline mt-4"
          onClick={onToggleDetails}
        >
          {showDetails ? (
            <>
              Show Less <FaChevronUp className="ml-1" />
            </>
          ) : (
            <>
              Show More <FaChevronDown className="ml-1" />
            </>
          )}
        </button>
        
        {/* Detailed analytics */}
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
          >
            {/* Team performance chart placeholder */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Team Performance Trends</h4>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 h-40 flex items-center justify-center">
                <FaChartBar className="text-gray-400 text-4xl" />
              </div>
              <p className="text-xs text-center text-gray-500 dark:text-gray-400 mt-2">
                Team performance over time
              </p>
            </div>
            
            {/* Skill gap analysis */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Team Skill Analysis</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Top Skills</h5>
                  <ul className="space-y-1">
                    {(teamStats.topSkills || []).slice(0, 3).map((skill, index) => (
                      <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-center">
                        <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        {skill.name} ({skill.level}%)
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Skill Gaps</h5>
                  <ul className="space-y-1">
                    {(teamStats.skillGaps || []).slice(0, 3).map((skill, index) => (
                      <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-center">
                        <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                        {skill.name} ({skill.level}%)
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
            
            {/* View full team analytics link */}
            <div className="text-center">
              <Link 
                to="/team/analytics" 
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center"
              >
                View Full Team Analytics
                <FaArrowRight className="ml-1 text-xs" />
              </Link>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default TeamPerformanceWidget;
