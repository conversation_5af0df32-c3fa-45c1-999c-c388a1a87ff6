import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  FaLightbulb, FaGraduationCap, FaGamepad, FaServer, 
  FaArrowRight, FaCode, FaNetworkWired, FaShieldAlt, 
  FaDatabase, FaLaptopCode, FaChartLine, FaUserGraduate
} from 'react-icons/fa';

/**
 * RecommendationsWidget Component
 * 
 * Displays personalized recommendations for learning modules, challenges, and simulations.
 * Adapts based on user activity, interests, and subscription level.
 */
const RecommendationsWidget = ({ 
  recommendations = {},
  userSkills = {},
  subscriptionLevel,
  onUpgrade
}) => {
  const navigate = useNavigate();
  
  // Default recommendations if not provided
  const recs = {
    modules: recommendations.modules || [],
    challenges: recommendations.challenges || [],
    simulations: recommendations.simulations || [],
    careerPaths: recommendations.careerPaths || [],
    ...recommendations
  };
  
  // Get category icon
  const getCategoryIcon = (category) => {
    switch(category?.toLowerCase()) {
      case 'web':
        return <FaCode className="text-purple-500" />;
      case 'network':
        return <FaNetworkWired className="text-blue-500" />;
      case 'security':
        return <FaShieldAlt className="text-green-500" />;
      case 'database':
        return <FaDatabase className="text-yellow-500" />;
      default:
        return <FaLaptopCode className="text-gray-500" />;
    }
  };
  
  // Handle recommendation click
  const handleRecommendationClick = (type, id) => {
    switch(type) {
      case 'module':
        navigate(`/global-learn?module=${id}`);
        break;
      case 'challenge':
        navigate(`/global-challenges?challenge=${id}`);
        break;
      case 'simulation':
        navigate(`/global-start-hack?scenario=${id}`);
        break;
      case 'careerPath':
        navigate(`/career-paths/${id}`);
        break;
      default:
        break;
    }
  };
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaLightbulb className="mr-2 text-yellow-500" />
            Recommended for You
          </h3>
        </div>
        
        {/* Skill-based recommendation header */}
        <div className="mb-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-lg p-3">
          <div className="flex items-start">
            <FaChartLine className="text-blue-600 dark:text-blue-400 mt-1 mr-3" />
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Based on Your Skills</h4>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Recommendations tailored to your current skill levels and learning goals.
              </p>
              <div className="flex flex-wrap gap-2 mt-2">
                {Object.entries(userSkills).slice(0, 3).map(([skill, level], index) => (
                  <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                    {skill}: {level}%
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Learning modules recommendations */}
        {recs.modules.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
              <FaGraduationCap className="mr-1 text-blue-600 dark:text-blue-400" />
              Learning Modules
            </h4>
            <div className="space-y-2">
              {recs.modules.slice(0, 2).map((module, index) => (
                <div 
                  key={index}
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                  onClick={() => handleRecommendationClick('module', module.id)}
                >
                  <div className="flex items-center">
                    <div className="mr-3">
                      {getCategoryIcon(module.category)}
                    </div>
                    <div className="flex-1">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white">{module.title}</h5>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {module.description}
                      </p>
                    </div>
                    <FaArrowRight className="text-gray-400 dark:text-gray-500 ml-2" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Challenges recommendations */}
        {recs.challenges.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
              <FaGamepad className="mr-1 text-green-600 dark:text-green-400" />
              Challenges
            </h4>
            <div className="space-y-2">
              {recs.challenges.slice(0, 2).map((challenge, index) => (
                <div 
                  key={index}
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                  onClick={() => handleRecommendationClick('challenge', challenge.id)}
                >
                  <div className="flex items-center">
                    <div className="mr-3">
                      {getCategoryIcon(challenge.category)}
                    </div>
                    <div className="flex-1">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white">{challenge.title}</h5>
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs mr-2">
                          {challenge.difficulty}
                        </span>
                        <span>{challenge.estimatedTime}</span>
                      </div>
                    </div>
                    <FaArrowRight className="text-gray-400 dark:text-gray-500 ml-2" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Simulations recommendations (for business users) */}
        {recs.simulations.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
              <FaServer className="mr-1 text-purple-600 dark:text-purple-400" />
              Simulations
            </h4>
            <div className="space-y-2">
              {recs.simulations.slice(0, 1).map((simulation, index) => (
                <div 
                  key={index}
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                  onClick={() => handleRecommendationClick('simulation', simulation.id)}
                >
                  <div className="flex items-center">
                    <div className="mr-3">
                      {getCategoryIcon(simulation.category)}
                    </div>
                    <div className="flex-1">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white">{simulation.title}</h5>
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs mr-2">
                          {simulation.difficulty}
                        </span>
                        <span>{simulation.estimatedTime}</span>
                      </div>
                    </div>
                    <FaArrowRight className="text-gray-400 dark:text-gray-500 ml-2" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Career path recommendations */}
        {recs.careerPaths.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
              <FaUserGraduate className="mr-1 text-indigo-600 dark:text-indigo-400" />
              Career Paths
            </h4>
            <div className="space-y-2">
              {recs.careerPaths.slice(0, 1).map((path, index) => (
                <div 
                  key={index}
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                  onClick={() => handleRecommendationClick('careerPath', path.id)}
                >
                  <div className="flex items-center">
                    <div className="mr-3">
                      <FaUserGraduate className="text-indigo-500" />
                    </div>
                    <div className="flex-1">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white">{path.title}</h5>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {path.description}
                      </p>
                    </div>
                    <FaArrowRight className="text-gray-400 dark:text-gray-500 ml-2" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* No recommendations message */}
        {recs.modules.length === 0 && recs.challenges.length === 0 && recs.simulations.length === 0 && recs.careerPaths.length === 0 && (
          <div className="text-center py-6 text-gray-500 dark:text-gray-400">
            <FaLightbulb className="mx-auto text-2xl text-yellow-500 mb-2" />
            <p>Complete more modules and challenges to get personalized recommendations.</p>
            <button
              className="mt-4 px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
              onClick={() => navigate('/global-learn')}
            >
              Explore Learning Modules
            </button>
          </div>
        )}
        
        {/* View all recommendations button */}
        {(recs.modules.length > 0 || recs.challenges.length > 0 || recs.simulations.length > 0 || recs.careerPaths.length > 0) && (
          <button
            className="w-full mt-2 flex items-center justify-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
            onClick={() => navigate('/recommendations')}
          >
            View All Recommendations
            <FaArrowRight className="ml-1 text-xs" />
          </button>
        )}
      </div>
    </motion.div>
  );
};

export default RecommendationsWidget;
