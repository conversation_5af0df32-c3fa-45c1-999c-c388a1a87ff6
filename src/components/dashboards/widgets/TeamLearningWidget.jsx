import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { 
  FaUsers, FaArrowRight, FaGraduationCap, FaUserGraduate, 
  FaChartBar, FaSort, FaSortUp, FaSortDown, FaUserPlus 
} from 'react-icons/fa';

/**
 * TeamLearningWidget Component
 * 
 * Business dashboard widget for tracking team learning progress.
 * Provides insights into team member progress and skill development.
 */
const TeamLearningWidget = ({ 
  teamMembers = [],
  teamProgress = {},
  onAssignLearning,
  onViewMemberProgress
}) => {
  const navigate = useNavigate();
  const [sortField, setSortField] = useState('progress');
  const [sortDirection, setSortDirection] = useState('desc');
  
  // Handle sort change
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // Sort team members
  const sortedMembers = [...teamMembers].sort((a, b) => {
    let comparison = 0;
    
    switch(sortField) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'progress':
        comparison = (a.progress || 0) - (b.progress || 0);
        break;
      case 'modules':
        comparison = (a.completedModules || 0) - (b.completedModules || 0);
        break;
      default:
        comparison = 0;
    }
    
    return sortDirection === 'asc' ? comparison : -comparison;
  });
  
  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="ml-1 text-gray-400" />;
    return sortDirection === 'asc' ? <FaSortUp className="ml-1 text-blue-600" /> : <FaSortDown className="ml-1 text-blue-600" />;
  };
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaUsers className="mr-2 text-blue-600 dark:text-blue-400" />
            Team Learning
          </h3>
          <button
            className="text-sm bg-blue-600 text-white px-3 py-1 rounded flex items-center hover:bg-blue-700 transition-colors"
            onClick={onAssignLearning}
          >
            <FaUserPlus className="mr-1" />
            Assign Learning
          </button>
        </div>
        
        {/* Team Progress Overview */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{teamProgress.averageProgress || 0}%</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Avg. Progress</div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{teamProgress.totalCompleted || 0}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Modules Completed</div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{teamMembers.length}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Team Members</div>
          </div>
        </div>
        
        {/* Team Members Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th 
                  scope="col" 
                  className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center">
                    Member {getSortIcon('name')}
                  </div>
                </th>
                <th 
                  scope="col" 
                  className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('progress')}
                >
                  <div className="flex items-center">
                    Progress {getSortIcon('progress')}
                  </div>
                </th>
                <th 
                  scope="col" 
                  className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('modules')}
                >
                  <div className="flex items-center">
                    Modules {getSortIcon('modules')}
                  </div>
                </th>
                <th scope="col" className="relative px-3 py-2">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {sortedMembers.slice(0, 5).map((member, index) => (
                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-3 py-2 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                        {member.avatar ? (
                          <img src={member.avatar} alt={member.name} className="h-8 w-8 rounded-full" />
                        ) : (
                          <FaUserGraduate className="text-gray-500 dark:text-gray-400" />
                        )}
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">{member.name}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">{member.role}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${member.progress || 0}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">{member.progress || 0}%</span>
                    </div>
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {member.completedModules || 0}/{member.totalModules || 0}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      onClick={() => onViewMemberProgress(member.id)}
                    >
                      View
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {teamMembers.length === 0 && (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
            No team members found.
          </div>
        )}
        
        {/* View All Link */}
        {teamMembers.length > 5 && (
          <div className="mt-4 text-center">
            <Link 
              to="/team/learning"
              className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center"
            >
              View All Team Members
              <FaArrowRight className="ml-1 text-xs" />
            </Link>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default TeamLearningWidget;
