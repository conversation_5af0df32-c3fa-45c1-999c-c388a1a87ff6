import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  FaChartLine, FaArrowRight, FaGamepad, FaCalendarAlt, 
  FaClock, FaTrophy, FaChartBar, FaChevronDown, FaChevronUp,
  FaCode, FaNetworkWired, FaShieldAlt, FaDatabase, FaLaptopCode
} from 'react-icons/fa';

/**
 * ChallengeAnalyticsWidget Component
 * 
 * Displays detailed analytics for challenge completion and performance.
 * Provides insights into challenge success rates, time spent, and skill development.
 */
const ChallengeAnalyticsWidget = ({ 
  challengeStats = {},
  timeRange = 'month',
  onTimeRangeChange,
  showDetails = false,
  onToggleDetails
}) => {
  // Default stats if not provided
  const stats = {
    challengesCompleted: challengeStats.challengesCompleted || 0,
    totalChallenges: challengeStats.totalChallenges || 0,
    timeSpent: challengeStats.timeSpent || 0, // in minutes
    averageScore: challengeStats.averageScore || 0,
    successRate: challengeStats.successRate || 0,
    pointsEarned: challengeStats.pointsEarned || 0,
    topCategories: challengeStats.topCategories || [],
    recentActivity: challengeStats.recentActivity || [],
    difficultyBreakdown: challengeStats.difficultyBreakdown || [],
    ...challengeStats
  };
  
  // Calculate completion percentage
  const completionPercentage = stats.totalChallenges > 0 
    ? Math.round((stats.challengesCompleted / stats.totalChallenges) * 100) 
    : 0;
  
  // Format time spent
  const formatTimeSpent = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };
  
  // Get time range label
  const getTimeRangeLabel = () => {
    switch(timeRange) {
      case 'week':
        return 'This Week';
      case 'month':
        return 'This Month';
      case 'year':
        return 'This Year';
      case 'all':
        return 'All Time';
      default:
        return 'This Month';
    }
  };
  
  // Get category icon
  const getCategoryIcon = (category) => {
    switch(category.toLowerCase()) {
      case 'web':
        return <FaCode className="text-purple-500" />;
      case 'network':
        return <FaNetworkWired className="text-blue-500" />;
      case 'security':
        return <FaShieldAlt className="text-green-500" />;
      case 'database':
        return <FaDatabase className="text-yellow-500" />;
      default:
        return <FaLaptopCode className="text-gray-500" />;
    }
  };
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaChartLine className="mr-2 text-blue-600 dark:text-blue-400" />
            Challenge Analytics
          </h3>
          
          {/* Time range selector */}
          <div className="relative">
            <select
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              value={timeRange}
              onChange={(e) => onTimeRangeChange && onTimeRangeChange(e.target.value)}
            >
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="year">This Year</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>
        
        {/* Main stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{completionPercentage}%</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Completion</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.challengesCompleted}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Challenges Completed</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{stats.successRate}%</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Success Rate</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{stats.pointsEarned}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Points Earned</div>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Progress</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {stats.challengesCompleted}/{stats.totalChallenges} Challenges
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full" 
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </div>
        
        {/* Top categories */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Top Categories</h4>
          <div className="space-y-2">
            {stats.topCategories.slice(0, 3).map((category, index) => (
              <div key={index} className="flex items-center">
                <div className="w-8 flex justify-center mr-2">
                  {getCategoryIcon(category.name)}
                </div>
                <div className="flex-1">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                    <div 
                      className={`h-2.5 rounded-full ${
                        index === 0 ? 'bg-blue-600' : index === 1 ? 'bg-green-600' : 'bg-purple-600'
                      }`}
                      style={{ width: `${category.percentage}%` }}
                    ></div>
                  </div>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 min-w-[60px] text-right">
                  {category.name} ({category.count})
                </span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Recent activity */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Activity</h4>
          <div className="space-y-2">
            {stats.recentActivity.slice(0, 3).map((activity, index) => (
              <div 
                key={index}
                className="flex items-start p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="mr-3 mt-0.5">
                  {activity.success ? (
                    <FaTrophy className="text-yellow-500" />
                  ) : (
                    <FaGamepad className="text-blue-500" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-800 dark:text-gray-200">{activity.title}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                    <FaCalendarAlt className="mr-1" />
                    {activity.date}
                    {activity.duration && (
                      <span className="ml-2 flex items-center">
                        <FaClock className="mr-1" />
                        {activity.duration}
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-xs font-medium">
                  {activity.points && (
                    <span className="px-2 py-0.5 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 rounded-full">
                      +{activity.points} pts
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Toggle details button */}
        <button
          className="w-full flex items-center justify-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
          onClick={onToggleDetails}
        >
          {showDetails ? (
            <>
              Show Less <FaChevronUp className="ml-1" />
            </>
          ) : (
            <>
              Show More <FaChevronDown className="ml-1" />
            </>
          )}
        </button>
        
        {/* Detailed analytics */}
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
          >
            {/* Difficulty breakdown */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Difficulty Breakdown</h4>
              <div className="space-y-3">
                {stats.difficultyBreakdown.map((difficulty, index) => (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">{difficulty.name}</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {difficulty.completed}/{difficulty.total} ({Math.round((difficulty.completed / difficulty.total) * 100)}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                      <div 
                        className={`h-1.5 rounded-full ${
                          difficulty.name === 'Beginner' ? 'bg-green-600' : 
                          difficulty.name === 'Intermediate' ? 'bg-yellow-600' : 'bg-red-600'
                        }`}
                        style={{ width: `${(difficulty.completed / difficulty.total) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Challenge trends chart placeholder */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Challenge Trends</h4>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 h-40 flex items-center justify-center">
                <FaChartBar className="text-gray-400 text-4xl" />
              </div>
              <p className="text-xs text-center text-gray-500 dark:text-gray-400 mt-2">
                Challenge activity for {getTimeRangeLabel().toLowerCase()}
              </p>
            </div>
            
            {/* View full analytics link */}
            <div className="text-center">
              <Link 
                to="/analytics/challenges" 
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center"
              >
                View Full Analytics
                <FaArrowRight className="ml-1 text-xs" />
              </Link>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default ChallengeAnalyticsWidget;
