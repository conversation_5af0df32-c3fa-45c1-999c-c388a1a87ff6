import React from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { FaGraduationCap, FaArrowRight, FaLock, FaCrown } from 'react-icons/fa';
import { SUBSCRIPTION_TIERS } from '../../../config/subscriptionTiers';

/**
 * LearningProgressWidget Component
 * 
 * Displays the user's learning progress and provides quick access to continue learning.
 * Adapts based on subscription level to show appropriate content and upgrade prompts.
 */
const LearningProgressWidget = ({ 
  userProgress = {}, 
  recentModules = [],
  subscriptionLevel = SUBSCRIPTION_TIERS.FREE,
  onUpgrade
}) => {
  const navigate = useNavigate();
  
  // Calculate overall progress percentage
  const calculateOverallProgress = () => {
    if (!userProgress.totalModules || userProgress.totalModules === 0) return 0;
    return Math.round((userProgress.completedModules / userProgress.totalModules) * 100);
  };
  
  const overallProgress = calculateOverallProgress();
  
  // Get the most recent module to continue
  const continueModule = recentModules && recentModules.length > 0 ? recentModules[0] : null;
  
  // Determine if user has limited access based on subscription
  const hasLimitedAccess = subscriptionLevel === SUBSCRIPTION_TIERS.FREE;
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaGraduationCap className="mr-2 text-blue-600 dark:text-blue-400" />
            Learning Progress
          </h3>
          <Link 
            to="/global-learn" 
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center"
          >
            View All
            <FaArrowRight className="ml-1 text-xs" />
          </Link>
        </div>
        
        {/* Overall Progress */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Progress</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{overallProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full" 
              style={{ width: `${overallProgress}%` }}
            ></div>
          </div>
        </div>
        
        {/* Continue Learning Section */}
        {continueModule ? (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Continue Learning</h4>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
              onClick={() => navigate(`/global-learn?module=${continueModule.id}`)}
            >
              <h5 className="font-medium text-gray-900 dark:text-white">{continueModule.title}</h5>
              <div className="flex justify-between items-center mt-2">
                <div className="flex-1 h-1.5 bg-gray-200 dark:bg-gray-600 rounded-full mr-4">
                  <div 
                    className="h-1.5 bg-blue-600 rounded-full" 
                    style={{ width: `${continueModule.progress || 0}%` }}
                  ></div>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">{continueModule.progress || 0}%</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-4 text-center py-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Start your learning journey today!
            </p>
            <button
              className="mt-2 px-4 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
              onClick={() => navigate('/global-learn')}
            >
              Browse Modules
            </button>
          </div>
        )}
        
        {/* Recent Modules */}
        {recentModules && recentModules.length > 1 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Modules</h4>
            <div className="space-y-2">
              {recentModules.slice(1, 3).map((module, index) => (
                <div 
                  key={index}
                  className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                  onClick={() => navigate(`/global-learn?module=${module.id}`)}
                >
                  <span className="text-sm text-gray-800 dark:text-gray-200 truncate">{module.title}</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">{module.progress || 0}%</span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Upgrade Prompt for Free Users */}
        {hasLimitedAccess && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between">
              <div>
                <span className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                  <FaCrown className="mr-1 text-yellow-500" />
                  Premium Content
                </span>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Unlock all learning modules and advanced features
                </p>
              </div>
              <button
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                onClick={onUpgrade}
              >
                Upgrade
              </button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default LearningProgressWidget;
