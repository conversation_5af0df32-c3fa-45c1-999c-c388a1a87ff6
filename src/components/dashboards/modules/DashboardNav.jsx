import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaChevronDown, FaChevronRight } from 'react-icons/fa';
import { navSections } from '../../../config/navigation';

function DashboardNav({ selectedSection, setSelectedSection }) {
  const [expandedSections, setExpandedSections] = useState({
    learn: true,
    startHack: true,
    attack: true,
    defence: true,
    malware: true,
    achievements: true,
    ctf: true,
    referrals: true,
    help: true
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const SectionHeader = ({ title, section, uppercase = true }) => (
    <button
      onClick={() => toggleSection(section)}
      className="w-full flex items-center justify-between text-xs text-gray-400 mb-2 hover:text-gray-300 transition-colors"
    >
      <span className={uppercase ? 'uppercase tracking-wider font-bold' : ''}>
        {title}
      </span>
      {expandedSections[section] ? (
        <FaChevronDown className="text-xs" />
      ) : (
        <FaChevronRight className="text-xs" />
      )}
    </button>
  );

  const NavItem = ({ item, indent = false }) => (
    <motion.button
      initial={false}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      onClick={() => setSelectedSection(item.id)}
      className={`w-full flex items-center gap-3 px-3 py-2 text-sm transition-colors ${
        indent ? 'pl-6' : ''
      } ${
        selectedSection === item.id
          ? 'text-primary bg-primary/10 rounded-lg'
          : 'text-gray-400 hover:text-white hover:bg-white/5 rounded-lg'
      }`}
    >
      <item.icon className="text-lg" />
      <span className="text-left">{item.label}</span>
    </motion.button>
  );

  return (
    <div className="p-4 space-y-6">
      {/* Learn Section */}
      <div>
        <SectionHeader title="Learn" section="learn" />
        {expandedSections.learn && (
          <div className="space-y-1">
            {navSections.learn.map(item => (
              <NavItem key={item.id} item={item} />
            ))}
          </div>
        )}
      </div>

      {/* Start Hack Section */}
      <div>
        <SectionHeader title="Start Hack" section="startHack" />
        {expandedSections.startHack && (
          <div className="space-y-4">
            {/* Attack Section */}
            <div>
              <SectionHeader title="Attack" section="attack" uppercase={false} />
              {expandedSections.attack && (
                <div className="space-y-1">
                  {navSections.startHack.attack.map(item => (
                    <NavItem key={item.id} item={item} indent />
                  ))}
                </div>
              )}
            </div>

            {/* Defence Section */}
            <div>
              <SectionHeader title="Defence" section="defence" uppercase={false} />
              {expandedSections.defence && (
                <div className="space-y-1">
                  {navSections.startHack.defence.map(item => (
                    <NavItem key={item.id} item={item} indent />
                  ))}
                </div>
              )}
            </div>

            {/* Malware Section */}
            <div>
              <SectionHeader title="Malware" section="malware" uppercase={false} />
              {expandedSections.malware && (
                <div className="space-y-1">
                  {navSections.startHack.malware.map(item => (
                    <NavItem key={item.id} item={item} indent />
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Achievements Section */}
      <div>
        <SectionHeader title="Achievements" section="achievements" />
        {expandedSections.achievements && (
          <div className="space-y-1">
            {navSections.achievements.map(item => (
              <NavItem key={item.id} item={item} />
            ))}
          </div>
        )}
      </div>

      {/* CTF Section */}
      <div>
        <SectionHeader title="CTF" section="ctf" />
        {expandedSections.ctf && (
          <div className="space-y-1">
            {navSections.ctf.map(item => (
              <NavItem key={item.id} item={item} />
            ))}
          </div>
        )}
      </div>

      {/* Referrals Section */}
      <div>
        <SectionHeader title="Referrals" section="referrals" />
        {expandedSections.referrals && (
          <div className="space-y-1">
            {navSections.referrals.map(item => (
              <NavItem key={item.id} item={item} />
            ))}
          </div>
        )}
      </div>

      {/* Help Section */}
      <div>
        <SectionHeader title="Help" section="help" />
        {expandedSections.help && (
          <div className="space-y-1">
            {navSections.help.map(item => (
              <NavItem key={item.id} item={item} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default DashboardNav;