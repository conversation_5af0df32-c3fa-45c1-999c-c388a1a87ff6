import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FaBars, FaChevronRight, FaChevronDown, FaSignOutAlt, FaUserTie, FaCrown } from 'react-icons/fa';
import UpgradeButton from '../../subscription/UpgradeButton';
import { useSubscription, SUBSCRIPTION_LEVELS } from '../../../contexts/SubscriptionContext';
import { navSections } from '../../../config/navigation';

function DashboardSidebar({ profile, selectedSection, setSelectedSection, onSignOut, subscriptionLevel }) {
  // Use the passed subscription level or get it from context
  const { subscriptionLevel: contextSubscriptionLevel } = useSubscription();
  const userSubscriptionLevel = subscriptionLevel || contextSubscriptionLevel;
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    learn: true,
    startHack: true,
    attack: true,
    defence: true,
    malware: true,
    achievements: true,
    ctf: true,
    referrals: true,
    help: true
  });
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      const isMobileView = window.innerWidth < 1024;
      setIsMobile(isMobileView);
      if (isMobileView) {
        setIsCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  const toggleCollapse = () => {
    setIsCollapsed(prev => !prev);
  };

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const renderNavItems = (items, sectionKey) => {
    if (!Array.isArray(items)) return null;

    // Check if this is a premium-only section
    const isPremiumSection = sectionKey === 'learn' || sectionKey === 'startHack';
    const hasPremiumAccess = userSubscriptionLevel === SUBSCRIPTION_LEVELS.PREMIUM ||
                            userSubscriptionLevel === SUBSCRIPTION_LEVELS.BUSINESS;

    return items.map((item) => {
      // For premium sections, show a premium badge for non-premium users
      const isPremiumItem = isPremiumSection && !hasPremiumAccess;

      return (
        <button
          key={item.id}
          onClick={() => {
            if (isPremiumItem) {
              // Navigate to pricing page for premium features
              window.location.href = '/pricing';
              return;
            }

            setSelectedSection(item.id);
            if (isMobile) {
              setIsCollapsed(true);
            }
          }}
          className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors ${
            selectedSection === item.id
              ? 'bg-[#88cc14]/10 text-[#88cc14]'
              : isPremiumItem
                ? 'text-gray-600 hover:text-gray-500 cursor-not-allowed'
                : 'text-gray-400 hover:text-white hover:bg-white/5'
          }`}
        >
          <item.icon className={`text-lg flex-shrink-0 ${isPremiumItem ? 'text-gray-600' : ''}`} />
          {!isCollapsed && (
            <div className="flex items-center justify-between w-full">
              <span className="whitespace-nowrap overflow-hidden text-ellipsis">
                {item.label}
              </span>
              {isPremiumItem && (
                <span className="text-xs bg-yellow-500/20 text-yellow-500 px-1.5 py-0.5 rounded">
                  PRO
                </span>
              )}
            </div>
          )}
        </button>
      );
    });
  };

  const renderSubsections = (section, sectionKey) => {
    if (!section) return null;

    return Object.entries(section).map(([key, items]) => (
      <div key={key} className="space-y-1">
        <button
          onClick={() => toggleSection(key)}
          className="w-full flex items-center justify-between text-xs text-gray-400 mb-2 hover:text-gray-300 transition-colors px-2"
        >
          <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
          {!isCollapsed && (
            <FaChevronRight
              className={`text-xs transform transition-transform duration-200 ${
                expandedSections[key] ? 'rotate-90' : ''
              }`}
            />
          )}
        </button>
        {expandedSections[key] && !isCollapsed && (
          <div className="ml-4 space-y-1">
            {renderNavItems(Array.isArray(items) ? items : Object.values(items).flat(), sectionKey)}
          </div>
        )}
      </div>
    ));
  };

  return (
    <>
      {/* Backdrop for mobile */}
      {!isCollapsed && isMobile && (
        <div
          className="fixed inset-0 bg-black/50 z-20"
          onClick={toggleCollapse}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed lg:static inset-y-0 left-0 bg-[#1A1F35] border-r border-gray-800 transition-all duration-300 ease-in-out ${
          isCollapsed ? 'w-20' : 'w-64'
        } ${
          isMobile && isCollapsed ? '-translate-x-full lg:translate-x-0' : 'translate-x-0'
        } z-30`}
      >
        {/* Logo */}
        <Link to="/" className="flex items-center justify-center h-16 border-b border-gray-800">
          <div className="relative">
            <div className="absolute inset-0 bg-[#88cc14]/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span className="font-bold text-2xl relative z-10">
              {isCollapsed ? (
                <span className="text-[#88cc14]">X</span>
              ) : (
                <>
                  <span className="text-[#88cc14]">X</span>
                  <span className="text-white">CERBERUS</span>
                </>
              )}
            </span>
          </div>
        </Link>

        {/* Toggle Button - Only show on desktop */}
        {!isMobile && (
          <button
            onClick={toggleCollapse}
            className="absolute -right-3 top-20 bg-[#1A1F35] border border-gray-800 rounded-full p-1.5 text-gray-400 hover:text-white transition-colors z-40"
          >
            <FaBars className="text-sm" />
          </button>
        )}

        {/* Navigation Menu */}
        <div className="h-[calc(100vh-4rem)] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-800 scrollbar-track-transparent">
          <nav className="p-4 space-y-6">
            {Object.entries(navSections).map(([sectionKey, items]) => (
              <div key={sectionKey}>
                <button
                  onClick={() => toggleSection(sectionKey)}
                  className="w-full flex items-center justify-between text-xs text-gray-400 mb-2 hover:text-gray-300 transition-colors"
                >
                  {!isCollapsed && (
                    <span className="uppercase tracking-wider font-bold">
                      {sectionKey.replace('_', ' ')}
                    </span>
                  )}
                  {!isCollapsed && (
                    <FaChevronRight
                      className={`text-xs transform transition-transform duration-200 ${
                        expandedSections[sectionKey] ? 'rotate-90' : ''
                      }`}
                    />
                  )}
                </button>

                <div className="space-y-1">
                  {typeof items === 'object' && !Array.isArray(items) ? (
                    renderSubsections(items, sectionKey)
                  ) : (
                    renderNavItems(items, sectionKey)
                  )}
                </div>
              </div>
            ))}
          </nav>
        </div>

        {/* Upgrade Button */}
        {!isCollapsed && (
          <div className="p-4">
            {userSubscriptionLevel === SUBSCRIPTION_LEVELS.FREE && (
              <UpgradeButton
                targetPlan="Premium"
                variant="primary"
                size="md"
                fullWidth
              >
                <div className="flex items-center gap-2">
                  <FaCrown />
                  <span>Upgrade to Premium</span>
                </div>
              </UpgradeButton>
            )}

            {userSubscriptionLevel === SUBSCRIPTION_LEVELS.PREMIUM && (
              <UpgradeButton
                targetPlan="Business"
                variant="secondary"
                size="md"
                fullWidth
              >
                <div className="flex items-center gap-2">
                  <FaUserTie />
                  <span>Upgrade to Business</span>
                </div>
              </UpgradeButton>
            )}
          </div>
        )}

        {/* Sign Out Button */}
        {!isCollapsed && (
          <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-800 bg-[#1A1F35]">
            <button
              onClick={onSignOut}
              className="w-full flex items-center gap-3 p-3 rounded-lg text-red-400 hover:bg-red-400/10 transition-colors"
            >
              <FaSignOutAlt />
              <span>Sign Out</span>
            </button>
          </div>
        )}
      </aside>
    </>
  );
}

export default DashboardSidebar;