import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaRegBell, FaSearch, FaUserCircle, FaChevronDown, FaCoins, FaWallet, FaCog, FaSignOutAlt } from 'react-icons/fa';

function DashboardHeader({ profile, subscription, coins, onSignOut }) {
  const [showProfileMenu, setShowProfileMenu] = useState(false);

  return (
    <div className="bg-[#1A1F35] border-b border-gray-800 sticky top-0 z-40">
      <div className="max-w-[2000px] mx-auto px-4 sm:px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left Side - Stats */}
          <div className="flex items-center gap-4">
            {/* Coins */}
            <div className="flex items-center gap-2 bg-[#88cc14]/10 px-3 py-1.5 rounded-lg">
              <FaCoins className="text-[#88cc14] text-sm" />
              <span className="text-[#88cc14] font-bold text-sm hidden sm:inline">
                {coins?.balance || 0} XC
              </span>
            </div>

            {/* Wallet - Hide on mobile */}
            <div className="hidden sm:flex items-center gap-2 bg-black/20 px-3 py-1.5 rounded-lg">
              <FaWallet className="text-gray-400 text-sm" />
              <span className="text-gray-400 text-sm">Wallet</span>
            </div>
          </div>
          
          {/* Right Side */}
          <div className="flex items-center gap-4">
            {/* Notifications */}
            <button className="relative p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-white/5">
              <FaRegBell />
              <span className="absolute top-1 right-1 w-2 h-2 bg-[#88cc14] rounded-full" />
            </button>

            {/* Profile Menu */}
            <div className="relative">
              <button
                onClick={() => setShowProfileMenu(!showProfileMenu)}
                className="flex items-center gap-3 hover:bg-white/5 p-2 rounded-lg transition-colors"
              >
                <div className="w-8 h-8 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile.username}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <FaUserCircle className="text-[#88cc14]" />
                  )}
                </div>
                <div className="text-left hidden sm:block">
                  <p className="text-sm font-medium text-white">{profile?.username || 'User'}</p>
                  <p className="text-xs text-gray-400">{subscription?.subscription_plan?.name || 'Free'} Plan</p>
                </div>
                <FaChevronDown className="text-gray-400 text-xs" />
              </button>

              {/* Profile Dropdown Menu */}
              <AnimatePresence>
                {showProfileMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="absolute right-0 mt-2 w-48 bg-[#1A1F35] border border-gray-800 rounded-lg shadow-xl overflow-hidden z-50"
                  >
                    <div className="p-2">
                      <button
                        onClick={() => {/* Handle settings */}}
                        className="w-full flex items-center gap-3 p-2 text-gray-400 hover:text-white hover:bg-white/5 rounded-lg transition-colors text-left"
                      >
                        <FaCog />
                        <span>Settings</span>
                      </button>

                      <button
                        onClick={onSignOut}
                        className="w-full flex items-center gap-3 p-2 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded-lg transition-colors text-left"
                      >
                        <FaSignOutAlt />
                        <span>Sign Out</span>
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DashboardHeader;