import React from 'react';
import { motion } from 'framer-motion';
import { FaGamepad, FaGraduationCap, FaCode, FaTrophy, FaCoins, FaChartLine, FaShieldAlt, FaServer } from 'react-icons/fa';
import { Link } from 'react-router-dom';

function DashboardContent({ selectedSection, profile, subscription, coins, challenges, completedChallenges, totalPoints }) {
  // Quick stats data
  const stats = [
    {
      icon: FaCoins,
      title: 'XCerberus Coins',
      value: coins?.balance || 0,
      suffix: ' XC',
      color: '#88cc14'
    },
    {
      icon: FaTrophy,
      title: 'Challenges Completed',
      value: completedChallenges || 0,
      color: '#88cc14'
    },
    {
      icon: FaChartLine,
      title: 'Total Points',
      value: totalPoints || 0,
      color: '#88cc14'
    }
  ];

  // Quick actions
  const quickActions = [
    {
      icon: FaGamepad,
      title: 'Start Hack',
      description: 'Begin a new hacking mission',
      link: '/global-start-hack'
    },
    {
      icon: FaGraduationCap,
      title: 'Learn',
      description: 'Access learning resources',
      link: '/global-learn'
    },
    {
      icon: FaCode,
      title: 'Challenges',
      description: 'Take on security challenges',
      link: '/global-challenges'
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-[2000px] mx-auto px-4 sm:px-6 py-6 sm:py-8"
    >
      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-4 sm:p-6"
          >
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                <stat.icon className="text-[#88cc14] text-xl" />
              </div>
              <div>
                <p className="text-gray-400 text-sm">{stat.title}</p>
                <p className="text-xl sm:text-2xl font-bold" style={{ color: stat.color }}>
                  {stat.value}{stat.suffix}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8">
        {quickActions.map((action, index) => (
          <Link
            key={index}
            to={action.link}
            className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-4 sm:p-6 hover:border-[#88cc14] transition-all duration-300 group"
          >
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                <action.icon className="text-[#88cc14] text-xl" />
              </div>
              <div>
                <h3 className="text-white font-bold">{action.title}</h3>
                <p className="text-gray-400 text-sm">{action.description}</p>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-4 sm:p-6">
        <h3 className="text-xl font-bold text-white mb-6">Recent Activity</h3>
        <div className="space-y-4">
          {challenges && challenges.length > 0 ? (
            challenges.slice(0, 5).map((activity, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-4 p-4 rounded-lg hover:bg-[#88cc14]/5"
              >
                <div className={`w-10 h-10 rounded-lg ${
                  activity.status === 'completed'
                    ? 'bg-[#88cc14]/10 text-[#88cc14]'
                    : 'bg-gray-800/50 text-gray-400'
                } flex items-center justify-center`}>
                  {activity.status === 'completed' ? <FaShieldAlt /> : <FaServer />}
                </div>
                <div className="flex-1">
                  <p className="text-white font-medium">
                    {activity.status === 'completed' ? 'Completed' : 'Started'} challenge: {activity.challenges?.title || 'Challenge'}
                  </p>
                  <p className="text-sm text-gray-400">
                    {new Date(activity.submission_time).toLocaleString()}
                  </p>
                </div>
                {activity.status === 'completed' && activity.points_earned > 0 && (
                  <div className="bg-[#88cc14]/10 text-[#88cc14] px-3 py-1 rounded-full text-sm">
                    +{activity.points_earned} points
                  </div>
                )}
              </motion.div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-400">
              <p>No recent activity</p>
              <Link to="/challenges" className="text-[#88cc14] hover:underline mt-2 inline-block">
                Start a challenge
              </Link>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}

export default DashboardContent;