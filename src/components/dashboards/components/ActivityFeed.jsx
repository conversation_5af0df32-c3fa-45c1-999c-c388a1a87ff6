import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaCode } from 'react-icons/fa';

function ActivityFeed({ title, activities, emptyMessage, emptyAction }) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
      <h3 className="text-xl font-bold text-gray-900 mb-6">{title}</h3>
      {activities && activities.length > 0 ? (
        <div className="space-y-4">
          {activities.map((activity, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center gap-4 p-4 rounded-lg hover:bg-gray-50"
            >
              <FaCode className={activity.status === 'completed' ? 'text-[#88cc14]' : 'text-gray-400'} />
              <div className="flex-1">
                <p className="text-gray-900">
                  {activity.status === 'completed' 
                    ? `Completed "${activity.challenges?.title || 'Challenge'}"` 
                    : `Started "${activity.challenges?.title || 'Challenge'}"`}
                </p>
                <p className="text-sm text-gray-500">
                  {new Date(activity.submission_time).toLocaleString()}
                </p>
              </div>
              {activity.status === 'completed' && (
                <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-medium">
                  +{activity.points_earned || 0} pts
                </div>
              )}
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>{emptyMessage}</p>
          {emptyAction && (
            <Link to={emptyAction.link} className="text-[#88cc14] hover:underline mt-2 inline-block">
              {emptyAction.text}
            </Link>
          )}
        </div>
      )}
    </div>
  );
}

export default ActivityFeed;