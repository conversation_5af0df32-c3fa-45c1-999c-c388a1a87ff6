import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>a<PERSON><PERSON>, Fa<PERSON>rown, FaSignOutAlt } from 'react-icons/fa';
import { motion } from 'framer-motion';

function DashboardSidebar({ profile, navItems, activeTab, onTabChange }) {
  const handleSignOut = () => {
    // <PERSON><PERSON> sign out logic
  };

  return (
    <div className="md:col-span-1">
      <div className="bg-white rounded-lg shadow-sm p-6">
        {/* Profile Header */}
        <div className="text-center mb-6">
          <div className="w-24 h-24 rounded-full bg-[#88cc14]/10 flex items-center justify-center mx-auto mb-4">
            {profile?.avatar_url ? (
              <img
                src={profile.avatar_url}
                alt={profile.username}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <FaUser className="text-[#88cc14] text-3xl" />
            )}
          </div>
          <h2 className="text-xl font-bold text-gray-900">{profile?.username || 'User'}</h2>
          <p className="text-gray-500">{profile?.email || '<EMAIL>'}</p>
        </div>

        {/* Navigation */}
        <nav className="space-y-2">
          {navItems.map(item => (
            <button
              key={item.id}
              onClick={() => onTabChange(item.id)}
              className={`w-full flex items-center gap-3 p-3 rounded-lg transition-colors ${
                activeTab === item.id
                  ? 'bg-[#88cc14]/10 text-[#88cc14]'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <item.icon />
              <span>{item.label}</span>
            </button>
          ))}
        </nav>

        <Link
          to="/pricing"
          className="w-full mt-6 bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors flex items-center justify-center gap-2"
        >
          <FaCrown />
          <span>Upgrade to Premium</span>
        </Link>

        <button
          onClick={handleSignOut}
          className="w-full mt-4 flex items-center gap-3 p-3 rounded-lg text-red-500 hover:bg-red-50 transition-colors"
        >
          <FaSignOutAlt />
          <span>Sign Out</span>
        </button>
      </div>
    </div>
  );
}

export default DashboardSidebar;