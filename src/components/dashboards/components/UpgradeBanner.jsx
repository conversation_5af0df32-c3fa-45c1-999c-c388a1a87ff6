import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaCrown } from 'react-icons/fa';

function UpgradeBanner() {
  return (
    <div className="bg-black rounded-lg shadow-sm p-8 text-white">
      <div className="flex flex-col md:flex-row items-center justify-between gap-6">
        <div className="flex items-center gap-6">
          <div className="w-16 h-16 bg-[#88cc14] rounded-full flex items-center justify-center">
            <FaCrown className="text-black text-2xl" />
          </div>
          <div>
            <h3 className="text-xl font-bold mb-2">Upgrade to Premium</h3>
            <p className="text-gray-400">Get unlimited access to all labs and challenges</p>
          </div>
        </div>
        <Link 
          to="/pricing" 
          className="bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
        >
          Upgrade Now
        </Link>
      </div>
    </div>
  );
}

export default UpgradeBanner;