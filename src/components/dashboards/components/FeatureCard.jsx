import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

function FeatureCard({ icon: Icon, title, description, linkTo, linkText, linkIcon: LinkIcon }) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center gap-4 mb-6">
        <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
          <Icon className="text-[#88cc14] text-xl" />
        </div>
        <div>
          <h3 className="text-lg font-bold text-gray-900">{title}</h3>
          <p className="text-gray-500">{description}</p>
        </div>
      </div>
      <Link
        to={linkTo}
        className="w-full bg-black text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-900 transition-colors flex items-center justify-center gap-2"
      >
        {LinkIcon && <LinkIcon />}
        <span>{linkText}</span>
      </Link>
    </div>
  );
}

export default FeatureCard;