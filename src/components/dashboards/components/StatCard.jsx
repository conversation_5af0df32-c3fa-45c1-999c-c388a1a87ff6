import React from 'react';
import { motion } from 'framer-motion';
import CountUp from 'react-countup';

function StatCard({ icon: Icon, title, value, suffix = '', color = '#88cc14' }) {
  return (
    <motion.div
      whileHover={{ scale: 1.03 }}
      className="bg-white rounded-lg shadow-sm p-6"
    >
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
          <Icon className="text-[#88cc14] text-xl" />
        </div>
        <div>
          <h3 className="text-gray-500">{title}</h3>
          <p className="text-2xl font-bold" style={{ color }}>
            <CountUp end={value} duration={1.5} suffix={suffix} />
          </p>
        </div>
      </div>
    </motion.div>
  );
}

export default StatCard;