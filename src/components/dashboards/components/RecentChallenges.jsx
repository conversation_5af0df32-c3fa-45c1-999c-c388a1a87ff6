import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaServer } from 'react-icons/fa';

function RecentChallenges({ challenges }) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
      <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Challenges</h2>
      <div className="space-y-4">
        {challenges && challenges.length > 0 ? (
          challenges.slice(0, 3).map((challenge, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-200 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-bold text-gray-900">{challenge.challenges?.title || 'Challenge'}</h3>
                <span className="text-sm text-gray-500">{new Date(challenge.submission_time).toLocaleString()}</span>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex-1 h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-[#88cc14] rounded-full"
                    style={{ width: challenge.status === 'completed' ? '100%' : '45%' }}
                  />
                </div>
                <span className="text-[#88cc14] font-bold">
                  {challenge.status === 'completed' ? '100%' : '45%'}
                </span>
              </div>
              <div className="mt-2 flex items-center gap-2 text-sm text-gray-500">
                <FaServer />
                <span>{challenge.challenges?.category || 'Challenge'}</span>
              </div>
            </motion.div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>No recent challenges</p>
            <Link to="/challenges" className="text-[#88cc14] hover:underline mt-2 inline-block">
              Start a challenge
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}

export default RecentChallenges;