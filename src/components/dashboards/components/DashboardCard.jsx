import React from 'react';
import { motion } from 'framer-motion';

function DashboardCard({ title, children, className = '', actions, icon: Icon }) {
  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
      className={`bg-white rounded-lg shadow-sm p-6 ${className}`}
    >
      {(title || Icon || actions) && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            {Icon && (
              <div className="w-8 h-8 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                <Icon className="text-[#88cc14]" />
              </div>
            )}
            {title && <h3 className="text-lg font-bold text-gray-900">{title}</h3>}
          </div>
          {actions && <div className="flex items-center gap-2">{actions}</div>}
        </div>
      )}
      {children}
    </motion.div>
  );
}

export default DashboardCard;