import React from 'react';
import { motion } from 'framer-motion';

function ChartCard({ title, subtitle, children, className = '', actions }) {
  return (
    <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-bold text-gray-900">{title}</h3>
          {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
        </div>
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>
      <div className="h-64">
        {children}
      </div>
    </div>
  );
}

export default ChartCard;