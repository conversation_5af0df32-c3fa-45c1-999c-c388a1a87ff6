import React from 'react';
import { motion } from 'framer-motion';

function TeamMemberCard({ name, role, avatar, active = true }) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
          {avatar ? (
            <img src={avatar} alt={name} className="w-full h-full rounded-full object-cover" />
          ) : (
            <span className="text-[#88cc14] font-bold">
              {name.charAt(0)}
            </span>
          )}
        </div>
        <div>
          <p className="font-medium text-gray-900">{name}</p>
          <p className="text-sm text-gray-500">{role}</p>
        </div>
      </div>
      <div className={`w-2 h-2 rounded-full ${
        active ? 'bg-green-500' : 'bg-gray-300'
      }`} />
    </div>
  );
}

export default TeamMemberCard;