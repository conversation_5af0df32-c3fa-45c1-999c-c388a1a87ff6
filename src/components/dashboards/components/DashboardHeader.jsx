import React from 'react';
import { motion } from 'framer-motion';
import { FaRegBell, FaSearch, FaUserCircle, FaChevronDown } from 'react-icons/fa';

function DashboardHeader({ title, subtitle, actions }) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {subtitle && <p className="text-gray-600">{subtitle}</p>}
        </div>
        
        {actions && (
          <div className="flex items-center gap-4 mt-4 md:mt-0">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
}

export default DashboardHeader;