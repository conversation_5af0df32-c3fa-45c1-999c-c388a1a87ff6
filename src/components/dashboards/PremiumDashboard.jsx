import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardHeader from './modules/DashboardHeader';
import DashboardSidebar from './modules/DashboardSidebar';
import DashboardContent from './modules/DashboardContent';
import { signOut } from '../../lib/auth';
import LearningAnalyticsWidget from './widgets/LearningAnalyticsWidget';
import ChallengeAnalyticsWidget from './widgets/ChallengeAnalyticsWidget';
import RecommendationsWidget from './widgets/RecommendationsWidget';
import AnalyticsChart from '../visualizations/AnalyticsChart';
import SkillsRadarChart from '../visualizations/SkillsRadarChart';
import { useDashboard } from '../../contexts/DashboardContext';
import { SUBSCRIPTION_TIERS } from '../../config/subscriptionTiers';

function PremiumDashboard({ subscription, profile, coins, challenges, completedChallenges, totalPoints }) {
  const [selectedSection, setSelectedSection] = useState('dashboard');
  const navigate = useNavigate();

  // Use dashboard context for data and state management
  const {
    // Data
    learningStats,
    challengeStats,
    recommendations,
    userSkills,

    // State
    timeRange,
    isLoading,
    error,
    showLearningDetails,
    showChallengeDetails,

    // Actions
    handleTimeRangeChange,
    toggleLearningDetails,
    toggleChallengeDetails,
    fetchAllData
  } = useDashboard();

  // Sample data for charts
  const learningProgressData = [
    [30, 45, 60, 70, 75, 85] // Last 6 months progress
  ];

  const learningProgressLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];

  const challengeCompletionData = [
    [2, 3, 5, 4, 8, 6] // Completed challenges by month
  ];

  // Skills data for radar chart
  const skillNames = Object.keys(userSkills || {});
  const skillValues = Object.values(userSkills || {});

  // Team average skills (mock data)
  const teamSkillValues = skillNames.map(skill => {
    // Generate a random value that's somewhat close to the user's skill
    const userValue = userSkills?.[skill] || 0;
    const min = Math.max(0, userValue - 20);
    const max = Math.min(100, userValue + 20);
    return Math.floor(Math.random() * (max - min + 1)) + min;
  });

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Handle upgrade click
  const handleUpgrade = () => {
    navigate('/pricing');
  };

  // Handle refresh data
  const handleRefreshData = () => {
    fetchAllData();
  };

  return (
    <div className="min-h-screen bg-[#0B1120] text-white">
      <div className="flex">
        {/* Sidebar */}
        <DashboardSidebar
          profile={profile}
          selectedSection={selectedSection}
          setSelectedSection={setSelectedSection}
          onSignOut={handleSignOut}
        />

        {/* Main Content */}
        <div className="flex-1">
          {/* Header */}
          <DashboardHeader
            profile={profile}
            subscription={subscription}
            coins={coins}
            onSignOut={handleSignOut}
          />

          {/* Dashboard Content */}
          {selectedSection === 'dashboard' ? (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">Premium Dashboard</h2>
                <button
                  onClick={handleRefreshData}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center"
                >
                  Refresh Data
                </button>
              </div>

              {/* Analytics Widgets */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <LearningAnalyticsWidget
                  learningStats={learningStats}
                  timeRange={timeRange}
                  onTimeRangeChange={handleTimeRangeChange}
                  showDetails={showLearningDetails}
                  onToggleDetails={toggleLearningDetails}
                  loading={isLoading.learning}
                  error={error.learning}
                />

                <ChallengeAnalyticsWidget
                  challengeStats={challengeStats}
                  timeRange={timeRange}
                  onTimeRangeChange={handleTimeRangeChange}
                  showDetails={showChallengeDetails}
                  onToggleDetails={toggleChallengeDetails}
                  loading={isLoading.challenges}
                  error={error.challenges}
                />
              </div>

              {/* Visualizations */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <AnalyticsChart
                  type="line"
                  data={learningProgressData}
                  labels={learningProgressLabels}
                  title="Learning Progress Over Time"
                  height={200}
                />

                <AnalyticsChart
                  type="bar"
                  data={challengeCompletionData}
                  labels={learningProgressLabels}
                  title="Challenges Completed by Month"
                  height={200}
                />
              </div>

              {/* Skills Radar Chart */}
              {skillNames.length > 0 && (
                <div className="mb-6">
                  <SkillsRadarChart
                    skills={skillNames}
                    dataSeries={[skillValues, teamSkillValues]}
                    labels={['Your Skills', 'Team Average']}
                    height={300}
                  />
                </div>
              )}

              {/* Recommendations Widget */}
              <div className="mb-6">
                <RecommendationsWidget
                  recommendations={recommendations}
                  userSkills={userSkills}
                  subscriptionLevel={SUBSCRIPTION_TIERS.PREMIUM}
                  onUpgrade={handleUpgrade}
                  loading={isLoading.recommendations}
                  error={error.recommendations}
                />
              </div>

              {/* Original Dashboard Content */}
              <DashboardContent
                selectedSection={selectedSection}
                profile={profile}
                subscription={subscription}
                coins={coins}
                challenges={challenges}
                completedChallenges={completedChallenges}
                totalPoints={totalPoints}
              />
            </div>
          ) : (
            <DashboardContent
              selectedSection={selectedSection}
              profile={profile}
              subscription={subscription}
              coins={coins}
              challenges={challenges}
              completedChallenges={completedChallenges}
              totalPoints={totalPoints}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default PremiumDashboard;