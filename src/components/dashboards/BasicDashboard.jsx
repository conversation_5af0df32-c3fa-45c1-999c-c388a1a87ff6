import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import {
  FaChartLine, FaTrophy, FaCode, FaBook, FaRocket, FaCrown, FaUser, FaCoins,
  FaLock, FaServer, FaShieldAlt, FaGraduationCap, FaTerminal, FaBug, FaHackerrank,
  FaNetworkWired, FaDatabase, FaLaptopCode, FaUserSecret, FaGamepad, FaSignOutAlt,
  FaWallet, FaShoppingBag, FaCog, FaPlay, FaUserTie
} from 'react-icons/fa';
import UpgradeButton from '../subscription/UpgradeButton';
import { signOut } from '../../lib/auth';
import LearningProgressWidget from './widgets/LearningProgressWidget';
import ChallengesWidget from './widgets/ChallengesWidget';
import { SUBSCRIPTION_TIERS } from '../../config/subscriptionTiers';

function BasicDashboard({ subscription, profile, coins, challenges, completedChallenges, totalPoints }) {
  const [activeTab, setActiveTab] = useState('overview');
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Sidebar navigation items
  const navItems = [
    { id: 'overview', label: 'Overview', icon: FaUser },
    { id: 'learning', label: 'Learning', icon: FaGraduationCap, action: () => navigate('/global-learn') },
    { id: 'challenges', label: 'Challenges', icon: FaGamepad, action: () => navigate('/global-challenges') },
    { id: 'wallet', label: 'Wallet', icon: FaWallet },
    { id: 'orders', label: 'Orders', icon: FaShoppingBag },
    { id: 'settings', label: 'Settings', icon: FaCog }
  ];

  // Stats data
  const statsData = [
    {
      icon: FaCoins,
      title: 'Balance',
      value: coins?.balance || 0,
      suffix: ' XC',
      color: '#88cc14'
    },
    {
      icon: FaShoppingBag,
      title: 'Orders',
      value: 0,
      color: '#88cc14'
    },
    {
      icon: FaTrophy,
      title: 'Challenges',
      value: completedChallenges || 0,
      color: '#88cc14'
    },
    {
      icon: FaChartLine,
      title: 'Points',
      value: totalPoints || 0,
      color: '#88cc14'
    }
  ];

  // Feature cards data
  const featureCardsData = [
    {
      icon: FaGraduationCap,
      title: 'Learning Paths',
      description: 'Master cybersecurity fundamentals',
      linkTo: '/learn',
      linkText: 'Start Learning',
      linkIcon: FaRocket
    },
    {
      icon: FaGamepad,
      title: 'Hacking Games',
      description: 'Practice with interactive challenges',
      linkTo: '/games',
      linkText: 'Play Games',
      linkIcon: FaPlay
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="md:col-span-1">
          <div className="bg-white rounded-lg shadow-sm p-6">
            {/* Profile Header */}
            <div className="text-center mb-6">
              <div className="w-24 h-24 rounded-full bg-[#88cc14]/10 flex items-center justify-center mx-auto mb-4">
                {profile?.avatar_url ? (
                  <img
                    src={profile.avatar_url}
                    alt={profile.username}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <FaUser className="text-[#88cc14] text-3xl" />
                )}
              </div>
              <h2 className="text-xl font-bold text-gray-900">{profile?.username || 'User'}</h2>
              <p className="text-gray-500">{profile?.email || '<EMAIL>'}</p>
            </div>

            {/* Navigation */}
            <nav className="space-y-2">
              {navItems.map(item => (
                <button
                  key={item.id}
                  onClick={() => item.action ? item.action() : setActiveTab(item.id)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-colors ${
                    activeTab === item.id
                      ? 'bg-[#88cc14]/10 text-[#88cc14]'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <item.icon />
                  <span>{item.label}</span>
                </button>
              ))}
            </nav>

            <div className="mt-6">
              <UpgradeButton
                targetPlan="Premium"
                variant="primary"
                size="md"
                fullWidth
              >
                Upgrade to Premium
              </UpgradeButton>
            </div>

            <div className="mt-3">
              <UpgradeButton
                targetPlan="Business"
                variant="secondary"
                size="md"
                fullWidth
              >
                Upgrade to Business
              </UpgradeButton>
            </div>

            <button
              onClick={handleSignOut}
              className="w-full mt-4 flex items-center gap-3 p-3 rounded-lg text-red-500 hover:bg-red-50 transition-colors"
            >
              <FaSignOutAlt />
              <span>Sign Out</span>
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="md:col-span-3">
          {/* Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {statsData.map((stat, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.03 }}
                className="bg-white rounded-lg shadow-sm p-6"
              >
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                    <stat.icon className="text-[#88cc14] text-xl" />
                  </div>
                  <div>
                    <h3 className="text-gray-500">{stat.title}</h3>
                    <p className="text-2xl font-bold" style={{ color: stat.color }}>
                      {stat.value}{stat.suffix}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Learning Progress Widget */}
          <div className="mb-8">
            <LearningProgressWidget
              userProgress={{
                completedModules: 2,
                totalModules: 10,
                currentModule: 'os-concepts'
              }}
              recentModules={[
                { id: 'os-introduction', title: 'Introduction to Operating Systems', progress: 75 },
                { id: 'network-basics', title: 'Networking Fundamentals', progress: 30 },
                { id: 'security-principles', title: 'Core Security Principles', progress: 10 }
              ]}
              subscriptionLevel={SUBSCRIPTION_TIERS.FREE}
              onUpgrade={() => navigate('/pricing')}
            />
          </div>

          {/* Challenges Widget */}
          <div className="mb-8">
            <ChallengesWidget
              completedChallenges={completedChallenges || 0}
              totalChallenges={10}
              recentChallenges={challenges?.slice(0, 2).map(challenge => ({
                id: challenge.challenge_id,
                title: challenge.challenges?.title || 'Challenge',
                difficulty: challenge.challenges?.difficulty || 'Beginner',
                category: challenge.challenges?.category || 'web',
                completed: challenge.status === 'completed',
                estimatedTime: '30 min'
              })) || []}
              recommendedChallenges={[
                {
                  id: 'web-1',
                  title: 'SQL Injection Basics',
                  difficulty: 'Beginner',
                  category: 'web',
                  estimatedTime: '30 min'
                }
              ]}
              subscriptionLevel={SUBSCRIPTION_TIERS.FREE}
              onUpgrade={() => navigate('/pricing')}
            />
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Recent Activity</h3>
            {challenges && challenges.length > 0 ? (
              <div className="space-y-4">
                {challenges.slice(0, 3).map((challenge, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-4 p-4 rounded-lg hover:bg-gray-50"
                  >
                    <FaCode className={challenge.status === 'completed' ? 'text-[#88cc14]' : 'text-gray-400'} />
                    <div className="flex-1">
                      <p className="text-gray-900">
                        {challenge.status === 'completed'
                          ? `Completed "${challenge.challenges?.title || 'Challenge'}"`
                          : `Started "${challenge.challenges?.title || 'Challenge'}"`}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(challenge.submission_time).toLocaleString()}
                      </p>
                    </div>
                    {challenge.status === 'completed' && (
                      <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-medium">
                        +{challenge.points_earned || 0} pts
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No recent activity</p>
                <Link to="/global-challenges" className="text-[#88cc14] hover:underline mt-2 inline-block">
                  Start a challenge
                </Link>
              </div>
            )}
          </div>

          {/* Available Features */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {featureCardsData.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                    <feature.icon className="text-[#88cc14] text-xl" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">{feature.title}</h3>
                    <p className="text-gray-500">{feature.description}</p>
                  </div>
                </div>
                <Link
                  to={feature.linkTo}
                  className="w-full bg-black text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-900 transition-colors flex items-center justify-center gap-2"
                >
                  {feature.linkIcon && <feature.linkIcon />}
                  <span>{feature.linkText}</span>
                </Link>
              </div>
            ))}
          </div>

          {/* Upgrade Banners */}
          <div className="space-y-6">
            {/* Premium Upgrade Banner */}
            <div className="bg-black rounded-lg shadow-sm p-8 text-white">
              <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                <div className="flex items-center gap-6">
                  <div className="w-16 h-16 bg-[#88cc14] rounded-full flex items-center justify-center">
                    <FaCrown className="text-black text-2xl" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Upgrade to Premium</h3>
                    <p className="text-gray-400">Get unlimited access to all labs and challenges</p>
                  </div>
                </div>
                <UpgradeButton
                  targetPlan="Premium"
                  variant="primary"
                  size="lg"
                >
                  Upgrade Now
                </UpgradeButton>
              </div>
            </div>

            {/* Business Upgrade Banner */}
            <div className="bg-[#0B1120] rounded-lg shadow-sm p-8 text-white">
              <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                <div className="flex items-center gap-6">
                  <div className="w-16 h-16 bg-[#88cc14] rounded-full flex items-center justify-center">
                    <FaUserTie className="text-black text-2xl" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Upgrade to Business</h3>
                    <p className="text-gray-400">Team management and custom labs for organizations</p>
                  </div>
                </div>
                <UpgradeButton
                  targetPlan="Business"
                  variant="secondary"
                  size="lg"
                >
                  Get Business
                </UpgradeButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BasicDashboard;