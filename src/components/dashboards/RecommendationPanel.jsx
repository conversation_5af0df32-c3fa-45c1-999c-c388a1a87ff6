import React from 'react';
import { motion } from 'framer-motion';
import { FaLightbulb, FaRocket, FaBook, FaTrophy, FaChartLine } from 'react-icons/fa';

function RecommendationPanel({ recommendations }) {
  if (!recommendations) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg p-6"
    >
      <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
        <FaLightbulb className="text-[#88cc14]" />
        Personalized Recommendations
      </h2>

      <div className="space-y-6">
        {/* Next Challenges */}
        <div>
          <h3 className="text-lg font-semibold text-[#88cc14] mb-3 flex items-center gap-2">
            <FaRocket className="text-sm" />
            Recommended Challenges
          </h3>
          <div className="space-y-2">
            {recommendations.nextChallenges.map((challenge, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-black/20 rounded-lg p-4 hover:bg-[#88cc14]/5 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white font-medium">{challenge.category}</p>
                    <p className="text-sm text-gray-400">
                      Recommended Difficulty: {challenge.recommendedDifficulty}
                    </p>
                  </div>
                  <button className="bg-[#88cc14]/10 text-[#88cc14] px-3 py-1 rounded-lg text-sm hover:bg-[#88cc14]/20 transition-colors">
                    Start
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Skills Focus */}
        <div>
          <h3 className="text-lg font-semibold text-[#88cc14] mb-3 flex items-center gap-2">
            <FaBook className="text-sm" />
            Skills to Focus On
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {recommendations.patterns.weaknesses.map((skill, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-black/20 rounded-lg p-3 text-center"
              >
                <p className="text-white">{skill}</p>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Strengths */}
        <div>
          <h3 className="text-lg font-semibold text-[#88cc14] mb-3 flex items-center gap-2">
            <FaTrophy className="text-sm" />
            Your Strengths
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {recommendations.patterns.strengths.map((strength, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-[#88cc14]/10 rounded-lg p-3 text-center"
              >
                <p className="text-[#88cc14]">{strength}</p>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Learning Path */}
        <div>
          <h3 className="text-lg font-semibold text-[#88cc14] mb-3 flex items-center gap-2">
            <FaChartLine className="text-sm" />
            Recommended Learning Path
          </h3>
          <div className="space-y-2">
            {recommendations.patterns.recommendedPaths.map((path, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-black/20 rounded-lg p-4 hover:bg-[#88cc14]/5 transition-colors"
              >
                <p className="text-white font-medium">{path}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export default RecommendationPanel;