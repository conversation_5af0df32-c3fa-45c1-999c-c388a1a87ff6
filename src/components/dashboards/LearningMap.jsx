import React, { useEffect, useRef } from 'react';
import ForceGraph3D from 'react-force-graph-3d';
import { motion } from 'framer-motion';
import { FaL<PERSON>, <PERSON>aUn<PERSON>, <PERSON>a<PERSON>heck } from 'react-icons/fa';
import * as THREE from 'three';

function LearningMap({ learningData = [], completedTopics = [], currentTopic = null }) {
  const fgRef = useRef();

  useEffect(() => {
    if (fgRef.current) {
      fgRef.current.d3Force('charge').strength(-120);
    }
  }, []);

  // Transform learning data into graph format
  const graphData = {
    nodes: learningData.map(topic => ({
      id: topic.id,
      name: topic.title,
      val: 1,
      color: completedTopics.includes(topic.id) 
        ? '#88cc14' 
        : currentTopic === topic.id
        ? '#00f3ff'
        : '#666',
      status: completedTopics.includes(topic.id) 
        ? 'completed' 
        : currentTopic === topic.id
        ? 'current'
        : 'locked'
    })),
    links: learningData.reduce((acc, topic) => {
      if (topic.dependencies) {
        topic.dependencies.forEach(dep => {
          acc.push({
            source: dep,
            target: topic.id,
            color: completedTopics.includes(dep) && completedTopics.includes(topic.id)
              ? '#88cc14'
              : '#333'
          });
        });
      }
      return acc;
    }, [])
  };

  const handleNodeClick = node => {
    if (fgRef.current) {
      const distance = 40;
      const distRatio = 1 + distance/Math.hypot(node.x, node.y, node.z);

      fgRef.current.cameraPosition(
        { x: node.x * distRatio, y: node.y * distRatio, z: node.z * distRatio },
        node,
        1000
      );
    }
  };

  return (
    <div className="w-full h-[600px] relative">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="absolute inset-0 bg-black/30 backdrop-blur-sm border border-[#88cc14]/20 rounded-lg overflow-hidden"
      >
        <ForceGraph3D
          ref={fgRef}
          graphData={graphData}
          nodeLabel="name"
          nodeColor="color"
          linkColor="color"
          nodeThreeObject={node => {
            const sprite = new THREE.Sprite(
              new THREE.SpriteMaterial({
                map: new THREE.CanvasTexture((() => {
                  const canvas = document.createElement('canvas');
                  canvas.width = 128;
                  canvas.height = 64;
                  const ctx = canvas.getContext('2d');
                  ctx.fillStyle = node.color;
                  ctx.font = '24px Arial';
                  ctx.fillText(node.name, 0, 32);
                  return canvas;
                })())
              })
            );
            sprite.scale.set(12, 6, 1);
            return sprite;
          }}
          nodeThreeObjectExtend={true}
          linkWidth={2}
          linkOpacity={0.5}
          backgroundColor="#00000000"
          onNodeClick={handleNodeClick}
        />

        {/* Legend */}
        <div className="absolute bottom-4 right-4 bg-black/50 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <FaCheck className="text-[#88cc14]" />
            <span className="text-white">Completed</span>
          </div>
          <div className="flex items-center gap-2 mb-2">
            <FaUnlock className="text-[#00f3ff]" />
            <span className="text-white">Current</span>
          </div>
          <div className="flex items-center gap-2">
            <FaLock className="text-gray-400" />
            <span className="text-white">Locked</span>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export default LearningMap;