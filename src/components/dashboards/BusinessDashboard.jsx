import React from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import {
  FaChartLine, FaTrophy, FaCode, FaBook, FaRocket, FaCrown,
  FaServer, FaShieldAlt, FaUsers, FaChartBar, FaCog, FaUserTie,
  FaCoins, FaUser, FaPlus, FaEllipsisH, FaSignOutAlt, FaGraduationCap, FaGamepad
} from 'react-icons/fa';
import { signOut } from '../../lib/auth';
import TeamLearningWidget from './widgets/TeamLearningWidget';
import StartHackWidget from './widgets/StartHackWidget';
import TeamPerformanceWidget from './widgets/TeamPerformanceWidget';
import RecommendationsWidget from './widgets/RecommendationsWidget';
import { SUBSCRIPTION_TIERS } from '../../config/subscriptionTiers';

function BusinessDashboard({ subscription, profile, coins, challenges, completedChallenges, totalPoints, recommendations }) {
  const navigate = useNavigate();
  const [showTeamDetails, setShowTeamDetails] = useState(false);

  // Sample team members data for demonstration
  const teamMembers = [
    {
      id: 1,
      name: 'John Doe',
      role: 'Security Analyst',
      progress: 75,
      completedModules: 15,
      totalModules: 20,
      completedChallenges: 12,
      totalChallenges: 25,
      points: 1850
    },
    {
      id: 2,
      name: 'Jane Smith',
      role: 'Network Engineer',
      progress: 60,
      completedModules: 12,
      totalModules: 20,
      completedChallenges: 8,
      totalChallenges: 25,
      points: 1200
    },
    {
      id: 3,
      name: 'Mike Johnson',
      role: 'Developer',
      progress: 45,
      completedModules: 9,
      totalModules: 20,
      completedChallenges: 6,
      totalChallenges: 25,
      points: 950
    },
    {
      id: 4,
      name: 'Sarah Williams',
      role: 'IT Manager',
      progress: 90,
      completedModules: 18,
      totalModules: 20,
      completedChallenges: 20,
      totalChallenges: 25,
      points: 2500
    },
    {
      id: 5,
      name: 'David Brown',
      role: 'Security Specialist',
      progress: 30,
      completedModules: 6,
      totalModules: 20,
      completedChallenges: 4,
      totalChallenges: 25,
      points: 650
    }
  ];

  // Sample team stats for demonstration
  const teamStats = {
    averageProgress: 60,
    totalModulesCompleted: 60,
    totalChallengesCompleted: 50,
    totalModules: 100,
    totalChallenges: 125,
    topSkills: [
      { name: 'Web Security', level: 75 },
      { name: 'Network Security', level: 60 },
      { name: 'Cryptography', level: 45 }
    ],
    skillGaps: [
      { name: 'Cloud Security', level: 25 },
      { name: 'Mobile Security', level: 30 },
      { name: 'IoT Security', level: 15 }
    ]
  };

  // Sample recommendations for demonstration
  const businessRecommendations = {
    modules: [
      { id: 'advanced-web-sec', title: 'Advanced Web Security', description: 'Learn advanced techniques for securing web applications', category: 'web' },
      { id: 'network-forensics', title: 'Network Forensics', description: 'Master the art of network traffic analysis', category: 'network' }
    ],
    challenges: [
      { id: 'csrf-challenge', title: 'CSRF Protection Bypass', difficulty: 'Intermediate', estimatedTime: '1 hour', category: 'web' },
      { id: 'packet-analysis', title: 'Network Packet Analysis', difficulty: 'Intermediate', estimatedTime: '45 min', category: 'network' }
    ],
    simulations: [
      { id: 'enterprise-breach', title: 'Enterprise Network Breach', difficulty: 'Advanced', estimatedTime: '4 hours', category: 'network' },
      { id: 'ransomware-response', title: 'Ransomware Incident Response', difficulty: 'Intermediate', estimatedTime: '3 hours', category: 'security' }
    ],
    careerPaths: [
      { id: 'security-analyst', title: 'Security Analyst Path', description: 'Develop the skills needed to become a security analyst' }
    ]
  };

  // Sample user skills for recommendations
  const userSkills = {
    'Web Security': 75,
    'Network Security': 60,
    'Cryptography': 45
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Toggle team details
  const toggleTeamDetails = () => {
    setShowTeamDetails(!showTeamDetails);
  };

  // Handle assign learning
  const handleAssignLearning = () => {
    navigate('/team/assign-learning');
  };

  // Handle view member progress
  const handleViewMemberProgress = (memberId) => {
    navigate(`/team/member/${memberId}/progress`);
  };

  // Handle export report
  const handleExportReport = () => {
    alert('Exporting team performance report...');
    // In a real app, this would generate and download a report
  };

  // Handle upgrade
  const handleUpgrade = () => {
    navigate('/pricing');
  };

  // Header actions
  const headerActions = (
    <>
      <button className="bg-[#88cc14] text-black px-6 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors">
        Team Management
      </button>
      <button className="bg-black text-white px-6 py-2 rounded-lg font-bold hover:bg-gray-900 transition-colors">
        Account Settings
      </button>
    </>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between">
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold text-gray-900">Business Dashboard</h1>
              <FaUserTie className="text-[#88cc14]" />
            </div>
            <p className="text-gray-600">Enterprise Account</p>
          </div>

          <div className="flex items-center gap-4 mt-4 md:mt-0">
            {headerActions}
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <motion.div
          whileHover={{ scale: 1.03 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
              <FaUsers className="text-[#88cc14] text-xl" />
            </div>
            <div>
              <h3 className="text-gray-500">Team Members</h3>
              <p className="text-2xl font-bold text-[#88cc14]">{subscription?.team_members || 0}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.03 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
              <FaChartBar className="text-[#88cc14] text-xl" />
            </div>
            <div>
              <h3 className="text-gray-500">Active Sessions</h3>
              <p className="text-2xl font-bold text-[#88cc14]">{subscription?.active_sessions || 0}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.03 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
              <FaCoins className="text-[#88cc14] text-xl" />
            </div>
            <div>
              <h3 className="text-gray-500">XCerberus Coins</h3>
              <p className="text-2xl font-bold text-[#88cc14]">
                {coins?.balance || 0}
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.03 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
              <FaTrophy className="text-[#88cc14] text-xl" />
            </div>
            <div>
              <h3 className="text-gray-500">Team Score</h3>
              <p className="text-2xl font-bold text-[#88cc14]">{totalPoints || 0}</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Team Performance Widget */}
      <div className="mb-8">
        <TeamPerformanceWidget
          teamMembers={teamMembers}
          teamStats={teamStats}
          onAssignLearning={handleAssignLearning}
          onViewMemberProgress={handleViewMemberProgress}
          onExportReport={handleExportReport}
          showDetails={showTeamDetails}
          onToggleDetails={toggleTeamDetails}
        />
      </div>

      {/* Team Learning and Start Hack Widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <TeamLearningWidget
          teamMembers={teamMembers}
          teamProgress={{
            averageProgress: teamStats.averageProgress,
            totalCompleted: teamStats.totalModulesCompleted,
            totalModules: teamStats.totalModules
          }}
          onAssignLearning={handleAssignLearning}
          onViewMemberProgress={handleViewMemberProgress}
        />

        <StartHackWidget
          scenarios={[
            { id: 'enterprise-breach', title: 'Enterprise Network Breach', type: 'network', difficulty: 'Advanced', estimatedTime: '4 hours' },
            { id: 'ransomware-response', title: 'Ransomware Incident Response', type: 'security', difficulty: 'Intermediate', estimatedTime: '3 hours' },
            { id: 'web-app-pentest', title: 'Web Application Penetration Test', type: 'web', difficulty: 'Intermediate', estimatedTime: '3 hours' }
          ]}
          subscriptionLevel={SUBSCRIPTION_TIERS.BUSINESS}
          onUpgrade={handleUpgrade}
        />
      </div>

      {/* Recommendations Widget */}
      <div className="mb-8">
        <RecommendationsWidget
          recommendations={businessRecommendations}
          userSkills={userSkills}
          subscriptionLevel={SUBSCRIPTION_TIERS.BUSINESS}
          onUpgrade={handleUpgrade}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Team Activity */}
        <div className="lg:col-span-2 space-y-8">
          {/* Team Performance */}
          <motion.div
            whileHover={{ y: -5 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                <FaUsers className="text-[#88cc14]" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">Team Performance</h3>
            </div>
            <div className="space-y-6">
              {[
                { name: "Red Team", progress: 85, members: 8, activeLabs: 4 },
                { name: "Blue Team", progress: 75, members: 10, activeLabs: 3 },
                { name: "SOC Team", progress: 90, members: 6, activeLabs: 5 }
              ].map((team, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-bold text-gray-900">{team.name}</h3>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>{team.members} members</span>
                        <span>{team.activeLabs} active labs</span>
                      </div>
                    </div>
                    <span className="text-[#88cc14] font-bold">{team.progress}%</span>
                  </div>
                  <div className="h-2 bg-gray-200 rounded-full">
                    <div
                      className="h-full bg-[#88cc14] rounded-full"
                      style={{ width: `${team.progress}%` }}
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Custom Labs */}
          <motion.div
            whileHover={{ y: -5 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                  <FaServer className="text-[#88cc14]" />
                </div>
                <h3 className="text-lg font-bold text-gray-900">Custom Labs</h3>
              </div>
              <button className="bg-[#88cc14] text-black px-4 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors">
                Create New Lab
              </button>
            </div>
            <div className="space-y-4">
              {[
                {
                  name: "Advanced Threat Hunting",
                  status: "Active",
                  users: 12,
                  completion: 45
                },
                {
                  name: "Cloud Security Assessment",
                  status: "Draft",
                  users: 0,
                  completion: 0
                },
                {
                  name: "Incident Response Simulation",
                  status: "Active",
                  users: 8,
                  completion: 75
                }
              ].map((lab, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                >
                  <div>
                    <h3 className="font-bold text-gray-900">{lab.name}</h3>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        lab.status === 'Active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {lab.status}
                      </span>
                      <span>{lab.users} users</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="w-32">
                      <div className="h-2 bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-[#88cc14] rounded-full"
                          style={{ width: `${lab.completion}%` }}
                        />
                      </div>
                    </div>
                    <button className="text-gray-500 hover:text-[#88cc14]">
                      <FaCog />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Sidebar */}
        <div className="space-y-8">
          {/* Team Members */}
          <motion.div
            whileHover={{ y: -5 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                  <FaUsers className="text-[#88cc14]" />
                </div>
                <h3 className="text-lg font-bold text-gray-900">Team Members</h3>
              </div>
              <button className="text-[#88cc14] hover:text-[#7ab811]">View All</button>
            </div>
            <div className="space-y-4">
              {[
                { name: "John Doe", role: "Team Lead", active: true },
                { name: "Jane Smith", role: "Security Analyst", active: true },
                { name: "Mike Johnson", role: "Pentester", active: false }
              ].map((member, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                      <span className="text-[#88cc14] font-bold">
                        {member.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{member.name}</p>
                      <p className="text-sm text-gray-500">{member.role}</p>
                    </div>
                  </div>
                  <div className={`w-2 h-2 rounded-full ${
                    member.active ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                </div>
              ))}
            </div>
          </motion.div>

          {/* Analytics */}
          <motion.div
            whileHover={{ y: -5 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                <FaChartLine className="text-[#88cc14]" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">Analytics</h3>
            </div>
            <div className="space-y-4">
              {[
                { label: "Lab Completion Rate", value: "78%" },
                { label: "Average Score", value: "85/100" },
                { label: "Active Time", value: "127h" }
              ].map((stat, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-gray-600">{stat.label}</span>
                  <span className="font-bold text-gray-900">{stat.value}</span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            whileHover={{ y: -5 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 rounded-lg bg-[#88cc14]/10 flex items-center justify-center">
                <FaRocket className="text-[#88cc14]" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">Quick Actions</h3>
            </div>
            <div className="space-y-2">
              {[
                { icon: FaUsers, text: "Manage Team", onClick: () => navigate('/team') },
                { icon: FaGraduationCap, text: "Learning Center", onClick: () => navigate('/global-learn') },
                { icon: FaGamepad, text: "Challenges", onClick: () => navigate('/global-challenges') },
                { icon: FaServer, text: "Start Hack", onClick: () => navigate('/global-start-hack') },
                { icon: FaChartBar, text: "View Reports", onClick: () => navigate('/team/reports') },
                { icon: FaCog, text: "Settings", onClick: () => navigate('/settings') },
                { icon: FaSignOutAlt, text: "Sign Out", onClick: handleSignOut }
              ].map((action, index) => (
                <button
                  key={index}
                  onClick={action.onClick}
                  className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <action.icon className="text-[#88cc14]" />
                  <span className="text-gray-700">{action.text}</span>
                </button>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

export default BusinessDashboard;