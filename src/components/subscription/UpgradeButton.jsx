import React, { useState } from 'react';
import { FaCrown, FaUserTie } from 'react-icons/fa';
import UpgradeModal from './UpgradeModal';
import { useSubscription } from '../../contexts/SubscriptionContext';

const UpgradeButton = ({ 
  targetPlan = 'Premium',
  variant = 'primary', // primary, secondary, text
  size = 'md', // sm, md, lg
  fullWidth = false,
  className = '',
  children
}) => {
  const [showModal, setShowModal] = useState(false);
  const { subscriptionLevel } = useSubscription();

  // Don't render if user already has the target plan or higher
  if ((targetPlan === 'Premium' && 
      (subscriptionLevel === 'Premium' || subscriptionLevel === 'Business')) ||
      (targetPlan === 'Business' && subscriptionLevel === 'Business')) {
    return null;
  }

  // Style variants
  const variantStyles = {
    primary: 'bg-[#88cc14] text-black hover:bg-[#7ab811]',
    secondary: 'bg-black text-white hover:bg-gray-900',
    text: 'text-[#88cc14] hover:underline bg-transparent'
  };

  // Size variants
  const sizeStyles = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2',
    lg: 'px-6 py-3 text-lg'
  };

  // Icon based on target plan
  const Icon = targetPlan === 'Business' ? FaUserTie : FaCrown;

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className={`
          ${variantStyles[variant]} 
          ${sizeStyles[size]} 
          ${fullWidth ? 'w-full' : ''}
          font-bold rounded-lg transition-colors flex items-center justify-center gap-2
          ${className}
        `}
      >
        <Icon />
        {children || `Upgrade to ${targetPlan}`}
      </button>

      <UpgradeModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        targetPlan={targetPlan}
        currentPlan={subscriptionLevel}
      />
    </>
  );
};

export default UpgradeButton;
