import React from 'react';
import { motion } from 'framer-motion';
import { FaCrown, FaArrowRight } from 'react-icons/fa';

/**
 * UpgradePrompt Component
 * 
 * Displays a prompt encouraging users to upgrade their subscription.
 * Used throughout the application to highlight premium features.
 */
const UpgradePrompt = ({
  title = 'Upgrade Your Experience',
  description = 'Unlock premium features and content with a subscription upgrade.',
  buttonText = 'Upgrade Now',
  onUpgrade,
  variant = 'default', // 'default', 'compact', 'inline'
  feature = null
}) => {
  // Render different variants of the upgrade prompt
  if (variant === 'compact') {
    return (
      <div className="bg-[#1A1F35] border border-[#88cc14]/20 rounded-lg p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center">
            <FaCrown className="text-[#88cc14]" />
          </div>
          <div>
            <p className="font-medium text-white">{title}</p>
            <p className="text-sm text-gray-400">{feature ? `Unlock ${feature} with Premium` : description}</p>
          </div>
        </div>
        <button
          onClick={onUpgrade}
          className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center gap-2"
        >
          {buttonText}
          <FaArrowRight />
        </button>
      </div>
    );
  }

  if (variant === 'inline') {
    return (
      <button
        onClick={onUpgrade}
        className="inline-flex items-center gap-2 text-[#88cc14] hover:text-[#7ab811]"
      >
        <FaCrown />
        <span>{feature ? `Upgrade to unlock ${feature}` : 'Upgrade to Premium'}</span>
      </button>
    );
  }

  // Default variant
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-[#1A1F35] border border-[#88cc14]/20 rounded-lg p-6 text-center"
    >
      <div className="w-16 h-16 bg-[#88cc14]/20 rounded-full flex items-center justify-center mx-auto mb-4">
        <FaCrown className="text-[#88cc14] text-2xl" />
      </div>
      
      <h3 className="text-xl font-bold text-white mb-2">
        {title}
      </h3>
      
      <p className="text-gray-300 mb-6 max-w-md mx-auto">
        {description}
      </p>
      
      <button
        onClick={onUpgrade}
        className="px-6 py-3 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg font-medium transition-colors"
      >
        {buttonText}
      </button>
    </motion.div>
  );
};

export default UpgradePrompt;
