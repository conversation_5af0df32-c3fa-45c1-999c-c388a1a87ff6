import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCrown, FaUserTie, FaTimes, FaCheck, FaSpinner } from 'react-icons/fa';
import { upgradeSubscription } from '../../lib/subscriptionUpgrade';

const UpgradeModal = ({ isOpen, onClose, targetPlan = 'Premium', currentPlan = 'Free' }) => {
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Plan details
  const planDetails = {
    'Free': {
      icon: null,
      title: 'Free Plan',
      price: '₹0',
      color: 'gray-600',
      features: [
        'Access to basic labs',
        'Limited challenges',
        'Community support'
      ]
    },
    'Premium': {
      icon: FaCrown,
      title: 'Premium Plan',
      price: '₹399',
      color: '#88cc14',
      features: [
        'Unlimited labs and challenges',
        'Attack Box access',
        'Private VPN',
        'Priority support'
      ]
    },
    'Business': {
      icon: FaUserTie,
      title: 'Business Plan',
      price: '₹999',
      color: '#88cc14',
      features: [
        'All Premium features',
        'Team management',
        'Custom labs',
        'Dedicated support',
        'Unlimited team members'
      ]
    }
  };

  const handleUpgrade = async () => {
    try {
      setProcessing(true);
      setError(null);
      
      await upgradeSubscription(targetPlan);
      
      setSuccess(true);
      setTimeout(() => {
        onClose();
        // Reload the page to reflect the new subscription
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      setError('Failed to upgrade subscription. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const targetPlanDetails = planDetails[targetPlan];
  const currentPlanDetails = planDetails[currentPlan];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-xl shadow-xl max-w-md w-full overflow-hidden"
          >
            {/* Header */}
            <div className="relative bg-gradient-to-r from-[#0B1120] to-[#1A1F35] p-6 text-white">
              <button
                onClick={onClose}
                disabled={processing}
                className="absolute top-4 right-4 text-white/80 hover:text-white"
              >
                <FaTimes />
              </button>
              
              <div className="flex items-center gap-3">
                {targetPlanDetails.icon && <targetPlanDetails.icon className="text-[#88cc14] text-2xl" />}
                <h2 className="text-xl font-bold">Upgrade to {targetPlan}</h2>
              </div>
              <p className="mt-2 text-gray-300">
                Unlock advanced features and take your cybersecurity skills to the next level.
              </p>
            </div>

            {/* Content */}
            <div className="p-6">
              {success ? (
                <div className="text-center py-6">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FaCheck className="text-green-600 text-2xl" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Upgrade Successful!</h3>
                  <p className="text-gray-600">
                    Your account has been upgraded to {targetPlan}.
                    Redirecting to your new dashboard...
                  </p>
                </div>
              ) : (
                <>
                  {/* Plan Comparison */}
                  <div className="mb-6">
                    <h3 className="font-bold text-gray-900 mb-4">You'll get access to:</h3>
                    <ul className="space-y-3">
                      {targetPlanDetails.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-3">
                          <div className="w-5 h-5 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                            <FaCheck className="text-[#88cc14] text-xs" />
                          </div>
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Price */}
                  <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-gray-600">Price</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {targetPlanDetails.price}<span className="text-sm font-normal text-gray-600">/month</span>
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-gray-600">Current plan</p>
                        <p className="font-medium text-gray-900">{currentPlanDetails.title}</p>
                      </div>
                    </div>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="mb-6 p-3 bg-red-50 text-red-700 rounded-lg text-sm">
                      {error}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-4">
                    <button
                      onClick={onClose}
                      disabled={processing}
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleUpgrade}
                      disabled={processing}
                      className="flex-1 bg-[#88cc14] text-black font-bold px-4 py-2 rounded-lg hover:bg-[#7ab811] transition-colors flex items-center justify-center gap-2"
                    >
                      {processing ? (
                        <>
                          <FaSpinner className="animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>Upgrade Now</>
                      )}
                    </button>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default UpgradeModal;
