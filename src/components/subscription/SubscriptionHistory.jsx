import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaHistory, FaArrowRight, FaCrown, FaUserTie, FaUser } from 'react-icons/fa';
import { supabase } from '../../lib/supabase';

const SubscriptionHistory = () => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchSubscriptionHistory = async () => {
      try {
        setLoading(true);
        setError(null);

        const { data: { session } } = await supabase.auth.getSession();
        if (!session) return;

        const { data, error } = await supabase
          .from('subscription_changes')
          .select('*')
          .eq('user_id', session.user.id)
          .order('change_time', { ascending: false });

        if (error) throw error;
        setHistory(data || []);
      } catch (err) {
        console.error('Error fetching subscription history:', err);
        setError('Failed to load subscription history');
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptionHistory();
  }, []);

  // Plan icons
  const getPlanIcon = (planName) => {
    switch (planName) {
      case 'Business':
        return FaUserTie;
      case 'Premium':
        return FaCrown;
      default:
        return FaUser;
    }
  };

  if (loading) {
    return (
      <div className="p-4 flex justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#88cc14]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-red-500">
        {error}
      </div>
    );
  }

  if (history.length === 0) {
    return (
      <div className="p-4 text-gray-500 text-center">
        No subscription changes found
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-bold flex items-center gap-2">
        <FaHistory />
        <span>Subscription History</span>
      </h3>
      
      <div className="space-y-3">
        {history.map((change, index) => {
          const FromIcon = getPlanIcon(change.from_plan);
          const ToIcon = getPlanIcon(change.to_plan);
          
          return (
            <motion.div
              key={change.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white p-4 rounded-lg shadow-sm"
            >
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                    <FromIcon className="text-gray-600" />
                  </div>
                  <span className="font-medium">{change.from_plan}</span>
                </div>
                
                <FaArrowRight className="text-gray-400" />
                
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
                    <ToIcon className="text-[#88cc14]" />
                  </div>
                  <span className="font-medium">{change.to_plan}</span>
                </div>
                
                <div className="ml-auto text-sm text-gray-500">
                  {new Date(change.change_time).toLocaleDateString()}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default SubscriptionHistory;
