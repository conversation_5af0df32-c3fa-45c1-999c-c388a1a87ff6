import React from 'react';
import { FaSun, FaMoon } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle = () => {
  const { isDark, toggleTheme } = useTheme();

  return (
    <motion.button
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      onClick={toggleTheme}
      className={`fixed bottom-4 right-4 z-50 p-4 rounded-full backdrop-blur-sm border shadow-lg transition-all duration-300 ${
        isDark 
          ? 'bg-white/10 border-[#00f3ff]/20 hover:border-[#00f3ff] hover:shadow-[#88cc14]/20' 
          : 'bg-black/10 border-[#00f3ff]/20 hover:border-[#00f3ff] hover:shadow-[#88cc14]/20'
      }`}
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      {isDark ? (
        <FaSun className="text-[#00f3ff] text-2xl" />
      ) : (
        <FaMoon className="text-[#00f3ff] text-2xl" />
      )}
    </motion.button>
  );
};

export default ThemeToggle;