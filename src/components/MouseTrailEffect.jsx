import React, { useEffect, useRef } from 'react';

const MouseTrailEffect = () => {
  const canvasRef = useRef(null);
  const pointsRef = useRef([]);
  const mouseRef = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationFrameId;
    let lastTime = 0;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    class Point {
      constructor(x, y) {
        this.x = x;
        this.y = y;
        this.age = 0;
        this.maxAge = 50;
        this.vx = (Math.random() - 0.5) * 2;
        this.vy = (Math.random() - 0.5) * 2;
      }

      update() {
        this.x += this.vx;
        this.y += this.vy;
        this.age++;
        return this.age < this.maxAge;
      }

      draw(ctx) {
        const opacity = 1 - this.age / this.maxAge;
        ctx.fillStyle = `rgba(0, 243, 255, ${opacity * 0.5})`;
        ctx.beginPath();
        ctx.arc(this.x, this.y, 2, 0, Math.PI * 2);
        ctx.fill();
      }
    }

    const animate = (currentTime) => {
      if (lastTime === 0) lastTime = currentTime;
      const deltaTime = currentTime - lastTime;

      ctx.fillStyle = 'rgba(12, 18, 31, 0.1)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Add new points based on mouse position
      if (deltaTime > 16) { // Limit to ~60fps
        pointsRef.current.push(new Point(mouseRef.current.x, mouseRef.current.y));
        lastTime = currentTime;
      }

      // Update and draw points
      pointsRef.current = pointsRef.current.filter(point => {
        const alive = point.update();
        if (alive) point.draw(ctx);
        return alive;
      });

      animationFrameId = requestAnimationFrame(animate);
    };

    window.addEventListener('resize', resizeCanvas);
    window.addEventListener('mousemove', (e) => {
      mouseRef.current = { x: e.clientX, y: e.clientY };
    });

    resizeCanvas();
    animate(0);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ background: 'transparent' }}
    />
  );
};

export default MouseTrailEffect;