import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaGraduationCap, FaTrophy, FaArrowR<PERSON>, FaCheck, FaTimes } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useGuestProgress } from '../../contexts/GuestProgressContext';
import LocalStorageService from '../../services/LocalStorageService';
import { MODULES } from '../../pages/StaticLearning';
import { CHALLENGES } from '../../pages/StaticChallenges';

// Learning paths
const LEARNING_PATHS = [
  {
    id: 'web-security',
    title: 'Web Security Specialist',
    description: 'Master web application security, from basic vulnerabilities to advanced exploitation techniques.',
    icon: 'FaGlobe',
    modules: ['web-security-fundamentals', 'advanced-web-security'],
    challenges: ['sql-injection-basics', 'xss-fundamentals'],
    forBeginners: true,
  },
  {
    id: 'network-security',
    title: 'Network Security Analyst',
    description: 'Learn to secure networks, detect intrusions, and analyze network traffic for threats.',
    icon: 'FaNetworkWired',
    modules: ['network-security-basics'],
    challenges: ['network-traffic-analysis'],
    forBeginners: true,
  },
  {
    id: 'cryptography',
    title: 'Cryptography Specialist',
    description: 'Understand encryption, hashing, and cryptographic protocols to secure data and communications.',
    icon: 'FaLock',
    modules: ['intro-to-cybersecurity'],
    challenges: ['password-cracking-basics'],
    forBeginners: true,
  },
  {
    id: 'osint',
    title: 'OSINT Investigator',
    description: 'Master the art of gathering intelligence from publicly available sources.',
    icon: 'FaSearch',
    modules: ['intro-to-cybersecurity'],
    challenges: ['osint-investigation'],
    forBeginners: false,
  },
  {
    id: 'forensics',
    title: 'Digital Forensics Examiner',
    description: 'Learn to collect, analyze, and preserve digital evidence for investigations.',
    icon: 'FaSearch',
    modules: ['intro-to-cybersecurity'],
    challenges: ['basic-steganography'],
    forBeginners: false,
  },
  {
    id: 'reverse-engineering',
    title: 'Reverse Engineer',
    description: 'Develop skills to analyze and understand compiled software and malware.',
    icon: 'FaCode',
    modules: ['intro-to-cybersecurity'],
    challenges: ['simple-binary-analysis'],
    forBeginners: false,
  },
];

// Quiz questions to determine the best path
const QUIZ_QUESTIONS = [
  {
    id: 'experience',
    question: 'What is your experience level in cybersecurity?',
    options: [
      { id: 'beginner', text: 'Beginner - I\'m just starting out' },
      { id: 'intermediate', text: 'Intermediate - I have some knowledge' },
      { id: 'advanced', text: 'Advanced - I\'m experienced in the field' },
    ],
  },
  {
    id: 'interest',
    question: 'Which area of cybersecurity interests you the most?',
    options: [
      { id: 'web', text: 'Web Application Security' },
      { id: 'network', text: 'Network Security' },
      { id: 'crypto', text: 'Cryptography' },
      { id: 'osint', text: 'Open Source Intelligence (OSINT)' },
      { id: 'forensics', text: 'Digital Forensics' },
      { id: 'reverse', text: 'Reverse Engineering' },
    ],
  },
  {
    id: 'goal',
    question: 'What is your primary goal in learning cybersecurity?',
    options: [
      { id: 'career', text: 'Start or advance my career' },
      { id: 'knowledge', text: 'Gain general knowledge' },
      { id: 'specific', text: 'Learn specific skills' },
      { id: 'fun', text: 'Just for fun and curiosity' },
    ],
  },
  {
    id: 'time',
    question: 'How much time can you dedicate to learning each week?',
    options: [
      { id: 'minimal', text: 'Less than 2 hours' },
      { id: 'moderate', text: '2-5 hours' },
      { id: 'significant', text: 'More than 5 hours' },
    ],
  },
];

const LearningPathRecommender = () => {
  const { darkMode } = useGlobalTheme();
  const { updateLastViewed } = useGuestProgress();
  const [showQuiz, setShowQuiz] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [recommendedPath, setRecommendedPath] = useState(null);
  const [quizCompleted, setQuizCompleted] = useState(false);
  
  // Check if user has already taken the quiz
  React.useEffect(() => {
    const savedPath = LocalStorageService.get('recommended_path', null);
    if (savedPath) {
      setRecommendedPath(savedPath);
      setQuizCompleted(true);
    }
  }, []);
  
  // Handle answer selection
  const handleAnswer = (questionId, answerId) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answerId,
    }));
    
    // Move to next question or finish quiz
    if (currentQuestion < QUIZ_QUESTIONS.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // Determine recommended path based on answers
      const recommendedPath = determineRecommendedPath(answers);
      setRecommendedPath(recommendedPath);
      setQuizCompleted(true);
      
      // Save recommended path
      LocalStorageService.set('recommended_path', recommendedPath);
    }
  };
  
  // Determine recommended path based on answers
  const determineRecommendedPath = (answers) => {
    // Simple algorithm to determine the best path
    const isBeginnerFriendly = answers.experience === 'beginner';
    
    // Map interests to paths
    const interestToPath = {
      'web': 'web-security',
      'network': 'network-security',
      'crypto': 'cryptography',
      'osint': 'osint',
      'forensics': 'forensics',
      'reverse': 'reverse-engineering',
    };
    
    // Get path based on interest
    const pathId = interestToPath[answers.interest] || 'web-security';
    
    // Find the path
    let path = LEARNING_PATHS.find(p => p.id === pathId);
    
    // If beginner and path is not beginner-friendly, default to web security
    if (isBeginnerFriendly && !path.forBeginners) {
      path = LEARNING_PATHS.find(p => p.id === 'web-security');
    }
    
    return path;
  };
  
  // Reset quiz
  const resetQuiz = () => {
    setCurrentQuestion(0);
    setAnswers({});
    setRecommendedPath(null);
    setQuizCompleted(false);
    setShowQuiz(false);
    
    // Remove saved path
    LocalStorageService.remove('recommended_path');
  };
  
  // Get module and challenge details for the recommended path
  const getPathDetails = (path) => {
    if (!path) return { modules: [], challenges: [] };
    
    const modules = path.modules.map(moduleId => 
      MODULES.find(m => m.id === moduleId || m.slug === moduleId)
    ).filter(Boolean);
    
    const challenges = path.challenges.map(challengeId => 
      CHALLENGES.find(c => c.id === challengeId || c.slug === challengeId)
    ).filter(Boolean);
    
    return { modules, challenges };
  };
  
  // If quiz is completed and we have a recommended path
  if (quizCompleted && recommendedPath) {
    const { modules, challenges } = getPathDetails(recommendedPath);
    
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden mb-6`}>
        <div className="p-4">
          <div className="flex justify-between items-start mb-4">
            <h3 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Your Recommended Learning Path
            </h3>
            <button 
              onClick={resetQuiz}
              className={`text-xs px-2 py-1 rounded ${
                darkMode ? 'bg-gray-800 text-gray-400' : 'bg-gray-100 text-gray-600'
              }`}
            >
              Retake Quiz
            </button>
          </div>
          
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} mb-4`}>
            <h4 className={`font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {recommendedPath.title}
            </h4>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
              {recommendedPath.description}
            </p>
            
            <div className="grid grid-cols-2 gap-2 mb-3">
              <div className={`p-2 rounded ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} text-center`}>
                <div className="text-sm text-gray-500">Modules</div>
                <div className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {modules.length}
                </div>
              </div>
              <div className={`p-2 rounded ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} text-center`}>
                <div className="text-sm text-gray-500">Challenges</div>
                <div className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {challenges.length}
                </div>
              </div>
            </div>
            
            <div className="flex justify-between">
              <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                Difficulty: {recommendedPath.forBeginners ? 'Beginner-friendly' : 'Intermediate'}
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                Est. completion: 2-4 weeks
              </div>
            </div>
          </div>
          
          <div className="mb-4">
            <h4 className={`font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Start with these modules:
            </h4>
            
            <div className="space-y-2">
              {modules.map((module, index) => (
                <Link 
                  key={index}
                  to={`/learn/${module.slug || module.id}`}
                  onClick={() => updateLastViewed(`/learn/${module.slug || module.id}`)}
                  className={`block p-3 rounded-lg ${
                    darkMode ? 'bg-[#0B1120] hover:bg-[#151F38]' : 'bg-gray-50 hover:bg-gray-100'
                  } transition-colors`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center mr-3">
                        <FaGraduationCap className="text-blue-500" />
                      </div>
                      <div>
                        <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {module.title}
                        </div>
                        <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {module.difficulty?.name || 'Beginner'} • {module.estimated_time || 30} min
                        </div>
                      </div>
                    </div>
                    <FaArrowRight className="text-[#88cc14]" />
                  </div>
                </Link>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className={`font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Then try these challenges:
            </h4>
            
            <div className="space-y-2">
              {challenges.map((challenge, index) => (
                <Link 
                  key={index}
                  to={`/challenges/${challenge.slug || challenge.id}`}
                  onClick={() => updateLastViewed(`/challenges/${challenge.slug || challenge.id}`)}
                  className={`block p-3 rounded-lg ${
                    darkMode ? 'bg-[#0B1120] hover:bg-[#151F38]' : 'bg-gray-50 hover:bg-gray-100'
                  } transition-colors`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center mr-3">
                        <FaTrophy className="text-yellow-500" />
                      </div>
                      <div>
                        <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {challenge.title}
                        </div>
                        <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {challenge.difficulty?.name || 'Beginner'} • {challenge.points || 100} points
                        </div>
                      </div>
                    </div>
                    <FaArrowRight className="text-[#88cc14]" />
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // If quiz is not shown, show the initial prompt
  if (!showQuiz) {
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden mb-6`}>
        <div className="p-4">
          <h3 className={`font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Not sure where to start?
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
            Take a quick quiz to get a personalized learning path based on your interests and experience level.
          </p>
          
          <button
            onClick={() => setShowQuiz(true)}
            className="w-full py-2 px-4 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors"
          >
            Take the Quiz
          </button>
        </div>
      </div>
    );
  }
  
  // Show the quiz
  const currentQ = QUIZ_QUESTIONS[currentQuestion];
  
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden mb-6`}>
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Learning Path Quiz
          </h3>
          <button 
            onClick={() => setShowQuiz(false)}
            className={`p-1 rounded-full ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          >
            <FaTimes className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
          </button>
        </div>
        
        <div className="mb-4">
          <div className="flex space-x-1 mb-4">
            {QUIZ_QUESTIONS.map((_, index) => (
              <div 
                key={index}
                className={`h-1 flex-1 rounded-full ${
                  index === currentQuestion
                    ? 'bg-[#88cc14]'
                    : index < currentQuestion
                      ? 'bg-gray-500'
                      : darkMode ? 'bg-gray-700' : 'bg-gray-300'
                }`}
              ></div>
            ))}
          </div>
          
          <h4 className={`text-lg font-medium mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {currentQ.question}
          </h4>
          
          <div className="space-y-2">
            {currentQ.options.map(option => (
              <button
                key={option.id}
                onClick={() => handleAnswer(currentQ.id, option.id)}
                className={`w-full p-3 text-left rounded-lg transition-colors ${
                  darkMode 
                    ? 'bg-[#0B1120] hover:bg-[#151F38] text-white' 
                    : 'bg-gray-50 hover:bg-gray-100 text-gray-900'
                } border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}
              >
                {option.text}
              </button>
            ))}
          </div>
        </div>
        
        <div className="flex justify-between">
          <div className="text-sm">
            Question {currentQuestion + 1} of {QUIZ_QUESTIONS.length}
          </div>
          
          {currentQuestion > 0 && (
            <button
              onClick={() => setCurrentQuestion(currentQuestion - 1)}
              className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} hover:underline`}
            >
              Back
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default LearningPathRecommender;
