import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Fa<PERSON>rown, FaLock, FaUnlock, FaClock, FaArrowRight, FaTimes } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import LocalStorageService from '../../services/LocalStorageService';

// Premium features to showcase
const PREMIUM_FEATURES = [
  {
    id: 'advanced-challenges',
    title: 'Advanced Challenges',
    description: 'Access to 150+ advanced cybersecurity challenges across all categories.',
    icon: 'FaTrophy',
    previewDuration: 30, // minutes
  },
  {
    id: 'learning-paths',
    title: 'Guided Learning Paths',
    description: 'Structured learning paths designed by cybersecurity experts to help you master specific skills.',
    icon: 'FaRoute',
    previewDuration: 60, // minutes
  },
  {
    id: 'virtual-labs',
    title: 'Virtual Labs',
    description: 'Hands-on practice in realistic environments with our virtual lab infrastructure.',
    icon: 'FaServer',
    previewDuration: 45, // minutes
  },
  {
    id: 'community-access',
    title: 'Community Access',
    description: 'Join our community of cybersecurity professionals and enthusiasts to share knowledge and insights.',
    icon: 'FaUsers',
    previewDuration: 120, // minutes
  },
  {
    id: 'certification-prep',
    title: 'Certification Prep',
    description: 'Materials and practice tests to help you prepare for industry certifications like OSCP, CEH, and more.',
    icon: 'FaCertificate',
    previewDuration: 90, // minutes
  },
];

const PremiumPreview = () => {
  const { darkMode } = useGlobalTheme();
  const [activeFeature, setActiveFeature] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [showBanner, setShowBanner] = useState(true);

  // Check if user has an active premium preview
  useEffect(() => {
    const savedPreview = LocalStorageService.get('premium_preview', null);

    if (savedPreview) {
      const { featureId, expiresAt } = savedPreview;
      const now = new Date().getTime();
      const expiry = new Date(expiresAt).getTime();

      if (now < expiry) {
        // Preview is still active
        setActiveFeature(featureId);
        setTimeRemaining(Math.floor((expiry - now) / 1000)); // in seconds
      } else {
        // Preview has expired
        LocalStorageService.remove('premium_preview');
      }
    }
  }, []);

  // Countdown timer for active preview
  useEffect(() => {
    if (timeRemaining > 0) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            setActiveFeature(null);
            LocalStorageService.remove('premium_preview');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [timeRemaining]);

  // Format time remaining
  const formatTimeRemaining = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Start a premium preview
  const startPreview = (featureId) => {
    const feature = PREMIUM_FEATURES.find(f => f.id === featureId);
    if (!feature) return;

    const now = new Date();
    const expiresAt = new Date(now.getTime() + feature.previewDuration * 60 * 1000);

    setActiveFeature(featureId);
    setTimeRemaining(feature.previewDuration * 60);

    // Save to localStorage
    LocalStorageService.set('premium_preview', {
      featureId,
      expiresAt: expiresAt.toISOString(),
    });
  };

  if (!showBanner) {
    return null;
  }

  // If there's an active preview
  if (activeFeature) {
    const feature = PREMIUM_FEATURES.find(f => f.id === activeFeature);

    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden mb-6`}>
        <div className="p-4">
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center mr-2">
                <FaCrown className="text-yellow-500" />
              </div>
              <h3 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Premium Preview Active
              </h3>
            </div>
            <button
              onClick={() => setShowBanner(false)}
              className={`p-1 rounded-full ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
            >
              <FaTimes className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
            </button>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} mb-4`}>
            <div className="flex justify-between items-start">
              <div>
                <h4 className={`font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {feature.title}
                </h4>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {feature.description}
                </p>
              </div>
              <div className="flex items-center bg-yellow-500/20 text-yellow-500 px-2 py-1 rounded">
                <FaClock className="mr-1" />
                <span className="font-mono">{formatTimeRemaining(timeRemaining)}</span>
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <Link
              to="/pricing"
              className="text-[#88cc14] hover:underline flex items-center"
            >
              Upgrade to Premium <FaArrowRight className="ml-1" />
            </Link>

            <button
              onClick={() => {
                setActiveFeature(null);
                setTimeRemaining(0);
                LocalStorageService.remove('premium_preview');
              }}
              className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} hover:underline`}
            >
              End Preview
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show available premium features
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden mb-6`}>
      <div className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center mr-2">
              <FaCrown className="text-yellow-500" />
            </div>
            <h3 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Try Premium Features
            </h3>
          </div>
          <button
            onClick={() => setShowBanner(false)}
            className={`p-1 rounded-full ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          >
            <FaTimes className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
          </button>
        </div>

        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
          Get a sneak peek at our premium features. Each preview is available for a limited time.
        </p>

        <div className="space-y-2 mb-4">
          {PREMIUM_FEATURES.slice(0, 3).map(feature => (
            <div
              key={feature.id}
              className={`p-3 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-between`}
            >
              <div className="flex items-center">
                <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center mr-3">
                  <FaLock className="text-yellow-500" />
                </div>
                <div>
                  <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {feature.title}
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {feature.previewDuration} min preview
                  </div>
                </div>
              </div>

              <button
                onClick={() => startPreview(feature.id)}
                className="px-3 py-1 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded flex items-center text-sm"
              >
                <FaUnlock className="mr-1" /> Try It
              </button>
            </div>
          ))}
        </div>

        <Link
          to="/pricing"
          className="block w-full py-2 px-4 text-center bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/30 rounded-lg transition-colors"
        >
          <FaCrown className="inline mr-1" /> See All Premium Features
        </Link>
      </div>
    </div>
  );
};

export default PremiumPreview;
