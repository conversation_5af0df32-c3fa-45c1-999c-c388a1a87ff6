import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { FaLightbulb, FaArrowRight, FaTimes, FaCheck } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import LocalStorageService from '../../services/LocalStorageService';

// Tour steps for different pages
const TOUR_STEPS = {
  '/challenges': [
    {
      id: 'challenges-intro',
      title: 'Welcome to Challenges',
      content: 'This is where you can find all available cybersecurity challenges to test your skills.',
      target: '.challenges-page', // CSS selector for the element to highlight
      placement: 'center',
    },
    {
      id: 'challenge-card',
      title: 'Challenge Cards',
      content: 'Each card represents a challenge. Click on a card to start the challenge.',
      target: '.challenge-card:first-child',
      placement: 'bottom',
    },
    {
      id: 'challenge-difficulty',
      title: 'Difficulty Levels',
      content: 'Challenges are categorized by difficulty. Start with beginner challenges and work your way up.',
      target: '.challenge-difficulty',
      placement: 'right',
    },
    {
      id: 'challenge-category',
      title: 'Challenge Categories',
      content: 'Challenges are organized by category, such as Web Security, Cryptography, and more.',
      target: '.challenge-category',
      placement: 'left',
    },
  ],
  '/learn': [
    {
      id: 'learn-intro',
      title: 'Welcome to Learning Modules',
      content: 'Here you can find comprehensive learning modules to build your cybersecurity knowledge.',
      target: '.learning-page',
      placement: 'center',
    },
    {
      id: 'module-card',
      title: 'Learning Module Cards',
      content: 'Each card represents a learning module. Click on a card to start learning.',
      target: '.module-card:first-child',
      placement: 'bottom',
    },
    {
      id: 'module-progress',
      title: 'Track Your Progress',
      content: 'Your progress in each module is tracked. Continue where you left off at any time.',
      target: '.module-progress',
      placement: 'right',
    },
  ],
  '/challenges/': [
    {
      id: 'challenge-simulator-intro',
      title: 'Challenge Simulator',
      content: 'This is the challenge simulator. Complete the challenge to earn points and achievements.',
      target: '.challenge-simulator',
      placement: 'center',
    },
    {
      id: 'challenge-description',
      title: 'Challenge Description',
      content: 'Read the challenge description carefully to understand what you need to do.',
      target: '.challenge-description',
      placement: 'bottom',
    },
    {
      id: 'challenge-hint',
      title: 'Hints',
      content: 'If you get stuck, you can use hints to help you solve the challenge.',
      target: '.hint-button',
      placement: 'left',
    },
    {
      id: 'challenge-console',
      title: 'Console Output',
      content: 'The console shows output from your actions. Pay attention to error messages and success indicators.',
      target: '.challenge-console',
      placement: 'top',
    },
  ],
  '/learn/': [
    {
      id: 'module-content-intro',
      title: 'Learning Module Content',
      content: 'This is the learning module content. Read through the material to learn new concepts.',
      target: '.module-content',
      placement: 'center',
    },
    {
      id: 'module-sections',
      title: 'Module Sections',
      content: 'Modules are divided into sections. Complete each section to progress through the module.',
      target: '.module-sections',
      placement: 'right',
    },
    {
      id: 'module-quiz',
      title: 'Knowledge Checks',
      content: 'Test your understanding with quizzes throughout the module.',
      target: '.module-quiz',
      placement: 'bottom',
    },
    {
      id: 'module-resources',
      title: 'Additional Resources',
      content: 'Find additional resources to deepen your understanding of the topic.',
      target: '.module-resources',
      placement: 'left',
    },
  ],
};

const GuidedTour = () => {
  const { darkMode } = useGlobalTheme();
  const location = useLocation();
  const [currentTour, setCurrentTour] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [targetElement, setTargetElement] = useState(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });

  // Find the appropriate tour for the current page
  useEffect(() => {
    // Check if we have a tour for this exact path
    let tour = TOUR_STEPS[location.pathname];

    // If not, check if we have a tour for a path prefix
    if (!tour) {
      const pathPrefix = Object.keys(TOUR_STEPS).find(prefix =>
        prefix.endsWith('/') && location.pathname.startsWith(prefix)
      );

      if (pathPrefix) {
        tour = TOUR_STEPS[pathPrefix];
      }
    }

    // Check if the user has already seen this tour
    if (tour) {
      const tourKey = `tour_${location.pathname.replace(/\//g, '_')}`;
      const tourCompleted = LocalStorageService.get(tourKey, false);

      if (!tourCompleted) {
        setCurrentTour(tour);
        setCurrentStep(0);
        setIsVisible(true);

        // Mark this tour as seen
        LocalStorageService.set(tourKey, true);
      }
    }
  }, [location.pathname]);

  // Update target element and position when current step changes
  useEffect(() => {
    if (!currentTour || !isVisible) return;

    const step = currentTour[currentStep];
    if (!step) return;

    // Find the target element
    const element = document.querySelector(step.target);
    setTargetElement(element);

    if (element && step.placement !== 'center') {
      // Calculate position based on element and placement
      const rect = element.getBoundingClientRect();
      let top, left;

      switch (step.placement) {
        case 'top':
          top = rect.top - 10 - 150; // 150px is the height of the tour box
          left = rect.left + rect.width / 2 - 150; // 150px is half the width of the tour box
          break;
        case 'bottom':
          top = rect.bottom + 10;
          left = rect.left + rect.width / 2 - 150;
          break;
        case 'left':
          top = rect.top + rect.height / 2 - 75;
          left = rect.left - 10 - 300; // 300px is the width of the tour box
          break;
        case 'right':
          top = rect.top + rect.height / 2 - 75;
          left = rect.right + 10;
          break;
        default:
          top = rect.top + rect.height / 2 - 75;
          left = rect.left + rect.width / 2 - 150;
      }

      // Ensure the tour box stays within the viewport
      top = Math.max(10, Math.min(window.innerHeight - 160, top));
      left = Math.max(10, Math.min(window.innerWidth - 310, left));

      setPosition({ top, left });
    } else {
      // Center in the viewport
      setPosition({
        top: window.innerHeight / 2 - 75,
        left: window.innerWidth / 2 - 150,
      });
    }

    // Highlight the target element
    if (element) {
      element.classList.add('tour-highlight');

      return () => {
        element.classList.remove('tour-highlight');
      };
    }
  }, [currentTour, currentStep, isVisible]);

  // Handle next step
  const handleNext = () => {
    if (currentStep < currentTour.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // End of tour
      setIsVisible(false);
      setCurrentTour(null);
    }
  };

  // Handle skip
  const handleSkip = () => {
    setIsVisible(false);
    setCurrentTour(null);
  };

  if (!isVisible || !currentTour) {
    return null;
  }

  const step = currentTour[currentStep];

  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-50"
        onClick={handleSkip}
      ></div>

      {/* Tour Box */}
      <div
        className={`fixed z-50 w-80 ${
          darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'
        } border rounded-lg shadow-lg overflow-hidden`}
        style={{
          top: `${position.top}px`,
          left: `${position.left}px`,
          transition: 'top 0.3s ease, left 0.3s ease'
        }}
      >
        <div className="p-4">
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-[#88cc14]/20 rounded-full flex items-center justify-center mr-2">
                <FaLightbulb className="text-[#88cc14]" />
              </div>
              <h3 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {step.title}
              </h3>
            </div>
            <button
              onClick={handleSkip}
              className={`p-1 rounded-full ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
            >
              <FaTimes className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
            </button>
          </div>

          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
            {step.content}
          </p>

          <div className="flex justify-between items-center">
            <div className="flex space-x-1">
              {currentTour.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === currentStep
                      ? 'bg-[#88cc14]'
                      : darkMode ? 'bg-gray-700' : 'bg-gray-300'
                  }`}
                ></div>
              ))}
            </div>

            <div className="flex space-x-2">
              <button
                onClick={handleSkip}
                className={`px-3 py-1 text-sm rounded ${
                  darkMode
                    ? 'hover:bg-gray-800 text-gray-400'
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                Skip
              </button>

              <button
                onClick={handleNext}
                className="px-3 py-1 text-sm bg-[#88cc14] hover:bg-[#7ab811] text-black rounded flex items-center"
              >
                {currentStep < currentTour.length - 1 ? (
                  <>Next <FaArrowRight className="ml-1" /></>
                ) : (
                  <>Done <FaCheck className="ml-1" /></>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Add global styles for tour highlighting */}
      <style>
        {`
          .tour-highlight {
            position: relative;
            z-index: 51;
            box-shadow: 0 0 0 4px rgba(136, 204, 20, 0.5);
            border-radius: 4px;
          }
        `}
      </style>
    </>
  );
};

export default GuidedTour;
