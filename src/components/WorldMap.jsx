import React, { useState, useEffect, memo } from 'react';
import { 
  ComposableMap, 
  Geographies, 
  Geography, 
  Marker,
  Line,
  ZoomableGroup
} from "react-simple-maps";
import { motion } from 'framer-motion';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

// URL to a GeoJSON containing all world countries
const geoUrl = "https://raw.githubusercontent.com/deldersveld/topojson/master/world-countries.json";

const WorldMap = ({ attacks, onCountryHover, onCountryClick }) => {
  const { darkMode } = useGlobalTheme();
  const [tooltipContent, setTooltipContent] = useState("");
  const [position, setPosition] = useState({ coordinates: [0, 0], zoom: 1 });
  const [selectedCountry, setSelectedCountry] = useState(null);
  
  // Map country codes to coordinates for attack visualization
  const countryCoordinates = {
    USA: [-95.7129, 37.0902],
    RUS: [105.3188, 61.5240],
    CHN: [104.1954, 35.8617],
    IND: [78.9629, 20.5937],
    BRA: [-51.9253, -14.2350],
    GBR: [-3.4360, 55.3781],
    DEU: [10.4515, 51.1657],
    AUS: [133.7751, -25.2744],
    JPN: [138.2529, 36.2048],
    CAN: [-106.3468, 56.1304],
    FRA: [2.2137, 46.2276],
    ITA: [12.5674, 41.8719],
    MEX: [-102.5528, 23.6345],
    ZAF: [22.9375, -30.5595],
    // Add more countries as needed
  };

  const handleMoveEnd = (position) => {
    setPosition(position);
  };

  const handleCountryHover = (geo) => {
    const { NAME, ISO_A3 } = geo.properties;
    setTooltipContent(`${NAME} (${ISO_A3})`);
    if (onCountryHover) onCountryHover({ name: NAME, code: ISO_A3 });
  };

  const handleCountryClick = (geo) => {
    const { NAME, ISO_A3 } = geo.properties;
    setSelectedCountry(ISO_A3);
    if (onCountryClick) onCountryClick({ name: NAME, code: ISO_A3 });
  };

  // Calculate curved path between two points
  const getCurvedPath = (start, end) => {
    const x1 = start[0];
    const y1 = start[1];
    const x2 = end[0];
    const y2 = end[1];
    
    // Calculate control point for the curve
    const mpx = (x2 + x1) * 0.5;
    const mpy = (y2 + y1) * 0.5;
    const angle = Math.atan2(y2 - y1, x2 - x1) - Math.PI / 2;
    const distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    const controlX = mpx + Math.cos(angle) * distance * 0.3;
    const controlY = mpy + Math.sin(angle) * distance * 0.3;
    
    return `M ${x1} ${y1} Q ${controlX} ${controlY} ${x2} ${y2}`;
  };

  return (
    <div className="relative w-full h-full">
      <ComposableMap
        projection="geoMercator"
        projectionConfig={{
          scale: 150,
        }}
        className={darkMode ? "dark-map" : "light-map"}
      >
        <ZoomableGroup
          zoom={position.zoom}
          center={position.coordinates}
          onMoveEnd={handleMoveEnd}
          maxZoom={5}
        >
          <Geographies geography={geoUrl}>
            {({ geographies }) =>
              geographies.map((geo) => (
                <Geography
                  key={geo.rsmKey}
                  geography={geo}
                  fill={selectedCountry === geo.properties.ISO_A3 
                    ? (darkMode ? "#88cc14" : "#3A5E8C") 
                    : (darkMode ? "#1a2e44" : "#d1d5db")}
                  stroke={darkMode ? "#3A5E8C" : "#88cc14"}
                  strokeWidth={0.5}
                  style={{
                    default: { outline: "none" },
                    hover: { 
                      fill: darkMode ? "#2a3e54" : "#e5e7eb",
                      outline: "none",
                      cursor: "pointer"
                    },
                    pressed: { outline: "none" }
                  }}
                  onMouseEnter={() => handleCountryHover(geo)}
                  onMouseLeave={() => setTooltipContent("")}
                  onClick={() => handleCountryClick(geo)}
                />
              ))
            }
          </Geographies>

          {/* Attack Visualizations */}
          {attacks.filter(attack => attack.active).map((attack, i) => {
            const sourceCoords = countryCoordinates[attack.source.code];
            const targetCoords = countryCoordinates[attack.target.code];
            
            if (!sourceCoords || !targetCoords) return null;
            
            const pathString = getCurvedPath(sourceCoords, targetCoords);
            
            return (
              <g key={`attack-${i}`}>
                {/* Attack Path */}
                <defs>
                  <linearGradient id={`gradient-${i}`} x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor={attack.severity === 'Critical' ? '#ef4444' : attack.severity === 'High' ? '#f97316' : '#eab308'} />
                    <stop offset="100%" stopColor="#88cc14" />
                  </linearGradient>
                </defs>
                
                <path
                  d={pathString}
                  fill="none"
                  stroke={`url(#gradient-${i})`}
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeDasharray="5,5"
                  className="animate-pulse"
                />
                
                {/* Source Marker */}
                <Marker coordinates={sourceCoords}>
                  <circle
                    r={4}
                    fill={attack.severity === 'Critical' ? '#ef4444' : attack.severity === 'High' ? '#f97316' : '#eab308'}
                    className="animate-pulse"
                  />
                </Marker>
                
                {/* Target Marker */}
                <Marker coordinates={targetCoords}>
                  <circle
                    r={5}
                    fill="#88cc14"
                    className="animate-pulse"
                  />
                </Marker>
                
                {/* Animated Particles */}
                {[...Array(3)].map((_, j) => (
                  <circle
                    key={`particle-${i}-${j}`}
                    r={3}
                    fill={attack.severity === 'Critical' ? '#ef4444' : attack.severity === 'High' ? '#f97316' : '#eab308'}
                  >
                    <animateMotion
                      path={pathString}
                      dur={`${1.5 + j * 0.5}s`}
                      repeatCount="indefinite"
                    />
                  </circle>
                ))}
              </g>
            );
          })}
        </ZoomableGroup>
      </ComposableMap>
      
      {/* Tooltip */}
      {tooltipContent && (
        <div className="absolute bottom-4 left-4 bg-black/70 text-white p-3 rounded-lg z-50 backdrop-blur-sm border border-gray-700">
          <div className="text-sm font-bold">{tooltipContent}</div>
        </div>
      )}
      
      {/* Map Controls */}
      <div className="absolute top-2 right-2 z-40 flex space-x-2">
        <button 
          onClick={() => setPosition(prev => ({ ...prev, zoom: Math.min(prev.zoom + 0.5, 5) }))}
          className="bg-black/40 hover:bg-black/60 text-white w-8 h-8 rounded-full flex items-center justify-center transition-all"
        >
          +
        </button>
        <button 
          onClick={() => setPosition(prev => ({ ...prev, zoom: Math.max(prev.zoom - 0.5, 1) }))}
          className="bg-black/40 hover:bg-black/60 text-white w-8 h-8 rounded-full flex items-center justify-center transition-all"
        >
          -
        </button>
        <button 
          onClick={() => setPosition({ coordinates: [0, 0], zoom: 1 })}
          className="bg-black/40 hover:bg-black/60 text-white w-8 h-8 rounded-full flex items-center justify-center transition-all text-xs"
        >
          Reset
        </button>
      </div>
    </div>
  );
};

export default memo(WorldMap);
