import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaLock, FaTrophy, FaGraduationCap, FaClock, FaArrowRight, FaCode, FaShieldAlt, FaSearch, FaNetworkWired, FaKey, FaMicrochip } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

const PreLoginContent = () => {
  const { darkMode } = useGlobalTheme();

  // Static challenges data
  const challenges = [
    {
      id: 'c1',
      slug: 'sql-injection-basics',
      title: 'SQL Injection Basics',
      description: 'Learn the fundamentals of SQL injection by exploiting a vulnerable login form. This challenge introduces you to one of the most common web application vulnerabilities.',
      category: { name: 'Web Security' },
      difficulty: { name: 'Begin<PERSON>' },
      type: { name: 'Exploitation' },
      points: 100,
      coin_reward: 10,
      estimated_time: 20,
      icon: <FaCode />
    },
    {
      id: 'c2',
      slug: 'password-cracking-basics',
      title: 'Password Cracking Basics',
      description: 'Learn the fundamentals of password cracking by breaking a series of increasingly complex password hashes. This challenge introduces you to common password cracking tools and techniques.',
      category: { name: 'Cryptography' },
      difficulty: { name: 'Beginner' },
      type: { name: 'Analysis' },
      points: 100,
      coin_reward: 10,
      estimated_time: 25,
      icon: <FaKey />
    },
    {
      id: 'c3',
      slug: 'network-traffic-analysis',
      title: 'Network Traffic Analysis',
      description: 'Analyze captured network traffic to identify suspicious activities and extract hidden information. This challenge introduces you to packet analysis and network forensics.',
      category: { name: 'Network Security' },
      difficulty: { name: 'Easy' },
      type: { name: 'Analysis' },
      points: 150,
      coin_reward: 15,
      estimated_time: 30,
      icon: <FaNetworkWired />
    },
    {
      id: 'c4',
      slug: 'osint-investigation',
      title: 'OSINT Investigation',
      description: 'Use Open Source Intelligence techniques to gather information about a fictional target. This challenge introduces you to the power of publicly available information.',
      category: { name: 'OSINT' },
      difficulty: { name: 'Easy' },
      type: { name: 'Reconnaissance' },
      points: 150,
      coin_reward: 15,
      estimated_time: 35,
      icon: <FaSearch />
    },
    {
      id: 'c5',
      slug: 'basic-steganography',
      title: 'Basic Steganography',
      description: 'Discover hidden messages concealed within digital images. This challenge introduces you to steganography techniques and tools.',
      category: { name: 'Forensics' },
      difficulty: { name: 'Easy' },
      type: { name: 'Analysis' },
      points: 150,
      coin_reward: 15,
      estimated_time: 25,
      icon: <FaSearch />
    },
    {
      id: 'c6',
      slug: 'simple-binary-analysis',
      title: 'Simple Binary Analysis',
      description: 'Analyze a simple executable file to understand its behavior and find hidden functionality. This challenge introduces you to basic reverse engineering concepts.',
      category: { name: 'Reverse Engineering' },
      difficulty: { name: 'Medium' },
      type: { name: 'Analysis' },
      points: 200,
      coin_reward: 20,
      estimated_time: 40,
      icon: <FaMicrochip />
    }
  ];

  // Static learning modules data
  const modules = [
    {
      id: 'm1',
      slug: 'intro-to-cybersecurity',
      title: 'Introduction to Cybersecurity',
      description: 'Learn the fundamentals of cybersecurity, including key concepts, common threats, and basic security practices. This module provides a solid foundation for beginners.',
      category: { name: 'Web Security' },
      difficulty: { name: 'Beginner' },
      estimated_time: 60,
      icon: <FaShieldAlt />
    },
    {
      id: 'm2',
      slug: 'web-security-fundamentals',
      title: 'Web Security Fundamentals',
      description: 'Learn about common web vulnerabilities, how they are exploited, and best practices for securing web applications. This module covers essential web security concepts.',
      category: { name: 'Web Security' },
      difficulty: { name: 'Beginner' },
      estimated_time: 75,
      icon: <FaCode />
    },
    {
      id: 'm3',
      slug: 'network-security-basics',
      title: 'Network Security Basics',
      description: 'Learn the fundamentals of network security, including common network attacks, defense mechanisms, and monitoring techniques. This module provides essential knowledge for securing networks.',
      category: { name: 'Network Security' },
      difficulty: { name: 'Beginner' },
      estimated_time: 70,
      icon: <FaNetworkWired />
    }
  ];

  // No loading or error states needed with static data

  // Format time (minutes to hours and minutes)
  const formatTime = (minutes) => {
    if (!minutes) return 'N/A';

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours === 0) return `${mins} min`;
    if (mins === 0) return `${hours} hr`;
    return `${hours} hr ${mins} min`;
  };

  return (
    <div className="py-16">
      <div className="container mx-auto px-4">
        {/* Challenges Section */}
        <div className="mb-16">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-3xl font-bold flex items-center">
                <FaTrophy className="text-[#88cc14] mr-3" /> Featured Challenges
              </h2>
              <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Test your cybersecurity skills with these hands-on challenges
              </p>
            </div>
            <Link
              to="/challenges"
              className="flex items-center text-[#88cc14] hover:underline"
            >
              View All Challenges <FaArrowRight className="ml-2" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {challenges.map(challenge => (
              <div
                key={challenge.id}
                className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden transition-transform hover:transform hover:scale-[1.02]`}
              >
                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold">{challenge.title}</h3>
                    <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
                      {challenge.icon}
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-3">
                    <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                      {challenge.category.name}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                      {challenge.difficulty.name}
                    </span>
                    {challenge.estimated_time && (
                      <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
                        <FaClock className="mr-1" /> {formatTime(challenge.estimated_time)}
                      </span>
                    )}
                  </div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 line-clamp-2`}>
                    {challenge.description}
                  </p>

                  <div className="flex justify-between items-center">
                    <span className="flex items-center">
                      <FaTrophy className="mr-1 text-yellow-500" /> {challenge.points} points
                    </span>

                    <Link
                      to={`/challenges/${challenge.slug || challenge.id}`}
                      className="px-4 py-2 theme-button-primary rounded-lg"
                    >
                      Try Challenge
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Learning Modules Section */}
        <div>
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-3xl font-bold flex items-center">
                <FaGraduationCap className="text-[#88cc14] mr-3" /> Learning Modules
              </h2>
              <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Start your cybersecurity journey with these introductory modules
              </p>
            </div>
            <Link
              to="/learn"
              className="flex items-center text-[#88cc14] hover:underline"
            >
              View All Modules <FaArrowRight className="ml-2" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {modules.map(module => (
              <div
                key={module.id}
                className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden transition-transform hover:transform hover:scale-[1.02]`}
              >
                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold">{module.title}</h3>
                    <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
                      {module.icon}
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-3">
                    <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                      {module.category.name}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                      {module.difficulty.name}
                    </span>
                    {module.estimated_time && (
                      <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
                        <FaClock className="mr-1" /> {formatTime(module.estimated_time)}
                      </span>
                    )}
                  </div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 line-clamp-2`}>
                    {module.description}
                  </p>

                  <Link
                    to={`/learn/${module.slug || module.id}`}
                    className="w-full px-4 py-2 theme-button-primary rounded-lg flex justify-center items-center"
                  >
                    Start Learning
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {/* Premium Content Teaser */}
          <div className={`mt-12 ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-blue-50 border-blue-200'} border rounded-lg p-8 text-center`}>
            <FaLock className="mx-auto text-4xl mb-4 text-[#88cc14]" />
            <h2 className="text-2xl font-bold mb-2">Unlock Premium Content</h2>
            <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} max-w-2xl mx-auto mb-6`}>
              Upgrade to premium to access all learning modules, challenges, and exclusive features including real Linux boxes for hands-on practice.
            </p>
            <Link
              to="/pricing"
              className="px-6 py-3 theme-button-primary rounded-lg inline-block font-semibold"
            >
              View Pricing Plans
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreLoginContent;
