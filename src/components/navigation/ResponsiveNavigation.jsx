import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FaBars, FaTimes } from 'react-icons/fa';
import { useIsMobile } from '../../hooks/useMediaQuery';

/**
 * A responsive navigation component
 * @param {Object} props - The component props
 * @param {Array} props.items - The navigation items
 * @param {React.ReactNode} props.logo - The logo component
 * @param {React.ReactNode} props.actions - Additional actions (e.g. buttons)
 * @param {string} props.className - Additional classes
 * @returns {React.ReactNode} - The responsive navigation
 */
const ResponsiveNavigation = ({ 
  items = [], 
  logo, 
  actions,
  className = '',
  ...props 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();
  const location = useLocation();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [location.pathname]);

  // Close mobile menu when screen size changes to desktop
  useEffect(() => {
    if (!isMobile) {
      setIsOpen(false);
    }
  }, [isMobile]);

  // Prevent scrolling when mobile menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  return (
    <nav 
      className={`fixed top-0 left-0 right-0 z-50 bg-[#0B1120] shadow-lg ${className}`}
      {...props}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            {logo}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {items.map((item, index) => (
              <Link
                key={index}
                to={item.to}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  location.pathname === item.to
                    ? 'text-[#88cc14] bg-[#88cc14]/10'
                    : 'text-gray-300 hover:text-white hover:bg-gray-800'
                }`}
              >
                <div className="flex items-center gap-2">
                  {item.icon && <item.icon />}
                  <span>{item.text}</span>
                </div>
              </Link>
            ))}

            {/* Additional Actions */}
            {actions && (
              <div className="ml-4">
                {actions}
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-800 focus:outline-none"
            >
              {isOpen ? <FaTimes /> : <FaBars />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-[#1A1F35]"
          >
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {items.map((item, index) => (
                <Link
                  key={index}
                  to={item.to}
                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    location.pathname === item.to
                      ? 'text-[#88cc14] bg-[#88cc14]/10'
                      : 'text-gray-300 hover:text-white hover:bg-gray-800'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    {item.icon && <item.icon />}
                    <span>{item.text}</span>
                  </div>
                </Link>
              ))}

              {/* Mobile Actions */}
              {actions && (
                <div className="mt-4 pt-4 border-t border-gray-800">
                  {actions}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default ResponsiveNavigation;
