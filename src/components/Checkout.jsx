import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaLock, FaCreditCard, FaPaypal } from 'react-icons/fa';
import AddressForm from './AddressForm';

function Checkout({ cart, addresses, onAddAddress, onPlaceOrder }) {
  const [step, setStep] = useState(1);
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState(null);

  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const shipping = cart.some(item => item.is_physical) ? 99 : 0;
  const total = subtotal + shipping;

  const handleAddressSubmit = async (addressData) => {
    const newAddress = await onAddAddress(addressData);
    setSelectedAddress(newAddress.id);
    setStep(2);
  };

  const handlePaymentSubmit = async () => {
    await onPlaceOrder({
      addressId: selectedAddress,
      paymentMethod,
      total
    });
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Steps */}
      <div className="flex items-center justify-between mb-8">
        {[
          { number: 1, title: 'Shipping' },
          { number: 2, title: 'Payment' },
          { number: 3, title: 'Review' }
        ].map((s) => (
          <div
            key={s.number}
            className={`flex items-center ${s.number < step ? 'text-[#88cc14]' : s.number === step ? 'text-gray-900' : 'text-gray-400'}`}
          >
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              s.number < step ? 'bg-[#88cc14] text-white' :
              s.number === step ? 'bg-gray-900 text-white' :
              'bg-gray-200 text-gray-600'
            }`}>
              {s.number}
            </div>
            <span className="ml-2 font-medium">{s.title}</span>
            {s.number < 3 && (
              <div className="mx-4 h-px w-16 bg-gray-200" />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        {step === 1 && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Shipping Address</h2>
            
            {/* Existing Addresses */}
            {addresses.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Saved Addresses</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {addresses.map((address) => (
                    <div
                      key={address.id}
                      onClick={() => setSelectedAddress(address.id)}
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                        selectedAddress === address.id
                          ? 'border-[#88cc14] bg-[#88cc14]/5'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-gray-900">{address.full_name}</div>
                      <div className="text-gray-600 text-sm">
                        {address.address_line1}
                        {address.address_line2 && <>, {address.address_line2}</>}
                      </div>
                      <div className="text-gray-600 text-sm">
                        {address.city}, {address.state} {address.postal_code}
                      </div>
                      <div className="text-gray-600 text-sm">{address.country}</div>
                      <div className="text-gray-600 text-sm">{address.phone}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* New Address Form */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {addresses.length > 0 ? 'Add New Address' : 'Enter Shipping Address'}
              </h3>
              <AddressForm onSubmit={handleAddressSubmit} />
            </div>
          </div>
        )}

        {step === 2 && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Payment Method</h2>
            
            <div className="space-y-4">
              {[
                { id: 'card', icon: FaCreditCard, title: 'Credit/Debit Card' },
                { id: 'paypal', icon: FaPaypal, title: 'PayPal' }
              ].map((method) => (
                <div
                  key={method.id}
                  onClick={() => setPaymentMethod(method.id)}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                    paymentMethod === method.id
                      ? 'border-[#88cc14] bg-[#88cc14]/5'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <method.icon className="text-2xl" />
                    <span className="font-medium">{method.title}</span>
                  </div>
                </div>
              ))}
            </div>

            {paymentMethod === 'card' && (
              <div className="mt-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Number
                  </label>
                  <input
                    type="text"
                    placeholder="1234 5678 9012 3456"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Expiry Date
                    </label>
                    <input
                      type="text"
                      placeholder="MM/YY"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      CVV
                    </label>
                    <input
                      type="text"
                      placeholder="123"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {step === 3 && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Order Summary</h2>
            
            <div className="space-y-4 mb-6">
              {cart.map((item) => (
                <div key={item.id} className="flex items-center gap-4">
                  <img
                    src={item.image_url}
                    alt={item.name}
                    className="w-16 h-16 object-cover rounded"
                  />
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{item.name}</h3>
                    <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
                  </div>
                  <div className="font-medium text-gray-900">
                    ₹{(item.price * item.quantity).toFixed(2)}
                  </div>
                </div>
              ))}
            </div>

            <div className="border-t border-gray-200 pt-4 space-y-2">
              <div className="flex justify-between text-gray-600">
                <span>Subtotal</span>
                <span>₹{subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-gray-600">
                <span>Shipping</span>
                <span>{shipping > 0 ? `₹${shipping.toFixed(2)}` : 'Free'}</span>
              </div>
              <div className="flex justify-between text-xl font-bold text-gray-900 pt-2">
                <span>Total</span>
                <span>₹{total.toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="mt-8 flex justify-between">
          {step > 1 && (
            <button
              onClick={() => setStep(step - 1)}
              className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Back
            </button>
          )}
          {step < 3 ? (
            <button
              onClick={() => setStep(step + 1)}
              disabled={step === 1 && !selectedAddress || step === 2 && !paymentMethod}
              className="ml-auto bg-[#88cc14] text-black font-bold px-6 py-2 rounded-lg hover:bg-[#7ab811] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Continue
            </button>
          ) : (
            <button
              onClick={handlePaymentSubmit}
              className="ml-auto bg-[#88cc14] text-black font-bold px-6 py-2 rounded-lg hover:bg-[#7ab811] transition-colors flex items-center gap-2"
            >
              <FaLock />
              Place Order
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

export default Checkout;