import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaLock, FaCrown, FaArrowRight, FaGraduationCap, FaGamepad, FaCode, FaChartLine, FaSearch } from 'react-icons/fa';
import { Link } from 'react-router-dom';

const DemoOverlay = ({ isOpen, onClose, demoType = 'learn' }) => {
  const [activeTab, setActiveTab] = useState(0);

  // Demo content based on type
  const getDemoContent = () => {
    switch(demoType) {
      case 'learn':
        return {
          title: 'Advanced Learning Paths',
          description: 'Structured learning paths designed by cybersecurity experts',
          features: [
            'Interactive lessons with hands-on labs',
            'AI-powered personalized learning recommendations',
            'Progress tracking and skill assessment',
            'Certification preparation materials',
            'Expert-curated content updated weekly'
          ],
          tabs: [
            {
              title: 'Web Security',
              content: (
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-primary">Web Application Security Path</h3>
                  <p className="text-gray-300">Master the art of finding and exploiting web vulnerabilities</p>

                  <div className="bg-[#1A1F35] p-4 rounded-lg">
                    <h4 className="font-bold mb-2 flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <FaCode className="text-primary text-xs" />
                      </div>
                      Module 1: HTTP Fundamentals
                    </h4>
                    <ul className="space-y-2 text-sm text-gray-400 ml-8">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        Understanding HTTP Requests and Responses
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        Headers, Methods, and Status Codes
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        Cookies and Session Management
                      </li>
                    </ul>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <h4 className="font-bold mb-2 flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <FaLock className="text-primary text-xs" />
                      </div>
                      Module 2: Cross-Site Scripting (XSS)
                    </h4>
                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <h4 className="font-bold mb-2 flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <FaLock className="text-primary text-xs" />
                      </div>
                      Module 3: SQL Injection
                    </h4>
                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>
                </div>
              )
            },
            {
              title: 'Network Security',
              content: (
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-primary">Network Penetration Testing</h3>
                  <p className="text-gray-300">Learn how to identify and exploit network vulnerabilities</p>

                  <div className="bg-[#1A1F35] p-4 rounded-lg">
                    <h4 className="font-bold mb-2 flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <FaChartLine className="text-primary text-xs" />
                      </div>
                      Module 1: Network Reconnaissance
                    </h4>
                    <ul className="space-y-2 text-sm text-gray-400 ml-8">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        Passive vs Active Reconnaissance
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        OSINT Techniques
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        Network Mapping Basics
                      </li>
                    </ul>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <h4 className="font-bold mb-2 flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <FaLock className="text-primary text-xs" />
                      </div>
                      Module 2: Port Scanning and Enumeration
                    </h4>
                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <h4 className="font-bold mb-2 flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <FaLock className="text-primary text-xs" />
                      </div>
                      Module 3: Vulnerability Assessment
                    </h4>
                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>
                </div>
              )
            },
            {
              title: 'Cloud Security',
              content: (
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-primary">Cloud Security Fundamentals</h3>
                  <p className="text-gray-300">Secure cloud environments and understand cloud-specific threats</p>

                  <div className="bg-[#1A1F35] p-4 rounded-lg">
                    <h4 className="font-bold mb-2 flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <FaChartLine className="text-primary text-xs" />
                      </div>
                      Module 1: Cloud Architecture
                    </h4>
                    <ul className="space-y-2 text-sm text-gray-400 ml-8">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        Cloud Service Models (IaaS, PaaS, SaaS)
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        Shared Responsibility Model
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        Cloud Deployment Models
                      </li>
                    </ul>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <h4 className="font-bold mb-2 flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <FaLock className="text-primary text-xs" />
                      </div>
                      Module 2: AWS Security
                    </h4>
                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <h4 className="font-bold mb-2 flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <FaLock className="text-primary text-xs" />
                      </div>
                      Module 3: Azure Security
                    </h4>
                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>
                </div>
              )
            }
          ]
        };
      case 'hack':
        return {
          title: 'Interactive Hacking Challenges',
          description: 'Real-world hacking scenarios in a safe, controlled environment',
          features: [
            'Realistic virtual machines and networks',
            'Guided and unguided hacking challenges',
            'Multiple difficulty levels from beginner to expert',
            'Detailed walkthroughs and solutions',
            'Regular updates with new challenges'
          ],
          tabs: [
            {
              title: 'Web Exploitation',
              content: (
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-primary">Web Exploitation Lab</h3>
                  <p className="text-gray-300">Practice exploiting common web vulnerabilities in a safe environment</p>

                  <div className="bg-[#1A1F35] p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-bold flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                          <FaCode className="text-green-500 text-xs" />
                        </div>
                        Vulnerable Blog
                      </h4>
                      <span className="px-2 py-0.5 bg-green-500/20 text-green-500 rounded text-xs">Easy</span>
                    </div>

                    <p className="text-sm text-gray-400 mb-4">
                      A simple blog application with multiple vulnerabilities including SQL injection and XSS.
                    </p>

                    <div className="flex justify-between items-center">
                      <div className="text-xs text-gray-500">
                        <span className="text-green-500">32%</span> completion rate
                      </div>
                      <button className="px-3 py-1 bg-primary text-black rounded-full text-xs font-medium">
                        Try Demo
                      </button>
                    </div>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-bold flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-yellow-500/20 flex items-center justify-center">
                          <FaLock className="text-yellow-500 text-xs" />
                        </div>
                        E-commerce Platform
                      </h4>
                      <span className="px-2 py-0.5 bg-yellow-500/20 text-yellow-500 rounded text-xs">Medium</span>
                    </div>

                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-bold flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center">
                          <FaLock className="text-red-500 text-xs" />
                        </div>
                        Banking Application
                      </h4>
                      <span className="px-2 py-0.5 bg-red-500/20 text-red-500 rounded text-xs">Hard</span>
                    </div>

                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>
                </div>
              )
            },
            {
              title: 'Network Exploitation',
              content: (
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-primary">Network Exploitation Lab</h3>
                  <p className="text-gray-300">Practice network penetration testing techniques</p>

                  <div className="bg-[#1A1F35] p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-bold flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                          <FaChartLine className="text-green-500 text-xs" />
                        </div>
                        Basic Network
                      </h4>
                      <span className="px-2 py-0.5 bg-green-500/20 text-green-500 rounded text-xs">Easy</span>
                    </div>

                    <p className="text-sm text-gray-400 mb-4">
                      A simple network with vulnerable services. Perfect for beginners.
                    </p>

                    <div className="flex justify-between items-center">
                      <div className="text-xs text-gray-500">
                        <span className="text-green-500">45%</span> completion rate
                      </div>
                      <button className="px-3 py-1 bg-primary text-black rounded-full text-xs font-medium">
                        Try Demo
                      </button>
                    </div>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-bold flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-yellow-500/20 flex items-center justify-center">
                          <FaLock className="text-yellow-500 text-xs" />
                        </div>
                        Corporate Network
                      </h4>
                      <span className="px-2 py-0.5 bg-yellow-500/20 text-yellow-500 rounded text-xs">Medium</span>
                    </div>

                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-bold flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center">
                          <FaLock className="text-red-500 text-xs" />
                        </div>
                        Active Directory
                      </h4>
                      <span className="px-2 py-0.5 bg-red-500/20 text-red-500 rounded text-xs">Hard</span>
                    </div>

                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>
                </div>
              )
            },
            {
              title: 'CTF Challenges',
              content: (
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-primary">Capture The Flag Challenges</h3>
                  <p className="text-gray-300">Test your skills with our CTF-style challenges</p>

                  <div className="bg-[#1A1F35] p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-bold flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                          <FaCode className="text-green-500 text-xs" />
                        </div>
                        Crypto Basics
                      </h4>
                      <span className="px-2 py-0.5 bg-green-500/20 text-green-500 rounded text-xs">Easy</span>
                    </div>

                    <p className="text-sm text-gray-400 mb-4">
                      A series of basic cryptography challenges to test your knowledge.
                    </p>

                    <div className="flex justify-between items-center">
                      <div className="text-xs text-gray-500">
                        <span className="text-green-500">60%</span> completion rate
                      </div>
                      <button className="px-3 py-1 bg-primary text-black rounded-full text-xs font-medium">
                        Try Demo
                      </button>
                    </div>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-bold flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-yellow-500/20 flex items-center justify-center">
                          <FaLock className="text-yellow-500 text-xs" />
                        </div>
                        Reverse Engineering
                      </h4>
                      <span className="px-2 py-0.5 bg-yellow-500/20 text-yellow-500 rounded text-xs">Medium</span>
                    </div>

                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>

                  <div className="bg-[#1A1F35] p-4 rounded-lg opacity-60">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-bold flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center">
                          <FaLock className="text-red-500 text-xs" />
                        </div>
                        Pwn Challenges
                      </h4>
                      <span className="px-2 py-0.5 bg-red-500/20 text-red-500 rounded text-xs">Hard</span>
                    </div>

                    <div className="flex justify-center items-center py-4">
                      <FaLock className="text-primary text-xl" />
                      <span className="ml-2 text-gray-400">Premium Content</span>
                    </div>
                  </div>
                </div>
              )
            }
          ]
        };
      default:
        return {
          title: 'Premium Features',
          description: 'Unlock all features with a premium subscription',
          features: [],
          tabs: []
        };
    }
  };

  const content = getDemoContent();

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-[#0F172A] rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-xl border border-gray-800"
          >
            {/* Header */}
            <div className="p-6 border-b border-gray-800 flex justify-between items-center">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                  {demoType === 'learn' ? (
                    <FaGraduationCap className="text-primary" />
                  ) : (
                    <FaGamepad className="text-primary" />
                  )}
                </div>
                <div>
                  <h2 className="text-xl font-bold">{content.title}</h2>
                  <p className="text-sm text-gray-400">{content.description}</p>
                </div>
              </div>

              <button
                onClick={onClose}
                className="text-gray-500 hover:text-white transition-colors"
              >
                <FaTimes />
              </button>
            </div>

            {/* Content */}
            <div className="flex flex-col md:flex-row h-[70vh]">
              {/* Left Panel - Features */}
              <div className="w-full md:w-64 bg-[#1A1F35] p-6 border-r border-gray-800 overflow-y-auto">
                <div className="mb-6">
                  <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                    <FaCrown className="text-yellow-500" />
                    <span>Premium Features</span>
                  </h3>

                  <ul className="space-y-3">
                    {content.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <FaCheck className="text-primary mt-1 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="pt-4 border-t border-gray-800">
                  <Link
                    to="/pricing"
                    className="bg-primary hover:bg-primary-hover text-black font-bold py-2 px-4 rounded-lg w-full block text-center transition-colors"
                  >
                    Upgrade Now
                  </Link>

                  <p className="text-xs text-gray-500 mt-2 text-center">
                    Starting at just $9.99/month
                  </p>
                </div>
              </div>

              {/* Right Panel - Demo Content */}
              <div className="flex-1 flex flex-col overflow-hidden">
                {/* Tabs */}
                {content.tabs.length > 0 && (
                  <div className="border-b border-gray-800 overflow-x-auto">
                    <div className="flex">
                      {content.tabs.map((tab, index) => (
                        <button
                          key={index}
                          onClick={() => setActiveTab(index)}
                          className={`px-6 py-3 text-sm whitespace-nowrap transition-colors ${
                            activeTab === index
                              ? 'border-b-2 border-primary text-white font-medium'
                              : 'text-gray-500 hover:text-gray-300'
                          }`}
                        >
                          {tab.title}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Tab Content */}
                <div className="flex-1 overflow-y-auto p-6">
                  {content.tabs.length > 0 ? (
                    content.tabs[activeTab].content
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full">
                      <FaLock className="text-primary text-4xl mb-4" />
                      <h3 className="text-xl font-bold mb-2">Premium Content</h3>
                      <p className="text-gray-400 text-center max-w-md mb-6">
                        Upgrade to a premium subscription to access this content and much more.
                      </p>
                      <Link
                        to="/pricing"
                        className="bg-primary hover:bg-primary-hover text-black font-bold py-2 px-6 rounded-lg flex items-center gap-2 transition-colors"
                      >
                        View Pricing <FaArrowRight />
                      </Link>
                    </div>
                  )}
                </div>

                {/* Footer */}
                <div className="p-4 border-t border-gray-800 bg-[#0F172A]">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-400">
                      This is just a preview. Unlock all content with a premium subscription.
                    </p>
                    <Link
                      to="/pricing"
                      className="text-primary hover:text-primary-hover text-sm font-medium flex items-center gap-1 transition-colors"
                    >
                      See Plans <FaArrowRight className="text-xs" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DemoOverlay;
