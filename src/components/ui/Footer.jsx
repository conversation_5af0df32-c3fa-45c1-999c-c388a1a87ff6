import React from 'react';
import { Link } from 'react-router-dom';
import { FaG<PERSON><PERSON>, Fa<PERSON>wi<PERSON>, FaDiscord, FaLinkedin } from 'react-icons/fa';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  const footerLinks = {
    platform: [
      { name: 'Challenges', path: '/challenges' },
      { name: 'Learning Modules', path: '/learn/modules' },
      { name: 'Leaderboard', path: '/leaderboard' },
      { name: 'Events', path: '/events' }
    ],
    company: [
      { name: 'About Us', path: '/about' },
      { name: 'Careers', path: '/careers' },
      { name: 'Blog', path: '/blog' },
      { name: 'Contact', path: '/contact' }
    ],
    legal: [
      { name: 'Terms of Service', path: '/terms' },
      { name: 'Privacy Policy', path: '/privacy' },
      { name: 'Cookie Policy', path: '/cookies' }
    ],
    social: [
      { name: 'GitHub', icon: <FaGithub />, url: 'https://github.com' },
      { name: 'Twitter', icon: <FaTwitter />, url: 'https://twitter.com' },
      { name: 'Discord', icon: <FaDiscord />, url: 'https://discord.com' },
      { name: 'LinkedIn', icon: <FaLinkedin />, url: 'https://linkedin.com' }
    ]
  };

  return (
    <footer className="bg-[#0B1120] border-t border-gray-800 py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Logo and Description */}
          <div>
            <div className="flex items-center mb-4">
              <span className="text-[#88cc14] font-bold text-2xl mr-1">X</span>
              <span className="font-bold text-2xl text-white">Cerberus</span>
            </div>
            <p className="text-gray-400 mb-4">
              The ultimate platform for cybersecurity training and skill development.
            </p>
            <div className="flex space-x-4">
              {footerLinks.social.map((link, index) => (
                <a 
                  key={index}
                  href={link.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-[#88cc14] transition-colors"
                  aria-label={link.name}
                >
                  {link.icon}
                </a>
              ))}
            </div>
          </div>
          
          {/* Platform Links */}
          <div>
            <h3 className="text-lg font-bold text-white mb-4">Platform</h3>
            <ul className="space-y-2">
              {footerLinks.platform.map((link, index) => (
                <li key={index}>
                  <Link 
                    to={link.path} 
                    className="text-gray-400 hover:text-[#88cc14] transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Company Links */}
          <div>
            <h3 className="text-lg font-bold text-white mb-4">Company</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <Link 
                    to={link.path} 
                    className="text-gray-400 hover:text-[#88cc14] transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Legal Links */}
          <div>
            <h3 className="text-lg font-bold text-white mb-4">Legal</h3>
            <ul className="space-y-2">
              {footerLinks.legal.map((link, index) => (
                <li key={index}>
                  <Link 
                    to={link.path} 
                    className="text-gray-400 hover:text-[#88cc14] transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
        
        {/* Bottom Section */}
        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-500 mb-4 md:mb-0">
            &copy; {currentYear} XCerberus. All rights reserved.
          </div>
          <div className="text-gray-500">
            Made with ❤️ for the cybersecurity community
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
