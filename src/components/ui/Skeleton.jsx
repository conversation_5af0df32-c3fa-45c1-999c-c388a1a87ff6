import React from 'react';

const Skeleton = ({
  className = '',
  variant = 'rectangular',
  width,
  height,
  animation = 'pulse',
  count = 1,
  rounded = 'rounded',
  ...props
}) => {
  const baseClasses = 'bg-gray-700/50 dark:bg-gray-700';
  const animationClasses = animation === 'pulse'
    ? 'animate-pulse'
    : animation === 'wave'
      ? 'animate-shimmer relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent'
      : '';

  const variantClasses = {
    rectangular: rounded,
    circular: 'rounded-full',
    text: `${rounded} h-4 w-3/4`,
    title: `${rounded} h-6 w-1/2 mb-2`,
    avatar: 'rounded-full h-12 w-12',
    button: `${rounded} h-10`,
    card: 'rounded-lg',
    image: 'rounded-lg'
  };

  const renderSkeleton = (index) => (
    <div
      key={index}
      className={`${baseClasses} ${animationClasses} ${variantClasses[variant] || rounded} ${className}`}
      style={{
        width: width || (variant === 'text' ? '75%' : '100%'),
        height: height || (
          variant === 'text' ? '1rem' :
          variant === 'title' ? '1.5rem' :
          variant === 'avatar' ? '3rem' :
          variant === 'button' ? '2.5rem' :
          variant === 'card' ? '200px' :
          variant === 'image' ? '200px' :
          '1rem'
        ),
        ...(count > 1 && index < count - 1 ? { marginBottom: '0.5rem' } : {})
      }}
      {...props}
    />
  );

  return (
    <>
      {Array.from({ length: count }).map((_, index) => renderSkeleton(index))}
    </>
  );
};

// Predefined skeleton layouts
export const CardSkeleton = ({ className = '', ...props }) => (
  <div className={`space-y-3 ${className}`} {...props}>
    <Skeleton variant="image" height={200} />
    <Skeleton variant="title" width="70%" />
    <Skeleton variant="text" count={3} />
    <Skeleton variant="button" width="40%" />
  </div>
);

export const ProfileSkeleton = ({ className = '', ...props }) => (
  <div className={`flex flex-col space-y-4 ${className}`} {...props}>
    <div className="flex items-center space-x-4">
      <Skeleton variant="avatar" width={64} height={64} />
      <div className="space-y-2 flex-1">
        <Skeleton variant="title" />
        <Skeleton variant="text" width="60%" />
      </div>
    </div>
    <Skeleton variant="text" count={4} />
  </div>
);

export const TableRowSkeleton = ({ columns = 4, className = '', ...props }) => (
  <div className={`flex space-x-4 ${className}`} {...props}>
    {Array.from({ length: columns }).map((_, i) => (
      <Skeleton
        key={i}
        variant="text"
        width={`${100 / columns}%`}
        className="my-2"
      />
    ))}
  </div>
);

export const ListSkeleton = ({ rows = 5, className = '', ...props }) => (
  <div className={`space-y-3 ${className}`} {...props}>
    {Array.from({ length: rows }).map((_, i) => (
      <div key={i} className="flex items-center space-x-4">
        <Skeleton variant="avatar" width={40} height={40} />
        <div className="space-y-2 flex-1">
          <Skeleton variant="text" width="40%" />
          <Skeleton variant="text" width="70%" />
        </div>
      </div>
    ))}
  </div>
);

export default Skeleton;