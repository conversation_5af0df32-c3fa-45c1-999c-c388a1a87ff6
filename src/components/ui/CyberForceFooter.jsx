import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ram, Fa<PERSON><PERSON>ed<PERSON>, FaPhone, FaEnvelope } from 'react-icons/fa6';
import { FaMapMarkerAlt } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const CyberForceFooter = () => {
  const { darkMode } = useGlobalTheme();
  const { t } = useLanguage();

  return (
    <footer className={`${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-12">
        {/* Top Section with Logo and Newsletter */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12 border-b border-gray-700 pb-12">
          <div>
            <h3 className="text-2xl font-bold mb-6">Stay connected by subscribing to our newsletter!</h3>
            <div className="flex flex-col sm:flex-row gap-2">
              <input
                type="email"
                placeholder="Your email address"
                className={`px-4 py-3 rounded-lg ${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border focus:outline-none focus:ring-2 focus:ring-[#0066cc] flex-grow`}
              />
              <button className="bg-[#0066cc] hover:bg-[#0055aa] text-white font-bold px-6 py-3 rounded-lg transition-colors">
                Subscribe
              </button>
            </div>
            <p className="mt-4 text-sm text-gray-400">Sign up now for news and updates</p>
          </div>
          <div className="flex justify-center md:justify-end items-start">
            <Link to="/" className="inline-block">
              <img
                src="/images/CyberForce.png"
                alt="CyberForce Logo"
                className="h-16 md:h-20"
              />
            </Link>
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          {/* Column 1: About */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-[#0066cc]">Start learning from our experts</h4>
            <p className={`mb-6 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Enhance your cybersecurity skills with our comprehensive training programs designed for the CyberForce Competition.
            </p>
            <div className="space-y-4">
              <div className="flex items-center">
                <FaPhone className="text-[#0066cc] mr-3" />
                <a href="tel:+96871104475" className="hover:text-[#0066cc] transition-colors">+968 71104475</a>
              </div>
              <div className="flex items-center">
                <FaEnvelope className="text-[#0066cc] mr-3" />
                <a href="mailto:<EMAIL>" className="hover:text-[#0066cc] transition-colors"><EMAIL></a>
              </div>
              <div className="flex items-start">
                <FaMapMarkerAlt className="text-[#0066cc] mr-3 mt-1" />
                <span>Sultanate of Oman, Muscat</span>
              </div>
            </div>
          </div>

          {/* Column 2: Links */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-[#0066cc]">Links</h4>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="hover:text-[#0066cc] transition-colors flex items-center">
                  <span className="w-2 h-2 bg-[#0066cc] rounded-full mr-2"></span>
                  Home
                </Link>
              </li>
              <li>
                <Link to="/challenges" className="hover:text-[#0066cc] transition-colors flex items-center">
                  <span className="w-2 h-2 bg-[#0066cc] rounded-full mr-2"></span>
                  Challenges
                </Link>
              </li>
              <li>
                <Link to="/learn" className="hover:text-[#0066cc] transition-colors flex items-center">
                  <span className="w-2 h-2 bg-[#0066cc] rounded-full mr-2"></span>
                  Learning
                </Link>
              </li>

              <li>
                <Link to="/terms" className="hover:text-[#0066cc] transition-colors flex items-center">
                  <span className="w-2 h-2 bg-[#0066cc] rounded-full mr-2"></span>
                  Terms & Conditions
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="hover:text-[#0066cc] transition-colors flex items-center">
                  <span className="w-2 h-2 bg-[#0066cc] rounded-full mr-2"></span>
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 3: Courses */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-[#0066cc]">Courses</h4>
            <ul className="space-y-3">
              <li>
                <Link to="/courses/governance" className="hover:text-[#0066cc] transition-colors flex items-center">
                  <span className="w-2 h-2 bg-[#0066cc] rounded-full mr-2"></span>
                  Governance, Risk and Compliance
                </Link>
              </li>
              <li>
                <Link to="/courses/cybersecurity" className="hover:text-[#0066cc] transition-colors flex items-center">
                  <span className="w-2 h-2 bg-[#0066cc] rounded-full mr-2"></span>
                  Cyber Security Operations & Technology
                </Link>
              </li>
              <li>
                <Link to="/courses/project-management" className="hover:text-[#0066cc] transition-colors flex items-center">
                  <span className="w-2 h-2 bg-[#0066cc] rounded-full mr-2"></span>
                  Project Management
                </Link>
              </li>
              <li>
                <Link to="/courses/industrial-control" className="hover:text-[#0066cc] transition-colors flex items-center">
                  <span className="w-2 h-2 bg-[#0066cc] rounded-full mr-2"></span>
                  Industrial Control Systems
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 4: Featured Post */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-[#0066cc]">Featured Post</h4>
            <div className={`rounded-lg overflow-hidden shadow-md ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'}`}>
              <img
                src="https://cyberforce.om/wp-content/uploads/2023/07/nasa-Q1p7bh3SHj8-unsplash-1-scaled.jpg"
                alt="Cybersecurity Skills"
                className="w-full h-40 object-cover"
              />
              <div className="p-4">
                <h5 className="font-bold mb-2">The Growing Importance of Cyber Security Skills</h5>
                <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-3`}>
                  In today's interconnected world, cyber security has become a critical concern for organizations...
                </p>
                <Link to="/blog/cybersecurity-skills" className="text-[#0066cc] text-sm font-medium hover:underline">
                  Read more
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm mb-4 md:mb-0">© Copyright {new Date().getFullYear()} by CyberForce</p>
          <div className="flex space-x-4">
            <a
              href="https://x.com/cyberforce_om"
              target="_blank"
              rel="noopener noreferrer"
              className="w-10 h-10 rounded-full bg-[#0066cc]/10 flex items-center justify-center hover:bg-[#0066cc]/20 transition-colors"
              aria-label="X (Twitter)"
            >
              <FaXTwitter className="text-[#0066cc]" />
            </a>
            <a
              href="https://www.instagram.com/cyberforce_om/"
              target="_blank"
              rel="noopener noreferrer"
              className="w-10 h-10 rounded-full bg-[#0066cc]/10 flex items-center justify-center hover:bg-[#0066cc]/20 transition-colors"
              aria-label="Instagram"
            >
              <FaInstagram className="text-[#0066cc]" />
            </a>
            <a
              href="https://www.linkedin.com/company/cyberforceoman/"
              target="_blank"
              rel="noopener noreferrer"
              className="w-10 h-10 rounded-full bg-[#0066cc]/10 flex items-center justify-center hover:bg-[#0066cc]/20 transition-colors"
              aria-label="LinkedIn"
            >
              <FaLinkedin className="text-[#0066cc]" />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default CyberForceFooter;
