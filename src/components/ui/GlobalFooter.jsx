import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>a<PERSON>wi<PERSON>, FaInstagram, FaLinkedin, FaEnvelope, FaPhone } from 'react-icons/fa6';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

const GlobalFooter = () => {
  const { darkMode } = useGlobalTheme();

  const footerLinks = [
    {
      title: 'Platform',
      links: [
        { name: 'Challenges', path: '/challenges' },
        { name: 'Learn', path: '/learn/modules' },
        { name: 'Simulations', path: '/simulations' },
        { name: 'Leaderboard', path: '/leaderboard' },
      ]
    },
    {
      title: 'Company',
      links: [
        { name: 'About Us', path: '/about' },
        { name: 'Services', path: '/services' },
        { name: 'Pricing', path: '/pricing' },
      ]
    },
    {
      title: 'Resources',
      links: [
        { name: 'Blog', path: '/blog' },
        { name: 'Security Insights', path: '/security-insights' },
      ]
    }
  ];

  const socialLinks = [
    { icon: <FaXTwitter />, url: 'https://x.com/cyberforce_om', label: 'X (Twitter)' },
    { icon: <FaInstagram />, url: 'https://www.instagram.com/cyberforce_om/', label: 'Instagram' },
    { icon: <FaLinkedin />, url: 'https://www.linkedin.com/company/cyberforceoman/', label: 'LinkedIn' },
  ];

  // Contact information
  const contactInfo = {
    email: '<EMAIL>',
    phone: '+968 71104475',
    address: 'Muscat, Oman'
  };

  return (
    <footer className={`${darkMode ? 'bg-[#0B1120] border-gray-800' : 'bg-gray-50 border-gray-200'} border-t mt-16`}>
      <div className="container mx-auto px-4 py-12">
        {/* Main Footer */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <Link to="/" className="flex items-center mb-4">
              <span className="text-[#4A5CBA] font-bold text-2xl mr-1">Cyber</span>
              <span className={`font-bold text-2xl text-[#F5B93F]`}>Force</span>
            </Link>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 max-w-md`}>
              CyberForce was established in 2023 through collaboration with Telecoms and Digital Technologies Academy. We empower individuals and organizations against cyber threats through innovative training.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.label}
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} transition-colors`}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerLinks.map((section, index) => (
            <div key={index}>
              <h3 className={`font-bold text-lg mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      to={link.path}
                      className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} transition-colors`}
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          {/* Contact Information */}
          <div>
            <h3 className={`font-bold text-lg mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Contact Us</h3>
            <ul className="space-y-3">
              <li className="flex items-center gap-2">
                <FaEnvelope className="text-[#4A5CBA]" />
                <a
                  href={`mailto:${contactInfo.email}`}
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} transition-colors`}
                >
                  {contactInfo.email}
                </a>
              </li>
              <li className="flex items-center gap-2">
                <FaPhone className="text-[#4A5CBA]" />
                <a
                  href={`tel:${contactInfo.phone}`}
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} transition-colors`}
                >
                  {contactInfo.phone}
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className={`pt-8 ${darkMode ? 'border-gray-800' : 'border-gray-200'} border-t flex flex-col md:flex-row justify-between items-center`}>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm mb-4 md:mb-0`}>
            &copy; {new Date().getFullYear()} CyberForce Oman. All rights reserved.
          </p>
          <div className="flex space-x-6">
            <Link to="/privacy" className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} text-sm transition-colors`}>
              Privacy Policy
            </Link>
            <Link to="/terms" className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} text-sm transition-colors`}>
              Terms of Service
            </Link>
            <Link to="/cookies" className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} text-sm transition-colors`}>
              Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default GlobalFooter;
