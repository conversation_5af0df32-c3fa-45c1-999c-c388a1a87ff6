import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaGlobe } from 'react-icons/fa';
import LanguageSelector from './LanguageSelector';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

function LanguageButton() {
  const { darkMode } = useGlobalTheme();
  const { changeLanguage } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="fixed bottom-20 left-4 md:bottom-4 md:left-4 z-50">
      <AnimatePresence>
        {isExpanded ? (
          <motion.div
            key="selector"
            initial={{ opacity: 0, scale: 0.8, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute bottom-16 left-0 mb-2"
          >
            <LanguageSelector
              darkMode={darkMode}
              onLanguageChange={(lang) => {
                changeLanguage(lang);
                setIsExpanded(false);
              }}
            />
          </motion.div>
        ) : null}
      </AnimatePresence>

      <motion.button
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-12 h-12 rounded-full flex items-center justify-center shadow-xl border-2 border-[#F5B93F] group ${
          darkMode
            ? 'bg-[#1A1F35] hover:bg-[#252D4A] text-white'
            : 'bg-white hover:bg-gray-100 text-gray-800'
        } transition-colors`}
      >
        <div className="relative flex items-center justify-center">
          <FaGlobe className="text-[#F5B93F] text-lg" />
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-[#F5B93F] rounded-full animate-pulse"></div>
        </div>
        <div className="absolute -top-8 left-0 bg-black/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-md whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
          <span className="inline-block">Language 🇺🇸 / 🇸🇦</span>
        </div>
      </motion.button>
    </div>
  );
}

export default LanguageButton;
