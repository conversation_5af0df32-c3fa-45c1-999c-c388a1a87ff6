import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FaBars, FaTimes, FaChevronDown, FaUser } from 'react-icons/fa';
import Button from './Button';
import <PERSON><PERSON><PERSON>or<PERSON>Logo from '../icons/CyberForceLogo';

const Header = ({ user, onSignOut }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [location.pathname]);

  // Handle clicks outside of profile menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileOpen && !event.target.closest('.profile-menu')) {
        setProfileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [profileOpen]);

  const navLinks = [
    { name: 'Home', path: '/' },
    { name: 'About', path: '/about' },
    { name: 'Services', path: '/services' },
    { name: 'Insights', path: '/security-insights' },
    { name: 'Challenges', path: '/challenges' },
    { name: 'Learn', path: '/learn' },
    { name: 'Leaderboard', path: '/leaderboard' },
    { name: 'Pricing', path: '/pricing' },
  ];

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-[#0B1120]/95 backdrop-blur-sm shadow-lg' : 'bg-[#0B1120]'
      } border-b border-gray-800`}
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <CyberForceLogo darkMode={true} />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className={`text-gray-300 hover:text-[#0066cc] transition-colors ${
                  location.pathname === link.path ? 'text-[#0066cc]' : ''
                }`}
              >
                {link.name}
              </Link>
            ))}
          </nav>

          {/* Auth Buttons or User Profile */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <div className="relative profile-menu">
                <button
                  onClick={() => setProfileOpen(!profileOpen)}
                  className="flex items-center gap-2 bg-[#1A1F35] hover:bg-[#252D4A] px-3 py-2 rounded-lg transition-colors"
                >
                  <div className="w-8 h-8 bg-[#0066cc] rounded-full flex items-center justify-center text-white">
                    {user.avatar || user.username?.charAt(0) || <FaUser />}
                  </div>
                  <span className="text-white">{user.username || 'User'}</span>
                  <FaChevronDown className={`text-gray-400 transition-transform ${profileOpen ? 'rotate-180' : ''}`} size={12} />
                </button>

                <AnimatePresence>
                  {profileOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute right-0 mt-2 w-48 bg-[#1A1F35] rounded-lg shadow-lg border border-gray-800 overflow-hidden z-50"
                    >
                      <div className="py-2">
                        <Link to="/profile" className="block px-4 py-2 text-gray-300 hover:bg-[#252D4A] hover:text-white">
                          Profile
                        </Link>
                        <Link to="/dashboard" className="block px-4 py-2 text-gray-300 hover:bg-[#252D4A] hover:text-white">
                          Dashboard
                        </Link>
                        <Link to="/settings" className="block px-4 py-2 text-gray-300 hover:bg-[#252D4A] hover:text-white">
                          Settings
                        </Link>
                        <div className="border-t border-gray-800 my-1"></div>
                        <button
                          onClick={onSignOut}
                          className="block w-full text-left px-4 py-2 text-red-400 hover:bg-[#252D4A]"
                        >
                          Sign Out
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              <>
                <Link to="/login" className="text-gray-300 hover:text-white transition-colors">
                  Log In
                </Link>
                <Button to="/signup" variant="primary" size="md">
                  Sign Up
                </Button>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-gray-300 hover:text-white"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden bg-[#1A1F35] border-t border-gray-800 overflow-hidden"
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="flex flex-col space-y-4">
                {navLinks.map((link) => (
                  <Link
                    key={link.path}
                    to={link.path}
                    className={`text-gray-300 hover:text-[#0066cc] transition-colors py-2 ${
                      location.pathname === link.path ? 'text-[#0066cc]' : ''
                    }`}
                  >
                    {link.name}
                  </Link>
                ))}

                {user ? (
                  <>
                    <div className="border-t border-gray-800 my-2 pt-2">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-[#0066cc] rounded-full flex items-center justify-center text-white">
                          {user.avatar || user.username?.charAt(0) || <FaUser />}
                        </div>
                        <div>
                          <div className="text-white">{user.username || 'User'}</div>
                          <div className="text-gray-400 text-sm">{user.email}</div>
                        </div>
                      </div>

                      <Link to="/profile" className="block py-2 text-gray-300 hover:text-[#0066cc]">
                        Profile
                      </Link>
                      <Link to="/dashboard" className="block py-2 text-gray-300 hover:text-[#0066cc]">
                        Dashboard
                      </Link>
                      <Link to="/settings" className="block py-2 text-gray-300 hover:text-[#0066cc]">
                        Settings
                      </Link>

                      <button
                        onClick={onSignOut}
                        className="w-full mt-4 py-2 text-red-400 hover:text-red-300 text-left"
                      >
                        Sign Out
                      </button>
                    </div>
                  </>
                ) : (
                  <div className="flex flex-col space-y-3 mt-2 pt-2 border-t border-gray-800">
                    <Button to="/login" variant="secondary" fullWidth>
                      Log In
                    </Button>
                    <Button to="/signup" variant="primary" fullWidth>
                      Sign Up
                    </Button>
                  </div>
                )}
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Header;
