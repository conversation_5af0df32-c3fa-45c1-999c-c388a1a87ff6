import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const variants = {
  primary: 'bg-[#88cc14] hover:bg-[#7ab811] text-black',
  secondary: 'bg-[#1A1F35] hover:bg-[#252D4A] text-white',
  outline: 'bg-transparent border border-[#88cc14] text-[#88cc14] hover:bg-[#88cc14]/10',
  ghost: 'bg-transparent hover:bg-gray-800/50 text-[#88cc14]',
  danger: 'bg-red-500 hover:bg-red-600 text-white',
  dark: 'bg-[#0B1120] hover:bg-gray-900 text-white border border-gray-700',
};

const sizes = {
  sm: 'py-1 px-3 text-sm',
  md: 'py-2 px-4',
  lg: 'py-3 px-6 text-lg',
};

function Button({
  children,
  variant = 'primary',
  size = 'md',
  to,
  className = '',
  icon,
  disabled = false,
  onClick,
  type = 'button',
  ...props
}) {
  const baseClasses = `font-bold rounded-lg transition-all duration-300 flex items-center justify-center gap-2 relative overflow-hidden ${variants[variant]} ${sizes[size]} ${className}`;

  const content = (
    <>
      {icon && <span className="text-lg">{icon}</span>}
      <span className="relative z-10">{children}</span>
      {variant === 'primary' || variant === 'secondary' ? (
        <motion.span
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-500"
          initial={{ x: '-100%' }}
          whileHover={{ x: '100%' }}
          transition={{ duration: 0.8, ease: 'easeInOut' }}
        />
      ) : null}
    </>
  );

  if (to) {
    return (
      <Link to={to} className={`${baseClasses} group`} {...props}>
        {content}
      </Link>
    );
  }

  return (
    <button
      type={type}
      className={`${baseClasses} group ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {content}
    </button>
  );
}

export default Button;