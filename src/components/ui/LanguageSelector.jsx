import React, { useState, useEffect, useRef } from 'react';
import { FaGlobe, FaChevronDown } from 'react-icons/fa';

const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ar', name: 'العربية', flag: '🇸🇦' },
];

const LanguageSelector = ({ onLanguageChange, darkMode }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLanguageSelect = (language) => {
    setSelectedLanguage(language);
    setIsOpen(false);
    if (onLanguageChange) {
      onLanguageChange(language.code);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div
        className={`w-40 rounded-lg shadow-lg border-2 border-[#F5B93F] overflow-hidden ${
          darkMode
            ? 'bg-[#1A1F35] text-white'
            : 'bg-white text-gray-800'
        }`}
      >
        <div className="py-1">
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => handleLanguageSelect(language)}
              className={`block w-full text-left px-3 py-2 text-sm transition-colors flex items-center ${
                darkMode
                  ? 'text-gray-200 hover:bg-[#252D4A]'
                  : 'text-gray-700 hover:bg-gray-100'
              } ${
                selectedLanguage.code === language.code
                  ? 'bg-[#F5B93F]/20 font-medium'
                  : ''
              }`}
            >
              <span className="text-xl mr-2">{language.flag}</span>
              <span>{language.name}</span>
              {selectedLanguage.code === language.code && (
                <span className="ml-auto w-2 h-2 rounded-full bg-[#F5B93F]"></span>
              )}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LanguageSelector;
