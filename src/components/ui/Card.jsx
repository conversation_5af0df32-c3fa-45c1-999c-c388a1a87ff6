import React from 'react';
import { motion } from 'framer-motion';

function Card({
  children,
  className = '',
  hover = true,
  animate = true,
  glow = false,
  accent = false,
  accentColor = 'green',
  ...props
}) {
  const accentColors = {
    green: 'bg-[#88cc14]',
    blue: 'bg-blue-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500',
  };

  const baseClasses = `
    bg-[#1A1F35]
    border border-gray-800
    rounded-xl overflow-hidden
    ${hover ? 'hover:border-[#88cc14]/50 transition-colors duration-300' : ''}
    ${glow ? 'shadow-lg shadow-[#88cc14]/10' : ''}
    ${className}
  `;

  const content = (
    <>
      {accent && (
        <div className={`h-1 ${accentColors[accentColor]}`}></div>
      )}
      <div className="p-6">
        {children}
      </div>
    </>
  );

  if (animate) {
    return (
      <motion.div
        className={baseClasses}
        whileHover={hover ? { y: -5 } : {}}
        transition={{ duration: 0.3 }}
        {...props}
      >
        {content}
      </motion.div>
    );
  }

  return (
    <div className={baseClasses} {...props}>
      {content}
    </div>
  );
}

export default Card;