import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaUserEdit, FaCrown, FaBuilding, FaUser, FaSearch, FaCheck, FaTimes } from 'react-icons/fa';

/**
 * UserSubscriptionManager Component
 * 
 * Allows admins to view and update user subscription tiers.
 */
const UserSubscriptionManager = () => {
  const { darkMode } = useGlobalTheme();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [newTier, setNewTier] = useState('');
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const [updateError, setUpdateError] = useState(null);

  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('profiles')
          .select('id, username, full_name, avatar_url, subscription_tier, created_at')
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        
        setUsers(data || []);
      } catch (error) {
        console.error('Error fetching users:', error);
        setError('Failed to load users. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchUsers();
  }, []);

  // Filter users based on search query
  const filteredUsers = users.filter(user => {
    const query = searchQuery.toLowerCase();
    return (
      user.username?.toLowerCase().includes(query) ||
      user.full_name?.toLowerCase().includes(query) ||
      user.subscription_tier?.toLowerCase().includes(query)
    );
  });

  // Handle user selection
  const handleSelectUser = (user) => {
    setSelectedUser(user);
    setNewTier(user.subscription_tier);
    setUpdateSuccess(false);
    setUpdateError(null);
  };

  // Handle tier change
  const handleTierChange = (e) => {
    setNewTier(e.target.value);
  };

  // Handle update subscription
  const handleUpdateSubscription = async () => {
    if (!selectedUser || !newTier) return;
    
    try {
      setLoading(true);
      setUpdateSuccess(false);
      setUpdateError(null);
      
      // Call the RPC function to update the user's subscription tier
      const { data, error } = await supabase.rpc(
        'update_user_subscription_tier',
        { 
          p_user_id: selectedUser.id,
          p_tier: newTier
        }
      );
      
      if (error) throw error;
      
      // Update the user in the local state
      setUsers(users.map(user => 
        user.id === selectedUser.id 
          ? { ...user, subscription_tier: newTier } 
          : user
      ));
      
      // Update the selected user
      setSelectedUser({ ...selectedUser, subscription_tier: newTier });
      
      setUpdateSuccess(true);
    } catch (error) {
      console.error('Error updating subscription tier:', error);
      setUpdateError('Failed to update subscription tier. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get tier icon
  const getTierIcon = (tier) => {
    switch (tier) {
      case 'premium':
        return <FaCrown className="text-yellow-500" />;
      case 'business':
        return <FaBuilding className="text-purple-500" />;
      default:
        return <FaUser className="text-blue-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">User Subscription Manager</h2>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className={darkMode ? 'text-gray-400' : 'text-gray-500'} />
          </div>
          <input
            type="text"
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`pl-10 pr-4 py-2 rounded-lg ${
              darkMode 
                ? 'bg-gray-800 border-gray-700 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Users List */}
        <div className={`lg:col-span-2 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border overflow-hidden`}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className={`${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                  <th className="px-4 py-3 text-left">User</th>
                  <th className="px-4 py-3 text-left">Subscription</th>
                  <th className="px-4 py-3 text-left">Joined</th>
                  <th className="px-4 py-3 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading && users.length === 0 ? (
                  <tr>
                    <td colSpan="4" className="px-4 py-3 text-center">
                      <div className="flex justify-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#88cc14]"></div>
                      </div>
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan="4" className="px-4 py-3 text-center text-red-500">
                      {error}
                    </td>
                  </tr>
                ) : filteredUsers.length === 0 ? (
                  <tr>
                    <td colSpan="4" className="px-4 py-3 text-center">
                      No users found matching your search.
                    </td>
                  </tr>
                ) : (
                  filteredUsers.map((user) => (
                    <tr 
                      key={user.id} 
                      className={`border-t ${
                        darkMode 
                          ? 'border-gray-800 hover:bg-gray-800' 
                          : 'border-gray-200 hover:bg-gray-50'
                      } ${selectedUser?.id === user.id ? 'bg-[#88cc14]/10' : ''}`}
                      onClick={() => handleSelectUser(user)}
                    >
                      <td className="px-4 py-3">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center">
                            {user.avatar_url ? (
                              <img 
                                src={user.avatar_url} 
                                alt={user.username} 
                                className="w-full h-full rounded-full object-cover"
                              />
                            ) : (
                              <span className="text-[#88cc14]">
                                {user.username?.charAt(0).toUpperCase() || 'U'}
                              </span>
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{user.username}</p>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              {user.full_name || 'No name provided'}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center gap-2">
                          {getTierIcon(user.subscription_tier)}
                          <span className="capitalize">{user.subscription_tier}</span>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        {formatDate(user.created_at)}
                      </td>
                      <td className="px-4 py-3">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSelectUser(user);
                          }}
                          className="p-2 text-blue-500 hover:text-blue-700 rounded-full hover:bg-blue-100"
                        >
                          <FaUserEdit />
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
        
        {/* User Details and Subscription Management */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
          {selectedUser ? (
            <div>
              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 rounded-full bg-[#88cc14]/20 flex items-center justify-center">
                  {selectedUser.avatar_url ? (
                    <img 
                      src={selectedUser.avatar_url} 
                      alt={selectedUser.username} 
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-[#88cc14] text-2xl">
                      {selectedUser.username?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  )}
                </div>
                <div>
                  <h3 className="text-xl font-bold">{selectedUser.username}</h3>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {selectedUser.full_name || 'No name provided'}
                  </p>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                    Current Subscription
                  </label>
                  <div className={`px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center gap-2`}>
                    {getTierIcon(selectedUser.subscription_tier)}
                    <span className="capitalize">{selectedUser.subscription_tier}</span>
                  </div>
                </div>
                
                <div>
                  <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                    Change Subscription Tier
                  </label>
                  <select
                    value={newTier}
                    onChange={handleTierChange}
                    className={`w-full px-3 py-2 rounded-lg ${
                      darkMode 
                        ? 'bg-gray-800 border-gray-700 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                  >
                    <option value="free">Free</option>
                    <option value="premium">Premium</option>
                    <option value="business">Business</option>
                  </select>
                </div>
                
                <button
                  onClick={handleUpdateSubscription}
                  disabled={loading || newTier === selectedUser.subscription_tier}
                  className={`w-full px-4 py-2 rounded-lg flex items-center justify-center gap-2 ${
                    loading || newTier === selectedUser.subscription_tier
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                  }`}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
                      <span>Updating...</span>
                    </>
                  ) : (
                    <>
                      <FaUserEdit />
                      <span>Update Subscription</span>
                    </>
                  )}
                </button>
                
                {updateSuccess && (
                  <div className="flex items-center gap-2 text-green-500 mt-2">
                    <FaCheck />
                    <span>Subscription updated successfully!</span>
                  </div>
                )}
                
                {updateError && (
                  <div className="flex items-center gap-2 text-red-500 mt-2">
                    <FaTimes />
                    <span>{updateError}</span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-64">
              <FaUserEdit className="text-4xl mb-4 text-gray-400" />
              <p className={`text-center ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Select a user to manage their subscription
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserSubscriptionManager;
