import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaPlus, FaEdit, FaTrash, FaSearch, FaFilter, FaFileAlt, FaTrophy, FaCode, FaGraduationCap, FaGamepad, FaNewspaper, FaEye } from 'react-icons/fa';
import ContentManagementWidget from '../dashboards/widgets/ContentManagementWidget';
import { SUBSCRIPTION_TIERS } from '../../config/subscriptionTiers';

const ContentManagement = () => {
  const navigate = useNavigate();
  const { darkMode } = useGlobalTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState('all');
  const [activeTab, setActiveTab] = useState('learning');

  // Mock learning modules data
  const mockLearningModules = [
    {
      id: 'lm1',
      title: 'Introduction to Cybersecurity',
      category: 'Fundamentals',
      difficulty: 'Beginner',
      status: 'published',
      author: 'Admin',
      lastUpdated: '2023-10-01T10:00:00Z',
      views: 1250,
      completions: 780
    },
    {
      id: 'lm2',
      title: 'Network Security Basics',
      category: 'Network Security',
      difficulty: 'Intermediate',
      status: 'published',
      author: 'Admin',
      lastUpdated: '2023-09-15T14:30:00Z',
      views: 980,
      completions: 540
    },
    {
      id: 'lm3',
      title: 'Web Application Security',
      category: 'Web Security',
      difficulty: 'Intermediate',
      status: 'published',
      author: 'Admin',
      lastUpdated: '2023-09-20T09:15:00Z',
      views: 1100,
      completions: 620
    },
    {
      id: 'lm4',
      title: 'Advanced Penetration Testing',
      category: 'Offensive Security',
      difficulty: 'Advanced',
      status: 'draft',
      author: 'Admin',
      lastUpdated: '2023-10-10T16:45:00Z',
      views: 0,
      completions: 0
    }
  ];

  // Mock challenges data
  const mockChallenges = [
    {
      id: 'ch1',
      title: 'SQL Injection Basics',
      category: 'Web Security',
      difficulty: 'Beginner',
      status: 'published',
      author: 'Admin',
      lastUpdated: '2023-09-05T11:30:00Z',
      attempts: 950,
      completions: 420
    },
    {
      id: 'ch2',
      title: 'Password Cracking Challenge',
      category: 'Cryptography',
      difficulty: 'Intermediate',
      status: 'published',
      author: 'Admin',
      lastUpdated: '2023-09-10T13:45:00Z',
      attempts: 780,
      completions: 310
    },
    {
      id: 'ch3',
      title: 'Network Traffic Analysis',
      category: 'Network Security',
      difficulty: 'Intermediate',
      status: 'published',
      author: 'Admin',
      lastUpdated: '2023-09-18T10:20:00Z',
      attempts: 650,
      completions: 280
    },
    {
      id: 'ch4',
      title: 'Advanced Exploit Development',
      category: 'Offensive Security',
      difficulty: 'Advanced',
      status: 'draft',
      author: 'Admin',
      lastUpdated: '2023-10-12T15:10:00Z',
      attempts: 0,
      completions: 0
    }
  ];

  // Mock start hack data
  const mockStartHacks = [
    {
      id: 'sh1',
      title: 'Build a Simple Firewall',
      category: 'Network Security',
      difficulty: 'Beginner',
      status: 'published',
      author: 'Admin',
      lastUpdated: '2023-09-08T09:30:00Z',
      starts: 680,
      completions: 320
    },
    {
      id: 'sh2',
      title: 'Create a Password Manager',
      category: 'Application Development',
      difficulty: 'Intermediate',
      status: 'published',
      author: 'Admin',
      lastUpdated: '2023-09-12T14:15:00Z',
      starts: 540,
      completions: 210
    },
    {
      id: 'sh3',
      title: 'Develop a Network Scanner',
      category: 'Network Security',
      difficulty: 'Intermediate',
      status: 'published',
      author: 'Admin',
      lastUpdated: '2023-09-22T11:40:00Z',
      starts: 490,
      completions: 180
    },
    {
      id: 'sh4',
      title: 'Build a Threat Intelligence Platform',
      category: 'Defensive Security',
      difficulty: 'Advanced',
      status: 'draft',
      author: 'Admin',
      lastUpdated: '2023-10-14T13:25:00Z',
      starts: 0,
      completions: 0
    }
  ];

  // Get content based on active tab
  const getContent = () => {
    switch (activeTab) {
      case 'challenges':
        return mockChallenges;
      case 'starthack':
        return mockStartHacks;
      case 'learning':
      default:
        return mockLearningModules;
    }
  };

  // Handle content actions for ContentManagementWidget
  const handleAddContent = (contentType) => {
    // Map our internal tab names to the widget's content types
    const typeMap = {
      'learning': 'modules',
      'challenges': 'challenges',
      'blogs': 'blog'
    };
    navigate(`/admin/${typeMap[contentType] || contentType}/new`);
  };

  const handleEditContent = (contentType, id) => {
    const typeMap = {
      'learning': 'modules',
      'challenges': 'challenges',
      'blogs': 'blog'
    };
    navigate(`/admin/${typeMap[contentType] || contentType}/edit/${id}`);
  };

  const handleDeleteContent = (contentType, id) => {
    // In a real app, this would call an API
    if (window.confirm(`Are you sure you want to delete this ${contentType}?`)) {
      console.log(`Deleting ${contentType} with ID: ${id}`);
      // Here you would update state or call an API
    }
  };

  const handleViewContent = (contentType, id) => {
    const typeMap = {
      'learning': 'global-learn',
      'challenges': 'global-challenges',
      'blogs': 'blog'
    };
    navigate(`/${typeMap[contentType] || contentType}?id=${id}`);
  };

  // Filter content based on search term and filter
  const filteredContent = getContent().filter(item => {
    // Search filter
    const matchesSearch =
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.toLowerCase().includes(searchTerm.toLowerCase());

    // Status/difficulty filter
    const matchesFilter =
      filterBy === 'all' ||
      item.status === filterBy ||
      item.difficulty.toLowerCase() === filterBy.toLowerCase();

    return matchesSearch && matchesFilter;
  });

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Map our content to the format expected by ContentManagementWidget
  const mapContentForWidget = () => {
    // Map learning modules
    const modules = mockLearningModules.map(module => ({
      id: module.id,
      title: module.title,
      description: `${module.category} - ${module.difficulty}`,
      tier: module.difficulty === 'Advanced' ? SUBSCRIPTION_TIERS.BUSINESS :
            module.difficulty === 'Intermediate' ? SUBSCRIPTION_TIERS.PREMIUM :
            SUBSCRIPTION_TIERS.FREE,
      status: module.status
    }));

    // Map challenges
    const challenges = mockChallenges.map(challenge => ({
      id: challenge.id,
      title: challenge.title,
      description: `${challenge.category} - ${challenge.difficulty}`,
      tier: challenge.difficulty === 'Advanced' ? SUBSCRIPTION_TIERS.BUSINESS :
            challenge.difficulty === 'Intermediate' ? SUBSCRIPTION_TIERS.PREMIUM :
            SUBSCRIPTION_TIERS.FREE,
      difficulty: challenge.difficulty.toLowerCase(),
      status: challenge.status
    }));

    // Map blog posts (using starthack as a placeholder)
    const blogPosts = mockStartHacks.map(hack => ({
      id: hack.id,
      title: hack.title,
      excerpt: `${hack.category} - ${hack.difficulty}`,
      author: hack.author,
      status: hack.status
    }));

    return { modules, challenges, blogPosts };
  };

  const { modules, challenges, blogPosts } = mapContentForWidget();

  return (
    <div>
      {/* Content Management Widget */}
      <div className="mb-8">
        <ContentManagementWidget
          modules={modules}
          challenges={challenges}
          blogPosts={blogPosts}
          onAddContent={handleAddContent}
          onEditContent={handleEditContent}
          onDeleteContent={handleDeleteContent}
          onViewContent={handleViewContent}
        />
      </div>

      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Content Management</h2>
        <button
          className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center"
        >
          <FaPlus className="mr-2" /> Add New Content
        </button>
      </div>

      <div className="flex mb-6 border-b border-gray-200 dark:border-gray-700">
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'learning'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('learning')}
        >
          <FaFileAlt className="inline mr-2" /> Learning Modules
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'challenges'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('challenges')}
        >
          <FaTrophy className="inline mr-2" /> Challenges
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'starthack'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('starthack')}
        >
          <FaCode className="inline mr-2" /> Start Hack
        </button>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className={`flex items-center ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg px-3 py-2`}>
            <FaSearch className={`mr-2 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <input
              type="text"
              placeholder="Search content..."
              className={`w-full bg-transparent focus:outline-none ${darkMode ? 'text-white' : 'text-gray-900'}`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div>
          <select
            className={`px-3 py-2 rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800 text-white' : 'bg-white border-gray-200 text-gray-900'}`}
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value)}
          >
            <option value="all">All Content</option>
            <option value="published">Published</option>
            <option value="draft">Drafts</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
      </div>

      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                <th className="px-4 py-3 text-left">Title</th>
                <th className="px-4 py-3 text-left">Category</th>
                <th className="px-4 py-3 text-left">Difficulty</th>
                <th className="px-4 py-3 text-left">Status</th>
                <th className="px-4 py-3 text-left">Last Updated</th>
                <th className="px-4 py-3 text-left">
                  {activeTab === 'learning' ? 'Views/Completions' :
                   activeTab === 'challenges' ? 'Attempts/Completions' :
                   'Starts/Completions'}
                </th>
                <th className="px-4 py-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredContent.map(item => (
                <tr
                  key={item.id}
                  className={`border-t ${darkMode ? 'border-gray-800 hover:bg-gray-800' : 'border-gray-200 hover:bg-gray-50'}`}
                >
                  <td className="px-4 py-3 font-medium">{item.title}</td>
                  <td className="px-4 py-3">{item.category}</td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      item.difficulty === 'Beginner'
                        ? 'bg-green-100 text-green-800'
                        : item.difficulty === 'Intermediate'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                    }`}>
                      {item.difficulty}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      item.status === 'published'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-3">{formatDate(item.lastUpdated)}</td>
                  <td className="px-4 py-3">
                    {activeTab === 'learning' ?
                      `${item.views} / ${item.completions}` :
                     activeTab === 'challenges' ?
                      `${item.attempts} / ${item.completions}` :
                      `${item.starts} / ${item.completions}`}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex space-x-2">
                      <button className="p-1 text-blue-500 hover:text-blue-700">
                        <FaEdit />
                      </button>
                      <button className="p-1 text-red-500 hover:text-red-700">
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}

              {filteredContent.length === 0 && (
                <tr>
                  <td colSpan="7" className="px-4 py-3 text-center">
                    No content found matching your search criteria.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ContentManagement;
