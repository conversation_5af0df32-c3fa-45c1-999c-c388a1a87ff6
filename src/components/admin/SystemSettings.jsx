import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { 
  FaCog, 
  FaSave, 
  FaUndo, 
  FaDatabase, 
  FaServer, 
  FaShieldAlt, 
  FaEnvelope,
  FaCheck,
  FaTimes
} from 'react-icons/fa';

/**
 * SystemSettings Component
 * 
 * Allows admins to configure system-wide settings.
 */
const SystemSettings = () => {
  const { darkMode } = useGlobalTheme();
  const [loading, setLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState(null);
  
  // System settings state
  const [settings, setSettings] = useState({
    general: {
      siteName: 'XCerberus',
      siteDescription: 'Cybersecurity Learning Platform',
      maintenanceMode: false,
      allowRegistration: true
    },
    subscription: {
      freeTierChallenges: 5,
      freeTierModules: 3,
      premiumPrice: 399,
      businessPrice: 999,
      allowCoinPurchase: true
    },
    email: {
      enableNotifications: true,
      welcomeEmailEnabled: true,
      subscriptionReminderDays: 3
    },
    security: {
      maxLoginAttempts: 5,
      sessionTimeoutMinutes: 60,
      requireEmailVerification: true
    }
  });
  
  // Handle input change
  const handleInputChange = (section, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    
    // Reset status messages
    setSaveSuccess(false);
    setSaveError(null);
  };
  
  // Handle checkbox change
  const handleCheckboxChange = (section, field) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: !prev[section][field]
      }
    }));
    
    // Reset status messages
    setSaveSuccess(false);
    setSaveError(null);
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setSaveSuccess(false);
      setSaveError(null);
      
      // In a real implementation, we would save to Supabase here
      // For now, we'll just simulate a successful save
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveSuccess(true);
    } catch (error) {
      console.error('Error saving settings:', error);
      setSaveError('Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Reset settings to default
  const handleReset = () => {
    // In a real implementation, we would fetch default settings from Supabase
    // For now, we'll just reset to the initial state
    setSettings({
      general: {
        siteName: 'XCerberus',
        siteDescription: 'Cybersecurity Learning Platform',
        maintenanceMode: false,
        allowRegistration: true
      },
      subscription: {
        freeTierChallenges: 5,
        freeTierModules: 3,
        premiumPrice: 399,
        businessPrice: 999,
        allowCoinPurchase: true
      },
      email: {
        enableNotifications: true,
        welcomeEmailEnabled: true,
        subscriptionReminderDays: 3
      },
      security: {
        maxLoginAttempts: 5,
        sessionTimeoutMinutes: 60,
        requireEmailVerification: true
      }
    });
    
    // Reset status messages
    setSaveSuccess(false);
    setSaveError(null);
  };
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">System Settings</h2>
        <div className="flex gap-2">
          <button
            onClick={handleReset}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              darkMode
                ? 'bg-gray-700 text-white hover:bg-gray-600'
                : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
            }`}
          >
            <FaUndo />
            <span>Reset</span>
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              loading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
            }`}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
                <span>Saving...</span>
              </>
            ) : (
              <>
                <FaSave />
                <span>Save Changes</span>
              </>
            )}
          </button>
        </div>
      </div>
      
      {saveSuccess && (
        <div className="mb-4 p-3 bg-green-100 text-green-800 rounded-lg flex items-center gap-2">
          <FaCheck />
          <span>Settings saved successfully!</span>
        </div>
      )}
      
      {saveError && (
        <div className="mb-4 p-3 bg-red-100 text-red-800 rounded-lg flex items-center gap-2">
          <FaTimes />
          <span>{saveError}</span>
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* General Settings */}
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
            <div className="flex items-center gap-3 mb-4">
              <FaCog className="text-blue-500" />
              <h3 className="text-lg font-semibold">General Settings</h3>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Site Name
                </label>
                <input
                  type="text"
                  value={settings.general.siteName}
                  onChange={(e) => handleInputChange('general', 'siteName', e.target.value)}
                  className={`w-full px-3 py-2 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                />
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Site Description
                </label>
                <input
                  type="text"
                  value={settings.general.siteDescription}
                  onChange={(e) => handleInputChange('general', 'siteDescription', e.target.value)}
                  className={`w-full px-3 py-2 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="maintenanceMode"
                  checked={settings.general.maintenanceMode}
                  onChange={() => handleCheckboxChange('general', 'maintenanceMode')}
                  className="h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"
                />
                <label htmlFor="maintenanceMode" className={`ml-2 block text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Maintenance Mode
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="allowRegistration"
                  checked={settings.general.allowRegistration}
                  onChange={() => handleCheckboxChange('general', 'allowRegistration')}
                  className="h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"
                />
                <label htmlFor="allowRegistration" className={`ml-2 block text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Allow New Registrations
                </label>
              </div>
            </div>
          </div>
          
          {/* Subscription Settings */}
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
            <div className="flex items-center gap-3 mb-4">
              <FaDatabase className="text-green-500" />
              <h3 className="text-lg font-semibold">Subscription Settings</h3>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Free Tier Challenges
                </label>
                <input
                  type="number"
                  min="0"
                  value={settings.subscription.freeTierChallenges}
                  onChange={(e) => handleInputChange('subscription', 'freeTierChallenges', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                />
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Free Tier Modules
                </label>
                <input
                  type="number"
                  min="0"
                  value={settings.subscription.freeTierModules}
                  onChange={(e) => handleInputChange('subscription', 'freeTierModules', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                />
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Premium Price (INR)
                </label>
                <input
                  type="number"
                  min="0"
                  value={settings.subscription.premiumPrice}
                  onChange={(e) => handleInputChange('subscription', 'premiumPrice', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                />
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Business Price (INR)
                </label>
                <input
                  type="number"
                  min="0"
                  value={settings.subscription.businessPrice}
                  onChange={(e) => handleInputChange('subscription', 'businessPrice', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="allowCoinPurchase"
                  checked={settings.subscription.allowCoinPurchase}
                  onChange={() => handleCheckboxChange('subscription', 'allowCoinPurchase')}
                  className="h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"
                />
                <label htmlFor="allowCoinPurchase" className={`ml-2 block text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Allow Coin Purchases
                </label>
              </div>
            </div>
          </div>
          
          {/* Email Settings */}
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
            <div className="flex items-center gap-3 mb-4">
              <FaEnvelope className="text-yellow-500" />
              <h3 className="text-lg font-semibold">Email Settings</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableNotifications"
                  checked={settings.email.enableNotifications}
                  onChange={() => handleCheckboxChange('email', 'enableNotifications')}
                  className="h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"
                />
                <label htmlFor="enableNotifications" className={`ml-2 block text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Enable Email Notifications
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="welcomeEmailEnabled"
                  checked={settings.email.welcomeEmailEnabled}
                  onChange={() => handleCheckboxChange('email', 'welcomeEmailEnabled')}
                  className="h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"
                />
                <label htmlFor="welcomeEmailEnabled" className={`ml-2 block text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Send Welcome Email
                </label>
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Subscription Reminder Days
                </label>
                <input
                  type="number"
                  min="1"
                  max="30"
                  value={settings.email.subscriptionReminderDays}
                  onChange={(e) => handleInputChange('email', 'subscriptionReminderDays', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                />
              </div>
            </div>
          </div>
          
          {/* Security Settings */}
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
            <div className="flex items-center gap-3 mb-4">
              <FaShieldAlt className="text-red-500" />
              <h3 className="text-lg font-semibold">Security Settings</h3>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Max Login Attempts
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={settings.security.maxLoginAttempts}
                  onChange={(e) => handleInputChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                />
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Session Timeout (minutes)
                </label>
                <input
                  type="number"
                  min="5"
                  max="1440"
                  value={settings.security.sessionTimeoutMinutes}
                  onChange={(e) => handleInputChange('security', 'sessionTimeoutMinutes', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireEmailVerification"
                  checked={settings.security.requireEmailVerification}
                  onChange={() => handleCheckboxChange('security', 'requireEmailVerification')}
                  className="h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"
                />
                <label htmlFor="requireEmailVerification" className={`ml-2 block text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Require Email Verification
                </label>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default SystemSettings;
