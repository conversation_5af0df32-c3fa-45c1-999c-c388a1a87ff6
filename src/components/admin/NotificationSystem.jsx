import React, { useState } from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaBell, FaPlus, FaEdit, FaTrash, FaSearch, FaFilter, FaCalendarAlt, FaUsers, FaUserTag, FaGlobe } from 'react-icons/fa';

const NotificationSystem = () => {
  const { darkMode } = useGlobalTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState('all');
  const [activeTab, setActiveTab] = useState('all');
  
  // Mock notifications data
  const mockNotifications = [
    {
      id: 'n1',
      title: 'New Learning Module Available',
      message: 'Check out our new "Advanced Penetration Testing" module to enhance your skills!',
      type: 'announcement',
      target: 'all',
      status: 'active',
      scheduled: false,
      createdAt: '2023-10-15T10:00:00Z',
      expiresAt: '2023-11-15T10:00:00Z',
      views: 450,
      clicks: 120
    },
    {
      id: 'n2',
      title: 'Premium Subscription Discount',
      message: 'Upgrade to Premium now and get 20% off for the first 3 months!',
      type: 'promotion',
      target: 'free',
      status: 'active',
      scheduled: false,
      createdAt: '2023-10-10T14:30:00Z',
      expiresAt: '2023-10-25T14:30:00Z',
      views: 320,
      clicks: 85
    },
    {
      id: 'n3',
      title: 'System Maintenance',
      message: 'The platform will be undergoing maintenance on October 20th from 2:00 AM to 4:00 AM UTC.',
      type: 'system',
      target: 'all',
      status: 'active',
      scheduled: true,
      scheduledFor: '2023-10-18T10:00:00Z',
      createdAt: '2023-10-12T09:15:00Z',
      expiresAt: '2023-10-21T09:15:00Z',
      views: 0,
      clicks: 0
    },
    {
      id: 'n4',
      title: 'New Challenge Released',
      message: 'Test your skills with our new "Network Traffic Analysis" challenge!',
      type: 'announcement',
      target: 'premium',
      status: 'active',
      scheduled: false,
      createdAt: '2023-10-08T16:45:00Z',
      expiresAt: '2023-11-08T16:45:00Z',
      views: 280,
      clicks: 95
    },
    {
      id: 'n5',
      title: 'Welcome to Business Tier',
      message: 'Thank you for upgrading to Business tier! Explore team features and advanced content.',
      type: 'welcome',
      target: 'business',
      status: 'active',
      scheduled: false,
      createdAt: '2023-10-05T11:30:00Z',
      expiresAt: '2023-11-05T11:30:00Z',
      views: 65,
      clicks: 40
    },
    {
      id: 'n6',
      title: 'Holiday Special Event',
      message: 'Join our upcoming holiday special event with exclusive challenges and rewards!',
      type: 'event',
      target: 'all',
      status: 'draft',
      scheduled: true,
      scheduledFor: '2023-12-01T00:00:00Z',
      createdAt: '2023-10-16T13:20:00Z',
      expiresAt: '2023-12-31T23:59:59Z',
      views: 0,
      clicks: 0
    }
  ];
  
  // Filter notifications based on search term, filter, and active tab
  const filteredNotifications = mockNotifications.filter(notification => {
    // Search filter
    const matchesSearch = 
      notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Status/type filter
    const matchesFilter = 
      filterBy === 'all' || 
      notification.status === filterBy ||
      notification.type === filterBy;
    
    // Tab filter
    const matchesTab = 
      activeTab === 'all' || 
      (activeTab === 'scheduled' && notification.scheduled) ||
      (activeTab === 'active' && notification.status === 'active' && !notification.scheduled) ||
      (activeTab === 'draft' && notification.status === 'draft');
    
    return matchesSearch && matchesFilter && matchesTab;
  });
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Get target display
  const getTargetDisplay = (target) => {
    switch (target) {
      case 'free':
        return (
          <span className="flex items-center">
            <FaUserTag className="mr-1 text-blue-500" /> Free Users
          </span>
        );
      case 'premium':
        return (
          <span className="flex items-center">
            <FaUserTag className="mr-1 text-green-500" /> Premium Users
          </span>
        );
      case 'business':
        return (
          <span className="flex items-center">
            <FaUserTag className="mr-1 text-purple-500" /> Business Users
          </span>
        );
      case 'all':
      default:
        return (
          <span className="flex items-center">
            <FaGlobe className="mr-1 text-gray-500" /> All Users
          </span>
        );
    }
  };
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Notification System</h2>
        <button 
          className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center"
        >
          <FaPlus className="mr-2" /> Create Notification
        </button>
      </div>
      
      <div className="flex mb-6 border-b border-gray-200 dark:border-gray-700">
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'all'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('all')}
        >
          All Notifications
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'active'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('active')}
        >
          Active
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'scheduled'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('scheduled')}
        >
          Scheduled
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'draft'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('draft')}
        >
          Drafts
        </button>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className={`flex items-center ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg px-3 py-2`}>
            <FaSearch className={`mr-2 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <input
              type="text"
              placeholder="Search notifications..."
              className={`w-full bg-transparent focus:outline-none ${darkMode ? 'text-white' : 'text-gray-900'}`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        
        <div>
          <select
            className={`px-3 py-2 rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800 text-white' : 'bg-white border-gray-200 text-gray-900'}`}
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value)}
          >
            <option value="all">All Types</option>
            <option value="announcement">Announcements</option>
            <option value="promotion">Promotions</option>
            <option value="system">System Notifications</option>
            <option value="event">Events</option>
            <option value="welcome">Welcome Messages</option>
          </select>
        </div>
      </div>
      
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                <th className="px-4 py-3 text-left">Title</th>
                <th className="px-4 py-3 text-left">Type</th>
                <th className="px-4 py-3 text-left">Target</th>
                <th className="px-4 py-3 text-left">Status</th>
                <th className="px-4 py-3 text-left">Created/Scheduled</th>
                <th className="px-4 py-3 text-left">Expires</th>
                <th className="px-4 py-3 text-left">Views/Clicks</th>
                <th className="px-4 py-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredNotifications.map(notification => (
                <tr 
                  key={notification.id} 
                  className={`border-t ${darkMode ? 'border-gray-800 hover:bg-gray-800' : 'border-gray-200 hover:bg-gray-50'}`}
                >
                  <td className="px-4 py-3 font-medium">{notification.title}</td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      notification.type === 'announcement' 
                        ? 'bg-blue-100 text-blue-800' 
                        : notification.type === 'promotion'
                          ? 'bg-green-100 text-green-800'
                          : notification.type === 'system'
                            ? 'bg-red-100 text-red-800'
                            : notification.type === 'event'
                              ? 'bg-purple-100 text-purple-800'
                              : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    {getTargetDisplay(notification.target)}
                  </td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      notification.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {notification.scheduled 
                        ? 'Scheduled' 
                        : notification.status.charAt(0).toUpperCase() + notification.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    {notification.scheduled 
                      ? formatDate(notification.scheduledFor)
                      : formatDate(notification.createdAt)}
                  </td>
                  <td className="px-4 py-3">{formatDate(notification.expiresAt)}</td>
                  <td className="px-4 py-3">{notification.views} / {notification.clicks}</td>
                  <td className="px-4 py-3">
                    <div className="flex space-x-2">
                      <button className="p-1 text-blue-500 hover:text-blue-700">
                        <FaEdit />
                      </button>
                      <button className="p-1 text-red-500 hover:text-red-700">
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
              
              {filteredNotifications.length === 0 && (
                <tr>
                  <td colSpan="8" className="px-4 py-3 text-center">
                    No notifications found matching your search criteria.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="mt-8">
        <h3 className="text-xl font-bold mb-4">Notification Templates</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-4`}>
            <div className="flex items-center mb-3">
              <FaBell className="text-blue-500 mr-2" />
              <h4 className="font-medium">Announcement Template</h4>
            </div>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
              Use this template for general announcements about new features, content, or updates.
            </p>
            <button className={`text-sm ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-800'}`}>
              Use Template
            </button>
          </div>
          
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-4`}>
            <div className="flex items-center mb-3">
              <FaBell className="text-green-500 mr-2" />
              <h4 className="font-medium">Promotion Template</h4>
            </div>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
              Use this template for promotional offers, discounts, and special deals.
            </p>
            <button className={`text-sm ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-800'}`}>
              Use Template
            </button>
          </div>
          
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-4`}>
            <div className="flex items-center mb-3">
              <FaBell className="text-red-500 mr-2" />
              <h4 className="font-medium">System Notification Template</h4>
            </div>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
              Use this template for system maintenance, downtime, or important updates.
            </p>
            <button className={`text-sm ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-800'}`}>
              Use Template
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationSystem;
