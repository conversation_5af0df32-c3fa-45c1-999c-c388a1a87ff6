import React, { useState } from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaSave, FaHome, FaGraduationCap, FaTrophy, FaCode, FaEye, FaEdit } from 'react-icons/fa';

const PageCustomization = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('landing');
  
  // Mock landing page sections data
  const [landingSections, setLandingSections] = useState([
    {
      id: 'hero',
      name: 'Hero Section',
      enabled: true,
      content: {
        heading: 'Master Cybersecurity Skills with Hands-on Learning',
        subheading: 'Interactive challenges, real-world simulations, and comprehensive learning paths',
        ctaText: 'Get Started',
        ctaLink: '/signup'
      }
    },
    {
      id: 'features',
      name: 'Features Section',
      enabled: true,
      content: {
        heading: 'Why Choose XCerberus',
        features: [
          {
            title: 'Interactive Learning',
            description: 'Learn by doing with hands-on exercises and real-world scenarios',
            icon: 'FaGraduationCap'
          },
          {
            title: 'Practical Challenges',
            description: 'Test your skills with our curated cybersecurity challenges',
            icon: 'FaTrophy'
          },
          {
            title: 'Start Hack Projects',
            description: 'Build your own security tools and applications',
            icon: 'FaCode'
          }
        ]
      }
    },
    {
      id: 'testimonials',
      name: 'Testimonials Section',
      enabled: true,
      content: {
        heading: 'What Our Users Say',
        testimonials: [
          {
            name: 'John Doe',
            role: 'Security Analyst',
            text: 'XCerberus helped me transition from a network admin to a security analyst. The hands-on challenges were invaluable.',
            avatar: 'avatar1.jpg'
          },
          {
            name: 'Jane Smith',
            role: 'Penetration Tester',
            text: 'The practical approach to learning made all the difference. I now feel confident in my pentesting skills.',
            avatar: 'avatar2.jpg'
          }
        ]
      }
    },
    {
      id: 'pricing',
      name: 'Pricing Preview',
      enabled: true,
      content: {
        heading: 'Choose Your Plan',
        subheading: 'Start with our free tier or upgrade for full access'
      }
    },
    {
      id: 'cta',
      name: 'Call to Action',
      enabled: true,
      content: {
        heading: 'Ready to Start Your Cybersecurity Journey?',
        subheading: 'Join thousands of security professionals who trust XCerberus',
        ctaText: 'Sign Up Now',
        ctaLink: '/signup'
      }
    }
  ]);
  
  // Mock dashboard customization data
  const [dashboardCustomization, setDashboardCustomization] = useState({
    free: {
      name: 'Free Dashboard',
      modules: [
        { id: 'overview', name: 'Overview', enabled: true, order: 1 },
        { id: 'learning', name: 'Learning', enabled: true, order: 2 },
        { id: 'challenges', name: 'Challenges', enabled: true, order: 3 },
        { id: 'starthack', name: 'Start Hack', enabled: true, order: 4 },
        { id: 'store', name: 'Store', enabled: true, order: 5 },
        { id: 'community', name: 'Community', enabled: false, order: 6 }
      ],
      widgets: [
        { id: 'progress', name: 'Progress Tracker', enabled: true, order: 1 },
        { id: 'recommended', name: 'Recommended Content', enabled: true, order: 2 },
        { id: 'leaderboard', name: 'Leaderboard Preview', enabled: true, order: 3 },
        { id: 'upgrade', name: 'Upgrade Banner', enabled: true, order: 4 }
      ]
    },
    premium: {
      name: 'Premium Dashboard',
      modules: [
        { id: 'overview', name: 'Overview', enabled: true, order: 1 },
        { id: 'learning', name: 'Learning', enabled: true, order: 2 },
        { id: 'challenges', name: 'Challenges', enabled: true, order: 3 },
        { id: 'starthack', name: 'Start Hack', enabled: true, order: 4 },
        { id: 'store', name: 'Store', enabled: true, order: 5 },
        { id: 'community', name: 'Community', enabled: true, order: 6 },
        { id: 'analytics', name: 'Personal Analytics', enabled: true, order: 7 }
      ],
      widgets: [
        { id: 'progress', name: 'Progress Tracker', enabled: true, order: 1 },
        { id: 'recommended', name: 'Recommended Content', enabled: true, order: 2 },
        { id: 'leaderboard', name: 'Full Leaderboard', enabled: true, order: 3 },
        { id: 'achievements', name: 'Achievements', enabled: true, order: 4 },
        { id: 'activity', name: 'Recent Activity', enabled: true, order: 5 }
      ]
    },
    business: {
      name: 'Business Dashboard',
      modules: [
        { id: 'overview', name: 'Overview', enabled: true, order: 1 },
        { id: 'learning', name: 'Learning', enabled: true, order: 2 },
        { id: 'challenges', name: 'Challenges', enabled: true, order: 3 },
        { id: 'starthack', name: 'Start Hack', enabled: true, order: 4 },
        { id: 'teams', name: 'Teams', enabled: true, order: 5 },
        { id: 'store', name: 'Store', enabled: true, order: 6 },
        { id: 'community', name: 'Community', enabled: true, order: 7 },
        { id: 'analytics', name: 'Team Analytics', enabled: true, order: 8 }
      ],
      widgets: [
        { id: 'progress', name: 'Team Progress', enabled: true, order: 1 },
        { id: 'recommended', name: 'Recommended Content', enabled: true, order: 2 },
        { id: 'leaderboard', name: 'Team Leaderboard', enabled: true, order: 3 },
        { id: 'achievements', name: 'Team Achievements', enabled: true, order: 4 },
        { id: 'activity', name: 'Team Activity', enabled: true, order: 5 },
        { id: 'messages', name: 'Team Messages', enabled: true, order: 6 }
      ]
    }
  });
  
  // Toggle section enabled state
  const toggleSectionEnabled = (id) => {
    setLandingSections(sections => 
      sections.map(section => 
        section.id === id ? { ...section, enabled: !section.enabled } : section
      )
    );
  };
  
  // Toggle module enabled state
  const toggleModuleEnabled = (tier, id) => {
    setDashboardCustomization(prev => ({
      ...prev,
      [tier]: {
        ...prev[tier],
        modules: prev[tier].modules.map(module => 
          module.id === id ? { ...module, enabled: !module.enabled } : module
        )
      }
    }));
  };
  
  // Toggle widget enabled state
  const toggleWidgetEnabled = (tier, id) => {
    setDashboardCustomization(prev => ({
      ...prev,
      [tier]: {
        ...prev[tier],
        widgets: prev[tier].widgets.map(widget => 
          widget.id === id ? { ...widget, enabled: !widget.enabled } : widget
        )
      }
    }));
  };
  
  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <div>
            <h3 className="text-xl font-bold mb-4">Dashboard Customization</h3>
            <p className="mb-6">Customize the dashboard layout and features for each subscription tier.</p>
            
            <div className="mb-8">
              <h4 className="text-lg font-bold mb-4">Free Dashboard</h4>
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-4`}>
                <h5 className="font-medium mb-3">Modules</h5>
                <div className="space-y-2">
                  {dashboardCustomization.free.modules.map(module => (
                    <div 
                      key={module.id}
                      className={`flex items-center justify-between p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}
                    >
                      <div className="flex items-center">
                        <span className="font-medium">{module.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <label className="flex items-center cursor-pointer">
                          <div className="relative">
                            <input
                              type="checkbox"
                              className="sr-only"
                              checked={module.enabled}
                              onChange={() => toggleModuleEnabled('free', module.id)}
                            />
                            <div className={`w-10 h-5 rounded-full ${module.enabled ? 'bg-[#88cc14]' : darkMode ? 'bg-gray-700' : 'bg-gray-300'}`}></div>
                            <div className={`absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform ${module.enabled ? 'transform translate-x-5' : ''}`}></div>
                          </div>
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
                <h5 className="font-medium mb-3">Widgets</h5>
                <div className="space-y-2">
                  {dashboardCustomization.free.widgets.map(widget => (
                    <div 
                      key={widget.id}
                      className={`flex items-center justify-between p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}
                    >
                      <div className="flex items-center">
                        <span className="font-medium">{widget.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <label className="flex items-center cursor-pointer">
                          <div className="relative">
                            <input
                              type="checkbox"
                              className="sr-only"
                              checked={widget.enabled}
                              onChange={() => toggleWidgetEnabled('free', widget.id)}
                            />
                            <div className={`w-10 h-5 rounded-full ${widget.enabled ? 'bg-[#88cc14]' : darkMode ? 'bg-gray-700' : 'bg-gray-300'}`}></div>
                            <div className={`absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform ${widget.enabled ? 'transform translate-x-5' : ''}`}></div>
                          </div>
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            <button className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center">
              <FaSave className="mr-2" /> Save Changes
            </button>
          </div>
        );
      case 'landing':
      default:
        return (
          <div>
            <h3 className="text-xl font-bold mb-4">Landing Page Customization</h3>
            <p className="mb-6">Customize the sections and content displayed on the landing page.</p>
            
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
              <div className="space-y-4">
                {landingSections.map(section => (
                  <div 
                    key={section.id}
                    className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-bold">{section.name}</h4>
                      <div className="flex items-center space-x-3">
                        <button className="p-2 text-blue-500 hover:text-blue-700 rounded-full">
                          <FaEdit />
                        </button>
                        <button className="p-2 text-blue-500 hover:text-blue-700 rounded-full">
                          <FaEye />
                        </button>
                        <label className="flex items-center cursor-pointer">
                          <div className="relative">
                            <input
                              type="checkbox"
                              className="sr-only"
                              checked={section.enabled}
                              onChange={() => toggleSectionEnabled(section.id)}
                            />
                            <div className={`w-10 h-5 rounded-full ${section.enabled ? 'bg-[#88cc14]' : darkMode ? 'bg-gray-700' : 'bg-gray-300'}`}></div>
                            <div className={`absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform ${section.enabled ? 'transform translate-x-5' : ''}`}></div>
                          </div>
                        </label>
                      </div>
                    </div>
                    
                    <div className={`p-3 rounded ${darkMode ? 'bg-gray-900' : 'bg-gray-100'}`}>
                      {section.id === 'hero' && (
                        <div>
                          <p className="font-medium">Heading: {section.content.heading}</p>
                          <p className="text-sm mt-1">Subheading: {section.content.subheading}</p>
                          <p className="text-sm mt-1">CTA: {section.content.ctaText}</p>
                        </div>
                      )}
                      
                      {section.id === 'features' && (
                        <div>
                          <p className="font-medium">Heading: {section.content.heading}</p>
                          <p className="text-sm mt-1">Features: {section.content.features.length} items</p>
                        </div>
                      )}
                      
                      {section.id === 'testimonials' && (
                        <div>
                          <p className="font-medium">Heading: {section.content.heading}</p>
                          <p className="text-sm mt-1">Testimonials: {section.content.testimonials.length} items</p>
                        </div>
                      )}
                      
                      {section.id === 'pricing' && (
                        <div>
                          <p className="font-medium">Heading: {section.content.heading}</p>
                          <p className="text-sm mt-1">Subheading: {section.content.subheading}</p>
                        </div>
                      )}
                      
                      {section.id === 'cta' && (
                        <div>
                          <p className="font-medium">Heading: {section.content.heading}</p>
                          <p className="text-sm mt-1">Subheading: {section.content.subheading}</p>
                          <p className="text-sm mt-1">CTA: {section.content.ctaText}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <button className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center">
              <FaSave className="mr-2" /> Save Changes
            </button>
          </div>
        );
    }
  };
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Page Customization</h2>
        <button 
          className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center"
        >
          <FaEye className="mr-2" /> Preview Changes
        </button>
      </div>
      
      <div className="flex mb-6 border-b border-gray-200 dark:border-gray-700">
        <button
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === 'landing'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('landing')}
        >
          <FaHome className="mr-2" /> Landing Page
        </button>
        <button
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === 'dashboard'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('dashboard')}
        >
          <FaGraduationCap className="mr-2" /> Dashboard
        </button>
      </div>
      
      {renderContent()}
    </div>
  );
};

export default PageCustomization;
