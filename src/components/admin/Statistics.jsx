import React, { useState } from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaUsers, FaUserTag, FaCoins, FaChartLine, FaFileAlt, FaTrophy, FaDownload, FaCalendarAlt } from 'react-icons/fa';

const Statistics = () => {
  const { darkMode } = useGlobalTheme();
  const [timeRange, setTimeRange] = useState('30days');
  
  // Mock statistics data
  const mockStats = {
    totalUsers: 1250,
    activeUsers: 980,
    newUsers: 125,
    premiumUsers: 320,
    businessUsers: 85,
    totalRevenue: 15750,
    monthlyRevenue: 4250,
    totalCoins: 78500,
    coinsSpent: 45200,
    learningModules: {
      total: 24,
      views: 8750,
      completions: 3200,
      avgCompletionRate: 36.5
    },
    challenges: {
      total: 32,
      attempts: 6500,
      completions: 2100,
      avgCompletionRate: 32.3
    },
    startHack: {
      total: 18,
      starts: 4200,
      completions: 1450,
      avgCompletionRate: 34.5
    }
  };
  
  // Mock chart data (simplified for this example)
  const mockChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: {
      users: [120, 150, 180, 220, 250, 310, 380, 450, 520, 600, 680, 750],
      revenue: [1200, 1500, 1800, 2200, 2500, 3100, 3800, 4500, 5200, 6000, 6800, 7500],
      engagement: [25, 28, 30, 32, 35, 38, 40, 42, 45, 48, 50, 52]
    }
  };
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Statistics & Tracking</h2>
        
        <div className="flex items-center space-x-4">
          <select
            className={`px-3 py-2 rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800 text-white' : 'bg-white border-gray-200 text-gray-900'}`}
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <option value="7days">Last 7 Days</option>
            <option value="30days">Last 30 Days</option>
            <option value="90days">Last 90 Days</option>
            <option value="year">Last Year</option>
            <option value="all">All Time</option>
          </select>
          
          <button 
            className={`px-3 py-2 rounded-lg border flex items-center ${darkMode ? 'bg-[#1A1F35] border-gray-800 hover:bg-gray-800' : 'bg-white border-gray-200 hover:bg-gray-100'}`}
          >
            <FaDownload className="mr-2" /> Export Report
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full bg-blue-500 bg-opacity-10">
              <FaUsers className="text-blue-500" />
            </div>
            <h3 className="ml-3 text-lg font-semibold">Users</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Total</p>
              <p className="text-2xl font-bold">{mockStats.totalUsers}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Active</p>
              <p className="text-2xl font-bold">{mockStats.activeUsers}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>New</p>
              <p className="text-2xl font-bold">{mockStats.newUsers}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Premium</p>
              <p className="text-2xl font-bold">{mockStats.premiumUsers}</p>
            </div>
          </div>
        </div>
        
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full bg-green-500 bg-opacity-10">
              <FaCoins className="text-green-500" />
            </div>
            <h3 className="ml-3 text-lg font-semibold">Revenue</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Total</p>
              <p className="text-2xl font-bold">₹{mockStats.totalRevenue}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Monthly</p>
              <p className="text-2xl font-bold">₹{mockStats.monthlyRevenue}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Coins Total</p>
              <p className="text-2xl font-bold">{mockStats.totalCoins}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Coins Spent</p>
              <p className="text-2xl font-bold">{mockStats.coinsSpent}</p>
            </div>
          </div>
        </div>
        
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full bg-purple-500 bg-opacity-10">
              <FaFileAlt className="text-purple-500" />
            </div>
            <h3 className="ml-3 text-lg font-semibold">Learning</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Modules</p>
              <p className="text-2xl font-bold">{mockStats.learningModules.total}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Views</p>
              <p className="text-2xl font-bold">{mockStats.learningModules.views}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Completions</p>
              <p className="text-2xl font-bold">{mockStats.learningModules.completions}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Completion Rate</p>
              <p className="text-2xl font-bold">{mockStats.learningModules.avgCompletionRate}%</p>
            </div>
          </div>
        </div>
        
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full bg-yellow-500 bg-opacity-10">
              <FaTrophy className="text-yellow-500" />
            </div>
            <h3 className="ml-3 text-lg font-semibold">Challenges</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Total</p>
              <p className="text-2xl font-bold">{mockStats.challenges.total}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Attempts</p>
              <p className="text-2xl font-bold">{mockStats.challenges.attempts}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Completions</p>
              <p className="text-2xl font-bold">{mockStats.challenges.completions}</p>
            </div>
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Completion Rate</p>
              <p className="text-2xl font-bold">{mockStats.challenges.avgCompletionRate}%</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <h3 className="text-lg font-semibold mb-4">User Growth</h3>
          <div className="h-64 flex items-center justify-center">
            <div className={`text-center ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              <FaChartLine className="mx-auto text-4xl mb-4" />
              <p>User growth chart will be displayed here.</p>
              <p className="text-sm mt-2">Total users: {mockStats.totalUsers}</p>
            </div>
          </div>
        </div>
        
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <h3 className="text-lg font-semibold mb-4">Revenue Trends</h3>
          <div className="h-64 flex items-center justify-center">
            <div className={`text-center ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              <FaChartLine className="mx-auto text-4xl mb-4" />
              <p>Revenue trends chart will be displayed here.</p>
              <p className="text-sm mt-2">Total revenue: ₹{mockStats.totalRevenue}</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6 mb-8`}>
        <h3 className="text-lg font-semibold mb-4">Engagement Metrics</h3>
        <div className="h-64 flex items-center justify-center">
          <div className={`text-center ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <FaChartLine className="mx-auto text-4xl mb-4" />
            <p>Engagement metrics chart will be displayed here.</p>
            <p className="text-sm mt-2">Average completion rate: {(mockStats.learningModules.avgCompletionRate + mockStats.challenges.avgCompletionRate) / 2}%</p>
          </div>
        </div>
      </div>
      
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Recent Activity</h3>
          <button className={`text-sm ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-800'}`}>
            View All
          </button>
        </div>
        
        <div className="space-y-4">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-green-500 bg-opacity-10 mr-3">
                <FaUserTag className="text-green-500" />
              </div>
              <div>
                <p className="font-medium">New Premium Subscription</p>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>User <EMAIL> upgraded to Premium</p>
              </div>
              <div className="ml-auto">
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>2 hours ago</p>
              </div>
            </div>
          </div>
          
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-blue-500 bg-opacity-10 mr-3">
                <FaUsers className="text-blue-500" />
              </div>
              <div>
                <p className="font-medium">New User Registration</p>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>5 new users registered in the last 24 hours</p>
              </div>
              <div className="ml-auto">
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>5 hours ago</p>
              </div>
            </div>
          </div>
          
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-purple-500 bg-opacity-10 mr-3">
                <FaFileAlt className="text-purple-500" />
              </div>
              <div>
                <p className="font-medium">New Learning Module Published</p>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>"Advanced Penetration Testing" module published</p>
              </div>
              <div className="ml-auto">
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>1 day ago</p>
              </div>
            </div>
          </div>
          
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-yellow-500 bg-opacity-10 mr-3">
                <FaTrophy className="text-yellow-500" />
              </div>
              <div>
                <p className="font-medium">Challenge Milestone</p>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>"SQL Injection Basics" reached 1000 attempts</p>
              </div>
              <div className="ml-auto">
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>2 days ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Statistics;
