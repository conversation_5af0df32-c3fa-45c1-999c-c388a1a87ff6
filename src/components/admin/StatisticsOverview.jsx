import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart, 
  Pie, 
  Cell, 
  LineChart,
  Line
} from 'recharts';

/**
 * StatisticsOverview Component
 * 
 * Displays various statistics and charts for the admin dashboard.
 */
const StatisticsOverview = () => {
  const { darkMode } = useGlobalTheme();
  const [stats, setStats] = useState({
    userStats: [],
    subscriptionStats: [],
    revenueStats: [],
    contentStats: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('month'); // 'week', 'month', 'year'
  
  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];
  
  // Mock data for demonstration
  const mockSubscriptionStats = [
    { name: 'Free', value: 65 },
    { name: 'Premium', value: 25 },
    { name: 'Business', value: 10 }
  ];
  
  const mockRevenueStats = [
    { month: 'Jan', revenue: 25000 },
    { month: 'Feb', revenue: 35000 },
    { month: 'Mar', revenue: 45000 },
    { month: 'Apr', revenue: 40000 },
    { month: 'May', revenue: 50000 },
    { month: 'Jun', revenue: 65000 }
  ];
  
  const mockUserGrowthStats = [
    { month: 'Jan', users: 120 },
    { month: 'Feb', users: 180 },
    { month: 'Mar', users: 250 },
    { month: 'Apr', users: 310 },
    { month: 'May', users: 390 },
    { month: 'Jun', users: 450 }
  ];
  
  const mockContentEngagementStats = [
    { name: 'Web Security', completed: 75, inProgress: 25 },
    { name: 'Network Security', completed: 60, inProgress: 40 },
    { name: 'Cryptography', completed: 45, inProgress: 55 },
    { name: 'OSINT', completed: 80, inProgress: 20 },
    { name: 'Reverse Engineering', completed: 30, inProgress: 70 }
  ];
  
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        
        // In a real implementation, we would fetch data from Supabase here
        // For now, we'll use mock data
        
        setStats({
          userStats: mockUserGrowthStats,
          subscriptionStats: mockSubscriptionStats,
          revenueStats: mockRevenueStats,
          contentStats: mockContentEngagementStats
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchStats();
  }, [timeRange]);
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"></div>
      </div>
    );
  }
  
  return (
    <div className="space-y-8">
      {/* Time Range Selector */}
      <div className="flex justify-end mb-4">
        <div className={`inline-flex rounded-md shadow-sm ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'}`}>
          <button
            onClick={() => setTimeRange('week')}
            className={`px-4 py-2 text-sm font-medium rounded-l-md ${
              timeRange === 'week'
                ? 'bg-[#88cc14] text-black'
                : darkMode
                  ? 'text-gray-400 hover:text-white hover:bg-gray-800'
                  : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            Week
          </button>
          <button
            onClick={() => setTimeRange('month')}
            className={`px-4 py-2 text-sm font-medium ${
              timeRange === 'month'
                ? 'bg-[#88cc14] text-black'
                : darkMode
                  ? 'text-gray-400 hover:text-white hover:bg-gray-800'
                  : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            Month
          </button>
          <button
            onClick={() => setTimeRange('year')}
            className={`px-4 py-2 text-sm font-medium rounded-r-md ${
              timeRange === 'year'
                ? 'bg-[#88cc14] text-black'
                : darkMode
                  ? 'text-gray-400 hover:text-white hover:bg-gray-800'
                  : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            Year
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Subscription Distribution */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} p-4 rounded-lg border`}>
          <h3 className="text-lg font-semibold mb-4">Subscription Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={stats.subscriptionStats}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {stats.subscriptionStats.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: darkMode ? '#1A1F35' : 'white',
                  borderColor: darkMode ? '#374151' : '#E5E7EB',
                  color: darkMode ? 'white' : 'black'
                }} 
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        {/* Revenue Trends */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} p-4 rounded-lg border`}>
          <h3 className="text-lg font-semibold mb-4">Revenue Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={stats.revenueStats}>
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#E5E7EB'} />
              <XAxis 
                dataKey="month" 
                tick={{ fill: darkMode ? '#9CA3AF' : '#4B5563' }} 
              />
              <YAxis 
                tick={{ fill: darkMode ? '#9CA3AF' : '#4B5563' }} 
              />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: darkMode ? '#1A1F35' : 'white',
                  borderColor: darkMode ? '#374151' : '#E5E7EB',
                  color: darkMode ? 'white' : 'black'
                }} 
              />
              <Legend />
              <Bar dataKey="revenue" fill="#88cc14" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* User Growth */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} p-4 rounded-lg border`}>
          <h3 className="text-lg font-semibold mb-4">User Growth</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={stats.userStats}>
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#E5E7EB'} />
              <XAxis 
                dataKey="month" 
                tick={{ fill: darkMode ? '#9CA3AF' : '#4B5563' }} 
              />
              <YAxis 
                tick={{ fill: darkMode ? '#9CA3AF' : '#4B5563' }} 
              />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: darkMode ? '#1A1F35' : 'white',
                  borderColor: darkMode ? '#374151' : '#E5E7EB',
                  color: darkMode ? 'white' : 'black'
                }} 
              />
              <Legend />
              <Line type="monotone" dataKey="users" stroke="#0088FE" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        {/* Content Engagement */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} p-4 rounded-lg border`}>
          <h3 className="text-lg font-semibold mb-4">Content Engagement</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={stats.contentStats}
              layout="vertical"
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#E5E7EB'} />
              <XAxis 
                type="number" 
                tick={{ fill: darkMode ? '#9CA3AF' : '#4B5563' }} 
              />
              <YAxis 
                dataKey="name" 
                type="category" 
                tick={{ fill: darkMode ? '#9CA3AF' : '#4B5563' }} 
                width={120}
              />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: darkMode ? '#1A1F35' : 'white',
                  borderColor: darkMode ? '#374151' : '#E5E7EB',
                  color: darkMode ? 'white' : 'black'
                }} 
              />
              <Legend />
              <Bar dataKey="completed" stackId="a" fill="#00C49F" />
              <Bar dataKey="inProgress" stackId="a" fill="#FFBB28" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default StatisticsOverview;
