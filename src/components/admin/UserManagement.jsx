import React, { useState } from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaUserPlus, FaUserEdit, FaUserMinus, FaSearch, FaFilter, FaDownload } from 'react-icons/fa';

const UserManagement = () => {
  const { darkMode } = useGlobalTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState('all');
  
  // Mock user data
  const mockUsers = [
    {
      id: 'u1',
      email: '<EMAIL>',
      username: 'free_user',
      fullName: 'Free User',
      subscriptionTier: 'free',
      coins: 100,
      lastLogin: '2023-10-15T14:30:00Z',
      status: 'active',
      createdAt: '2023-09-01T10:00:00Z'
    },
    {
      id: 'u2',
      email: '<EMAIL>',
      username: 'premium_user',
      fullName: 'Premium User',
      subscriptionTier: 'premium',
      coins: 500,
      lastLogin: '2023-10-16T09:15:00Z',
      status: 'active',
      createdAt: '2023-09-05T11:30:00Z'
    },
    {
      id: 'u3',
      email: '<EMAIL>',
      username: 'business_user',
      fullName: 'Business User',
      subscriptionTier: 'business',
      coins: 1000,
      lastLogin: '2023-10-14T16:45:00Z',
      status: 'active',
      createdAt: '2023-08-20T14:20:00Z'
    },
    {
      id: 'u4',
      email: '<EMAIL>',
      username: 'chitti',
      fullName: 'Goutham Kumar Chitti',
      subscriptionTier: 'free',
      coins: 100,
      lastLogin: '2023-10-16T11:10:00Z',
      status: 'active',
      createdAt: '2023-10-01T09:00:00Z'
    },
    {
      id: 'u5',
      email: '<EMAIL>',
      username: 'inactive_user',
      fullName: 'Inactive User',
      subscriptionTier: 'free',
      coins: 50,
      lastLogin: '2023-09-01T08:20:00Z',
      status: 'inactive',
      createdAt: '2023-07-15T13:40:00Z'
    }
  ];
  
  // Filter users based on search term and filter
  const filteredUsers = mockUsers.filter(user => {
    // Search filter
    const matchesSearch = 
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.fullName.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Subscription filter
    const matchesFilter = 
      filterBy === 'all' || 
      user.subscriptionTier === filterBy ||
      (filterBy === 'inactive' && user.status === 'inactive');
    
    return matchesSearch && matchesFilter;
  });
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">User Management</h2>
        <button 
          className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center"
        >
          <FaUserPlus className="mr-2" /> Add New User
        </button>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className={`flex items-center ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg px-3 py-2`}>
            <FaSearch className={`mr-2 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <input
              type="text"
              placeholder="Search users..."
              className={`w-full bg-transparent focus:outline-none ${darkMode ? 'text-white' : 'text-gray-900'}`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        
        <div>
          <select
            className={`px-3 py-2 rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800 text-white' : 'bg-white border-gray-200 text-gray-900'}`}
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value)}
          >
            <option value="all">All Users</option>
            <option value="free">Free Tier</option>
            <option value="premium">Premium Tier</option>
            <option value="business">Business Tier</option>
            <option value="inactive">Inactive Users</option>
          </select>
        </div>
        
        <button 
          className={`px-3 py-2 rounded-lg border flex items-center ${darkMode ? 'bg-[#1A1F35] border-gray-800 hover:bg-gray-800' : 'bg-white border-gray-200 hover:bg-gray-100'}`}
        >
          <FaDownload className="mr-2" /> Export
        </button>
      </div>
      
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                <th className="px-4 py-3 text-left">User</th>
                <th className="px-4 py-3 text-left">Email</th>
                <th className="px-4 py-3 text-left">Subscription</th>
                <th className="px-4 py-3 text-left">Coins</th>
                <th className="px-4 py-3 text-left">Last Login</th>
                <th className="px-4 py-3 text-left">Status</th>
                <th className="px-4 py-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map(user => (
                <tr 
                  key={user.id} 
                  className={`border-t ${darkMode ? 'border-gray-800 hover:bg-gray-800' : 'border-gray-200 hover:bg-gray-50'}`}
                >
                  <td className="px-4 py-3">
                    <div>
                      <div className="font-medium">{user.fullName}</div>
                      <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>@{user.username}</div>
                    </div>
                  </td>
                  <td className="px-4 py-3">{user.email}</td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      user.subscriptionTier === 'premium' 
                        ? 'bg-green-100 text-green-800' 
                        : user.subscriptionTier === 'business'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-blue-100 text-blue-800'
                    }`}>
                      {user.subscriptionTier.charAt(0).toUpperCase() + user.subscriptionTier.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-3">{user.coins}</td>
                  <td className="px-4 py-3">{formatDate(user.lastLogin)}</td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      user.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex space-x-2">
                      <button className="p-1 text-blue-500 hover:text-blue-700">
                        <FaUserEdit />
                      </button>
                      <button className="p-1 text-red-500 hover:text-red-700">
                        <FaUserMinus />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
              
              {filteredUsers.length === 0 && (
                <tr>
                  <td colSpan="7" className="px-4 py-3 text-center">
                    No users found matching your search criteria.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default UserManagement;
