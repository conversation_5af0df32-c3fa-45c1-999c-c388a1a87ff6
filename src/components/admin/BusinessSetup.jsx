import React, { useState } from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaSave, FaCoins, FaUsers, FaBuilding, FaCreditCard } from 'react-icons/fa';

const BusinessSetup = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('subscription');
  
  // Mock subscription tiers data
  const [subscriptionTiers, setSubscriptionTiers] = useState({
    free: {
      name: 'Free',
      price: 0,
      currency: 'INR',
      billingCycle: 'monthly',
      features: [
        'Access to 5 basic challenges',
        'Limited learning content',
        'Access to 15 Start Hack features (requires coins)',
        'Community access (read-only)'
      ],
      limits: {
        challenges: 5,
        learningModules: 10,
        startHack: 15
      }
    },
    premium: {
      name: 'Premium',
      price: 399,
      currency: 'INR',
      billingCycle: 'monthly',
      features: [
        'Full access to learning modules',
        'Access to all Start Hack features',
        'Access to 15 challenges',
        'Community access',
        'Real Linux boxes'
      ],
      limits: {
        challenges: 15,
        learningModules: 'unlimited',
        startHack: 'unlimited'
      }
    },
    business: {
      name: 'Business',
      price: 'custom',
      currency: 'INR',
      billingCycle: 'monthly',
      features: [
        'All Premium features',
        'Team formation (up to 15 groups)',
        'Up to 8 members per team',
        'Internal messaging',
        'Team analytics',
        'Priority support'
      ],
      limits: {
        teams: 15,
        membersPerTeam: 8,
        challenges: 'unlimited',
        learningModules: 'unlimited',
        startHack: 'unlimited'
      }
    }
  });
  
  // Mock coin packages data
  const [coinPackages, setCoinPackages] = useState([
    {
      id: 'cp1',
      name: 'Starter Pack',
      coins: 100,
      price: 99,
      currency: 'INR',
      popular: false
    },
    {
      id: 'cp2',
      name: 'Value Pack',
      coins: 300,
      price: 249,
      currency: 'INR',
      popular: true
    },
    {
      id: 'cp3',
      name: 'Pro Pack',
      coins: 500,
      price: 399,
      currency: 'INR',
      popular: false
    },
    {
      id: 'cp4',
      name: 'Ultimate Pack',
      coins: 1000,
      price: 699,
      currency: 'INR',
      popular: false
    }
  ]);
  
  // Mock payment settings
  const [paymentSettings, setPaymentSettings] = useState({
    gateway: 'razorpay',
    testMode: true,
    supportedCurrencies: ['INR'],
    defaultCurrency: 'INR',
    taxRate: 18, // GST in India
    invoicePrefix: 'XCB-'
  });
  
  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'coins':
        return (
          <div>
            <h3 className="text-xl font-bold mb-4">Coin Packages</h3>
            <p className="mb-6">Configure coin packages that users can purchase.</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {coinPackages.map((pkg, index) => (
                <div 
                  key={pkg.id}
                  className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-4 relative ${pkg.popular ? 'ring-2 ring-[#88cc14]' : ''}`}
                >
                  {pkg.popular && (
                    <div className="absolute top-0 right-0 bg-[#88cc14] text-black text-xs font-bold px-2 py-1 rounded-bl-lg rounded-tr-lg">
                      Popular
                    </div>
                  )}
                  <h4 className="text-lg font-bold mb-2">{pkg.name}</h4>
                  <div className="flex items-center mb-4">
                    <FaCoins className="text-yellow-500 mr-2" />
                    <span className="text-2xl font-bold">{pkg.coins}</span>
                    <span className={`ml-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>coins</span>
                  </div>
                  <div className="mb-4">
                    <span className="text-xl font-bold">{pkg.currency} {pkg.price}</span>
                  </div>
                  <button className="w-full py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors">
                    Edit Package
                  </button>
                </div>
              ))}
            </div>
            
            <button className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center">
              Add New Package
            </button>
          </div>
        );
      case 'payment':
        return (
          <div>
            <h3 className="text-xl font-bold mb-4">Payment Settings</h3>
            <p className="mb-6">Configure payment gateway and related settings.</p>
            
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
              <h4 className="text-lg font-bold mb-4">Payment Gateway</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block mb-2">Payment Provider</label>
                  <select
                    className={`w-full px-3 py-2 rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800 text-white' : 'bg-white border-gray-200 text-gray-900'}`}
                    value={paymentSettings.gateway}
                    onChange={(e) => setPaymentSettings({...paymentSettings, gateway: e.target.value})}
                  >
                    <option value="razorpay">Razorpay</option>
                    <option value="stripe">Stripe</option>
                    <option value="paypal">PayPal</option>
                  </select>
                </div>
                
                <div>
                  <label className="block mb-2">Mode</label>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={paymentSettings.testMode}
                        onChange={() => setPaymentSettings({...paymentSettings, testMode: true})}
                        className="mr-2"
                      />
                      Test Mode
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={!paymentSettings.testMode}
                        onChange={() => setPaymentSettings({...paymentSettings, testMode: false})}
                        className="mr-2"
                      />
                      Live Mode
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block mb-2">Default Currency</label>
                  <select
                    className={`w-full px-3 py-2 rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800 text-white' : 'bg-white border-gray-200 text-gray-900'}`}
                    value={paymentSettings.defaultCurrency}
                    onChange={(e) => setPaymentSettings({...paymentSettings, defaultCurrency: e.target.value})}
                  >
                    <option value="INR">Indian Rupee (₹)</option>
                    <option value="USD">US Dollar ($)</option>
                    <option value="EUR">Euro (€)</option>
                  </select>
                </div>
                
                <div>
                  <label className="block mb-2">Tax Rate (%)</label>
                  <input
                    type="number"
                    className={`w-full px-3 py-2 rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800 text-white' : 'bg-white border-gray-200 text-gray-900'}`}
                    value={paymentSettings.taxRate}
                    onChange={(e) => setPaymentSettings({...paymentSettings, taxRate: parseFloat(e.target.value)})}
                  />
                </div>
              </div>
            </div>
            
            <button className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center">
              <FaSave className="mr-2" /> Save Settings
            </button>
          </div>
        );
      case 'subscription':
      default:
        return (
          <div>
            <h3 className="text-xl font-bold mb-4">Subscription Tiers</h3>
            <p className="mb-6">Configure subscription tiers and their features.</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {Object.entries(subscriptionTiers).map(([key, tier]) => (
                <div 
                  key={key}
                  className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}
                >
                  <h4 className="text-xl font-bold mb-2">{tier.name}</h4>
                  <div className="mb-4">
                    <span className="text-2xl font-bold">
                      {tier.price === 'custom' ? 'Custom' : `${tier.currency} ${tier.price}`}
                    </span>
                    {tier.price !== 'custom' && tier.price > 0 && (
                      <span className={`ml-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        /{tier.billingCycle}
                      </span>
                    )}
                  </div>
                  
                  <div className="mb-4">
                    <h5 className="font-medium mb-2">Features:</h5>
                    <ul className="space-y-2">
                      {tier.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-[#88cc14] mr-2">✓</span>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <button className="w-full py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors">
                    Edit Tier
                  </button>
                </div>
              ))}
            </div>
          </div>
        );
    }
  };
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Business Setup</h2>
        <button 
          className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center"
        >
          <FaSave className="mr-2" /> Save All Changes
        </button>
      </div>
      
      <div className="flex mb-6 border-b border-gray-200 dark:border-gray-700">
        <button
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === 'subscription'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('subscription')}
        >
          <FaUsers className="mr-2" /> Subscription Tiers
        </button>
        <button
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === 'coins'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('coins')}
        >
          <FaCoins className="mr-2" /> Coin Packages
        </button>
        <button
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === 'payment'
              ? 'border-b-2 border-[#88cc14] text-[#88cc14]'
              : darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('payment')}
        >
          <FaCreditCard className="mr-2" /> Payment Settings
        </button>
      </div>
      
      {renderContent()}
    </div>
  );
};

export default BusinessSetup;
