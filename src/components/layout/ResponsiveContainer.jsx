import React from 'react';
import { useIsMobile, useIsTablet, useIsDesktop } from '../../hooks/useMediaQuery';

/**
 * A responsive container component that renders different content based on screen size
 * @param {Object} props - The component props
 * @param {React.ReactNode} props.children - The default content
 * @param {React.ReactNode} props.mobile - The content to render on mobile
 * @param {React.ReactNode} props.tablet - The content to render on tablet
 * @param {React.ReactNode} props.desktop - The content to render on desktop
 * @returns {React.ReactNode} - The responsive content
 */
const ResponsiveContainer = ({ 
  children, 
  mobile, 
  tablet, 
  desktop,
  className = '',
  ...props 
}) => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isDesktop = useIsDesktop();

  // Determine which content to render
  let content = children;

  if (isMobile && mobile) {
    content = mobile;
  } else if (isTablet && tablet) {
    content = tablet;
  } else if (isDesktop && desktop) {
    content = desktop;
  }

  return (
    <div className={className} {...props}>
      {content}
    </div>
  );
};

export default ResponsiveContainer;
