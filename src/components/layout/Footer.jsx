import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aG<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>rd, FaEnvelope } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import XCerberusLogo from '../icons/XCerberusLogo';

const Footer = () => {
  const { darkMode } = useGlobalTheme();
  const { trackEvent } = useAnalytics();

  const handleSocialClick = (platform) => {
    trackEvent('social_click', { platform });
  };

  return (
    <footer className={`${darkMode ? 'bg-[#0B1120] text-gray-300' : 'bg-gray-100 text-gray-700'} py-8`}>
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-10">
          {/* Logo and Description */}
          <div className="col-span-1 sm:col-span-2 lg:col-span-1">
            <Link to="/" className="inline-flex items-center mb-4">
              <XCerberusLogo darkMode={darkMode} />
            </Link>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 max-w-xs`}>
              Empowering cybersecurity professionals with hands-on learning and real-world challenges.
            </p>
            <div className="flex flex-wrap gap-4">
              <a
                href="https://twitter.com/xcerberus"
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => handleSocialClick('twitter')}
                className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200`}
                aria-label="Twitter"
              >
                <FaTwitter size={20} />
              </a>
              <a
                href="https://linkedin.com/company/xcerberus"
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => handleSocialClick('linkedin')}
                className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200`}
                aria-label="LinkedIn"
              >
                <FaLinkedin size={20} />
              </a>
              <a
                href="https://github.com/xcerberus"
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => handleSocialClick('github')}
                className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200`}
                aria-label="GitHub"
              >
                <FaGithub size={20} />
              </a>
              <a
                href="https://discord.gg/xcerberus"
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => handleSocialClick('discord')}
                className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200`}
                aria-label="Discord"
              >
                <FaDiscord size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/learn"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  Learning Modules
                </Link>
              </li>
              <li>
                <Link
                  to="/challenges"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  Challenges
                </Link>
              </li>
              <li>
                <Link
                  to="/teams"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  Teams
                </Link>
              </li>
              <li>
                <Link
                  to="/leaderboard"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  Leaderboard
                </Link>
              </li>
              <li>
                <Link
                  to="/store"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  Store
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4">Resources</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/blog"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  Blog
                </Link>
              </li>
              <li>
                <Link
                  to="/documentation"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  Documentation
                </Link>
              </li>
              <li>
                <Link
                  to="/faq"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  FAQ
                </Link>
              </li>
              <li>
                <Link
                  to="/support"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  Support
                </Link>
              </li>
              <li>
                <a
                  href="https://status.xcerberus.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors duration-200 block`}
                >
                  System Status
                </a>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div className="col-span-1 sm:col-span-2 lg:col-span-1">
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 max-w-xs`}>
              Have questions or feedback? We'd love to hear from you!
            </p>
            <a
              href="mailto:<EMAIL>"
              className="flex items-center mb-4 text-[#88cc14] hover:text-[#a0e82a] transition-colors duration-200"
            >
              <FaEnvelope className="mr-2" />
              <EMAIL>
            </a>
            <Link
              to="/contact"
              className="inline-block px-5 py-2.5 bg-[#88cc14] text-white rounded-lg hover:bg-[#a0e82a] transition-colors duration-200 font-medium text-sm"
            >
              Contact Us
            </Link>
          </div>
        </div>

        {/* Bottom Section */}
        <div className={`mt-10 pt-6 border-t ${darkMode ? 'border-gray-800' : 'border-gray-300'}`}>
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2">
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm text-center sm:text-left`}>
                &copy; {new Date().getFullYear()}
              </p>
              <XCerberusLogo darkMode={darkMode} size="small" />
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm text-center sm:text-left`}>
                All rights reserved.
              </p>
            </div>
            <div className="flex flex-wrap justify-center gap-4 sm:gap-6">
              <Link
                to="/privacy"
                className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} text-sm transition-colors duration-200`}
              >
                Privacy Policy
              </Link>
              <Link
                to="/terms"
                className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} text-sm transition-colors duration-200`}
              >
                Terms of Service
              </Link>
              <Link
                to="/cookies"
                className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} text-sm transition-colors duration-200`}
              >
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
