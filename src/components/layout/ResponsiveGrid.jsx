import React from 'react';

/**
 * A responsive grid component
 * @param {Object} props - The component props
 * @param {React.ReactNode} props.children - The grid items
 * @param {number} props.cols - The number of columns on desktop
 * @param {number} props.colsMd - The number of columns on tablet
 * @param {number} props.colsSm - The number of columns on mobile
 * @param {string} props.gap - The gap between grid items
 * @param {string} props.className - Additional classes
 * @returns {React.ReactNode} - The responsive grid
 */
const ResponsiveGrid = ({ 
  children, 
  cols = 3, 
  colsMd = 2, 
  colsSm = 1, 
  gap = '1rem',
  className = '',
  ...props 
}) => {
  // Generate grid template columns based on screen size
  const gridTemplateColumns = {
    gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))`,
  };

  // Generate responsive classes
  const responsiveClasses = `
    grid
    grid-cols-${colsSm}
    md:grid-cols-${colsMd}
    lg:grid-cols-${cols}
    gap-4
    ${className}
  `;

  return (
    <div 
      className={responsiveClasses}
      style={{ gap }}
      {...props}
    >
      {children}
    </div>
  );
};

export default ResponsiveGrid;
