import React from 'react';
import { FaFire, FaTrophy, FaCode, FaBook, FaChartLine, FaArrowRight } from 'react-icons/fa';

const DashboardHome = () => {
  // Mock data for the dashboard
  const stats = [
    { label: 'Offensive', value: '25%', color: 'bg-red-500' },
    { label: 'Defensive', value: '40%', color: 'bg-blue-500' },
    { label: 'General', value: '35%', color: 'bg-green-500' },
  ];
  
  const currentPath = {
    title: 'Bug Bounty Hunter',
    progress: 35,
    modules: 12,
    completed: 4,
  };
  
  const weeklyStreak = {
    current: 3,
    days: [true, true, true, false, false, false, false], // Sun to Sat
  };
  
  const recentModules = [
    { name: 'Web Requests', difficulty: 'Easy', completed: true },
    { name: 'Introduction to Web Applications', difficulty: 'Fundamental', completed: false },
    { name: 'Using Web Proxies', difficulty: 'Easy', completed: false },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-[#0F172A] rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-2">Welcome back, Username!</h2>
        <p className="text-gray-400">Continue your cybersecurity journey where you left off.</p>
      </div>
      
      {/* Stats Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {stats.map((stat, index) => (
          <div key={index} className="bg-[#0F172A] rounded-lg p-6 flex flex-col items-center">
            <div className={`w-24 h-24 rounded-full ${stat.color} bg-opacity-20 flex items-center justify-center mb-4 border-4 ${stat.color} border-opacity-50`}>
              <span className="text-2xl font-bold">{stat.value}</span>
            </div>
            <h3 className="text-lg font-medium">{stat.label}</h3>
          </div>
        ))}
      </div>
      
      {/* Current Path Section */}
      <div className="bg-[#0F172A] rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold">Currently Enrolled Path</h3>
          <button className="text-primary hover:underline flex items-center gap-1 text-sm">
            View All <FaArrowRight className="text-xs" />
          </button>
        </div>
        
        <div className="flex flex-col md:flex-row gap-6">
          <div className="bg-[#1E293B] rounded-lg p-4 flex-1">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-12 h-12 rounded-lg bg-primary bg-opacity-20 flex items-center justify-center">
                <FaCode className="text-primary text-xl" />
              </div>
              <div>
                <h4 className="font-bold">{currentPath.title}</h4>
                <p className="text-sm text-gray-400">
                  {currentPath.completed} of {currentPath.modules} modules completed
                </p>
              </div>
            </div>
            
            <div className="w-full bg-gray-800 rounded-full h-2 mb-2">
              <div 
                className="bg-primary h-2 rounded-full" 
                style={{ width: `${currentPath.progress}%` }}
              ></div>
            </div>
            
            <div className="flex justify-between text-xs text-gray-400">
              <span>{currentPath.progress}% complete</span>
              <span>Estimated time: 12 hours left</span>
            </div>
            
            <button className="mt-4 w-full bg-primary hover:bg-primary-hover text-black font-medium py-2 rounded-lg transition-colors">
              Continue Learning
            </button>
          </div>
          
          <div className="bg-[#1E293B] rounded-lg p-4 md:w-80">
            <h4 className="font-bold mb-4">Weekly Streak <span className="text-primary">🔥 {weeklyStreak.current}</span></h4>
            
            <div className="flex justify-between mb-4">
              {weeklyStreak.days.map((active, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${
                    active ? 'bg-primary text-black' : 'bg-gray-800 text-gray-600'
                  }`}>
                    {active ? <FaFire /> : index + 1}
                  </div>
                  <span className="text-xs text-gray-500">
                    {['S', 'M', 'T', 'W', 'T', 'F', 'S'][index]}
                  </span>
                </div>
              ))}
            </div>
            
            <p className="text-sm text-gray-400 text-center">
              Keep your streak going! Complete at least one module today.
            </p>
          </div>
        </div>
      </div>
      
      {/* Recent Activity Section */}
      <div className="bg-[#0F172A] rounded-lg p-6">
        <h3 className="text-lg font-bold mb-4">Recent Activity</h3>
        
        <div className="space-y-4">
          {recentModules.map((module, index) => (
            <div key={index} className="bg-[#1E293B] rounded-lg p-4 flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div className={`w-10 h-10 rounded-lg ${module.completed ? 'bg-green-500/20' : 'bg-blue-500/20'} flex items-center justify-center`}>
                  {module.completed ? <FaBook className="text-green-500" /> : <FaBook className="text-blue-500" />}
                </div>
                <div>
                  <h4 className="font-medium">{module.name}</h4>
                  <p className="text-xs text-gray-400">{module.difficulty}</p>
                </div>
              </div>
              
              <div>
                {module.completed ? (
                  <span className="px-3 py-1 bg-green-500/20 text-green-500 rounded-full text-xs">Completed</span>
                ) : (
                  <button className="px-3 py-1 bg-primary text-black rounded-full text-xs font-medium">
                    Continue
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Recommendations Section */}
      <div className="bg-[#0F172A] rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold">Recommended For You</h3>
          <button className="text-primary hover:underline flex items-center gap-1 text-sm">
            View All <FaArrowRight className="text-xs" />
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-[#1E293B] rounded-lg p-4">
            <div className="flex items-center gap-4 mb-2">
              <div className="w-10 h-10 rounded-lg bg-purple-500/20 flex items-center justify-center">
                <FaCode className="text-purple-500" />
              </div>
              <h4 className="font-medium">SQL Injection Fundamentals</h4>
            </div>
            <p className="text-sm text-gray-400 mb-3">
              Learn how to exploit SQL injection vulnerabilities in web applications.
            </p>
            <div className="flex justify-between items-center">
              <span className="px-2 py-0.5 bg-yellow-500/20 text-yellow-500 rounded text-xs">Medium</span>
              <button className="text-primary hover:underline text-sm">Start Learning</button>
            </div>
          </div>
          
          <div className="bg-[#1E293B] rounded-lg p-4">
            <div className="flex items-center gap-4 mb-2">
              <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
                <FaChartLine className="text-blue-500" />
              </div>
              <h4 className="font-medium">Network Traffic Analysis</h4>
            </div>
            <p className="text-sm text-gray-400 mb-3">
              Master the art of analyzing network traffic to detect security threats.
            </p>
            <div className="flex justify-between items-center">
              <span className="px-2 py-0.5 bg-red-500/20 text-red-500 rounded text-xs">Hard</span>
              <button className="text-primary hover:underline text-sm">Start Learning</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardHome;
