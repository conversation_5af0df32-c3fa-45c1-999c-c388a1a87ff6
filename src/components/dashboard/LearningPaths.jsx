import React, { useState } from 'react';
import { FaCode, FaShieldAlt, FaServer, FaNetworkWired, FaLock, FaSearch, FaFilter, FaStar, FaArrowRight } from 'react-icons/fa';

const LearningPaths = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Mock data for learning paths
  const learningPaths = [
    {
      id: 1,
      title: 'Bug Bounty Hunter',
      description: 'Learn how to find and report security vulnerabilities in web applications.',
      category: 'offensive',
      difficulty: 'Intermediate',
      modules: 12,
      estimatedHours: 24,
      rating: 4.8,
      enrolled: true,
      progress: 35,
      image: 'bug-bounty.jpg',
      tags: ['Web Security', 'Vulnerability Assessment', 'Reporting'],
      aiMatch: 95,
    },
    {
      id: 2,
      title: 'Network Defender',
      description: 'Master the skills needed to protect networks from cyber attacks.',
      category: 'defensive',
      difficulty: 'Advanced',
      modules: 15,
      estimatedHours: 30,
      rating: 4.6,
      enrolled: false,
      progress: 0,
      image: 'network-defender.jpg',
      tags: ['Network Security', 'Firewall', 'IDS/IPS'],
      aiMatch: 82,
    },
    {
      id: 3,
      title: 'Web Application Pentester',
      description: 'Learn how to conduct thorough penetration tests on web applications.',
      category: 'offensive',
      difficulty: 'Advanced',
      modules: 18,
      estimatedHours: 36,
      rating: 4.9,
      enrolled: false,
      progress: 0,
      image: 'web-pentester.jpg',
      tags: ['Web Security', 'Penetration Testing', 'OWASP Top 10'],
      aiMatch: 88,
    },
    {
      id: 4,
      title: 'Security Operations Analyst',
      description: 'Develop the skills to detect, analyze, and respond to security incidents.',
      category: 'defensive',
      difficulty: 'Intermediate',
      modules: 14,
      estimatedHours: 28,
      rating: 4.7,
      enrolled: false,
      progress: 0,
      image: 'soc-analyst.jpg',
      tags: ['SIEM', 'Incident Response', 'Threat Hunting'],
      aiMatch: 76,
    },
  ];
  
  // Filter categories
  const categories = [
    { id: 'all', label: 'All Paths', icon: FaCode },
    { id: 'offensive', label: 'Offensive', icon: FaCode },
    { id: 'defensive', label: 'Defensive', icon: FaShieldAlt },
    { id: 'general', label: 'General', icon: FaServer },
  ];
  
  // Filter learning paths based on active filter and search query
  const filteredPaths = learningPaths.filter(path => {
    const matchesCategory = activeFilter === 'all' || path.category === activeFilter;
    const matchesSearch = path.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          path.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          path.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });
  
  // Get category icon
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'offensive':
        return <FaCode className="text-red-500" />;
      case 'defensive':
        return <FaShieldAlt className="text-blue-500" />;
      case 'general':
        return <FaServer className="text-green-500" />;
      default:
        return <FaCode className="text-primary" />;
    }
  };
  
  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Beginner':
        return 'text-green-500 bg-green-500/20';
      case 'Intermediate':
        return 'text-yellow-500 bg-yellow-500/20';
      case 'Advanced':
        return 'text-red-500 bg-red-500/20';
      default:
        return 'text-blue-500 bg-blue-500/20';
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-[#0F172A] rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-2">Learning Paths</h2>
        <p className="text-gray-400">Discover structured learning paths to master cybersecurity skills.</p>
      </div>
      
      {/* AI Recommendation Banner */}
      <div className="bg-gradient-to-r from-[#1E293B] to-[#0F172A] rounded-lg p-6 border border-primary/30">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                <FaStar className="text-primary" />
              </div>
              <h3 className="text-lg font-bold">AI-Powered Recommendations</h3>
            </div>
            <p className="text-gray-400">
              Our AI has analyzed your learning style, progress, and goals to recommend the best paths for you.
            </p>
          </div>
          <button className="bg-primary hover:bg-primary-hover text-black font-medium px-4 py-2 rounded-lg transition-colors">
            Personalize My Learning
          </button>
        </div>
      </div>
      
      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
            <input
              type="text"
              placeholder="Search learning paths..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-[#1E293B] border border-gray-700 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-primary"
            />
          </div>
        </div>
        
        <div className="flex overflow-x-auto pb-2 gap-2">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setActiveFilter(category.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                activeFilter === category.id
                  ? 'bg-primary text-black font-medium'
                  : 'bg-[#1E293B] text-gray-400 hover:bg-gray-700 hover:text-white'
              }`}
            >
              <category.icon />
              <span>{category.label}</span>
            </button>
          ))}
        </div>
      </div>
      
      {/* Learning Paths Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPaths.map(path => (
          <div key={path.id} className="bg-[#1E293B] rounded-lg overflow-hidden border border-gray-800 hover:border-primary/50 transition-colors">
            {/* Path Header */}
            <div className="h-32 bg-gradient-to-r from-[#0F172A] to-[#1E293B] relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-16 h-16 rounded-full bg-[#0F172A] border-2 border-primary flex items-center justify-center">
                  {getCategoryIcon(path.category)}
                </div>
              </div>
              
              {path.aiMatch > 85 && (
                <div className="absolute top-2 right-2 px-2 py-1 bg-primary/90 text-black rounded text-xs font-bold flex items-center gap-1">
                  <FaStar />
                  <span>AI Match: {path.aiMatch}%</span>
                </div>
              )}
            </div>
            
            {/* Path Content */}
            <div className="p-4">
              <h3 className="text-lg font-bold mb-2">{path.title}</h3>
              <p className="text-sm text-gray-400 mb-4 line-clamp-2">{path.description}</p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {path.tags.map((tag, index) => (
                  <span key={index} className="px-2 py-0.5 bg-[#0F172A] text-gray-400 rounded text-xs">
                    {tag}
                  </span>
                ))}
              </div>
              
              <div className="flex justify-between items-center mb-4">
                <span className={`px-2 py-0.5 rounded text-xs ${getDifficultyColor(path.difficulty)}`}>
                  {path.difficulty}
                </span>
                
                <div className="flex items-center gap-1">
                  <FaStar className="text-yellow-500 text-xs" />
                  <span className="text-sm">{path.rating}</span>
                </div>
              </div>
              
              <div className="flex justify-between text-sm text-gray-400 mb-4">
                <span>{path.modules} Modules</span>
                <span>~{path.estimatedHours} Hours</span>
              </div>
              
              {path.enrolled ? (
                <div>
                  <div className="w-full bg-gray-800 rounded-full h-2 mb-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${path.progress}%` }}
                    ></div>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">{path.progress}% complete</span>
                    <button className="text-primary hover:underline text-sm flex items-center gap-1">
                      Continue <FaArrowRight className="text-xs" />
                    </button>
                  </div>
                </div>
              ) : (
                <button className="w-full bg-primary hover:bg-primary-hover text-black font-medium py-2 rounded-lg transition-colors">
                  Enroll Now
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LearningPaths;
