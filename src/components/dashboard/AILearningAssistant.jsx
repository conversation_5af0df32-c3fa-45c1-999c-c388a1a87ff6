import React, { useState, useRef, useEffect } from 'react';
import { FaTimes, FaPaperPlane, FaRobot, FaUser, FaLightbulb, FaCode, FaLink, FaSpinner } from 'react-icons/fa';

const AILearningAssistant = ({ isOpen, onClose }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      role: 'assistant',
      content: "Hi there! I'm your AI learning assistant. How can I help you with your cybersecurity learning journey today?",
      timestamp: new Date(),
    }
  ]);
  
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  
  // Suggested questions
  const suggestedQuestions = [
    "Explain SQL injection in simple terms",
    "What's the difference between XSS and CSRF?",
    "How do I set up a practice environment for web hacking?",
    "What should I learn next after web application security?",
  ];
  
  // Auto-scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Handle sending a message
  const handleSendMessage = () => {
    if (inputValue.trim() === '') return;
    
    // Add user message
    const userMessage = {
      id: messages.length + 1,
      role: 'user',
      content: inputValue,
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);
    
    // Simulate AI response after a delay
    setTimeout(() => {
      const aiResponse = generateAIResponse(inputValue);
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };
  
  // Handle pressing Enter to send message
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  // Handle clicking a suggested question
  const handleSuggestedQuestion = (question) => {
    setInputValue(question);
    
    // Add user message
    const userMessage = {
      id: messages.length + 1,
      role: 'user',
      content: question,
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);
    
    // Simulate AI response after a delay
    setTimeout(() => {
      const aiResponse = generateAIResponse(question);
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };
  
  // Simple mock AI response generator
  const generateAIResponse = (userInput) => {
    const lowerInput = userInput.toLowerCase();
    let response = '';
    
    if (lowerInput.includes('sql injection')) {
      response = {
        type: 'text',
        content: "SQL Injection is a code injection technique where an attacker inserts malicious SQL code into input fields that are later passed to a database query. This can allow attackers to view, modify, or delete data they shouldn't have access to.",
      };
    } else if (lowerInput.includes('xss') && lowerInput.includes('csrf')) {
      response = {
        type: 'comparison',
        content: {
          title: "XSS vs CSRF: Key Differences",
          items: [
            {
              term: "Cross-Site Scripting (XSS)",
              definition: "Attacker injects malicious scripts into websites viewed by other users. The script runs in the victim's browser with their privileges."
            },
            {
              term: "Cross-Site Request Forgery (CSRF)",
              definition: "Tricks the victim into submitting a malicious request to a website where they're authenticated. The attack leverages the victim's authenticated session."
            }
          ]
        }
      };
    } else if (lowerInput.includes('practice environment') || lowerInput.includes('set up')) {
      response = {
        type: 'steps',
        content: {
          title: "Setting Up a Web Hacking Practice Environment",
          steps: [
            "Install a virtual machine software like VirtualBox or VMware",
            "Set up OWASP WebGoat or DVWA (Damn Vulnerable Web Application)",
            "Consider using Kali Linux as your attack platform",
            "Use Burp Suite Community Edition as your proxy tool",
            "Join platforms like HackTheBox or TryHackMe for structured practice"
          ]
        }
      };
    } else if (lowerInput.includes('learn next') || lowerInput.includes('after web')) {
      response = {
        type: 'recommendations',
        content: {
          title: "After Web Application Security",
          paths: [
            {
              name: "Network Penetration Testing",
              description: "Learn how to test network infrastructure for vulnerabilities"
            },
            {
              name: "Mobile Application Security",
              description: "Explore security issues in Android and iOS applications"
            },
            {
              name: "API Security",
              description: "Focus on securing and testing APIs"
            },
            {
              name: "Cloud Security",
              description: "Learn about securing AWS, Azure, or GCP environments"
            }
          ]
        }
      };
    } else {
      response = {
        type: 'text',
        content: "I understand you're asking about " + userInput + ". This is an interesting topic in cybersecurity. Would you like me to provide more specific information or resources about this? Feel free to ask more detailed questions so I can better assist you with your learning journey.",
      };
    }
    
    return {
      id: messages.length + 2,
      role: 'assistant',
      content: response,
      timestamp: new Date(),
    };
  };
  
  // Render message content based on type
  const renderMessageContent = (message) => {
    if (message.role === 'user' || typeof message.content === 'string') {
      return <p className="whitespace-pre-wrap">{message.content}</p>;
    }
    
    const content = message.content;
    
    switch (content.type) {
      case 'text':
        return <p className="whitespace-pre-wrap">{content.content}</p>;
        
      case 'comparison':
        return (
          <div>
            <h4 className="font-bold mb-2">{content.content.title}</h4>
            <div className="space-y-3">
              {content.content.items.map((item, index) => (
                <div key={index} className="bg-[#0F172A] p-3 rounded-lg">
                  <div className="font-bold text-primary">{item.term}</div>
                  <div className="text-sm mt-1">{item.definition}</div>
                </div>
              ))}
            </div>
          </div>
        );
        
      case 'steps':
        return (
          <div>
            <h4 className="font-bold mb-2">{content.content.title}</h4>
            <ol className="list-decimal list-inside space-y-2">
              {content.content.steps.map((step, index) => (
                <li key={index} className="pl-2">{step}</li>
              ))}
            </ol>
          </div>
        );
        
      case 'recommendations':
        return (
          <div>
            <h4 className="font-bold mb-2">{content.content.title}</h4>
            <div className="space-y-2">
              {content.content.paths.map((path, index) => (
                <div key={index} className="bg-[#0F172A] p-3 rounded-lg">
                  <div className="font-bold">{path.name}</div>
                  <div className="text-sm text-gray-400 mt-1">{path.description}</div>
                </div>
              ))}
            </div>
          </div>
        );
        
      default:
        return <p>Unsupported message type</p>;
    }
  };
  
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
      <div className="bg-[#1E293B] w-full max-w-2xl rounded-lg shadow-xl flex flex-col h-[80vh] mx-4">
        {/* Header */}
        <div className="p-4 border-b border-gray-800 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
              <FaRobot className="text-primary" />
            </div>
            <h3 className="font-bold">AI Learning Assistant</h3>
          </div>
          
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FaTimes />
          </button>
        </div>
        
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map(message => (
            <div 
              key={message.id} 
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div 
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === 'user' 
                    ? 'bg-primary text-black' 
                    : 'bg-[#0F172A]'
                }`}
              >
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-6 h-6 rounded-full bg-gray-800 flex items-center justify-center">
                    {message.role === 'user' 
                      ? <FaUser className="text-xs" /> 
                      : <FaRobot className="text-xs text-primary" />
                    }
                  </div>
                  <span className="text-xs opacity-75">
                    {message.role === 'user' ? 'You' : 'AI Assistant'}
                  </span>
                </div>
                
                <div className={`${message.role === 'user' ? '' : 'text-gray-200'}`}>
                  {typeof message.content === 'string' 
                    ? <p className="whitespace-pre-wrap">{message.content}</p>
                    : renderMessageContent(message)
                  }
                </div>
                
                <div className="text-right text-xs opacity-50 mt-1">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className="flex justify-start">
              <div className="max-w-[80%] rounded-lg p-3 bg-[#0F172A]">
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-6 h-6 rounded-full bg-gray-800 flex items-center justify-center">
                    <FaRobot className="text-xs text-primary" />
                  </div>
                  <span className="text-xs opacity-75">AI Assistant</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <FaSpinner className="animate-spin text-primary" />
                  <span>Thinking...</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        {/* Suggested Questions */}
        {messages.length < 3 && (
          <div className="p-4 border-t border-gray-800">
            <div className="text-sm text-gray-400 mb-2 flex items-center gap-2">
              <FaLightbulb className="text-primary" />
              <span>Suggested questions:</span>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {suggestedQuestions.map((question, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestedQuestion(question)}
                  className="bg-[#0F172A] hover:bg-gray-800 text-sm px-3 py-1.5 rounded-full transition-colors"
                >
                  {question}
                </button>
              ))}
            </div>
          </div>
        )}
        
        {/* Input */}
        <div className="p-4 border-t border-gray-800">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask me anything about cybersecurity..."
                className="w-full bg-[#0F172A] border border-gray-700 rounded-lg px-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-primary resize-none h-12 max-h-32"
                rows={1}
              />
              
              <div className="absolute right-2 bottom-2 flex gap-2">
                <button className="text-gray-400 hover:text-white transition-colors">
                  <FaCode />
                </button>
                <button className="text-gray-400 hover:text-white transition-colors">
                  <FaLink />
                </button>
              </div>
            </div>
            
            <button
              onClick={handleSendMessage}
              disabled={inputValue.trim() === ''}
              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                inputValue.trim() === ''
                  ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                  : 'bg-primary text-black hover:bg-primary-hover'
              } transition-colors`}
            >
              <FaPaperPlane className="text-sm" />
            </button>
          </div>
          
          <div className="text-xs text-gray-500 mt-2 text-center">
            AI responses are generated based on your learning progress and may not always be accurate.
          </div>
        </div>
      </div>
    </div>
  );
};

export default AILearningAssistant;
