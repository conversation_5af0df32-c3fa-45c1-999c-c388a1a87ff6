import React, { useState } from 'react';
import { FaChartLine, FaCalendarAlt, FaFire, FaTrophy, FaCertificate, FaChevronDown, FaChevronUp, FaCode, FaShieldAlt, FaServer, FaNetworkWired, FaLock } from 'react-icons/fa';

const ProgressTracker = () => {
  const [activeTimeframe, setActiveTimeframe] = useState('month');
  const [expandedSkill, setExpandedSkill] = useState(null);
  
  // Mock data for progress tracking
  const progressData = {
    overview: {
      totalXP: 2450,
      level: 8,
      nextLevelXP: 3000,
      streak: 12,
      completedModules: 24,
      completedPaths: 1,
      hoursSpent: 42,
    },
    skills: [
      {
        name: 'Web Security',
        icon: FaCode,
        color: '#ec4899',
        level: 7,
        progress: 70,
        recentActivity: [
          { date: '2023-06-01', xp: 120, modules: ['XSS Fundamentals', 'CSRF Protection'] },
          { date: '2023-06-03', xp: 80, modules: ['SQL Injection Advanced'] },
          { date: '2023-06-07', xp: 150, modules: ['Web Cache Poisoning', 'JWT Attacks'] },
        ],
      },
      {
        name: 'Network Security',
        icon: FaNetworkWired,
        color: '#3b82f6',
        level: 5,
        progress: 50,
        recentActivity: [
          { date: '2023-06-02', xp: 100, modules: ['Wireshark Basics', 'Network Protocols'] },
          { date: '2023-06-05', xp: 75, modules: ['Firewall Configuration'] },
        ],
      },
      {
        name: 'Cryptography',
        icon: FaLock,
        color: '#f59e0b',
        level: 3,
        progress: 30,
        recentActivity: [
          { date: '2023-06-04', xp: 60, modules: ['Encryption Basics'] },
        ],
      },
      {
        name: 'System Security',
        icon: FaServer,
        color: '#10b981',
        level: 4,
        progress: 40,
        recentActivity: [
          { date: '2023-06-06', xp: 90, modules: ['Linux Hardening', 'Privilege Escalation'] },
        ],
      },
      {
        name: 'Defensive Security',
        icon: FaShieldAlt,
        color: '#8b5cf6',
        level: 6,
        progress: 60,
        recentActivity: [
          { date: '2023-06-08', xp: 110, modules: ['SIEM Basics', 'Log Analysis'] },
        ],
      },
    ],
    achievements: [
      { name: 'First Blood', description: 'Complete your first module', date: '2023-05-15', icon: FaTrophy },
      { name: 'Web Warrior', description: 'Complete 10 web security modules', date: '2023-05-28', icon: FaCode },
      { name: 'Consistent Learner', description: 'Maintain a 7-day streak', date: '2023-06-01', icon: FaFire },
    ],
    certificates: [
      { name: 'Web Application Security Fundamentals', issueDate: '2023-06-01', id: 'CERT-WAS-123' },
    ],
  };
  
  // Time frames for filtering
  const timeframes = [
    { id: 'week', label: 'This Week' },
    { id: 'month', label: 'This Month' },
    { id: 'year', label: 'This Year' },
    { id: 'all', label: 'All Time' },
  ];
  
  // Toggle skill details
  const toggleSkillDetails = (index) => {
    if (expandedSkill === index) {
      setExpandedSkill(null);
    } else {
      setExpandedSkill(index);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-[#0F172A] rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-2">Your Progress</h2>
        <p className="text-gray-400">Track your learning journey and skill development.</p>
      </div>
      
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-[#0F172A] rounded-lg p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-primary/20 flex items-center justify-center">
              <FaChartLine className="text-primary text-xl" />
            </div>
            <div>
              <h3 className="text-lg font-bold">{progressData.overview.totalXP} XP</h3>
              <p className="text-sm text-gray-400">Total Experience</p>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="flex justify-between text-sm mb-1">
              <span>Level {progressData.overview.level}</span>
              <span>Level {progressData.overview.level + 1}</span>
            </div>
            <div className="w-full bg-gray-800 rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full" 
                style={{ width: `${(progressData.overview.totalXP / progressData.overview.nextLevelXP) * 100}%` }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 mt-1 text-right">
              {progressData.overview.nextLevelXP - progressData.overview.totalXP} XP to next level
            </p>
          </div>
        </div>
        
        <div className="bg-[#0F172A] rounded-lg p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-blue-500/20 flex items-center justify-center">
              <FaCalendarAlt className="text-blue-500 text-xl" />
            </div>
            <div>
              <h3 className="text-lg font-bold">{progressData.overview.hoursSpent} hours</h3>
              <p className="text-sm text-gray-400">Learning Time</p>
            </div>
          </div>
          
          <div className="mt-4 flex justify-between">
            <div className="text-center">
              <p className="text-2xl font-bold">{progressData.overview.completedModules}</p>
              <p className="text-xs text-gray-400">Modules</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{progressData.overview.completedPaths}</p>
              <p className="text-xs text-gray-400">Paths</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">{progressData.overview.streak}</p>
              <p className="text-xs text-gray-400">Day Streak</p>
            </div>
          </div>
        </div>
        
        <div className="bg-[#0F172A] rounded-lg p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-purple-500/20 flex items-center justify-center">
              <FaTrophy className="text-purple-500 text-xl" />
            </div>
            <div>
              <h3 className="text-lg font-bold">{progressData.achievements.length}</h3>
              <p className="text-sm text-gray-400">Achievements</p>
            </div>
          </div>
          
          <div className="mt-4 space-y-2">
            {progressData.achievements.slice(0, 2).map((achievement, index) => (
              <div key={index} className="flex items-center gap-2">
                <achievement.icon className="text-yellow-500 text-sm" />
                <p className="text-sm truncate">{achievement.name}</p>
              </div>
            ))}
            
            {progressData.achievements.length > 2 && (
              <p className="text-xs text-primary hover:underline cursor-pointer">
                +{progressData.achievements.length - 2} more achievements
              </p>
            )}
          </div>
        </div>
        
        <div className="bg-[#0F172A] rounded-lg p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-green-500/20 flex items-center justify-center">
              <FaCertificate className="text-green-500 text-xl" />
            </div>
            <div>
              <h3 className="text-lg font-bold">{progressData.certificates.length}</h3>
              <p className="text-sm text-gray-400">Certificates</p>
            </div>
          </div>
          
          <div className="mt-4 space-y-2">
            {progressData.certificates.map((certificate, index) => (
              <div key={index} className="bg-[#1E293B] p-2 rounded text-sm">
                <p className="font-medium truncate">{certificate.name}</p>
                <p className="text-xs text-gray-400">Issued: {new Date(certificate.issueDate).toLocaleDateString()}</p>
              </div>
            ))}
            
            {progressData.certificates.length === 0 && (
              <p className="text-sm text-gray-400">Complete learning paths to earn certificates</p>
            )}
          </div>
        </div>
      </div>
      
      {/* Skills Progress */}
      <div className="bg-[#0F172A] rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-bold">Skills Progress</h3>
          
          <div className="flex bg-[#1E293B] rounded-lg overflow-hidden">
            {timeframes.map(timeframe => (
              <button
                key={timeframe.id}
                onClick={() => setActiveTimeframe(timeframe.id)}
                className={`px-3 py-1.5 text-sm transition-colors ${
                  activeTimeframe === timeframe.id
                    ? 'bg-primary text-black font-medium'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                {timeframe.label}
              </button>
            ))}
          </div>
        </div>
        
        <div className="space-y-4">
          {progressData.skills.map((skill, index) => (
            <div key={index} className="bg-[#1E293B] rounded-lg overflow-hidden">
              <div 
                className="p-4 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSkillDetails(index)}
              >
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: `${skill.color}20` }}>
                    <skill.icon style={{ color: skill.color }} />
                  </div>
                  
                  <div>
                    <h4 className="font-bold">{skill.name}</h4>
                    <p className="text-xs text-gray-400">Level {skill.level}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-32">
                    <div className="w-full bg-gray-800 rounded-full h-2">
                      <div 
                        className="h-2 rounded-full" 
                        style={{ width: `${skill.progress}%`, backgroundColor: skill.color }}
                      ></div>
                    </div>
                    <p className="text-xs text-right mt-1">{skill.progress}%</p>
                  </div>
                  
                  {expandedSkill === index ? <FaChevronUp className="text-gray-400" /> : <FaChevronDown className="text-gray-400" />}
                </div>
              </div>
              
              {expandedSkill === index && (
                <div className="p-4 border-t border-gray-800">
                  <h5 className="font-medium text-sm mb-3">Recent Activity</h5>
                  
                  {skill.recentActivity.length > 0 ? (
                    <div className="space-y-3">
                      {skill.recentActivity.map((activity, actIndex) => (
                        <div key={actIndex} className="bg-[#0F172A] p-3 rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm">{new Date(activity.date).toLocaleDateString()}</span>
                            <span className="text-sm font-bold text-primary">+{activity.xp} XP</span>
                          </div>
                          
                          <div className="space-y-1">
                            {activity.modules.map((module, modIndex) => (
                              <div key={modIndex} className="text-sm flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                <span>{module}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-400">No recent activity for this skill</p>
                  )}
                  
                  <div className="mt-4 flex justify-end">
                    <button className="text-primary hover:underline text-sm">View Skill Details</button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
      
      {/* Learning Recommendations */}
      <div className="bg-[#0F172A] rounded-lg p-6">
        <h3 className="text-lg font-bold mb-4">Recommended Next Steps</h3>
        
        <div className="space-y-4">
          <div className="bg-[#1E293B] p-4 rounded-lg border-l-4 border-primary">
            <h4 className="font-bold mb-1">Continue Your Current Path</h4>
            <p className="text-sm text-gray-400 mb-3">You're making great progress in the Bug Bounty Hunter path. Keep going!</p>
            <button className="bg-primary hover:bg-primary-hover text-black font-medium px-4 py-2 rounded-lg transition-colors text-sm">
              Continue Learning
            </button>
          </div>
          
          <div className="bg-[#1E293B] p-4 rounded-lg border-l-4 border-blue-500">
            <h4 className="font-bold mb-1">Improve Your Network Security Skills</h4>
            <p className="text-sm text-gray-400 mb-3">Based on your progress, we recommend focusing on network security next.</p>
            <button className="bg-[#0F172A] hover:bg-gray-800 text-white px-4 py-2 rounded-lg transition-colors text-sm">
              Explore Network Security Modules
            </button>
          </div>
          
          <div className="bg-[#1E293B] p-4 rounded-lg border-l-4 border-purple-500">
            <h4 className="font-bold mb-1">Join the Weekly Challenge</h4>
            <p className="text-sm text-gray-400 mb-3">Test your skills and earn bonus XP by participating in this week's challenge.</p>
            <button className="bg-[#0F172A] hover:bg-gray-800 text-white px-4 py-2 rounded-lg transition-colors text-sm">
              View Challenge
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;
