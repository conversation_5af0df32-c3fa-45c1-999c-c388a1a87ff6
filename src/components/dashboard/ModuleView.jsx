import React, { useState } from 'react';
import { FaArrowLeft, FaArrowRight, FaCode, FaLightbulb, FaExclamationTriangle, FaCheck, FaBookmark, FaRegBookmark, FaPlay } from 'react-icons/fa';

const ModuleView = () => {
  const [currentSection, setCurrentSection] = useState(0);
  const [bookmarked, setBookmarked] = useState(false);
  const [showHint, setShowHint] = useState(false);
  
  // Mock data for a module
  const module = {
    title: 'Introduction to Web Applications',
    path: 'Bug Bounty Hunter',
    sections: [
      {
        title: 'Web Application Architecture',
        content: `
          <h2>Understanding Web Application Architecture</h2>
          <p>Web applications are complex systems that involve multiple components working together to deliver functionality to users. The basic architecture includes:</p>
          <ul>
            <li><strong>Client-side (Frontend):</strong> The user interface that runs in the browser</li>
            <li><strong>Server-side (Backend):</strong> The application logic and data processing</li>
            <li><strong>Database:</strong> Where application data is stored and retrieved</li>
          </ul>
          <p>Understanding how these components interact is essential for identifying security vulnerabilities.</p>
          
          <div class="bg-[#0F172A] p-4 rounded-lg my-4">
            <h3 class="font-bold mb-2">Key Concept: Client-Server Model</h3>
            <p>In the client-server model, the client (browser) sends requests to the server, which processes these requests and returns responses. This communication typically happens over HTTP or HTTPS protocols.</p>
          </div>
          
          <p>From a security perspective, each component presents different attack surfaces and potential vulnerabilities.</p>
        `,
        type: 'text',
      },
      {
        title: 'HTTP Protocol Basics',
        content: `
          <h2>HTTP Protocol: The Foundation of Web Communication</h2>
          <p>HTTP (Hypertext Transfer Protocol) is the foundation of data communication on the web. Understanding HTTP is crucial for web application security testing.</p>
          
          <h3>HTTP Request Structure</h3>
          <p>An HTTP request consists of:</p>
          <ul>
            <li><strong>Request line:</strong> Method, URL, and HTTP version</li>
            <li><strong>Headers:</strong> Additional information about the request</li>
            <li><strong>Body:</strong> Optional data sent to the server</li>
          </ul>
          
          <div class="bg-[#0F172A] p-4 rounded-lg my-4 font-mono text-sm">
            <p>GET /index.html HTTP/1.1</p>
            <p>Host: example.com</p>
            <p>User-Agent: Mozilla/5.0</p>
            <p>Accept: text/html</p>
          </div>
          
          <h3>HTTP Response Structure</h3>
          <p>An HTTP response consists of:</p>
          <ul>
            <li><strong>Status line:</strong> HTTP version, status code, and reason phrase</li>
            <li><strong>Headers:</strong> Additional information about the response</li>
            <li><strong>Body:</strong> The requested resource or error message</li>
          </ul>
          
          <div class="bg-[#0F172A] p-4 rounded-lg my-4 font-mono text-sm">
            <p>HTTP/1.1 200 OK</p>
            <p>Content-Type: text/html</p>
            <p>Content-Length: 1234</p>
            <p>Server: Apache</p>
            <p></p>
            <p>&lt;html&gt;...&lt;/html&gt;</p>
          </div>
          
          <p>Security vulnerabilities often arise from improper handling of HTTP requests and responses.</p>
        `,
        type: 'text',
      },
      {
        title: 'Web Technologies Overview',
        content: `
          <h2>Modern Web Technologies</h2>
          <p>Web applications use various technologies on both client and server sides:</p>
          
          <h3>Client-side Technologies</h3>
          <ul>
            <li><strong>HTML:</strong> Structure of web pages</li>
            <li><strong>CSS:</strong> Styling and layout</li>
            <li><strong>JavaScript:</strong> Client-side functionality and interactivity</li>
            <li><strong>Frontend Frameworks:</strong> React, Angular, Vue.js, etc.</li>
          </ul>
          
          <h3>Server-side Technologies</h3>
          <ul>
            <li><strong>Languages:</strong> PHP, Python, Ruby, Java, Node.js, etc.</li>
            <li><strong>Frameworks:</strong> Laravel, Django, Ruby on Rails, Spring, Express, etc.</li>
            <li><strong>Databases:</strong> MySQL, PostgreSQL, MongoDB, etc.</li>
          </ul>
          
          <div class="bg-yellow-500/20 p-4 rounded-lg my-4 border-l-4 border-yellow-500">
            <h3 class="font-bold text-yellow-500 mb-2">Security Consideration</h3>
            <p>Each technology stack has its own security considerations and potential vulnerabilities. Understanding the technologies used in a web application helps identify potential security issues.</p>
          </div>
          
          <p>As a bug bounty hunter, you'll need to adapt your testing approach based on the technologies used in the target application.</p>
        `,
        type: 'text',
      },
      {
        title: 'Interactive Demo: HTTP Requests',
        content: {
          description: `
            <h2>Interactive HTTP Request Demo</h2>
            <p>In this interactive demo, you'll see how HTTP requests and responses work in practice. You can modify the request parameters and see how the response changes.</p>
            <p>Click the "Run Demo" button to start the interactive session.</p>
          `,
          codeExample: `
            // Example HTTP GET request using JavaScript
            fetch('https://api.example.com/data', {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Authorization': 'Bearer token123'
              }
            })
            .then(response => response.json())
            .then(data => console.log(data))
            .catch(error => console.error('Error:', error));
          `
        },
        type: 'interactive',
      },
      {
        title: 'Knowledge Check',
        content: {
          questions: [
            {
              question: 'Which component of a web application is responsible for storing data?',
              options: [
                'Frontend',
                'Backend',
                'Database',
                'API'
              ],
              correctAnswer: 2,
              explanation: 'The database is responsible for storing and retrieving application data. The frontend displays the data, the backend processes it, and APIs facilitate communication between components.'
            },
            {
              question: 'Which HTTP method is typically used to submit form data to a server?',
              options: [
                'GET',
                'POST',
                'PUT',
                'DELETE'
              ],
              correctAnswer: 1,
              explanation: 'POST is typically used to submit form data to a server. GET is used to request data, PUT is used to update existing resources, and DELETE is used to remove resources.'
            },
            {
              question: 'Which of the following is NOT a client-side technology?',
              options: [
                'HTML',
                'CSS',
                'JavaScript',
                'PHP'
              ],
              correctAnswer: 3,
              explanation: 'PHP is a server-side scripting language. HTML, CSS, and JavaScript are all client-side technologies that run in the browser.'
            }
          ]
        },
        type: 'quiz',
      }
    ],
    progress: 0,
    estimatedTime: 25, // minutes
  };
  
  const currentSectionData = module.sections[currentSection];
  
  // Handle navigation between sections
  const goToNextSection = () => {
    if (currentSection < module.sections.length - 1) {
      setCurrentSection(currentSection + 1);
      setShowHint(false);
    }
  };
  
  const goToPreviousSection = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
      setShowHint(false);
    }
  };
  
  // Render content based on section type
  const renderSectionContent = () => {
    switch (currentSectionData.type) {
      case 'text':
        return (
          <div 
            className="prose prose-invert max-w-none"
            dangerouslySetInnerHTML={{ __html: currentSectionData.content }}
          />
        );
      
      case 'interactive':
        return (
          <div>
            <div 
              className="prose prose-invert max-w-none mb-6"
              dangerouslySetInnerHTML={{ __html: currentSectionData.content.description }}
            />
            
            <div className="bg-[#0F172A] p-4 rounded-lg mb-6 font-mono text-sm overflow-x-auto">
              <pre>{currentSectionData.content.codeExample}</pre>
            </div>
            
            <button className="bg-primary hover:bg-primary-hover text-black font-medium px-4 py-2 rounded-lg transition-colors flex items-center gap-2">
              <FaPlay />
              Run Demo
            </button>
          </div>
        );
      
      case 'quiz':
        return (
          <div>
            <h2 className="text-xl font-bold mb-4">Knowledge Check</h2>
            <p className="text-gray-400 mb-6">Test your understanding of the concepts covered in this module.</p>
            
            <div className="space-y-8">
              {currentSectionData.content.questions.map((q, qIndex) => (
                <div key={qIndex} className="bg-[#0F172A] p-6 rounded-lg">
                  <h3 className="font-bold mb-4">{qIndex + 1}. {q.question}</h3>
                  
                  <div className="space-y-3 mb-4">
                    {q.options.map((option, oIndex) => (
                      <div key={oIndex} className="flex items-center gap-3">
                        <div className="w-6 h-6 rounded-full border border-gray-600 flex items-center justify-center">
                          {oIndex === q.correctAnswer ? (
                            <div className="w-3 h-3 rounded-full bg-primary"></div>
                          ) : null}
                        </div>
                        <span>{option}</span>
                      </div>
                    ))}
                  </div>
                  
                  {showHint && (
                    <div className="bg-primary/10 border border-primary/30 p-4 rounded-lg mt-4">
                      <div className="flex items-center gap-2 text-primary font-bold mb-2">
                        <FaLightbulb />
                        <span>Explanation</span>
                      </div>
                      <p>{q.explanation}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            <div className="flex justify-center mt-6">
              <button 
                onClick={() => setShowHint(!showHint)}
                className="text-primary hover:underline flex items-center gap-2"
              >
                <FaLightbulb />
                {showHint ? 'Hide Explanation' : 'Show Explanation'}
              </button>
            </div>
          </div>
        );
      
      default:
        return <p>Content not available</p>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Module Header */}
      <div className="bg-[#0F172A] rounded-lg p-6">
        <div className="flex items-center gap-2 text-gray-400 text-sm mb-2">
          <span>{module.path}</span>
          <span>•</span>
          <span>Module {currentSection + 1} of {module.sections.length}</span>
        </div>
        
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">{module.title}</h2>
          
          <button 
            onClick={() => setBookmarked(!bookmarked)}
            className="text-gray-400 hover:text-primary transition-colors"
          >
            {bookmarked ? <FaBookmark className="text-primary" /> : <FaRegBookmark />}
          </button>
        </div>
      </div>
      
      {/* Progress Bar */}
      <div className="bg-[#0F172A] rounded-lg p-4">
        <div className="flex justify-between text-sm text-gray-400 mb-2">
          <span>Progress</span>
          <span>{Math.round((currentSection / (module.sections.length - 1)) * 100)}%</span>
        </div>
        
        <div className="w-full bg-gray-800 rounded-full h-2">
          <div 
            className="bg-primary h-2 rounded-full transition-all duration-300" 
            style={{ width: `${(currentSection / (module.sections.length - 1)) * 100}%` }}
          ></div>
        </div>
        
        <div className="flex justify-between text-xs text-gray-500 mt-2">
          <span>Estimated time: {module.estimatedTime} minutes</span>
          <span>Section {currentSection + 1} of {module.sections.length}</span>
        </div>
      </div>
      
      {/* Section Navigation */}
      <div className="bg-[#0F172A] rounded-lg p-4">
        <div className="flex overflow-x-auto gap-2 pb-2">
          {module.sections.map((section, index) => (
            <button
              key={index}
              onClick={() => setCurrentSection(index)}
              className={`px-3 py-1.5 rounded-lg text-sm whitespace-nowrap transition-colors ${
                currentSection === index
                  ? 'bg-primary text-black font-medium'
                  : index < currentSection
                    ? 'bg-green-500/20 text-green-500'
                    : 'bg-[#1E293B] text-gray-400 hover:bg-gray-700 hover:text-white'
              }`}
            >
              {index < currentSection && <FaCheck className="inline mr-1 text-xs" />}
              {section.title}
            </button>
          ))}
        </div>
      </div>
      
      {/* Section Content */}
      <div className="bg-[#0F172A] rounded-lg p-6">
        <h3 className="text-xl font-bold mb-6">{currentSectionData.title}</h3>
        
        {renderSectionContent()}
      </div>
      
      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <button
          onClick={goToPreviousSection}
          disabled={currentSection === 0}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            currentSection === 0
              ? 'bg-gray-800 text-gray-600 cursor-not-allowed'
              : 'bg-[#1E293B] text-white hover:bg-gray-700'
          }`}
        >
          <FaArrowLeft />
          Previous
        </button>
        
        <button
          onClick={goToNextSection}
          disabled={currentSection === module.sections.length - 1}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            currentSection === module.sections.length - 1
              ? 'bg-gray-800 text-gray-600 cursor-not-allowed'
              : 'bg-primary hover:bg-primary-hover text-black font-medium'
          }`}
        >
          Next
          <FaArrowRight />
        </button>
      </div>
      
      {/* AI Assistant */}
      <div className="fixed bottom-6 right-6">
        <button className="w-12 h-12 rounded-full bg-primary text-black flex items-center justify-center shadow-lg hover:bg-primary-hover transition-colors">
          <FaLightbulb className="text-xl" />
        </button>
      </div>
    </div>
  );
};

export default ModuleView;
