import React from 'react';
import { Link } from 'react-router-dom';
import { FaArrowRight } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

const SecurityTrainingOptions = () => {
  const { darkMode } = useGlobalTheme();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Offensive Security Card */}
      <div className={`${darkMode ? 'bg-[#0F1729]' : 'bg-gray-900'} rounded-lg overflow-hidden border border-red-600`}>
        <div className="p-4 border-b border-red-600 flex items-center">
          <svg className="w-6 h-6 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 17l-5-5m0 0l5-5m-5 5h12" />
          </svg>
          <h3 className="text-xl font-bold text-red-500">Offensive Security</h3>
        </div>

        <div className="p-6">
          {/* Grid Background with Visualization */}
          <div className="relative h-[150px] mb-6 overflow-hidden rounded-lg">
            <div className="absolute inset-0 bg-black">
              <div className="absolute inset-0 opacity-80">
                <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <pattern id="smallGrid" width="8" height="8" patternUnits="userSpaceOnUse">
                      <path d="M 8 0 L 0 0 0 8" fill="none" stroke="rgba(255,255,255,0.1)" strokeWidth="0.5" />
                    </pattern>
                    <pattern id="grid" width="80" height="80" patternUnits="userSpaceOnUse">
                      <rect width="80" height="80" fill="url(#smallGrid)" />
                      <path d="M 80 0 L 0 0 0 80" fill="none" stroke="rgba(255,255,255,0.2)" strokeWidth="1" />
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid)" />
                </svg>
              </div>

              {/* Exploit Visualization */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="flex space-x-16">
                  {/* Exploit Icon */}
                  <div className="w-12 h-12 bg-red-900/50 flex items-center justify-center rounded">
                    <svg className="w-6 h-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>

                  {/* Server Icon */}
                  <div className="w-12 h-12 bg-blue-900/50 flex items-center justify-center rounded">
                    <svg className="w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                    </svg>
                  </div>
                </div>

                {/* Attack Path Animation */}
                <svg className="absolute inset-0" width="100%" height="100%" viewBox="0 0 300 150">
                  <path
                    d="M 100 75 L 200 75"
                    stroke="rgba(239, 68, 68, 0.5)"
                    strokeWidth="2"
                    strokeDasharray="6,3"
                    className="animate-dash-slow"
                  />
                </svg>
              </div>
            </div>
          </div>

          <p className="text-gray-300 mb-6">
            Learn ethical hacking techniques to identify and exploit vulnerabilities in systems, networks, and applications. Our offensive security simulations teach you to think like an attacker to better defend your systems.
          </p>

          <div className="space-y-3 mb-6">
            <div className="flex items-center">
              <div className="w-5 h-5 rounded-full bg-red-500/10 flex items-center justify-center mr-2">
                <span className="text-red-500 text-xs">✓</span>
              </div>
              <span className="text-gray-300 text-sm">Penetration Testing</span>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 rounded-full bg-red-500/10 flex items-center justify-center mr-2">
                <span className="text-red-500 text-xs">✓</span>
              </div>
              <span className="text-gray-300 text-sm">Vulnerability Assessment</span>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 rounded-full bg-red-500/10 flex items-center justify-center mr-2">
                <span className="text-red-500 text-xs">✓</span>
              </div>
              <span className="text-gray-300 text-sm">Exploit Development</span>
            </div>
          </div>

          <Link to="/simulations/offensive" className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 inline-flex items-center">
            Try offensive simulations <FaArrowRight className="ml-2" />
          </Link>
        </div>
      </div>

      {/* Defensive Security Card */}
      <div className={`${darkMode ? 'bg-[#0F1729]' : 'bg-gray-900'} rounded-lg overflow-hidden border border-blue-600`}>
        <div className="p-4 border-b border-blue-600 flex items-center">
          <svg className="w-6 h-6 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          <h3 className="text-xl font-bold text-blue-500">Defensive Security</h3>
        </div>

        <div className="p-6">
          {/* Grid Background with Visualization */}
          <div className="relative h-[150px] mb-6 overflow-hidden rounded-lg">
            <div className="absolute inset-0 bg-black">
              <div className="absolute inset-0 opacity-80">
                <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <pattern id="smallGridDef" width="8" height="8" patternUnits="userSpaceOnUse">
                      <path d="M 8 0 L 0 0 0 8" fill="none" stroke="rgba(255,255,255,0.1)" strokeWidth="0.5" />
                    </pattern>
                    <pattern id="gridDef" width="80" height="80" patternUnits="userSpaceOnUse">
                      <rect width="80" height="80" fill="url(#smallGridDef)" />
                      <path d="M 80 0 L 0 0 0 80" fill="none" stroke="rgba(255,255,255,0.2)" strokeWidth="1" />
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#gridDef)" />
                </svg>
              </div>

              {/* Defense Visualization */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="flex space-x-8">
                  {/* Firewall Icon */}
                  <div className="w-12 h-12 bg-red-900/50 flex items-center justify-center rounded">
                    <svg className="w-6 h-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>

                  {/* Shield Icon */}
                  <div className="w-12 h-12 bg-green-900/50 flex items-center justify-center rounded">
                    <svg className="w-6 h-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>

                  {/* Encryption Icon */}
                  <div className="w-12 h-12 bg-blue-900/50 flex items-center justify-center rounded">
                    <svg className="w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                </div>

                {/* Labels */}
                <div className="absolute bottom-2 left-1/4 transform -translate-x-1/2 text-xs text-red-400">Firewall</div>
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-xs text-green-400">IDS/IPS</div>
                <div className="absolute bottom-2 right-1/4 transform translate-x-1/2 text-xs text-blue-400">Encryption</div>
              </div>
            </div>
          </div>

          <p className="text-gray-300 mb-6">
            Master the art of protecting systems and networks from cyber threats. Our defensive security simulations teach you to detect, analyze, and respond to attacks in real-time.
          </p>

          <div className="space-y-3 mb-6">
            <div className="flex items-center">
              <div className="w-5 h-5 rounded-full bg-blue-500/10 flex items-center justify-center mr-2">
                <span className="text-blue-500 text-xs">✓</span>
              </div>
              <span className="text-gray-300 text-sm">Security Operations</span>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 rounded-full bg-blue-500/10 flex items-center justify-center mr-2">
                <span className="text-blue-500 text-xs">✓</span>
              </div>
              <span className="text-gray-300 text-sm">Incident Response</span>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 rounded-full bg-blue-500/10 flex items-center justify-center mr-2">
                <span className="text-blue-500 text-xs">✓</span>
              </div>
              <span className="text-gray-300 text-sm">Threat Hunting</span>
            </div>
          </div>

          <Link to="/simulations/defensive" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 inline-flex items-center">
            Try defensive simulations <FaArrowRight className="ml-2" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SecurityTrainingOptions;
