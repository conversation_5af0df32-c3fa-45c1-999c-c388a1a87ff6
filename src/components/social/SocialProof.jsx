import React, { useState, useEffect } from 'react';
import { FaUsers, FaTrophy, FaGraduationCap, FaUserAstronaut } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

// Mock data for social proof
const MOCK_DATA = {
  activeUsers: {
    min: 120,
    max: 250,
  },
  completedChallenges: {
    min: 1500,
    max: 2200,
  },
  completedModules: {
    min: 3200,
    max: 4500,
  },
  leaderboard: [
    { username: '<PERSON>ber<PERSON>in<PERSON>', points: 12500, avatar: null },
    { username: 'HackMaster', points: 11200, avatar: null },
    { username: 'SecureShield', points: 10800, avatar: null },
    { username: 'CodeBreaker', points: 9500, avatar: null },
    { username: 'Byte<PERSON>ef<PERSON>', points: 8900, avatar: null },
  ],
  testimonials: [
    {
      id: 1,
      username: '<PERSON><PERSON><PERSON>',
      text: 'The challenges on <PERSON><PERSON>erber<PERSON> helped me prepare for my OSCP certification. Highly recommended!',
      rating: 5,
    },
    {
      id: 2,
      username: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      text: 'I love how the learning modules build on each other. Great progression from beginner to advanced topics.',
      rating: 5,
    },
    {
      id: 3,
      username: 'NetworkNinja',
      text: 'The hands-on approach makes learning cybersecurity concepts much more engaging than just reading theory.',
      rating: 4,
    },
  ],
};

const SocialProof = ({ variant = 'sidebar' }) => {
  const { darkMode } = useGlobalTheme();
  const [activeUsers, setActiveUsers] = useState(0);
  const [completedChallenges, setCompletedChallenges] = useState(0);
  const [completedModules, setCompletedModules] = useState(0);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  
  // Generate random numbers within ranges for stats
  useEffect(() => {
    const generateRandomInRange = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
    
    setActiveUsers(generateRandomInRange(MOCK_DATA.activeUsers.min, MOCK_DATA.activeUsers.max));
    setCompletedChallenges(generateRandomInRange(MOCK_DATA.completedChallenges.min, MOCK_DATA.completedChallenges.max));
    setCompletedModules(generateRandomInRange(MOCK_DATA.completedModules.min, MOCK_DATA.completedModules.max));
    
    // Rotate testimonials every 10 seconds
    const testimonialInterval = setInterval(() => {
      setCurrentTestimonial(prev => (prev + 1) % MOCK_DATA.testimonials.length);
    }, 10000);
    
    return () => clearInterval(testimonialInterval);
  }, []);
  
  // Format large numbers with commas
  const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };
  
  if (variant === 'sidebar') {
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden mb-6`}>
        <div className="p-4">
          <h3 className={`font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Community Activity
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center mr-3">
                <FaUsers className="text-blue-500" />
              </div>
              <div>
                <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatNumber(activeUsers)} users online
                </div>
                <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Join the community
                </div>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center mr-3">
                <FaTrophy className="text-yellow-500" />
              </div>
              <div>
                <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatNumber(completedChallenges)} challenges completed
                </div>
                <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  This month
                </div>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center mr-3">
                <FaGraduationCap className="text-green-500" />
              </div>
              <div>
                <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatNumber(completedModules)} modules completed
                </div>
                <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  By our community
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-6">
            <h4 className={`font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Top Performers
            </h4>
            
            <div className={`${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} rounded-lg p-3`}>
              {MOCK_DATA.leaderboard.slice(0, 3).map((user, index) => (
                <div key={index} className="flex items-center justify-between mb-2 last:mb-0">
                  <div className="flex items-center">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                      index === 0 
                        ? 'bg-yellow-500 text-black' 
                        : index === 1 
                          ? 'bg-gray-300 text-black' 
                          : 'bg-amber-700 text-white'
                    }`}>
                      {index + 1}
                    </div>
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-[#88cc14] rounded-full flex items-center justify-center text-black mr-2">
                        <FaUserAstronaut size={12} />
                      </div>
                      <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {user.username}
                      </span>
                    </div>
                  </div>
                  <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {formatNumber(user.points)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  if (variant === 'testimonial') {
    const testimonial = MOCK_DATA.testimonials[currentTestimonial];
    
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden mb-6`}>
        <div className="p-4">
          <div className="flex items-center mb-3">
            <div className="w-8 h-8 bg-[#88cc14] rounded-full flex items-center justify-center text-black mr-2">
              {testimonial.username.charAt(0)}
            </div>
            <div>
              <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {testimonial.username}
              </div>
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <svg 
                    key={i}
                    className={`w-4 h-4 ${i < testimonial.rating ? 'text-yellow-500' : 'text-gray-400'}`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
            </div>
          </div>
          
          <p className={`text-sm italic ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
            "{testimonial.text}"
          </p>
          
          <div className="flex justify-between items-center">
            <div className="flex space-x-1">
              {MOCK_DATA.testimonials.map((_, index) => (
                <div 
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === currentTestimonial
                      ? 'bg-[#88cc14]'
                      : darkMode ? 'bg-gray-700' : 'bg-gray-300'
                  }`}
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return null;
};

export default SocialProof;
