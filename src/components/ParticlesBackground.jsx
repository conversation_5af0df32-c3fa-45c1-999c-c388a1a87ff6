import { useCallback } from "react";
import Particles from "react-particles";
import { loadFull } from "tsparticles";

const ParticlesBackground = () => {
  const particlesInit = useCallback(async engine => {
    await loadFull(engine);
  }, []);

  return (
    <Particles
      className="absolute inset-0"
      init={particlesInit}
      options={{
        background: {
          opacity: 0
        },
        fpsLimit: 60,
        particles: {
          color: {
            value: "rgb(45, 212, 191)"
          },
          links: {
            color: "rgb(45, 212, 191)",
            distance: 150,
            enable: true,
            opacity: 0.1,
            width: 1,
            triangles: {
              enable: true,
              opacity: 0.05
            }
          },
          move: {
            enable: true,
            speed: 0.5, // Reduced speed
            direction: "none",
            random: true,
            straight: false,
            outModes: {
              default: "bounce"
            }
          },
          number: {
            density: {
              enable: true,
              area: 1000 // Increased area for fewer particles
            },
            value: 40 // Reduced number of particles
          },
          opacity: {
            value: 0.1,
            random: true
          },
          shape: {
            type: ["circle"]
          },
          size: {
            value: { min: 1, max: 2 },
            random: true
          }
        },
        interactivity: {
          detect_on: "window",
          events: {
            onHover: {
              enable: false
            },
            onClick: {
              enable: false
            },
            resize: true
          }
        },
        detectRetina: true
      }}
    />
  );
};

export default ParticlesBackground;