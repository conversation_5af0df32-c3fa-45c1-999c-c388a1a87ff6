import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaLock, FaCoi<PERSON>, FaCrown } from 'react-icons/fa';
import { useSubscription } from '../../contexts/SubscriptionContext';
import Button from '../ui/Button';

/**
 * RestrictedContent component
 * Displays a restricted content overlay with appropriate messaging based on restriction type
 * 
 * @param {Object} props
 * @param {ReactNode} props.children - The content to display when access is granted
 * @param {string} props.type - The type of restriction ('subscription', 'coins')
 * @param {string} props.feature - The feature being restricted ('learn', 'challenges', 'startHack')
 * @param {string} props.requiredTier - The subscription tier required (for subscription restrictions)
 * @param {string} props.difficulty - The difficulty level (for coin-based restrictions)
 * @param {string} props.itemId - The ID of the item being restricted
 * @param {Function} props.onPurchase - Callback function when purchase is successful
 */
const RestrictedContent = ({
  children,
  type = 'subscription',
  feature,
  requiredTier = 'premium',
  difficulty,
  itemId,
  onPurchase,
}) => {
  const {
    subscriptionLevel,
    userCoins,
    hasAccess,
    requiresCoins,
    getItemCost,
    hasEnoughCoins,
    purchaseWithCoins,
    upgradeSubscription,
    tierFeatures,
  } = useSubscription();

  // Check if user has access to this content
  const canAccess = () => {
    if (type === 'subscription') {
      return hasAccess(feature);
    } else if (type === 'coins') {
      return !requiresCoins('challenge', difficulty) || hasEnoughCoins('challenge', difficulty);
    }
    return false;
  };

  // If user has access, render children
  if (canAccess()) {
    return <>{children}</>;
  }

  // Handle coin purchase
  const handlePurchase = async () => {
    try {
      await purchaseWithCoins('challenge', itemId, difficulty);
      if (onPurchase) onPurchase();
    } catch (error) {
      console.error('Purchase failed:', error);
      // You could show an error toast here
    }
  };

  // Handle subscription upgrade
  const handleUpgrade = async () => {
    try {
      // In a real app, this would redirect to a payment page
      // For demo purposes, we'll just upgrade directly
      await upgradeSubscription(requiredTier);
    } catch (error) {
      console.error('Upgrade failed:', error);
      // You could show an error toast here
    }
  };

  // Render appropriate restriction overlay based on type
  return (
    <div className="relative group">
      {/* Blurred content in background */}
      <div className="filter blur-sm opacity-30 pointer-events-none">
        {children}
      </div>
      
      {/* Restriction overlay */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="absolute inset-0 bg-[#0B1120]/80 flex items-center justify-center z-10"
      >
        <div className="bg-[#1A1F35] p-6 md:p-8 rounded-xl border border-gray-800 max-w-md text-center">
          {type === 'subscription' ? (
            // Subscription restriction
            <>
              <div className="w-16 h-16 bg-[#88cc14]/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaCrown className="text-[#88cc14] text-3xl" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">Premium Feature</h3>
              <p className="text-gray-300 mb-6">
                This {feature === 'learn' ? 'learning module' : 
                      feature === 'challenges' ? 'challenge' : 
                      feature === 'startHack' ? 'hacking simulation' : 'feature'} 
                is only available to {requiredTier} subscribers.
              </p>
              
              <div className="space-y-4">
                <Button 
                  variant="primary" 
                  size="lg" 
                  fullWidth
                  onClick={handleUpgrade}
                >
                  Upgrade to {requiredTier.charAt(0).toUpperCase() + requiredTier.slice(1)} (${tierFeatures[requiredTier]?.price})
                </Button>
                <Link to="/pricing" className="text-[#88cc14] hover:underline block">
                  View all subscription options
                </Link>
              </div>
            </>
          ) : (
            // Coin-based restriction
            <>
              <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaCoins className="text-yellow-500 text-3xl" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">Purchase Required</h3>
              <p className="text-gray-300 mb-6">
                This {difficulty} challenge requires {getItemCost('challenge', difficulty)} coins to unlock.
              </p>
              
              <div className="flex items-center justify-center gap-2 mb-4">
                <FaCoins className="text-yellow-500" />
                <span className="text-white font-bold">Your balance: {userCoins} coins</span>
              </div>
              
              <div className="space-y-4">
                {hasEnoughCoins('challenge', difficulty) ? (
                  <Button 
                    variant="primary" 
                    size="lg" 
                    fullWidth
                    onClick={handlePurchase}
                  >
                    Unlock for {getItemCost('challenge', difficulty)} coins
                  </Button>
                ) : (
                  <>
                    <p className="text-red-400 mb-2">You don't have enough coins</p>
                    <Link to="/store">
                      <Button 
                        variant="primary" 
                        size="lg" 
                        fullWidth
                      >
                        Get More Coins
                      </Button>
                    </Link>
                  </>
                )}
                
                <div className="pt-2 border-t border-gray-700">
                  <Link to="/pricing" className="text-[#88cc14] hover:underline block">
                    Upgrade to Business for unlimited access
                  </Link>
                </div>
              </div>
            </>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default RestrictedContent;
