import React from 'react';
import { Link } from 'react-router-dom';
import { FaCrown } from 'react-icons/fa';

/**
 * SimpleUpgradeBanner component
 * A simplified version of the upgrade banner that doesn't rely on complex context
 */
const SimpleUpgradeBanner = ({
  targetTier = 'premium',
  price = 399,
  currency = '₹',
  message = 'Upgrade to Premium for full access to all features',
}) => {
  return (
    <div className="bg-[#1A1F35] border border-[#88cc14]/30 rounded-lg p-4 mb-6 relative overflow-hidden">
      <div className="flex flex-col md:flex-row items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
            <FaCrown className="text-[#88cc14] text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-white">{targetTier.charAt(0).toUpperCase() + targetTier.slice(1)} Subscription</h3>
            <p className="text-gray-300 text-sm">{message}</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {targetTier === 'business' ? (
            <span className="text-white font-bold">Contact Sales</span>
          ) : (
            <span className="text-white font-bold">{currency}{price}</span>
          )}
          <Link
            to="/pricing"
            className="bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium px-4 py-2 rounded-lg transition-colors"
          >
            Upgrade Now
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SimpleUpgradeBanner;
