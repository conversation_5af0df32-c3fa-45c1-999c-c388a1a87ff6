import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { <PERSON>a<PERSON>ock, Fa<PERSON>oins, FaCrown } from 'react-icons/fa';
import { useSubscription } from '../../contexts/SubscriptionContext';
import Card from '../ui/Card';

/**
 * LockedItem component
 * Displays a locked item card with appropriate messaging based on restriction type
 * 
 * @param {Object} props
 * @param {string} props.title - The title of the item
 * @param {string} props.description - The description of the item
 * @param {string} props.image - The image URL for the item
 * @param {string} props.type - The type of restriction ('subscription', 'coins')
 * @param {string} props.feature - The feature being restricted ('learn', 'challenges', 'startHack')
 * @param {string} props.requiredTier - The subscription tier required (for subscription restrictions)
 * @param {string} props.difficulty - The difficulty level (for coin-based restrictions)
 * @param {string} props.to - The link to navigate to when clicked
 */
const LockedItem = ({
  title,
  description,
  image,
  type = 'subscription',
  feature,
  requiredTier = 'premium',
  difficulty,
  to,
}) => {
  const {
    requiresCoins,
    getItemCost,
  } = useSubscription();

  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Link to={to || '/pricing'}>
        <Card className="h-full relative overflow-hidden group" hover={false} animate={false}>
          {/* Overlay */}
          <div className="absolute inset-0 bg-black/70 backdrop-blur-sm flex flex-col items-center justify-center z-10 p-4">
            {type === 'subscription' ? (
              <>
                <FaCrown className="text-[#88cc14] text-4xl mb-3" />
                <h3 className="text-xl font-bold text-white mb-1">{title}</h3>
                <p className="text-gray-300 text-sm mb-3 text-center">{description}</p>
                <span className="bg-[#88cc14] text-black px-3 py-1 rounded-full text-sm font-bold">
                  {requiredTier.charAt(0).toUpperCase() + requiredTier.slice(1)} Required
                </span>
              </>
            ) : (
              <>
                <FaCoins className="text-yellow-500 text-4xl mb-3" />
                <h3 className="text-xl font-bold text-white mb-1">{title}</h3>
                <p className="text-gray-300 text-sm mb-3 text-center">{description}</p>
                <span className="bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold flex items-center gap-1">
                  <FaCoins className="text-sm" />
                  {getItemCost('challenge', difficulty)} Coins
                </span>
              </>
            )}
          </div>
          
          {/* Background content (blurred) */}
          <div className="relative h-40 overflow-hidden">
            {image ? (
              <img 
                src={image} 
                alt={title} 
                className="w-full h-full object-cover filter blur-sm opacity-30"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 filter blur-sm opacity-30" />
            )}
            <div className="absolute top-2 right-2 bg-black/50 p-1 rounded-full">
              <FaLock className="text-white" />
            </div>
          </div>
          
          <div className="p-4 filter blur-sm opacity-30">
            <h3 className="font-bold text-lg mb-1 text-white">{title}</h3>
            <p className="text-gray-400 text-sm line-clamp-2">{description}</p>
          </div>
        </Card>
      </Link>
    </motion.div>
  );
};

export default LockedItem;
