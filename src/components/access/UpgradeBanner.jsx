import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaCrown, FaArrowRight } from 'react-icons/fa';
import { useSubscription } from '../../contexts/SubscriptionContext';
import Button from '../ui/Button';

/**
 * UpgradeBanner component
 * Displays a banner prompting users to upgrade their subscription
 * 
 * @param {Object} props
 * @param {string} props.targetTier - The subscription tier to promote ('premium', 'business')
 * @param {string} props.message - Custom message to display
 * @param {boolean} props.showClose - Whether to show a close button
 * @param {Function} props.onClose - Callback when close button is clicked
 */
const UpgradeBanner = ({
  targetTier = 'premium',
  message,
  showClose = false,
  onClose,
}) => {
  const { tierFeatures } = useSubscription();
  
  const tierInfo = tierFeatures[targetTier];
  
  if (!tierInfo) return null;
  
  const defaultMessage = `Upgrade to ${targetTier.charAt(0).toUpperCase() + targetTier.slice(1)} for full access to all features`;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-[#1A1F35] border border-[#88cc14]/30 rounded-lg p-4 mb-6 relative overflow-hidden"
    >
      {/* Background decoration */}
      <div className="absolute -top-10 -right-10 w-40 h-40 bg-[#88cc14]/10 rounded-full blur-xl"></div>
      <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl"></div>
      
      <div className="flex flex-col md:flex-row items-center justify-between gap-4 relative z-10">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center">
            <FaCrown className="text-[#88cc14] text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-white">{targetTier.charAt(0).toUpperCase() + targetTier.slice(1)} Subscription</h3>
            <p className="text-gray-300 text-sm">{message || defaultMessage}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <span className="text-white font-bold">${tierInfo.price}</span>
          <Link to="/pricing">
            <Button 
              variant="primary" 
              size="sm"
              icon={<FaArrowRight />}
              iconPosition="right"
            >
              Upgrade Now
            </Button>
          </Link>
        </div>
      </div>
      
      {showClose && (
        <button 
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-400 hover:text-white"
          aria-label="Close"
        >
          &times;
        </button>
      )}
    </motion.div>
  );
};

export default UpgradeBanner;
