import React, { useState, useEffect, useRef, useCallback } from 'react';

const VirtualizedList = ({
  items = [],
  itemHeight = 50,
  overscan = 5,
  renderItem,
  className = '',
  containerHeight = 400,
  onEndReached,
  endReachedThreshold = 0.8,
  keyExtractor = (item, index) => index,
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const [hasReachedEnd, setHasReachedEnd] = useState(false);

  // Update container width on resize
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  // Calculate visible items
  const visibleItemCount = Math.ceil(containerHeight / itemHeight) + overscan * 2;
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(items.length - 1, startIndex + visibleItemCount - 1);

  // Handle scroll
  const handleScroll = useCallback((e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    setScrollTop(scrollTop);

    // Check if we've reached the end
    const scrollPosition = scrollTop + clientHeight;
    const scrollThreshold = scrollHeight * endReachedThreshold;
    
    if (scrollPosition >= scrollThreshold && !hasReachedEnd && onEndReached) {
      setHasReachedEnd(true);
      onEndReached();
    } else if (scrollPosition < scrollThreshold && hasReachedEnd) {
      setHasReachedEnd(false);
    }
  }, [endReachedThreshold, hasReachedEnd, onEndReached]);

  // Reset hasReachedEnd when items change
  useEffect(() => {
    setHasReachedEnd(false);
  }, [items.length]);

  // Visible items
  const visibleItems = items.slice(startIndex, endIndex + 1);

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div
        className="relative"
        style={{ height: items.length * itemHeight, width: '100%' }}
      >
        {visibleItems.map((item, index) => {
          const actualIndex = startIndex + index;
          const key = keyExtractor(item, actualIndex);
          
          return (
            <div
              key={key}
              className="absolute w-full"
              style={{
                top: actualIndex * itemHeight,
                height: itemHeight,
              }}
            >
              {renderItem({ item, index: actualIndex, width: containerWidth })}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default VirtualizedList;
