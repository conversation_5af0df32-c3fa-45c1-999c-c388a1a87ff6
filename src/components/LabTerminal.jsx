import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

function LabTerminal({ commands = [], onCommandComplete }) {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState([]);
  const [currentTask, setCurrentTask] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const terminalRef = useRef(null);
  const outputEndRef = useRef(null);

  // Scroll to bottom when output changes
  useEffect(() => {
    outputEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [output]);

  // Initialize terminal with welcome message
  useEffect(() => {
    setOutput([
      { type: 'system', text: '=== XCerberus OS Lab Terminal ===' },
      { type: 'system', text: 'Type "help" for available commands' },
      { type: 'system', text: '' }
    ]);
    
    if (commands.length > 0) {
      setOutput(prev => [
        ...prev,
        { type: 'system', text: `Current task: ${commands[0].description}` }
      ]);
    }
  }, [commands]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!input.trim() || isTyping) return;

    const command = input.trim();
    setOutput(prev => [...prev, { type: 'input', text: `$ ${command}` }]);
    setInput('');
    setIsTyping(true);

    // Process command
    setTimeout(() => {
      if (command === 'help') {
        setOutput(prev => [
          ...prev,
          { type: 'system', text: 'Available commands:' },
          { type: 'system', text: '  help     - Show this help message' },
          { type: 'system', text: '  clear    - Clear terminal screen' },
          { type: 'system', text: '' },
          { type: 'system', text: 'Task-specific commands:' },
          ...commands.map(cmd => ({ 
            type: 'system', 
            text: `  ${cmd.command}  - ${cmd.description}` 
          }))
        ]);
      } else if (command === 'clear') {
        setOutput([]);
      } else {
        // Check if command matches current task
        const currentCommand = commands[currentTask];
        if (currentCommand && command === currentCommand.command) {
          // Command successful
          setOutput(prev => [
            ...prev,
            { type: 'success', text: '✓ Command successful!' },
            { type: 'output', text: currentCommand.expectedOutput || 'Command executed successfully.' }
          ]);

          // Notify parent component
          onCommandComplete?.(currentCommand);

          // Move to next task if available
          if (currentTask < commands.length - 1) {
            setTimeout(() => {
              setCurrentTask(prev => prev + 1);
              setOutput(prev => [
                ...prev,
                { type: 'system', text: '' },
                { type: 'system', text: `Next task: ${commands[currentTask + 1].description}` }
              ]);
            }, 1000);
          } else {
            setOutput(prev => [
              ...prev,
              { type: 'success', text: '🎉 All tasks completed! Congratulations!' }
            ]);
          }
        } else {
          // Command failed
          setOutput(prev => [
            ...prev,
            { type: 'error', text: 'Command not recognized or incorrect for current task.' }
          ]);

          // Show hint if available
          if (currentCommand?.hint) {
            setOutput(prev => [
              ...prev,
              { type: 'hint', text: `Hint: ${currentCommand.hint}` }
            ]);
          }
        }
      }
      setIsTyping(false);
    }, 500);
  };

  return (
    <div className="bg-black rounded-lg overflow-hidden border border-gray-800">
      <div className="p-3 border-b border-gray-800 bg-gray-900">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span className="ml-2 text-gray-400 text-sm">XCerberus@terminal:~</span>
        </div>
      </div>
      
      <div 
        ref={terminalRef}
        className="h-96 p-4 font-mono text-sm overflow-y-auto bg-black text-gray-300"
      >
        {output.map((line, i) => (
          <div 
            key={i} 
            className={`mb-2 ${
              line.type === 'input' ? 'text-green-400' :
              line.type === 'success' ? 'text-green-400' :
              line.type === 'error' ? 'text-red-400' :
              line.type === 'hint' ? 'text-yellow-400' :
              line.type === 'output' ? 'text-blue-300' :
              'text-gray-300'
            }`}
          >
            {line.text}
          </div>
        ))}
        
        {isTyping && (
          <div className="text-green-400 animate-pulse">_</div>
        )}
        
        <div ref={outputEndRef}></div>
        
        <form onSubmit={handleSubmit} className="mt-2 flex items-center">
          <span className="text-green-400 mr-2">$</span>
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="flex-1 bg-transparent border-none outline-none text-green-400"
            disabled={isTyping}
            autoFocus
          />
        </form>
      </div>
    </div>
  );
}

export default LabTerminal;