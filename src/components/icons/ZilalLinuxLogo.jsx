import React from 'react';

function ZilalLinuxLogo({ className = "", darkMode = false, size = "normal" }) {
  // Determine the size of the logo
  const logoSize = size === "small" ? "30" : "40";
  const textSize = size === "small" ? "text-xl" : "text-2xl";
  return (
    <div className={`flex items-center ${className}`}>
      {/* SVG Logo based on current design */}
      <svg width={logoSize} height={logoSize} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
        {/* Dark background circle */}
        <circle cx="16" cy="16" r="16" fill="#0F1729"/>

        {/* Dotted circle */}
        <circle
          cx="16"
          cy="16"
          r="12"
          stroke="#88cc14"
          strokeWidth="1.5"
          strokeDasharray="2 2"
        />

        {/* Z letter */}
        <path
          d="M10 10H22M10 22H22M22 10L10 22"
          stroke="#88cc14"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>

      {/* Text part */}
      <span className={`font-bold ${textSize} ${darkMode ? 'text-white' : 'text-gray-900'}`}>
        <span className="text-[#88cc14]">Zilal</span> Linux
      </span>
    </div>
  );
}

export default ZilalLinuxLogo;
