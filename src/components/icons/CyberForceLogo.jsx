import React from 'react';

function CyberForceLogo({ className = "", darkMode = false, size = "normal" }) {
  // Determine the size of the logo
  const logoSize = size === "small" ? "30" : "40";
  const textSize = size === "small" ? "text-xl" : "text-2xl";

  return (
    <div className={`flex items-center ${className}`}>
      {/* Logo Image */}
      <img
        src="/images/CyberForce.png"
        alt="CyberForce Logo"
        className="h-8 mr-2"
      />

      {/* Text part */}
      <span className={`font-bold ${textSize}`}>
        <span className="text-[#4A5CBA]">Cyber</span>
        <span className="text-[#F5B93F]">Force</span>
      </span>
    </div>
  );
}

export default CyberForceLogo;
