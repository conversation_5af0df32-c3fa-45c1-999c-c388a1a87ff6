import React from 'react';

function XCerberusLogo({ className = "", darkMode = false, size = "normal" }) {
  // Determine the size of the logo
  const logoSize = size === "small" ? "30" : "40";
  const textSize = size === "small" ? "text-xl" : "text-2xl";
  return (
    <div className={`flex items-center ${className}`}>
      {/* SVG Logo based on current design */}
      <svg width={logoSize} height={logoSize} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
        {/* Dark background circle */}
        <circle cx="16" cy="16" r="16" fill="#0F1729"/>

        {/* Dotted circle */}
        <circle
          cx="16"
          cy="16"
          r="12"
          stroke="#88cc14"
          strokeWidth="1.5"
          strokeDasharray="2 2"
        />

        {/* X letter */}
        <path
          d="M11 9L16 16L11 23"
          stroke="#88cc14"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M21 9L16 16L21 23"
          stroke="#88cc14"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>

      {/* Text part - using only the text without the X since it's in the SVG */}
      <span className={`font-bold ${textSize} ${darkMode ? 'text-white' : 'text-gray-900'}`}>
        <span className="text-[#88cc14]">X</span>Cerberus
      </span>
    </div>
  );
}

export default XCerberusLogo;
