import React from 'react';

function XCerberusIcon({ className = "", size = 24 }) {
  // Calculate stroke width based on size
  const strokeWidth = Math.max(1.5, size * 0.08);

  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      <svg width={size} height={size} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        {/* Dark background circle */}
        <circle cx="16" cy="16" r="16" fill="#0F1729"/>

        {/* Dotted circle */}
        <circle
          cx="16"
          cy="16"
          r="12"
          stroke="#88cc14"
          strokeWidth={strokeWidth * 0.6}
          strokeDasharray="2 2"
        />

        {/* X letter */}
        <path
          d="M11 9L16 16L11 23"
          stroke="#88cc14"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M21 9L16 16L21 23"
          stroke="#88cc14"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
}

export default XCerberusIcon;
