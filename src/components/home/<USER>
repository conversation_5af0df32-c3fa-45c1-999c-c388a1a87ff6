import React from 'react';
import { motion } from 'framer-motion';

const CTASection = () => {
  return (
    <section className="py-16 md:py-24 px-5">
      <div className="container mx-auto">
        <motion.div 
          className="challenge-card max-w-4xl mx-auto text-center p-8 md:p-10 relative overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          whileHover={{ 
            scale: 1.02,
            boxShadow: '0 10px 30px rgba(136, 204, 20, 0.3)',
            borderColor: '#88cc14'
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-transparent to-[#88cc14]/5 opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
          <div className="relative z-10">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900">
              Ready to Test Your Skills?
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              Join thousands of hackers and security professionals.
              Start your journey into ethical hacking today.
            </p>
            <button className="primary-button text-lg px-8 py-4 hover:scale-105 transition-transform relative overflow-hidden group">
              <span className="relative z-10">Begin Training</span>
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-500" style={{transform: 'translateX(-100%)', animation: 'borderSlide 2s infinite'}}></span>
            </button>
          </div>
          <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-[#88cc14]/5 rounded-full transform rotate-12 opacity-70 blur-xl"></div>
          <div className="absolute -top-10 -left-10 w-40 h-40 bg-[#88cc14]/5 rounded-full transform -rotate-12 opacity-70 blur-xl"></div>
        </motion.div>
      </div>
    </section>
  );
};

export default CTASection;