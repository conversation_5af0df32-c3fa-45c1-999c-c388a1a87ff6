import React from 'react';
import { motion } from 'framer-motion';
import { FaTerminal, FaShieldVirus, FaServer, FaLock, FaUserShield, FaNetworkWired } from 'react-icons/fa';

const features = [
  {
    icon: FaTerminal,
    title: "Real-world Scenarios",
    description: "Practice on simulated environments that mirror actual cyber attacks",
    color: "#F5B93F" // Gold
  },
  {
    icon: FaShieldVirus,
    title: "Defense Techniques",
    description: "Learn to protect systems by understanding how they're attacked",
    color: "#10B981" // Green
  },
  {
    icon: FaServer,
    title: "Live Machines",
    description: "Access vulnerable systems ready for exploitation and practice",
    color: "#4A5CBA" // Blue
  },
  {
    icon: FaLock,
    title: "Secure Environment",
    description: "Train in a controlled, isolated environment with professional guidance",
    color: "#EC4899" // Pink
  },
  {
    icon: FaUserShield,
    title: "Expert Mentorship",
    description: "Learn from industry professionals with real-world experience",
    color: "#8B5CF6" // Purple
  },
  {
    icon: FaNetworkWired,
    title: "Network Security",
    description: "Master techniques to secure networks against unauthorized access",
    color: "#F5B93F" // Gold
  }
];

const FeaturesSection = () => {
  return (
    <section className="py-16 md:py-24 px-5 relative overflow-hidden">
      {/* Background grid pattern */}
      <div className="absolute inset-0 opacity-5">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
          <pattern id="grid-pattern-features" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(0,0,0,0.3)" strokeWidth="0.5" />
          </pattern>
          <rect width="100%" height="100%" fill="url(#grid-pattern-features)" />
        </svg>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(10)].map((_, i) => (
          <motion.div
            key={i}
            initial={{
              x: Math.random() * 1000,
              y: Math.random() * 600,
              opacity: 0
            }}
            animate={{
              x: Math.random() * 1000,
              y: Math.random() * 600,
              opacity: [0, 0.5, 0]
            }}
            transition={{
              duration: Math.random() * 8 + 8,
              repeat: Infinity,
              delay: Math.random() * 5
            }}
            className="absolute w-1.5 h-1.5 rounded-full"
            style={{
              backgroundColor: i % 5 === 0 ? '#F5B93F' :
                              i % 5 === 1 ? '#10B981' :
                              i % 5 === 2 ? '#EC4899' :
                              i % 5 === 3 ? '#8B5CF6' : '#4A5CBA',
              boxShadow: i % 5 === 0 ? '0 0 5px #F5B93F' :
                         i % 5 === 1 ? '0 0 5px #10B981' :
                         i % 5 === 2 ? '0 0 5px #EC4899' :
                         i % 5 === 3 ? '0 0 5px #8B5CF6' : '0 0 5px #4A5CBA'
            }}
          />
        ))}
      </div>

      <div className="container mx-auto relative z-10">
        <div className="text-center mb-14">
          <motion.h2
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="section-title text-3xl md:text-4xl font-bold mb-6 text-gray-900 dark:text-white"
          >
            Why Choose <span className="text-[#4A5CBA]">Cyber</span><span className="text-[#F5B93F]">Force</span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="section-description text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
          >
            Learn cybersecurity through immersive, hands-on experiences with our cutting-edge platform
          </motion.p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 section-gap">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="feature-card p-6 md:p-8 bg-white dark:bg-[#0F1729] rounded-lg border border-gray-100 dark:border-gray-800 relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{
                scale: 1.03,
                boxShadow: `0 10px 25px ${feature.color}30`,
                borderColor: feature.color
              }}
            >
              {/* Glowing corner effect */}
              <div className="absolute top-0 left-0 w-20 h-20 opacity-10" style={{
                background: `radial-gradient(circle at top left, ${feature.color}, transparent 70%)`
              }}></div>

              <div className="relative">
                <div className="w-14 h-14 rounded-lg flex items-center justify-center mb-5" style={{
                  backgroundColor: `${feature.color}15`,
                  border: `1px solid ${feature.color}30`
                }}>
                  <feature.icon className="text-2xl" style={{ color: feature.color }} />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">{feature.title}</h3>
                <p className="text-gray-600 dark:text-gray-400">{feature.description}</p>
              </div>

              {/* Corner accent */}
              <div className="absolute bottom-0 right-0 w-4 h-4 border-b border-r" style={{ borderColor: feature.color }}></div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;