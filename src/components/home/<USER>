import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { FaUserNinja, FaFlag, FaLaptopCode, FaUserSecret, FaShieldAlt, FaGlobe, FaServer, FaNetworkWired, FaLock, FaDatabase } from 'react-icons/fa';
import CyberSecurityVisualization from './CyberSecurityVisualization';

const stats = [
  { icon: FaUserNinja, value: "1000+", label: "Active Labs" },
  { icon: FaLaptopCode, value: "500+", label: "Tutorials" },
  { icon: FaLock, value: "100%", label: "Secure" },
  { icon: FaGlobe, value: "50+", label: "Countries" }
];

const features = [
  {
    icon: FaNetworkWired,
    title: "Network Security",
    description: "Learn to secure networks against unauthorized access and attacks"
  },
  {
    icon: FaDatabase,
    title: "Data Protection",
    description: "Master techniques to protect sensitive data from breaches"
  },
  {
    icon: FaUserSecret,
    title: "Threat Intelligence",
    description: "Stay ahead of emerging threats with real-time intelligence"
  }
];

const HeroSection = ({ onShowDemo }) => {
  const navigate = useNavigate();

  return (
    <section className="pt-24 lg:pt-32 pb-16 md:pb-24 px-5">
      <div className="container mx-auto">
        <div className="flex flex-col lg:flex-row gap-12 md:gap-16 items-center mb-16">
          {/* Main Content - Always First on Mobile */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="text-center sm:text-left w-full lg:w-1/2 lg:order-1 order-1"
            style={{ order: 1 }}
          >
            {/* Logo and Badge */}
            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 mb-8">
              <img
                src="/images/CyberForce.svg"
                alt="CyberForce Logo"
                className="h-24 sm:h-32 mb-4 sm:mb-0"
              />
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#4A5CBA]/10 text-[#4A5CBA] text-sm font-bold">
                <FaShieldAlt className="mr-2" />
                #1 Cybersecurity Training Platform
              </div>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-8 leading-tight text-gray-900 dark:text-white hero-title">
              <div className="flex flex-col sm:flex-row sm:items-center justify-center sm:justify-start gap-2">
                <span>Master</span>
                <span className="text-[#4A5CBA]">Cybersecurity</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center justify-center sm:justify-start gap-2">
                <span>Through</span>
                <span className="text-[#F5B93F]">Real-World Challenges</span>
              </div>
            </h1>
            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 mb-10 max-w-2xl mx-auto sm:mx-0 hero-description">
              Join thousands of security professionals already training on CyberForce.
              Learn, practice, and compete in a safe environment with our advanced training platform.
            </p>
            <div className="flex flex-wrap gap-5 justify-center sm:justify-start">
              <button
                onClick={() => navigate('/learn')}
                className="bg-[#4A5CBA] text-white font-bold py-3 px-8 rounded-lg hover:bg-[#3A4CAA] transition-colors"
              >
                Start Learning
              </button>
              <button
                onClick={() => navigate('/challenges')}
                className="border-2 border-[#F5B93F] text-[#F5B93F] dark:text-[#F5B93F] font-bold py-3 px-8 rounded-lg hover:bg-[#F5B93F]/10 transition-colors"
              >
                Explore Challenges
              </button>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mt-14">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{
                    scale: 1.05,
                    boxShadow: '0 0 20px rgba(74, 92, 186, 0.3)'
                  }}
                  className="stat-card text-center p-4 rounded-lg bg-white border border-gray-200 hover:border-[#4A5CBA] transition-all duration-300 relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-transparent to-[#4A5CBA]/5 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10">
                    <div className="w-12 h-12 mx-auto mb-2 bg-[#4A5CBA]/10 rounded-full flex items-center justify-center transform transition-transform duration-300 hover:scale-110">
                      <stat.icon className="text-[#4A5CBA] text-xl" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Feature Highlights */}
            <div className="mt-16">
              <h3 className="text-2xl font-bold mb-6 text-gray-900">Key Features</h3>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                    className="p-5 rounded-lg bg-white border border-gray-200 hover:border-[#F5B93F] transition-all duration-300 relative overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent to-[#F5B93F]/5 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative z-10">
                      <div className="w-10 h-10 mb-3 bg-[#F5B93F]/10 rounded-full flex items-center justify-center">
                        <feature.icon className="text-[#F5B93F] text-lg" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-900 mb-2">{feature.title}</h4>
                      <p className="text-sm text-gray-600">{feature.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Cybersecurity Visualization - Second on Mobile */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="w-full lg:w-1/2 lg:order-2 order-2"
            style={{ order: 2 }}
          >
            <div className="rounded-lg shadow-2xl overflow-hidden">
              {/* Cybersecurity Visualization */}
              <CyberSecurityVisualization />

              {/* Features */}
              <div className="bg-[#0F1729] p-4 border-t border-[#4A5CBA]/20">
                <div className="flex flex-wrap gap-3 justify-center">
                  <span className="px-3 py-1 bg-[#4A5CBA]/10 text-[#4A5CBA] text-xs rounded-full">Advanced Protection</span>
                  <span className="px-3 py-1 bg-[#F5B93F]/10 text-[#F5B93F] text-xs rounded-full">Multi-Layer Security</span>
                  <span className="px-3 py-1 bg-[#10B981]/10 text-[#10B981] text-xs rounded-full">Real-time Monitoring</span>
                  <span className="px-3 py-1 bg-[#EC4899]/10 text-[#EC4899] text-xs rounded-full">Threat Detection</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;