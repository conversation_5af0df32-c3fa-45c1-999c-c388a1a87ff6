import React, { useState } from 'react';
import { motion } from 'framer-motion';
import WebExploitChallenge from '../challenges/WebExploitChallenge';
import BinaryChallenge from '../challenges/BinaryChallenge';
import CryptoChallenge from '../challenges/CryptoChallenge';

const ChallengeModal = ({ challenge, onClose }) => {
  const [started, setStarted] = useState(false);
  const [completed, setCompleted] = useState(false);

  if (!challenge) return null;

  const handleComplete = () => {
    setCompleted(true);
  };

  const renderChallenge = () => {
    switch (challenge.id) {
      case 1:
        return <WebExploitChallenge onComplete={handleComplete} />;
      case 2:
        return <BinaryChallenge onComplete={handleComplete} />;
      case 3:
        return <CryptoChallenge onComplete={handleComplete} />;
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4 md:p-6 overflow-y-auto">
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-gray-900 rounded-lg w-full max-w-4xl my-8 relative"
      >
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white bg-gray-800 hover:bg-gray-700 rounded-full p-2 transition-colors"
          aria-label="Close challenge"
        >
          <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="p-6 md:p-8">
          <div className="flex items-center mb-8">
            <div className="w-12 h-12 rounded-lg flex items-center justify-center mr-4 bg-[#88cc14]/10">
              <challenge.icon className="text-2xl text-[#88cc14]" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-white">{challenge.title}</h3>
              <div className="flex items-center mt-1">
                <span className="text-sm text-gray-400">{challenge.difficulty}</span>
                <span className="mx-2 text-gray-600">•</span>
                <span className="text-sm text-[#88cc14]">{challenge.points} pts</span>
              </div>
            </div>
          </div>

          {!started ? (
            <div className="space-y-6 text-gray-300">
              <div>
                <h4 className="text-lg font-bold text-white mb-3">Objective</h4>
                <p>{challenge.content.objective}</p>
              </div>

              <div>
                <h4 className="text-lg font-bold text-white mb-3">Tasks</h4>
                <ul className="space-y-3">
                  {challenge.content.tasks.map((task, index) => (
                    <li key={index} className="flex items-center">
                      <span className="w-5 h-5 rounded-full bg-[#88cc14]/10 text-[#88cc14] flex items-center justify-center mr-3 text-sm">
                        {index + 1}
                      </span>
                      {task}
                    </li>
                  ))}
                </ul>
              </div>

              <button
                onClick={() => setStarted(true)}
                className="w-full bg-[#88cc14] text-black font-bold py-3 px-4 rounded-lg hover:bg-[#7ab811] transition-all duration-300 mt-8"
              >
                Start Challenge
              </button>
            </div>
          ) : (
            <div className="challenge-content">
              {renderChallenge()}
              
              {completed && (
                <div className="mt-8 flex justify-end">
                  <button
                    onClick={onClose}
                    className="bg-[#88cc14] text-black font-bold py-3 px-6 rounded-lg hover:bg-[#7ab811] transition-all duration-300"
                  >
                    Close Challenge
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default ChallengeModal;