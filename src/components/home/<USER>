import React from 'react';
import { motion } from 'framer-motion';
import { Fa<PERSON>workWired, FaBug, FaLock } from 'react-icons/fa';

const challenges = [
  {
    id: 1,
    title: "Web Exploitation",
    description: "Master SQL injection, XSS, and CSRF attacks",
    icon: FaNetworkWired,
    difficulty: "Medium",
    points: 500,
    content: {
      objective: "Exploit vulnerabilities in a web application",
      tasks: [
        "Find and exploit SQL injection vulnerability",
        "Execute a successful XSS attack",
        "Perform CSRF attack"
      ],
      hints: [
        "Check input validation",
        "Look for unescaped output",
        "Examine request headers"
      ]
    }
  },
  {
    id: 2,
    title: "Binary Analysis",
    description: "Reverse engineer binaries and exploit buffer overflows",
    icon: FaBug,
    difficulty: "Hard",
    points: 750,
    content: {
      objective: "Analyze and exploit a binary application",
      tasks: [
        "Identify buffer overflow vulnerability",
        "Create exploit payload",
        "Achieve remote code execution"
      ],
      hints: [
        "Check stack alignment",
        "Look for unsafe functions",
        "Analyze memory layout"
      ]
    }
  },
  {
    id: 3,
    title: "Cryptography",
    description: "Break encryption and secure communications",
    icon: FaLock,
    difficulty: "Expert",
    points: 1000,
    content: {
      objective: "Break various encryption schemes",
      tasks: [
        "Crack weak encryption",
        "Perform man-in-the-middle attack",
        "Break secure communication protocol"
      ],
      hints: [
        "Check for weak keys",
        "Analyze traffic patterns",
        "Look for implementation flaws"
      ]
    }
  }
];

const ChallengesSection = ({ onStartChallenge }) => {
  return (
    <section className="py-16 md:py-24 px-5 bg-gray-50">
      <div className="container mx-auto">
        <div className="text-center mb-14">
          <h2 className="section-title text-3xl md:text-4xl font-bold mb-6 text-gray-900">Featured Challenges</h2>
          <p className="section-description text-gray-600 max-w-2xl mx-auto">
            Test your skills with real-world scenarios and learn from hands-on experience
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 section-gap">
          {challenges.map((challenge, index) => (
            <motion.div
              key={challenge.id}
              className="challenge-card p-6 md:p-8 flex flex-col h-full"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ 
                scale: 1.03,
                boxShadow: '0 10px 25px rgba(45, 212, 191, 0.3)',
                borderColor: 'rgb(45, 212, 191)'
              }}
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 rounded-lg flex items-center justify-center mr-4 bg-primary/10">
                  <challenge.icon className="text-2xl text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{challenge.title}</h3>
                  <div className="flex items-center mt-1">
                    <span className="text-sm text-gray-600">{challenge.difficulty}</span>
                    <span className="mx-2 text-gray-400">•</span>
                    <span className="text-sm text-primary">{challenge.points} pts</span>
                  </div>
                </div>
              </div>
              
              {/* Fixed height content area */}
              <div className="flex-1 mb-6">
                <p className="text-gray-600">{challenge.description}</p>
              </div>
              
              {/* Button always at bottom */}
              <button 
                onClick={() => onStartChallenge(challenge)}
                className="w-full bg-black text-white py-3 px-4 rounded-lg hover:bg-gray-800 transition-colors"
              >
                Start Challenge
              </button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ChallengesSection;
export { challenges };