import React from 'react';
import { motion } from 'framer-motion';
import {
  FaLinux, FaNetworkWired, FaUserShield, FaShieldVirus,
  FaSearch, FaServer, FaVirus, FaCode, FaDatabase, FaLock
} from 'react-icons/fa';

const learningPaths = [
  { icon: FaLinux, label: "Linux Fundamentals" },
  { icon: FaNetworkWired, label: "Networking" },
  { icon: FaUserShield, label: "Penetration Testing" },
  { icon: FaShieldVirus, label: "Red Teaming" },
  { icon: FaSearch, label: "SIEM" },
  { icon: FaServer, label: "Virtual Machines" },
  { icon: FaShieldVirus, label: "EDR" },
  { icon: FaSearch, label: "Threat Hunting" },
  { icon: FaVirus, label: "Malware Analysis" },
  { icon: FaCode, label: "Web Security" },
  { icon: FaDatabase, label: "Network Security" },
  { icon: FaLock, label: "Cryptography" }
];

const LearningPathsSection = ({ onShowDemo }) => {
  return (
    <section className="py-16 md:py-24 px-5 bg-gray-50">
      <div className="container mx-auto">
        <div className="text-center mb-14">
          <h2 className="section-title text-3xl md:text-4xl font-bold mb-6 text-gray-900">Learning Paths</h2>
          <p className="section-description text-gray-600 max-w-3xl mx-auto mb-6">
            Master both offensive and defensive security from zero to hero. Our comprehensive curriculum covers everything from basics to advanced techniques, with hands-on labs, learning modules, challenges, and competitions.
          </p>
          <button
            onClick={() => onShowDemo('learn')}
            className="bg-primary text-black font-bold py-2 px-6 rounded-lg hover:bg-primary-hover transition-colors inline-flex items-center gap-2"
          >
            Try Learning Demo
            <FaSearch className="text-sm" />
          </button>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 md:gap-6 card-grid">
          {learningPaths.map((path, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{
                scale: 1.05,
                boxShadow: '0 0 15px rgba(45, 212, 191, 0.3)',
                borderColor: 'rgb(45, 212, 191)'
              }}
              className="challenge-card cursor-pointer text-center p-5 relative overflow-hidden"
              onClick={() => onShowDemo('learn')}
            >
              <div className="relative z-10">
                <div className="w-12 h-12 mx-auto mb-3 bg-primary/10 rounded-full flex items-center justify-center transform transition-transform duration-300 group-hover:scale-110">
                  <path.icon className="text-primary text-xl" />
                </div>
                <div className="text-sm font-bold text-gray-900">{path.label}</div>
              </div>
              <div className="absolute inset-0 bg-gradient-to-br from-transparent to-primary/5 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default LearningPathsSection;