import React from 'react';
import { motion } from 'framer-motion';
import { FaShieldAlt } from 'react-icons/fa';

const CyberSecurityVisualization = () => {
  return (
    <div className="relative w-full h-[500px] flex items-center justify-center">
      {/* Dark background with grid pattern */}
      <div className="absolute inset-0 bg-[#0B1120] rounded-lg overflow-hidden">
        {/* Grid background */}
        <div className="absolute inset-0 opacity-20">
          <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <pattern id="grid-pattern" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" />
            </pattern>
            <rect width="100%" height="100%" fill="url(#grid-pattern)" />
          </svg>
        </div>
        
        {/* Connection dots */}
        <div className="absolute inset-0">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full w-1 h-1 bg-white opacity-40"
              style={{
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animation: `pulse ${2 + Math.random() * 3}s infinite`
              }}
            />
          ))}
        </div>
      </div>
      
      {/* Nested Squares with Glowing Borders */}
      <div className="absolute inset-0 flex items-center justify-center">
        {/* Outer square - Gold */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          className="relative w-[80%] h-[80%] rounded-lg"
        >
          <div className="absolute inset-0 rounded-lg border-2 border-[#F5B93F] opacity-80">
            {/* Corner accents */}
            <div className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-[#F5B93F]" style={{ borderRadius: '4px 0 0 0' }}></div>
            <div className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-[#F5B93F]" style={{ borderRadius: '0 4px 0 0' }}></div>
            <div className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-[#F5B93F]" style={{ borderRadius: '0 0 0 4px' }}></div>
            <div className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-[#F5B93F]" style={{ borderRadius: '0 0 4px 0' }}></div>
          </div>
          
          {/* Animated glow effect */}
          <div className="absolute inset-0 rounded-lg">
            <div className="absolute inset-0 rounded-lg border-2 border-[#F5B93F] opacity-0 animate-pulse-slow" 
              style={{ 
                boxShadow: '0 0 15px #F5B93F',
                filter: 'blur(2px)'
              }}
            ></div>
          </div>
          
          {/* Second square - Green */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="absolute inset-[8%] rounded-lg"
          >
            <div className="absolute inset-0 rounded-lg border-2 border-[#10B981] opacity-80">
              {/* Corner accents */}
              <div className="absolute top-0 left-0 w-5 h-5 border-t-2 border-l-2 border-[#10B981]" style={{ borderRadius: '4px 0 0 0' }}></div>
              <div className="absolute top-0 right-0 w-5 h-5 border-t-2 border-r-2 border-[#10B981]" style={{ borderRadius: '0 4px 0 0' }}></div>
              <div className="absolute bottom-0 left-0 w-5 h-5 border-b-2 border-l-2 border-[#10B981]" style={{ borderRadius: '0 0 0 4px' }}></div>
              <div className="absolute bottom-0 right-0 w-5 h-5 border-b-2 border-r-2 border-[#10B981]" style={{ borderRadius: '0 0 4px 0' }}></div>
            </div>
            
            {/* Animated glow effect */}
            <div className="absolute inset-0 rounded-lg">
              <div className="absolute inset-0 rounded-lg border-2 border-[#10B981] opacity-0 animate-pulse-medium" 
                style={{ 
                  boxShadow: '0 0 15px #10B981',
                  filter: 'blur(2px)',
                  animationDelay: '0.5s'
                }}
              ></div>
            </div>
            
            {/* Third square - Pink */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="absolute inset-[10%] rounded-lg"
            >
              <div className="absolute inset-0 rounded-lg border-2 border-[#EC4899] opacity-80">
                {/* Corner accents */}
                <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#EC4899]" style={{ borderRadius: '4px 0 0 0' }}></div>
                <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#EC4899]" style={{ borderRadius: '0 4px 0 0' }}></div>
                <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#EC4899]" style={{ borderRadius: '0 0 0 4px' }}></div>
                <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#EC4899]" style={{ borderRadius: '0 0 4px 0' }}></div>
              </div>
              
              {/* Animated glow effect */}
              <div className="absolute inset-0 rounded-lg">
                <div className="absolute inset-0 rounded-lg border-2 border-[#EC4899] opacity-0 animate-pulse-medium" 
                  style={{ 
                    boxShadow: '0 0 15px #EC4899',
                    filter: 'blur(2px)',
                    animationDelay: '1s'
                  }}
                ></div>
              </div>
              
              {/* Fourth square - Purple */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="absolute inset-[12%] rounded-lg"
              >
                <div className="absolute inset-0 rounded-lg border-2 border-[#8B5CF6] opacity-80">
                  {/* Corner accents */}
                  <div className="absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 border-[#8B5CF6]" style={{ borderRadius: '4px 0 0 0' }}></div>
                  <div className="absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 border-[#8B5CF6]" style={{ borderRadius: '0 4px 0 0' }}></div>
                  <div className="absolute bottom-0 left-0 w-3 h-3 border-b-2 border-l-2 border-[#8B5CF6]" style={{ borderRadius: '0 0 0 4px' }}></div>
                  <div className="absolute bottom-0 right-0 w-3 h-3 border-b-2 border-r-2 border-[#8B5CF6]" style={{ borderRadius: '0 0 4px 0' }}></div>
                </div>
                
                {/* Animated glow effect */}
                <div className="absolute inset-0 rounded-lg">
                  <div className="absolute inset-0 rounded-lg border-2 border-[#8B5CF6] opacity-0 animate-pulse-medium" 
                    style={{ 
                      boxShadow: '0 0 15px #8B5CF6',
                      filter: 'blur(2px)',
                      animationDelay: '1.5s'
                    }}
                  ></div>
                </div>
                
                {/* Fifth square - Blue */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                  className="absolute inset-[14%] rounded-lg"
                >
                  <div className="absolute inset-0 rounded-lg border-2 border-[#4A5CBA] opacity-80">
                    {/* Corner accents */}
                    <div className="absolute top-0 left-0 w-2 h-2 border-t-2 border-l-2 border-[#4A5CBA]" style={{ borderRadius: '4px 0 0 0' }}></div>
                    <div className="absolute top-0 right-0 w-2 h-2 border-t-2 border-r-2 border-[#4A5CBA]" style={{ borderRadius: '0 4px 0 0' }}></div>
                    <div className="absolute bottom-0 left-0 w-2 h-2 border-b-2 border-l-2 border-[#4A5CBA]" style={{ borderRadius: '0 0 0 4px' }}></div>
                    <div className="absolute bottom-0 right-0 w-2 h-2 border-b-2 border-r-2 border-[#4A5CBA]" style={{ borderRadius: '0 0 4px 0' }}></div>
                  </div>
                  
                  {/* Animated glow effect */}
                  <div className="absolute inset-0 rounded-lg">
                    <div className="absolute inset-0 rounded-lg border-2 border-[#4A5CBA] opacity-0 animate-pulse-medium" 
                      style={{ 
                        boxShadow: '0 0 15px #4A5CBA',
                        filter: 'blur(2px)',
                        animationDelay: '2s'
                      }}
                    ></div>
                  </div>
                  
                  {/* Center circle with shield */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ 
                      type: "spring",
                      stiffness: 260,
                      damping: 20,
                      delay: 1.2 
                    }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    {/* Outer circle with gold border */}
                    <div className="relative w-32 h-32">
                      {/* Animated rotating border */}
                      <div className="absolute inset-0 rounded-full border-2 border-[#F5B93F] animate-spin-slow"
                        style={{ 
                          boxShadow: '0 0 20px rgba(245, 185, 63, 0.5)',
                          animationDuration: '20s'
                        }}
                      ></div>
                      
                      {/* Inner circle with shield */}
                      <div className="absolute inset-[15%] rounded-full bg-[#0B1120] border-2 border-[#F5B93F] flex items-center justify-center overflow-hidden">
                        {/* Glowing background */}
                        <div className="absolute inset-0 bg-gradient-to-r from-[#4A5CBA]/20 to-[#F5B93F]/20 animate-pulse-slow"></div>
                        
                        {/* Shield icon */}
                        <div className="relative z-10">
                          <div className="text-[#F5B93F] text-4xl">
                            <FaShieldAlt />
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
      
      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            initial={{ 
              x: Math.random() * 400 - 200, 
              y: Math.random() * 400 - 200,
              opacity: 0 
            }}
            animate={{ 
              x: Math.random() * 400 - 200, 
              y: Math.random() * 400 - 200,
              opacity: [0, 0.7, 0] 
            }}
            transition={{
              duration: Math.random() * 8 + 8,
              repeat: Infinity,
              delay: Math.random() * 5
            }}
            className="absolute top-1/2 left-1/2 w-1.5 h-1.5 rounded-full"
            style={{ 
              backgroundColor: i % 5 === 0 ? '#F5B93F' : 
                              i % 5 === 1 ? '#10B981' : 
                              i % 5 === 2 ? '#EC4899' : 
                              i % 5 === 3 ? '#8B5CF6' : '#4A5CBA',
              boxShadow: i % 5 === 0 ? '0 0 5px #F5B93F' : 
                         i % 5 === 1 ? '0 0 5px #10B981' : 
                         i % 5 === 2 ? '0 0 5px #EC4899' : 
                         i % 5 === 3 ? '0 0 5px #8B5CF6' : '0 0 5px #4A5CBA'
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default CyberSecurityVisualization;
