import React from 'react';
import { motion } from 'framer-motion';
import { FaCrown, FaMedal, FaUser } from 'react-icons/fa';

const LeaderboardCard = ({ user, rank, showDetails = false }) => {
  const getRankIcon = () => {
    switch (rank) {
      case 1:
        return <FaCrown className="text-yellow-400 text-2xl" />;
      case 2:
        return <FaMedal className="text-gray-400 text-2xl" />;
      case 3:
        return <FaMedal className="text-amber-600 text-2xl" />;
      default:
        return <span className="text-gray-500 font-mono">#{rank}</span>;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white rounded-lg p-4 flex items-center gap-4 ${
        rank <= 3 ? 'border-2 border-[#88cc14]' : 'border border-gray-200'
      }`}
    >
      <div className="w-10 h-10 flex items-center justify-center">
        {getRankIcon()}
      </div>

      <div className="flex items-center gap-3 flex-1">
        <div className="w-10 h-10 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
          {user.users?.avatar_url ? (
            <img
              src={user.users.avatar_url}
              alt={user.users.username}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <FaUser className="text-[#88cc14]" />
          )}
        </div>

        <div>
          <p className="font-medium text-gray-900">{user.users?.username || 'Anonymous'}</p>
          {showDetails && (
            <p className="text-sm text-gray-500">
              {user.challenges_completed} challenges completed
            </p>
          )}
        </div>
      </div>

      <div className="text-right">
        <p className="text-[#88cc14] font-bold">{user.total_points} pts</p>
        {showDetails && (
          <p className="text-sm text-gray-500">
            Last active: {new Date(user.updated_at).toLocaleDateString()}
          </p>
        )}
      </div>
    </motion.div>
  );
};

export default LeaderboardCard;