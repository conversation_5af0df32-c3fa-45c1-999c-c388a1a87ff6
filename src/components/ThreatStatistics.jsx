import React, { useState, useEffect } from 'react';
import { FaShieldAlt, FaExclamationTriangle, FaGlobe, FaServer, FaIndustry } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import Card from '../components/ui/Card';
import RealTimeThreatService from '../services/RealTimeThreatService';

const ThreatStatistics = () => {
  const { darkMode } = useGlobalTheme();
  const [statistics, setStatistics] = useState({
    totalAttacks: 0,
    attacksByType: {},
    attacksBySeverity: {},
    attacksBySource: {},
    attacksByTarget: {},
    attacksByIndustry: {}
  });
  const [activeTab, setActiveTab] = useState('types');

  useEffect(() => {
    const threatService = RealTimeThreatService.getInstance().initialize();
    
    // Get initial statistics
    setStatistics(threatService.getStatistics());
    
    // Add listener for updates
    threatService.addListener(data => {
      setStatistics(data.statistics);
    });
    
    // Start real-time updates
    threatService.startRealTimeUpdates();
    
    // Cleanup
    return () => {
      threatService.removeListener(setStatistics);
      threatService.stopRealTimeUpdates();
    };
  }, []);

  // Helper function to get top items from an object
  const getTopItems = (obj, count = 5) => {
    return Object.entries(obj)
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([name, value]) => ({ name, value }));
  };

  // Get data for the active tab
  const getTabData = () => {
    switch (activeTab) {
      case 'types':
        return getTopItems(statistics.attacksByType);
      case 'severity':
        return getTopItems(statistics.attacksBySeverity);
      case 'sources':
        return getTopItems(statistics.attacksBySource);
      case 'targets':
        return getTopItems(statistics.attacksByTarget);
      case 'industries':
        return getTopItems(statistics.attacksByIndustry);
      default:
        return [];
    }
  };

  // Get color for severity
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'Critical':
        return '#ff0000';
      case 'High':
        return '#ff3300';
      case 'Medium':
        return '#ffaa00';
      case 'Low':
        return '#ffff00';
      default:
        return '#88cc14';
    }
  };

  // Get color for attack type
  const getTypeColor = (type) => {
    switch (type) {
      case 'Ransomware':
        return '#ff0000';
      case 'DDoS':
        return '#ff3300';
      case 'Phishing':
        return '#ff6600';
      case 'Malware':
        return '#ff9900';
      case 'SQL Injection':
        return '#ffcc00';
      case 'XSS':
        return '#ffff00';
      case 'Zero-day Exploit':
        return '#ff00ff';
      default:
        return '#88cc14';
    }
  };

  // Get bar color based on tab and item
  const getBarColor = (item) => {
    if (activeTab === 'severity') {
      return getSeverityColor(item.name);
    } else if (activeTab === 'types') {
      return getTypeColor(item.name);
    } else if (activeTab === 'sources') {
      return '#ff3300';
    } else if (activeTab === 'targets') {
      return '#3366ff';
    } else {
      return '#88cc14';
    }
  };

  // Get icon for tab
  const getTabIcon = (tab) => {
    switch (tab) {
      case 'types':
        return <FaExclamationTriangle />;
      case 'severity':
        return <FaShieldAlt />;
      case 'sources':
        return <FaGlobe />;
      case 'targets':
        return <FaServer />;
      case 'industries':
        return <FaIndustry />;
      default:
        return null;
    }
  };

  // Calculate max value for scaling
  const maxValue = Math.max(...getTabData().map(item => item.value), 1);

  return (
    <Card className="overflow-hidden">
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
            Threat Intelligence Statistics
          </h3>
          <div className="text-sm font-medium text-gray-500">
            Total Attacks: {statistics.totalAttacks}
          </div>
        </div>

        {/* Tabs */}
        <div className="flex overflow-x-auto mb-4 pb-1">
          {['types', 'severity', 'sources', 'targets', 'industries'].map(tab => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-3 py-1.5 mr-2 rounded-md text-xs font-medium flex items-center gap-1.5 whitespace-nowrap transition-colors ${
                activeTab === tab
                  ? 'bg-[#88cc14]/10 text-[#88cc14] border border-[#88cc14]/20'
                  : darkMode
                    ? 'bg-gray-800/50 text-gray-400 hover:bg-gray-800 border border-gray-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
              }`}
            >
              {getTabIcon(tab)}
              <span>{tab.charAt(0).toUpperCase() + tab.slice(1)}</span>
            </button>
          ))}
        </div>

        {/* Chart */}
        <div className="space-y-2">
          {getTabData().map((item, index) => (
            <div key={index} className="relative">
              <div className="flex justify-between items-center mb-1">
                <div className="text-sm font-medium">{item.name}</div>
                <div className="text-xs text-gray-500">{item.value}</div>
              </div>
              <div 
                className={`h-2 rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}
              >
                <div 
                  className="h-full rounded-full transition-all duration-500 ease-out"
                  style={{ 
                    width: `${(item.value / maxValue) * 100}%`,
                    backgroundColor: getBarColor(item)
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default ThreatStatistics;
