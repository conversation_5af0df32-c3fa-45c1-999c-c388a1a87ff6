import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import ThreeGlobe from 'three-globe';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import countries from '../data/countries.json';

// Earth texture
const EARTH_TEXTURE = 'https://unpkg.com/three-globe@2.28.0/example/img/earth-blue-marble.jpg';
const EARTH_BUMP = 'https://unpkg.com/three-globe@2.28.0/example/img/earth-topology.png';

const ThreatGlobe = ({ width = '100%', height = '600px' }) => {
  const { darkMode } = useGlobalTheme();
  const containerRef = useRef();
  const globeRef = useRef();
  const rendererRef = useRef();
  const sceneRef = useRef();
  const cameraRef = useRef();
  const controlsRef = useRef();
  const frameRef = useRef();
  const [attackData, setAttackData] = useState([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Generate mock attack data
  const generateAttackData = () => {
    const attacks = [];
    const attackTypes = ['Ransomware', 'DDoS', 'Phishing', 'Malware', 'SQL Injection', 'XSS'];
    const severities = ['High', 'Medium', 'Low'];
    
    // Major countries for source of attacks
    const sourceCountries = [
      { name: 'Russia', lat: 55.7558, lng: 37.6173 },
      { name: 'China', lat: 39.9042, lng: 116.4074 },
      { name: 'North Korea', lat: 39.0392, lng: 125.7625 },
      { name: 'Iran', lat: 35.6892, lng: 51.3890 },
      { name: 'United States', lat: 37.0902, lng: -95.7129 }
    ];
    
    // Target countries (more diverse)
    const targetCountries = [
      { name: 'United States', lat: 37.0902, lng: -95.7129 },
      { name: 'United Kingdom', lat: 51.5074, lng: -0.1278 },
      { name: 'Germany', lat: 51.1657, lng: 10.4515 },
      { name: 'France', lat: 46.2276, lng: 2.2137 },
      { name: 'Japan', lat: 36.2048, lng: 138.2529 },
      { name: 'South Korea', lat: 35.9078, lng: 127.7669 },
      { name: 'Australia', lat: -25.2744, lng: 133.7751 },
      { name: 'India', lat: 20.5937, lng: 78.9629 },
      { name: 'Brazil', lat: -14.2350, lng: -51.9253 },
      { name: 'Canada', lat: 56.1304, lng: -106.3468 }
    ];
    
    // Generate 20-30 attacks
    const numAttacks = Math.floor(Math.random() * 10) + 20;
    
    for (let i = 0; i < numAttacks; i++) {
      const source = sourceCountries[Math.floor(Math.random() * sourceCountries.length)];
      const target = targetCountries[Math.floor(Math.random() * targetCountries.length)];
      
      // Ensure source and target are different
      if (source.name === target.name) continue;
      
      const attackType = attackTypes[Math.floor(Math.random() * attackTypes.length)];
      const severity = severities[Math.floor(Math.random() * severities.length)];
      
      // Add some randomness to coordinates to avoid overlapping arcs
      const jitter = () => (Math.random() - 0.5) * 2;
      
      attacks.push({
        startLat: source.lat + jitter() * 0.5,
        startLng: source.lng + jitter() * 0.5,
        endLat: target.lat + jitter() * 0.5,
        endLng: target.lng + jitter() * 0.5,
        color: severity === 'High' ? '#ff0000' : severity === 'Medium' ? '#ffaa00' : '#ffff00',
        type: attackType,
        source: source.name,
        target: target.name,
        severity: severity,
        time: new Date().toISOString()
      });
    }
    
    return attacks;
  };

  // Initialize the globe
  useEffect(() => {
    if (!containerRef.current || isInitialized) return;

    // Scene
    const scene = new THREE.Scene();
    sceneRef.current = scene;
    scene.background = new THREE.Color(darkMode ? 0x000000 : 0xffffff);

    // Camera
    const camera = new THREE.PerspectiveCamera(45, containerRef.current.clientWidth / containerRef.current.clientHeight, 0.1, 1000);
    cameraRef.current = camera;
    camera.position.z = 300;

    // Renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    rendererRef.current = renderer;
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    containerRef.current.appendChild(renderer.domElement);

    // Controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controlsRef.current = controls;
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.5;
    controls.minDistance = 150;
    controls.maxDistance = 500;

    // Globe
    const globe = new ThreeGlobe()
      .globeImageUrl(EARTH_TEXTURE)
      .bumpImageUrl(EARTH_BUMP)
      .atmosphereColor(darkMode ? 'rgba(25, 118, 210, 0.7)' : 'rgba(65, 148, 240, 0.7)')
      .atmosphereAltitude(0.15)
      .arcColor('color')
      .arcStroke(0.5)
      .arcDashLength(0.4)
      .arcDashGap(0.2)
      .arcDashAnimateTime(1500)
      .arcsTransitionDuration(1000)
      .arcAltitudeAutoScale(0.4)
      .pointsData([])
      .pointColor(() => darkMode ? '#88cc14' : '#00ff00')
      .pointAltitude(0.07)
      .pointRadius(0.05)
      .pointsMerge(true)
      .hexPolygonsData(countries.features)
      .hexPolygonResolution(3)
      .hexPolygonMargin(0.7)
      .hexPolygonColor(() => darkMode ? '#1f2937' : '#2c3e50');

    globeRef.current = globe;
    scene.add(globe);

    // Add ambient light
    const ambientLight = new THREE.AmbientLight(0xbbbbbb, 0.3);
    scene.add(ambientLight);

    // Add directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    // Animation loop
    const animate = () => {
      frameRef.current = requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    animate();

    // Handle window resize
    const handleResize = () => {
      if (!containerRef.current) return;
      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };
    window.addEventListener('resize', handleResize);

    // Generate initial attack data
    setAttackData(generateAttackData());
    setIsInitialized(true);

    // Cleanup
    return () => {
      if (frameRef.current) cancelAnimationFrame(frameRef.current);
      if (rendererRef.current && rendererRef.current.domElement) {
        rendererRef.current.domElement.remove();
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [darkMode, isInitialized]);

  // Update attack data periodically
  useEffect(() => {
    if (!isInitialized) return;

    // Update arcs data
    if (globeRef.current && attackData.length > 0) {
      globeRef.current.arcsData(attackData);
    }

    // Generate new attacks every 5 seconds
    const interval = setInterval(() => {
      setAttackData(prevData => {
        // Remove some old attacks
        const remainingAttacks = prevData.slice(Math.floor(Math.random() * 5));
        // Add new attacks
        return [...remainingAttacks, ...generateAttackData().slice(0, 5)];
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [attackData, isInitialized]);

  // Update globe background color when theme changes
  useEffect(() => {
    if (sceneRef.current) {
      sceneRef.current.background = new THREE.Color(darkMode ? 0x000000 : 0xffffff);
    }
  }, [darkMode]);

  return (
    <div 
      ref={containerRef} 
      style={{ 
        width, 
        height, 
        position: 'relative',
        borderRadius: '8px',
        overflow: 'hidden'
      }}
    >
      <div 
        style={{ 
          position: 'absolute', 
          top: '10px', 
          left: '10px', 
          zIndex: 10,
          background: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(4px)',
          padding: '10px',
          borderRadius: '8px',
          border: darkMode ? '1px solid rgba(75, 85, 99, 0.5)' : '1px solid rgba(229, 231, 235, 0.5)',
          color: darkMode ? 'white' : 'black',
          fontSize: '12px'
        }}
      >
        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Threat Severity</div>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '3px' }}>
          <span style={{ display: 'inline-block', width: '10px', height: '10px', backgroundColor: '#ff0000', marginRight: '5px', borderRadius: '50%' }}></span>
          <span>High</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '3px' }}>
          <span style={{ display: 'inline-block', width: '10px', height: '10px', backgroundColor: '#ffaa00', marginRight: '5px', borderRadius: '50%' }}></span>
          <span>Medium</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ display: 'inline-block', width: '10px', height: '10px', backgroundColor: '#ffff00', marginRight: '5px', borderRadius: '50%' }}></span>
          <span>Low</span>
        </div>
      </div>
      
      <div 
        style={{ 
          position: 'absolute', 
          bottom: '10px', 
          right: '10px', 
          zIndex: 10,
          background: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(4px)',
          padding: '10px',
          borderRadius: '8px',
          border: darkMode ? '1px solid rgba(75, 85, 99, 0.5)' : '1px solid rgba(229, 231, 235, 0.5)',
          color: darkMode ? 'white' : 'black',
          fontSize: '12px',
          maxWidth: '300px'
        }}
      >
        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Live Attacks: {attackData.length}</div>
        <div style={{ maxHeight: '150px', overflowY: 'auto' }}>
          {attackData.slice(0, 5).map((attack, idx) => (
            <div key={idx} style={{ marginBottom: '5px', fontSize: '11px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ display: 'inline-block', width: '8px', height: '8px', backgroundColor: attack.color, marginRight: '5px', borderRadius: '50%' }}></span>
                <span style={{ fontWeight: 'bold' }}>{attack.type}</span>
              </div>
              <div style={{ marginLeft: '13px' }}>{attack.source} → {attack.target}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ThreatGlobe;
