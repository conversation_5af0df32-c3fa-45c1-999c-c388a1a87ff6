import React, { useState, useEffect } from 'react';
import { <PERSON>aTrophy, FaSearch, FaLaptopCode, FaCheckCircle, FaLock, FaArrowRight, FaExclamationTriangle, FaSpinner } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import threatIntelligenceService from '../../services/ThreatIntelligenceService';

// Sample hunting challenges data
const HUNTING_CHALLENGES = [
  {
    id: 'hunt-1',
    title: 'Ransomware Detection Challenge',
    difficulty: 'Beginner',
    category: 'Endpoint',
    description: 'Learn to identify ransomware activity by analyzing endpoint logs and system behavior.',
    objectives: [
      'Identify suspicious process execution patterns',
      'Detect file encryption activities',
      'Recognize ransomware persistence mechanisms'
    ],
    points: 100,
    estimatedTime: '30 minutes',
    prerequisites: [],
    unlocked: true
  },
  {
    id: 'hunt-2',
    title: 'C2 Communication Hunt',
    difficulty: 'Intermediate',
    category: 'Network',
    description: 'Hunt for command and control (C2) communications by analyzing network traffic patterns.',
    objectives: [
      'Identify beaconing patterns in network traffic',
      'Detect domain generation algorithms (DGA)',
      'Recognize encrypted C2 channels'
    ],
    points: 200,
    estimatedTime: '45 minutes',
    prerequisites: ['hunt-1'],
    unlocked: true
  },
  {
    id: 'hunt-3',
    title: 'Advanced Persistent Threat (APT) Hunt',
    difficulty: 'Advanced',
    category: 'Multi-source',
    description: 'Learn to track sophisticated APT activities across multiple data sources.',
    objectives: [
      'Correlate indicators across different log sources',
      'Identify lateral movement techniques',
      'Detect data exfiltration attempts'
    ],
    points: 350,
    estimatedTime: '60 minutes',
    prerequisites: ['hunt-1', 'hunt-2'],
    unlocked: false
  },
  {
    id: 'hunt-4',
    title: 'Supply Chain Compromise Detection',
    difficulty: 'Advanced',
    category: 'Multi-source',
    description: 'Hunt for indicators of supply chain compromises in your environment.',
    objectives: [
      'Identify suspicious software updates',
      'Detect unusual certificate usage',
      'Recognize compromised vendor access'
    ],
    points: 400,
    estimatedTime: '75 minutes',
    prerequisites: ['hunt-2', 'hunt-3'],
    unlocked: false
  }
];

// Sample user progress data
const USER_PROGRESS = {
  completedChallenges: ['hunt-1'],
  currentChallenge: 'hunt-2',
  totalPoints: 100,
  badges: [
    { id: 'beginner-hunter', name: 'Beginner Threat Hunter', icon: 'FaSearch' }
  ],
  skills: {
    'Log Analysis': 75,
    'Network Traffic Analysis': 40,
    'MITRE ATT&CK Knowledge': 60,
    'Threat Intelligence': 30
  }
};

const ThreatHuntingAcademy = ({ userLevel = 'beginner' }) => {
  const { darkMode } = useGlobalTheme();
  const [challenges, setChallenges] = useState([]);
  const [userProgress, setUserProgress] = useState(null);
  const [selectedChallenge, setSelectedChallenge] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('challenges');

  // Load real-time data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch real-time hunting challenges based on threat intelligence
        const realChallenges = await threatIntelligenceService.getHuntingChallenges();

        // If we got real challenges, use them; otherwise fall back to sample data
        if (realChallenges && realChallenges.length > 0) {
          setChallenges(realChallenges);
        } else {
          console.log('Using fallback challenge data');
          setChallenges(HUNTING_CHALLENGES);
        }

        // For now, we'll use the sample user progress data
        // In a real implementation, this would come from a user database
        setUserProgress(USER_PROGRESS);

      } catch (error) {
        console.error('Error fetching hunting challenges:', error);
        setChallenges(HUNTING_CHALLENGES);
        setUserProgress(USER_PROGRESS);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Start a challenge
  const startChallenge = (challengeId) => {
    const challenge = challenges.find(c => c.id === challengeId);
    setSelectedChallenge(challenge);
  };

  // Complete a challenge
  const completeChallenge = (challengeId) => {
    // In a real app, this would call an API to update user progress
    const updatedProgress = {
      ...userProgress,
      completedChallenges: [...userProgress.completedChallenges, challengeId],
      totalPoints: userProgress.totalPoints + (challenges.find(c => c.id === challengeId)?.points || 0)
    };

    setUserProgress(updatedProgress);
    setSelectedChallenge(null);

    // Update challenges to unlock new ones
    const updatedChallenges = challenges.map(challenge => {
      if (!challenge.unlocked && challenge.prerequisites.every(prereq =>
        updatedProgress.completedChallenges.includes(prereq)
      )) {
        return { ...challenge, unlocked: true };
      }
      return challenge;
    });

    setChallenges(updatedChallenges);
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex flex-col justify-center items-center h-64">
          <FaSpinner className="animate-spin text-blue-500 text-4xl mb-4" />
          <p className="text-gray-300">Loading threat hunting challenges based on real-time intelligence...</p>
        </div>
      </div>
    );
  }

  // Render challenge details when a challenge is selected
  if (selectedChallenge) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="mb-6">
          <button
            className="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded flex items-center text-sm"
            onClick={() => setSelectedChallenge(null)}
          >
            Back to Challenges
          </button>
        </div>

        <div className="flex items-center mb-4">
          <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-3 ${
            selectedChallenge.difficulty === 'Beginner' ? 'bg-blue-500/20' :
            selectedChallenge.difficulty === 'Intermediate' ? 'bg-yellow-500/20' :
            'bg-red-500/20'
          }`}>
            <FaSearch className={
              selectedChallenge.difficulty === 'Beginner' ? 'text-blue-500' :
              selectedChallenge.difficulty === 'Intermediate' ? 'text-yellow-500' :
              'text-red-500'
            } />
          </div>
          <div>
            <h2 className="text-xl font-bold">{selectedChallenge.title}</h2>
            <div className="flex items-center text-sm">
              <span className={`px-2 py-0.5 rounded mr-2 ${
                selectedChallenge.difficulty === 'Beginner' ? 'bg-blue-900 text-blue-200' :
                selectedChallenge.difficulty === 'Intermediate' ? 'bg-yellow-900 text-yellow-200' :
                'bg-red-900 text-red-200'
              }`}>
                {selectedChallenge.difficulty}
              </span>
              <span className="text-gray-400 mr-2">{selectedChallenge.category}</span>
              <span className="text-gray-400">{selectedChallenge.estimatedTime}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-700 rounded-lg p-4 mb-6">
          <h3 className="font-semibold mb-2">Challenge Description</h3>
          <p className="text-gray-300 mb-4">{selectedChallenge.description}</p>

          <h3 className="font-semibold mb-2">Objectives</h3>
          <ul className="list-disc list-inside space-y-1 text-gray-300 mb-4">
            {selectedChallenge.objectives.map((objective, index) => (
              <li key={index}>{objective}</li>
            ))}
          </ul>

          {/* Show real-time data if available */}
          {selectedChallenge.realData && (
            <div className="bg-blue-900 bg-opacity-30 border border-blue-800 rounded p-3 mb-4">
              <h4 className="font-semibold text-blue-300 mb-2">Based on Real Threat Intelligence</h4>

              {selectedChallenge.realData.pulseId && (
                <div className="text-sm text-gray-300 mb-2">
                  <span className="text-gray-400">Intelligence Source:</span> AlienVault OTX
                </div>
              )}

              {selectedChallenge.realData.ipAddresses && (
                <div className="text-sm text-gray-300 mb-2">
                  <span className="text-gray-400">Sample Malicious IPs:</span> {selectedChallenge.realData.ipAddresses.slice(0, 2).join(', ')}
                </div>
              )}

              {selectedChallenge.realData.tags && selectedChallenge.realData.tags.length > 0 && (
                <div className="text-sm text-gray-300 mb-2">
                  <span className="text-gray-400">Associated Tags:</span>{' '}
                  <div className="inline-flex flex-wrap gap-1 mt-1">
                    {selectedChallenge.realData.tags.slice(0, 5).map((tag, index) => (
                      <span key={index} className="bg-gray-700 px-2 py-0.5 rounded text-xs">{tag}</span>
                    ))}
                  </div>
                </div>
              )}

              {selectedChallenge.realData.references && selectedChallenge.realData.references.length > 0 && (
                <div className="text-sm text-gray-300">
                  <span className="text-gray-400">References:</span> {selectedChallenge.realData.references.length} available
                </div>
              )}
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-gray-400">
            <div>Points: {selectedChallenge.points}</div>
            <div>Estimated Time: {selectedChallenge.estimatedTime}</div>
          </div>
        </div>

        <div className="bg-gray-700 rounded-lg p-4 mb-6">
          <h3 className="font-semibold mb-4">Challenge Environment</h3>

          <div className="bg-gray-800 p-4 rounded-lg mb-4 font-mono text-sm">
            <div className="flex justify-between items-center mb-2">
              <div>Threat Hunting Console</div>
              <div className="text-green-400">● Connected</div>
            </div>
            <div className="border-t border-gray-700 pt-2">
              <p className="text-gray-400 mb-2">// This is a simulated environment. In a real implementation, this would be an interactive console.</p>
              <p className="mb-1">$ <span className="text-green-400">hunt</span> --init</p>
              <p className="text-gray-300 mb-1">Initializing hunting environment...</p>
              <p className="text-gray-300 mb-1">Loading data sources...</p>
              <p className="text-green-400 mb-1">Environment ready!</p>
              <p className="mb-1">$ <span className="text-green-400">hunt</span> --list-data-sources</p>
              <p className="text-gray-300 mb-1">Available data sources:</p>
              <p className="text-gray-300 mb-1">- Windows Event Logs (last 7 days)</p>
              <p className="text-gray-300 mb-1">- Network Traffic Captures (last 24 hours)</p>
              <p className="text-gray-300 mb-1">- Endpoint Telemetry (last 7 days)</p>
              <p className="mb-1">$ _</p>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded"
              onClick={() => completeChallenge(selectedChallenge.id)}
            >
              Complete Challenge
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-bold">Threat Hunting Academy</h2>
          <p className="text-gray-400">
            Learn and practice threat hunting skills with real-world scenarios based on actual threat intelligence.
          </p>
        </div>
        <div className="bg-gray-700 rounded-lg p-2 flex items-center">
          <FaTrophy className="text-yellow-400 mr-2" />
          <span className="font-bold">{userProgress.totalPoints} XP</span>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-700 mb-6">
        <button
          className={`flex items-center px-4 py-2 ${
            activeTab === 'challenges'
              ? 'border-b-2 border-blue-500 text-blue-500'
              : 'text-gray-400 hover:text-gray-200'
          }`}
          onClick={() => setActiveTab('challenges')}
        >
          <FaLaptopCode className="mr-2" />
          Challenges
        </button>
        <button
          className={`flex items-center px-4 py-2 ${
            activeTab === 'skills'
              ? 'border-b-2 border-blue-500 text-blue-500'
              : 'text-gray-400 hover:text-gray-200'
          }`}
          onClick={() => setActiveTab('skills')}
        >
          <FaSearch className="mr-2" />
          Skills
        </button>
        <button
          className={`flex items-center px-4 py-2 ${
            activeTab === 'badges'
              ? 'border-b-2 border-blue-500 text-blue-500'
              : 'text-gray-400 hover:text-gray-200'
          }`}
          onClick={() => setActiveTab('badges')}
        >
          <FaTrophy className="mr-2" />
          Badges
        </button>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'challenges' && (
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {challenges.map(challenge => (
              <div
                key={challenge.id}
                className={`bg-gray-700 p-4 rounded-lg border-l-4 ${
                  userProgress.completedChallenges.includes(challenge.id)
                    ? 'border-green-500'
                    : challenge.unlocked
                      ? 'border-blue-500'
                      : 'border-gray-600'
                }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold">{challenge.title}</h3>
                  {userProgress.completedChallenges.includes(challenge.id) && (
                    <FaCheckCircle className="text-green-500" />
                  )}
                </div>

                <div className="flex items-center text-xs mb-2">
                  <span className={`px-2 py-0.5 rounded mr-2 ${
                    challenge.difficulty === 'Beginner' ? 'bg-blue-900 text-blue-200' :
                    challenge.difficulty === 'Intermediate' ? 'bg-yellow-900 text-yellow-200' :
                    'bg-red-900 text-red-200'
                  }`}>
                    {challenge.difficulty}
                  </span>
                  <span className="text-gray-400">{challenge.category}</span>
                </div>

                <p className="text-sm text-gray-300 mb-3 line-clamp-2">{challenge.description}</p>

                <div className="flex justify-between items-center text-sm">
                  <div className="text-gray-400">{challenge.points} XP</div>

                  {challenge.unlocked ? (
                    <button
                      className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded flex items-center text-xs"
                      onClick={() => startChallenge(challenge.id)}
                    >
                      {userProgress.completedChallenges.includes(challenge.id) ? 'Replay' : 'Start'} <FaArrowRight className="ml-1" />
                    </button>
                  ) : (
                    <div className="flex items-center text-gray-500">
                      <FaLock className="mr-1" /> Locked
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'skills' && (
        <div>
          <div className="bg-gray-700 rounded-lg p-4 mb-6">
            <h3 className="font-semibold mb-4">Your Hunting Skills</h3>

            <div className="space-y-4">
              {Object.entries(userProgress.skills).map(([skill, level]) => (
                <div key={skill}>
                  <div className="flex justify-between items-center mb-1">
                    <div>{skill}</div>
                    <div className="text-sm text-gray-400">{level}/100</div>
                  </div>
                  <div className="w-full bg-gray-600 rounded-full h-2.5">
                    <div
                      className={`h-2.5 rounded-full ${
                        level >= 75 ? 'bg-green-600' :
                        level >= 50 ? 'bg-blue-600' :
                        level >= 25 ? 'bg-yellow-600' :
                        'bg-red-600'
                      }`}
                      style={{ width: `${level}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="font-semibold mb-4">Recommended Skill Development</h3>

            <div className="space-y-4">
              <div className="border-l-4 border-yellow-500 pl-3 py-1">
                <div className="font-medium">Network Traffic Analysis</div>
                <div className="text-sm text-gray-400 mb-1">Current Level: 40/100</div>
                <p className="text-sm text-gray-300">Focus on learning to identify C2 traffic patterns and unusual protocol behaviors.</p>
                <button className="mt-2 bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs">
                  Start Learning Path
                </button>
              </div>

              <div className="border-l-4 border-red-500 pl-3 py-1">
                <div className="font-medium">Threat Intelligence</div>
                <div className="text-sm text-gray-400 mb-1">Current Level: 30/100</div>
                <p className="text-sm text-gray-300">Improve your ability to leverage threat intelligence in hunting activities.</p>
                <button className="mt-2 bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs">
                  Start Learning Path
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'badges' && (
        <div>
          <div className="bg-gray-700 rounded-lg p-4 mb-6">
            <h3 className="font-semibold mb-4">Earned Badges</h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-800 p-4 rounded-lg text-center">
                <div className="w-16 h-16 rounded-full bg-blue-900 flex items-center justify-center mx-auto mb-2">
                  <FaSearch className="text-blue-300 text-2xl" />
                </div>
                <div className="font-medium">Beginner Threat Hunter</div>
                <div className="text-xs text-gray-400">Completed first hunt</div>
              </div>
            </div>
          </div>

          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="font-semibold mb-4">Available Badges</h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-800 p-4 rounded-lg text-center opacity-50">
                <div className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2">
                  <FaLaptopCode className="text-gray-500 text-2xl" />
                </div>
                <div className="font-medium">Network Hunter</div>
                <div className="text-xs text-gray-400">Complete C2 hunt</div>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg text-center opacity-50">
                <div className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2">
                  <FaExclamationTriangle className="text-gray-500 text-2xl" />
                </div>
                <div className="font-medium">APT Hunter</div>
                <div className="text-xs text-gray-400">Complete APT hunt</div>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg text-center opacity-50">
                <div className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2">
                  <FaTrophy className="text-gray-500 text-2xl" />
                </div>
                <div className="font-medium">Master Hunter</div>
                <div className="text-xs text-gray-400">Complete all hunts</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThreatHuntingAcademy;
