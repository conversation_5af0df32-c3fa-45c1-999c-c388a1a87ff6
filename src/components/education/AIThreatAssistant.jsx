import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaPaper<PERSON>lane, FaInfoCircle, FaSearch, FaCode, FaExclamationTriangle, FaSpinner } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import threatIntelligenceService from '../../services/ThreatIntelligenceService';
import { getAIResponse } from '../../lib/gemini';

// Sample conversation history
const INITIAL_MESSAGES = [
  {
    role: 'assistant',
    content: 'Hello! I\'m your AI Threat Intelligence Assistant. I can help you understand cybersecurity concepts, analyze threat data, and learn about attack techniques. What would you like to know about?',
    timestamp: new Date().toISOString()
  }
];

// Sample suggested questions
const SUGGESTED_QUESTIONS = [
  "What is a command and control (C2) server?",
  "Explain the MITRE ATT&CK framework",
  "How do I detect lateral movement in a network?",
  "What are common indicators of a ransomware attack?",
  "Explain the difference between signature and behavior-based detection"
];

// Sample responses for demonstration
const SAMPLE_RESPONSES = {
  "What is a command and control (C2) server?":
    "A Command and Control (C2) server is a computer controlled by an attacker or cybercriminal which is used to send commands to systems compromised by malware and receive stolen data from a target network.\n\nKey characteristics of C2 servers include:\n\n1. **Central Control Point**: They serve as the centralized management point for compromised systems (often called a botnet).\n\n2. **Communication Channels**: They use various protocols for communication, including HTTP/HTTPS, DNS, and social media platforms to blend in with normal traffic.\n\n3. **Functions**:\n   - Send commands to compromised systems\n   - Receive stolen data\n   - Distribute malware updates\n   - Control lateral movement within networks\n\n4. **Detection Challenges**: Modern C2 infrastructure often uses encryption, legitimate services, and domain fronting to evade detection.\n\nIdentifying C2 communication is a critical part of threat hunting and incident response. Common detection methods include looking for unusual outbound connections, beaconing patterns, and anomalous DNS queries.",

  "Explain the MITRE ATT&CK framework":
    "The MITRE ATT&CK (Adversarial Tactics, Techniques, and Common Knowledge) framework is a globally-accessible knowledge base of adversary tactics and techniques based on real-world observations of cyber attacks.\n\n**Key Components:**\n\n1. **Tactics**: The tactical goals that attackers try to achieve during an operation (the \"why\" of an attack technique).\n   - Examples: Initial Access, Execution, Persistence, Privilege Escalation, Defense Evasion\n\n2. **Techniques**: The specific methods used by attackers to achieve tactical goals (the \"how\").\n   - Examples: Phishing (for Initial Access), PowerShell (for Execution)\n\n3. **Sub-techniques**: More specific methods under a technique.\n   - Example: Spearphishing Attachment (sub-technique of Phishing)\n\n4. **Procedures**: Specific implementations of techniques used by threat actors.\n\n**Practical Applications:**\n\n- **Threat Intelligence**: Mapping observed attacker behavior to known patterns\n- **Detection & Analytics**: Building detection rules based on technique behaviors\n- **Red Teaming**: Simulating realistic attack scenarios\n- **Gap Analysis**: Assessing security coverage against known attack methods\n\nThe framework is organized into different matrices for Enterprise (Windows, macOS, Linux), Mobile, and ICS (Industrial Control Systems) environments.\n\nATT&CK has become the de facto standard for describing and categorizing adversary behavior and is widely used by security teams for improving defenses based on real attack patterns.",

  "How do I detect lateral movement in a network?":
    "Detecting lateral movement in a network is crucial for identifying attackers who have already gained initial access and are attempting to expand their foothold. Here are effective detection strategies:\n\n**1. Network Traffic Analysis:**\n- Monitor for unusual SMB, RDP, WMI, or PowerShell Remoting traffic between endpoints that don't typically communicate\n- Look for authentication attempts from unexpected source systems\n- Detect unusual port/protocol usage between internal systems\n\n**2. Authentication Monitoring:**\n- Track authentication events across systems (Windows Event IDs 4624, 4625, 4648, 4672)\n- Look for accounts authenticating to multiple systems in short timeframes\n- Monitor for credential dumping activities (Event ID 10 for Mimikatz)\n\n**3. Endpoint Detection:**\n- Monitor for new service creation on remote systems\n- Track process creation with command-line arguments indicating remote execution\n- Look for unusual scheduled task creation\n\n**4. Behavioral Analytics:**\n- Establish baselines of normal user and system behavior\n- Alert on deviations from typical access patterns\n- Use machine learning to identify anomalous authentication sequences\n\n**5. Key Tools and Techniques:**\n- Deploy honeypots/honeytoken accounts to detect unauthorized access attempts\n- Implement network segmentation to limit lateral movement opportunities\n- Use EDR solutions with specific lateral movement detection capabilities\n\n**Common Lateral Movement Techniques to Monitor:**\n- Pass-the-Hash/Pass-the-Ticket attacks\n- Use of PsExec, WMI, PowerShell remoting\n- Remote service creation and exploitation\n- Internal port scanning and discovery activities\n\nEffective lateral movement detection requires visibility across multiple data sources and correlation between network and endpoint telemetry.",

  "What are common indicators of a ransomware attack?":
    "**Common Indicators of a Ransomware Attack**\n\nRansomware attacks typically exhibit several observable indicators across different phases of the attack lifecycle:\n\n**Pre-encryption Indicators:**\n\n1. **Unusual Authentication Activities:**\n   - Multiple failed login attempts\n   - Authentication from unusual locations/times\n   - Sudden privileged account creation\n\n2. **Suspicious Network Traffic:**\n   - Communication with known C2 servers\n   - Unusual SMB traffic (used for file discovery)\n   - Unexpected data transfer patterns\n\n3. **System/Admin Tool Misuse:**\n   - Unexpected use of legitimate tools like PsExec, WMI, PowerShell\n   - Volume Shadow Copy deletion commands\n   - Suspicious registry modifications\n   - Disabling of security tools\n\n**Active Encryption Indicators:**\n\n1. **File System Activities:**\n   - High CPU/disk usage across multiple systems\n   - Rapid file modifications (extensions changing)\n   - Access to unusually large numbers of files\n   - Creation of ransom notes in multiple directories\n\n2. **Process Behavior:**\n   - Unusual process lineage (e.g., Office app spawning cmd.exe)\n   - Processes accessing large numbers of files\n   - Known ransomware process names or patterns\n\n**Post-encryption Indicators:**\n\n1. **System Evidence:**\n   - Changed file extensions (e.g., .encrypted, .locked, .crypted)\n   - Ransom notes (often as text files or desktop wallpaper)\n   - Inability to open common files\n   - Applications failing to start\n\n2. **Communication Evidence:**\n   - Tor installation for dark web payment sites\n   - Cryptocurrency wallet addresses in ransom notes\n   - Communication attempts to payment verification servers\n\n**Detection Best Practices:**\n- Deploy file integrity monitoring\n- Monitor for mass file type changes\n- Implement behavioral analytics for process activity\n- Create alerts for known ransomware file operations\n- Monitor for encryption command-line parameters\n\nEarly detection of these indicators can help organizations contain ransomware before widespread encryption occurs.",

  "Explain the difference between signature and behavior-based detection":
    "**Signature-Based vs. Behavior-Based Detection**\n\n**Signature-Based Detection:**\n\nSignature-based detection identifies threats by matching patterns against known malicious code, file hashes, or specific byte sequences.\n\n**Characteristics:**\n- Relies on a database of known threat signatures\n- Very effective against known, previously analyzed threats\n- Low false positive rate for identified threats\n- Fast processing and minimal system impact\n\n**Limitations:**\n- Cannot detect zero-day or previously unseen threats\n- Easily evaded by polymorphic or obfuscated malware\n- Requires constant signature updates\n- Ineffective against fileless malware\n\n**Behavior-Based Detection:**\n\nBehavior-based detection identifies threats by monitoring and analyzing the actions and behaviors of programs, processes, or users to identify suspicious or malicious activity patterns.\n\n**Characteristics:**\n- Analyzes actions rather than code patterns\n- Can detect novel and zero-day threats\n- Effective against polymorphic and obfuscated malware\n- Provides protection against fileless attacks\n\n**Limitations:**\n- Higher false positive potential\n- More resource-intensive\n- Requires baseline establishment and tuning\n- More complex to implement and manage\n\n**Key Differences:**\n\n| Aspect | Signature-Based | Behavior-Based |\n|--------|----------------|----------------|\n| Detection Method | Pattern matching | Activity analysis |\n| Zero-day Detection | Poor | Good |\n| Resource Usage | Lower | Higher |\n| False Positives | Lower | Higher |\n| Evasion Difficulty | Easier to evade | Harder to evade |\n| Implementation | Simpler | More complex |\n\n**Modern Approach:**\nToday's most effective security solutions combine both approaches:\n- Signature-based detection for efficient identification of known threats\n- Behavior-based detection for identifying novel attacks and evasive malware\n- Machine learning to improve both methods over time\n\nThis hybrid approach provides comprehensive protection against both known and emerging threats while balancing performance and accuracy."
};

const AIThreatAssistant = () => {
  const { darkMode } = useGlobalTheme();
  const [messages, setMessages] = useState(INITIAL_MESSAGES);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);
  const [suggestedQuestions, setSuggestedQuestions] = useState(SUGGESTED_QUESTIONS);
  const [aiData, setAiData] = useState(null);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load real-time data
  useEffect(() => {
    const fetchAIData = async () => {
      try {
        setDataLoading(true);
        setError(null);

        // Fetch real-time AI assistant data based on threat intelligence
        const data = await threatIntelligenceService.getAIAssistantData();

        if (data) {
          // Update suggested questions if available
          if (data.suggestedQuestions && data.suggestedQuestions.length > 0) {
            setSuggestedQuestions(data.suggestedQuestions);
          }

          // Update related learning resources if available
          if (data.relatedLearning && data.relatedLearning.length > 0) {
            setAiData({
              relatedLearning: data.relatedLearning,
              recentThreats: data.recentThreats || []
            });
          }
        }
      } catch (err) {
        console.error('Error fetching AI assistant data:', err);
        setError('Failed to load threat intelligence data.');
      } finally {
        setDataLoading(false);
      }
    };

    fetchAIData();
  }, []);

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending a message
  const handleSendMessage = async (message = inputMessage) => {
    if (!message.trim()) return;

    // Add user message to chat
    const userMessage = {
      role: 'user',
      content: message,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);
    setIsLoading(true);

    try {
      // First check if we have a sample response for common questions
      let responseContent = null;

      // Check if we have a sample response for this question
      for (const [question, answer] of Object.entries(SAMPLE_RESPONSES)) {
        if (message.toLowerCase().includes(question.toLowerCase()) ||
            question.toLowerCase().includes(message.toLowerCase())) {
          responseContent = answer;
          break;
        }
      }

      // If no sample response, call the Gemini API
      if (!responseContent) {
        try {
          const aiResponse = await getAIResponse(message);
          responseContent = aiResponse.content;
        } catch (error) {
          console.error('Error getting AI response:', error);
          // Throw the error to be handled by the outer catch block
          throw new Error("I'm having trouble connecting to my knowledge base right now. Please try again later.");
        }
      }

      const assistantMessage = {
        role: 'assistant',
        content: responseContent,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error processing message:', error);

      // Add error message with retry option
      const errorMessage = {
        role: 'assistant',
        content: error.message || "I'm sorry, I encountered an error while processing your request. Please try again later.",
        timestamp: new Date().toISOString(),
        isError: true
      };

      setMessages(prev => [...prev, errorMessage]);

      // Add retry button after error
      setTimeout(() => {
        const retryMessage = {
          role: 'system',
          content: 'Would you like to try again?',
          timestamp: new Date().toISOString(),
          isRetryPrompt: true,
          originalMessage: message
        };
        setMessages(prev => [...prev, retryMessage]);
      }, 1000);
    } finally {
      setIsTyping(false);
      setIsLoading(false);
    }
  };

  // Handle suggested question click
  const handleSuggestedQuestion = (question) => {
    handleSendMessage(question);
  };

  // Format message content with markdown-like styling
  const formatMessageContent = (content) => {
    // Split by newlines and process each line
    return content.split('\n').map((line, index) => {
      // Bold text (between ** **)
      line = line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

      // Check if line is a list item
      if (line.match(/^\d+\.\s/)) {
        return <li key={index} className="ml-5 list-decimal">{line.replace(/^\d+\.\s/, '')}</li>;
      } else if (line.match(/^-\s/)) {
        return <li key={index} className="ml-5 list-disc">{line.replace(/^-\s/, '')}</li>;
      } else if (line.match(/^\s*$/)) {
        return <br key={index} />;
      } else if (line.match(/^#{1,6}\s/)) {
        // Headers
        const level = line.match(/^(#{1,6})\s/)[1].length;
        const text = line.replace(/^#{1,6}\s/, '');
        switch(level) {
          case 1: return <h1 key={index} className="text-xl font-bold my-2">{text}</h1>;
          case 2: return <h2 key={index} className="text-lg font-bold my-2">{text}</h2>;
          case 3: return <h3 key={index} className="text-md font-bold my-2">{text}</h3>;
          default: return <h4 key={index} className="font-bold my-1">{text}</h4>;
        }
      } else {
        // Regular paragraph with inline formatting
        return <p key={index} dangerouslySetInnerHTML={{ __html: line }} />;
      }
    });
  };

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mr-3">
            <FaRobot className="text-blue-500" />
          </div>
          <div>
            <h2 className="text-xl font-bold">AI Threat Intelligence Assistant</h2>
            <p className="text-gray-400">Ask questions about cybersecurity concepts and threats</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chat Area */}
        <div className="lg:col-span-2">
          <div className="bg-gray-700 rounded-lg p-4 h-[500px] flex flex-col">
            {/* Messages Container */}
            <div className="flex-1 overflow-y-auto mb-4 space-y-4">
              {messages.map((message, index) => (
                <div
                  key={index}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  {message.isRetryPrompt ? (
                    <div className="w-full bg-gray-900/50 rounded-lg p-3 text-center">
                      <p className="text-gray-400 mb-2">{message.content}</p>
                      <button
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 rounded text-sm"
                        onClick={() => handleSendMessage(message.originalMessage)}
                      >
                        Retry
                      </button>
                    </div>
                  ) : (
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        message.role === 'user'
                          ? 'bg-blue-600 text-white'
                          : message.isError
                            ? 'bg-red-900/30 border border-red-800 text-gray-200'
                            : 'bg-gray-800 text-gray-200'
                      }`}
                    >
                      <div className="flex items-center mb-1">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                          message.role === 'user'
                            ? 'bg-blue-700'
                            : message.isError
                              ? 'bg-red-800'
                              : 'bg-gray-700'
                        }`}>
                          {message.role === 'user'
                            ? <FaUser className="text-xs" />
                            : message.isError
                              ? <FaExclamationTriangle className="text-xs" />
                              : <FaRobot className="text-xs" />
                          }
                        </div>
                        <div className="text-xs opacity-75">
                          {message.role === 'user' ? 'You' : 'AI Assistant'} • {
                            new Date(message.timestamp).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })
                          }
                        </div>
                      </div>
                      <div className="text-sm space-y-1">
                        {formatMessageContent(message.content)}
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* Typing indicator */}
              {isTyping && (
                <div className="flex justify-start">
                  <div className="bg-gray-800 text-gray-200 rounded-lg p-3 max-w-[80%]">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="mt-auto">
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSendMessage();
                }}
                className="flex items-center"
              >
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Ask about cybersecurity threats, techniques, or concepts..."
                  className="flex-1 bg-gray-800 border border-gray-600 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  className={`bg-blue-600 hover:bg-blue-700 rounded-r px-4 py-2 flex items-center ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={isLoading}
                >
                  <FaPaperPlane />
                </button>
              </form>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Suggested Questions */}
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="font-semibold mb-3 flex items-center">
              <FaInfoCircle className="mr-2 text-blue-400" />
              Suggested Questions
            </h3>
            {dataLoading ? (
              <div className="flex justify-center py-4">
                <FaSpinner className="animate-spin text-blue-500" />
              </div>
            ) : (
              <div className="space-y-2">
                {suggestedQuestions.map((question, index) => (
                  <button
                    key={index}
                    className="w-full text-left bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm transition-colors"
                    onClick={() => handleSuggestedQuestion(question)}
                    disabled={isLoading}
                  >
                    {question}
                  </button>
                ))}
              </div>
            )}
            {error && (
              <div className="text-xs text-red-400 mt-2">
                <FaExclamationTriangle className="inline-block mr-1" />
                {error}
              </div>
            )}
          </div>

          {/* Learning Resources */}
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="font-semibold mb-3 flex items-center">
              <FaSearch className="mr-2 text-green-400" />
              Related Learning
            </h3>
            {dataLoading ? (
              <div className="flex justify-center py-4">
                <FaSpinner className="animate-spin text-green-500" />
              </div>
            ) : aiData && aiData.relatedLearning ? (
              <div className="space-y-2">
                {aiData.relatedLearning.map((resource, index) => (
                  <div key={index} className="bg-gray-800 rounded p-3">
                    <h4 className="font-medium text-sm">{resource.title}</h4>
                    <p className="text-xs text-gray-400 mb-2">{resource.description}</p>
                    <button className="text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1">
                      View Module
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-2">
                <div className="bg-gray-800 rounded p-3">
                  <h4 className="font-medium text-sm">MITRE ATT&CK Framework</h4>
                  <p className="text-xs text-gray-400 mb-2">Comprehensive threat model and knowledge base</p>
                  <button className="text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1">
                    View Module
                  </button>
                </div>
                <div className="bg-gray-800 rounded p-3">
                  <h4 className="font-medium text-sm">Threat Hunting Fundamentals</h4>
                  <p className="text-xs text-gray-400 mb-2">Learn proactive threat detection techniques</p>
                  <button className="text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1">
                    View Module
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Recent Threats */}
          {aiData && aiData.recentThreats && aiData.recentThreats.length > 0 && (
            <div className="bg-gray-700 rounded-lg p-4">
              <h3 className="font-semibold mb-3 flex items-center">
                <FaExclamationTriangle className="mr-2 text-red-400" />
                Recent Threats
              </h3>
              <div className="space-y-3">
                {aiData.recentThreats.map((threat, index) => (
                  <div key={index} className="bg-gray-800 rounded p-3">
                    <h4 className="font-medium text-sm">{threat.name}</h4>
                    <p className="text-xs text-gray-400 mb-1">
                      {new Date(threat.created).toLocaleDateString()}
                    </p>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {threat.tags.slice(0, 3).map((tag, tagIndex) => (
                        <span key={tagIndex} className="bg-gray-700 px-2 py-0.5 rounded text-xs">{tag}</span>
                      ))}
                      {threat.tags.length > 3 && (
                        <span className="bg-gray-700 px-2 py-0.5 rounded text-xs">+{threat.tags.length - 3} more</span>
                      )}
                    </div>
                    <button
                      className="text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1"
                      onClick={() => handleSuggestedQuestion(`What is ${threat.name}?`)}
                    >
                      Ask About This Threat
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Tools */}
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="font-semibold mb-3 flex items-center">
              <FaCode className="mr-2 text-yellow-400" />
              Threat Analysis Tools
            </h3>
            <div className="space-y-2">
              <button className="w-full bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm flex items-center justify-between">
                <span>IOC Analyzer</span>
                <FaExclamationTriangle className="text-yellow-400" />
              </button>
              <button className="w-full bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm flex items-center justify-between">
                <span>YARA Rule Generator</span>
                <FaCode className="text-blue-400" />
              </button>
              <button className="w-full bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm flex items-center justify-between">
                <span>Threat Intelligence Search</span>
                <FaSearch className="text-green-400" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIThreatAssistant;
