import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaLaptopCode, FaBook, FaCheckCircle, FaArrowRight, FaDownload, FaCode, FaSpinner, FaShieldAlt, FaSearch } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import threatIntelligenceService from '../../services/ThreatIntelligenceService';

// Simulated Gemini API response for threat of the week
const THREAT_OF_THE_WEEK = {
  title: "Emotet Banking Trojan Resurgence",
  summary: "Emotet has reemerged with enhanced evasion techniques and is targeting financial institutions worldwide. This modular banking trojan now uses advanced polymorphic code to evade detection and has added new modules for credential theft and lateral movement.",
  technicalDetails: {
    attackVectors: ["Phishing emails with malicious Office documents", "Malicious JavaScript downloaders", "Exploitation of SMB vulnerabilities for lateral movement"],
    indicators: [
      { type: "File Hash (SHA-256)", value: "a2d31d36f5d68a85a7a7d35f6c8964c9a6061c1f46e9a1fcc6586edc49f28920" },
      { type: "Domain", value: "cdn-storage-service-secure.com" },
      { type: "IP Address", value: "**************" },
      { type: "User-Agent", value: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 Edg/92.0.902.55" }
    ],
    techniques: [
      { id: "T1566.001", name: "Phishing: Spearphishing Attachment", description: "Emotet is primarily distributed through phishing emails containing malicious Word or Excel documents with macros." },
      { id: "T1027", name: "Obfuscated Files or Information", description: "Uses heavily obfuscated code and encrypted payloads to evade detection." },
      { id: "T1210", name: "Exploitation of Remote Services", description: "Leverages EternalBlue and other SMB exploits for lateral movement within networks." }
    ]
  },
  timeline: [
    { date: "2014", event: "Initial discovery as a banking trojan" },
    { date: "2020 January", event: "Takedown by international law enforcement" },
    { date: "2021 November", event: "Reemergence with new infrastructure" },
    { date: "2023 March", event: "Addition of new evasion techniques" },
    { date: "2023 October", event: "Current campaign targeting financial sector" }
  ],
  detectionMethods: [
    "Monitor for suspicious PowerShell commands with encoded parameters",
    "Implement email filtering for documents with macros",
    "Deploy network monitoring for unusual SMB traffic",
    "Use memory-based detection for Emotet's in-memory operations"
  ],
  mitigationSteps: [
    "Disable Office macros in documents from external sources",
    "Implement application allowlisting to prevent unauthorized code execution",
    "Segment networks to limit lateral movement capabilities",
    "Deploy advanced endpoint protection with behavioral detection"
  ],
  learningObjectives: [
    "Understand polymorphic malware evasion techniques",
    "Identify Emotet's infection chain and persistence mechanisms",
    "Learn effective detection strategies for banking trojans",
    "Practice incident response for Emotet infections"
  ],
  challengeScenario: {
    description: "You are a security analyst at a financial institution that has detected potential Emotet indicators. Your task is to analyze the provided evidence, confirm the infection, and recommend containment steps.",
    evidenceFiles: ["network_capture.pcap", "suspicious_process_logs.txt", "email_headers.txt"],
    questions: [
      "Identify the initial infection vector based on the provided evidence",
      "Determine which systems have been compromised",
      "Recommend immediate containment actions",
      "Create a timeline of the attack based on log evidence"
    ]
  }
};

const ThreatOfTheWeek = () => {
  const { darkMode } = useGlobalTheme();
  const [activeSection, setActiveSection] = useState('overview');
  const [completedSections, setCompletedSections] = useState([]);
  const [showCertificate, setShowCertificate] = useState(false);

  // State for loading real-time threat data
  const [isLoading, setIsLoading] = useState(true);
  const [threatData, setThreatData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchThreatData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Fetch real-time threat of the week data
        const data = await threatIntelligenceService.getThreatOfTheWeek();
        setThreatData(data);
      } catch (err) {
        console.error('Error fetching threat of the week:', err);
        setError('Failed to load threat data. Using fallback data.');
        // Use fallback data if API fails
        setThreatData(THREAT_OF_THE_WEEK);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchThreatData();
  }, []);

  // Mark section as completed
  const completeSection = (section) => {
    if (!completedSections.includes(section)) {
      const newCompletedSections = [...completedSections, section];
      setCompletedSections(newCompletedSections);
      
      // If all sections are completed, show certificate
      const allSections = ['overview', 'technical', 'detection', 'challenge'];
      if (allSections.every(s => newCompletedSections.includes(s))) {
        setShowCertificate(true);
      }
    }
  };
  
  // Render threat content
  const renderThreatContent = () => {
    return (
      <>
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center mb-2">
            <div className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center mr-3">
              <FaExclamationTriangle className="text-red-500 text-xl" />
            </div>
            <div>
              <h2 className="text-xl font-bold">Threat of the Week: {threatData.title}</h2>
              <p className="text-gray-400">Updated {new Date().toLocaleDateString()}</p>
            </div>
          </div>
          <p className="text-gray-300 mt-3">{threatData.summary}</p>
        </div>

        {/* Progress Tracker */}
        <div className="mb-6 bg-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">Your Learning Progress</h3>
          <div className="flex flex-wrap gap-2">
            {['overview', 'technical', 'detection', 'challenge'].map((section) => (
              <div
                key={section}
                className={`px-3 py-1 rounded-full text-sm flex items-center ${
                  completedSections.includes(section)
                    ? 'bg-green-900 text-green-200'
                    : 'bg-gray-600 text-gray-300'
                }`}
              >
                {completedSections.includes(section) && <FaCheckCircle className="mr-1" />}
                {section.charAt(0).toUpperCase() + section.slice(1)}
              </div>
            ))}
          </div>
          <div className="mt-3">
            <div className="w-full bg-gray-600 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${(completedSections.length / 4) * 100}%` }}
              ></div>
            </div>
            <div className="text-right text-sm text-gray-400 mt-1">
              {completedSections.length}/4 completed
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex border-b border-gray-700 mb-6">
          <button
            className={`flex items-center px-4 py-2 ${
              activeSection === 'overview'
                ? 'border-b-2 border-blue-500 text-blue-500'
                : 'text-gray-400 hover:text-gray-200'
            }`}
            onClick={() => setActiveSection('overview')}
          >
            <FaBook className="mr-2" />
            Overview
          </button>
          <button
            className={`flex items-center px-4 py-2 ${
              activeSection === 'technical'
                ? 'border-b-2 border-blue-500 text-blue-500'
                : 'text-gray-400 hover:text-gray-200'
            }`}
            onClick={() => setActiveSection('technical')}
          >
            <FaCode className="mr-2" />
            Technical Details
          </button>
          <button
            className={`flex items-center px-4 py-2 ${
              activeSection === 'detection'
                ? 'border-b-2 border-blue-500 text-blue-500'
                : 'text-gray-400 hover:text-gray-200'
            }`}
            onClick={() => setActiveSection('detection')}
          >
            <FaLaptopCode className="mr-2" />
            Detection & Mitigation
          </button>
          <button
            className={`flex items-center px-4 py-2 ${
              activeSection === 'challenge'
                ? 'border-b-2 border-blue-500 text-blue-500'
                : 'text-gray-400 hover:text-gray-200'
            }`}
            onClick={() => setActiveSection('challenge')}
          >
            <FaExclamationTriangle className="mr-2" />
            Challenge
          </button>
        </div>

        {/* Content Sections */}
        <div className="mb-6">
          {activeSection === 'overview' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Threat Overview</h3>
              <p className="text-gray-300 mb-4">{threatData.summary}</p>

              <h4 className="font-semibold mb-2 mt-6">Historical Timeline</h4>
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-gray-600"></div>

                {/* Timeline events */}
                <div className="space-y-4 ml-10">
                  {threatData.timeline.map((event, index) => (
                    <div key={index} className="relative">
                      {/* Timeline dot */}
                      <div className="absolute -left-10 mt-1 w-6 h-6 rounded-full bg-blue-900 border-2 border-blue-500 flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-blue-300"></div>
                      </div>

                      {/* Event content */}
                      <div className="bg-gray-700 p-3 rounded">
                        <div className="font-medium">{event.date}</div>
                        <div className="text-sm text-gray-300">{event.event}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <h4 className="font-semibold mb-2 mt-6">Learning Objectives</h4>
              <ul className="list-disc list-inside space-y-2 text-gray-300">
                {threatData.learningObjectives.map((objective, index) => (
                  <li key={index}>{objective}</li>
                ))}
              </ul>

              <div className="mt-6 flex justify-end">
                <button
                  className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center"
                  onClick={() => {
                    completeSection('overview');
                    setActiveSection('technical');
                  }}
                >
                  Continue to Technical Details <FaArrowRight className="ml-2" />
                </button>
              </div>
            </div>
          )}

          {activeSection === 'technical' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Technical Analysis</h3>

              <h4 className="font-semibold mb-2">Attack Vectors</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-300 mb-4">
                {threatData.technicalDetails.attackVectors.map((vector, index) => (
                  <li key={index}>{vector}</li>
                ))}
              </ul>

              <h4 className="font-semibold mb-2 mt-6">MITRE ATT&CK Techniques</h4>
              <div className="space-y-4 mb-6">
                {threatData.technicalDetails.techniques.map((technique, index) => (
                  <div key={index} className="bg-gray-700 p-3 rounded">
                    <div className="flex justify-between">
                      <div className="font-medium">{technique.name}</div>
                      <div className="text-sm bg-blue-900 text-blue-200 px-2 py-0.5 rounded">{technique.id}</div>
                    </div>
                    <div className="text-sm text-gray-300 mt-1">{technique.description}</div>
                  </div>
                ))}
              </div>

              <h4 className="font-semibold mb-2">Indicators of Compromise (IoCs)</h4>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-700">
                    <tr>
                      <th className="px-4 py-2 text-left">Type</th>
                      <th className="px-4 py-2 text-left">Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    {threatData.technicalDetails.indicators.map((ioc, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-gray-700 bg-opacity-50' : ''}>
                        <td className="px-4 py-2">{ioc.type}</td>
                        <td className="px-4 py-2 font-mono">{ioc.value}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-6 flex justify-between">
                <button
                  className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded flex items-center"
                  onClick={() => setActiveSection('overview')}
                >
                  Back to Overview
                </button>
                <button
                  className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center"
                  onClick={() => {
                    completeSection('technical');
                    setActiveSection('detection');
                  }}
                >
                  Continue to Detection & Mitigation <FaArrowRight className="ml-2" />
                </button>
              </div>
            </div>
          )}

          {activeSection === 'detection' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Detection & Mitigation Strategies</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="font-semibold mb-3 flex items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-900 flex items-center justify-center mr-2">
                      <FaSearch className="text-blue-300" />
                    </div>
                    Detection Methods
                  </h4>
                  <ul className="list-disc list-inside space-y-2 text-gray-300">
                    {threatData.detectionMethods.map((method, index) => (
                      <li key={index}>{method}</li>
                    ))}
                  </ul>
                </div>

                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="font-semibold mb-3 flex items-center">
                    <div className="w-8 h-8 rounded-full bg-green-900 flex items-center justify-center mr-2">
                      <FaShieldAlt className="text-green-300" />
                    </div>
                    Mitigation Steps
                  </h4>
                  <ul className="list-disc list-inside space-y-2 text-gray-300">
                    {threatData.mitigationSteps.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg mb-6">
                <h4 className="font-semibold mb-3">YARA Rule Example</h4>
                <pre className="bg-gray-800 p-3 rounded font-mono text-sm overflow-x-auto">
{`rule ${threatData.title.replace(/\s+/g, '_')} {
    meta:
        description = "Detects ${threatData.title}"
        author = "XCerberus Threat Intelligence"
        date = "${new Date().toISOString().split('T')[0]}"
        hash = "${threatData.technicalDetails.indicators.find(i => i.type.includes('Hash'))?.value || 'unknown'}"
    
    strings:
        $s1 = "${threatData.technicalDetails.indicators.find(i => i.type.includes('domain'))?.value || threatData.title}" ascii wide
        
        $code1 = { 83 EC 20 53 55 56 57 8B 7C 24 34 }
        $code2 = { 68 ?? ?? ?? ?? 68 ?? ?? ?? ?? E8 ?? ?? ?? ?? }
        
    condition:
        uint16(0) == 0x5A4D and
        filesize < 2MB and
        (all of ($s*) or all of ($code*))
}`}
                </pre>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg mb-6">
                <h4 className="font-semibold mb-3">Sigma Rule Example</h4>
                <pre className="bg-gray-800 p-3 rounded font-mono text-sm overflow-x-auto">
{`title: ${threatData.title} Detection
id: ${Math.random().toString(36).substring(2, 15)}
status: experimental
description: Detects ${threatData.title} activity
references:
    - https://xcerberus.com/threat-intelligence/${threatData.title.toLowerCase().replace(/\s+/g, '-')}
author: XCerberus Threat Intelligence
date: ${new Date().toISOString().split('T')[0]}
tags:
    - attack.execution
    - attack.t1204
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        CommandLine|contains:
            - 'regsvr32.exe /s /u /i:'
            - 'cmd.exe /c powershell -w hidden -ep bypass -enc'
            - 'wscript.exe //E:'
    condition: selection
falsepositives:
    - Legitimate administrative scripts
level: high`}
                </pre>
              </div>

              <div className="mt-6 flex justify-between">
                <button
                  className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded flex items-center"
                  onClick={() => setActiveSection('technical')}
                >
                  Back to Technical Details
                </button>
                <button
                  className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center"
                  onClick={() => {
                    completeSection('detection');
                    setActiveSection('challenge');
                  }}
                >
                  Continue to Challenge <FaArrowRight className="ml-2" />
                </button>
              </div>
            </div>
          )}

          {activeSection === 'challenge' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Hands-on Challenge</h3>
              <div className="bg-gray-700 p-4 rounded-lg mb-6">
                <h4 className="font-semibold mb-2">Scenario</h4>
                <p className="text-gray-300 mb-4">{threatData.challengeScenario.description}</p>

                <h4 className="font-semibold mb-2">Available Evidence</h4>
                <div className="flex flex-wrap gap-2 mb-4">
                  {threatData.challengeScenario.evidenceFiles.map((file, index) => (
                    <div key={index} className="bg-gray-800 px-3 py-2 rounded flex items-center">
                      <FaDownload className="mr-2 text-blue-400" />
                      <span className="font-mono text-sm">{file}</span>
                    </div>
                  ))}
                </div>

                <h4 className="font-semibold mb-2">Challenge Questions</h4>
                <div className="space-y-4">
                  {threatData.challengeScenario.questions.map((question, index) => (
                    <div key={index} className="bg-gray-800 p-3 rounded">
                      <p className="mb-2">{question}</p>
                      <textarea
                        className="w-full bg-gray-700 border border-gray-600 rounded p-2 text-sm"
                        rows="2"
                        placeholder="Enter your answer here..."
                      ></textarea>
                    </div>
                  ))}
                </div>

                <div className="mt-6">
                  <button
                    className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded"
                    onClick={() => completeSection('challenge')}
                  >
                    Submit Answers
                  </button>
                </div>
              </div>

              <div className="mt-6 flex justify-between">
                <button
                  className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded flex items-center"
                  onClick={() => setActiveSection('detection')}
                >
                  Back to Detection & Mitigation
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Certificate of Completion */}
        {showCertificate && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="bg-white text-black max-w-2xl w-full p-8 rounded-lg">
              <div className="border-8 border-double border-gray-300 p-6 text-center">
                <h2 className="text-3xl font-bold mb-4">Certificate of Completion</h2>
                <p className="text-xl mb-6">This certifies that</p>
                <p className="text-2xl font-bold mb-6">Cybersecurity Student</p>
                <p className="text-xl mb-6">has successfully completed the threat analysis module</p>
                <p className="text-2xl font-bold mb-8">{threatData.title}</p>
                <div className="flex justify-between items-center mt-12">
                  <div className="text-left">
                    <p className="font-bold">XCerberus Academy</p>
                    <p>Threat Intelligence Division</p>
                  </div>
                  <div className="text-right">
                    <p>{new Date().toLocaleDateString()}</p>
                    <p>Certificate ID: TOW-{new Date().toISOString().split('T')[0]}</p>
                  </div>
                </div>
              </div>
              <div className="mt-6 flex justify-center">
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded mr-4"
                  onClick={() => setShowCertificate(false)}
                >
                  Close
                </button>
                <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center">
                  <FaDownload className="mr-2" /> Download Certificate
                </button>
              </div>
            </div>
          </div>
        )}
      </>
    );
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex flex-col justify-center items-center h-64">
          <FaSpinner className="animate-spin text-blue-500 text-4xl mb-4" />
          <p className="text-gray-300">Fetching real-time threat intelligence data...</p>
        </div>
      </div>
    );
  }
  
  // Render error state with fallback data
  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="bg-red-900 bg-opacity-20 border border-red-800 rounded-lg p-4 text-red-400 mb-6">
          <FaExclamationTriangle className="inline-block mr-2" />
          {error}
        </div>
        {threatData && renderThreatContent()}
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      {renderThreatContent()}
    </div>
  );
};

export default ThreatOfTheWeek;
