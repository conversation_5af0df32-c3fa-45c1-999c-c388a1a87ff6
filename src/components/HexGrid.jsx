import React from 'react';

function HexGrid() {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <div className="absolute w-full h-full opacity-10">
        {/* Hexagon Grid Pattern */}
        <svg width="100%" height="100%">
          <pattern id="hexagons" width="50" height="43.4" patternUnits="userSpaceOnUse">
            <path
              d="M25 0l25 14.4v28.8L25 57.8 0 43.4V14.4z"
              fill="none"
              stroke="#00f3ff"
              strokeWidth="1"
            />
          </pattern>
          <rect width="100%" height="100%" fill="url(#hexagons)" />
        </svg>
      </div>
    </div>
  );
}

export default HexGrid;