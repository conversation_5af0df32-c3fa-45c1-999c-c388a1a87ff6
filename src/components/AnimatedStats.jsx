import React from 'react';
import CountUp from 'react-countup';
import { useInView } from 'react-intersection-observer';
import { motion } from 'framer-motion';

const StatCard = ({ icon: Icon, label, value, suffix = '', delay = 0 }) => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true
  });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.6, delay }}
      className="bg-black/40 p-6 rounded-lg border border-[#00f3ff]/20 backdrop-blur-sm"
    >
      <Icon className="text-[#00f3ff] text-3xl mb-4" />
      <h3 className="text-lg text-gray-400 mb-2">{label}</h3>
      <div className="text-2xl font-bold text-[#00f3ff]">
        {inView ? (
          <CountUp end={value} duration={2.5} suffix={suffix} />
        ) : (
          '0' + suffix
        )}
      </div>
    </motion.div>
  );
};

export default StatCard;