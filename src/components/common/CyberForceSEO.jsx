import React from 'react';
import { Helmet } from 'react-helmet';

const CyberForceSEO = ({ 
  title, 
  description, 
  keywords = [], 
  ogImage = '/images/CyberForce.png',
  ogType = 'website',
  twitterCard = 'summary_large_image',
  canonicalUrl,
  structuredData
}) => {
  // Default site name
  const siteName = 'CyberForce - Advanced Cybersecurity Training Platform';
  
  // Format the title
  const formattedTitle = title ? `${title} | CyberForce` : siteName;
  
  // Default description if not provided
  const defaultDescription = 'CyberForce is a premier destination for comprehensive cyber security capacity building and training, empowering individuals and organizations to effectively navigate the complex world of cyber security.';
  
  // Use provided description or default
  const metaDescription = description || defaultDescription;
  
  // Default keywords
  const defaultKeywords = [
    'cybersecurity', 
    'cyber security training', 
    'security challenges', 
    'CTF', 
    'cyber defense', 
    'security training', 
    'Oman cybersecurity',
    'CyberForce Oman',
    'cybersecurity learning platform'
  ];
  
  // Combine default and provided keywords
  const metaKeywords = [...defaultKeywords, ...keywords].join(', ');
  
  // Get canonical URL or current URL
  const canonical = canonicalUrl || (typeof window !== 'undefined' ? window.location.href : 'https://cyberforce.om');

  // Default structured data
  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "CyberForce",
    "url": "https://cyberforce.om",
    "logo": "https://cyberforce.om/images/CyberForce.png",
    "sameAs": [
      "https://www.linkedin.com/company/cyberforceoman/",
      "https://www.instagram.com/cyberforce_om/",
      "https://x.com/cyberforce_om"
    ],
    "description": metaDescription,
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "Oman",
      "addressLocality": "Muscat"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+96871104475",
      "contactType": "customer service",
      "email": "<EMAIL>"
    }
  };

  // Use provided structured data or default
  const finalStructuredData = structuredData || defaultStructuredData;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{formattedTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={metaKeywords} />
      
      {/* Canonical Link */}
      <link rel="canonical" href={canonical} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={formattedTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:url" content={canonical} />
      <meta property="og:type" content={ogType} />
      <meta property="og:site_name" content={siteName} />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:site" content="@cyberforce_om" />
      <meta name="twitter:title" content={formattedTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Additional Meta Tags */}
      <meta name="author" content="CyberForce" />
      <meta name="language" content="English" />
      <meta name="robots" content="index, follow" />
      <meta name="googlebot" content="index, follow" />
      <meta name="revisit-after" content="7 days" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />
      <meta name="geo.region" content="OM" />
      <meta name="geo.placename" content="Muscat" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(finalStructuredData)}
      </script>
    </Helmet>
  );
};

export default CyberForceSEO;
