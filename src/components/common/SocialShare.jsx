import React, { useState } from 'react';
import { 
  FaTwitter, 
  FaFacebook, 
  FaLinkedin, 
  FaReddit, 
  FaLink, 
  FaWhatsapp,
  FaTelegram,
  FaCheck
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAnalytics } from '../../contexts/AnalyticsContext';

const SocialShare = ({ 
  url = window.location.href, 
  title = 'Check out XCerberus!', 
  description = 'XCerberus - The ultimate cybersecurity learning platform',
  hashtags = ['cybersecurity', 'hacking', 'learning'],
  showCopyLink = true,
  compact = false,
  className = ''
}) => {
  const { darkMode } = useGlobalTheme();
  const { trackEvent } = useAnalytics();
  const [copied, setCopied] = useState(false);

  // Format hashtags for Twitter
  const formattedHashtags = hashtags.map(tag => tag.startsWith('#') ? tag.substring(1) : tag).join(',');

  // Handle share click
  const handleShareClick = (platform) => {
    trackEvent('social_share', { platform, url });
  };

  // Copy link to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(url).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      trackEvent('copy_link', { url });
    });
  };

  // Generate share URLs
  const shareUrls = {
    twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}&hashtags=${formattedHashtags}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
    reddit: `https://www.reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`,
    whatsapp: `https://api.whatsapp.com/send?text=${encodeURIComponent(title + ' ' + url)}`,
    telegram: `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`
  };

  // Button style based on theme
  const buttonClass = darkMode 
    ? 'bg-[#252D4A] hover:bg-[#313e6a] text-white' 
    : 'bg-gray-100 hover:bg-gray-200 text-gray-800';

  return (
    <div className={`${className}`}>
      {!compact && <h3 className="text-lg font-semibold mb-3">Share this page</h3>}
      
      <div className="flex flex-wrap gap-2">
        {/* Twitter */}
        <a
          href={shareUrls.twitter}
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => handleShareClick('twitter')}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-4 py-2'} rounded-lg flex items-center transition-colors`}
          aria-label="Share on Twitter"
        >
          <FaTwitter className={compact ? '' : 'mr-2'} />
          {!compact && <span>Twitter</span>}
        </a>
        
        {/* Facebook */}
        <a
          href={shareUrls.facebook}
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => handleShareClick('facebook')}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-4 py-2'} rounded-lg flex items-center transition-colors`}
          aria-label="Share on Facebook"
        >
          <FaFacebook className={compact ? '' : 'mr-2'} />
          {!compact && <span>Facebook</span>}
        </a>
        
        {/* LinkedIn */}
        <a
          href={shareUrls.linkedin}
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => handleShareClick('linkedin')}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-4 py-2'} rounded-lg flex items-center transition-colors`}
          aria-label="Share on LinkedIn"
        >
          <FaLinkedin className={compact ? '' : 'mr-2'} />
          {!compact && <span>LinkedIn</span>}
        </a>
        
        {/* Reddit */}
        <a
          href={shareUrls.reddit}
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => handleShareClick('reddit')}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-4 py-2'} rounded-lg flex items-center transition-colors`}
          aria-label="Share on Reddit"
        >
          <FaReddit className={compact ? '' : 'mr-2'} />
          {!compact && <span>Reddit</span>}
        </a>
        
        {/* WhatsApp */}
        <a
          href={shareUrls.whatsapp}
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => handleShareClick('whatsapp')}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-4 py-2'} rounded-lg flex items-center transition-colors`}
          aria-label="Share on WhatsApp"
        >
          <FaWhatsapp className={compact ? '' : 'mr-2'} />
          {!compact && <span>WhatsApp</span>}
        </a>
        
        {/* Telegram */}
        <a
          href={shareUrls.telegram}
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => handleShareClick('telegram')}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-4 py-2'} rounded-lg flex items-center transition-colors`}
          aria-label="Share on Telegram"
        >
          <FaTelegram className={compact ? '' : 'mr-2'} />
          {!compact && <span>Telegram</span>}
        </a>
        
        {/* Copy Link */}
        {showCopyLink && (
          <button
            onClick={copyToClipboard}
            className={`${buttonClass} ${compact ? 'p-2' : 'px-4 py-2'} rounded-lg flex items-center transition-colors`}
            aria-label="Copy link"
          >
            {copied ? (
              <>
                <FaCheck className={compact ? '' : 'mr-2'} />
                {!compact && <span>Copied!</span>}
              </>
            ) : (
              <>
                <FaLink className={compact ? '' : 'mr-2'} />
                {!compact && <span>Copy Link</span>}
              </>
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default SocialShare;
