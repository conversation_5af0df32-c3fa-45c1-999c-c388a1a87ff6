import React from 'react';
import { Helmet } from 'react-helmet';

const SEO = ({ 
  title, 
  description, 
  keywords = [], 
  ogImage = '/og-image.jpg',
  ogType = 'website',
  twitterCard = 'summary_large_image',
  canonicalUrl,
  structuredData
}) => {
  // Default site name
  const siteName = 'XCerberus - Cybersecurity Learning Platform';
  
  // Format the title
  const formattedTitle = title ? `${title} | XCerberus` : siteName;
  
  // Default description if not provided
  const defaultDescription = 'XCerberus is a comprehensive cybersecurity learning platform offering hands-on challenges, learning modules, and team collaboration features for cybersecurity professionals.';
  
  // Use provided description or default
  const metaDescription = description || defaultDescription;
  
  // Default keywords
  const defaultKeywords = ['cybersecurity', 'hacking', 'learning', 'challenges', 'CTF', 'security', 'training'];
  
  // Combine default and provided keywords
  const metaKeywords = [...defaultKeywords, ...keywords].join(', ');
  
  // Get canonical URL or current URL
  const canonical = canonicalUrl || window.location.href;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{formattedTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={metaKeywords} />
      
      {/* Canonical Link */}
      <link rel="canonical" href={canonical} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={formattedTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:url" content={canonical} />
      <meta property="og:type" content={ogType} />
      <meta property="og:site_name" content={siteName} />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:site" content="@xcerberus" />
      <meta name="twitter:title" content={formattedTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEO;
