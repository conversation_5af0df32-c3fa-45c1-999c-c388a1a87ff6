import React from 'react';
import { Helmet } from 'react-helmet';

const ChallengeSEO = ({ challengeId, challengeTitle, challengeDescription }) => {
  // Default site name
  const siteName = 'XCerberus - Cybersecurity Learning Platform';
  
  // Format the title
  const title = challengeId 
    ? `${challengeTitle || `Challenge ${challengeId}`} | XCerberus Challenges` 
    : 'Cybersecurity Challenges | XCerberus';
  
  // Default description if not provided
  const defaultDescription = challengeId
    ? `Take on the ${challengeTitle || `Challenge ${challengeId}`} and test your cybersecurity skills on XCerberus.`
    : 'Explore and solve real-world cybersecurity challenges on XCerberus. Practice your skills, earn points, and climb the leaderboard.';
  
  // Use provided description or default
  const description = challengeDescription || defaultDescription;
  
  // Keywords
  const keywords = [
    'cybersecurity challenges', 
    'hacking challenges', 
    'CTF', 
    'capture the flag', 
    'security training', 
    'penetration testing',
    'ethical hacking',
    'cybersecurity skills',
    'security practice'
  ].join(', ');
  
  // Current URL
  const url = window.location.href;
  
  // OG Image
  const ogImage = '/images/challenge-og-image.jpg';

  // Structured data for challenges
  const structuredData = {
    "@context": "https://schema.org",
    "@type": challengeId ? "LearningResource" : "ItemList",
    ...(challengeId ? {
      "name": challengeTitle || `Challenge ${challengeId}`,
      "description": description,
      "provider": {
        "@type": "Organization",
        "name": "XCerberus",
        "url": "https://xcerberus.com"
      },
      "audience": {
        "@type": "Audience",
        "audienceType": "Cybersecurity Professionals"
      },
      "learningResourceType": "Challenge",
      "skillLevel": "Intermediate",
      "url": url
    } : {
      "name": "XCerberus Cybersecurity Challenges",
      "description": description,
      "url": url,
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Web Security Challenges",
          "url": `${window.location.origin}/challenges?category=web-security`
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Network Security Challenges",
          "url": `${window.location.origin}/challenges?category=network-security`
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Cryptography Challenges",
          "url": `${window.location.origin}/challenges?category=cryptography`
        }
      ]
    })
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      
      {/* Canonical Link */}
      <link rel="canonical" href={url} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content={siteName} />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@xcerberus" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

export default ChallengeSEO;
