import React, { Component } from 'react';
import { FaExclamationTriangle } from 'react-icons/fa';
import logService from '../../services/LogService';

class SimpleErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error
    logService.logError(
      this.props.userId || null,
      'react_error',
      error.message,
      error.stack,
      this.props.componentName || 'ErrorBoundary',
      {
        componentStack: errorInfo.componentStack,
        location: window.location.href
      }
    );
    
    this.setState({
      errorInfo
    });
    
    // Also log to console for development
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
  }

  render() {
    const { hasError, error, errorInfo } = this.state;
    const { fallback, children } = this.props;
    
    if (hasError) {
      // If a custom fallback is provided, use it
      if (fallback) {
        return fallback(error, errorInfo);
      }
      
      // Otherwise, use the default fallback UI
      return (
        <div className="p-4 m-4 border border-red-300 rounded-lg bg-red-50 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300">
          <div className="flex items-center mb-3">
            <FaExclamationTriangle className="text-red-500 mr-2 text-xl" />
            <h2 className="text-lg font-semibold">Something went wrong</h2>
          </div>
          <p className="mb-3">We've encountered an error and our team has been notified.</p>
          <details className="bg-white dark:bg-gray-800 p-2 rounded-md text-sm">
            <summary className="cursor-pointer">Technical Details</summary>
            <p className="mt-2 font-mono text-xs overflow-auto max-h-40 p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {error && error.toString()}
              <br />
              {errorInfo && errorInfo.componentStack}
            </p>
          </details>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Reload Page
          </button>
        </div>
      );
    }

    return children;
  }
}

export default SimpleErrorBoundary;
