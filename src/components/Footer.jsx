import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaGithub, FaLinkedin, FaD<PERSON>rd } from 'react-icons/fa';

function Footer() {
  return (
    <footer className="bg-[#0B1120] text-white py-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Logo and Description */}
          <div>
            <div className="flex items-center mb-4">
              <span className="font-bold text-xl">
                <span className="text-[#88cc14]">X</span>Cerberus
              </span>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Empowering cybersecurity professionals with hands-on learning and real-world challenges.
            </p>
            <div className="flex space-x-4">
              <a href="#" aria-label="Twitter" className="text-gray-400 hover:text-[#88cc14]">
                <FaTwitter />
              </a>
              <a href="#" aria-label="LinkedIn" className="text-gray-400 hover:text-[#88cc14]">
                <FaLinkedin />
              </a>
              <a href="#" aria-label="GitHub" className="text-gray-400 hover:text-[#88cc14]">
                <FaGithub />
              </a>
              <a href="#" aria-label="Discord" className="text-gray-400 hover:text-[#88cc14]">
                <FaDiscord />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-bold mb-4">Quick Links</h4>
            <ul className="space-y-2 text-gray-400 text-sm">
              <li><Link to="/learn" className="hover:text-[#88cc14]">Learning Modules</Link></li>
              <li><Link to="/challenges" className="hover:text-[#88cc14]">Challenges</Link></li>
              <li><Link to="/teams" className="hover:text-[#88cc14]">Teams</Link></li>
              <li><Link to="/leaderboard" className="hover:text-[#88cc14]">Leaderboard</Link></li>
              <li><Link to="/store" className="hover:text-[#88cc14]">Store</Link></li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h4 className="font-bold mb-4">Resources</h4>
            <ul className="space-y-2 text-gray-400 text-sm">
              <li><Link to="/blog" className="hover:text-[#88cc14]">Blog</Link></li>
              <li><Link to="/documentation" className="hover:text-[#88cc14]">Documentation</Link></li>
              <li><Link to="/faq" className="hover:text-[#88cc14]">FAQ</Link></li>
              <li><Link to="/support" className="hover:text-[#88cc14]">Support</Link></li>
              <li><Link to="/status" className="hover:text-[#88cc14]">System Status</Link></li>
            </ul>
          </div>

          {/* Contact Us */}
          <div>
            <h4 className="font-bold mb-4">Contact Us</h4>
            <p className="text-gray-400 text-sm mb-4">
              Have questions or feedback? We'd love to hear from you!
            </p>
            <a href="mailto:<EMAIL>" className="text-[#88cc14] text-sm flex items-center mb-4">
              <span className="mr-2">✉</span> <EMAIL>
            </a>
            <Link to="/contact" className="bg-[#88cc14] text-black px-4 py-2 rounded text-sm inline-block hover:bg-[#88cc14]/80">
              Contact Us
            </Link>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-800 pt-4">
          <div className="flex flex-col md:flex-row justify-between items-center text-xs">
            <p className="text-gray-400 mb-4 md:mb-0">
              © {new Date().getFullYear()} XCerberus. All rights reserved.
            </p>
            <div className="flex space-x-6 text-gray-400">
              <Link to="/privacy" className="hover:text-[#88cc14]">Privacy Policy</Link>
              <Link to="/terms" className="hover:text-[#88cc14]">Terms of Service</Link>
              <Link to="/cookies" className="hover:text-[#88cc14]">Cookie Policy</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;