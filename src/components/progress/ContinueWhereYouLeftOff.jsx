import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaArrowRight, FaTrophy, FaGraduationCap, FaClock } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useGuestProgress } from '../../contexts/GuestProgressContext';
import { CHALLENGES } from '../../pages/StaticChallenges';
import { MODULES } from '../../pages/StaticLearning';

const ContinueWhereYouLeftOff = () => {
  const { darkMode } = useGlobalTheme();
  const { lastViewed, progress } = useGuestProgress();
  
  // If no last viewed page, don't show
  if (!lastViewed.page) {
    return null;
  }
  
  // Get the last activity
  const lastActivity = progress.lastActivity;
  
  // Find the challenge or module data
  let continueItem = null;
  
  if (lastActivity) {
    if (lastActivity.type === 'challenge') {
      continueItem = CHALLENGES.find(c => c.id === lastActivity.id || c.slug === lastActivity.id);
    } else if (lastActivity.type === 'module') {
      continueItem = MODULES.find(m => m.id === lastActivity.id || m.slug === lastActivity.id);
    }
  }
  
  // Format the timestamp
  const formatTimeAgo = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  };
  
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-4 mb-6`}>
      <div className="flex justify-between items-center mb-3">
        <h3 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Continue Where You Left Off
        </h3>
        <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          <FaClock className="inline mr-1" /> {formatTimeAgo(lastViewed.timestamp)}
        </span>
      </div>
      
      {continueItem ? (
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-between`}>
          <div className="flex items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
              lastActivity.type === 'challenge' 
                ? 'bg-yellow-500/20 text-yellow-500' 
                : 'bg-blue-500/20 text-blue-500'
            }`}>
              {lastActivity.type === 'challenge' ? <FaTrophy /> : <FaGraduationCap />}
            </div>
            <div>
              <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {continueItem.title}
              </h4>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {lastActivity.type === 'challenge' ? 'Challenge' : 'Learning Module'}
              </p>
            </div>
          </div>
          
          <Link 
            to={lastActivity.type === 'challenge' 
              ? `/challenges/${continueItem.slug || continueItem.id}` 
              : `/learn/${continueItem.slug || continueItem.id}`
            }
            className="flex items-center text-[#88cc14] hover:underline"
          >
            Continue <FaArrowRight className="ml-1" />
          </Link>
        </div>
      ) : (
        <Link 
          to={lastViewed.page}
          className={`p-4 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-between`}
        >
          <div>
            <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Return to previous page
            </h4>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {lastViewed.page}
            </p>
          </div>
          
          <span className="flex items-center text-[#88cc14]">
            Continue <FaArrowRight className="ml-1" />
          </span>
        </Link>
      )}
    </div>
  );
};

export default ContinueWhereYouLeftOff;
