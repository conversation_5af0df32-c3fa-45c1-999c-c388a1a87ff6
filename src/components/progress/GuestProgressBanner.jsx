import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { FaSave, FaChartLine, FaTimes, FaSignInAlt, FaUserPlus } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useGuestProgress } from '../../contexts/GuestProgressContext';
import { useAuth } from '../../contexts/AuthContext';

const GuestProgressBanner = () => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const { getOverallProgress, achievements } = useGuestProgress();
  const [dismissed, setDismissed] = useState(false);
  
  // If user is logged in or banner is dismissed, don't show
  if (user || dismissed) {
    return null;
  }
  
  const stats = getOverallProgress();
  const hasProgress = stats.completedItems > 0;
  
  if (!hasProgress) {
    return null; // Don't show banner if no progress
  }
  
  return (
    <div className={`fixed bottom-4 right-4 z-50 w-full max-w-md ${
      darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'
    } border rounded-lg shadow-lg overflow-hidden`}>
      <div className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center mr-3">
              <FaChartLine className="text-[#88cc14]" />
            </div>
            <h3 className={`font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Your Progress
            </h3>
          </div>
          <button 
            onClick={() => setDismissed(true)}
            className={`p-1 rounded-full ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          >
            <FaTimes className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
          </button>
        </div>
        
        <div className="mb-4">
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
            You've made progress! Sign up to save your work.
          </p>
          
          <div className="grid grid-cols-2 gap-3 mb-3">
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'}`}>
              <div className="text-sm text-gray-500">Challenges</div>
              <div className="text-xl font-bold">{stats.completedChallenges}/{stats.challengeCount}</div>
              <div className="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 mt-1">
                <div 
                  className="bg-[#88cc14] h-1.5 rounded-full" 
                  style={{ width: `${stats.challengePercentage}%` }}
                ></div>
              </div>
            </div>
            
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'}`}>
              <div className="text-sm text-gray-500">Learning</div>
              <div className="text-xl font-bold">{stats.completedModules}/{stats.moduleCount}</div>
              <div className="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 mt-1">
                <div 
                  className="bg-[#88cc14] h-1.5 rounded-full" 
                  style={{ width: `${stats.modulePercentage}%` }}
                ></div>
              </div>
            </div>
          </div>
          
          {achievements.length > 0 && (
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} mb-3`}>
              <div className="text-sm text-gray-500 mb-2">Achievements Earned</div>
              <div className="flex flex-wrap gap-2">
                {achievements.slice(0, 3).map(achievement => (
                  <div 
                    key={achievement.id}
                    className="px-2 py-1 bg-yellow-500/20 text-yellow-500 rounded text-xs flex items-center"
                  >
                    {achievement.title}
                  </div>
                ))}
                {achievements.length > 3 && (
                  <div className="px-2 py-1 bg-gray-500/20 text-gray-500 rounded text-xs">
                    +{achievements.length - 3} more
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        
        <div className="flex gap-2">
          <Link 
            to="/signup" 
            className="flex-1 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium py-2 px-4 rounded-lg flex items-center justify-center"
          >
            <FaUserPlus className="mr-2" /> Sign Up
          </Link>
          <Link 
            to="/login" 
            className={`flex-1 ${
              darkMode 
                ? 'bg-[#0B1120] hover:bg-[#151F38] text-white border-gray-800' 
                : 'bg-gray-50 hover:bg-gray-100 text-gray-900 border-gray-300'
            } border py-2 px-4 rounded-lg flex items-center justify-center`}
          >
            <FaSignInAlt className="mr-2" /> Log In
          </Link>
        </div>
      </div>
    </div>
  );
};

export default GuestProgressBanner;
