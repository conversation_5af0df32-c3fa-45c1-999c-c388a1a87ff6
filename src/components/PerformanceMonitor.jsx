import React, { useState, useEffect } from 'react';

const PerformanceMonitor = ({ showInProduction = false }) => {
  const [metrics, setMetrics] = useState({
    fcp: null,
    lcp: null,
    fid: null,
    cls: null,
    ttfb: null,
    memory: null,
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only run in development or if explicitly allowed in production
    if (process.env.NODE_ENV !== 'development' && !showInProduction) {
      return;
    }

    // First Contentful Paint
    const reportFCP = (entry) => {
      setMetrics(prev => ({ ...prev, fcp: entry.startTime }));
    };

    // Largest Contentful Paint
    const reportLCP = (entry) => {
      setMetrics(prev => ({ ...prev, lcp: entry.startTime }));
    };

    // First Input Delay
    const reportFID = (entry) => {
      setMetrics(prev => ({ ...prev, fid: entry.processingStart - entry.startTime }));
    };

    // Cumulative Layout Shift
    const reportCLS = (entry) => {
      setMetrics(prev => ({ ...prev, cls: entry.value }));
    };

    // Time to First Byte
    if (performance.getEntriesByType) {
      const navEntry = performance.getEntriesByType('navigation')[0];
      if (navEntry) {
        setMetrics(prev => ({ ...prev, ttfb: navEntry.responseStart }));
      }
    }

    // Memory usage
    if (performance.memory) {
      setMetrics(prev => ({ ...prev, memory: performance.memory.usedJSHeapSize / (1024 * 1024) }));
    }

    // Register performance observers
    if ('PerformanceObserver' in window) {
      // FCP
      const fcpObserver = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          reportFCP(entry);
        }
      });
      fcpObserver.observe({ type: 'paint', buffered: true });

      // LCP
      const lcpObserver = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          reportLCP(entry);
        }
      });
      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });

      // FID
      const fidObserver = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          reportFID(entry);
        }
      });
      fidObserver.observe({ type: 'first-input', buffered: true });

      // CLS
      const clsObserver = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          reportCLS(entry);
        }
      });
      clsObserver.observe({ type: 'layout-shift', buffered: true });

      return () => {
        fcpObserver.disconnect();
        lcpObserver.disconnect();
        fidObserver.disconnect();
        clsObserver.disconnect();
      };
    }
  }, [showInProduction]);

  // Toggle visibility with keyboard shortcut (Alt+P)
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.altKey && e.key === 'p') {
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  if (!isVisible) return null;

  const formatMetric = (value, unit = 'ms') => {
    if (value === null) return 'N/A';
    return `${Math.round(value * 100) / 100} ${unit}`;
  };

  const getMetricColor = (name, value) => {
    if (value === null) return 'text-gray-400';
    
    // Thresholds based on Core Web Vitals
    switch (name) {
      case 'fcp':
        return value < 1800 ? 'text-green-500' : value < 3000 ? 'text-yellow-500' : 'text-red-500';
      case 'lcp':
        return value < 2500 ? 'text-green-500' : value < 4000 ? 'text-yellow-500' : 'text-red-500';
      case 'fid':
        return value < 100 ? 'text-green-500' : value < 300 ? 'text-yellow-500' : 'text-red-500';
      case 'cls':
        return value < 0.1 ? 'text-green-500' : value < 0.25 ? 'text-yellow-500' : 'text-red-500';
      case 'ttfb':
        return value < 200 ? 'text-green-500' : value < 500 ? 'text-yellow-500' : 'text-red-500';
      default:
        return 'text-white';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-black/80 text-white p-3 rounded-lg text-xs shadow-lg">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">Performance Metrics</h3>
        <button 
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>FCP:</span>
          <span className={getMetricColor('fcp', metrics.fcp)}>{formatMetric(metrics.fcp)}</span>
        </div>
        <div className="flex justify-between">
          <span>LCP:</span>
          <span className={getMetricColor('lcp', metrics.lcp)}>{formatMetric(metrics.lcp)}</span>
        </div>
        <div className="flex justify-between">
          <span>FID:</span>
          <span className={getMetricColor('fid', metrics.fid)}>{formatMetric(metrics.fid)}</span>
        </div>
        <div className="flex justify-between">
          <span>CLS:</span>
          <span className={getMetricColor('cls', metrics.cls)}>{formatMetric(metrics.cls, '')}</span>
        </div>
        <div className="flex justify-between">
          <span>TTFB:</span>
          <span className={getMetricColor('ttfb', metrics.ttfb)}>{formatMetric(metrics.ttfb)}</span>
        </div>
        {metrics.memory !== null && (
          <div className="flex justify-between">
            <span>Memory:</span>
            <span>{formatMetric(metrics.memory, 'MB')}</span>
          </div>
        )}
      </div>
      <div className="mt-2 text-gray-400 text-[10px]">
        Press Alt+P to toggle
      </div>
    </div>
  );
};

export default PerformanceMonitor;
