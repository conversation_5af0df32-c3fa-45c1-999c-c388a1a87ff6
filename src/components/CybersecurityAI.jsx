import React, { useEffect } from 'react';
import { useChat } from '../contexts/ChatContext';
import ChatWindow from './chat/ChatWindow';
import ChatButton from './chat/ChatButton';

function CybersecurityAI() {
  const { state, toggleChat } = useChat();
  const { isOpen } = state;

  // <PERSON>le keyboard shortcut to open chat (Alt+A)
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.altKey && e.key === 'a') {
        toggleChat();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [toggleChat]);

  return isOpen ? <ChatWindow /> : <ChatButton />;
}

export default CybersecurityAI;