import React from 'react';
import { Link } from 'react-router-dom';

function FallbackPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white p-4">
      <div className="text-center max-w-md">
        <h1 className="text-4xl font-bold mb-4 text-green-400">XCerberus</h1>
        <p className="text-xl mb-8">Cybersecurity Learning Platform</p>
        
        <div className="bg-gray-800 p-6 rounded-lg mb-8">
          <h2 className="text-xl font-bold mb-4">Navigation Options</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Link 
              to="/simplified-dashboard" 
              className="bg-green-500 hover:bg-green-600 text-black font-bold py-2 px-4 rounded-lg transition-colors"
            >
              Dashboard
            </Link>
            <Link 
              to="/learn/modules" 
              className="bg-green-500 hover:bg-green-600 text-black font-bold py-2 px-4 rounded-lg transition-colors"
            >
              Learning Modules
            </Link>
            <Link 
              to="/challenges" 
              className="bg-green-500 hover:bg-green-600 text-black font-bold py-2 px-4 rounded-lg transition-colors"
            >
              Challenges
            </Link>
            <Link 
              to="/test" 
              className="bg-green-500 hover:bg-green-600 text-black font-bold py-2 px-4 rounded-lg transition-colors"
            >
              Test Page
            </Link>
          </div>
        </div>
        
        <p className="text-sm text-gray-400">
          If you're seeing this page, there might be an issue with the main landing page.
        </p>
      </div>
    </div>
  );
}

export default FallbackPage;
