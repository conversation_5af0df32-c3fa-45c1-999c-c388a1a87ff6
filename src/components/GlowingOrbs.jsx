import React, { useEffect, useRef } from 'react';

const GlowingOrbs = () => {
  const containerRef = useRef(null);
  
  useEffect(() => {
    const container = containerRef.current;
    const orbs = [];
    const numOrbs = 3; // Reduced number of orbs
    
    for (let i = 0; i < numOrbs; i++) {
      const orb = document.createElement('div');
      orb.className = 'absolute rounded-full blur-xl';
      orb.style.width = '300px';
      orb.style.height = '300px';
      orb.style.background = `radial-gradient(circle, rgba(45,212,191,0.1) 0%, rgba(45,212,191,0) 70%)`;
      orb.style.transform = `translate(-50%, -50%)`;
      
      const animation = orb.animate([
        { 
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`
        },
        { 
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`
        }
      ], {
        duration: 15000 + (Math.random() * 10000),
        iterations: Infinity,
        direction: 'alternate',
        easing: 'ease-in-out'
      });
      
      container.appendChild(orb);
      orbs.push({ element: orb, animation });
    }
    
    return () => {
      orbs.forEach(({ element, animation }) => {
        animation.cancel();
        element.remove();
      });
    };
  }, []);

  return (
    <div ref={containerRef} className="fixed inset-0 overflow-hidden pointer-events-none z-0" />
  );
};

export default GlowingOrbs;