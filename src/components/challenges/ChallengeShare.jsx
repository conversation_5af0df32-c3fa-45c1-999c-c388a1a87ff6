import React from 'react';
import { FaTwitter, FaFacebook, FaLinkedin, FaLink, FaCheck } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAnalytics } from '../../contexts/AnalyticsContext';

const ChallengeShare = ({ challengeId, challengeTitle, compact = false }) => {
  const { darkMode } = useGlobalTheme();
  const { trackEvent } = useAnalytics();
  const [copied, setCopied] = React.useState(false);
  
  // Generate share URL
  const shareUrl = `${window.location.origin}/challenges/${challengeId}`;
  
  // Generate share text
  const shareTitle = challengeTitle 
    ? `Check out the "${challengeTitle}" challenge on XCerberus!` 
    : `Check out this cybersecurity challenge on XCerberus!`;
  
  // Format hashtags for Twitter
  const hashtags = ['cybersecurity', 'hacking', 'challenge', 'xcerberus'];
  const formattedHashtags = hashtags.join(',');
  
  // Generate share URLs
  const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareTitle)}&hashtags=${formattedHashtags}`;
  const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
  const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
  
  // Handle share click
  const handleShareClick = (platform) => {
    trackEvent('challenge_share', { platform, challengeId });
  };
  
  // Copy link to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(shareUrl).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      trackEvent('challenge_copy_link', { challengeId });
    });
  };
  
  // Button style based on theme
  const buttonClass = darkMode 
    ? 'bg-[#252D4A] hover:bg-[#313e6a] text-white' 
    : 'bg-gray-100 hover:bg-gray-200 text-gray-800';
  
  return (
    <div className={compact ? 'flex gap-2' : 'mt-4'}>
      {!compact && <h3 className="text-sm font-medium mb-2">Share this challenge:</h3>}
      
      <div className={`flex ${compact ? '' : 'gap-2'}`}>
        {/* Twitter */}
        <a
          href={twitterUrl}
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => handleShareClick('twitter')}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-3 py-2'} rounded-lg flex items-center transition-colors`}
          aria-label="Share on Twitter"
        >
          <FaTwitter className={compact ? 'text-sm' : 'mr-2'} />
          {!compact && <span className="text-sm">Twitter</span>}
        </a>
        
        {/* Facebook */}
        <a
          href={facebookUrl}
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => handleShareClick('facebook')}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-3 py-2'} rounded-lg flex items-center transition-colors ml-2`}
          aria-label="Share on Facebook"
        >
          <FaFacebook className={compact ? 'text-sm' : 'mr-2'} />
          {!compact && <span className="text-sm">Facebook</span>}
        </a>
        
        {/* LinkedIn */}
        <a
          href={linkedinUrl}
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => handleShareClick('linkedin')}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-3 py-2'} rounded-lg flex items-center transition-colors ml-2`}
          aria-label="Share on LinkedIn"
        >
          <FaLinkedin className={compact ? 'text-sm' : 'mr-2'} />
          {!compact && <span className="text-sm">LinkedIn</span>}
        </a>
        
        {/* Copy Link */}
        <button
          onClick={copyToClipboard}
          className={`${buttonClass} ${compact ? 'p-2' : 'px-3 py-2'} rounded-lg flex items-center transition-colors ml-2`}
          aria-label="Copy link"
        >
          {copied ? (
            <>
              <FaCheck className={compact ? 'text-sm' : 'mr-2'} />
              {!compact && <span className="text-sm">Copied!</span>}
            </>
          ) : (
            <>
              <FaLink className={compact ? 'text-sm' : 'mr-2'} />
              {!compact && <span className="text-sm">Copy Link</span>}
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ChallengeShare;
