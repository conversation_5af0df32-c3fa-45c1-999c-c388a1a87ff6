import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaSearch, FaFilter, FaSpinner, FaLock, FaCoins, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';

const ChallengeList = () => {
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const [challenges, setChallenges] = useState([]);
  const [filteredChallenges, setFilteredChallenges] = useState([]);
  const [categories, setCategories] = useState([]);
  const [difficulties, setDifficulties] = useState([]);
  const [completedChallenges, setCompletedChallenges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    category: '',
    difficulty: '',
    completed: '',
    premium: ''
  });

  // Fetch challenges and filter options
  useEffect(() => {
    const fetchChallenges = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch challenges
        const { data: challengesData, error: challengesError } = await supabase
          .from('challenges')
          .select(`
            id,
            title,
            description,
            category_id,
            difficulty_id,
            points,
            is_premium,
            is_business,
            requires_coins,
            coin_cost,
            category:challenge_categories(name),
            difficulty:challenge_difficulty_levels(name)
          `)
          .eq('is_active', true)
          .order('difficulty_id', { ascending: true })
          .order('points', { ascending: true });

        if (challengesError) throw challengesError;

        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('challenge_categories')
          .select('id, name')
          .order('display_order', { ascending: true });

        if (categoriesError) throw categoriesError;

        // Fetch difficulties
        const { data: difficultiesData, error: difficultiesError } = await supabase
          .from('challenge_difficulty_levels')
          .select('id, name')
          .order('display_order', { ascending: true });

        if (difficultiesError) throw difficultiesError;

        // Fetch completed challenges
        const { data: completionsData, error: completionsError } = await supabase
          .from('challenge_completions')
          .select('challenge_id')
          .eq('user_id', user.id);

        if (completionsError) throw completionsError;

        // Set state
        setChallenges(challengesData);
        setFilteredChallenges(challengesData);
        setCategories(categoriesData);
        setDifficulties(difficultiesData);
        setCompletedChallenges(completionsData.map(c => c.challenge_id));
      } catch (error) {
        console.error('Error fetching challenges:', error);
        setError('Failed to load challenges. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchChallenges();
    }
  }, [user]);

  // Apply filters and search
  useEffect(() => {
    if (!challenges.length) return;

    let filtered = [...challenges];

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(challenge => 
        challenge.category.name === filters.category
      );
    }

    // Apply difficulty filter
    if (filters.difficulty) {
      filtered = filtered.filter(challenge => 
        challenge.difficulty.name === filters.difficulty
      );
    }

    // Apply completion filter
    if (filters.completed === 'completed') {
      filtered = filtered.filter(challenge => 
        completedChallenges.includes(challenge.id)
      );
    } else if (filters.completed === 'incomplete') {
      filtered = filtered.filter(challenge => 
        !completedChallenges.includes(challenge.id)
      );
    }

    // Apply premium filter
    if (filters.premium === 'premium') {
      filtered = filtered.filter(challenge => 
        challenge.is_premium && !challenge.is_business
      );
    } else if (filters.premium === 'business') {
      filtered = filtered.filter(challenge => 
        challenge.is_business
      );
    } else if (filters.premium === 'free') {
      filtered = filtered.filter(challenge => 
        !challenge.is_premium && !challenge.is_business
      );
    }

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(challenge => 
        challenge.title.toLowerCase().includes(query) ||
        challenge.description.toLowerCase().includes(query)
      );
    }

    setFilteredChallenges(filtered);
  }, [challenges, filters, searchQuery, completedChallenges]);

  // Handle filter change
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      category: '',
      difficulty: '',
      completed: '',
      premium: ''
    });
    setSearchQuery('');
  };

  // Check if user has access to a challenge
  const hasAccess = (challenge) => {
    if (challenge.is_business && profile?.subscription_tier !== 'business') {
      return false;
    }
    
    if (challenge.is_premium && profile?.subscription_tier === 'free') {
      return false;
    }
    
    return true;
  };

  // Render loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <FaSpinner className="animate-spin text-4xl text-[#88cc14]" />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`${darkMode ? 'bg-red-900/20 border-red-900/30' : 'bg-red-100 border-red-200'} border rounded-lg p-6 text-center`}>
        <h2 className="text-2xl font-bold text-red-500 mb-4">Error</h2>
        <p className={`${darkMode ? 'text-red-300' : 'text-red-700'}`}>{error}</p>
        <button 
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div>
      {/* Filters and Search */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                className={`w-full pl-10 pr-4 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                placeholder="Search challenges..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <FaSearch className="absolute left-3 top-3 text-gray-400" />
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            {/* Category Filter */}
            <select
              className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.name}>
                  {category.name}
                </option>
              ))}
            </select>

            {/* Difficulty Filter */}
            <select
              className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
              value={filters.difficulty}
              onChange={(e) => handleFilterChange('difficulty', e.target.value)}
            >
              <option value="">All Difficulties</option>
              {difficulties.map(difficulty => (
                <option key={difficulty.id} value={difficulty.name}>
                  {difficulty.name}
                </option>
              ))}
            </select>

            {/* Completion Filter */}
            <select
              className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
              value={filters.completed}
              onChange={(e) => handleFilterChange('completed', e.target.value)}
            >
              <option value="">All Status</option>
              <option value="completed">Completed</option>
              <option value="incomplete">Incomplete</option>
            </select>

            {/* Premium Filter */}
            <select
              className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
              value={filters.premium}
              onChange={(e) => handleFilterChange('premium', e.target.value)}
            >
              <option value="">All Tiers</option>
              <option value="free">Free</option>
              <option value="premium">Premium</option>
              <option value="business">Business</option>
            </select>

            {/* Reset Filters */}
            <button
              onClick={resetFilters}
              className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-[#252D4A] hover:bg-[#313e6a] text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-800'} border border-transparent`}
            >
              Reset
            </button>
          </div>
        </div>
      </div>

      {/* Challenge Count */}
      <div className="mb-4">
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Showing {filteredChallenges.length} of {challenges.length} challenges
        </p>
      </div>

      {/* Challenge Grid */}
      {filteredChallenges.length === 0 ? (
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
          <FaExclamationTriangle className="mx-auto text-4xl mb-4 text-yellow-500" />
          <h2 className="text-xl font-bold mb-2">No Challenges Found</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Try adjusting your filters or search query.
          </p>
          <button
            onClick={resetFilters}
            className="mt-4 px-4 py-2 theme-button-primary rounded-lg"
          >
            Reset Filters
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredChallenges.map(challenge => (
            <div 
              key={challenge.id}
              className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden transition-transform hover:transform hover:scale-[1.02]`}
            >
              <div className={`p-4 ${
                completedChallenges.includes(challenge.id)
                  ? darkMode ? 'bg-[#88cc14]/10' : 'bg-[#88cc14]/5'
                  : ''
              }`}>
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-bold">{challenge.title}</h3>
                  <div className="flex items-center">
                    {challenge.is_business && (
                      <span className={`ml-2 px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                        Business
                      </span>
                    )}
                    {challenge.is_premium && !challenge.is_business && (
                      <span className={`ml-2 px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                        Premium
                      </span>
                    )}
                    {challenge.requires_coins && (
                      <span className={`ml-2 px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-yellow-900/30 text-yellow-300' : 'bg-yellow-100 text-yellow-800'}`}>
                        <FaCoins className="mr-1" /> {challenge.coin_cost}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 mb-3">
                  <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                    {challenge.category.name}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                    {challenge.difficulty.name}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-green-900/20 text-green-300' : 'bg-green-100 text-green-800'}`}>
                    {challenge.points} Points
                  </span>
                </div>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 line-clamp-2`}>
                  {challenge.description}
                </p>
                <div className="flex justify-between items-center">
                  {completedChallenges.includes(challenge.id) && (
                    <span className="flex items-center text-[#88cc14]">
                      <FaCheckCircle className="mr-1" /> Completed
                    </span>
                  )}
                  {!completedChallenges.includes(challenge.id) && (
                    <span className="invisible">Placeholder</span>
                  )}
                  {hasAccess(challenge) ? (
                    <Link
                      to={`/challenges/${challenge.id}`}
                      className="px-4 py-2 theme-button-primary rounded-lg"
                    >
                      View Challenge
                    </Link>
                  ) : (
                    <Link
                      to="/pricing"
                      className={`px-4 py-2 rounded-lg flex items-center ${darkMode ? 'bg-[#252D4A] text-gray-300' : 'bg-gray-200 text-gray-700'}`}
                    >
                      <FaLock className="mr-2" /> Upgrade
                    </Link>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChallengeList;
