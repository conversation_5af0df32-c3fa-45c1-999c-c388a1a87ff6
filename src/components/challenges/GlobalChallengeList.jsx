import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { <PERSON>aLock, FaCrown, FaBuilding, FaFilter, FaSearch, FaStar, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useAuth } from '../../contexts/AuthContext';
import { SUBSCRIPTION_TIERS } from '../../config/subscriptionTiers';
import UpgradeBanner from '../access/UpgradeBanner';
import LockedItem from '../access/LockedItem';

// Sample challenge data - in a real app, this would come from an API
const sampleChallenges = [
  {
    id: 'web-1',
    title: 'SQL Injection Basics',
    description: 'Learn the fundamentals of SQL injection attacks and how to prevent them.',
    difficulty: 'beginner',
    category: 'web',
    points: 100,
    completionRate: 78,
    estimatedTime: '30 min',
    tier: 'free'
  },
  {
    id: 'web-2',
    title: 'Cross-Site Scripting (XSS)',
    description: 'Understand how XSS attacks work and implement proper defenses.',
    difficulty: 'beginner',
    category: 'web',
    points: 150,
    completionRate: 65,
    estimatedTime: '45 min',
    tier: 'free'
  },
  {
    id: 'network-1',
    title: 'Network Traffic Analysis',
    description: 'Analyze network packets to identify suspicious activities.',
    difficulty: 'intermediate',
    category: 'network',
    points: 200,
    completionRate: 52,
    estimatedTime: '1 hour',
    tier: 'free'
  },
  {
    id: 'crypto-1',
    title: 'Cryptography Basics',
    description: 'Learn about encryption, hashing, and cryptographic attacks.',
    difficulty: 'beginner',
    category: 'crypto',
    points: 120,
    completionRate: 70,
    estimatedTime: '40 min',
    tier: 'premium'
  },
  {
    id: 'forensics-1',
    title: 'Digital Forensics Investigation',
    description: 'Recover and analyze digital evidence from compromised systems.',
    difficulty: 'intermediate',
    category: 'forensics',
    points: 250,
    completionRate: 45,
    estimatedTime: '1.5 hours',
    tier: 'premium'
  },
  {
    id: 'binary-1',
    title: 'Buffer Overflow Exploitation',
    description: 'Exploit buffer overflow vulnerabilities in C programs.',
    difficulty: 'advanced',
    category: 'binary',
    points: 300,
    completionRate: 30,
    estimatedTime: '2 hours',
    tier: 'premium'
  },
  {
    id: 'web-3',
    title: 'Advanced Web Application Attacks',
    description: 'Master complex web attacks including CSRF, SSRF, and more.',
    difficulty: 'advanced',
    category: 'web',
    points: 350,
    completionRate: 25,
    estimatedTime: '2.5 hours',
    tier: 'business'
  },
  {
    id: 'network-2',
    title: 'Enterprise Network Penetration',
    description: 'Conduct a full penetration test on an enterprise network.',
    difficulty: 'advanced',
    category: 'network',
    points: 400,
    completionRate: 20,
    estimatedTime: '3 hours',
    tier: 'business'
  }
];

/**
 * GlobalChallengeList Component
 *
 * A unified challenge list that adapts based on user subscription level.
 * Provides appropriate access controls and upgrade prompts for different content tiers.
 */
const GlobalChallengeList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    subscriptionLevel,
    hasAccess,
    getRemainingContent,
    trackContentUsage,
    requiresCoins,
    hasEnoughCoins,
    purchaseWithCoins
  } = useSubscription();

  // State for challenges
  const [challenges, setChallenges] = useState([]);
  const [filteredChallenges, setFilteredChallenges] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [remainingChallenges, setRemainingChallenges] = useState(0);
  const [showFilters, setShowFilters] = useState(false);

  // Load challenges
  useEffect(() => {
    // In a real app, this would be an API call
    setChallenges(sampleChallenges);
    setFilteredChallenges(sampleChallenges);

    // Get remaining challenges if user is logged in
    if (user) {
      const { remaining } = getRemainingContent('challenges');
      setRemainingChallenges(remaining);
    }
  }, [getRemainingContent, user]);

  // Filter challenges when filters change
  useEffect(() => {
    let filtered = challenges;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(challenge => challenge.category === selectedCategory);
    }

    // Filter by difficulty
    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(challenge => challenge.difficulty === selectedDifficulty);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        challenge =>
          challenge.title.toLowerCase().includes(query) ||
          challenge.description.toLowerCase().includes(query)
      );
    }

    setFilteredChallenges(filtered);
  }, [challenges, selectedCategory, selectedDifficulty, searchQuery]);

  // Handle challenge selection
  const handleChallengeSelect = (challenge) => {
    // If user is not logged in, redirect to login
    if (!user) {
      navigate('/login');
      return;
    }

    // Check if user has access to this challenge
    const hasAccessToChallenge = hasAccess('challenges', challenge.tier);

    if (!hasAccessToChallenge) {
      // Show upgrade prompt
      return;
    }

    // For free users, track challenge usage
    if (subscriptionLevel === SUBSCRIPTION_TIERS.FREE) {
      const stillHasAccess = trackContentUsage('challenges', challenge.id);
      if (!stillHasAccess) {
        // User has reached their limit
        return;
      }
    }

    // Check if challenge requires coins (for premium users)
    if (subscriptionLevel === SUBSCRIPTION_TIERS.PREMIUM &&
        requiresCoins('challenge', challenge.difficulty)) {

      if (!hasEnoughCoins(challenge.points)) {
        // Not enough coins
        return;
      }

      // Purchase with coins
      purchaseWithCoins(challenge.points);
    }

    // Navigate to challenge
    navigate(`/challenges/${challenge.id}`);
  };

  // Get subscription tier icon
  const getTierIcon = (tier) => {
    switch(tier) {
      case SUBSCRIPTION_TIERS.PREMIUM:
        return <FaCrown className="text-yellow-400" />;
      case SUBSCRIPTION_TIERS.BUSINESS:
        return <FaBuilding className="text-blue-400" />;
      default:
        return null;
    }
  };

  // Render tier badge
  const renderTierBadge = (tier) => {
    if (!tier || tier === SUBSCRIPTION_TIERS.FREE) return null;

    const icon = getTierIcon(tier);
    const isAccessible = hasAccess('challenges', tier);

    return (
      <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
        isAccessible
          ? tier === SUBSCRIPTION_TIERS.PREMIUM
            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
          : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
      }`}>
        {icon}
        <span className="ml-1">{tier.charAt(0).toUpperCase() + tier.slice(1)}</span>
        {!isAccessible && <FaLock className="ml-1 text-xs" />}
      </span>
    );
  };

  // Render difficulty badge
  const renderDifficultyBadge = (difficulty) => {
    let bgColor, textColor;

    switch(difficulty) {
      case 'beginner':
        bgColor = 'bg-green-100 dark:bg-green-900';
        textColor = 'text-green-800 dark:text-green-300';
        break;
      case 'intermediate':
        bgColor = 'bg-yellow-100 dark:bg-yellow-900';
        textColor = 'text-yellow-800 dark:text-yellow-300';
        break;
      case 'advanced':
        bgColor = 'bg-red-100 dark:bg-red-900';
        textColor = 'text-red-800 dark:text-red-300';
        break;
      default:
        bgColor = 'bg-gray-100 dark:bg-gray-900';
        textColor = 'text-gray-800 dark:text-gray-300';
    }

    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${bgColor} ${textColor}`}>
        {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
      </span>
    );
  };

  // Render category badge
  const renderCategoryBadge = (category) => {
    let bgColor, textColor, icon;

    switch(category) {
      case 'web':
        bgColor = 'bg-purple-100 dark:bg-purple-900';
        textColor = 'text-purple-800 dark:text-purple-300';
        break;
      case 'network':
        bgColor = 'bg-blue-100 dark:bg-blue-900';
        textColor = 'text-blue-800 dark:text-blue-300';
        break;
      case 'crypto':
        bgColor = 'bg-green-100 dark:bg-green-900';
        textColor = 'text-green-800 dark:text-green-300';
        break;
      case 'forensics':
        bgColor = 'bg-yellow-100 dark:bg-yellow-900';
        textColor = 'text-yellow-800 dark:text-yellow-300';
        break;
      case 'binary':
        bgColor = 'bg-red-100 dark:bg-red-900';
        textColor = 'text-red-800 dark:text-red-300';
        break;
      default:
        bgColor = 'bg-gray-100 dark:bg-gray-900';
        textColor = 'text-gray-800 dark:text-gray-300';
    }

    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${bgColor} ${textColor}`}>
        {category.charAt(0).toUpperCase() + category.slice(1)}
      </span>
    );
  };

  // Render challenge card
  const renderChallengeCard = (challenge) => {
    // For non-logged in users, only free challenges appear accessible
    const isAccessible = user ? hasAccess('challenges', challenge.tier) : challenge.tier === SUBSCRIPTION_TIERS.FREE;

    return (
      <div
        key={challenge.id}
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-all duration-200 ${
          isAccessible
            ? 'hover:shadow-lg cursor-pointer transform hover:-translate-y-1'
            : 'opacity-75'
        }`}
        onClick={() => handleChallengeSelect(challenge)}
      >
        <div className="p-5">
          <div className="flex justify-between items-start">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              {challenge.title}
              {renderTierBadge(challenge.tier)}
            </h3>

            {!isAccessible && (
              <FaLock className="text-gray-400 dark:text-gray-500" />
            )}
          </div>

          <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
            {challenge.description}
          </p>

          <div className="mt-4 flex flex-wrap gap-2">
            {renderDifficultyBadge(challenge.difficulty)}
            {renderCategoryBadge(challenge.category)}
          </div>

          <div className="mt-4 flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center">
              <FaStar className="text-yellow-400 mr-1" />
              <span>{challenge.points} points</span>
            </div>
            <span>{challenge.estimatedTime}</span>
          </div>

          {!isAccessible && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                className="w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(user ? '/pricing' : '/login');
                }}
              >
                {user ? 'Upgrade to Access' : 'Sign In to Access'}
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Filters section */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Challenges</h2>
              <button
                className="ml-4 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 md:hidden"
                onClick={() => setShowFilters(!showFilters)}
              >
                <FaFilter className="mr-2" />
                {showFilters ? <FaChevronUp /> : <FaChevronDown />}
              </button>
            </div>

            <div className="mt-4 md:mt-0 relative">
              <div className="flex items-center">
                <div className="relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaSearch className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Search challenges"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Mobile filters */}
          <div className={`md:hidden mt-4 ${showFilters ? 'block' : 'hidden'}`}>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Category</label>
                <select
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="all">All Categories</option>
                  <option value="web">Web</option>
                  <option value="network">Network</option>
                  <option value="crypto">Crypto</option>
                  <option value="forensics">Forensics</option>
                  <option value="binary">Binary</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Difficulty</label>
                <select
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                >
                  <option value="all">All Difficulties</option>
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
              </div>
            </div>
          </div>

          {/* Desktop filters */}
          <div className="hidden md:flex md:items-center md:space-x-4 mt-4">
            <div>
              <label className="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">Category:</label>
              <select
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="all">All Categories</option>
                <option value="web">Web</option>
                <option value="network">Network</option>
                <option value="crypto">Crypto</option>
                <option value="forensics">Forensics</option>
                <option value="binary">Binary</option>
              </select>
            </div>

            <div>
              <label className="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">Difficulty:</label>
              <select
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
              >
                <option value="all">All Difficulties</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Login prompt for non-logged in users */}
      {!user && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <UpgradeBanner
            title="Sign In to Track Your Progress"
            description="Create an account or sign in to save your progress and access more challenges."
            buttonText="Sign In"
            onButtonClick={() => navigate('/login')}
            variant="prominent"
          />
        </div>
      )}

      {/* Subscription banner for free users */}
      {user && subscriptionLevel === SUBSCRIPTION_TIERS.FREE && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <UpgradeBanner
            title="Unlock All Challenges"
            description={`You have access to ${remainingChallenges} more challenges with your free account. Upgrade to unlock all content.`}
            buttonText="View Plans"
            onButtonClick={() => navigate('/pricing')}
          />
        </div>
      )}

      {/* Challenge grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredChallenges.map(challenge => renderChallengeCard(challenge))}
        </div>

        {filteredChallenges.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">No challenges match your filters. Try adjusting your search criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GlobalChallengeList;
