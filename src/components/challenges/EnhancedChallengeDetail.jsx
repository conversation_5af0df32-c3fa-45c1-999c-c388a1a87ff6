import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { FaArrowLeft, FaSpinner, FaClock, FaStar, FaLock, FaCheckCircle, FaExclamationTriangle, FaTrophy, FaCoins, FaLightbulb } from 'react-icons/fa';
import { useEnhancedChallenge } from '../../contexts/EnhancedChallengeContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import SimpleUpgradeBanner from '../access/SimpleUpgradeBanner';

const EnhancedChallengeDetail = () => {
  const { darkMode } = useGlobalTheme();
  const { user, profile } = useAuth();
  const { 
    getChallengeById, 
    submitChallengeAttempt, 
    purchaseHint,
    rateChallenge,
    isChallengeCompleted,
    getChallengeAttempts,
    hasUserPurchasedHint,
    loading: contextLoading, 
    error: contextError 
  } = useEnhancedChallenge();
  const { challengeId } = useParams();
  const navigate = useNavigate();
  
  const [challenge, setChallenge] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [solution, setSolution] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [submitResult, setSubmitResult] = useState(null);
  const [userRating, setUserRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [submittingRating, setSubmittingRating] = useState(false);
  const [attempts, setAttempts] = useState([]);
  const [completed, setCompleted] = useState(false);
  const [purchasingHint, setPurchasingHint] = useState(false);
  const [purchasedHints, setPurchasedHints] = useState({});

  // Fetch challenge data
  useEffect(() => {
    const fetchChallenge = async () => {
      try {
        setLoading(true);
        const challengeData = await getChallengeById(challengeId);
        setChallenge(challengeData);
        
        // Set completion status
        setCompleted(isChallengeCompleted(challengeData.id));
        
        // Set attempts
        setAttempts(getChallengeAttempts(challengeData.id));
        
        // Set purchased hints
        const hintStatus = {};
        if (challengeData.hints) {
          challengeData.hints.forEach(hint => {
            hintStatus[hint.id] = hasUserPurchasedHint(hint.id);
          });
        }
        setPurchasedHints(hintStatus);
      } catch (error) {
        console.error('Error fetching challenge:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchChallenge();
  }, [challengeId, getChallengeById, isChallengeCompleted, getChallengeAttempts, hasUserPurchasedHint]);

  // Handle solution submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!solution.trim()) return;
    
    try {
      setSubmitting(true);
      setSubmitResult(null);
      
      const result = await submitChallengeAttempt(challenge.id, solution);
      
      setSubmitResult(result);
      
      if (result.isCorrect) {
        setCompleted(true);
        // Update attempts
        setAttempts(getChallengeAttempts(challenge.id));
      }
    } catch (error) {
      console.error('Error submitting solution:', error);
      setSubmitResult({
        success: false,
        message: error.message
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle hint purchase
  const handlePurchaseHint = async (hintId, coinCost) => {
    try {
      setPurchasingHint(true);
      
      const result = await purchaseHint(hintId, coinCost);
      
      if (result.success) {
        setPurchasedHints(prev => ({
          ...prev,
          [hintId]: true
        }));
      }
    } catch (error) {
      console.error('Error purchasing hint:', error);
    } finally {
      setPurchasingHint(false);
    }
  };

  // Handle rating submission
  const handleRatingSubmit = async () => {
    try {
      if (!user) {
        navigate('/login', { state: { from: `/challenges/${challengeId}` } });
        return;
      }
      
      if (userRating === 0) return;
      
      setSubmittingRating(true);
      await rateChallenge(challenge.id, userRating, feedback);
      setSubmittingRating(false);
    } catch (error) {
      console.error('Error submitting rating:', error);
      setSubmittingRating(false);
    }
  };

  // Format time (minutes to hours and minutes)
  const formatTime = (minutes) => {
    if (!minutes) return 'N/A';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours === 0) return `${mins} min`;
    if (mins === 0) return `${hours} hr`;
    return `${hours} hr ${mins} min`;
  };

  // Check if user has access to this challenge
  const hasAccess = () => {
    if (!challenge) return false;
    if (!challenge.is_premium && !challenge.is_business) return true;
    if (challenge.is_premium && !challenge.is_business && profile?.subscription_tier === 'premium') return true;
    if (profile?.subscription_tier === 'business') return true;
    return false;
  };

  // Render loading state
  if (loading || contextLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <FaSpinner className="animate-spin text-4xl text-[#88cc14]" />
      </div>
    );
  }

  // Render error state
  if (error || contextError) {
    return (
      <div className={`${darkMode ? 'bg-red-900/20 border-red-900/30' : 'bg-red-100 border-red-200'} border rounded-lg p-6 text-center`}>
        <h2 className="text-2xl font-bold text-red-500 mb-4">Error</h2>
        <p className={`${darkMode ? 'text-red-300' : 'text-red-700'}`}>{error || contextError}</p>
        <button 
          onClick={() => navigate('/challenges')}
          className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
        >
          Back to Challenges
        </button>
      </div>
    );
  }

  // Render challenge not found
  if (!challenge) {
    return (
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
        <FaExclamationTriangle className="mx-auto text-4xl mb-4 text-yellow-500" />
        <h2 className="text-xl font-bold mb-2">Challenge Not Found</h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          The challenge you're looking for doesn't exist or you don't have access to it.
        </p>
        <Link
          to="/challenges"
          className="mt-4 px-4 py-2 theme-button-primary rounded-lg inline-block"
        >
          Browse Challenges
        </Link>
      </div>
    );
  }

  // Render premium content banner if user doesn't have access
  if (!hasAccess()) {
    return (
      <div>
        <div className="mb-6">
          <Link to="/challenges" className="flex items-center text-[#88cc14] hover:underline">
            <FaArrowLeft className="mr-2" /> Back to Challenges
          </Link>
        </div>
        
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
          <h1 className="text-2xl font-bold mb-4">{challenge.title}</h1>
          
          <div className="flex flex-wrap gap-2 mb-4">
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
              {challenge.category.name}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
              {challenge.difficulty.name}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-green-900/20 text-green-300' : 'bg-green-100 text-green-800'}`}>
              {challenge.type.name}
            </span>
            {challenge.estimated_time && (
              <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
                <FaClock className="mr-1" /> {formatTime(challenge.estimated_time)}
              </span>
            )}
            {challenge.is_business && (
              <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                Business
              </span>
            )}
            {challenge.is_premium && !challenge.is_business && (
              <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                Premium
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-4 mb-6">
            <div className="flex items-center">
              <FaTrophy className="mr-1 text-yellow-500" /> {challenge.points} points
            </div>
            <div className="flex items-center">
              <FaCoins className="mr-1 text-yellow-500" /> {challenge.coin_reward} coins
            </div>
          </div>
          
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
            {challenge.description}
          </p>
          
          <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6 text-center mb-6`}>
            <FaLock className="mx-auto text-4xl mb-4 text-[#88cc14]" />
            <h2 className="text-xl font-bold mb-2">Premium Content</h2>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
              This challenge is available to {challenge.is_business ? 'Business' : 'Premium'} subscribers only.
            </p>
            <Link
              to="/pricing"
              className="px-4 py-2 theme-button-primary rounded-lg inline-block"
            >
              Upgrade Now
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Parse content
  const content = challenge.content?.[0]?.content || {};
  
  return (
    <div>
      <div className="mb-6">
        <Link to="/challenges" className="flex items-center text-[#88cc14] hover:underline">
          <FaArrowLeft className="mr-2" /> Back to Challenges
        </Link>
      </div>
      
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
        <h1 className="text-2xl font-bold mb-4">{challenge.title}</h1>
        
        <div className="flex flex-wrap gap-2 mb-4">
          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
            {challenge.category.name}
          </span>
          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
            {challenge.difficulty.name}
          </span>
          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-green-900/20 text-green-300' : 'bg-green-100 text-green-800'}`}>
            {challenge.type.name}
          </span>
          {challenge.estimated_time && (
            <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
              <FaClock className="mr-1" /> {formatTime(challenge.estimated_time)}
            </span>
          )}
          {challenge.is_business && (
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
              Business
            </span>
          )}
          {challenge.is_premium && !challenge.is_business && (
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
              Premium
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-4 mb-6">
          <div className="flex items-center">
            <FaTrophy className="mr-1 text-yellow-500" /> {challenge.points} points
          </div>
          <div className="flex items-center">
            <FaCoins className="mr-1 text-yellow-500" /> {challenge.coin_reward} coins
          </div>
        </div>
        
        {/* Challenge Description */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Description</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {challenge.description}
          </p>
        </div>
        
        {/* Challenge Content */}
        <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6 mb-6`}>
          <h2 className="text-xl font-semibold mb-4">Challenge Details</h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
            {content.description || 'Challenge details will be displayed here.'}
          </p>
          
          {content.flag_format && (
            <div className="mb-4">
              <h3 className="text-lg font-medium mb-2">Flag Format</h3>
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-3 font-mono`}>
                {content.flag_format}
              </div>
            </div>
          )}
        </div>
        
        {/* Hints */}
        {challenge.hints && challenge.hints.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Hints</h2>
            <div className="space-y-4">
              {challenge.hints.map((hint, index) => (
                <div 
                  key={hint.id}
                  className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4`}
                >
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium">Hint {index + 1}</h3>
                    {purchasedHints[hint.id] ? (
                      <span className="text-[#88cc14] flex items-center">
                        <FaCheckCircle className="mr-1" /> Purchased
                      </span>
                    ) : (
                      <button
                        onClick={() => handlePurchaseHint(hint.id, hint.coin_cost)}
                        disabled={purchasingHint || (profile?.coins || 0) < hint.coin_cost}
                        className={`flex items-center px-3 py-1 rounded-lg ${
                          (profile?.coins || 0) < hint.coin_cost
                            ? darkMode ? 'bg-gray-800 text-gray-500' : 'bg-gray-200 text-gray-500'
                            : 'theme-button-primary'
                        }`}
                      >
                        {purchasingHint ? (
                          <>
                            <FaSpinner className="animate-spin mr-1" /> Purchasing...
                          </>
                        ) : (
                          <>
                            <FaCoins className="mr-1" /> {hint.coin_cost} coins
                          </>
                        )}
                      </button>
                    )}
                  </div>
                  
                  {purchasedHints[hint.id] ? (
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {hint.hint}
                    </p>
                  ) : (
                    <div className="flex items-center justify-center py-4">
                      <FaLightbulb className={`text-2xl ${darkMode ? 'text-gray-600' : 'text-gray-400'}`} />
                      <span className={`ml-2 ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                        Purchase this hint to reveal it
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Solution Submission */}
        {!completed ? (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Submit Solution</h2>
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <input
                  type="text"
                  value={solution}
                  onChange={(e) => setSolution(e.target.value)}
                  placeholder="Enter flag (e.g., flag{...})"
                  className={`w-full p-3 rounded-lg ${
                    darkMode
                      ? 'bg-[#252D4A] border-gray-700 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } border`}
                  required
                />
              </div>
              <button
                type="submit"
                disabled={submitting}
                className="w-full px-4 py-3 theme-button-primary rounded-lg flex items-center justify-center"
              >
                {submitting ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" /> Submitting...
                  </>
                ) : (
                  'Submit Solution'
                )}
              </button>
            </form>
            
            {/* Submission Result */}
            {submitResult && (
              <div className={`mt-4 p-4 rounded-lg ${
                submitResult.isCorrect
                  ? darkMode ? 'bg-green-900/20 border-green-900/30 text-green-300' : 'bg-green-100 border-green-200 text-green-800'
                  : darkMode ? 'bg-red-900/20 border-red-900/30 text-red-300' : 'bg-red-100 border-red-200 text-red-800'
              } border`}>
                <div className="flex items-center">
                  {submitResult.isCorrect ? (
                    <FaCheckCircle className="mr-2 text-green-500" />
                  ) : (
                    <FaExclamationTriangle className="mr-2 text-red-500" />
                  )}
                  <p>{submitResult.message}</p>
                </div>
                
                {submitResult.isCorrect && (
                  <div className="mt-2 flex flex-col sm:flex-row gap-4">
                    <div className="flex items-center">
                      <FaTrophy className="mr-1 text-yellow-500" /> {submitResult.pointsEarned} points earned
                    </div>
                    <div className="flex items-center">
                      <FaCoins className="mr-1 text-yellow-500" /> {submitResult.coinsEarned} coins earned
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className={`mb-6 p-4 rounded-lg ${darkMode ? 'bg-green-900/20 border-green-900/30' : 'bg-green-100 border-green-200'} border`}>
            <div className="flex items-center">
              <FaCheckCircle className="mr-2 text-green-500" />
              <p className={`${darkMode ? 'text-green-300' : 'text-green-800'}`}>
                You have successfully completed this challenge!
              </p>
            </div>
          </div>
        )}
        
        {/* Previous Attempts */}
        {attempts.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Your Attempts</h2>
            <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4`}>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className={`${darkMode ? 'border-gray-700' : 'border-gray-300'} border-b`}>
                      <th className="text-left py-2 px-4">Date</th>
                      <th className="text-left py-2 px-4">Solution</th>
                      <th className="text-left py-2 px-4">Result</th>
                    </tr>
                  </thead>
                  <tbody>
                    {attempts.map((attempt, index) => (
                      <tr 
                        key={attempt.id || index}
                        className={`${darkMode ? 'border-gray-700' : 'border-gray-300'} border-b last:border-b-0`}
                      >
                        <td className="py-2 px-4">
                          {new Date(attempt.created_at).toLocaleString()}
                        </td>
                        <td className="py-2 px-4 font-mono">
                          {attempt.solution}
                        </td>
                        <td className="py-2 px-4">
                          {attempt.is_correct ? (
                            <span className="text-green-500 flex items-center">
                              <FaCheckCircle className="mr-1" /> Correct
                            </span>
                          ) : (
                            <span className="text-red-500 flex items-center">
                              <FaExclamationTriangle className="mr-1" /> Incorrect
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        
        {/* Challenge Rating */}
        <div className="mt-8 pt-6 border-t border-gray-700">
          <h2 className="text-xl font-semibold mb-4">Rate this Challenge</h2>
          <div className="flex items-center mb-4">
            <span className="mr-2">Rating:</span>
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setUserRating(star)}
                  onMouseEnter={() => setHoverRating(star)}
                  onMouseLeave={() => setHoverRating(0)}
                  className="text-2xl focus:outline-none"
                >
                  <FaStar
                    className={`${
                      (hoverRating || userRating) >= star
                        ? 'text-yellow-400'
                        : darkMode ? 'text-gray-700' : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>
          
          {/* Feedback textarea (shows when rating is selected) */}
          {userRating > 0 && (
            <div>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                placeholder="Share your feedback about this challenge (optional)"
                className={`w-full p-3 rounded-lg ${
                  darkMode
                    ? 'bg-[#252D4A] border-gray-700 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } border`}
                rows={3}
              ></textarea>
              <button
                onClick={handleRatingSubmit}
                disabled={submittingRating}
                className="mt-2 px-4 py-2 theme-button-primary rounded-lg"
              >
                {submittingRating ? (
                  <>
                    <FaSpinner className="inline animate-spin mr-2" /> Submitting...
                  </>
                ) : (
                  'Submit Rating'
                )}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedChallengeDetail;
