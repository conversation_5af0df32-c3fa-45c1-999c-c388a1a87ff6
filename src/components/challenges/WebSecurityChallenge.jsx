import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaC<PERSON>, FaLightbulb, FaExclamationTriangle } from 'react-icons/fa';
import { webSecurityAI } from '../../lib/webSecurityAI';

const WebSecurityChallenge = ({ onComplete }) => {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState([]);
  const [step, setStep] = useState(0);
  const [success, setSuccess] = useState(false);
  const [context, setContext] = useState('');
  const [monitor, setMonitor] = useState(null);
  const [hints, setHints] = useState([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [feedback, setFeedback] = useState(null);

  useEffect(() => {
    const initializeChallenge = async () => {
      try {
        // Initialize challenge monitor with mock user ID if not authenticated
        const userId = 'local-user-' + Math.random().toString(36).substring(2, 9);
        const challengeMonitor = webSecurityAI.createMonitor(
          'web-security-101',
          userId
        );
        setMonitor(challengeMonitor);

        // Generate initial challenge
        const challenge = await webSecurityAI.challengeGenerator.generateChallenge(
          'Medium',
          'Web Security'
        );

        setContext(challenge.description);
        setOutput([{ type: 'system', text: 'Challenge environment initialized...' }]);
      } catch (error) {
        console.error('Error initializing challenge:', error);
        setOutput(prev => [...prev, {
          type: 'error',
          text: 'Error initializing challenge. Please try again.'
        }]);
      }
    };

    initializeChallenge();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim() || isAnalyzing) return;

    try {
      setIsAnalyzing(true);
      setOutput(prev => [...prev, { type: 'input', text: `$ ${input}` }]);

      // Track attempt
      const attemptResult = await monitor?.trackAttempt(input, null);

      // Validate solution
      const result = await webSecurityAI.challengeGenerator.validateSolution(
        'web-security-101',
        input
      );

      if (result.success) {
        setOutput(prev => [
          ...prev,
          { type: 'success', text: result.feedback }
        ]);

        if (step < 2) {
          // Generate next challenge
          const nextChallenge = await webSecurityAI.challengeGenerator.generateChallenge(
            'Hard',
            'Web Security'
          );
          setContext(nextChallenge.description);
          setStep(prev => prev + 1);
        } else {
          setSuccess(true);
          onComplete?.();
        }
      } else {
        setOutput(prev => [
          ...prev,
          { type: 'error', text: 'Attempt failed.' }
        ]);

        // Generate contextual hint
        const hint = await webSecurityAI.challengeGenerator.generateHint(
          'web-security-101',
          {
            userId: monitor?.userId,
            currentApproach: input,
            timeSpent: Math.round((Date.now() - monitor?.startTime) / 1000 / 60),
            failureReasons: ['Invalid payload', 'Missing parameter']
          }
        );

        setHints(prev => [...prev, hint]);
      }

      // Analyze attempt pattern
      if (attemptResult) {
        if (attemptResult.bruteForce) {
          setFeedback({
            type: 'warning',
            message: 'Try a more methodical approach instead of brute forcing.'
          });
        } else if (attemptResult.methodical) {
          setFeedback({
            type: 'success',
            message: 'Good systematic approach! Keep exploring variations.'
          });
        } else if (attemptResult.innovative) {
          setFeedback({
            type: 'success',
            message: 'Interesting approach! Creative thinking is key.'
          });
        }
      }
    } catch (error) {
      console.error('Error processing attempt:', error);
      setOutput(prev => [...prev, {
        type: 'error',
        text: 'Error processing attempt. Please try again.'
      }]);
    } finally {
      setInput('');
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="bg-black rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <FaCode className="text-[#88cc14]" />
            Web Security Challenge
          </h3>
          <div className="text-[#88cc14] font-bold">
            Step {step + 1}/3
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Challenge Context */}
        <div className="bg-gray-900 p-4 rounded-lg mb-4">
          <pre className="text-gray-300 whitespace-pre-wrap font-mono text-sm">
            {context}
          </pre>
        </div>

        {/* Terminal Output */}
        <div className="h-64 mb-6 bg-gray-900 rounded-lg border border-gray-800 overflow-y-auto p-4 font-mono">
          {output.map((line, i) => (
            <div
              key={i}
              className={`mb-2 ${
                line.type === 'input' ? 'text-blue-400' :
                line.type === 'success' ? 'text-green-400' :
                line.type === 'error' ? 'text-red-400' :
                line.type === 'hint' ? 'text-yellow-400' :
                'text-gray-300'
              }`}
            >
              {line.text}
            </div>
          ))}
          {isAnalyzing && (
            <div className="text-[#88cc14] animate-pulse">
              Analyzing payload...
            </div>
          )}
        </div>

        {/* Input Form */}
        <form onSubmit={handleSubmit} className="mb-6">
          <div className="flex gap-2">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="flex-1 bg-gray-900 text-white px-4 py-2 rounded-lg border border-gray-800 focus:outline-none focus:border-[#88cc14]"
              placeholder="Enter your payload..."
              disabled={isAnalyzing}
            />
            <button
              type="submit"
              disabled={isAnalyzing}
              className="bg-[#88cc14] text-black px-6 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors disabled:opacity-50"
            >
              Execute
            </button>
          </div>
        </form>

        {/* Hints */}
        {hints.length > 0 && (
          <div className="bg-yellow-900/20 border border-yellow-900/30 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-yellow-500 mb-2">
              <FaLightbulb />
              <span className="font-bold">Hints</span>
            </div>
            <div className="space-y-2">
              {hints.map((hint, index) => (
                <p key={index} className="text-gray-300 text-sm">
                  {hint}
                </p>
              ))}
            </div>
          </div>
        )}

        {/* Feedback */}
        {feedback && (
          <div className={`p-4 rounded-lg mb-6 ${
            feedback.type === 'warning' 
              ? 'bg-yellow-900/20 border-yellow-900/30 text-yellow-500'
              : 'bg-green-900/20 border-green-900/30 text-green-500'
          }`}>
            <p>{feedback.message}</p>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-gray-900 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <FaExclamationTriangle className="text-[#88cc14]" />
            <span className="text-white font-bold">Challenge Guidelines</span>
          </div>
          <ul className="text-gray-400 space-y-2 text-sm">
            <li>• Analyze the vulnerable code carefully</li>
            <li>• Craft your payload methodically</li>
            <li>• Consider different attack vectors</li>
            <li>• Use hints when stuck</li>
          </ul>
        </div>
      </div>

      {/* Success Modal */}
      {success && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-gray-900 p-8 rounded-lg max-w-md text-center"
          >
            <div className="w-16 h-16 bg-[#88cc14] rounded-full flex items-center justify-center mx-auto mb-6">
              <FaCode className="text-2xl text-black" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">
              Challenge Complete!
            </h3>
            <p className="text-gray-400 mb-6">
              You've successfully demonstrated your web security skills!
            </p>
            <button
              onClick={() => onComplete?.()}
              className="bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold hover:bg-[#7ab811] transition-colors"
            >
              Continue
            </button>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default WebSecurityChallenge;