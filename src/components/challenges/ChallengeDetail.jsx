import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaTrophy, FaClock, FaCoins, FaLock, FaArrowRight, FaSpinner } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { getChallengeBySlug, getUserProgress } from '../../services/DataService';

// Format time (minutes to hours and minutes)
const formatTime = (minutes) => {
  if (!minutes) return 'N/A';

  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;

  if (hours === 0) {
    return `${mins} min`;
  } else if (mins === 0) {
    return `${hours} hr`;
  } else {
    return `${hours} hr ${mins} min`;
  }
};

const ChallengeDetail = () => {
  const { darkMode } = useGlobalTheme();
  const { challengeId } = useParams();
  const navigate = useNavigate();
  const { profile } = useAuth();
  const [challenge, setChallenge] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userProgress, setUserProgress] = useState(null);
  
  // Fetch challenge data
  useEffect(() => {
    const fetchChallenge = async () => {
      try {
        setLoading(true);
        
        // Fetch challenge by slug
        const challengeData = await getChallengeBySlug(challengeId);
        
        if (!challengeData) {
          setError('Challenge not found');
          return;
        }
        
        setChallenge(challengeData);
        
        // Fetch user progress if authenticated
        if (profile) {
          const progressData = await getUserProgress();
          setUserProgress(progressData.challenges[challengeData.id] || null);
        }
        
        setError(null);
      } catch (err) {
        console.error('Error fetching challenge:', err);
        setError('Failed to load challenge. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchChallenge();
  }, [challengeId, profile]);
  
  // Show loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <FaSpinner className="animate-spin text-4xl text-[#88cc14] mb-4" />
        <p>Loading challenge...</p>
      </div>
    );
  }
  
  // Show error state
  if (error || !challenge) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <h2 className="text-2xl font-bold mb-4">Challenge Not Found</h2>
        <p className="mb-6">The challenge you're looking for doesn't exist or has been removed.</p>
        <button
          onClick={() => navigate('/challenges')}
          className="px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg transition-colors"
        >
          Back to Challenges
        </button>
      </div>
    );
  }
  
  return (
    <div>
      {/* Challenge Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/challenges')}
          className={`flex items-center mb-4 ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'}`}
        >
          <FaArrowLeft className="mr-2" /> Back to Challenges
        </button>
        
        <h1 className="text-3xl font-bold mb-2">{challenge.title}</h1>
        
        <div className="flex flex-wrap gap-2 mb-4">
          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
            {challenge.category.name}
          </span>
          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
            {challenge.difficulty.name}
          </span>
          {challenge.estimated_time && (
            <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
              <FaClock className="mr-1" /> {formatTime(challenge.estimated_time)}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center">
            <FaTrophy className="mr-1 text-yellow-500" /> {challenge.points} points
          </div>
          <div className="flex items-center">
            <FaCoins className="mr-1 text-yellow-500" /> {challenge.coin_reward} coins reward
          </div>
        </div>
        
        <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-4`}>
          {challenge.description}
        </p>
      </div>
      
      {/* Challenge Content */}
      <div className="space-y-6">
        {/* Description */}
        {challenge.content.description && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Description</h2>
            <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              {challenge.content.description}
            </p>
          </div>
        )}
        
        {/* Scenario */}
        {challenge.content.scenario && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Scenario</h2>
            <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4`}>
              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {challenge.content.scenario}
              </p>
            </div>
          </div>
        )}
        
        {/* Objectives */}
        {challenge.content.objectives && challenge.content.objectives.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Objectives</h2>
            <ul className={`list-disc pl-5 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              {challenge.content.objectives.map((objective, index) => (
                <li key={index} className="mb-1">{objective}</li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Challenge Files */}
        {challenge.content.challenge_files && challenge.content.challenge_files.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Challenge Files</h2>
            <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4`}>
              <ul className="space-y-2">
                {challenge.content.challenge_files.map((file, index) => (
                  <li key={index}>
                    <a
                      href={file.url}
                      className="flex items-center text-[#88cc14] hover:underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <FaArrowRight className="mr-2 text-xs" /> {file.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
        
        {/* Challenge Data */}
        {challenge.content.challenge_data && Object.keys(challenge.content.challenge_data).length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Challenge Data</h2>
            <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4`}>
              {Object.entries(challenge.content.challenge_data).map(([key, value]) => (
                <div key={key} className="mb-2">
                  <span className="font-semibold">{key}:</span> {value}
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Flag Format */}
        {challenge.content.flag_format && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Flag Format</h2>
            <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4 font-mono`}>
              {challenge.content.flag_format}
            </div>
          </div>
        )}
        
        {/* Hints */}
        {challenge.content.hints && challenge.content.hints.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Hints</h2>
            <div className="space-y-4">
              {challenge.content.hints.map((hint, index) => (
                <div
                  key={hint.id}
                  className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4`}
                >
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium">Hint {index + 1}</h3>
                    <button
                      className={`flex items-center px-3 py-1 rounded-lg ${
                        profile?.subscription_tier === 'premium' || profile?.subscription_tier === 'business'
                          ? 'theme-button-primary'
                          : darkMode ? 'bg-gray-800 text-gray-500' : 'bg-gray-200 text-gray-500'
                      }`}
                      disabled={!profile || profile.subscription_tier === 'free'}
                    >
                      <FaCoins className="mr-1" /> {hint.coin_cost} coins
                    </button>
                  </div>
                  
                  {profile?.subscription_tier === 'premium' || profile?.subscription_tier === 'business' ? (
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {hint.hint}
                    </p>
                  ) : (
                    <div className="flex items-center justify-center py-4">
                      <FaLock className={`text-2xl ${darkMode ? 'text-gray-600' : 'text-gray-400'}`} />
                      <span className={`ml-2 ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                        Upgrade to premium to unlock hints
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Submit Flag */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">Submit Flag</h2>
          <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-6`}>
            <div className="mb-4">
              <label htmlFor="flag" className="block mb-2 font-medium">
                Enter Flag
              </label>
              <input
                type="text"
                id="flag"
                className={`w-full p-3 rounded-lg ${darkMode ? 'bg-[#1A1F35] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                placeholder="flag{...}"
              />
            </div>
            <button
              className="w-full py-3 theme-button-primary rounded-lg font-medium"
            >
              Submit Flag
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChallengeDetail;
