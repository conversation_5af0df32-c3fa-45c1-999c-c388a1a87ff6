import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const CryptoChallenge = ({ onComplete }) => {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState([]);
  const [step, setStep] = useState(0);
  const [success, setSuccess] = useState(false);
  const [context, setContext] = useState('');

  const steps = [
    {
      instruction: "Classical Cryptography - Caesar Cipher",
      context: `
Encrypted message:
"PBQRPENPXVAT"

Known information:
- Message is encrypted using Caesar cipher
- Shift value is between 1-25
- Message is related to this challenge

Goal: Decrypt the message.
      `,
      validation: (input) => input.toLowerCase() === "codecracking",
      success: "Successfully decrypted! The shift value was 13 (ROT13).",
      hint: "Try each possible shift value systematically"
    },
    {
      instruction: "Public Key Cryptography - Weak Prime",
      context: `
RSA parameters:
n = 77 (public modulus)
e = 7 (public exponent)
c = 42 (encrypted message)

Known information:
- n is product of two primes
- One prime is less than 10
- Message m is a single digit

Goal: Factor n and decrypt the message.
      `,
      validation: (input) => {
        const answer = "m=4"; // n=77=7*11, d=43, c=42
        return input.toLowerCase().replace(/\s/g, '') === answer.toLowerCase();
      },
      success: "Correct! You've broken RSA with weak primes (7 and 11).",
      hint: "Try small prime numbers as factors of 77"
    },
    {
      instruction: "Protocol Attack - Diffie-Hellman",
      context: `
Diffie-Hellman parameters:
p = 23 (prime modulus)
g = 5 (generator)
A = 8 (Alice's public key)
B = 19 (Bob's public key)

Intercepted messages:
[A -> B]: g^a mod p = 8
[B -> A]: g^b mod p = 19

Goal: Find the shared secret key.
      `,
      validation: (input) => {
        const answer = "key=2"; // Shared secret is 2
        return input.toLowerCase().replace(/\s/g, '') === answer.toLowerCase();
      },
      success: "Man-in-the-middle attack successful! You've found the shared secret.",
      hint: "Use discrete logarithm for small numbers"
    }
  ];

  useEffect(() => {
    setContext(steps[step].context);
    setOutput([{ type: 'system', text: 'Cryptographic challenge initialized...' }]);
  }, [step]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!input.trim()) return;
    
    const currentStep = steps[step];
    const isCorrect = currentStep.validation(input);
    
    setOutput(prev => [...prev, { type: 'input', text: `$ ${input}` }]);

    setTimeout(() => {
      if (isCorrect) {
        setOutput(prev => [
          ...prev,
          { type: 'success', text: currentStep.success },
          { type: 'system', text: 'Preparing next challenge...' }
        ]);

        if (step < steps.length - 1) {
          setTimeout(() => setStep(step + 1), 1500);
        } else {
          setSuccess(true);
          onComplete && onComplete();
        }
      } else {
        setOutput(prev => [
          ...prev,
          { type: 'error', text: 'Decryption failed.' },
          { type: 'hint', text: `Hint: ${currentStep.hint}` }
        ]);
      }
      setInput('');
    }, 500);
  };

  return (
    <div className="bg-black text-green-400 p-6 rounded-lg font-mono">
      <div className="mb-6">
        <div className="text-xs text-gray-500 mb-2">Challenge Progress: {step + 1}/{steps.length}</div>
        <div className="h-2 bg-gray-800 rounded-full">
          <div 
            className="h-full bg-green-500 rounded-full transition-all duration-300"
            style={{ width: `${((step + 1) / steps.length) * 100}%` }}
          />
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-bold mb-4">{steps[step].instruction}</h3>
        <div className="bg-black/30 rounded-lg p-4 border border-gray-800">
          <pre className="text-gray-400 text-sm whitespace-pre-wrap font-mono">
            {context}
          </pre>
        </div>
      </div>

      <div className="h-48 mb-6 bg-black/50 rounded-lg border border-gray-800 overflow-y-auto">
        <div className="p-4 space-y-2">
          {output.map((line, i) => (
            <div 
              key={i} 
              className={`${
                line.type === 'input' ? 'text-blue-400' :
                line.type === 'success' ? 'text-green-400' :
                line.type === 'error' ? 'text-red-400' :
                line.type === 'hint' ? 'text-yellow-400' :
                line.type === 'system' ? 'text-purple-400' :
                'text-gray-400'
              }`}
            >
              {line.text}
            </div>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="flex gap-2">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          className="flex-1 bg-gray-900 text-green-400 px-4 py-2 rounded-lg border border-gray-800 focus:outline-none focus:border-green-500"
          placeholder="Enter your solution..."
          disabled={success}
        />
        <button 
          type="submit"
          className="bg-green-600 text-black px-6 py-2 rounded-lg font-bold hover:bg-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={success}
        >
          Execute
        </button>
      </form>

      {success && (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-6 p-4 bg-green-500/20 text-green-400 rounded-lg border border-green-500/20"
        >
          <h4 className="text-lg font-bold mb-2">🎉 Challenge Complete!</h4>
          <p>You've successfully demonstrated understanding of:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Classical cryptography</li>
            <li>Public key cryptography</li>
            <li>Cryptographic protocol analysis</li>
          </ul>
        </motion.div>
      )}
    </div>
  );
};

export default CryptoChallenge;