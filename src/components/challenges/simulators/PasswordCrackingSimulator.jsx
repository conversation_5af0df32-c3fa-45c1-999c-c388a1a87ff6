import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON>ock, <PERSON>aUnlock, FaLightbulb, FaCheck, FaInfoCircle, FaExclamationTriangle } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';

const PasswordCrackingSimulator = ({ onComplete }) => {
  const { darkMode } = useGlobalTheme();
  const [currentHash, setCurrentHash] = useState(0);
  const [password, setPassword] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [message, setMessage] = useState(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [hintLevel, setHintLevel] = useState(0);
  const [startTime] = useState(new Date());
  const [crackedPasswords, setCrackedPasswords] = useState([]);
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'info', text: 'Password Cracking Challenge initialized...' },
    { type: 'info', text: 'Target: Password Hash Database' },
    { type: 'info', text: 'Objective: Crack all three password hashes' },
    { type: 'system', text: 'Loading hash #1: 5f4dcc3b5aa765d61d8327deb882cf99 (MD5)' }
  ]);
  
  const hashes = [
    { 
      hash: '5f4dcc3b5aa765d61d8327deb882cf99', 
      type: 'MD5', 
      password: 'password',
      hint1: 'This is one of the most common passwords used.',
      hint2: 'It\'s literally the word "password".'
    },
    { 
      hash: '5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8', 
      type: 'SHA-1', 
      password: 'password1',
      hint1: 'This password is similar to the first one, but with a number added.',
      hint2: 'Try adding a "1" to the end of the first password.'
    },
    { 
      hash: '$2y$10$F9hXnLl7IVhMJ.Zyj0jGAO38BXCxV/N4NLnLVVEx7hHQmWkr0qcp.', 
      type: 'bcrypt', 
      password: 'security42',
      hint1: 'This is a common word related to this field of study, followed by a two-digit number.',
      hint2: 'The word is "security" and the number is the "Answer to the Ultimate Question of Life, the Universe, and Everything" in The Hitchhiker\'s Guide to the Galaxy.'
    }
  ];
  
  const addToConsole = (type, text) => {
    setConsoleOutput(prev => [...prev, { type, text }]);
    // Auto-scroll console to bottom
    setTimeout(() => {
      const consoleElement = document.getElementById('password-console');
      if (consoleElement) {
        consoleElement.scrollTop = consoleElement.scrollHeight;
      }
    }, 100);
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    setAttempts(prev => prev + 1);
    
    // Simulate cracking attempt
    addToConsole('system', `Attempting to crack ${hashes[currentHash].type} hash with password: ${password}`);
    
    setTimeout(() => {
      if (password.toLowerCase() === hashes[currentHash].password.toLowerCase()) {
        // Correct password
        addToConsole('success', `Hash cracked! Password: ${password}`);
        
        // Add to cracked passwords
        setCrackedPasswords(prev => [...prev, password]);
        
        if (currentHash < hashes.length - 1) {
          // Move to next hash
          setCurrentHash(prev => prev + 1);
          setPassword('');
          addToConsole('system', `Loading hash #${currentHash + 2}: ${hashes[currentHash + 1].hash} (${hashes[currentHash + 1].type})`);
          setMessage({ type: 'success', text: `Great job! You've cracked hash #${currentHash + 1}. Now try the next one.` });
        } else {
          // All hashes cracked
          setIsSuccess(true);
          setMessage({ type: 'success', text: 'Congratulations! You\'ve cracked all the password hashes!' });
          
          // Calculate time spent
          const endTime = new Date();
          const timeSpent = Math.floor((endTime - startTime) / 1000); // in seconds
          const minutes = Math.floor(timeSpent / 60);
          const seconds = timeSpent % 60;
          const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
          
          // Call the onComplete callback with challenge results
          if (onComplete) {
            onComplete({
              success: true,
              flag: `flag{${crackedPasswords.join('_')}_master_cracker}`,
              attempts: attempts + 1,
              timeSpent: formattedTime,
            });
          }
        }
      } else {
        // Incorrect password
        addToConsole('error', 'Incorrect password. Try again.');
        setMessage({ type: 'error', text: 'Incorrect password. Try again.' });
      }
    }, 800); // Simulate processing delay
  };
  
  const getNextHint = () => {
    setHintLevel(prev => prev + 1);
    setShowHint(true);
    
    // Add hint request to console
    addToConsole('system', 'Hint requested...');
    
    if (hintLevel % 2 === 0) {
      // First hint for current hash
      addToConsole('hint', hashes[currentHash].hint1);
    } else {
      // Second hint for current hash
      addToConsole('hint', hashes[currentHash].hint2);
    }
  };
  
  // Auto-scroll console on new messages
  useEffect(() => {
    const consoleElement = document.getElementById('password-console');
    if (consoleElement) {
      consoleElement.scrollTop = consoleElement.scrollHeight;
    }
  }, [consoleOutput]);
  
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
      {/* Challenge Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex justify-between items-center`}>
        <h2 className="text-xl font-bold flex items-center">
          {isSuccess ? <FaUnlock className="text-green-500 mr-2" /> : <FaLock className="text-red-500 mr-2" />}
          Password Cracking Challenge
        </h2>
        <div>
          <button 
            onClick={getNextHint}
            className={`px-3 py-1 rounded-lg flex items-center ${
              darkMode 
                ? 'bg-gray-800 hover:bg-gray-700 text-gray-300' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
            }`}
          >
            <FaLightbulb className="mr-1 text-yellow-500" /> Get Hint
          </button>
        </div>
      </div>
      
      {/* Challenge Content */}
      <div className="p-6">
        {/* Status Message */}
        {message && (
          <div className={`mb-4 p-3 rounded-lg ${
            message.type === 'success' 
              ? darkMode ? 'bg-green-900/20 text-green-300 border border-green-900/30' : 'bg-green-100 text-green-800 border border-green-200'
              : message.type === 'warning'
                ? darkMode ? 'bg-yellow-900/20 text-yellow-300 border border-yellow-900/30' : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                : darkMode ? 'bg-red-900/20 text-red-300 border border-red-900/30' : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-start">
              {message.type === 'success' ? (
                <FaCheck className="mt-1 mr-2" />
              ) : message.type === 'warning' ? (
                <FaExclamationTriangle className="mt-1 mr-2" />
              ) : (
                <FaInfoCircle className="mt-1 mr-2" />
              )}
              <div>{message.text}</div>
            </div>
          </div>
        )}
        
        {/* Challenge Description */}
        <div className={`mb-6 p-4 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
          <h3 className="font-bold mb-2">Challenge Scenario:</h3>
          <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-4`}>
            You have obtained a file containing password hashes from a security breach. Your task is to crack these hashes to reveal the original passwords.
          </p>
          <div className="flex items-center">
            <FaInfoCircle className={`mr-2 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>
              Password cracking is the process of recovering passwords from data that has been stored in or transmitted in a hashed form.
            </p>
          </div>
        </div>
        
        {/* Password Cracking Interface */}
        <div className="mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0F172A] border-gray-800' : 'bg-white border-gray-300'} border`}>
            <div className="text-center mb-4">
              <h3 className="text-xl font-bold">Password Hash Cracker</h3>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Hash {currentHash + 1} of {hashes.length}: {hashes[currentHash].type}
              </p>
            </div>
            
            <div className={`p-3 mb-4 rounded ${darkMode ? 'bg-black' : 'bg-gray-900'} font-mono text-green-400 overflow-x-auto`}>
              {hashes[currentHash].hash}
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className={`block mb-2 text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Password Guess
                </label>
                <input
                  type="text"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`w-full p-2.5 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-gray-50 border-gray-300 text-gray-900'
                  } border`}
                  placeholder="Enter password guess"
                  disabled={isSuccess}
                />
              </div>
              
              <button
                type="submit"
                className={`w-full py-2.5 px-5 rounded-lg ${
                  isSuccess
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
                disabled={isSuccess}
              >
                {isSuccess ? 'All Hashes Cracked!' : 'Crack Hash'}
              </button>
            </form>
            
            {/* Progress Indicator */}
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Progress</span>
                <span>{Math.round((crackedPasswords.length / hashes.length) * 100)}%</span>
              </div>
              <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
                <div 
                  className="h-2 rounded-full bg-green-500" 
                  style={{ width: `${(crackedPasswords.length / hashes.length) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Console Output */}
        <div className="mb-4">
          <h3 className={`text-lg font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Console Output:</h3>
          <div 
            id="password-console"
            className={`h-64 overflow-y-auto p-3 rounded-lg font-mono text-sm ${
              darkMode ? 'bg-black text-green-400' : 'bg-gray-900 text-green-400'
            }`}
          >
            {consoleOutput.map((line, index) => (
              <div key={index} className={`mb-1 ${
                line.type === 'error' ? 'text-red-400' :
                line.type === 'success' ? 'text-green-400' :
                line.type === 'query' ? 'text-yellow-400' :
                line.type === 'hint' ? 'text-blue-400' :
                line.type === 'system' ? 'text-purple-400' : 'text-green-400'
              }`}>
                {line.type === 'error' && '[-] '}
                {line.type === 'success' && '[+] '}
                {line.type === 'query' && '[>] '}
                {line.type === 'hint' && '[?] '}
                {line.type === 'system' && '[*] '}
                {line.type === 'info' && '[i] '}
                {line.text}
              </div>
            ))}
          </div>
        </div>
        
        {/* Hints Section */}
        {showHint && (
          <div className={`mt-4 p-4 rounded-lg ${
            darkMode ? 'bg-yellow-900/20 border-yellow-900/30' : 'bg-yellow-50 border-yellow-200'
          } border`}>
            <div className="flex items-start">
              <FaLightbulb className={`mt-1 mr-2 ${darkMode ? 'text-yellow-400' : 'text-yellow-600'}`} />
              <div>
                <h4 className={`font-medium ${darkMode ? 'text-yellow-300' : 'text-yellow-800'}`}>Hint {hintLevel}:</h4>
                <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                  {hintLevel % 2 === 1 ? hashes[currentHash].hint1 : hashes[currentHash].hint2}
                </p>
              </div>
            </div>
          </div>
        )}
        
        {/* Success Message */}
        {isSuccess && (
          <div className={`mt-6 p-4 rounded-lg ${
            darkMode ? 'bg-green-900/20 border-green-900/30' : 'bg-green-100 border-green-200'
          } border`}>
            <h3 className={`text-lg font-bold mb-2 ${darkMode ? 'text-green-300' : 'text-green-800'}`}>
              Challenge Completed!
            </h3>
            <p className={`mb-3 ${darkMode ? 'text-green-200' : 'text-green-700'}`}>
              Congratulations! You've successfully cracked all the password hashes.
            </p>
            <div className={`p-3 rounded ${darkMode ? 'bg-black' : 'bg-gray-900'} font-mono text-green-400 mb-3`}>
              Flag: flag{'{' + crackedPasswords.join('_') + '_master_cracker}'}
            </div>
            <h4 className={`font-medium mb-2 ${darkMode ? 'text-green-300' : 'text-green-800'}`}>What you learned:</h4>
            <ul className={`list-disc pl-5 ${darkMode ? 'text-green-200' : 'text-green-700'}`}>
              <li>How to identify different types of password hashes</li>
              <li>Techniques for cracking password hashes of varying complexity</li>
              <li>The importance of strong, unique passwords</li>
              <li>Why modern hashing algorithms like bcrypt are more secure than older ones like MD5</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default PasswordCrackingSimulator;
