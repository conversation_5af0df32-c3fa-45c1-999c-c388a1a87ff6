import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>aU<PERSON>lock, FaLightbulb, FaCheck, FaInfoCircle, FaExclamationTriangle } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';

const SQLInjectionSimulator = ({ onComplete }) => {
  const { darkMode } = useGlobalTheme();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [message, setMessage] = useState(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [hintLevel, setHintLevel] = useState(0);
  const [startTime] = useState(new Date());
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'info', text: 'SQL Injection Challenge initialized...' },
    { type: 'info', text: 'Target: BankSecure Login Portal' },
    { type: 'info', text: 'Objective: Bypass authentication without valid credentials' },
    { type: 'system', text: 'Type your SQL injection payload in the username field.' }
  ]);
  
  // Simulated backend query
  const simulateLoginQuery = (user, pass) => {
    // This simulates what would happen on the server
    const simulatedQuery = `SELECT * FROM users WHERE username='${user}' AND password='${pass}'`;
    
    // Add the query to console output
    addToConsole('query', `Executing: ${simulatedQuery}`);
    
    // Check for SQL injection patterns
    if (user.includes("'") || user.includes('"')) {
      // Basic SQL injection detected
      if (
        user.toLowerCase().includes("' or '1'='1") || 
        user.toLowerCase().includes("' or 1=1") ||
        user.toLowerCase().includes('" or "1"="1') ||
        user.toLowerCase().includes('" or 1=1') ||
        user.toLowerCase().includes("' or '1'='1'--") ||
        user.toLowerCase().includes("' or 1=1--") ||
        user.toLowerCase().includes("admin'--")
      ) {
        // Successful SQL injection
        return { success: true, query: simulatedQuery };
      } else {
        // SQL injection attempt, but not quite right
        return { success: false, error: 'SQL syntax error', query: simulatedQuery };
      }
    }
    
    // Regular login attempt (will always fail in this simulation)
    return { success: false, error: 'Invalid credentials', query: simulatedQuery };
  };
  
  const addToConsole = (type, text) => {
    setConsoleOutput(prev => [...prev, { type, text }]);
    // Auto-scroll console to bottom
    setTimeout(() => {
      const consoleElement = document.getElementById('sql-console');
      if (consoleElement) {
        consoleElement.scrollTop = consoleElement.scrollHeight;
      }
    }, 100);
  };
  
  const handleLogin = (e) => {
    e.preventDefault();
    setAttempts(prev => prev + 1);
    
    // Simulate server request
    addToConsole('system', 'Sending login request...');
    
    setTimeout(() => {
      const result = simulateLoginQuery(username, password);
      
      if (result.success) {
        setIsSuccess(true);
        setMessage({ type: 'success', text: 'Authentication bypassed successfully! You have exploited the SQL injection vulnerability.' });
        addToConsole('success', 'Authentication bypassed! Access granted to admin account.');
        addToConsole('system', 'Flag captured: flag{sql_injection_master_2023}');
        
        // Calculate time spent
        const endTime = new Date();
        const timeSpent = Math.floor((endTime - startTime) / 1000); // in seconds
        const minutes = Math.floor(timeSpent / 60);
        const seconds = timeSpent % 60;
        const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        // Call the onComplete callback with challenge results
        if (onComplete) {
          onComplete({
            success: true,
            flag: 'flag{sql_injection_master_2023}',
            attempts: attempts + 1,
            timeSpent: formattedTime,
          });
        }
      } else {
        if (result.error === 'SQL syntax error') {
          setMessage({ type: 'warning', text: 'SQL syntax error in your injection attempt. You\'re on the right track!' });
          addToConsole('error', 'Database returned: SQL syntax error near "' + username + '"');
        } else {
          setMessage({ type: 'error', text: 'Login failed: Invalid credentials' });
          addToConsole('error', 'Authentication failed: Invalid username or password');
        }
      }
    }, 800); // Simulate network delay
  };
  
  const getNextHint = () => {
    setHintLevel(prev => prev + 1);
    setShowHint(true);
    
    // Add hint request to console
    addToConsole('system', 'Hint requested...');
    
    const hints = [
      "Try entering special characters like a single quote (') in the username field to see how the application responds.",
      "The login query might look like: SELECT * FROM users WHERE username='[input]' AND password='[input]'",
      "Using a statement like ' OR '1'='1 might help you bypass the authentication by making the WHERE clause always true.",
      "Try this payload in the username field: ' OR '1'='1'-- (The -- comments out the rest of the query)"
    ];
    
    if (hintLevel < hints.length) {
      addToConsole('hint', hints[hintLevel]);
    }
  };
  
  // Auto-scroll console on new messages
  useEffect(() => {
    const consoleElement = document.getElementById('sql-console');
    if (consoleElement) {
      consoleElement.scrollTop = consoleElement.scrollHeight;
    }
  }, [consoleOutput]);
  
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
      {/* Challenge Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex justify-between items-center`}>
        <h2 className="text-xl font-bold flex items-center">
          {isSuccess ? <FaUnlock className="text-green-500 mr-2" /> : <FaLock className="text-red-500 mr-2" />}
          SQL Injection Challenge
        </h2>
        <div>
          <button 
            onClick={getNextHint}
            className={`px-3 py-1 rounded-lg flex items-center ${
              darkMode 
                ? 'bg-gray-800 hover:bg-gray-700 text-gray-300' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
            }`}
          >
            <FaLightbulb className="mr-1 text-yellow-500" /> Get Hint
          </button>
        </div>
      </div>
      
      {/* Challenge Content */}
      <div className="p-6">
        {/* Status Message */}
        {message && (
          <div className={`mb-4 p-3 rounded-lg ${
            message.type === 'success' 
              ? darkMode ? 'bg-green-900/20 text-green-300 border border-green-900/30' : 'bg-green-100 text-green-800 border border-green-200'
              : message.type === 'warning'
                ? darkMode ? 'bg-yellow-900/20 text-yellow-300 border border-yellow-900/30' : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                : darkMode ? 'bg-red-900/20 text-red-300 border border-red-900/30' : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-start">
              {message.type === 'success' ? (
                <FaCheck className="mt-1 mr-2" />
              ) : message.type === 'warning' ? (
                <FaExclamationTriangle className="mt-1 mr-2" />
              ) : (
                <FaInfoCircle className="mt-1 mr-2" />
              )}
              <div>{message.text}</div>
            </div>
          </div>
        )}
        
        {/* Challenge Description */}
        <div className={`mb-6 p-4 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
          <h3 className="font-bold mb-2">Challenge Scenario:</h3>
          <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-4`}>
            BankSecure is a financial institution that prides itself on security. However, their web developers have made a critical mistake in their login form. Your goal is to exploit this vulnerability to gain unauthorized access.
          </p>
          <div className="flex items-center">
            <FaInfoCircle className={`mr-2 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>
              SQL injection is one of the most common web application vulnerabilities. It occurs when user input is incorrectly filtered and directly included in SQL queries.
            </p>
          </div>
        </div>
        
        {/* Login Form Simulation */}
        <div className="mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0F172A] border-gray-800' : 'bg-white border-gray-300'} border`}>
            <div className="text-center mb-4">
              <h3 className="text-xl font-bold">BankSecure Login Portal</h3>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Secure Banking for Everyone</p>
            </div>
            
            <form onSubmit={handleLogin}>
              <div className="mb-4">
                <label className={`block mb-2 text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Username
                </label>
                <input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className={`w-full p-2.5 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-gray-50 border-gray-300 text-gray-900'
                  } border`}
                  placeholder="Enter username"
                  disabled={isSuccess}
                />
              </div>
              
              <div className="mb-4">
                <label className={`block mb-2 text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Password
                </label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`w-full p-2.5 rounded-lg ${
                    darkMode 
                      ? 'bg-gray-800 border-gray-700 text-white' 
                      : 'bg-gray-50 border-gray-300 text-gray-900'
                  } border`}
                  placeholder="Enter password"
                  disabled={isSuccess}
                />
              </div>
              
              <button
                type="submit"
                className={`w-full py-2.5 px-5 rounded-lg ${
                  isSuccess
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
                disabled={isSuccess}
              >
                {isSuccess ? 'Access Granted' : 'Login'}
              </button>
            </form>
          </div>
        </div>
        
        {/* Console Output */}
        <div className="mb-4">
          <h3 className={`text-lg font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Console Output:</h3>
          <div 
            id="sql-console"
            className={`h-64 overflow-y-auto p-3 rounded-lg font-mono text-sm ${
              darkMode ? 'bg-black text-green-400' : 'bg-gray-900 text-green-400'
            }`}
          >
            {consoleOutput.map((line, index) => (
              <div key={index} className={`mb-1 ${
                line.type === 'error' ? 'text-red-400' :
                line.type === 'success' ? 'text-green-400' :
                line.type === 'query' ? 'text-yellow-400' :
                line.type === 'hint' ? 'text-blue-400' :
                line.type === 'system' ? 'text-purple-400' : 'text-green-400'
              }`}>
                {line.type === 'error' && '[-] '}
                {line.type === 'success' && '[+] '}
                {line.type === 'query' && '[>] '}
                {line.type === 'hint' && '[?] '}
                {line.type === 'system' && '[*] '}
                {line.type === 'info' && '[i] '}
                {line.text}
              </div>
            ))}
          </div>
        </div>
        
        {/* Hints Section */}
        {showHint && (
          <div className={`mt-4 p-4 rounded-lg ${
            darkMode ? 'bg-yellow-900/20 border-yellow-900/30' : 'bg-yellow-50 border-yellow-200'
          } border`}>
            <div className="flex items-start">
              <FaLightbulb className={`mt-1 mr-2 ${darkMode ? 'text-yellow-400' : 'text-yellow-600'}`} />
              <div>
                <h4 className={`font-medium ${darkMode ? 'text-yellow-300' : 'text-yellow-800'}`}>Hint {hintLevel}:</h4>
                {hintLevel === 1 && (
                  <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                    Try entering special characters like a single quote (') in the username field to see how the application responds.
                  </p>
                )}
                {hintLevel === 2 && (
                  <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                    The login query might look like: SELECT * FROM users WHERE username='[input]' AND password='[input]'
                  </p>
                )}
                {hintLevel === 3 && (
                  <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                    Using a statement like ' OR '1'='1 might help you bypass the authentication by making the WHERE clause always true.
                  </p>
                )}
                {hintLevel === 4 && (
                  <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                    Try this payload in the username field: ' OR '1'='1'-- (The -- comments out the rest of the query)
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
        
        {/* Success Message */}
        {isSuccess && (
          <div className={`mt-6 p-4 rounded-lg ${
            darkMode ? 'bg-green-900/20 border-green-900/30' : 'bg-green-100 border-green-200'
          } border`}>
            <h3 className={`text-lg font-bold mb-2 ${darkMode ? 'text-green-300' : 'text-green-800'}`}>
              Challenge Completed!
            </h3>
            <p className={`mb-3 ${darkMode ? 'text-green-200' : 'text-green-700'}`}>
              Congratulations! You've successfully exploited the SQL injection vulnerability in the login form.
            </p>
            <div className={`p-3 rounded ${darkMode ? 'bg-black' : 'bg-gray-900'} font-mono text-green-400 mb-3`}>
              Flag: flag{'{sql_injection_master_2023}'}
            </div>
            <h4 className={`font-medium mb-2 ${darkMode ? 'text-green-300' : 'text-green-800'}`}>What you learned:</h4>
            <ul className={`list-disc pl-5 ${darkMode ? 'text-green-200' : 'text-green-700'}`}>
              <li>How SQL injection vulnerabilities occur in web applications</li>
              <li>How to identify and exploit vulnerable input fields</li>
              <li>The importance of parameterized queries to prevent SQL injection</li>
              <li>How to bypass authentication using SQL injection techniques</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default SQLInjectionSimulator;
