import React, { useState } from 'react';
import { FaLock, FaUnlock, FaLightbulb, FaNetworkWired } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';

const NetworkTrafficSimulator = ({ onComplete }) => {
  const { darkMode } = useGlobalTheme();
  const [showHint, setShowHint] = useState(false);
  const [hintLevel, setHintLevel] = useState(0);
  
  const getNextHint = () => {
    setHintLevel(prev => prev + 1);
    setShowHint(true);
  };
  
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
      {/* Challenge Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex justify-between items-center`}>
        <h2 className="text-xl font-bold flex items-center">
          <FaNetworkWired className="text-blue-500 mr-2" />
          Network Traffic Analysis Challenge
        </h2>
        <div>
          <button 
            onClick={getNextHint}
            className={`px-3 py-1 rounded-lg flex items-center ${
              darkMode 
                ? 'bg-gray-800 hover:bg-gray-700 text-gray-300' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
            }`}
          >
            <FaLightbulb className="mr-1 text-yellow-500" /> Get Hint
          </button>
        </div>
      </div>
      
      {/* Challenge Content */}
      <div className="p-6">
        <div className={`p-8 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border text-center`}>
          <h3 className="text-xl font-bold mb-4">Network Traffic Analysis Simulator</h3>
          <p className="mb-6">This challenge is coming soon! Check back later for a fully interactive network traffic analysis simulation.</p>
          
          <div className="flex justify-center">
            <button
              onClick={() => {
                if (onComplete) {
                  onComplete({
                    success: true,
                    flag: 'flag{network_analysis_expert}',
                    attempts: 1,
                    timeSpent: '10:00',
                  });
                }
              }}
              className="px-6 py-3 theme-button-primary rounded-lg"
            >
              Simulate Completion
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkTrafficSimulator;
