import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { <PERSON>aLock, FaUnlock, FaLightbulb, FaCheck, FaInfoCircle, FaExclamationTriangle, FaTrophy, FaArrowLeft } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import SQLInjectionSimulator from './simulators/SQLInjectionSimulator';
import PasswordCrackingSimulator from './simulators/PasswordCrackingSimulator';
import NetworkTrafficSimulator from './simulators/NetworkTrafficSimulator';
import OSINTSimulator from './simulators/OSINTSimulator';
import SteganographySimulator from './simulators/SteganographySimulator';
import BinaryAnalysisSimulator from './simulators/BinaryAnalysisSimulator';

// Static challenges data
const CHALLENGES = [
  {
    id: 'c1',
    slug: 'sql-injection-basics',
    title: 'SQL Injection Basics',
    description: 'Learn the fundamentals of SQL injection by exploiting a vulnerable login form. This challenge introduces you to one of the most common web application vulnerabilities.',
    category: { name: 'Web Security' },
    difficulty: { name: 'Beginner' },
    type: { name: 'Exploitation' },
    points: 100,
    coin_reward: 10,
    estimated_time: 20,
    simulator: SQLInjectionSimulator
  },
  {
    id: 'c2',
    slug: 'password-cracking-basics',
    title: 'Password Cracking Basics',
    description: 'Learn the fundamentals of password cracking by breaking a series of increasingly complex password hashes. This challenge introduces you to common password cracking tools and techniques.',
    category: { name: 'Cryptography' },
    difficulty: { name: 'Beginner' },
    type: { name: 'Analysis' },
    points: 100,
    coin_reward: 10,
    estimated_time: 25,
    simulator: PasswordCrackingSimulator
  },
  {
    id: 'c3',
    slug: 'network-traffic-analysis',
    title: 'Network Traffic Analysis',
    description: 'Analyze captured network traffic to identify suspicious activities and extract hidden information. This challenge introduces you to packet analysis and network forensics.',
    category: { name: 'Network Security' },
    difficulty: { name: 'Easy' },
    type: { name: 'Analysis' },
    points: 150,
    coin_reward: 15,
    estimated_time: 30,
    simulator: NetworkTrafficSimulator
  },
  {
    id: 'c4',
    slug: 'osint-investigation',
    title: 'OSINT Investigation',
    description: 'Use Open Source Intelligence techniques to gather information about a fictional target. This challenge introduces you to the power of publicly available information.',
    category: { name: 'OSINT' },
    difficulty: { name: 'Easy' },
    type: { name: 'Reconnaissance' },
    points: 150,
    coin_reward: 15,
    estimated_time: 35,
    simulator: OSINTSimulator
  },
  {
    id: 'c5',
    slug: 'basic-steganography',
    title: 'Basic Steganography',
    description: 'Discover hidden messages concealed within digital images. This challenge introduces you to steganography techniques and tools.',
    category: { name: 'Forensics' },
    difficulty: { name: 'Easy' },
    type: { name: 'Analysis' },
    points: 150,
    coin_reward: 15,
    estimated_time: 25,
    simulator: SteganographySimulator
  },
  {
    id: 'c6',
    slug: 'simple-binary-analysis',
    title: 'Simple Binary Analysis',
    description: 'Analyze a simple executable file to understand its behavior and find hidden functionality. This challenge introduces you to basic reverse engineering concepts.',
    category: { name: 'Reverse Engineering' },
    difficulty: { name: 'Medium' },
    type: { name: 'Analysis' },
    points: 200,
    coin_reward: 20,
    estimated_time: 40,
    simulator: BinaryAnalysisSimulator
  }
];

// Format time (minutes to hours and minutes)
const formatTime = (minutes) => {
  if (!minutes) return 'N/A';
  
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours === 0) return `${mins} min`;
  if (mins === 0) return `${hours} hr`;
  return `${hours} hr ${mins} min`;
};

const ChallengeSimulator = () => {
  const { darkMode } = useGlobalTheme();
  const { challengeId } = useParams();
  const navigate = useNavigate();
  const [challenge, setChallenge] = useState(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [results, setResults] = useState(null);
  
  useEffect(() => {
    // Find the challenge by slug
    const foundChallenge = CHALLENGES.find(c => c.slug === challengeId || c.id === challengeId);
    setChallenge(foundChallenge);
  }, [challengeId]);
  
  const handleChallengeComplete = (results) => {
    setIsCompleted(true);
    setResults(results);
  };
  
  if (!challenge) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
        <div className="container mx-auto px-4">
          <div className="mb-6">
            <button 
              onClick={() => navigate('/challenges')}
              className="flex items-center text-[#88cc14] hover:underline"
            >
              <FaArrowLeft className="mr-2" /> Back to Challenges
            </button>
          </div>
          
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
            <h2 className="text-2xl font-bold mb-4">Challenge Not Found</h2>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              The challenge you're looking for doesn't exist or has been removed.
            </p>
            <button
              onClick={() => navigate('/challenges')}
              className="mt-4 px-6 py-2 theme-button-primary rounded-lg"
            >
              Browse Challenges
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  const Simulator = challenge.simulator;
  
  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <div className="mb-6">
          <button 
            onClick={() => navigate('/challenges')}
            className="flex items-center text-[#88cc14] hover:underline"
          >
            <FaArrowLeft className="mr-2" /> Back to Challenges
          </button>
        </div>
        
        <div className="mb-6">
          <h1 className="text-3xl font-bold">{challenge.title}</h1>
          <div className="flex flex-wrap gap-2 mt-2">
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
              {challenge.category.name}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-purple-900/20 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
              {challenge.difficulty.name}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-green-900/20 text-green-300' : 'bg-green-100 text-green-800'}`}>
              {challenge.type.name}
            </span>
            <span className={`px-2 py-1 rounded text-xs flex items-center ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-800'}`}>
              <FaTrophy className="mr-1 text-yellow-500" /> {challenge.points} points
            </span>
          </div>
        </div>
        
        {/* Challenge Simulator */}
        <Simulator onComplete={handleChallengeComplete} />
        
        {/* Results Summary (shown after completion) */}
        {isCompleted && results && (
          <div className={`mt-8 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <FaTrophy className="text-yellow-500 mr-2" /> Challenge Completed!
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#252D4A]' : 'bg-gray-100'}`}>
                <div className="text-sm text-gray-500 mb-1">Points Earned</div>
                <div className="text-2xl font-bold">{challenge.points}</div>
              </div>
              
              <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#252D4A]' : 'bg-gray-100'}`}>
                <div className="text-sm text-gray-500 mb-1">Coins Rewarded</div>
                <div className="text-2xl font-bold">{challenge.coin_reward}</div>
              </div>
              
              <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#252D4A]' : 'bg-gray-100'}`}>
                <div className="text-sm text-gray-500 mb-1">Time Spent</div>
                <div className="text-2xl font-bold">{results.timeSpent || '15:32'}</div>
              </div>
            </div>
            
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#252D4A]' : 'bg-gray-100'} mb-6`}>
              <h3 className="text-lg font-bold mb-2">Your Flag</h3>
              <div className={`p-3 rounded ${darkMode ? 'bg-black' : 'bg-gray-900'} font-mono text-green-400`}>
                {results.flag}
              </div>
            </div>
            
            <div className="flex justify-between">
              <button
                onClick={() => navigate('/challenges')}
                className={`px-6 py-2 rounded-lg ${
                  darkMode 
                    ? 'bg-gray-800 hover:bg-gray-700 text-white' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
                }`}
              >
                Back to Challenges
              </button>
              
              <button
                onClick={() => {
                  setIsCompleted(false);
                  setResults(null);
                  // This would typically navigate to the next challenge
                  // For now, we'll just reset the current one
                }}
                className="px-6 py-2 theme-button-primary rounded-lg"
              >
                Next Challenge
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChallengeSimulator;
