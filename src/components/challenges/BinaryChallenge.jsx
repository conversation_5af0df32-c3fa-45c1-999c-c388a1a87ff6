import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const BinaryChallenge = ({ onComplete }) => {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState([]);
  const [step, setStep] = useState(0);
  const [success, setSuccess] = useState(false);
  const [context, setContext] = useState('');

  const steps = [
    {
      instruction: "Buffer Overflow - Stack Smashing",
      context: `
Vulnerable C program:
void vulnerable_function(char *input) {
    char buffer[64];
    strcpy(buffer, input);  // No bounds checking!
}

Memory layout:
[buffer(64 bytes)][saved EBP(4 bytes)][return address(4 bytes)]

Goal: Overflow the buffer to crash the program.
      `,
      validation: (input) => {
        // Check if input is long enough to overflow buffer
        return input.length > 64 && /A{64,}/.test(input);
      },
      success: "Buffer overflow successful! Program crashed due to stack smashing.",
      hint: "Fill the buffer with 'A's and overflow into saved EBP and return address"
    },
    {
      instruction: "Shellcode Injection",
      context: `
x86 Assembly shellcode template:
\\x31\\xc0       ; xor eax, eax
\\x50           ; push eax
\\x68//sh      ; push "//sh"
\\x68/bin      ; push "/bin"
\\x89\\xe3       ; mov ebx, esp
\\x50           ; push eax
\\x53           ; push ebx
\\x89\\xe1       ; mov ecx, esp
\\xb0\\x0b       ; mov al, 11
\\xcd\\x80       ; int 0x80

Goal: Create working shellcode with NOP sled.
      `,
      validation: (input) => {
        return (
          input.startsWith("\\x90") && // NOP sled
          input.includes("\\x31\\xc0") && // shellcode start
          input.includes("\\xcd\\x80") // syscall
        );
      },
      success: "Shellcode successfully crafted! This would spawn a shell when executed.",
      hint: "Start with NOP sled (\\x90) followed by the shellcode template"
    },
    {
      instruction: "Return-to-libc Attack",
      context: `
Memory layout with ASLR disabled:
system() address: 0xb7e63190
"/bin/sh" string: 0xb7f83a24
exit() address: 0xb7e55180

Goal: Craft payload to call system("/bin/sh") without shellcode.
      `,
      validation: (input) => {
        return (
          input.includes("0xb7e63190") && // system()
          input.includes("0xb7f83a24") && // "/bin/sh"
          input.includes("0xb7e55180") // exit()
        );
      },
      success: "Return-to-libc attack successful! This would execute shell command without shellcode.",
      hint: "Chain addresses: system() + exit() + \"/bin/sh\""
    }
  ];

  useEffect(() => {
    setContext(steps[step].context);
    setOutput([{ type: 'system', text: 'Binary loaded into memory...' }]);
  }, [step]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!input.trim()) return;
    
    const currentStep = steps[step];
    const isCorrect = currentStep.validation(input);
    
    setOutput(prev => [...prev, { type: 'input', text: `$ ${input}` }]);

    setTimeout(() => {
      if (isCorrect) {
        setOutput(prev => [
          ...prev,
          { type: 'success', text: currentStep.success },
          { type: 'system', text: 'Preparing next challenge...' }
        ]);

        if (step < steps.length - 1) {
          setTimeout(() => setStep(step + 1), 1500);
        } else {
          setSuccess(true);
          onComplete && onComplete();
        }
      } else {
        setOutput(prev => [
          ...prev,
          { type: 'error', text: 'Exploitation failed.' },
          { type: 'hint', text: `Hint: ${currentStep.hint}` }
        ]);
      }
      setInput('');
    }, 500);
  };

  return (
    <div className="bg-black text-green-400 p-6 rounded-lg font-mono">
      <div className="mb-6">
        <div className="text-xs text-gray-500 mb-2">Challenge Progress: {step + 1}/{steps.length}</div>
        <div className="h-2 bg-gray-800 rounded-full">
          <div 
            className="h-full bg-green-500 rounded-full transition-all duration-300"
            style={{ width: `${((step + 1) / steps.length) * 100}%` }}
          />
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-bold mb-4">{steps[step].instruction}</h3>
        <div className="bg-black/30 rounded-lg p-4 border border-gray-800">
          <pre className="text-gray-400 text-sm whitespace-pre-wrap font-mono">
            {context}
          </pre>
        </div>
      </div>

      <div className="h-48 mb-6 bg-black/50 rounded-lg border border-gray-800 overflow-y-auto">
        <div className="p-4 space-y-2">
          {output.map((line, i) => (
            <div 
              key={i} 
              className={`${
                line.type === 'input' ? 'text-blue-400' :
                line.type === 'success' ? 'text-green-400' :
                line.type === 'error' ? 'text-red-400' :
                line.type === 'hint' ? 'text-yellow-400' :
                line.type === 'system' ? 'text-purple-400' :
                'text-gray-400'
              }`}
            >
              {line.text}
            </div>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="flex gap-2">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          className="flex-1 bg-gray-900 text-green-400 px-4 py-2 rounded-lg border border-gray-800 focus:outline-none focus:border-green-500"
          placeholder="Enter your exploit..."
          disabled={success}
        />
        <button 
          type="submit"
          className="bg-green-600 text-black px-6 py-2 rounded-lg font-bold hover:bg-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={success}
        >
          Execute
        </button>
      </form>

      {success && (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-6 p-4 bg-green-500/20 text-green-400 rounded-lg border border-green-500/20"
        >
          <h4 className="text-lg font-bold mb-2">🎉 Challenge Complete!</h4>
          <p>You've successfully demonstrated understanding of:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Buffer Overflow exploitation</li>
            <li>Shellcode development</li>
            <li>Return-to-libc attacks</li>
          </ul>
        </motion.div>
      )}
    </div>
  );
};

export default BinaryChallenge;