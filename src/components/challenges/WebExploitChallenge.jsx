import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const WebExploitChallenge = ({ onComplete }) => {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState([]);
  const [step, setStep] = useState(0);
  const [success, setSuccess] = useState(false);
  const [context, setContext] = useState('');

  const steps = [
    {
      instruction: "SQL Injection Challenge - Login Bypass",
      context: `
Database: users
Table structure:
  - username (varchar)
  - password (varchar)
  - role (varchar)

Login query:
SELECT * FROM users WHERE username = '[INPUT]' AND password = 'password'

Goal: Bypass login without knowing the password.
      `,
      validation: (input) => {
        const sqlInjectionPatterns = [
          "' OR '1'='1",
          "' OR 1=1 --",
          "' OR 'x'='x",
          "admin'--"
        ];
        return sqlInjectionPatterns.some(pattern => input.toLowerCase().includes(pattern.toLowerCase()));
      },
      success: "SQL injection successful! Query returned all users, bypassing authentication.",
      hint: "Try making the WHERE clause always true with OR condition"
    },
    {
      instruction: "XSS Challenge - Steal Cookies",
      context: `
Vulnerable page code:
<div>Welcome, [INPUT]!</div>

The input is directly inserted into HTML without sanitization.
Goal: Create a payload that would steal user cookies.
      `,
      validation: (input) => {
        return (
          input.includes("<script>") &&
          (input.includes("document.cookie") || input.includes("fetch")) &&
          (input.includes("</script>") || input.includes("/>"))
        );
      },
      success: "XSS payload successful! This would steal user cookies when executed.",
      hint: "Use <script> tag to access document.cookie and send it to an attacker's server"
    },
    {
      instruction: "CSRF Challenge - Unauthorized Transfer",
      context: `
Vulnerable transfer endpoint:
POST /api/transfer
Content-Type: application/x-www-form-urlencoded

amount=[AMOUNT]&to=[ACCOUNT]

Goal: Create a form that automatically submits a transfer when loaded.
      `,
      validation: (input) => {
        return (
          input.toLowerCase().includes("<form") &&
          input.toLowerCase().includes("method='post'") &&
          input.toLowerCase().includes("action='/api/transfer'") &&
          input.toLowerCase().includes("onload") &&
          input.toLowerCase().includes("submit()")
        );
      },
      success: "CSRF attack crafted! This form would automatically submit when a user visits the page.",
      hint: "Create a form that submits automatically using onload event"
    }
  ];

  useEffect(() => {
    setContext(steps[step].context);
    setOutput([{ type: 'system', text: 'Challenge environment initialized...' }]);
  }, [step]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!input.trim()) return;
    
    const currentStep = steps[step];
    const isCorrect = currentStep.validation(input);
    
    setOutput(prev => [...prev, { type: 'input', text: `$ ${input}` }]);

    setTimeout(() => {
      if (isCorrect) {
        setOutput(prev => [
          ...prev,
          { type: 'success', text: currentStep.success },
          { type: 'system', text: 'Moving to next challenge...' }
        ]);

        if (step < steps.length - 1) {
          setTimeout(() => setStep(step + 1), 1500);
        } else {
          setSuccess(true);
          onComplete && onComplete();
        }
      } else {
        setOutput(prev => [
          ...prev,
          { type: 'error', text: 'Attempt failed.' },
          { type: 'hint', text: `Hint: ${currentStep.hint}` }
        ]);
      }
      setInput('');
    }, 500);
  };

  return (
    <div className="bg-black text-green-400 p-6 rounded-lg font-mono">
      <div className="mb-6">
        <div className="text-xs text-gray-500 mb-2">Challenge Progress: {step + 1}/{steps.length}</div>
        <div className="h-2 bg-gray-800 rounded-full">
          <div 
            className="h-full bg-green-500 rounded-full transition-all duration-300"
            style={{ width: `${((step + 1) / steps.length) * 100}%` }}
          />
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-bold mb-4">{steps[step].instruction}</h3>
        <div className="bg-black/30 rounded-lg p-4 border border-gray-800">
          <pre className="text-gray-400 text-sm whitespace-pre-wrap font-mono">
            {context}
          </pre>
        </div>
      </div>

      <div className="h-48 mb-6 bg-black/50 rounded-lg border border-gray-800 overflow-y-auto">
        <div className="p-4 space-y-2">
          {output.map((line, i) => (
            <div 
              key={i} 
              className={`${
                line.type === 'input' ? 'text-blue-400' :
                line.type === 'success' ? 'text-green-400' :
                line.type === 'error' ? 'text-red-400' :
                line.type === 'hint' ? 'text-yellow-400' :
                line.type === 'system' ? 'text-purple-400' :
                'text-gray-400'
              }`}
            >
              {line.text}
            </div>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="flex gap-2">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          className="flex-1 bg-gray-900 text-green-400 px-4 py-2 rounded-lg border border-gray-800 focus:outline-none focus:border-green-500"
          placeholder="Enter your payload..."
          disabled={success}
        />
        <button 
          type="submit"
          className="bg-green-600 text-black px-6 py-2 rounded-lg font-bold hover:bg-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={success}
        >
          Execute
        </button>
      </form>

      {success && (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-6 p-4 bg-green-500/20 text-green-400 rounded-lg border border-green-500/20"
        >
          <h4 className="text-lg font-bold mb-2">🎉 Challenge Complete!</h4>
          <p>You've successfully demonstrated understanding of:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>SQL Injection techniques</li>
            <li>Cross-Site Scripting (XSS) attacks</li>
            <li>Cross-Site Request Forgery (CSRF)</li>
          </ul>
        </motion.div>
      )}
    </div>
  );
};

export default WebExploitChallenge;