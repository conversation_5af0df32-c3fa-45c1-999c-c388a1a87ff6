import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { FaLock, FaCoins, FaLightbulb, FaTerminal, FaCode, FaCheckCircle, FaTimesCircle, FaSpinner } from 'react-icons/fa';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus, prism } from 'react-syntax-highlighter/dist/esm/styles/prism';

const ChallengeInterface = () => {
  const { challengeId } = useParams();
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const navigate = useNavigate();
  const [challenge, setChallenge] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [solution, setSolution] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [result, setResult] = useState(null);
  const [containerInfo, setContainerInfo] = useState(null);
  const [containerLoading, setContainerLoading] = useState(false);
  const [hints, setHints] = useState([]);
  const [hintsLoading, setHintsLoading] = useState(false);
  const [purchasingHint, setPurchasingHint] = useState(false);
  const [selectedHint, setSelectedHint] = useState(null);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const terminalRef = useRef(null);
  const iframeRef = useRef(null);

  // Fetch challenge details
  useEffect(() => {
    const fetchChallenge = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch challenge from database
        const { data, error } = await supabase
          .from('challenges')
          .select(`
            *,
            category:challenge_categories(*),
            difficulty:challenge_difficulty_levels(*),
            type:challenge_types(*),
            content:challenge_content(*),
            docker_config:challenge_docker_config(*)
          `)
          .eq('id', challengeId)
          .single();

        if (error) throw error;

        // Check if user has completed this challenge
        const { data: completion, error: completionError } = await supabase
          .from('challenge_completions')
          .select('*')
          .eq('challenge_id', challengeId)
          .eq('user_id', user.id)
          .single();

        if (!completionError) {
          setResult({
            correct: true,
            points: completion.score,
            message: 'You have already completed this challenge!'
          });
        }

        setChallenge({
          ...data,
          completed: completion ? true : false
        });
      } catch (error) {
        console.error('Error fetching challenge:', error);
        setError('Failed to load challenge. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchChallenge();
    } else {
      navigate('/login');
    }
  }, [challengeId, user, navigate]);

  // Fetch hints
  useEffect(() => {
    const fetchHints = async () => {
      try {
        setHintsLoading(true);

        // Fetch hints from database
        const { data, error } = await supabase
          .from('challenge_hints')
          .select('*')
          .eq('challenge_id', challengeId)
          .order('display_order', { ascending: true });

        if (error) throw error;

        // Fetch user's purchased hints
        const { data: purchasedHints, error: purchasedError } = await supabase
          .from('user_hint_purchases')
          .select('hint_id')
          .eq('user_id', user.id)
          .eq('challenge_id', challengeId);

        if (purchasedError) throw purchasedError;

        const purchasedHintIds = purchasedHints.map(h => h.hint_id);

        // Mark hints as purchased or not
        const processedHints = data.map(hint => ({
          ...hint,
          purchased: purchasedHintIds.includes(hint.id),
          hint_text: purchasedHintIds.includes(hint.id) ? hint.hint_text : null
        }));

        setHints(processedHints);
      } catch (error) {
        console.error('Error fetching hints:', error);
      } finally {
        setHintsLoading(false);
      }
    };

    if (challengeId && user) {
      fetchHints();
    }
  }, [challengeId, user]);

  // Start container if challenge requires it
  const startContainer = async () => {
    try {
      setContainerLoading(true);
      setError(null);

      // Call challenge manager API to start container
      const response = await fetch(`/api/challenges/${challengeId}/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user.id
        },
        body: JSON.stringify({})
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start challenge environment');
      }

      const data = await response.json();
      setContainerInfo(data);

      // If we have an iframe, set its src to the container URL
      if (iframeRef.current && data.instanceIp && data.instancePort) {
        const containerUrl = `http://${data.instanceIp}:${data.instancePort}?token=${data.accessToken}`;
        iframeRef.current.src = containerUrl;
      }
    } catch (error) {
      console.error('Error starting container:', error);
      setError(error.message);
    } finally {
      setContainerLoading(false);
    }
  };

  // Submit solution
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!solution.trim()) {
      setError('Please enter a solution');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);
      setResult(null);

      // Call challenge manager API to submit solution
      const response = await fetch(`/api/challenges/${challengeId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user.id
        },
        body: JSON.stringify({ solution })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit solution');
      }

      const data = await response.json();
      setResult(data);

      // If correct, update challenge state
      if (data.correct) {
        setChallenge(prev => ({
          ...prev,
          completed: true
        }));
      }
    } catch (error) {
      console.error('Error submitting solution:', error);
      setError(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  // Purchase hint
  const purchaseHint = async (hintId) => {
    try {
      setPurchasingHint(true);
      setError(null);

      // Call challenge manager API to purchase hint
      const response = await fetch(`/api/challenges/${challengeId}/hints/${hintId}/purchase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user.id
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to purchase hint');
      }

      const data = await response.json();

      // Update hints state
      setHints(prev => prev.map(hint => 
        hint.id === hintId 
          ? { ...hint, purchased: true, hint_text: data.hint_text } 
          : hint
      ));

      // Close modal
      setShowPurchaseModal(false);
      setSelectedHint(null);
    } catch (error) {
      console.error('Error purchasing hint:', error);
      setError(error.message);
    } finally {
      setPurchasingHint(false);
    }
  };

  // Handle hint click
  const handleHintClick = (hint) => {
    if (hint.purchased) {
      // If already purchased, just show the hint
      setSelectedHint(hint);
    } else {
      // If not purchased, show purchase modal
      setSelectedHint(hint);
      setShowPurchaseModal(true);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
        <div className="container mx-auto px-4">
          <div className="flex justify-center items-center h-64">
            <FaSpinner className="animate-spin text-4xl text-[#88cc14]" />
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
        <div className="container mx-auto px-4">
          <div className={`${darkMode ? 'bg-red-900/20 border-red-900/30' : 'bg-red-100 border-red-200'} border rounded-lg p-6 text-center`}>
            <h2 className="text-2xl font-bold text-red-500 mb-4">Error</h2>
            <p className={`${darkMode ? 'text-red-300' : 'text-red-700'}`}>{error}</p>
            <button 
              onClick={() => navigate('/challenges')}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
            >
              Back to Challenges
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render challenge not found state
  if (!challenge) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
        <div className="container mx-auto px-4">
          <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 text-center`}>
            <h2 className="text-2xl font-bold mb-4">Challenge Not Found</h2>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>The challenge you're looking for doesn't exist or you don't have access to it.</p>
            <button 
              onClick={() => navigate('/challenges')}
              className="mt-4 px-4 py-2 theme-button-primary rounded-lg"
            >
              Back to Challenges
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        {/* Challenge Header */}
        <div className="mb-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold">{challenge.title}</h1>
              <div className="flex flex-wrap gap-2 mt-2">
                <span className={`px-3 py-1 rounded-full text-sm ${darkMode ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-800'}`}>
                  {challenge.category.name}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm ${darkMode ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-800'}`}>
                  {challenge.difficulty.name}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm ${darkMode ? 'bg-green-900/30 text-green-300' : 'bg-green-100 text-green-800'}`}>
                  {challenge.points} Points
                </span>
                {challenge.completed && (
                  <span className={`px-3 py-1 rounded-full text-sm ${darkMode ? 'bg-[#88cc14]/20 text-[#88cc14]' : 'bg-[#88cc14]/20 text-[#88cc14]'}`}>
                    Completed
                  </span>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <button 
                onClick={() => navigate('/challenges')}
                className={`px-4 py-2 ${darkMode ? 'bg-[#1A1F35] hover:bg-[#252D4A] text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-800'} rounded-lg`}
              >
                Back to Challenges
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Challenge Description */}
          <div className="lg:col-span-2">
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
              <h2 className="text-xl font-bold mb-4">Description</h2>
              <div className="prose max-w-none mb-6 theme-prose">
                <ReactMarkdown
                  components={{
                    code({node, inline, className, children, ...props}) {
                      const match = /language-(\w+)/.exec(className || '')
                      return !inline && match ? (
                        <SyntaxHighlighter
                          style={darkMode ? vscDarkPlus : prism}
                          language={match[1]}
                          PreTag="div"
                          {...props}
                        >
                          {String(children).replace(/\n$/, '')}
                        </SyntaxHighlighter>
                      ) : (
                        <code className={className} {...props}>
                          {children}
                        </code>
                      )
                    }
                  }}
                >
                  {challenge.description}
                </ReactMarkdown>
              </div>

              {/* Challenge Environment (if applicable) */}
              {challenge.docker_config && (
                <div className="mb-6">
                  <h2 className="text-xl font-bold mb-4">Challenge Environment</h2>
                  {containerInfo ? (
                    <div>
                      <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4 mb-4`}>
                        <div className="flex justify-between items-center">
                          <div>
                            <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                              Your environment is running and will expire in{' '}
                              <span className="font-bold">
                                {new Date(containerInfo.expiresAt) > new Date() 
                                  ? Math.ceil((new Date(containerInfo.expiresAt) - new Date()) / (1000 * 60)) 
                                  : 0} minutes
                              </span>
                            </p>
                          </div>
                          <button
                            onClick={startContainer}
                            className="px-4 py-2 theme-button-primary rounded-lg"
                            disabled={containerLoading}
                          >
                            {containerLoading ? 'Restarting...' : 'Restart Environment'}
                          </button>
                        </div>
                      </div>
                      <div className={`${darkMode ? 'bg-black border-gray-800' : 'bg-white border-gray-300'} border rounded-lg overflow-hidden`} style={{ height: '500px' }}>
                        <iframe
                          ref={iframeRef}
                          title="Challenge Environment"
                          className="w-full h-full"
                          sandbox="allow-same-origin allow-scripts allow-forms"
                        ></iframe>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FaTerminal className="mx-auto text-4xl mb-4 text-gray-400" />
                      <p className="mb-4">Start the challenge environment to begin.</p>
                      <button
                        onClick={startContainer}
                        className="px-4 py-2 theme-button-primary rounded-lg"
                        disabled={containerLoading}
                      >
                        {containerLoading ? (
                          <>
                            <FaSpinner className="inline animate-spin mr-2" />
                            Starting Environment...
                          </>
                        ) : (
                          'Start Environment'
                        )}
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Solution Submission */}
              <div>
                <h2 className="text-xl font-bold mb-4">Submit Solution</h2>
                {result ? (
                  <div className={`${result.correct 
                    ? darkMode ? 'bg-green-900/20 border-green-900/30 text-green-300' : 'bg-green-100 border-green-200 text-green-800'
                    : darkMode ? 'bg-red-900/20 border-red-900/30 text-red-300' : 'bg-red-100 border-red-200 text-red-800'
                  } border rounded-lg p-4 mb-4`}>
                    <div className="flex items-center">
                      {result.correct ? (
                        <FaCheckCircle className="text-green-500 mr-2 text-xl" />
                      ) : (
                        <FaTimesCircle className="text-red-500 mr-2 text-xl" />
                      )}
                      <p>{result.message}</p>
                    </div>
                    {result.correct && (
                      <p className="mt-2">
                        You earned <span className="font-bold">{result.points}</span> points!
                      </p>
                    )}
                  </div>
                ) : null}
                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <label htmlFor="solution" className="block mb-2">Enter your solution:</label>
                    <input
                      type="text"
                      id="solution"
                      className={`w-full p-3 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                      value={solution}
                      onChange={(e) => setSolution(e.target.value)}
                      placeholder="XCerberus{flag_format}"
                      required
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full px-4 py-3 theme-button-primary rounded-lg font-bold"
                    disabled={submitting || challenge.completed}
                  >
                    {submitting ? (
                      <>
                        <FaSpinner className="inline animate-spin mr-2" />
                        Submitting...
                      </>
                    ) : challenge.completed ? (
                      'Already Completed'
                    ) : (
                      'Submit Solution'
                    )}
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Hints */}
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6 mb-6`}>
              <h2 className="text-xl font-bold mb-4">Hints</h2>
              {hintsLoading ? (
                <div className="flex justify-center py-4">
                  <FaSpinner className="animate-spin text-2xl text-[#88cc14]" />
                </div>
              ) : hints.length === 0 ? (
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>No hints available for this challenge.</p>
              ) : (
                <div className="space-y-3">
                  {hints.map((hint) => (
                    <div 
                      key={hint.id}
                      className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4 cursor-pointer hover:border-[#88cc14] transition-colors`}
                      onClick={() => handleHintClick(hint)}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <FaLightbulb className={`mr-2 ${hint.purchased ? 'text-[#88cc14]' : 'text-gray-400'}`} />
                          <span className="font-medium">Hint {hint.display_order}</span>
                        </div>
                        {hint.purchased ? (
                          <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-green-900/30 text-green-300' : 'bg-green-100 text-green-800'}`}>
                            Unlocked
                          </span>
                        ) : (
                          <span className="flex items-center">
                            <FaCoins className="text-yellow-500 mr-1" />
                            <span>{hint.coin_cost}</span>
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Challenge Details */}
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg p-6`}>
              <h2 className="text-xl font-bold mb-4">Challenge Details</h2>
              <div className="space-y-3">
                <div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Category</p>
                  <p className="font-medium">{challenge.category.name}</p>
                </div>
                <div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Difficulty</p>
                  <p className="font-medium">{challenge.difficulty.name}</p>
                </div>
                <div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Points</p>
                  <p className="font-medium">{challenge.points}</p>
                </div>
                {challenge.estimated_time && (
                  <div>
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Estimated Time</p>
                    <p className="font-medium">{challenge.estimated_time} minutes</p>
                  </div>
                )}
                {challenge.author && (
                  <div>
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Author</p>
                    <p className="font-medium">{challenge.author.username}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hint Purchase Modal */}
      {showPurchaseModal && selectedHint && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${darkMode ? 'bg-[#1A1F35] text-white' : 'bg-white text-gray-900'} rounded-lg p-6 max-w-md w-full`}>
            <h2 className="text-xl font-bold mb-4">Purchase Hint</h2>
            <p className="mb-4">
              Are you sure you want to purchase this hint for <span className="font-bold">{selectedHint.coin_cost}</span> coins?
            </p>
            <p className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-3 mb-4`}>
              Your current balance: <span className="font-bold">{profile?.coins || 0}</span> coins
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowPurchaseModal(false);
                  setSelectedHint(null);
                }}
                className={`px-4 py-2 ${darkMode ? 'bg-[#252D4A] hover:bg-[#313e6a] text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-800'} rounded-lg`}
                disabled={purchasingHint}
              >
                Cancel
              </button>
              <button
                onClick={() => purchaseHint(selectedHint.id)}
                className="px-4 py-2 theme-button-primary rounded-lg"
                disabled={purchasingHint || (profile?.coins || 0) < selectedHint.coin_cost}
              >
                {purchasingHint ? (
                  <>
                    <FaSpinner className="inline animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  'Purchase Hint'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Hint View Modal */}
      {selectedHint && selectedHint.purchased && !showPurchaseModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${darkMode ? 'bg-[#1A1F35] text-white' : 'bg-white text-gray-900'} rounded-lg p-6 max-w-md w-full`}>
            <h2 className="text-xl font-bold mb-4">Hint {selectedHint.display_order}</h2>
            <div className={`${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-100 border-gray-300'} border rounded-lg p-4 mb-4`}>
              <p>{selectedHint.hint_text}</p>
            </div>
            <div className="flex justify-end">
              <button
                onClick={() => setSelectedHint(null)}
                className="px-4 py-2 theme-button-primary rounded-lg"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChallengeInterface;
