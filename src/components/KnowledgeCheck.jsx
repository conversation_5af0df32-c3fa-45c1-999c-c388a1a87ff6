import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaRedo } from 'react-icons/fa';

const KnowledgeCheck = ({ questions }) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);

  if (!questions || questions.length === 0) {
    return null;
  }

  const handleAnswer = (questionIndex, optionIndex) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionIndex]: optionIndex
    }));
  };

  const calculateScore = () => {
    let correctAnswers = 0;
    Object.entries(selectedAnswers).forEach(([questionIndex, answer]) => {
      if (questions[questionIndex].correct === answer) {
        correctAnswers++;
      }
    });
    return correctAnswers;
  };

  const handleSubmit = () => {
    const finalScore = calculateScore();
    setScore(finalScore);
    setShowResults(true);
  };

  const resetQuiz = () => {
    setSelectedAnswers({});
    setShowResults(false);
    setScore(0);
    setCurrentQuestion(0);
  };

  const isQuizComplete = Object.keys(selectedAnswers).length === questions.length;

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
    >
      <h3 className="text-xl font-bold text-gray-900 mb-6">
        Knowledge Check
      </h3>

      {!showResults ? (
        <div className="space-y-8">
          {questions.map((question, questionIndex) => (
            <motion.div
              key={questionIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: questionIndex * 0.1 }}
              className="space-y-4"
            >
              <p className="font-medium text-gray-900">
                {questionIndex + 1}. {question.question}
              </p>
              <div className="space-y-2">
                {question.options.map((option, optionIndex) => (
                  <button
                    key={optionIndex}
                    onClick={() => handleAnswer(questionIndex, optionIndex)}
                    className={`w-full text-left p-4 rounded-lg transition-all ${
                      selectedAnswers[questionIndex] === optionIndex
                        ? 'bg-[#88cc14]/10 border-[#88cc14] border'
                        : 'bg-gray-50 hover:bg-[#88cc14]/5 border border-gray-200'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-5 h-5 rounded-full border ${
                        selectedAnswers[questionIndex] === optionIndex
                          ? 'border-[#88cc14] bg-[#88cc14]'
                          : 'border-gray-300'
                      } flex items-center justify-center`}>
                        {selectedAnswers[questionIndex] === optionIndex && (
                          <FaCheck className="text-white text-xs" />
                        )}
                      </div>
                      <span className="text-gray-700">{option}</span>
                    </div>
                  </button>
                ))}
              </div>
            </motion.div>
          ))}

          <button
            onClick={handleSubmit}
            disabled={!isQuizComplete}
            className={`w-full py-3 px-4 rounded-lg font-bold transition-all ${
              isQuizComplete
                ? 'bg-[#88cc14] text-black hover:bg-[#7ab811]'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }`}
          >
            Submit Answers
          </button>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="space-y-6"
        >
          <div className="text-center">
            <div className="text-4xl font-bold mb-2">
              {score} / {questions.length}
            </div>
            <p className="text-gray-600">
              {score === questions.length
                ? "Perfect score! You've mastered this topic!"
                : score > questions.length / 2
                ? "Good job! Keep practicing to improve further."
                : "Keep studying! You'll get better with practice."}
            </p>
          </div>

          <div className="space-y-6">
            {questions.map((question, questionIndex) => {
              const isCorrect = selectedAnswers[questionIndex] === question.correct;
              return (
                <div
                  key={questionIndex}
                  className={`p-4 rounded-lg ${
                    isCorrect ? 'bg-green-50' : 'bg-red-50'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    {isCorrect ? (
                      <FaCheck className="text-green-500 mt-1" />
                    ) : (
                      <FaTimes className="text-red-500 mt-1" />
                    )}
                    <div>
                      <p className="font-medium text-gray-900 mb-2">
                        {question.question}
                      </p>
                      <p className="text-sm text-gray-600">
                        {question.explanation}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          <button
            onClick={resetQuiz}
            className="w-full flex items-center justify-center gap-2 bg-gray-900 text-white py-3 px-4 rounded-lg font-bold hover:bg-gray-800 transition-all"
          >
            <FaRedo />
            Try Again
          </button>
        </motion.div>
      )}
    </motion.div>
  );
};

export default KnowledgeCheck;