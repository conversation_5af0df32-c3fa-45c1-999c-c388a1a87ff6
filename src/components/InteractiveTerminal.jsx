import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Typed from 'typed.js';

function InteractiveTerminal() {
  const [input, setInput] = useState('');
  const [history, setHistory] = useState([]);
  const [currentDir, setCurrentDir] = useState('/XCerberus');
  const [isTyping, setIsTyping] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [loggedIn, setLoggedIn] = useState(false);
  const [username, setUsername] = useState('guest');
  const [currentView, setCurrentView] = useState(null);
  const [authStage, setAuthStage] = useState(0);
  const [authData, setAuthData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const terminalRef = useRef(null);
  const inputRef = useRef(null);
  const contentRef = useRef(null);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Initialize typed.js
  useEffect(() => {
    if (isMobile || !terminalRef.current) return;

    let typed;
    try {
      const element = terminalRef.current.querySelector('.typed-text');
      if (!element) return;

      typed = new Typed(element, {
        strings: [
          'Welcome to XCerberus Terminal v2.5.0\nEstablishing secure connection...\nConnection established.\nType "help" to see available commands.'
        ],
        typeSpeed: 40,
        showCursor: true,
        cursorChar: '█',
        onComplete: () => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }
      });
    } catch (error) {
      console.error('Error initializing typed.js:', error);
    }

    return () => {
      if (typed) typed.destroy();
    };
  }, [isMobile]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim() || isTyping) return;

    // Add command to history
    setHistory(prev => [...prev, {
      type: 'input',
      text: `${loggedIn ? username : 'guest'}@XCerberus:${currentDir}$ ${input}`,
      timestamp: new Date().toLocaleTimeString()
    }]);

    // Process command
    const command = input.trim().toLowerCase();
    let response = [];

    switch (command) {
      case 'help':
        response = [
          { type: 'success', text: '\nAvailable Commands:' },
          { type: 'output', text: '  help     - Show this help message' },
          { type: 'output', text: '  clear    - Clear terminal screen' },
          { type: 'output', text: '  whoami   - Show current user' },
          { type: 'output', text: '  pwd      - Show current directory' },
          { type: 'output', text: '  ls       - List files and directories' },
          { type: 'output', text: '  cd       - Change directory' }
        ];
        break;

      case 'clear':
        setHistory([]);
        break;

      case 'whoami':
        response = [
          { type: 'output', text: loggedIn ? username : 'guest (not logged in)' }
        ];
        break;

      case 'pwd':
        response = [
          { type: 'output', text: currentDir }
        ];
        break;

      case 'ls':
        response = [
          {
            type: 'output',
            text: '<span class="text-primary">courses/</span>  <span class="text-primary">challenges/</span>  <span class="text-primary">tools/</span>  README.md',
            isHtml: true
          }
        ];
        break;

      default:
        response = [
          {
            type: 'error',
            text: `Command not found: ${command}. Type 'help' for available commands.`
          }
        ];
    }

    // Add response to history
    setHistory(prev => [...prev, ...response.map(line => ({
      ...line,
      timestamp: new Date().toLocaleTimeString()
    }))]);

    // Clear input
    setInput('');

    // Scroll to bottom
    setTimeout(() => {
      if (contentRef.current) {
        contentRef.current.scrollTop = contentRef.current.scrollHeight;
      }
    }, 0);
  };

  const handleTerminalClick = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // If mobile, show a simplified version
  if (isMobile) {
    return (
      <div className="terminal w-full max-w-2xl mx-auto font-mono text-sm relative bg-[#1c1c1c] border border-[#2d2d2d] rounded-lg overflow-hidden">
        <div className="terminal-header">
          <div className="flex items-center justify-between p-3">
            <div className="flex items-center gap-2">
              <div className="flex gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <span className="text-gray-400 text-sm ml-2">guest@XCerberus:~</span>
            </div>
          </div>
        </div>
        <div className="p-4 text-primary">
          <div>Welcome to XCerberus Terminal v2.5.0</div>
          <div>Establishing secure connection...</div>
          <div>Connection established.</div>
          <div>Type "help" to see available commands.</div>
          <div className="mt-4 flex items-center">
            <span className="text-primary whitespace-nowrap select-none">guest@XCerberus:~$</span>
            <span className="ml-2 animate-pulse">█</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={terminalRef}
      className="terminal w-full max-w-2xl mx-auto font-mono text-sm relative bg-[#1c1c1c] border border-[#2d2d2d] rounded-lg overflow-hidden shadow-xl"
      onClick={handleTerminalClick}
    >
      <div className="terminal-header sticky top-0 z-10 bg-[#252525] border-b border-gray-800">
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center gap-2">
            <div className="flex gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
            </div>
            <span className="text-gray-400 text-sm ml-2">
              {loggedIn ? (
                <span className="text-primary">{username}@XCerberus:{currentDir}</span>
              ) : (
                <span>guest@XCerberus:{currentDir}</span>
              )}
            </span>
          </div>
          <div className="text-gray-400 text-xs flex items-center gap-2">
            {loggedIn ? (
              <span className="bg-primary/20 text-primary px-2 py-0.5 rounded-full text-xs">
                Secure
              </span>
            ) : (
              <span className="bg-yellow-500/20 text-yellow-500 px-2 py-0.5 rounded-full text-xs">
                Guest
              </span>
            )}
            <span>{new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </div>

      <div
        ref={contentRef}
        className="p-4 h-[calc(500px-80px)] overflow-y-auto pb-16 bg-[#1c1c1c]"
      >
        <div className="typed-text mb-4 text-primary"></div>

        <AnimatePresence>
          {history.map((entry, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
              className={`mb-2 font-mono leading-relaxed ${
                entry.type === 'input' ? 'text-primary' :
                entry.type === 'success' ? 'text-green-400' :
                entry.type === 'error' ? 'text-red-400' :
                entry.type === 'warning' ? 'text-yellow-400' :
                entry.type === 'scanning' ? 'text-blue-400' :
                entry.type === 'system' ? 'text-purple-400' :
                'text-gray-300'
              }`}
            >
              {entry.type === 'input' ? (
                <div className="flex items-center gap-2">
                  <span className="text-primary">{entry.text}</span>
                </div>
              ) : entry.isHtml ? (
                <div
                  className="pl-0"
                  dangerouslySetInnerHTML={{ __html: entry.text }}
                />
              ) : (
                <div className="pl-0 whitespace-pre-wrap">
                  {entry.text}
                  {entry.children?.map((child, j) => (
                    <div key={j} className="pl-4 text-gray-400">
                      {child}
                    </div>
                  ))}
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {isTyping && (
          <div className="flex items-center gap-1 text-primary">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        )}

        <div className="h-16"></div>
      </div>

      <div className="absolute bottom-0 left-0 right-0 bg-[#1c1c1c] border-t border-gray-800 p-4">
        <form
          onSubmit={handleSubmit}
          className="flex items-center gap-2"
        >
          <motion.span
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-primary whitespace-nowrap select-none"
          >
            {loggedIn ? (
              <span className="text-primary">{username}@XCerberus:{currentDir}$</span>
            ) : (
              <>guest@XCerberus:{currentDir}$</>
            )}
          </motion.span>
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="flex-1 bg-transparent border-none outline-none text-primary min-w-0 font-mono"
            autoFocus
            disabled={isTyping}
            spellCheck="false"
            autoComplete="off"
            autoCapitalize="off"
          />
        </form>
      </div>
    </div>
  );
}

export default InteractiveTerminal;