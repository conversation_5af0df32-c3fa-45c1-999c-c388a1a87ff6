import React from 'react';
import { motion } from 'framer-motion';
import { useChat } from '../../contexts/ChatContext';
import CyberForceIcon from '../icons/CyberForceIcon';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

function ChatButton() {
  const { toggleChat } = useChat();
  const { darkMode } = useGlobalTheme();

  return (
    <motion.button
      onClick={toggleChat}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="fixed bottom-20 right-4 md:bottom-4 md:right-4 bg-[#4A5CBA] text-white p-3 rounded-full shadow-xl hover:bg-[#3A4CAA] transition-colors z-50 group border-2 border-[#F5B93F]/50"
    >
      <CyberForceIcon size={24} />
      <span className="absolute -top-2 -right-2 w-3 h-3 bg-[#F5B93F] rounded-full animate-pulse"></span>
      <div className="absolute -top-10 right-0 bg-black/80 backdrop-blur-sm text-white text-xs px-3 py-1.5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
        Ask Cyber<span className="text-[#F5B93F]">Force</span> AI
      </div>
    </motion.button>
  );
}

export default ChatButton;