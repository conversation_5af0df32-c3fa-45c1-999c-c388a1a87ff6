import React, { useRef } from 'react';
import { FaPaperPlane, FaLightbulb, FaTimes } from 'react-icons/fa';
import { useChat } from '../../contexts/ChatContext';
import { motion } from 'framer-motion';

function ChatInput() {
  const { state, setInput, setShowSuggestions, handleSubmit } = useChat();
  const { input, isTyping } = state;
  const inputRef = useRef(null);

  const onSubmit = (e) => {
    e.preventDefault();
    if (!input.trim() || isTyping) return;

    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('Submitting input:', input);
    }

    handleSubmit(input);
  };

  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!input.trim() || isTyping) return;
      handleSubmit(input);
    }
  };

  // Also handle button click separately
  const handleSendClick = () => {
    if (!input.trim() || isTyping) return;

    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('Send button clicked, submitting:', input);
    }

    handleSubmit(input);
  };

  return (
    <form
      onSubmit={onSubmit}
      className="p-3 bg-[#0B1120] border-t border-gray-800"
    >
      <div className="flex items-center gap-2 bg-[#1A1F35] rounded-lg p-2 shadow-inner border border-[#4A5CBA]/30">
        <motion.span
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-[#F5B93F] whitespace-nowrap select-none ml-2 font-mono text-sm hidden sm:inline-block"
        >
          CyberForce@ai:~$
        </motion.span>
        <div className="flex-1 relative">
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full bg-transparent border-none outline-none text-white min-w-0 font-mono pr-6 text-sm"
            placeholder={isTyping ? "AI is thinking..." : "Ask me about cybersecurity..."}
            disabled={isTyping}
            spellCheck="false"
            autoComplete="off"
            autoCapitalize="off"
          />
          {input && !isTyping && (
            <button
              type="button"
              onClick={() => setInput('')}
              className="absolute right-0 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white transition-colors"
            >
              <FaTimes className="text-xs" />
            </button>
          )}
        </div>
        <button
          type="button"
          onClick={handleSendClick}
          disabled={!input.trim() || isTyping}
          className={`p-2 rounded-full ${
            !input.trim() || isTyping
              ? 'text-gray-600 cursor-not-allowed'
              : 'text-[#F5B93F] hover:bg-[#4A5CBA]/30'
          } transition-colors`}
          title="Send message"
        >
          <FaPaperPlane className="text-sm" />
        </button>
      </div>
      <div className="flex justify-between items-center mt-2 px-1">
        <div className="flex items-center gap-1 text-xs text-gray-400">
          <FaLightbulb className="text-[#F5B93F] text-xs" />
          <span className="text-xs hidden sm:inline-block">Ask about cybersecurity topics, tools, or techniques</span>
          <span className="text-xs inline-block sm:hidden">Ask about cybersecurity</span>
        </div>
        <button
          type="button"
          onClick={() => setShowSuggestions(prev => !prev)}
          className="text-gray-500 hover:text-[#F5B93F] transition-colors p-1"
          title="Show suggestions"
        >
          <FaLightbulb className="text-xs" />
        </button>
      </div>
    </form>
  );
}

export default ChatInput;