import React, { useRef, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaMinus,
  FaExpand,
  FaTimes,
  FaHistory,
  FaUser,
  FaTrash,
  FaCog,
  FaDownload,
  FaQuestionCircle,
  FaRobot,
  FaLanguage
} from 'react-icons/fa';
import { useChat } from '../../contexts/ChatContext';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import ChatSuggestions from './ChatSuggestions';
import CyberForceIcon from '../icons/CyberForceIcon';
import { useLanguage } from '../../contexts/LanguageContext';

function ChatWindow() {
  const { state, dispatch, minimizeChat, maximizeChat, toggleExpand, clearMessages, setMessages } = useChat();
  const { isMinimized, isExpanded, conversationHistory } = state;
  const [showHistory, setShowHistory] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const { currentLanguage, changeLanguage } = useLanguage();
  const chatContainerRef = useRef(null);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (chatContainerRef.current) {
        // Adjust chat container based on window size
        if (window.innerWidth < 640 && isExpanded) {
          // On small screens, make sure the chat doesn't overflow
          chatContainerRef.current.style.maxHeight = `${window.innerHeight - 20}px`;
        }
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call once on mount

    return () => window.removeEventListener('resize', handleResize);
  }, [isExpanded]);

  if (isMinimized) {
    return (
      <motion.button
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        onClick={maximizeChat}
        className="fixed bottom-4 right-4 bg-[#4A5CBA] text-white p-4 rounded-full shadow-lg hover:bg-[#3A4CAA] transition-colors z-50 group border border-[#4A5CBA]/30"
      >
        <CyberForceIcon size={28} />
        <span className="absolute -top-2 -right-2 w-4 h-4 bg-red-500 rounded-full animate-pulse"></span>
        <div className="absolute -top-10 right-0 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
          CyberForce AI
        </div>
      </motion.button>
    );
  }

  return (
    <div
      ref={chatContainerRef}
      className={`fixed ${
        isExpanded
          ? 'inset-4 md:inset-10'
          : 'bottom-4 right-4 w-[90vw] max-w-md h-[500px] md:h-[550px]'
      } z-50 transition-all duration-300`}
    >
      <motion.div
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 50, opacity: 0 }}
        className="bg-gray-900 rounded-lg shadow-2xl border-2 border-[#4A5CBA]/30 overflow-hidden w-full h-full flex flex-col"
      >
        {/* Header */}
        <div className="bg-[#0B1120] p-3 flex items-center justify-between border-b border-gray-800">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-[#4A5CBA]/20 flex items-center justify-center border border-[#F5B93F]/50">
              <CyberForceIcon size={20} />
            </div>
            <div>
              <h3 className="font-bold text-white text-sm">Cyber<span className="text-[#F5B93F]">Force</span> AI</h3>
              <p className="text-xs text-gray-400">Advanced Cybersecurity Assistant</p>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={() => {
                setShowHistory(false);
                setShowSettings(false);
                setShowHelp(!showHelp);
              }}
              className="text-gray-400 hover:text-[#F5B93F] transition-colors p-1"
              title="Help"
            >
              <FaQuestionCircle className="text-xs" />
            </button>
            <button
              onClick={() => {
                setShowHistory(false);
                setShowHelp(false);
                setShowSettings(!showSettings);
              }}
              className="text-gray-400 hover:text-[#F5B93F] transition-colors p-1"
              title="Settings"
            >
              <FaCog className="text-xs" />
            </button>
            <button
              onClick={() => {
                setShowSettings(false);
                setShowHelp(false);
                setShowHistory(!showHistory);
              }}
              className="text-gray-400 hover:text-[#F5B93F] transition-colors p-1"
              title="Conversation History"
            >
              <FaHistory className="text-xs" />
            </button>
            <button
              onClick={clearMessages}
              className="text-gray-400 hover:text-[#F5B93F] transition-colors p-1"
              title="Clear Conversation"
            >
              <FaTrash className="text-xs" />
            </button>
            <button
              onClick={minimizeChat}
              className="text-gray-400 hover:text-[#F5B93F] transition-colors p-1"
              title="Minimize"
            >
              <FaMinus className="text-xs" />
            </button>
            <button
              onClick={toggleExpand}
              className="text-gray-400 hover:text-[#F5B93F] transition-colors p-1"
              title={isExpanded ? "Minimize" : "Expand"}
            >
              <FaExpand className="text-xs" />
            </button>
          </div>
        </div>

        {/* Conversation History Panel */}
        {showHistory && (
          <div className="bg-gray-800 border-b border-gray-700 p-2 max-h-60 overflow-y-auto">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-xs font-bold text-gray-400">Conversation History</h4>
              <button
                onClick={() => {
                  clearMessages();
                  dispatch({ type: 'CLEAR_ACTIVE_CONVERSATION' });
                  setShowHistory(false);
                }}
                className="text-xs text-[#F5B93F] hover:text-white transition-colors px-2 py-1 rounded bg-gray-700 hover:bg-gray-600"
              >
                New Chat
              </button>
            </div>

            {conversationHistory && conversationHistory.length > 0 ? (
              <div className="space-y-2">
                {conversationHistory.map((conversation) => (
                  <div
                    key={conversation.id}
                    className="p-2 bg-gray-700 rounded cursor-pointer hover:bg-gray-600 transition-colors"
                    onClick={() => {
                      // Load this conversation
                      setMessages(conversation.messages);
                      dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: conversation.id });
                      setShowHistory(false);
                    }}
                  >
                    <div className="flex items-center gap-1 mb-1">
                      <FaUser className="text-xs text-blue-400" />
                      <span className="text-xs text-gray-300 truncate">
                        {conversation.title}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">
                        {conversation.messages.length} messages
                      </span>
                      <span className="text-xs text-gray-500">
                        {conversation.updatedAt.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <p className="text-xs">No conversation history found</p>
              </div>
            )}
          </div>
        )}

        {/* Settings Panel */}
        {showSettings && (
          <div className="bg-gray-800 border-b border-gray-700 p-3 max-h-80 overflow-y-auto">
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-sm font-bold text-white">AI Assistant Settings</h4>
              <button
                onClick={() => setShowSettings(false)}
                className="text-gray-400 hover:text-white transition-colors p-1"
              >
                <FaTimes className="text-xs" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <h5 className="text-xs font-medium text-gray-300 mb-2">Language</h5>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => changeLanguage('en')}
                    className={`flex items-center gap-2 p-2 rounded-lg border ${
                      currentLanguage === 'en'
                        ? 'border-[#F5B93F] bg-[#F5B93F]/10'
                        : 'border-gray-700 hover:bg-gray-700'
                    }`}
                  >
                    <FaLanguage className="text-[#F5B93F] text-xs" />
                    <span className="text-white text-xs">English</span>
                  </button>
                  <button
                    onClick={() => changeLanguage('ar')}
                    className={`flex items-center gap-2 p-2 rounded-lg border ${
                      currentLanguage === 'ar'
                        ? 'border-[#F5B93F] bg-[#F5B93F]/10'
                        : 'border-gray-700 hover:bg-gray-700'
                    }`}
                  >
                    <FaLanguage className="text-[#F5B93F] text-xs" />
                    <span className="text-white text-xs">العربية</span>
                  </button>
                </div>
              </div>

              <div>
                <h5 className="text-xs font-medium text-gray-300 mb-2">AI Model</h5>
                <div className="bg-gray-700 p-2 rounded-lg border border-gray-600">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FaRobot className="text-[#F5B93F] text-xs" />
                      <span className="text-white text-xs">CyberForce AI</span>
                    </div>
                    <span className="text-[0.65rem] bg-[#F5B93F]/20 text-[#F5B93F] px-1.5 py-0.5 rounded">Active</span>
                  </div>
                  <p className="text-[0.65rem] text-gray-400 mt-1">
                    Specialized in cybersecurity topics, threat intelligence, and security best practices.
                  </p>
                </div>
              </div>

              <div>
                <h5 className="text-xs font-medium text-gray-300 mb-2">Chat Options</h5>
                <div className="space-y-2">
                  <button className="w-full flex items-center justify-between p-2 bg-gray-700 hover:bg-gray-600 rounded-lg border border-gray-600">
                    <div className="flex items-center gap-2">
                      <FaDownload className="text-[#F5B93F] text-xs" />
                      <span className="text-white text-xs">Export Conversation</span>
                    </div>
                  </button>
                  <button
                    onClick={clearMessages}
                    className="w-full flex items-center justify-between p-2 bg-gray-700 hover:bg-gray-600 rounded-lg border border-gray-600"
                  >
                    <div className="flex items-center gap-2">
                      <FaTrash className="text-red-500 text-xs" />
                      <span className="text-white text-xs">Clear Current Conversation</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Help Panel */}
        {showHelp && (
          <div className="bg-gray-800 border-b border-gray-700 p-3 max-h-80 overflow-y-auto">
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-sm font-bold text-white">Help & Information</h4>
              <button
                onClick={() => setShowHelp(false)}
                className="text-gray-400 hover:text-white transition-colors p-1"
              >
                <FaTimes className="text-xs" />
              </button>
            </div>

            <div className="space-y-3">
              <div>
                <h5 className="text-xs font-medium text-gray-300 mb-1">About CyberForce AI</h5>
                <p className="text-[0.65rem] text-gray-300">
                  CyberForce AI is an advanced cybersecurity assistant designed to help you with security topics,
                  threat intelligence, and best practices. It can answer questions about various cybersecurity domains
                  and provide guidance on security tools and techniques.
                </p>
              </div>

              <div>
                <h5 className="text-xs font-medium text-gray-300 mb-1">Example Questions</h5>
                <div className="space-y-1">
                  <div className="bg-gray-700 p-1.5 rounded text-[0.65rem] text-gray-300 border-l-2 border-[#F5B93F]">
                    "What is a SIEM system and how does it work?"
                  </div>
                  <div className="bg-gray-700 p-1.5 rounded text-[0.65rem] text-gray-300 border-l-2 border-[#F5B93F]">
                    "Explain the concept of Zero Trust architecture"
                  </div>
                  <div className="bg-gray-700 p-1.5 rounded text-[0.65rem] text-gray-300 border-l-2 border-[#F5B93F]">
                    "What are the best practices for incident response?"
                  </div>
                </div>
              </div>

              <div>
                <h5 className="text-xs font-medium text-gray-300 mb-1">Tips</h5>
                <ul className="list-disc list-inside text-[0.65rem] text-gray-300 space-y-0.5 pl-1">
                  <li>Be specific with your questions for better answers</li>
                  <li>You can ask follow-up questions for more details</li>
                  <li>Use the settings panel to change language preferences</li>
                  <li>Access your conversation history from the history panel</li>
                  <li>Press Alt+A as a keyboard shortcut to open the chat</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Messages */}
        <ChatMessages />

        {/* Input */}
        <ChatInput />

        {/* Suggestions */}
        <ChatSuggestions />
      </motion.div>
    </div>
  );
}

export default ChatWindow;