import {
  FaBook, FaUserGraduate, FaRocket, FaServer, FaCode,
  FaCloud, FaWifi, FaShieldAlt, FaLock, FaVirus,
  FaEnvelope, FaAward, FaTrophy, FaFlag, FaPlay,
  FaCheck, FaCalendar, FaCalendarAlt, FaGift, FaHeadset,
  FaQuestionCircle, FaDiscord, FaUserSecret, FaNetworkWired,
  FaDatabase, FaLaptopCode, FaGamepad, FaBug, FaTerminal,
  FaChartLine, FaUsers, FaGraduationCap, FaCrown
} from 'react-icons/fa';

export const navSections = {
  learn: [
    { id: 'learning-modules', label: 'Learning Modules', icon: FaBook, route: '/learn/modules' },
    { id: 'certifications', label: 'Certifications', icon: FaUserGraduate, route: '/learn/certifications' },
    { id: 'career-paths', label: 'Career Paths', icon: FaRocket, route: '/learn/paths' }
  ],
  startHack: {
    attack: [
      { id: 'web-penetration', label: 'Web Penetration', icon: FaCode, route: '/hack/web' },
      { id: 'network-penetration', label: 'Network Penetration', icon: FaNetworkWired, route: '/hack/network' },
      { id: 'cloud-security', label: 'Cloud Security', icon: FaCloud, route: '/hack/cloud' },
      { id: 'wireless-security', label: 'Wireless Security', icon: FaWifi, route: '/hack/wireless' }
    ],
    defence: [
      { id: 'siem', label: 'SIEM', icon: FaShieldAlt, route: '/hack/siem' },
      { id: 'incident-response', label: 'Incident Response', icon: FaLock, route: '/hack/incident' },
      { id: 'threat-hunting', label: 'Threat Hunting', icon: FaUserSecret, route: '/hack/threat-hunting' }
    ],
    malware: [
      { id: 'malware-analysis', label: 'Malware Analysis', icon: FaVirus, route: '/hack/malware' },
      { id: 'reverse-engineering', label: 'Reverse Engineering', icon: FaBug, route: '/hack/reverse' },
      { id: 'exploit-development', label: 'Exploit Development', icon: FaTerminal, route: '/hack/exploit' }
    ]
  },
  challenges: [
    { id: 'all-challenges', label: 'All Challenges', icon: FaFlag, route: '/challenges' }
  ]
};