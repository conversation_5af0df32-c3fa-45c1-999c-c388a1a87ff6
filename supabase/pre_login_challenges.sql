-- Pre-login Challenges for XCerberus
-- These challenges will be available to users before they log in

-- First, let's make sure we have the necessary tables
-- (This is just a check, the tables should already exist from our previous schema)

-- Insert detailed challenge categories if they don't exist
INSERT INTO challenge_categories (name, description, icon, display_order) VALUES
  ('Web Security', 'Challenges focused on web application vulnerabilities and exploits', 'globe', 1),
  ('Cryptography', 'Challenges involving encryption, decryption, and cryptographic attacks', 'key', 2),
  ('Network Security', 'Challenges related to network protocols, traffic analysis, and network attacks', 'network-wired', 3),
  ('OSINT', 'Open Source Intelligence gathering and analysis challenges', 'eye', 4),
  ('Forensics', 'Digital forensics challenges involving file analysis and recovery', 'search', 5),
  ('Reverse Engineering', 'Challenges focused on analyzing and understanding compiled code', 'microchip', 6)
ON CONFLICT (name) DO NOTHING;

-- Insert challenge difficulty levels if they don't exist
INSERT INTO challenge_difficulty_levels (name, description, display_order) VALUES
  ('Beginner', 'Entry-level challenges suitable for newcomers to cybersecurity', 1),
  ('Easy', 'Challenges requiring basic cybersecurity knowledge', 2),
  ('Medium', 'Challenges requiring intermediate cybersecurity skills', 3),
  ('Hard', 'Challenging problems for experienced cybersecurity practitioners', 4),
  ('Expert', 'Very difficult challenges for cybersecurity experts', 5)
ON CONFLICT (name) DO NOTHING;

-- Insert challenge types if they don't exist
INSERT INTO challenge_types (name, description) VALUES
  ('CTF', 'Capture The Flag style challenges with a flag to find'),
  ('Analysis', 'Challenges focused on analyzing security issues'),
  ('Exploitation', 'Challenges requiring exploitation of vulnerabilities'),
  ('Puzzle', 'Logic and problem-solving challenges with a security focus'),
  ('Reconnaissance', 'Challenges focused on information gathering techniques')
ON CONFLICT (name) DO NOTHING;

-- Now, let's create 6 detailed pre-login challenges

-- Challenge 1: Basic SQL Injection
INSERT INTO challenges (
  title, 
  slug, 
  description, 
  category_id, 
  difficulty_id, 
  type_id, 
  points, 
  coin_reward, 
  estimated_time, 
  is_premium, 
  is_business,
  is_active
) VALUES (
  'Basic SQL Injection', 
  'basic-sql-injection', 
  'Learn the fundamentals of SQL injection by exploiting a vulnerable login form. This challenge introduces you to one of the most common web application vulnerabilities.',
  (SELECT id FROM challenge_categories WHERE name = 'Web Security'),
  (SELECT id FROM challenge_difficulty_levels WHERE name = 'Beginner'),
  (SELECT id FROM challenge_types WHERE name = 'Exploitation'),
  100, 
  10, 
  20,
  FALSE, 
  FALSE,
  TRUE
)
ON CONFLICT (slug) DO UPDATE SET
  description = EXCLUDED.description,
  points = EXCLUDED.points,
  coin_reward = EXCLUDED.coin_reward,
  estimated_time = EXCLUDED.estimated_time,
  is_premium = EXCLUDED.is_premium,
  is_business = EXCLUDED.is_business,
  is_active = EXCLUDED.is_active;

-- Challenge 1 Content
INSERT INTO challenge_content (
  challenge_id,
  content
) VALUES (
  (SELECT id FROM challenges WHERE slug = 'basic-sql-injection'),
  '{
    "description": "In this challenge, you will learn about SQL injection, one of the most common web application vulnerabilities. You are presented with a login form for a fictional bank. Your task is to bypass the authentication without knowing a valid username or password.",
    "scenario": "BankSecure is a financial institution that prides itself on security. However, their web developers have made a critical mistake in their login form. Your goal is to exploit this vulnerability to gain unauthorized access.",
    "objectives": [
      "Understand how SQL injection works",
      "Identify vulnerable input fields",
      "Craft a SQL injection payload to bypass authentication",
      "Successfully log in without valid credentials"
    ],
    "resources": [
      {
        "name": "SQL Injection Cheat Sheet",
        "url": "https://www.example.com/sql-injection-cheatsheet"
      },
      {
        "name": "Introduction to SQL",
        "url": "https://www.example.com/sql-intro"
      }
    ],
    "challenge_url": "/challenges/simulator/basic-sql-injection",
    "flag_format": "flag{...}",
    "hints_available": true
  }'
)
ON CONFLICT (challenge_id) DO UPDATE SET
  content = EXCLUDED.content;

-- Challenge 1 Hints
INSERT INTO challenge_hints (
  challenge_id,
  hint,
  coin_cost,
  display_order
) VALUES
  (
    (SELECT id FROM challenges WHERE slug = 'basic-sql-injection'),
    'Try entering special characters in the username field to see how the application responds.',
    5,
    1
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'basic-sql-injection'),
    'The login query might look like: SELECT * FROM users WHERE username=\'[input]\' AND password=\'[input]\'',
    10,
    2
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'basic-sql-injection'),
    'Using a single quote followed by OR 1=1 might help you bypass the authentication.',
    15,
    3
  )
ON CONFLICT DO NOTHING;

-- Challenge 2: Password Cracking Basics
INSERT INTO challenges (
  title, 
  slug, 
  description, 
  category_id, 
  difficulty_id, 
  type_id, 
  points, 
  coin_reward, 
  estimated_time, 
  is_premium, 
  is_business,
  is_active
) VALUES (
  'Password Cracking Basics', 
  'password-cracking-basics', 
  'Learn the fundamentals of password cracking by breaking a series of increasingly complex password hashes. This challenge introduces you to common password cracking tools and techniques.',
  (SELECT id FROM challenge_categories WHERE name = 'Cryptography'),
  (SELECT id FROM challenge_difficulty_levels WHERE name = 'Beginner'),
  (SELECT id FROM challenge_types WHERE name = 'Analysis'),
  100, 
  10, 
  25,
  FALSE, 
  FALSE,
  TRUE
)
ON CONFLICT (slug) DO UPDATE SET
  description = EXCLUDED.description,
  points = EXCLUDED.points,
  coin_reward = EXCLUDED.coin_reward,
  estimated_time = EXCLUDED.estimated_time,
  is_premium = EXCLUDED.is_premium,
  is_business = EXCLUDED.is_business,
  is_active = EXCLUDED.is_active;

-- Challenge 2 Content
INSERT INTO challenge_content (
  challenge_id,
  content
) VALUES (
  (SELECT id FROM challenges WHERE slug = 'password-cracking-basics'),
  '{
    "description": "In this challenge, you will learn about password cracking techniques. You are given a series of password hashes of increasing complexity. Your task is to crack these hashes to reveal the original passwords.",
    "scenario": "You have obtained a file containing password hashes from a security breach. To understand the vulnerability, you need to determine the original passwords that these hashes represent.",
    "objectives": [
      "Understand different types of password hashes",
      "Learn to identify hash types",
      "Use online resources and tools to crack simple hashes",
      "Understand password complexity and its impact on security"
    ],
    "resources": [
      {
        "name": "Hash Identifier Guide",
        "url": "https://www.example.com/hash-identifier"
      },
      {
        "name": "Common Password Lists",
        "url": "https://www.example.com/password-lists"
      }
    ],
    "challenge_data": {
      "hash1": "5f4dcc3b5aa765d61d8327deb882cf99",
      "hash2": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8",
      "hash3": "$2y$10$F9hXnLl7IVhMJ.Zyj0jGAO38BXCxV/N4NLnLVVEx7hHQmWkr0qcp."
    },
    "flag_format": "flag{password1_password2_password3}",
    "hints_available": true
  }'
)
ON CONFLICT (challenge_id) DO UPDATE SET
  content = EXCLUDED.content;

-- Challenge 2 Hints
INSERT INTO challenge_hints (
  challenge_id,
  hint,
  coin_cost,
  display_order
) VALUES
  (
    (SELECT id FROM challenges WHERE slug = 'password-cracking-basics'),
    'The first hash is an MD5 hash of a very common password.',
    5,
    1
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'password-cracking-basics'),
    'The second hash is SHA-1. Try using an online hash cracker for these simple hashes.',
    10,
    2
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'password-cracking-basics'),
    'The third hash is bcrypt, which is much more secure. The password is a common word followed by a two-digit number.',
    15,
    3
  )
ON CONFLICT DO NOTHING;

-- Challenge 3: Network Traffic Analysis
INSERT INTO challenges (
  title, 
  slug, 
  description, 
  category_id, 
  difficulty_id, 
  type_id, 
  points, 
  coin_reward, 
  estimated_time, 
  is_premium, 
  is_business,
  is_active
) VALUES (
  'Network Traffic Analysis', 
  'network-traffic-analysis', 
  'Analyze captured network traffic to identify suspicious activities and extract hidden information. This challenge introduces you to packet analysis and network forensics.',
  (SELECT id FROM challenge_categories WHERE name = 'Network Security'),
  (SELECT id FROM challenge_difficulty_levels WHERE name = 'Easy'),
  (SELECT id FROM challenge_types WHERE name = 'Analysis'),
  150, 
  15, 
  30,
  FALSE, 
  FALSE,
  TRUE
)
ON CONFLICT (slug) DO UPDATE SET
  description = EXCLUDED.description,
  points = EXCLUDED.points,
  coin_reward = EXCLUDED.coin_reward,
  estimated_time = EXCLUDED.estimated_time,
  is_premium = EXCLUDED.is_premium,
  is_business = EXCLUDED.is_business,
  is_active = EXCLUDED.is_active;

-- Challenge 3 Content
INSERT INTO challenge_content (
  challenge_id,
  content
) VALUES (
  (SELECT id FROM challenges WHERE slug = 'network-traffic-analysis'),
  '{
    "description": "In this challenge, you will analyze a packet capture (PCAP) file containing network traffic. Your task is to identify suspicious activities and extract hidden information from the traffic.",
    "scenario": "The security team at a company has captured network traffic during a suspected security incident. They need your help to analyze the traffic and determine what happened.",
    "objectives": [
      "Learn to use basic packet analysis tools",
      "Identify different types of network protocols",
      "Detect suspicious patterns in network traffic",
      "Extract hidden data from packet payloads"
    ],
    "resources": [
      {
        "name": "Introduction to Wireshark",
        "url": "https://www.example.com/wireshark-intro"
      },
      {
        "name": "Common Network Protocols",
        "url": "https://www.example.com/network-protocols"
      }
    ],
    "challenge_files": [
      {
        "name": "capture.pcap",
        "url": "/challenges/files/capture.pcap"
      }
    ],
    "flag_format": "flag{...}",
    "hints_available": true
  }'
)
ON CONFLICT (challenge_id) DO UPDATE SET
  content = EXCLUDED.content;

-- Challenge 3 Hints
INSERT INTO challenge_hints (
  challenge_id,
  hint,
  coin_cost,
  display_order
) VALUES
  (
    (SELECT id FROM challenges WHERE slug = 'network-traffic-analysis'),
    'Focus on HTTP traffic in the capture file. There might be interesting information in the requests and responses.',
    5,
    1
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'network-traffic-analysis'),
    'Look for unusual or repeated patterns in the traffic. Some data might be hidden using encoding techniques.',
    10,
    2
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'network-traffic-analysis'),
    'Check the DNS queries in the capture. Sometimes data can be exfiltrated through DNS requests.',
    15,
    3
  )
ON CONFLICT DO NOTHING;

-- Challenge 4: OSINT Investigation
INSERT INTO challenges (
  title, 
  slug, 
  description, 
  category_id, 
  difficulty_id, 
  type_id, 
  points, 
  coin_reward, 
  estimated_time, 
  is_premium, 
  is_business,
  is_active
) VALUES (
  'OSINT Investigation', 
  'osint-investigation', 
  'Use Open Source Intelligence techniques to gather information about a fictional target. This challenge introduces you to the power of publicly available information.',
  (SELECT id FROM challenge_categories WHERE name = 'OSINT'),
  (SELECT id FROM challenge_difficulty_levels WHERE name = 'Easy'),
  (SELECT id FROM challenge_types WHERE name = 'Reconnaissance'),
  150, 
  15, 
  35,
  FALSE, 
  FALSE,
  TRUE
)
ON CONFLICT (slug) DO UPDATE SET
  description = EXCLUDED.description,
  points = EXCLUDED.points,
  coin_reward = EXCLUDED.coin_reward,
  estimated_time = EXCLUDED.estimated_time,
  is_premium = EXCLUDED.is_premium,
  is_business = EXCLUDED.is_business,
  is_active = EXCLUDED.is_active;

-- Challenge 4 Content
INSERT INTO challenge_content (
  challenge_id,
  content
) VALUES (
  (SELECT id FROM challenges WHERE slug = 'osint-investigation'),
  '{
    "description": "In this challenge, you will use Open Source Intelligence (OSINT) techniques to gather information about a fictional person. Your task is to find specific details about the target using only publicly available information.",
    "scenario": "You are a security researcher conducting a social engineering risk assessment for a company. You need to demonstrate how much information can be gathered about an individual using only public sources.",
    "objectives": [
      "Learn basic OSINT techniques",
      "Discover information across multiple platforms",
      "Correlate data from different sources",
      "Understand the privacy implications of public information"
    ],
    "resources": [
      {
        "name": "OSINT Framework",
        "url": "https://www.example.com/osint-framework"
      },
      {
        "name": "Social Media Search Techniques",
        "url": "https://www.example.com/social-media-search"
      }
    ],
    "target_info": {
      "name": "Alex Morgan",
      "twitter": "@alexm_security",
      "profession": "Cybersecurity Professional"
    },
    "flag_format": "flag{firstname_birthyear_city_hobby}",
    "hints_available": true
  }'
)
ON CONFLICT (challenge_id) DO UPDATE SET
  content = EXCLUDED.content;

-- Challenge 4 Hints
INSERT INTO challenge_hints (
  challenge_id,
  hint,
  coin_cost,
  display_order
) VALUES
  (
    (SELECT id FROM challenges WHERE slug = 'osint-investigation'),
    'Start by searching for the target on major social media platforms. Look for connections between accounts.',
    5,
    1
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'osint-investigation'),
    'Check for any technical forums or professional networks where a cybersecurity professional might be active.',
    10,
    2
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'osint-investigation'),
    'Images shared by the target might contain metadata or location information. Also look for recurring locations in posts.',
    15,
    3
  )
ON CONFLICT DO NOTHING;

-- Challenge 5: Basic Steganography
INSERT INTO challenges (
  title, 
  slug, 
  description, 
  category_id, 
  difficulty_id, 
  type_id, 
  points, 
  coin_reward, 
  estimated_time, 
  is_premium, 
  is_business,
  is_active
) VALUES (
  'Basic Steganography', 
  'basic-steganography', 
  'Discover hidden messages concealed within digital images. This challenge introduces you to steganography techniques and tools.',
  (SELECT id FROM challenge_categories WHERE name = 'Forensics'),
  (SELECT id FROM challenge_difficulty_levels WHERE name = 'Easy'),
  (SELECT id FROM challenge_types WHERE name = 'Analysis'),
  150, 
  15, 
  25,
  FALSE, 
  FALSE,
  TRUE
)
ON CONFLICT (slug) DO UPDATE SET
  description = EXCLUDED.description,
  points = EXCLUDED.points,
  coin_reward = EXCLUDED.coin_reward,
  estimated_time = EXCLUDED.estimated_time,
  is_premium = EXCLUDED.is_premium,
  is_business = EXCLUDED.is_business,
  is_active = EXCLUDED.is_active;

-- Challenge 5 Content
INSERT INTO challenge_content (
  challenge_id,
  content
) VALUES (
  (SELECT id FROM challenges WHERE slug = 'basic-steganography'),
  '{
    "description": "In this challenge, you will learn about steganography - the practice of concealing messages or information within other non-secret data or a physical object. Your task is to find hidden messages within digital images.",
    "scenario": "You have intercepted several images that are suspected to contain hidden messages. These messages might contain crucial information about an upcoming security threat.",
    "objectives": [
      "Understand the concept of steganography",
      "Learn to use basic steganography detection tools",
      "Extract hidden data from images",
      "Decode the concealed messages"
    ],
    "resources": [
      {
        "name": "Introduction to Steganography",
        "url": "https://www.example.com/steganography-intro"
      },
      {
        "name": "Common Steganography Tools",
        "url": "https://www.example.com/stego-tools"
      }
    ],
    "challenge_files": [
      {
        "name": "innocent_image.png",
        "url": "/challenges/files/innocent_image.png"
      },
      {
        "name": "vacation_photo.jpg",
        "url": "/challenges/files/vacation_photo.jpg"
      }
    ],
    "flag_format": "flag{...}",
    "hints_available": true
  }'
)
ON CONFLICT (challenge_id) DO UPDATE SET
  content = EXCLUDED.content;

-- Challenge 5 Hints
INSERT INTO challenge_hints (
  challenge_id,
  hint,
  coin_cost,
  display_order
) VALUES
  (
    (SELECT id FROM challenges WHERE slug = 'basic-steganography'),
    'Try examining the images with a hex editor. Look for text strings that appear after the image data.',
    5,
    1
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'basic-steganography'),
    'One of the images contains data hidden using the least significant bit (LSB) technique.',
    10,
    2
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'basic-steganography'),
    'The password to extract the hidden data is related to the filename of the image.',
    15,
    3
  )
ON CONFLICT DO NOTHING;

-- Challenge 6: Simple Binary Analysis
INSERT INTO challenges (
  title, 
  slug, 
  description, 
  category_id, 
  difficulty_id, 
  type_id, 
  points, 
  coin_reward, 
  estimated_time, 
  is_premium, 
  is_business,
  is_active
) VALUES (
  'Simple Binary Analysis', 
  'simple-binary-analysis', 
  'Analyze a simple executable file to understand its behavior and find hidden functionality. This challenge introduces you to basic reverse engineering concepts.',
  (SELECT id FROM challenge_categories WHERE name = 'Reverse Engineering'),
  (SELECT id FROM challenge_difficulty_levels WHERE name = 'Medium'),
  (SELECT id FROM challenge_types WHERE name = 'Analysis'),
  200, 
  20, 
  40,
  FALSE, 
  FALSE,
  TRUE
)
ON CONFLICT (slug) DO UPDATE SET
  description = EXCLUDED.description,
  points = EXCLUDED.points,
  coin_reward = EXCLUDED.coin_reward,
  estimated_time = EXCLUDED.estimated_time,
  is_premium = EXCLUDED.is_premium,
  is_business = EXCLUDED.is_business,
  is_active = EXCLUDED.is_active;

-- Challenge 6 Content
INSERT INTO challenge_content (
  challenge_id,
  content
) VALUES (
  (SELECT id FROM challenges WHERE slug = 'simple-binary-analysis'),
  '{
    "description": "In this challenge, you will analyze a simple executable file to understand its behavior and find hidden functionality. This will introduce you to basic reverse engineering concepts and tools.",
    "scenario": "You have obtained a suspicious executable file that appears to be a simple calculator application. However, there are rumors that it contains hidden functionality that might be malicious.",
    "objectives": [
      "Learn to use basic reverse engineering tools",
      "Analyze the behavior of an executable without running it",
      "Identify hidden functions and features",
      "Extract hardcoded secrets from binary files"
    ],
    "resources": [
      {
        "name": "Introduction to Binary Analysis",
        "url": "https://www.example.com/binary-analysis-intro"
      },
      {
        "name": "Common Reverse Engineering Tools",
        "url": "https://www.example.com/re-tools"
      }
    ],
    "challenge_files": [
      {
        "name": "calculator.exe",
        "url": "/challenges/files/calculator.exe"
      }
    ],
    "flag_format": "flag{...}",
    "hints_available": true
  }'
)
ON CONFLICT (challenge_id) DO UPDATE SET
  content = EXCLUDED.content;

-- Challenge 6 Hints
INSERT INTO challenge_hints (
  challenge_id,
  hint,
  coin_cost,
  display_order
) VALUES
  (
    (SELECT id FROM challenges WHERE slug = 'simple-binary-analysis'),
    'Start by examining the strings in the binary. Look for anything unusual or out of place for a calculator application.',
    5,
    1
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'simple-binary-analysis'),
    'The application has a hidden command-line parameter that activates its secret functionality.',
    10,
    2
  ),
  (
    (SELECT id FROM challenges WHERE slug = 'simple-binary-analysis'),
    'Try using a decompiler to examine the main function. Look for conditional branches that might lead to hidden features.',
    15,
    3
  )
ON CONFLICT DO NOTHING;
