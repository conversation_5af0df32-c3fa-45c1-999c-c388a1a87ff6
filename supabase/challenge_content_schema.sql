-- XCerberus Challenge Content Schema
-- Designed for extreme scalability (100M+ users)

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements"; -- For query performance monitoring

-- Challenge Categories Table
CREATE TABLE IF NOT EXISTS challenge_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Difficulty Levels Table
CREATE TABLE IF NOT EXISTS challenge_difficulty_levels (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  points_multiplier FLOAT DEFAULT 1.0,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Types Table
CREATE TABLE IF NOT EXISTS challenge_types (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  container_template TEXT,
  resource_requirements JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Challenges Table (Partitioned for scalability)
CREATE TABLE IF NOT EXISTS challenges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE,
  description TEXT NOT NULL,
  category_id UUID REFERENCES challenge_categories(id),
  difficulty_id UUID REFERENCES challenge_difficulty_levels(id),
  type_id UUID REFERENCES challenge_types(id),
  points INTEGER NOT NULL,
  estimated_time INTEGER, -- In minutes
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  requires_coins BOOLEAN DEFAULT FALSE,
  coin_cost INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT NULL,
  author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_challenges_category_difficulty ON challenges(category_id, difficulty_id);
CREATE INDEX idx_challenges_premium_business ON challenges(is_premium, is_business);
CREATE INDEX idx_challenges_active ON challenges(is_active);
CREATE INDEX idx_challenges_search ON challenges USING gin(title gin_trgm_ops, description gin_trgm_ops);

-- Challenge Content Table (Separate from challenges for better performance)
CREATE TABLE IF NOT EXISTS challenge_content (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  content JSONB NOT NULL, -- Structured content for the challenge
  solution TEXT,
  solution_hash TEXT, -- Hashed solution for secure validation
  hints JSONB, -- Array of hints with costs
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Files Table
CREATE TABLE IF NOT EXISTS challenge_files (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  is_downloadable BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Docker Configuration Table
CREATE TABLE IF NOT EXISTS challenge_docker_config (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  image_name TEXT NOT NULL,
  container_config JSONB NOT NULL,
  port_mappings JSONB,
  environment_variables JSONB,
  resource_limits JSONB,
  timeout_seconds INTEGER DEFAULT 3600, -- 1 hour default
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Attempts Table (Partitioned by user_id for scalability)
CREATE TABLE IF NOT EXISTS challenge_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  submission TEXT,
  is_correct BOOLEAN,
  points_earned INTEGER DEFAULT 0,
  attempt_number INTEGER,
  time_spent INTEGER, -- In seconds
  hints_used JSONB,
  container_id TEXT,
  ip_address TEXT,
  user_agent TEXT,
  attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_challenge_attempts_user ON challenge_attempts(user_id);
CREATE INDEX idx_challenge_attempts_challenge ON challenge_attempts(challenge_id);
CREATE INDEX idx_challenge_attempts_date ON challenge_attempts(attempted_at);

-- Challenge Completions Table (Partitioned by user_id for scalability)
CREATE TABLE IF NOT EXISTS challenge_completions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  score INTEGER NOT NULL,
  time_spent INTEGER, -- In seconds
  attempt_count INTEGER DEFAULT 1,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(challenge_id, user_id)
);

-- Create index for faster queries
CREATE INDEX idx_challenge_completions_user ON challenge_completions(user_id);
CREATE INDEX idx_challenge_completions_challenge ON challenge_completions(challenge_id);
CREATE INDEX idx_challenge_completions_date ON challenge_completions(completed_at);

-- Challenge Ratings Table
CREATE TABLE IF NOT EXISTS challenge_ratings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  feedback TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(challenge_id, user_id)
);

-- Challenge Comments Table
CREATE TABLE IF NOT EXISTS challenge_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  parent_id UUID REFERENCES challenge_comments(id) ON DELETE CASCADE,
  comment TEXT NOT NULL,
  is_solution BOOLEAN DEFAULT FALSE,
  is_approved BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Hints Table
CREATE TABLE IF NOT EXISTS challenge_hints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  hint_text TEXT NOT NULL,
  coin_cost INTEGER DEFAULT 0,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Tags Table
CREATE TABLE IF NOT EXISTS challenge_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge-Tag Relationship Table
CREATE TABLE IF NOT EXISTS challenge_tag_relations (
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES challenge_tags(id) ON DELETE CASCADE,
  PRIMARY KEY (challenge_id, tag_id)
);

-- Challenge Prerequisites Table
CREATE TABLE IF NOT EXISTS challenge_prerequisites (
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  prerequisite_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  PRIMARY KEY (challenge_id, prerequisite_id)
);

-- Team Challenge Attempts Table
CREATE TABLE IF NOT EXISTS team_challenge_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  initiator_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  submission TEXT,
  is_correct BOOLEAN,
  points_earned INTEGER DEFAULT 0,
  attempt_number INTEGER,
  time_spent INTEGER, -- In seconds
  hints_used JSONB,
  container_id TEXT,
  attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Challenge Completions Table
CREATE TABLE IF NOT EXISTS team_challenge_completions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  score INTEGER NOT NULL,
  time_spent INTEGER, -- In seconds
  attempt_count INTEGER DEFAULT 1,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(challenge_id, team_id)
);

-- Challenge Container Instances Table
CREATE TABLE IF NOT EXISTS challenge_container_instances (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  container_id TEXT NOT NULL,
  instance_ip TEXT,
  instance_port INTEGER,
  access_token TEXT,
  status TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  terminated_at TIMESTAMP WITH TIME ZONE
);

-- Functions

-- Function to get challenge completion rate
CREATE OR REPLACE FUNCTION get_challenge_completion_rate(challenge_id UUID)
RETURNS FLOAT AS $$
DECLARE
  attempts INTEGER;
  completions INTEGER;
  rate FLOAT;
BEGIN
  SELECT COUNT(DISTINCT user_id) INTO attempts
  FROM challenge_attempts
  WHERE challenge_id = get_challenge_completion_rate.challenge_id;
  
  SELECT COUNT(*) INTO completions
  FROM challenge_completions
  WHERE challenge_id = get_challenge_completion_rate.challenge_id;
  
  IF attempts = 0 THEN
    rate := 0;
  ELSE
    rate := (completions::FLOAT / attempts::FLOAT) * 100;
  END IF;
  
  RETURN rate;
END;
$$ LANGUAGE plpgsql;

-- Function to get challenge average rating
CREATE OR REPLACE FUNCTION get_challenge_average_rating(challenge_id UUID)
RETURNS FLOAT AS $$
DECLARE
  avg_rating FLOAT;
BEGIN
  SELECT AVG(rating) INTO avg_rating
  FROM challenge_ratings
  WHERE challenge_id = get_challenge_average_rating.challenge_id;
  
  RETURN COALESCE(avg_rating, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to get user challenge progress
CREATE OR REPLACE FUNCTION get_user_challenge_progress(user_id UUID)
RETURNS TABLE (
  total_challenges INTEGER,
  completed_challenges INTEGER,
  total_points INTEGER,
  completion_percentage FLOAT
) AS $$
DECLARE
  total INTEGER;
  completed INTEGER;
  points INTEGER;
  percentage FLOAT;
BEGIN
  -- Get total active challenges
  SELECT COUNT(*) INTO total
  FROM challenges
  WHERE is_active = TRUE;
  
  -- Get completed challenges
  SELECT COUNT(*), COALESCE(SUM(score), 0) INTO completed, points
  FROM challenge_completions
  WHERE user_id = get_user_challenge_progress.user_id;
  
  -- Calculate percentage
  IF total = 0 THEN
    percentage := 0;
  ELSE
    percentage := (completed::FLOAT / total::FLOAT) * 100;
  END IF;
  
  RETURN QUERY SELECT total, completed, points, percentage;
END;
$$ LANGUAGE plpgsql;

-- Function to get recommended challenges for a user
CREATE OR REPLACE FUNCTION get_recommended_challenges(user_id UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  category_name TEXT,
  difficulty_name TEXT,
  points INTEGER,
  relevance_score FLOAT
) AS $$
BEGIN
  RETURN QUERY
  WITH user_completed_challenges AS (
    SELECT challenge_id
    FROM challenge_completions
    WHERE user_id = get_recommended_challenges.user_id
  ),
  user_categories AS (
    SELECT c.category_id, COUNT(*) as category_count
    FROM challenge_completions cc
    JOIN challenges c ON cc.challenge_id = c.id
    WHERE cc.user_id = get_recommended_challenges.user_id
    GROUP BY c.category_id
  ),
  user_difficulties AS (
    SELECT c.difficulty_id, COUNT(*) as difficulty_count
    FROM challenge_completions cc
    JOIN challenges c ON cc.challenge_id = c.id
    WHERE cc.user_id = get_recommended_challenges.user_id
    GROUP BY c.difficulty_id
  )
  SELECT 
    c.id,
    c.title,
    c.description,
    cat.name as category_name,
    diff.name as difficulty_name,
    c.points,
    -- Calculate relevance score based on user's history
    (
      CASE
        WHEN uc.category_count IS NOT NULL THEN 0.5
        ELSE 0.1
      END +
      CASE
        WHEN ud.difficulty_count IS NOT NULL THEN 0.3
        ELSE 0.1
      END +
      CASE
        WHEN c.is_premium = TRUE THEN 0.2
        ELSE 0.1
      END
    ) as relevance_score
  FROM challenges c
  JOIN challenge_categories cat ON c.category_id = cat.id
  JOIN challenge_difficulty_levels diff ON c.difficulty_id = diff.id
  LEFT JOIN user_categories uc ON c.category_id = uc.category_id
  LEFT JOIN user_difficulties ud ON c.difficulty_id = ud.difficulty_id
  WHERE 
    c.is_active = TRUE
    AND c.id NOT IN (SELECT challenge_id FROM user_completed_challenges)
  ORDER BY relevance_score DESC, c.created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Insert sample data for testing

-- Insert challenge categories
INSERT INTO challenge_categories (name, description, icon, display_order) VALUES
  ('Web Security', 'Challenges focused on web application security', 'fa-globe', 1),
  ('Network Security', 'Challenges focused on network security', 'fa-network-wired', 2),
  ('Cryptography', 'Challenges focused on cryptography', 'fa-key', 3),
  ('Reverse Engineering', 'Challenges focused on reverse engineering', 'fa-microchip', 4),
  ('Forensics', 'Challenges focused on digital forensics', 'fa-search', 5),
  ('Binary Exploitation', 'Challenges focused on binary exploitation', 'fa-bug', 6),
  ('OSINT', 'Challenges focused on open-source intelligence', 'fa-eye', 7),
  ('Mobile Security', 'Challenges focused on mobile application security', 'fa-mobile-alt', 8)
ON CONFLICT (name) DO NOTHING;

-- Insert challenge difficulty levels
INSERT INTO challenge_difficulty_levels (name, description, points_multiplier, display_order) VALUES
  ('Beginner', 'Entry-level challenges for beginners', 1.0, 1),
  ('Intermediate', 'Challenges for users with some experience', 1.5, 2),
  ('Advanced', 'Challenging problems for experienced users', 2.0, 3),
  ('Expert', 'Very difficult challenges for experts', 3.0, 4)
ON CONFLICT (name) DO NOTHING;

-- Insert challenge types
INSERT INTO challenge_types (name, description, container_template, resource_requirements) VALUES
  ('Static', 'Challenges that do not require a container', NULL, '{"cpu": 0, "memory": 0}'),
  ('Web', 'Web application challenges', 'web-security', '{"cpu": 0.2, "memory": 256}'),
  ('Network', 'Network security challenges', 'network-security', '{"cpu": 0.3, "memory": 512}'),
  ('Cryptography', 'Cryptography challenges', 'cryptography', '{"cpu": 0.2, "memory": 256}'),
  ('Reverse Engineering', 'Reverse engineering challenges', 'reverse-engineering', '{"cpu": 0.5, "memory": 1024}'),
  ('Forensics', 'Digital forensics challenges', 'forensics', '{"cpu": 0.3, "memory": 512}'),
  ('Binary Exploitation', 'Binary exploitation challenges', 'binary-exploitation', '{"cpu": 0.5, "memory": 1024}'),
  ('Linux Box', 'Full Linux environment challenges', 'linux-box', '{"cpu": 1.0, "memory": 2048}')
ON CONFLICT (name) DO NOTHING;

-- Row Level Security Policies

-- Challenges: Access based on subscription tier
ALTER TABLE challenges ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view non-premium challenges" 
ON challenges FOR SELECT 
USING (
  NOT is_premium OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business'))
);

CREATE POLICY "Premium users can view premium challenges" 
ON challenges FOR SELECT 
USING (
  NOT is_business OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business')
);

-- Challenge Content: Access based on subscription tier and coin purchase
ALTER TABLE challenge_content ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view challenge content they have access to" 
ON challenge_content FOR SELECT 
USING (
  challenge_id IN (
    SELECT id FROM challenges WHERE 
      (NOT is_premium OR auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business')))
      AND
      (NOT is_business OR auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business'))
      AND
      (NOT requires_coins OR auth.uid() IN (
        SELECT user_id FROM user_inventory WHERE item_id IN (
          SELECT id FROM store_items WHERE content->>'challenge_id' = challenge_id::text
        )
      ))
  )
);

-- Challenge Attempts: Users can only view their own attempts
ALTER TABLE challenge_attempts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own attempts" 
ON challenge_attempts FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own attempts" 
ON challenge_attempts FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Challenge Completions: Users can only view their own completions
ALTER TABLE challenge_completions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own completions" 
ON challenge_completions FOR SELECT 
USING (auth.uid() = user_id);

-- Team Challenge Attempts: Team members can view their team's attempts
ALTER TABLE team_challenge_attempts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view their team's attempts" 
ON team_challenge_attempts FOR SELECT 
USING (
  team_id IN (
    SELECT team_id FROM team_members WHERE user_id = auth.uid()
  )
);

-- Team Challenge Completions: Team members can view their team's completions
ALTER TABLE team_challenge_completions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view their team's completions" 
ON team_challenge_completions FOR SELECT 
USING (
  team_id IN (
    SELECT team_id FROM team_members WHERE user_id = auth.uid()
  )
);

-- Challenge Container Instances: Users can only view their own instances
ALTER TABLE challenge_container_instances ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own container instances" 
ON challenge_container_instances FOR SELECT 
USING (
  auth.uid() = user_id OR 
  team_id IN (
    SELECT team_id FROM team_members WHERE user_id = auth.uid()
  )
);

-- Indexes for performance optimization

-- Create indexes for frequently queried columns
CREATE INDEX IF NOT EXISTS idx_challenges_title_trgm ON challenges USING gin (title gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_challenges_description_trgm ON challenges USING gin (description gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_challenge_attempts_timestamp ON challenge_attempts (attempted_at);
CREATE INDEX IF NOT EXISTS idx_challenge_completions_timestamp ON challenge_completions (completed_at);
CREATE INDEX IF NOT EXISTS idx_challenge_ratings_challenge_id ON challenge_ratings (challenge_id);
CREATE INDEX IF NOT EXISTS idx_challenge_comments_challenge_id ON challenge_comments (challenge_id);
CREATE INDEX IF NOT EXISTS idx_challenge_comments_parent_id ON challenge_comments (parent_id);
CREATE INDEX IF NOT EXISTS idx_challenge_container_instances_status ON challenge_container_instances (status);
CREATE INDEX IF NOT EXISTS idx_challenge_container_instances_expires_at ON challenge_container_instances (expires_at);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_challenge_attempts_user_challenge ON challenge_attempts (user_id, challenge_id);
CREATE INDEX IF NOT EXISTS idx_challenge_completions_user_challenge ON challenge_completions (user_id, challenge_id);
CREATE INDEX IF NOT EXISTS idx_team_challenge_attempts_team_challenge ON team_challenge_attempts (team_id, challenge_id);
CREATE INDEX IF NOT EXISTS idx_team_challenge_completions_team_challenge ON team_challenge_completions (team_id, challenge_id);
