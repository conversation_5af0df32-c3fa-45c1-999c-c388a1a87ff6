-- Drop existing functions first to avoid conflicts
DO $$ 
BEGIN
  DROP FUNCTION IF EXISTS search_ai_responses(text);
  DROP FUNCTION IF EXISTS search_ai_responses(text, text, text);
  DROP FUNCTION IF EXISTS search_ai_responses_v2(text, text, text);
  DROP FUNCTION IF EXISTS search_ai_responses_v3(text, text, text);
  DROP FUNCTION IF EXISTS search_ai_responses_v4(text, text, text);
EXCEPTION
  WHEN undefined_object THEN null;
END $$;

-- <PERSON><PERSON> improved search function with unique name
CREATE OR REPLACE FUNCTION search_ai_responses_v5(
  search_term TEXT,
  search_domain TEXT DEFAULT NULL,
  search_language TEXT DEFAULT 'english'
)
RETURNS TABLE (
  id UUID,
  keyword TEXT,
  content TEXT,
  category TEXT,
  domain TEXT,
  language TEXT,
  similarity DOUBLE PRECISION
) AS $$
BEGIN
  -- First try exact match
  RETURN QUERY
  SELECT 
    ar.id,
    ar.keyword,
    ar.content,
    ar.category,
    ar.domain,
    ar.language,
    1.0::double precision AS similarity
  FROM 
    ai_responses ar
  WHERE 
    LOWER(ar.keyword) = LOWER(search_term)
    AND (search_domain IS NULL OR ar.domain = search_domain)
    AND ar.language = search_language
  LIMIT 1;

  -- If no exact match, try fuzzy search with improved matching
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      ar.id,
      ar.keyword,
      ar.content,
      ar.category,
      ar.domain,
      ar.language,
      similarity(ar.keyword, search_term)::double precision AS similarity
    FROM 
      ai_responses ar
    WHERE 
      (ar.keyword % search_term OR 
       ar.keyword ILIKE '%' || search_term || '%' OR
       search_term ILIKE '%' || ar.keyword || '%')
      AND (search_domain IS NULL OR ar.domain = search_domain)
      AND ar.language = search_language
    ORDER BY 
      similarity DESC,
      LENGTH(ar.keyword) ASC  -- Prefer shorter, more precise matches
    LIMIT 1;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Add SIEM-related responses with improved formatting
INSERT INTO ai_responses (keyword, content, category, domain, language)
VALUES 
('what is siem', E'SIEM (Security Information and Event Management)\n\n1. Core Components:\n   • Log Collection & Aggregation\n   • Real-time Analysis\n   • Threat Detection\n   • Alert Generation\n   • Incident Response\n\n2. Key Features:\n   • Centralized Logging\n   • Event Correlation\n   • Automated Alerting\n   • Compliance Reporting\n   • Threat Intelligence\n\n3. Use Cases:\n   • Security Monitoring\n   • Incident Detection\n   • Compliance Management\n   • Forensic Analysis\n   • Threat Hunting\n\n4. Benefits:\n   • Improved Detection\n   • Faster Response\n   • Better Compliance\n   • Enhanced Visibility', 'technical', 'TECHNICAL', 'english'),

('siem tools', E'Popular SIEM Solutions:\n\n1. Splunk Enterprise Security\n   • Real-time Analytics\n   • Machine Learning\n   • Threat Intelligence\n   • Custom Dashboards\n\n2. IBM QRadar\n   • AI-powered Analysis\n   • Automated Response\n   • Risk Management\n   • Compliance Tools\n\n3. Microsoft Sentinel\n   • Cloud-native SIEM\n   • Azure Integration\n   • SOAR Capabilities\n   • Cost-effective\n\n4. LogRhythm\n   • UEBA Features\n   • Case Management\n   • Automated Playbooks\n   • Compliance Reports', 'technical', 'TECHNICAL', 'english'),

('siem implementation', E'SIEM Implementation Guide:\n\n1. Planning Phase\n   • Define Requirements\n   • Select Solution\n   • Design Architecture\n   • Set Objectives\n\n2. Deployment Steps\n   • Install Platform\n   • Configure Sources\n   • Set Up Rules\n   • Test System\n\n3. Optimization\n   • Tune Rules\n   • Reduce Noise\n   • Add Sources\n   • Train Team\n\n4. Maintenance\n   • Monitor Health\n   • Update Rules\n   • Review Alerts\n   • Improve Process', 'technical', 'TECHNICAL', 'english')

ON CONFLICT (keyword) DO UPDATE 
SET 
  content = EXCLUDED.content,
  category = EXCLUDED.category,
  domain = EXCLUDED.domain,
  language = EXCLUDED.language;

-- Create better indexes for search performance
DROP INDEX IF EXISTS ai_responses_keyword_trgm_idx;
CREATE INDEX ai_responses_keyword_trgm_idx ON ai_responses USING gin (keyword gin_trgm_ops);

DROP INDEX IF EXISTS ai_responses_content_trgm_idx;
CREATE INDEX ai_responses_content_trgm_idx ON ai_responses USING gin (content gin_trgm_ops);