-- Create search_ai_responses_v2 function with improved functionality
CREATE OR REPLACE FUNCTION search_ai_responses_v2(
  search_term TEXT,
  search_domain TEXT DEFAULT NULL,
  search_language TEXT DEFAULT 'english'
)
RETURNS TABLE (
  id UUID,
  keyword TEXT,
  content TEXT,
  category TEXT,
  domain TEXT,
  language TEXT,
  similarity DOUBLE PRECISION
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ar.id,
    ar.keyword,
    ar.content,
    ar.category,
    ar.domain,
    ar.language,
    similarity(ar.keyword, search_term)::double precision AS similarity
  FROM 
    ai_responses ar
  WHERE 
    ar.keyword % search_term
    AND (search_domain IS NULL OR ar.domain = search_domain)
    AND ar.language = search_language
  ORDER BY 
    similarity DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Add domain field if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'ai_responses' 
    AND column_name = 'domain'
  ) THEN
    ALTER TABLE ai_responses ADD COLUMN domain text;
  END IF;
END $$;

-- Update existing responses with domains if domain is NULL
UPDATE ai_responses
SET domain = 
  CASE 
    WHEN category ILIKE '%governance%' OR category ILIKE '%compliance%' OR category ILIKE '%risk%' 
    THEN 'GOVERNANCE'
    WHEN category ILIKE '%incident%' OR category ILIKE '%response%' OR category ILIKE '%forensic%'
    THEN 'INCIDENT_RESPONSE'
    WHEN category ILIKE '%trend%' OR category ILIKE '%news%' OR category ILIKE '%update%'
    THEN 'LATEST_TRENDS'
    ELSE 'TECHNICAL'
  END
WHERE domain IS NULL;

-- Create indexes if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_indexes 
    WHERE indexname = 'ai_responses_domain_idx'
  ) THEN
    CREATE INDEX ai_responses_domain_idx ON ai_responses (domain);
  END IF;

  IF NOT EXISTS (
    SELECT 1 
    FROM pg_indexes 
    WHERE indexname = 'ai_responses_domain_lang_idx'
  ) THEN
    CREATE INDEX ai_responses_domain_lang_idx ON ai_responses (domain, language);
  END IF;
END $$;