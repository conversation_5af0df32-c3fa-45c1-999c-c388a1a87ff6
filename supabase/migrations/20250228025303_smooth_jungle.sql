-- Add more common responses for basic queries
INSERT INTO ai_responses (keyword, content, category, language)
VALUES 
('what is cybersecurity', 'Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These attacks are usually aimed at accessing, changing, or destroying sensitive information; extorting money from users; or interrupting normal business processes.', 'cybersecurity', 'english'),

('సైబర్ సెక్యూరిటీ అంటే ఏమిటి', 'సైబర్ సెక్యూరిటీ అనేది సిస్టమ్‌లు, నెట్‌వర్క్‌లు మరియు ప్రోగ్రామ్‌లను డిజిటల్ దాడుల నుండి రక్షించే విధానం. ఈ దాడులు సాధారణంగా సున్నితమైన సమాచారాన్ని ప్రాప్తి చేయడం, మార్చడం లేదా నాశనం చేయడం లక్ష్యంగా ఉంటాయి.', 'cybersecurity', 'telugu'),

('what is a buffer overflow attack', 'A buffer overflow attack occurs when a program or process attempts to write more data to a fixed-length block of memory (a buffer) than the buffer is allocated to hold. This can lead to code execution, crashes, or data exposure.', 'cybersecurity', 'english'),

('బఫర్ ఓవర్‌ఫ్లో దాడి అంటే ఏమిటి', 'బఫర్ ఓవర్‌ఫ్లో దాడి అనేది ఒక ప్రోగ్రామ్ లేదా ప్రాసెస్ మెమరీలో కేటాయించిన స్థలం కంటే ఎక్కువ డేటాను వ్రాయడానికి ప్రయత్నించినప్పుడు జరుగుతుంది. ఇది కోడ్ ఎక్సిక్యూషన్, క్రాష్‌లు లేదా డేటా లీకేజీకి దారితీయవచ్చు.', 'cybersecurity', 'telugu'),

('what is sql injection', 'SQL Injection is a code injection technique that exploits vulnerabilities in the database layer of an application. It occurs when user input is incorrectly filtered and directly included in SQL statements.', 'cybersecurity', 'english'),

('SQL ఇంజెక్షన్ అంటే ఏమిటి', 'SQL ఇంజెక్షన్ అనేది అప్లికేషన్ యొక్క డేటాబేస్ లేయర్‌లో ఉన్న లోపాలను ఆసరాగా చేసుకునే కోడ్ ఇంజెక్షన్ పద్ధతి. యూజర్ ఇన్‌పుట్‌ను సరిగ్గా ఫిల్టర్ చేయకపోవడం వల్ల ఇది జరుగుతుంది.', 'cybersecurity', 'telugu')

ON CONFLICT (keyword) DO NOTHING;

-- Create a function to handle fuzzy search
CREATE OR REPLACE FUNCTION search_ai_responses(search_term TEXT)
RETURNS TABLE (
  id UUID,
  keyword TEXT,
  content TEXT,
  category TEXT,
  language TEXT,
  similarity FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ar.id,
    ar.keyword,
    ar.content,
    ar.category,
    ar.language,
    similarity(ar.keyword, search_term) AS similarity
  FROM 
    ai_responses ar
  WHERE 
    ar.keyword % search_term
  ORDER BY 
    similarity DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;