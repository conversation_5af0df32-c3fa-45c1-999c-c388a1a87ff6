/*
  # Fix User Data Schema

  1. New Tables
    - `user_profiles` - Extended user profile information
    - `user_activity` - Track user activity and achievements
  
  2. Changes
    - Add missing columns to existing tables
    - Update RLS policies
    - Add triggers for user creation
*/

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username text UNIQUE NOT NULL,
  full_name text,
  avatar_url text,
  bio text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own profile"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Create user_activity table
CREATE TABLE IF NOT EXISTS user_activity (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type text NOT NULL,
  description text,
  points_earned integer DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE user_activity ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own activity"
  ON user_activity
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Create user_coins table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_coins (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  balance integer DEFAULT 0,
  last_transaction_at timestamptz DEFAULT now()
);

ALTER TABLE user_coins ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own coins"
  ON user_coins
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Create function to initialize user data
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Create user profile
  INSERT INTO public.user_profiles (id, username, full_name)
  VALUES (new.id, new.raw_user_meta_data->>'username', new.raw_user_meta_data->>'full_name');

  -- Initialize user coins
  INSERT INTO public.user_coins (user_id, balance)
  VALUES (new.id, 0);

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to get user profile with challenge count
CREATE OR REPLACE FUNCTION get_user_profile(user_id uuid)
RETURNS json AS $$
DECLARE
  profile json;
BEGIN
  SELECT json_build_object(
    'id', p.id,
    'username', p.username,
    'full_name', p.full_name,
    'avatar_url', p.avatar_url,
    'email', u.email,
    'challenges_completed', COALESCE(
      (SELECT COUNT(*) 
       FROM challenge_submissions cs 
       WHERE cs.user_id = p.id AND cs.status = 'completed'),
      0
    )
  )
  INTO profile
  FROM user_profiles p
  JOIN auth.users u ON u.id = p.id
  WHERE p.id = user_id;

  RETURN profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;