-- Improve premium subscription handling
CREATE OR REPLACE FUNCTION handle_premium_subscription(
  p_user_id UUID,
  p_email TEXT
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_premium_plan_id UUID;
  v_free_plan_id UUID;
  v_current_period_start TIMESTAMPTZ;
  v_current_period_end TIMESTAMPTZ;
BEGIN
  -- Get plan IDs
  SELECT id INTO v_premium_plan_id
  FROM subscription_plans
  WHERE name = 'Premium'
  LIMIT 1;

  SELECT id INTO v_free_plan_id
  FROM subscription_plans
  WHERE name = 'Free'
  LIMIT 1;

  -- Set subscription period
  v_current_period_start := NOW();
  v_current_period_end := v_current_period_start + INTERVAL '30 days';

  -- Determine which plan to use
  IF p_email = 'chitti.gouth<PERSON><PERSON>@gmail.com' AND v_premium_plan_id IS NOT NULL THEN
    -- Premium user
    BEGIN
      INSERT INTO user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        payment_method_id
      )
      VALUES (
        p_user_id,
        v_premium_plan_id,
        'active',
        v_current_period_start,
        v_current_period_end,
        'default'
      )
      ON CONFLICT (user_id) 
      DO UPDATE SET
        plan_id = v_premium_plan_id,
        status = 'active',
        current_period_start = v_current_period_start,
        current_period_end = v_current_period_end,
        updated_at = NOW();
    EXCEPTION 
      WHEN OTHERS THEN
        -- Log error but don't fail
        RAISE NOTICE 'Error setting premium subscription: %', SQLERRM;
    END;
  ELSIF v_free_plan_id IS NOT NULL THEN
    -- Free user
    BEGIN
      INSERT INTO user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        payment_method_id
      )
      VALUES (
        p_user_id,
        v_free_plan_id,
        'active',
        v_current_period_start,
        v_current_period_end,
        'default'
      )
      ON CONFLICT (user_id) 
      DO UPDATE SET
        plan_id = v_free_plan_id,
        status = 'active',
        current_period_start = v_current_period_start,
        current_period_end = v_current_period_end,
        updated_at = NOW();
    EXCEPTION 
      WHEN OTHERS THEN
        -- Log error but don't fail
        RAISE NOTICE 'Error setting free subscription: %', SQLERRM;
    END;
  END IF;
END;
$$;

-- Create function to get user subscription with plan details
CREATE OR REPLACE FUNCTION get_user_subscription(p_user_id UUID)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_subscription jsonb;
  v_free_plan_id UUID;
BEGIN
  -- Get subscription data
  SELECT jsonb_build_object(
    'id', us.id,
    'user_id', us.user_id,
    'plan_id', us.plan_id,
    'status', us.status,
    'current_period_start', us.current_period_start,
    'current_period_end', us.current_period_end,
    'subscription_plan', jsonb_build_object(
      'id', sp.id,
      'name', sp.name,
      'features', sp.features
    )
  )
  INTO v_subscription
  FROM user_subscriptions us
  JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE us.user_id = p_user_id;

  -- If no subscription found, create a free one
  IF v_subscription IS NULL THEN
    -- Get free plan ID
    SELECT id INTO v_free_plan_id
    FROM subscription_plans
    WHERE name = 'Free'
    LIMIT 1;

    IF v_free_plan_id IS NOT NULL THEN
      -- Create free subscription
      INSERT INTO user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        payment_method_id
      )
      VALUES (
        p_user_id,
        v_free_plan_id,
        'active',
        NOW(),
        NOW() + INTERVAL '30 days',
        'default'
      )
      RETURNING jsonb_build_object(
        'id', id,
        'user_id', user_id,
        'plan_id', plan_id,
        'status', status,
        'current_period_start', current_period_start,
        'current_period_end', current_period_end,
        'subscription_plan', (
          SELECT jsonb_build_object(
            'id', id,
            'name', name,
            'features', features
          )
          FROM subscription_plans
          WHERE id = v_free_plan_id
        )
      )
      INTO v_subscription;
    END IF;
  END IF;

  RETURN v_subscription;
END;
$$;

-- Create trigger to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user_registration()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Initialize user data
  INSERT INTO users (
    id,
    email,
    username,
    full_name
  ) VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'username', 'user_' || substr(new.id::text, 1, 8)),
    COALESCE(new.raw_user_meta_data->>'full_name', 'New User')
  );

  -- Initialize user coins
  INSERT INTO user_coins (
    user_id,
    balance,
    last_transaction_at
  ) VALUES (
    new.id,
    100,
    now()
  );

  -- Create initial coin transaction
  INSERT INTO coin_transactions (
    user_id,
    amount,
    transaction_type,
    description
  ) VALUES (
    new.id,
    100,
    'credit',
    'Welcome bonus'
  );

  -- Initialize leaderboard entry
  INSERT INTO leaderboard (
    user_id,
    total_points,
    challenges_completed,
    rank
  ) VALUES (
    new.id,
    0,
    0,
    (SELECT COALESCE(MAX(rank), 0) + 1 FROM leaderboard)
  );

  -- Handle subscription
  PERFORM handle_premium_subscription(new.id, new.email);

  RETURN new;
END;
$$;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user_registration();