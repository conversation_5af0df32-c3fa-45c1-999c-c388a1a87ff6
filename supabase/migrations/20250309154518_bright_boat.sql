/*
  # Fix User Profiles and Triggers

  1. Changes
    - Drop existing trigger and function to avoid conflicts
    - Create new trigger function with better error handling
    - Add new trigger for user profile creation
    - Add policies for user profile access

  2. Security
    - Enable RLS on user_profiles table
    - Add policies for user profile access
    - Ensure secure default values
*/

-- Drop existing trigger and function if they exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user_registration();

-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (
    id,
    username,
    full_name,
    avatar_url,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
    NEW.raw_user_meta_data->>'avatar_url',
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the new trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Ensure RLS is enabled
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can read own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.user_profiles;

-- Create new policies
CREATE POLICY "Users can read own profile"
ON public.user_profiles
FOR SELECT
TO authenticated
USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
ON public.user_profiles
FOR UPDATE
TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Create missing profiles for existing users
DO $$
BEGIN
  INSERT INTO public.user_profiles (
    id,
    username,
    full_name,
    created_at,
    updated_at
  )
  SELECT 
    users.id,
    COALESCE(users.raw_user_meta_data->>'username', split_part(users.email, '@', 1)),
    COALESCE(users.raw_user_meta_data->>'full_name', split_part(users.email, '@', 1)),
    NOW(),
    NOW()
  FROM auth.users
  WHERE NOT EXISTS (
    SELECT 1 FROM public.user_profiles WHERE user_profiles.id = users.id
  );
END $$;