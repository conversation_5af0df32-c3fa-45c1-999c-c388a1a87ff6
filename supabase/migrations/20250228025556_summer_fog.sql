-- Fix the search_ai_responses function to use double precision
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION search_ai_responses(search_term TEXT)
RETURNS TABLE (
  id UUID,
  keyword TEXT,
  content TEXT,
  category TEXT,
  language TEXT,
  similarity DOUBLE PRECISION
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ar.id,
    ar.keyword,
    ar.content,
    ar.category,
    ar.language,
    similarity(ar.keyword, search_term)::double precision AS similarity
  FROM 
    ai_responses ar
  WHERE 
    ar.keyword % search_term
  ORDER BY 
    similarity DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Add more responses with proper formatting
INSERT INTO ai_responses (keyword, content, category, language)
VALUES 
('cyber kill chain', 'The Cyber Kill Chain is a model developed by <PERSON> Martin that describes the stages of a cyberattack. The seven stages are: 1) Reconnaissance - gathering information about the target, 2) Weaponization - creating malware tailored to the target, 3) Delivery - transmitting the weapon to the target, 4) Exploitation - triggering the malicious code, 5) Installation - installing malware on the target, 6) Command and Control - establishing persistent access, 7) Actions on Objectives - achieving the attack goals.', 'cybersecurity', 'english'),

('what is your name', 'I am XCerberus AI, your cybersecurity assistant. I can help you with various cybersecurity topics, answer questions, and provide guidance on security best practices.', 'general', 'english')

ON CONFLICT (keyword) DO NOTHING;