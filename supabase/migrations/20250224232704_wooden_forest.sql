/*
  # Update Users Table Policies

  1. Changes
    - Add policy existence checks before creation
    - Add public registration policy
    - Update existing policies
  
  2. Security
    - Enable RLS on users table
    - Add policies for user data access control
    - Allow public registration
*/

DO $$ 
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can read own data" ON users;
  DROP POLICY IF EXISTS "Users can update own data" ON users;
  DROP POLICY IF EXISTS "Users can insert their own data" ON users;
  DROP POLICY IF EXISTS "Public can create new users" ON users;
END $$;

-- Create new policies
CREATE POLICY "Users can read own data"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own data"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can insert their own data"
  ON users
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Public can create new users"
  ON users
  FOR INSERT
  TO public
  WITH CHECK (true);

-- Add policy to allow users to read their own coin balance
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can read own coin balance" ON user_coins;
  CREATE POLICY "Users can read own coin balance"
    ON user_coins
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);
END $$;

-- Add policy to allow users to read their own transactions
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can read own transactions" ON coin_transactions;
  CREATE POLICY "Users can read own transactions"
    ON coin_transactions
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);
END $$;

-- Add policy to allow users to read their own submissions
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can read own submissions" ON challenge_submissions;
  CREATE POLICY "Users can read own submissions"
    ON challenge_submissions
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);
END $$;

-- Add policy to allow users to create submissions
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can create submissions" ON challenge_submissions;
  CREATE POLICY "Users can create submissions"
    ON challenge_submissions
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);
END $$;

-- Add policy to allow public to read challenges
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Anyone can read challenges" ON challenges;
  CREATE POLICY "Anyone can read challenges"
    ON challenges
    FOR SELECT
    TO public
    USING (true);
END $$;

-- Add policy to allow public to read leaderboard
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Anyone can read leaderboard" ON leaderboard;
  CREATE POLICY "Anyone can read leaderboard"
    ON leaderboard
    FOR SELECT
    TO public
    USING (true);
END $$;