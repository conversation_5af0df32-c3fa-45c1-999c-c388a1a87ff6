/*
  # OS Simulator Sessions Migration Fix
  
  1. Changes
    - Drop existing policies before creating new ones
    - Add IF NOT EXISTS checks
    - Ensure clean policy creation
*/

-- Drop existing policies if they exist
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can read own sessions" ON os_simulator_sessions;
  DROP POLICY IF EXISTS "Users can insert own sessions" ON os_simulator_sessions;
  DROP POLICY IF EXISTS "Users can update own sessions" ON os_simulator_sessions;
EXCEPTION
  WHEN undefined_object THEN null;
END $$;

-- Create the sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS os_simulator_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users ON DELETE CASCADE,
  topic text NOT NULL,
  state jsonb NOT NULL DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE os_simulator_sessions ENABLE ROW LEVEL SECURITY;

-- Create new policies with unique names
CREATE POLICY "os_simulator_sessions_select_20250319"
  ON os_simulator_sessions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "os_simulator_sessions_insert_20250319"
  ON os_simulator_sessions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "os_simulator_sessions_update_20250319"
  ON os_simulator_sessions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create index for faster lookups if it doesn't exist
CREATE INDEX IF NOT EXISTS os_simulator_sessions_user_topic_idx 
  ON os_simulator_sessions (user_id, topic);

-- Drop existing trigger and function if they exist
DROP TRIGGER IF EXISTS update_os_simulator_sessions_updated_at ON os_simulator_sessions;
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Create function for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for timestamp updates
CREATE TRIGGER update_os_simulator_sessions_updated_at
  BEFORE UPDATE
  ON os_simulator_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();