-- Update subscription plans to change Basic to Free
UPDATE subscription_plans 
SET name = 'Free', price = 0
WHERE name = 'Basic';

-- Make sure we have all three plans
DO $$ 
DECLARE
  free_plan_exists boolean;
  premium_plan_exists boolean;
  business_plan_exists boolean;
BEGIN
  -- Check if plans exist
  SELECT EXISTS(SELECT 1 FROM subscription_plans WHERE name = 'Free') INTO free_plan_exists;
  SELECT EXISTS(SELECT 1 FROM subscription_plans WHERE name = 'Premium') INTO premium_plan_exists;
  SELECT EXISTS(SELECT 1 FROM subscription_plans WHERE name = 'Business') INTO business_plan_exists;
  
  -- Create Free plan if it doesn't exist
  IF NOT free_plan_exists THEN
    INSERT INTO subscription_plans (name, description, price, interval, features) VALUES
    (
      'Free',
      'Perfect for beginners',
      0.00,
      'month',
      '{
        "labs": 10,
        "challenges": 5,
        "support": "community",
        "tools": ["basic-lab", "community-forum"]
      }'
    );
  END IF;
  
  -- Create Premium plan if it doesn't exist
  IF NOT premium_plan_exists THEN
    INSERT INTO subscription_plans (name, description, price, interval, features) VALUES
    (
      'Premium',
      'For dedicated learners',
      399.00,
      'month',
      '{
        "labs": -1,
        "challenges": -1,
        "support": "priority",
        "tools": ["advanced-lab", "attack-box", "private-labs", "community-forum"]
      }'
    );
  END IF;
  
  -- Create Business plan if it doesn't exist
  IF NOT business_plan_exists THEN
    INSERT INTO subscription_plans (name, description, price, interval, features) VALUES
    (
      'Business',
      'Enterprise-grade solution',
      999.00,
      'month',
      '{
        "labs": -1,
        "challenges": -1,
        "support": "dedicated",
        "tools": ["advanced-lab", "attack-box", "private-labs", "team-management", "api-access", "custom-labs"]
      }'
    );
  END IF;
END $$;

-- Update existing user subscriptions to use the Free plan if they were on Basic
DO $$ 
DECLARE
  free_plan_id uuid;
BEGIN
  -- Get the Free plan ID
  SELECT id INTO free_plan_id FROM subscription_plans WHERE name = 'Free';
  
  -- Update subscriptions
  UPDATE user_subscriptions
  SET plan_id = free_plan_id
  WHERE plan_id IN (SELECT id FROM subscription_plans WHERE name = 'Basic');
END $$;