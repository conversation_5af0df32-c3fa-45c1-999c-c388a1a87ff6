-- Drop existing functions first to avoid conflicts
DO $$ 
BEGIN
  DROP FUNCTION IF EXISTS search_ai_responses(text);
  DROP FUNCTION IF EXISTS search_ai_responses(text, text, text);
  DROP FUNCTION IF EXISTS search_ai_responses_v2(text, text, text);
  DROP FUNCTION IF EXISTS search_ai_responses_v3(text, text, text);
  DROP FUNCTION IF EXISTS search_ai_responses_v4(text, text, text);
  DROP FUNCTION IF EXISTS search_ai_responses_v5(text, text, text);
EXCEPTION
  WHEN undefined_object THEN null;
END $$;

-- <PERSON><PERSON> improved search function with unique name
CREATE OR REPLACE FUNCTION search_ai_responses_v6(
  search_term TEXT,
  search_domain TEXT DEFAULT NULL,
  search_language TEXT DEFAULT 'english'
)
RETURNS TABLE (
  id UUID,
  keyword TEXT,
  content TEXT,
  category TEXT,
  domain TEXT,
  language TEXT,
  similarity DOUBLE PRECISION
) AS $$
BEGIN
  -- First try exact match
  RETURN QUERY
  SELECT 
    ar.id,
    ar.keyword,
    ar.content,
    ar.category,
    ar.domain,
    ar.language,
    1.0::double precision AS similarity
  FROM 
    ai_responses ar
  WHERE 
    LOWER(ar.keyword) = LOWER(search_term)
    AND (search_domain IS NULL OR ar.domain = search_domain)
    AND ar.language = search_language
  LIMIT 1;

  -- If no exact match, try fuzzy search
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      ar.id,
      ar.keyword,
      ar.content,
      ar.category,
      ar.domain,
      ar.language,
      similarity(ar.keyword, search_term)::double precision AS similarity
    FROM 
      ai_responses ar
    WHERE 
      ar.keyword % search_term
      AND (search_domain IS NULL OR ar.domain = search_domain)
      AND ar.language = search_language
    ORDER BY 
      similarity DESC
    LIMIT 1;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Add SIEM-related responses
INSERT INTO ai_responses (keyword, content, category, domain, language)
VALUES 
('what is siem', E'## Topic Overview
SIEM (Security Information and Event Management) is a comprehensive security solution that combines:

### Key Points
* Centralized log management and analysis
* Real-time security event monitoring
* Automated threat detection and alerting
* Compliance reporting and auditing

### Technical Details
1. Security Information Management (SIM):
   • Log storage and management
   • Data analysis and reporting
   • Compliance monitoring

2. Security Event Management (SEM):
   • Real-time monitoring
   • Event correlation
   • Security alerts
   • Incident response

### Best Practices
* Start with critical log sources
* Establish clear use cases
* Tune alerts to reduce noise
* Regular maintenance and updates

### Security Implications
SIEM is crucial for:
* Early threat detection
* Incident response
* Compliance requirements
* Security visibility

### Additional Resources
* SIEM vendor documentation
* MITRE ATT&CK framework
* Compliance standards', 'technical', 'TECHNICAL', 'english'),

('siem tools', E'## Topic Overview
Overview of popular SIEM tools and platforms.

### Key Points
* Enterprise-grade solutions
* Cloud and on-premise options
* AI/ML capabilities
* Integration features

### Technical Details
1. Splunk Enterprise Security
   • Industry leader
   • Powerful search capabilities
   • Advanced analytics

2. IBM QRadar
   • AI-powered analytics
   • Threat intelligence
   • Risk management

3. Microsoft Sentinel
   • Cloud-native SIEM
   • Azure integration
   • AI-powered analysis

### Best Practices
* Choose based on requirements
* Consider scalability needs
* Plan for training needs
* Evaluate total cost

### Additional Resources
* Vendor comparisons
* Implementation guides
* Training materials', 'technical', 'TECHNICAL', 'english'),

('siem implementation', E'## Topic Overview
Guide to implementing SIEM solutions effectively.

### Key Points
* Phased implementation approach
* Clear objectives and metrics
* Stakeholder involvement
* Resource planning

### Technical Details
1. Planning Phase
   • Define objectives
   • Identify data sources
   • Plan architecture
   • Set retention policies

2. Deployment Steps
   • Start small, scale gradually
   • Configure data collection
   • Set up correlation rules
   • Establish baselines

### Best Practices
* Document everything
* Train the team
* Monitor performance
* Regular reviews

### Security Implications
* Improved detection capability
* Better incident response
* Enhanced compliance
* Reduced risk exposure

### Additional Resources
* Implementation frameworks
* Best practice guides
* Training resources', 'technical', 'TECHNICAL', 'english')

ON CONFLICT (keyword) DO UPDATE 
SET 
  content = EXCLUDED.content,
  category = EXCLUDED.category,
  domain = EXCLUDED.domain,
  language = EXCLUDED.language;