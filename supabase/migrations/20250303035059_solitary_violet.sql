-- Improve premium subscription handling
CREATE OR REPLACE FUNCTION handle_premium_subscription(
  p_user_id UUID,
  p_email TEXT
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_premium_plan_id UUID;
  v_current_period_start TIMESTAMPTZ;
  v_current_period_end TIMESTAMPTZ;
BEGIN
  -- Only proceed for premium email
  IF p_email = 'chitti.go<PERSON><PERSON><PERSON>@gmail.com' THEN
    -- Get premium plan ID
    SELECT id INTO v_premium_plan_id
    FROM subscription_plans
    WHERE name = 'Premium'
    LIMIT 1;

    IF v_premium_plan_id IS NOT NULL THEN
      -- Set subscription period
      v_current_period_start := NOW();
      v_current_period_end := v_current_period_start + INTERVAL '30 days';

      -- Try to update existing subscription first
      UPDATE user_subscriptions
      SET
        plan_id = v_premium_plan_id,
        status = 'active',
        current_period_start = v_current_period_start,
        current_period_end = v_current_period_end,
        updated_at = NOW()
      WHERE user_id = p_user_id;
      
      -- If no rows were updated, insert new subscription
      IF NOT FOUND THEN
        BEGIN
          INSERT INTO user_subscriptions (
            user_id,
            plan_id,
            status,
            current_period_start,
            current_period_end,
            payment_method_id
          )
          VALUES (
            p_user_id,
            v_premium_plan_id,
            'active',
            v_current_period_start,
            v_current_period_end,
            'default'
          );
        EXCEPTION WHEN unique_violation THEN
          -- If insert fails due to race condition, try update again
          UPDATE user_subscriptions
          SET
            plan_id = v_premium_plan_id,
            status = 'active',
            current_period_start = v_current_period_start,
            current_period_end = v_current_period_end,
            updated_at = NOW()
          WHERE user_id = p_user_id;
        END;
      END IF;
    END IF;
  END IF;
END;
$$;

-- Create function to get user subscription with plan details
CREATE OR REPLACE FUNCTION get_user_subscription(p_user_id UUID)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_subscription jsonb;
BEGIN
  SELECT jsonb_build_object(
    'id', us.id,
    'user_id', us.user_id,
    'plan_id', us.plan_id,
    'status', us.status,
    'current_period_start', us.current_period_start,
    'current_period_end', us.current_period_end,
    'subscription_plan', jsonb_build_object(
      'id', sp.id,
      'name', sp.name,
      'features', sp.features
    )
  )
  INTO v_subscription
  FROM user_subscriptions us
  JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE us.user_id = p_user_id;

  RETURN v_subscription;
END;
$$;