/*
  # AI Response System Update

  1. Tables
    - ai_responses: Stores AI response templates
    - chat_logs: Tracks user-AI interactions
  
  2. Security
    - Enable RLS on both tables
    - Public read access for AI responses
    - Authenticated user access for chat logs

  3. Data
    - Updated AI responses with emoji formatting
    - Improved content organization
*/

-- Drop existing policies first
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "ai_responses_select_20250225" ON ai_responses;
  DROP POLICY IF EXISTS "chat_logs_select_20250225" ON chat_logs;
  DROP POLICY IF EXISTS "chat_logs_insert_20250225" ON chat_logs;
EXCEPTION
  WHEN undefined_object THEN null;
END $$;

-- Create AI responses table if not exists
CREATE TABLE IF NOT EXISTS ai_responses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  keyword text NOT NULL UNIQUE,
  content text NOT NULL,
  category text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create chat logs table if not exists
CREATE TABLE IF NOT EXISTS chat_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  user_question text NOT NULL,
  ai_response text,
  timestamp timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE ai_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_logs ENABLE ROW LEVEL SECURITY;

-- Create policies with unique names
CREATE POLICY "ai_responses_select_20250225_v2"
  ON ai_responses
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "chat_logs_select_20250225_v2"
  ON chat_logs
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "chat_logs_insert_20250225_v2"
  ON chat_logs
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Clear existing responses to avoid conflicts
TRUNCATE TABLE ai_responses;

-- Insert initial AI responses with emojis and improved formatting
INSERT INTO ai_responses (keyword, content, category) VALUES
('help_v2', E'I can help you with these areas:\n\n🔒 Security Concepts\n   • Fundamentals of cybersecurity\n   • Security best practices\n   • Risk assessment\n\n💻 Technical Skills\n   • Hands-on labs and exercises\n   • Tool usage and configuration\n   • System hardening\n\n🎯 Challenges\n   • CTF competitions\n   • Real-world scenarios\n   • Skill assessments\n\nWhat would you like to explore?', 'general'),

('linux_v2', E'Linux is essential for cybersecurity. Here''s what you can learn:\n\n📟 Command Line Basics\n   • Navigation and file management\n   • Process control\n   • System monitoring\n\n⚙️ System Administration\n   • User management\n   • Service configuration\n   • Package management\n\n🛡️ Security Hardening\n   • Permissions and access control\n   • Firewall configuration\n   • Security auditing\n\nWhich area interests you?', 'technical'),

('hack_v2', E'Ethical Hacking Methodology:\n\n1. 🔍 Reconnaissance\n   • Information gathering\n   • Network mapping\n   • Target identification\n\n2. 🎯 Scanning\n   • Vulnerability assessment\n   • Port scanning\n   • Service enumeration\n\n3. 🔓 Exploitation\n   • Vulnerability exploitation\n   • Privilege escalation\n   • Post-exploitation\n\n⚠️ Remember: Only practice on authorized systems!', 'security'),

('challenge_v2', E'Available Challenge Categories:\n\n🌐 Web Security\n   • SQL Injection\n   • XSS Attacks\n   • CSRF Exploitation\n\n💻 Binary Exploitation\n   • Buffer Overflows\n   • Format String Vulnerabilities\n   • ROP Chains\n\n🔐 Cryptography\n   • Classic Ciphers\n   • Modern Encryption\n   • Hash Cracking\n\nWould you like a personalized challenge recommendation?', 'challenges'),

('tools_v2', E'Essential Security Tools:\n\n🔍 Reconnaissance\n   • Nmap - Network mapping\n   • Maltego - Information gathering\n   • Shodan - Internet scanning\n\n🛡️ Analysis\n   • Wireshark - Packet analysis\n   • Burp Suite - Web testing\n   • Metasploit - Penetration testing\n\n🔧 Forensics\n   • Volatility - Memory analysis\n   • Autopsy - Disk analysis\n   • NetworkMiner - Network forensics\n\nWhich tool would you like to learn more about?', 'technical'),

('ctf_v2', E'CTF Categories and Skills:\n\n🌐 Web Exploitation\n   • SQL Injection\n   • XSS Attacks\n   • Authentication Bypass\n\n💻 Binary Exploitation\n   • Buffer Overflows\n   • Format Strings\n   • Shellcoding\n\n🔐 Cryptography\n   • Classic Ciphers\n   • Modern Crypto\n   • Hash Cracking\n\n🔍 Forensics\n   • File Analysis\n   • Memory Forensics\n   • Network Forensics\n\nReady to start your first challenge?', 'challenges'),

('network_v2', E'Network Security Topics:\n\n📡 Infrastructure\n   • TCP/IP Protocol Suite\n   • Network Architecture\n   • Routing & Switching\n\n🛡️ Defense\n   • Firewalls & IDS/IPS\n   • VPNs & Encryption\n   • Access Control\n\n📊 Monitoring\n   • Traffic Analysis\n   • Log Management\n   • Incident Response\n\nWhich area would you like to explore?', 'technical'),

('password_v2', E'Password Security Guidelines:\n\n🔑 Strong Passwords\n   • Minimum 12 characters\n   • Mix of characters\n   • Unique per account\n\n🔐 Authentication\n   • Two-factor (2FA)\n   • Biometric options\n   • Hardware keys\n\n🛡️ Management\n   • Password managers\n   • Regular updates\n   • Secure storage\n\nNeed help implementing these practices?', 'security'),

('malware_v2', E'Malware Types and Analysis:\n\n🦠 Common Types\n   • Viruses & Worms\n   • Trojans & Backdoors\n   • Ransomware & Crypters\n\n🔍 Analysis Methods\n   • Static Analysis\n   • Dynamic Analysis\n   • Memory Analysis\n\n🛡️ Protection\n   • Prevention\n   • Detection\n   • Removal\n\nWould you like to learn about malware analysis?', 'security'),

('web_v2', E'Web Security Topics:\n\n🔓 Common Vulnerabilities\n   • SQL Injection (SQLi)\n   • Cross-Site Scripting (XSS)\n   • Cross-Site Request Forgery (CSRF)\n\n🛡️ Security Headers\n   • Content Security Policy\n   • CORS Configuration\n   • XSS Protection\n\n🔒 Best Practices\n   • Input Validation\n   • Output Encoding\n   • Access Control\n\nWhich topic would you like to explore?', 'technical');