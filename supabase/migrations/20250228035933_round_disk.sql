-- Create hot and cold cache tables for AI responses
CREATE TABLE IF NOT EXISTS ai_responses_hot (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  query text NOT NULL,
  content text NOT NULL,
  category text NOT NULL,
  language text DEFAULT 'english',
  feedback_count integer DEFAULT 1,
  created_at timestamptz DEFAULT now(),
  UNIQUE(query, language)
);

CREATE TABLE IF NOT EXISTS ai_responses_cold (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  query text NOT NULL,
  content text NOT NULL,
  category text NOT NULL,
  language text DEFAULT 'english',
  feedback_count integer DEFAULT 1,
  created_at timestamptz DEFAULT now(),
  UNIQUE(query, language)
);

-- Create feedback tracking table
CREATE TABLE IF NOT EXISTS ai_response_feedback (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  query text NOT NULL,
  response_content text NOT NULL,
  feedback_type text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE ai_responses_hot ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_responses_cold ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_response_feedback ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read hot responses"
  ON ai_responses_hot
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Anyone can read cold responses"
  ON ai_responses_cold
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Authenticated users can insert feedback"
  ON ai_response_feedback
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Create indexes for better search performance
CREATE INDEX ai_responses_hot_query_idx ON ai_responses_hot USING gin (query gin_trgm_ops);
CREATE INDEX ai_responses_cold_query_idx ON ai_responses_cold USING gin (query gin_trgm_ops);