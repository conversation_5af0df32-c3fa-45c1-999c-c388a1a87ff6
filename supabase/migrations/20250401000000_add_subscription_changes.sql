-- Create subscription_changes table to track subscription upgrades
CREATE TABLE IF NOT EXISTS subscription_changes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  from_plan text NOT NULL,
  to_plan text NOT NULL,
  change_time timestamptz NOT NULL DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Add RLS policies
ALTER TABLE subscription_changes ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own subscription changes
CREATE POLICY "Users can read own subscription changes"
  ON subscription_changes FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Allow users to insert their own subscription changes
CREATE POLICY "Users can insert own subscription changes"
  ON subscription_changes FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Create function to handle subscription upgrades
CREATE OR REPLACE FUNCTION handle_subscription_upgrade(
  p_user_id uuid,
  p_new_plan_name text
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_plan_id uuid;
  v_current_plan_name text;
  v_new_plan_id uuid;
  v_current_period_start timestamptz;
  v_current_period_end timestamptz;
  v_result json;
BEGIN
  -- Get current subscription
  SELECT 
    us.plan_id, 
    sp.name 
  INTO 
    v_current_plan_id, 
    v_current_plan_name
  FROM 
    user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE 
    us.user_id = p_user_id;

  -- Get new plan ID
  SELECT id INTO v_new_plan_id
  FROM subscription_plans
  WHERE name = p_new_plan_name;

  IF v_new_plan_id IS NULL THEN
    RAISE EXCEPTION 'Plan % not found', p_new_plan_name;
  END IF;

  -- Set subscription period
  v_current_period_start := NOW();
  v_current_period_end := v_current_period_start + INTERVAL '30 days';

  -- Update subscription
  UPDATE user_subscriptions
  SET 
    plan_id = v_new_plan_id,
    status = 'active',
    current_period_start = v_current_period_start,
    current_period_end = v_current_period_end,
    updated_at = NOW()
  WHERE 
    user_id = p_user_id;

  -- Track the change
  INSERT INTO subscription_changes (
    user_id,
    from_plan,
    to_plan,
    change_time
  ) VALUES (
    p_user_id,
    v_current_plan_name,
    p_new_plan_name,
    NOW()
  );

  -- Also track in user activity
  INSERT INTO user_activity (
    user_id,
    activity_type,
    description,
    created_at
  ) VALUES (
    p_user_id,
    'subscription_change',
    format('Upgraded from %s to %s', v_current_plan_name, p_new_plan_name),
    NOW()
  );

  -- Return result
  SELECT json_build_object(
    'success', true,
    'message', format('Subscription upgraded from %s to %s', v_current_plan_name, p_new_plan_name),
    'from_plan', v_current_plan_name,
    'to_plan', p_new_plan_name
  ) INTO v_result;

  RETURN v_result;
END;
$$;
