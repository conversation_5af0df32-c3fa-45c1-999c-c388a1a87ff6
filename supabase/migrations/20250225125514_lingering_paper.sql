-- Create AI responses table
CREATE TABLE IF NOT EXISTS ai_responses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  keyword text NOT NULL UNIQUE,
  content text NOT NULL,
  category text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create chat logs table
CREATE TABLE IF NOT EXISTS chat_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  user_question text NOT NULL,
  ai_response text,
  timestamp timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE ai_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_logs ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read AI responses"
  ON ai_responses
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Users can read own chat logs"
  ON chat_logs
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert chat logs"
  ON chat_logs
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Insert initial AI responses
INSERT INTO ai_responses (keyword, content, category) VALUES
('help', 'I can help you with:\n- Understanding cybersecurity concepts\n- Solving technical challenges\n- Learning new security tools\n- Finding relevant resources\n- Answering your questions\n\nWhat would you like to know more about?', 'general'),
('linux', 'Linux is essential for cybersecurity. Here are key areas:\n1. Basic Commands\n2. System Administration\n3. Security Hardening\n4. Network Configuration\n\nWhich area would you like to explore?', 'technical'),
('hack', 'Ethical hacking involves:\n1. Reconnaissance\n2. Scanning\n3. Gaining Access\n4. Maintaining Access\n5. Covering Tracks\n\nRemember: Only practice on authorized systems!', 'security'),
('challenge', 'Our platform offers various challenges:\n- Web Security\n- Binary Exploitation\n- Cryptography\n- Network Security\n\nWould you like me to recommend a challenge based on your skill level?', 'challenges'),
('tools', 'Essential cybersecurity tools:\n1. Nmap - Network scanning\n2. Wireshark - Packet analysis\n3. Metasploit - Penetration testing\n4. Burp Suite - Web security\n\nWhich tool would you like to learn more about?', 'technical'),
('ctf', 'Capture The Flag (CTF) categories:\n1. Web Exploitation\n2. Binary Exploitation\n3. Cryptography\n4. Forensics\n5. Reverse Engineering\n\nReady to start a challenge?', 'challenges'),
('network', 'Network security fundamentals:\n1. TCP/IP Protocol\n2. Firewalls & IDS\n3. VPNs & Encryption\n4. Network Monitoring\n\nWhat aspect interests you?', 'technical'),
('password', 'Password security best practices:\n1. Use strong, unique passwords\n2. Enable 2FA/MFA\n3. Use a password manager\n4. Regular password updates\n\nNeed help implementing these?', 'security'),
('malware', 'Types of malware:\n1. Viruses\n2. Trojans\n3. Ransomware\n4. Spyware\n5. Rootkits\n\nWould you like to learn about malware analysis?', 'security'),
('web', 'Web security topics:\n1. SQL Injection\n2. XSS (Cross-Site Scripting)\n3. CSRF Attacks\n4. Authentication Bypass\n\nWhich vulnerability interests you?', 'technical');