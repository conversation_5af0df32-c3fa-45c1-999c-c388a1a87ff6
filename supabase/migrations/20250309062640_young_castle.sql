/*
  # Create OS Simulator Sessions Table

  1. New Tables
    - `os_simulator_sessions`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `topic` (text)
      - `state` (jsonb)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `os_simulator_sessions` table
    - Add policies for authenticated users to:
      - Read own sessions
      - Insert own sessions
      - Update own sessions

  3. Indexing
    - Create index on user_id and topic for faster lookups

  4. Triggers
    - Add updated_at trigger for automatic timestamp updates
*/

-- Drop existing policies if they exist
DO $$ 
BEGIN
  -- Drop read policy if exists
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'os_simulator_sessions' 
    AND policyname = 'Users can read own sessions'
  ) THEN
    DROP POLICY IF EXISTS "Users can read own sessions" ON os_simulator_sessions;
  END IF;

  -- Drop insert policy if exists
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'os_simulator_sessions' 
    AND policyname = 'Users can insert own sessions'
  ) THEN
    DROP POLICY IF EXISTS "Users can insert own sessions" ON os_simulator_sessions;
  END IF;

  -- Drop update policy if exists
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'os_simulator_sessions' 
    AND policyname = 'Users can update own sessions'
  ) THEN
    DROP POLICY IF EXISTS "Users can update own sessions" ON os_simulator_sessions;
  END IF;
END $$;

-- Create the sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS os_simulator_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users ON DELETE CASCADE,
  topic text NOT NULL,
  state jsonb NOT NULL DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE os_simulator_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can read own sessions"
  ON os_simulator_sessions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sessions"
  ON os_simulator_sessions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions"
  ON os_simulator_sessions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS os_simulator_sessions_user_topic_idx ON os_simulator_sessions (user_id, topic);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_os_simulator_sessions_updated_at ON os_simulator_sessions;

CREATE TRIGGER update_os_simulator_sessions_updated_at
  BEFORE UPDATE
  ON os_simulator_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();