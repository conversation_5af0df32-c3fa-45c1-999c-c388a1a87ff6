-- Add more common responses for basic queries
INSERT INTO ai_responses (keyword, content, category, language)
VALUES 
('what is cybersecurity', 'Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These attacks are usually aimed at accessing, changing, or destroying sensitive information; extorting money from users; or interrupting normal business processes. Implementing effective cybersecurity measures is particularly challenging today because there are more devices than people, and attackers are becoming more innovative.', 'cybersecurity', 'english'),

('what is a buffer overflow attack', 'A buffer overflow attack occurs when a program or process attempts to write more data to a fixed-length block of memory (a buffer) than the buffer is allocated to hold. By sending carefully crafted input to an application, an attacker can cause the application to execute arbitrary code, crash the application, or expose sensitive information. Buffer overflow vulnerabilities are commonly found in programs written in languages like C and C++ that don''t have built-in protection against accessing or overwriting data in memory.', 'cybersecurity', 'english'),

('what is sql injection', 'SQL Injection is a code injection technique that exploits vulnerabilities in the database layer of an application. It occurs when user input is incorrectly filtered and directly included in SQL statements. This allows attackers to execute arbitrary SQL commands, potentially leading to unauthorized data access, modification, or deletion. For example, an attacker might input something like "'' OR 1=1 --" into a login form to bypass authentication. Prevention methods include using parameterized queries, stored procedures, and input validation.', 'cybersecurity', 'english'),

('im goutham', 'Hello Goutham! I''m your cybersecurity AI assistant. How can I help you with cybersecurity topics today?', 'general', 'english')

ON CONFLICT (keyword) DO NOTHING;

-- Create a function to handle fuzzy search
CREATE OR REPLACE FUNCTION search_ai_responses(search_term TEXT)
RETURNS TABLE (
  id UUID,
  keyword TEXT,
  content TEXT,
  category TEXT,
  language TEXT,
  similarity FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ar.id,
    ar.keyword,
    ar.content,
    ar.category,
    ar.language,
    similarity(ar.keyword, search_term) AS similarity
  FROM 
    ai_responses ar
  WHERE 
    ar.keyword % search_term
  ORDER BY 
    similarity DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;