-- Drop existing trigger first
DO $$ 
BEGIN
  DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
  
  -- Drop functions in correct order
  DROP FUNCTION IF EXISTS handle_auth_sign_in();
  DROP FUNCTION IF EXISTS handle_premium_subscription(UUID, TEXT);
  DROP FUNCTION IF EXISTS handle_new_user_registration();
  DROP FUNCTION IF EXISTS get_user_subscription(UUID);
EXCEPTION 
  WHEN OTHERS THEN NULL;
END $$;

-- Create user roles table
CREATE TABLE IF NOT EXISTS user_roles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  role text NOT NULL DEFAULT 'basic',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id)
);

ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own role"
  ON user_roles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Create function to handle user registration
CREATE OR REPLACE FUNCTION handle_new_user_registration()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_free_plan_id UUID;
  v_premium_plan_id UUID;
  v_role text;
BEGIN
  -- Get plan IDs
  SELECT id INTO v_free_plan_id
  FROM subscription_plans
  WHERE name = 'Free'
  LIMIT 1;

  SELECT id INTO v_premium_plan_id
  FROM subscription_plans
  WHERE name = 'Premium'
  LIMIT 1;

  -- Create user profile
  INSERT INTO users (
    id,
    email,
    username,
    full_name
  ) VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'username', 'user_' || substr(new.id::text, 1, 8)),
    COALESCE(new.raw_user_meta_data->>'full_name', 'New User')
  );

  -- Determine role and subscription based on email
  IF new.email = '<EMAIL>' THEN
    v_role := 'premium';
    
    -- Create premium subscription
    INSERT INTO user_subscriptions (
      user_id,
      plan_id,
      status,
      current_period_start,
      current_period_end,
      payment_method_id
    ) VALUES (
      new.id,
      v_premium_plan_id,
      'active',
      NOW(),
      NOW() + INTERVAL '30 days',
      'default'
    );
  ELSE
    v_role := 'basic';
    
    -- Create free subscription
    INSERT INTO user_subscriptions (
      user_id,
      plan_id,
      status,
      current_period_start,
      current_period_end,
      payment_method_id
    ) VALUES (
      new.id,
      v_free_plan_id,
      'active',
      NOW(),
      NOW() + INTERVAL '30 days',
      'default'
    );
  END IF;

  -- Create user role
  INSERT INTO user_roles (
    user_id,
    role
  ) VALUES (
    new.id,
    v_role
  );

  -- Initialize user coins
  INSERT INTO user_coins (
    user_id,
    balance,
    last_transaction_at
  ) VALUES (
    new.id,
    100,
    now()
  );

  -- Create initial coin transaction
  INSERT INTO coin_transactions (
    user_id,
    amount,
    transaction_type,
    description
  ) VALUES (
    new.id,
    100,
    'credit',
    'Welcome bonus'
  );

  -- Initialize leaderboard entry
  INSERT INTO leaderboard (
    user_id,
    total_points,
    challenges_completed,
    rank
  ) VALUES (
    new.id,
    0,
    0,
    (SELECT COALESCE(MAX(rank), 0) + 1 FROM leaderboard)
  );

  RETURN new;
END;
$$;

-- Create function to get user subscription with role
CREATE OR REPLACE FUNCTION get_user_subscription(p_user_id UUID)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_subscription jsonb;
  v_role text;
BEGIN
  -- Get user role
  SELECT role INTO v_role
  FROM user_roles
  WHERE user_id = p_user_id;

  -- Get subscription data with role
  SELECT jsonb_build_object(
    'id', us.id,
    'user_id', us.user_id,
    'plan_id', us.plan_id,
    'status', us.status,
    'role', COALESCE(v_role, 'basic'),
    'current_period_start', us.current_period_start,
    'current_period_end', us.current_period_end,
    'subscription_plan', jsonb_build_object(
      'id', sp.id,
      'name', sp.name,
      'features', sp.features
    )
  )
  INTO v_subscription
  FROM user_subscriptions us
  JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE us.user_id = p_user_id;

  RETURN v_subscription;
END;
$$;

-- Create new trigger for user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user_registration();