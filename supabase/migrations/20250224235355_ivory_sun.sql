-- Drop existing trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create improved function to initialize user data
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Create user profile
  INSERT INTO public.users (
    id,
    email,
    username,
    full_name
  ) VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'username', 'user_' || substr(new.id::text, 1, 8)),
    COALESCE(new.raw_user_meta_data->>'full_name', 'New User')
  );

  -- Initialize user coins with starting balance
  INSERT INTO public.user_coins (
    user_id,
    balance,
    last_transaction_at
  ) VALUES (
    new.id,
    100,
    now()
  );

  -- Create initial coin transaction
  INSERT INTO public.coin_transactions (
    user_id,
    amount,
    transaction_type,
    description
  ) VALUES (
    new.id,
    100,
    'credit',
    'Welcome bonus'
  );

  -- Initialize leaderboard entry
  INSERT INTO public.leaderboard (
    user_id,
    total_points,
    challenges_completed,
    rank
  ) VALUES (
    new.id,
    0,
    0,
    (SELECT COALESCE(MAX(rank), 0) + 1 FROM public.leaderboard)
  );

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Update get_user_profile function to handle missing data
CREATE OR REPLACE FUNCTION get_user_profile(user_id uuid)
RETURNS json AS $$
DECLARE
  profile json;
BEGIN
  -- First ensure user exists
  IF NOT EXISTS (SELECT 1 FROM users WHERE id = user_id) THEN
    -- Create default user data if missing
    INSERT INTO users (id, email, username, full_name)
    SELECT 
      user_id,
      (SELECT email FROM auth.users WHERE id = user_id),
      'user_' || substr(user_id::text, 1, 8),
      'New User';
      
    -- Create coins entry if missing
    INSERT INTO user_coins (user_id, balance)
    VALUES (user_id, 100)
    ON CONFLICT (user_id) DO NOTHING;
    
    -- Create leaderboard entry if missing
    INSERT INTO leaderboard (user_id, total_points, challenges_completed, rank)
    VALUES (user_id, 0, 0, (SELECT COALESCE(MAX(rank), 0) + 1 FROM leaderboard))
    ON CONFLICT (user_id) DO NOTHING;
  END IF;

  SELECT json_build_object(
    'id', u.id,
    'email', u.email,
    'username', u.username,
    'full_name', u.full_name,
    'avatar_url', u.avatar_url,
    'challenges_completed', COALESCE(
      (SELECT COUNT(*) 
       FROM challenge_submissions cs 
       WHERE cs.user_id = u.id AND cs.status = 'completed'),
      0
    ),
    'coins', COALESCE(
      (SELECT balance 
       FROM user_coins uc 
       WHERE uc.user_id = u.id),
      0
    ),
    'rank', COALESCE(
      (SELECT rank 
       FROM leaderboard l 
       WHERE l.user_id = u.id),
      0
    )
  )
  INTO profile
  FROM users u
  WHERE u.id = user_id;

  RETURN profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;