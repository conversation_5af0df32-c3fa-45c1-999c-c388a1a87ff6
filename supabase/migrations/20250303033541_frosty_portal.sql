-- Create function to ensure premium subscription
CREATE OR REPLACE FUNCTION ensure_premium_subscription(
  p_user_id UUID,
  p_plan_id UUID
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_period_start TIMESTAMPTZ;
  v_current_period_end TIMESTAMPTZ;
BEGIN
  -- Set subscription period
  v_current_period_start := NOW();
  v_current_period_end := v_current_period_start + INTERVAL '30 days';

  -- Insert or update subscription
  INSERT INTO user_subscriptions (
    user_id,
    plan_id,
    status,
    current_period_start,
    current_period_end,
    payment_method_id
  )
  VALUES (
    p_user_id,
    p_plan_id,
    'active',
    v_current_period_start,
    v_current_period_end,
    'default'
  )
  ON CONFLICT (user_id)
  DO UPDATE SET
    plan_id = p_plan_id,
    status = 'active',
    current_period_start = v_current_period_start,
    current_period_end = v_current_period_end,
    updated_at = NOW();
END;
$$;

-- Ensure RLS policies are correct
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can read own subscription" ON user_subscriptions;
  DROP POLICY IF EXISTS "Users can update own subscription" ON user_subscriptions;
  
  CREATE POLICY "Users can read own subscription"
    ON user_subscriptions
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can update own subscription"
    ON user_subscriptions
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);
END $$;