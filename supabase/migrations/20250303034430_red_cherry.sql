-- <PERSON><PERSON> improved function to handle premium subscription
CREATE OR REPLACE FUNCTION handle_premium_subscription(
  p_user_id UUID,
  p_email TEXT
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_premium_plan_id UUID;
  v_current_period_start TIMESTAMPTZ;
  v_current_period_end TIMESTAMPTZ;
BEGIN
  -- Only proceed for premium email
  IF p_email = 'chitti.gouth<PERSON><PERSON>@gmail.com' THEN
    -- Get premium plan ID
    SELECT id INTO v_premium_plan_id
    FROM subscription_plans
    WHERE name = 'Premium'
    LIMIT 1;

    IF v_premium_plan_id IS NOT NULL THEN
      -- Set subscription period
      v_current_period_start := NOW();
      v_current_period_end := v_current_period_start + INTERVAL '30 days';

      -- Update or insert subscription
      INSERT INTO user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        payment_method_id
      )
      VALUES (
        p_user_id,
        v_premium_plan_id,
        'active',
        v_current_period_start,
        v_current_period_end,
        'default'
      )
      ON CONFLICT (user_id) 
      DO UPDATE SET
        plan_id = v_premium_plan_id,
        status = 'active',
        current_period_start = v_current_period_start,
        current_period_end = v_current_period_end,
        updated_at = NOW();
    END IF;
  END IF;
END;
$$;

-- Create trigger to handle premium subscription on sign in
CREATE OR REPLACE FUNCTION handle_auth_sign_in()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Handle premium subscription
  PERFORM handle_premium_subscription(new.id, new.email);
  RETURN new;
END;
$$;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_sign_in ON auth.users;

-- Create trigger for sign in
CREATE TRIGGER on_auth_sign_in
  AFTER INSERT OR UPDATE ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_auth_sign_in();