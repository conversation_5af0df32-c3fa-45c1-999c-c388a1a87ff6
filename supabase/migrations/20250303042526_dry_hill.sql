-- Drop existing triggers and functions in correct order
DO $$ 
BEGIN
  -- First drop the trigger
  DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
  
  -- Then drop the functions
  DROP FUNCTION IF EXISTS handle_auth_sign_in();
  DROP FUNCTION IF EXISTS handle_premium_subscription(UUID, TEXT);
  DROP FUNCTION IF EXISTS handle_new_user_registration();
  DROP FUNCTION IF EXISTS get_user_subscription(UUID);
EXCEPTION 
  WHEN OTHERS THEN NULL;
END $$;

-- Create function to handle user registration
CREATE OR REPLACE FUNCTION handle_new_user_registration()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_free_plan_id UUID;
  v_premium_plan_id UUID;
  v_current_period_start TIMESTAMPTZ;
  v_current_period_end TIMESTAMPTZ;
BEGIN
  -- Get plan IDs
  SELECT id INTO v_free_plan_id
  FROM subscription_plans
  WHERE name = 'Free'
  LIMIT 1;

  SELECT id INTO v_premium_plan_id
  FROM subscription_plans
  WHERE name = 'Premium'
  LIMIT 1;

  -- Set subscription period
  v_current_period_start := NOW();
  v_current_period_end := v_current_period_start + INTERVAL '30 days';

  -- Create user profile
  INSERT INTO users (
    id,
    email,
    username,
    full_name
  ) VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'username', 'user_' || substr(new.id::text, 1, 8)),
    COALESCE(new.raw_user_meta_data->>'full_name', 'New User')
  );

  -- Initialize user coins
  INSERT INTO user_coins (
    user_id,
    balance,
    last_transaction_at
  ) VALUES (
    new.id,
    100,
    now()
  );

  -- Create initial coin transaction
  INSERT INTO coin_transactions (
    user_id,
    amount,
    transaction_type,
    description
  ) VALUES (
    new.id,
    100,
    'credit',
    'Welcome bonus'
  );

  -- Initialize leaderboard entry
  INSERT INTO leaderboard (
    user_id,
    total_points,
    challenges_completed,
    rank
  ) VALUES (
    new.id,
    0,
    0,
    (SELECT COALESCE(MAX(rank), 0) + 1 FROM leaderboard)
  );

  -- Create subscription based on email
  IF new.email = '<EMAIL>' AND v_premium_plan_id IS NOT NULL THEN
    -- Premium user
    INSERT INTO user_subscriptions (
      user_id,
      plan_id,
      status,
      current_period_start,
      current_period_end,
      payment_method_id
    ) VALUES (
      new.id,
      v_premium_plan_id,
      'active',
      v_current_period_start,
      v_current_period_end,
      'default'
    )
    ON CONFLICT (user_id) DO UPDATE SET
      plan_id = v_premium_plan_id,
      status = 'active',
      current_period_start = v_current_period_start,
      current_period_end = v_current_period_end,
      updated_at = NOW();
  ELSIF v_free_plan_id IS NOT NULL THEN
    -- Free user
    INSERT INTO user_subscriptions (
      user_id,
      plan_id,
      status,
      current_period_start,
      current_period_end,
      payment_method_id
    ) VALUES (
      new.id,
      v_free_plan_id,
      'active',
      v_current_period_start,
      v_current_period_end,
      'default'
    )
    ON CONFLICT (user_id) DO UPDATE SET
      plan_id = v_free_plan_id,
      status = 'active',
      current_period_start = v_current_period_start,
      current_period_end = v_current_period_end,
      updated_at = NOW();
  END IF;

  RETURN new;
END;
$$;

-- Create new trigger for user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user_registration();

-- Ensure RLS policies
DO $$ 
BEGIN
  -- Drop existing policies
  DROP POLICY IF EXISTS "Users can read own subscription" ON user_subscriptions;
  DROP POLICY IF EXISTS "Users can update own subscription" ON user_subscriptions;
  
  -- Create new policies
  CREATE POLICY "Users can read own subscription"
    ON user_subscriptions
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can update own subscription"
    ON user_subscriptions
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);
END $$;