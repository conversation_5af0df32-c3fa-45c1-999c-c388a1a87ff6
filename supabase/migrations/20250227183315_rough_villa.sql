/*
  # Fix user subscriptions RLS policies

  1. New Policies
    - Add policies for user_subscriptions table
    - Fix RLS for subscription management
  
  2. Security
    - Enable RLS on user_subscriptions
    - Add proper policies for authenticated users
*/

-- Drop existing policies if they exist
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can read own subscription" ON user_subscriptions;
  DROP POLICY IF EXISTS "Users can insert own subscription" ON user_subscriptions;
  DROP POLICY IF EXISTS "Users can update own subscription" ON user_subscriptions;
EXCEPTION
  WHEN undefined_object THEN null;
END $$;

-- Make sure RLS is enabled
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

-- Create new policies with unique names
CREATE POLICY "user_subscriptions_select_20250226"
  ON user_subscriptions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "user_subscriptions_insert_20250226"
  ON user_subscriptions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_subscriptions_update_20250226"
  ON user_subscriptions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- <PERSON>reate function to ensure user has a subscription
CREATE OR REPLACE FUNCTION ensure_user_subscription_20250226(p_user_id uuid)
RETURNS void AS $$
DECLARE
  basic_plan_id uuid;
BEGIN
  -- Get the Basic plan ID
  SELECT id INTO basic_plan_id FROM subscription_plans WHERE name = 'Basic' LIMIT 1;
  
  -- If no Basic plan exists, exit
  IF basic_plan_id IS NULL THEN
    RETURN;
  END IF;
  
  -- Create a basic subscription if none exists
  INSERT INTO user_subscriptions (
    user_id,
    plan_id,
    status,
    current_period_start,
    current_period_end,
    payment_method_id
  )
  VALUES (
    p_user_id,
    basic_plan_id,
    'active',
    now(),
    now() + interval '30 days',
    'default'
  )
  ON CONFLICT (user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Initialize subscriptions for existing users
DO $$
DECLARE
  user_record RECORD;
  basic_plan_id uuid;
BEGIN
  -- Get the Basic plan ID
  SELECT id INTO basic_plan_id FROM subscription_plans WHERE name = 'Basic' LIMIT 1;
  
  -- If no Basic plan exists, exit
  IF basic_plan_id IS NULL THEN
    RETURN;
  END IF;
  
  -- For each user without a subscription, create a Basic one
  FOR user_record IN 
    SELECT id FROM auth.users 
    WHERE id NOT IN (SELECT user_id FROM user_subscriptions)
  LOOP
    PERFORM ensure_user_subscription_20250226(user_record.id);
  END LOOP;
END $$;