-- Drop existing functions first to avoid conflicts
DROP FUNCTION IF EXISTS search_ai_responses(text);
DROP FUNCTION IF EXISTS search_ai_responses(text, text, text);
DROP FUNCTION IF EXISTS search_ai_responses_v2(text, text, text);
DROP FUNCTION IF EXISTS search_ai_responses_v3(text, text, text);

-- <PERSON><PERSON> improved search function with better matching
CREATE OR REPLACE FUNCTION search_ai_responses_v4(
  search_term TEXT,
  search_domain TEXT DEFAULT NULL,
  search_language TEXT DEFAULT 'english'
)
RETURNS TABLE (
  id UUID,
  keyword TEXT,
  content TEXT,
  category TEXT,
  domain TEXT,
  language TEXT,
  similarity DOUBLE PRECISION
) AS $$
BEGIN
  -- First try exact match
  RETURN QUERY
  SELECT 
    ar.id,
    ar.keyword,
    ar.content,
    ar.category,
    ar.domain,
    ar.language,
    1.0::double precision AS similarity
  FROM 
    ai_responses ar
  WHERE 
    LOWER(ar.keyword) = LOWER(search_term)
    AND (search_domain IS NULL OR ar.domain = search_domain)
    AND ar.language = search_language
  LIMIT 1;

  -- If no exact match, try fuzzy search with better matching
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      ar.id,
      ar.keyword,
      ar.content,
      ar.category,
      ar.domain,
      ar.language,
      similarity(ar.keyword, search_term)::double precision AS similarity
    FROM 
      ai_responses ar
    WHERE 
      (ar.keyword % search_term OR 
       ar.keyword ILIKE '%' || search_term || '%' OR
       search_term ILIKE '%' || ar.keyword || '%')
      AND (search_domain IS NULL OR ar.domain = search_domain)
      AND ar.language = search_language
    ORDER BY 
      similarity DESC,
      LENGTH(ar.keyword) ASC  -- Prefer shorter, more precise matches
    LIMIT 1;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Add SQL Injection related responses
INSERT INTO ai_responses (keyword, content, category, domain, language)
VALUES 
('explain sql injection', 'SQL Injection is a critical web security vulnerability that allows attackers to manipulate database queries by injecting malicious SQL code.

Key Concepts:
1. Attack Mechanism
   • Exploits improper input validation
   • Modifies SQL query structure
   • Bypasses security controls

2. Common Techniques
   • Union-based injection
   • Error-based injection
   • Boolean-based injection
   • Time-based injection
   • Out-of-band injection

3. Prevention Methods
   • Use parameterized queries
   • Input validation and sanitization
   • Escape special characters
   • Principle of least privilege
   • WAF implementation

4. Impact
   • Data theft
   • Authentication bypass
   • Data manipulation
   • System compromise

5. Detection
   • Web application firewalls
   • Log monitoring
   • Security testing
   • Code review', 'technical', 'TECHNICAL', 'english'),

('sql injection prevention', 'Best practices for preventing SQL Injection attacks:

1. Parameterized Queries
   • Use prepared statements
   • Bind variables
   • ORM frameworks
   • Stored procedures

2. Input Validation
   • Whitelist validation
   • Type checking
   • Length limits
   • Format validation

3. Security Controls
   • WAF configuration
   • Database hardening
   • Least privilege access
   • Regular updates

4. Code Examples
   • Node.js/PostgreSQL:
     - Use $1, $2 placeholders
     - Never concatenate strings
   • Python/SQLAlchemy:
     - Use ORM methods
     - Avoid raw queries
   • Java/JDBC:
     - PreparedStatement
     - Avoid Statement class

5. Testing
   • Automated scanning
   • Penetration testing
   • Code review
   • Security audits', 'technical', 'TECHNICAL', 'english'),

('sql injection examples', 'Common SQL Injection attack examples and scenarios:

1. Authentication Bypass
   • Input: '' OR ''1''=''1
   • Effect: Always true condition
   • Impact: Unauthorized access

2. Union-Based
   • Input: '' UNION SELECT username,password FROM users--
   • Effect: Combines queries
   • Impact: Data exposure

3. Error-Based
   • Input: '' AND 1=CONVERT(int,(SELECT @@version))--
   • Effect: Forces error messages
   • Impact: Information disclosure

4. Blind SQL Injection
   • Input: '' AND 1=(SELECT CASE WHEN (1=1) THEN 1 ELSE 0 END)--
   • Effect: Boolean/time-based inference
   • Impact: Data extraction

5. Defense Examples
   • Original: "SELECT * FROM users WHERE username=''" + username + "''"
   • Secure: "SELECT * FROM users WHERE username=$1"
   • Parameters: [username]

WARNING: Use these examples for learning and testing only in authorized environments!', 'technical', 'TECHNICAL', 'english')

ON CONFLICT (keyword) DO UPDATE 
SET 
  content = EXCLUDED.content,
  category = EXCLUDED.category,
  domain = EXCLUDED.domain,
  language = EXCLUDED.language;

-- Create better indexes for search
DROP INDEX IF EXISTS ai_responses_keyword_trgm_idx;
CREATE INDEX ai_responses_keyword_trgm_idx ON ai_responses USING gin (keyword gin_trgm_ops);

DROP INDEX IF EXISTS ai_responses_content_trgm_idx;
CREATE INDEX ai_responses_content_trgm_idx ON ai_responses USING gin (content gin_trgm_ops);