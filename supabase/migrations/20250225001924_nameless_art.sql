/*
  # Fix RLS Policies for User Coins

  1. Changes
    - Drop existing RLS policies
    - Add proper RLS policies for user_coins table
    - Add insert policy for initialization
    - Add function to ensure user coins exist

  2. Security
    - Enable RLS
    - Add policies for authenticated users
    - Secure initialization process
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Users can read own coins" ON user_coins;
DROP POLICY IF EXISTS "Users can read own coin balance" ON user_coins;
DROP POLICY IF EXISTS "System can manage coins" ON user_coins;

-- Ensure RLS is enabled
ALTER TABLE user_coins ENABLE ROW LEVEL SECURITY;

-- Add new policies with unique names
CREATE POLICY "user_coins_select_20250225"
  ON user_coins
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "user_coins_insert_20250225"
  ON user_coins
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_coins_update_20250225"
  ON user_coins
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Function to ensure user coins exist
CREATE OR REPLACE FUNCTION ensure_user_coins_20250225(p_user_id uuid)
RETURNS void AS $$
BEGIN
  INSERT INTO user_coins (user_id, balance)
  VALUES (p_user_id, 100)
  ON CONFLICT (user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user coins safely
CREATE OR REPLACE FUNCTION get_user_coins_20250225(p_user_id uuid)
RETURNS TABLE (
  balance integer,
  last_transaction_at timestamptz
) AS $$
BEGIN
  -- Ensure coins exist
  PERFORM ensure_user_coins_20250225(p_user_id);
  
  -- Return coins data
  RETURN QUERY
  SELECT uc.balance, uc.last_transaction_at
  FROM user_coins uc
  WHERE uc.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Initialize coins for existing users
DO $$
DECLARE
  user_record RECORD;
BEGIN
  FOR user_record IN SELECT id FROM auth.users
  LOOP
    PERFORM ensure_user_coins_20250225(user_record.id);
  END LOOP;
END $$;