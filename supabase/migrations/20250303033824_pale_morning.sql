-- Ensure unique constraint on user_subscriptions
DO $$ 
BEGIN
  -- Drop existing unique constraint if it exists
  ALTER TABLE user_subscriptions DROP CONSTRAINT IF EXISTS user_subscriptions_user_id_key;
  
  -- Add unique constraint
  ALTER TABLE user_subscriptions ADD CONSTRAINT user_subscriptions_user_id_key UNIQUE (user_id);
END $$;

-- Update ensure_premium_subscription function to handle conflicts better
CREATE OR REPLACE FUNCTION ensure_premium_subscription(
  p_user_id UUID,
  p_plan_id UUID
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_period_start TIMESTAMPTZ;
  v_current_period_end TIMESTAMPTZ;
BEGIN
  -- Set subscription period
  v_current_period_start := NOW();
  v_current_period_end := v_current_period_start + INTERVAL '30 days';

  -- Try to update existing subscription first
  UPDATE user_subscriptions
  SET
    plan_id = p_plan_id,
    status = 'active',
    current_period_start = v_current_period_start,
    current_period_end = v_current_period_end,
    updated_at = NOW()
  WHERE user_id = p_user_id;
  
  -- If no rows were updated, insert new subscription
  IF NOT FOUND THEN
    INSERT INTO user_subscriptions (
      user_id,
      plan_id,
      status,
      current_period_start,
      current_period_end,
      payment_method_id
    )
    VALUES (
      p_user_id,
      p_plan_id,
      'active',
      v_current_period_start,
      v_current_period_end,
      'default'
    )
    ON CONFLICT (user_id) DO UPDATE
    SET
      plan_id = EXCLUDED.plan_id,
      status = EXCLUDED.status,
      current_period_start = EXCLUDED.current_period_start,
      current_period_end = EXCLUDED.current_period_end,
      updated_at = NOW();
  END IF;
END;
$$;