-- Drop trigger first to remove dependency
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Drop existing function
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create improved function to initialize user data
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Create user profile
  INSERT INTO public.users (
    id,
    email,
    username,
    full_name
  ) VALUES (
    new.id,
    new.email,
    new.raw_user_meta_data->>'username',
    new.raw_user_meta_data->>'full_name'
  );

  -- Initialize user coins with starting balance
  INSERT INTO public.user_coins (
    user_id,
    balance,
    last_transaction_at
  ) VALUES (
    new.id,
    100, -- Give users 100 coins to start
    now()
  );

  -- Create initial coin transaction
  INSERT INTO public.coin_transactions (
    user_id,
    amount,
    transaction_type,
    description
  ) VALUES (
    new.id,
    100,
    'credit',
    'Welcome bonus'
  );

  -- Initialize leaderboard entry
  INSERT INTO public.leaderboard (
    user_id,
    total_points,
    challenges_completed,
    rank
  ) VALUES (
    new.id,
    0,
    0,
    (SELECT COALESCE(MAX(rank), 0) + 1 FROM public.leaderboard)
  );

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to get complete user profile
CREATE OR REPLACE FUNCTION get_user_profile(user_id uuid)
RETURNS json AS $$
DECLARE
  profile json;
BEGIN
  SELECT json_build_object(
    'id', u.id,
    'email', u.email,
    'username', u.username,
    'full_name', u.full_name,
    'avatar_url', u.avatar_url,
    'challenges_completed', COALESCE(
      (SELECT COUNT(*) 
       FROM challenge_submissions cs 
       WHERE cs.user_id = u.id AND cs.status = 'completed'),
      0
    ),
    'coins', COALESCE(
      (SELECT balance 
       FROM user_coins uc 
       WHERE uc.user_id = u.id),
      0
    ),
    'rank', COALESCE(
      (SELECT rank 
       FROM leaderboard l 
       WHERE l.user_id = u.id),
      0
    )
  )
  INTO profile
  FROM users u
  WHERE u.id = user_id;

  RETURN profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add missing RLS policies
DO $$ 
BEGIN
  -- Users table policies
  DROP POLICY IF EXISTS "Users can read own data" ON users;
  DROP POLICY IF EXISTS "Users can update own data" ON users;
  
  CREATE POLICY "Users can read own data"
    ON users
    FOR SELECT
    TO authenticated
    USING (auth.uid() = id);

  CREATE POLICY "Users can update own data"
    ON users
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = id);

  -- User coins policies
  DROP POLICY IF EXISTS "Users can read own coins" ON user_coins;
  
  CREATE POLICY "Users can read own coins"
    ON user_coins
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

  -- Coin transactions policies
  DROP POLICY IF EXISTS "Users can read own transactions" ON coin_transactions;
  
  CREATE POLICY "Users can read own transactions"
    ON coin_transactions
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);
END $$;