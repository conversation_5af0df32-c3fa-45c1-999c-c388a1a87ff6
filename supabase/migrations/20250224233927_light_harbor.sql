/*
  # Add Physical Products Support

  1. New Tables
    - `product_categories`
      - `id` (uuid, primary key)
      - `name` (text)
      - `slug` (text)
      - `description` (text)
      - `parent_id` (uuid, self-reference)

    - `product_variants`
      - `id` (uuid, primary key)
      - `product_id` (uuid, references products)
      - `sku` (text)
      - `size` (text)
      - `color` (text)
      - `stock` (integer)
      - `price` (decimal)

    - `shipping_addresses`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references users)
      - `full_name` (text)
      - `address_line1` (text)
      - `address_line2` (text)
      - `city` (text)
      - `state` (text)
      - `postal_code` (text)
      - `country` (text)
      - `phone` (text)

  2. Security
    - Enable RLS on all new tables
    - Add appropriate policies
*/

-- Product Categories Table
CREATE TABLE IF NOT EXISTS product_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  slug text NOT NULL UNIQUE,
  description text,
  parent_id uuid REFERENCES product_categories(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read product categories"
  ON product_categories
  FOR SELECT
  TO public
  USING (true);

-- Add physical product columns to products table
ALTER TABLE products 
  ADD COLUMN IF NOT EXISTS category_id uuid REFERENCES product_categories(id),
  ADD COLUMN IF NOT EXISTS is_physical boolean DEFAULT false,
  ADD COLUMN IF NOT EXISTS weight decimal(10,2),
  ADD COLUMN IF NOT EXISTS dimensions jsonb;

-- Product Variants Table
CREATE TABLE IF NOT EXISTS product_variants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id) ON DELETE CASCADE,
  sku text NOT NULL UNIQUE,
  size text,
  color text,
  stock integer NOT NULL DEFAULT 0,
  price decimal(10,2) NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE product_variants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read product variants"
  ON product_variants
  FOR SELECT
  TO public
  USING (true);

-- Shipping Addresses Table
CREATE TABLE IF NOT EXISTS shipping_addresses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  full_name text NOT NULL,
  address_line1 text NOT NULL,
  address_line2 text,
  city text NOT NULL,
  state text NOT NULL,
  postal_code text NOT NULL,
  country text NOT NULL,
  phone text,
  is_default boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE shipping_addresses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own addresses"
  ON shipping_addresses
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own addresses"
  ON shipping_addresses
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own addresses"
  ON shipping_addresses
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own addresses"
  ON shipping_addresses
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Add shipping details to orders table
ALTER TABLE orders
  ADD COLUMN IF NOT EXISTS shipping_address_id uuid REFERENCES shipping_addresses(id),
  ADD COLUMN IF NOT EXISTS shipping_method text,
  ADD COLUMN IF NOT EXISTS shipping_cost decimal(10,2),
  ADD COLUMN IF NOT EXISTS tracking_number text,
  ADD COLUMN IF NOT EXISTS estimated_delivery timestamptz;

-- Add variant support to order_items
ALTER TABLE order_items
  ADD COLUMN IF NOT EXISTS variant_id uuid REFERENCES product_variants(id);

-- Insert product categories
INSERT INTO product_categories (name, slug, description) VALUES
('Apparel', 'apparel', 'T-shirts, hoodies, and other clothing items'),
('Accessories', 'accessories', 'Stickers, pins, and other accessories'),
('Digital', 'digital', 'Digital products and subscriptions');

-- Insert sample physical products
DO $$ 
DECLARE 
  apparel_id uuid;
  accessories_id uuid;
  tshirt_id uuid;
  hoodie_id uuid;
BEGIN
  -- Get category IDs
  SELECT id INTO apparel_id FROM product_categories WHERE slug = 'apparel';
  SELECT id INTO accessories_id FROM product_categories WHERE slug = 'accessories';

  -- Insert T-shirt
  INSERT INTO products (
    name, 
    description, 
    price,
    category_id,
    is_physical,
    weight,
    dimensions,
    stock,
    image_url,
    category
  ) VALUES (
    'Hacker Elite T-Shirt',
    'Premium cotton t-shirt with XCerberus logo',
    29.99,
    apparel_id,
    true,
    0.2,
    '{"length": 30, "width": 20, "height": 2}',
    100,
    'https://example.com/tshirt.png',
    'Apparel'
  ) RETURNING id INTO tshirt_id;

  -- Insert Hoodie
  INSERT INTO products (
    name, 
    description, 
    price,
    category_id,
    is_physical,
    weight,
    dimensions,
    stock,
    image_url,
    category
  ) VALUES (
    'XCerberus Hoodie',
    'Comfortable hoodie with embroidered logo',
    59.99,
    apparel_id,
    true,
    0.5,
    '{"length": 35, "width": 25, "height": 3}',
    50,
    'https://example.com/hoodie.png',
    'Apparel'
  ) RETURNING id INTO hoodie_id;

  -- Insert Sticker Pack
  INSERT INTO products (
    name, 
    description, 
    price,
    category_id,
    is_physical,
    weight,
    dimensions,
    stock,
    image_url,
    category
  ) VALUES (
    'Hacker Sticker Pack',
    'Set of 5 high-quality vinyl stickers',
    9.99,
    accessories_id,
    true,
    0.05,
    '{"length": 15, "width": 10, "height": 0.1}',
    200,
    'https://example.com/stickers.png',
    'Accessories'
  );

  -- Insert T-shirt variants
  INSERT INTO product_variants (product_id, sku, size, color, stock, price)
  SELECT 
    tshirt_id as product_id,
    'TSH-BLK-' || size as sku,
    size,
    'Black',
    20,
    29.99
  FROM unnest(ARRAY['S', 'M', 'L', 'XL']) as size;

  -- Insert Hoodie variants
  INSERT INTO product_variants (product_id, sku, size, color, stock, price)
  SELECT 
    hoodie_id as product_id,
    'HOD-' || color || '-' || size as sku,
    size,
    color,
    10,
    59.99
  FROM unnest(ARRAY['S', 'M', 'L', 'XL']) as size
  CROSS JOIN unnest(ARRAY['Black', 'Navy', 'Gray']) as color;
END $$;