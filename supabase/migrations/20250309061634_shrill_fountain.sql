/*
  Create OS Simulator Sessions Table

  1. New Tables
    - os_simulator_sessions
      - id (uuid, primary key)
      - user_id (uuid, references auth.users)
      - topic (text)
      - state (jsonb)
      - created_at (timestamptz)
      - updated_at (timestamptz)

  2. Security
    - Enable RLS
    - Add policies for authenticated users
*/

-- Create the sessions table
CREATE TABLE IF NOT EXISTS os_simulator_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users ON DELETE CASCADE,
  topic text NOT NULL,
  state jsonb NOT NULL DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE os_simulator_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can read own sessions"
  ON os_simulator_sessions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sessions"
  ON os_simulator_sessions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions"
  ON os_simulator_sessions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create index for faster lookups
CREATE INDEX os_simulator_sessions_user_topic_idx ON os_simulator_sessions (user_id, topic);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_os_simulator_sessions_updated_at
  BEFORE UPDATE
  ON os_simulator_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();