-- Add domain field to ai_responses table
ALTER TABLE ai_responses ADD COLUMN IF NOT EXISTS domain text;

-- Update existing responses with domains
UPDATE ai_responses
SET domain = 
  CASE 
    WHEN category ILIKE '%governance%' OR category ILIKE '%compliance%' OR category ILIKE '%risk%' 
    THEN 'GOVERNANCE'
    WHEN category ILIKE '%incident%' OR category ILIKE '%response%' OR category ILIKE '%forensic%'
    THEN 'INCIDENT_RESPONSE'
    WHEN category ILIKE '%trend%' OR category ILIKE '%news%' OR category ILIKE '%update%'
    THEN 'LATEST_TRENDS'
    ELSE 'TECHNICAL'
  END
WHERE domain IS NULL;

-- Create index on domain for better query performance
CREATE INDEX IF NOT EXISTS ai_responses_domain_idx ON ai_responses (domain);

-- Create combined index for common queries
CREATE INDEX IF NOT EXISTS ai_responses_domain_lang_idx ON ai_responses (domain, language);

-- Fix the search_ai_responses function
CREATE OR REPLACE FUNCTION search_ai_responses_v2(
  search_term TEXT,
  search_domain TEXT DEFAULT NULL,
  search_language TEXT DEFAULT 'english'
)
RETURNS TABLE (
  id UUID,
  keyword TEXT,
  content TEXT,
  category TEXT,
  domain TEXT,
  language TEXT,
  similarity DOUBLE PRECISION
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ar.id,
    ar.keyword,
    ar.content,
    ar.category,
    ar.domain,
    ar.language,
    similarity(ar.keyword, search_term)::double precision AS similarity
  FROM 
    ai_responses ar
  WHERE 
    ar.keyword % search_term
    AND (search_domain IS NULL OR ar.domain = search_domain)
    AND ar.language = search_language
  ORDER BY 
    similarity DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;