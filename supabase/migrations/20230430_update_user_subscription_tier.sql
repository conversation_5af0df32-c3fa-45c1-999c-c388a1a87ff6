-- Function to update a user's subscription tier
CREATE OR <PERSON>EPLACE FUNCTION update_user_subscription_tier(p_user_id UUID, p_tier TEXT)
RETURNS VOID AS $$
DECLARE
  v_subscription_id UUID;
  v_subscription_exists BOOLEAN;
BEGIN
  -- Check if the user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User with ID % does not exist', p_user_id;
  END IF;
  
  -- Check if the tier is valid
  IF p_tier NOT IN ('free', 'premium', 'business') THEN
    RAISE EXCEPTION 'Invalid subscription tier: %', p_tier;
  END IF;
  
  -- Check if the user has a subscription in the subscriptions table
  SELECT EXISTS (
    SELECT 1 FROM subscriptions WHERE user_id = p_user_id
  ) INTO v_subscription_exists;
  
  IF v_subscription_exists THEN
    -- Update existing subscription
    UPDATE subscriptions
    SET 
      tier = p_tier,
      updated_at = NOW()
    WHERE user_id = p_user_id;
  ELSE
    -- Create new subscription
    INSERT INTO subscriptions (
      user_id,
      tier,
      start_date,
      is_active,
      created_at,
      updated_at
    ) VALUES (
      p_user_id,
      p_tier,
      NOW(),
      TRUE,
      NOW(),
      NOW()
    );
  END IF;
  
  -- Update the profile's subscription_tier field
  UPDATE profiles
  SET 
    subscription_tier = p_tier,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Add a record to subscription_history for auditing
  INSERT INTO subscription_history (
    user_id,
    previous_tier,
    new_tier,
    changed_by,
    changed_at
  ) VALUES (
    p_user_id,
    (SELECT tier FROM subscriptions WHERE user_id = p_user_id),
    p_tier,
    auth.uid(),
    NOW()
  );
  
  -- If upgrading to premium or business, add some bonus coins
  IF p_tier = 'premium' THEN
    UPDATE profiles
    SET coins = coins + 500
    WHERE id = p_user_id;
  ELSIF p_tier = 'business' THEN
    UPDATE profiles
    SET coins = coins + 1000
    WHERE id = p_user_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
