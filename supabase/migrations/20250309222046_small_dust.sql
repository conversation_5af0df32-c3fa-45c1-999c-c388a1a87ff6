/*
  # Add RLS policies for leaderboard table

  1. Security Changes
    - Enable RLS on leaderboard table
    - Add policies for:
      - Public read access to leaderboard
      - Authenticated users can update their own entries
      - System can insert/update any entries
      - Prevent direct deletions

  2. Changes
    - Enable RLS
    - Add SELECT policy for public access
    - Add INSERT/UPDATE policies for authenticated users
    - Add trigger to handle point updates
*/

-- Enable RLS
ALTER TABLE public.leaderboard ENABLE ROW LEVEL SECURITY;

-- Allow public read access to all leaderboard entries
CREATE POLICY "Anyone can view leaderboard" ON public.leaderboard
  FOR SELECT
  TO public
  USING (true);

-- Allow authenticated users to update their own entries
CREATE POLICY "Users can update own leaderboard entry" ON public.leaderboard
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Allow authenticated users to insert their own entries
CREATE POLICY "Users can insert own leaderboard entry" ON public.leaderboard
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- <PERSON>reate function to update leaderboard on challenge completion
CREATE OR REPLACE FUNCTION update_leaderboard()
RETURNS TRIGGER AS $$
BEGIN
  -- Update or insert leaderboard entry
  INSERT INTO public.leaderboard (user_id, total_points, challenges_completed)
  VALUES (
    NEW.user_id,
    COALESCE(NEW.points_earned, 0),
    1
  )
  ON CONFLICT (user_id)
  DO UPDATE SET
    total_points = leaderboard.total_points + COALESCE(NEW.points_earned, 0),
    challenges_completed = leaderboard.challenges_completed + 1,
    updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;