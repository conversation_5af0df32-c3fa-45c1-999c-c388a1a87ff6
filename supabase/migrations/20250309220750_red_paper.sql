/*
  # Challenge Analytics System

  1. New Tables
    - challenge_attempts
      - Tracks individual attempts at challenges
      - Stores payload, result, and timing data
    - challenge_analytics
      - Stores aggregated analytics and patterns
      - Used for AI analysis and recommendations
    - challenge_feedback
      - Stores user feedback and ratings
      - Used to improve challenge quality

  2. Security
    - Enable RLS on all tables
    - Add policies for secure access
    - Protect sensitive data

  3. Changes
    - Add analytics tracking
    - Add real-time monitoring
    - Add AI feedback system
*/

-- Challenge attempts tracking
CREATE TABLE IF NOT EXISTS challenge_attempts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  challenge_id uuid REFERENCES challenges(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  payload text NOT NULL,
  result jsonb,
  execution_time integer, -- in milliseconds
  memory_used integer,   -- in bytes
  cpu_usage float,       -- percentage
  status text NOT NULL CHECK (status IN ('success', 'failure', 'error')),
  error_message text,
  created_at timestamptz DEFAULT now(),
  
  -- Constraints
  CONSTRAINT valid_execution_time CHECK (execution_time >= 0),
  CONSTRAINT valid_memory_used CHECK (memory_used >= 0),
  CONSTRAINT valid_cpu_usage CHECK (cpu_usage >= 0 AND cpu_usage <= 100)
);

-- Challenge analytics
CREATE TABLE IF NOT EXISTS challenge_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  challenge_id uuid REFERENCES challenges(id) ON DELETE CASCADE,
  total_attempts integer DEFAULT 0,
  successful_attempts integer DEFAULT 0,
  average_completion_time integer, -- in milliseconds
  difficulty_rating float,
  success_rate float,
  popular_techniques jsonb,
  common_mistakes jsonb,
  updated_at timestamptz DEFAULT now(),
  
  -- Constraints
  CONSTRAINT valid_attempts CHECK (total_attempts >= 0),
  CONSTRAINT valid_successful_attempts CHECK (successful_attempts >= 0),
  CONSTRAINT valid_completion_time CHECK (average_completion_time >= 0),
  CONSTRAINT valid_difficulty CHECK (difficulty_rating >= 0 AND difficulty_rating <= 10),
  CONSTRAINT valid_success_rate CHECK (success_rate >= 0 AND success_rate <= 100)
);

-- Challenge feedback
CREATE TABLE IF NOT EXISTS challenge_feedback (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  challenge_id uuid REFERENCES challenges(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  rating integer CHECK (rating >= 1 AND rating <= 5),
  difficulty_rating integer CHECK (difficulty_rating >= 1 AND difficulty_rating <= 5),
  feedback_text text,
  created_at timestamptz DEFAULT now(),
  
  -- Unique constraint to prevent multiple ratings
  UNIQUE(challenge_id, user_id)
);

-- Enable RLS
ALTER TABLE challenge_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE challenge_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE challenge_feedback ENABLE ROW LEVEL SECURITY;

-- Policies for challenge_attempts
CREATE POLICY "Users can insert their own attempts"
  ON challenge_attempts
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own attempts"
  ON challenge_attempts
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Policies for challenge_analytics
CREATE POLICY "Anyone can view challenge analytics"
  ON challenge_analytics
  FOR SELECT
  TO public
  USING (true);

-- Policies for challenge_feedback
CREATE POLICY "Users can insert their own feedback"
  ON challenge_feedback
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view all feedback"
  ON challenge_feedback
  FOR SELECT
  TO public
  USING (true);

-- Function to update challenge analytics
CREATE OR REPLACE FUNCTION update_challenge_analytics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update analytics when a new attempt is made
  INSERT INTO challenge_analytics (
    challenge_id,
    total_attempts,
    successful_attempts,
    average_completion_time,
    success_rate
  )
  SELECT
    NEW.challenge_id,
    COUNT(*),
    COUNT(*) FILTER (WHERE status = 'success'),
    AVG(execution_time) FILTER (WHERE status = 'success'),
    (COUNT(*) FILTER (WHERE status = 'success')::float / COUNT(*)::float) * 100
  FROM challenge_attempts
  WHERE challenge_id = NEW.challenge_id
  ON CONFLICT (challenge_id)
  DO UPDATE SET
    total_attempts = EXCLUDED.total_attempts,
    successful_attempts = EXCLUDED.successful_attempts,
    average_completion_time = EXCLUDED.average_completion_time,
    success_rate = EXCLUDED.success_rate,
    updated_at = now();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update analytics
CREATE TRIGGER update_challenge_analytics_trigger
  AFTER INSERT OR UPDATE ON challenge_attempts
  FOR EACH ROW
  EXECUTE FUNCTION update_challenge_analytics();

-- Function to calculate challenge difficulty
CREATE OR REPLACE FUNCTION calculate_challenge_difficulty()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE challenge_analytics
  SET difficulty_rating = (
    SELECT
      CASE
        WHEN success_rate >= 80 THEN 1
        WHEN success_rate >= 60 THEN 2
        WHEN success_rate >= 40 THEN 3
        WHEN success_rate >= 20 THEN 4
        ELSE 5
      END +
      CASE
        WHEN average_completion_time >= 3600000 THEN 5  -- More than 1 hour
        WHEN average_completion_time >= 1800000 THEN 4  -- More than 30 minutes
        WHEN average_completion_time >= 900000 THEN 3   -- More than 15 minutes
        WHEN average_completion_time >= 300000 THEN 2   -- More than 5 minutes
        ELSE 1
      END
  ) / 2
  WHERE challenge_id = NEW.challenge_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update difficulty
CREATE TRIGGER update_challenge_difficulty_trigger
  AFTER UPDATE ON challenge_analytics
  FOR EACH ROW
  EXECUTE FUNCTION calculate_challenge_difficulty();