/*
  # Subscription System Schema

  1. New Tables
    - subscriptions
      - Tracks user subscription details
      - Handles subscription status and history
    - subscription_features
      - Defines features available for each tier
    - subscription_usage
      - Tracks feature usage per user
  
  2. Security
    - Enable RLS on all tables
    - Add policies for subscription access
*/

-- Subscription Plans Table
CREATE TABLE subscription_plans (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  price decimal(10,2) NOT NULL,
  interval text NOT NULL,
  features jsonb NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can read subscription plans"
  ON subscription_plans FOR SELECT
  TO public
  USING (true);

-- User Subscriptions Table
CREATE TABLE user_subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id uuid REFERENCES subscription_plans(id),
  status text NOT NULL DEFAULT 'inactive',
  current_period_start timestamptz,
  current_period_end timestamptz,
  cancel_at_period_end boolean DEFAULT false,
  payment_method_id text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id)
);

ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can read own subscription"
  ON user_subscriptions FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Subscription Usage Table
CREATE TABLE subscription_usage (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  feature text NOT NULL,
  usage_count integer DEFAULT 0,
  last_used_at timestamptz DEFAULT now(),
  reset_at timestamptz,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, feature)
);

ALTER TABLE subscription_usage ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can read own usage"
  ON subscription_usage FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Insert default subscription plans
INSERT INTO subscription_plans (name, description, price, interval, features) VALUES
(
  'Basic',
  'Perfect for beginners',
  199.00,
  'month',
  '{
    "labs": 10,
    "challenges": 5,
    "support": "community",
    "tools": ["basic-lab", "community-forum"]
  }'
),
(
  'Premium',
  'For dedicated learners',
  399.00,
  'month',
  '{
    "labs": -1,
    "challenges": -1,
    "support": "priority",
    "tools": ["advanced-lab", "attack-box", "private-labs", "community-forum"]
  }'
),
(
  'Business',
  'Enterprise-grade solution',
  999.00,
  'month',
  '{
    "labs": -1,
    "challenges": -1,
    "support": "dedicated",
    "tools": ["advanced-lab", "attack-box", "private-labs", "team-management", "api-access", "custom-labs"]
  }'
);