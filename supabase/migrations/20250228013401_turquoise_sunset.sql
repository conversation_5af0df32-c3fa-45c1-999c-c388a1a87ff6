/*
  # Cybersecurity AI Updates
  
  1. Updates
    - Add language field to ai_responses table
    - Create index on keyword field for better search performance
    - Fix text search functionality
    
  2. Security
    - Ensure RLS policies are in place
*/

-- Add language field to ai_responses table if it doesn't exist
ALTER TABLE ai_responses ADD COLUMN IF NOT EXISTS language text DEFAULT 'english';

-- Create index on keyword field for better search performance
CREATE INDEX IF NOT EXISTS ai_responses_keyword_idx ON ai_responses (keyword);

-- Create trigram index for better fuzzy search
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE INDEX IF NOT EXISTS ai_responses_keyword_trgm_idx ON ai_responses USING gin (keyword gin_trgm_ops);

-- Ensure RLS is enabled
ALTER TABLE ai_responses ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "ai_responses_select_20250225_v2" ON ai_responses;
  DROP POLICY IF EXISTS "ai_responses_select_20250228" ON ai_responses;
EXCEPTION
  WHEN undefined_object THEN null;
END $$;

-- Create policies with unique names
CREATE POLICY "ai_responses_select_20250228_v2"
  ON ai_responses
  FOR SELECT
  TO public
  USING (true);

-- Create policy for inserting responses
CREATE POLICY "ai_responses_insert_20250228"
  ON ai_responses
  FOR INSERT
  TO authenticated
  WITH CHECK (true);