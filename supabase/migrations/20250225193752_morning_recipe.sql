/*
  # Game System Tables

  1. New Tables
    - `game_missions` - Stores mission/game definitions
    - `game_phases` - Stores phases for each mission
    - `game_commands` - Stores valid commands and responses
    - `game_progress` - Tracks user progress in games

  2. Security
    - Enable RLS on all tables
    - Add policies for read/write access
*/

-- Create game missions table
CREATE TABLE IF NOT EXISTS game_missions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text NOT NULL,
  difficulty text NOT NULL,
  category text NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create game phases table
CREATE TABLE IF NOT EXISTS game_phases (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  mission_id uuid REFERENCES game_missions(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text NOT NULL,
  sequence_order integer NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create game commands table
CREATE TABLE IF NOT EXISTS game_commands (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  phase_id uuid REFERENCES game_phases(id) ON DELETE CASCADE,
  command text NOT NULL,
  description text,
  expected_output text NOT NULL,
  hint text,
  sequence_order integer NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create game progress table
CREATE TABLE IF NOT EXISTS game_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  mission_id uuid REFERENCES game_missions(id) ON DELETE CASCADE,
  current_phase integer DEFAULT 0,
  completed_commands jsonb DEFAULT '[]',
  status text DEFAULT 'in_progress',
  started_at timestamptz DEFAULT now(),
  completed_at timestamptz,
  UNIQUE(user_id, mission_id)
);

-- Enable RLS
ALTER TABLE game_missions ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_phases ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_commands ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_progress ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read game missions"
  ON game_missions
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Anyone can read game phases"
  ON game_phases
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Anyone can read game commands"
  ON game_commands
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Users can read own progress"
  ON game_progress
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own progress"
  ON game_progress
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own progress"
  ON game_progress
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Insert initial game data
INSERT INTO game_missions (id, title, description, difficulty, category) VALUES
(
  'f7d92100-d3f1-4d2b-9d1a-b5f0b7e3d5a1',
  'Server Breach',
  'Break into a high-security server by exploiting vulnerabilities and bypassing security measures.',
  'Medium',
  'Infrastructure'
);

-- Insert phases for Server Breach mission
INSERT INTO game_phases (mission_id, name, description, sequence_order) VALUES
(
  'f7d92100-d3f1-4d2b-9d1a-b5f0b7e3d5a1',
  'Reconnaissance',
  'Gather intelligence about the target system',
  1
),
(
  'f7d92100-d3f1-4d2b-9d1a-b5f0b7e3d5a1',
  'Initial Access',
  'Exploit identified vulnerabilities to gain access',
  2
),
(
  'f7d92100-d3f1-4d2b-9d1a-b5f0b7e3d5a1',
  'Privilege Escalation',
  'Escalate privileges to gain administrative access',
  3
),
(
  'f7d92100-d3f1-4d2b-9d1a-b5f0b7e3d5a1',
  'AI Shutdown',
  'Locate and terminate the rogue AI process',
  4
);

-- Insert commands for Reconnaissance phase
WITH recon_phase AS (
  SELECT id FROM game_phases 
  WHERE mission_id = 'f7d92100-d3f1-4d2b-9d1a-b5f0b7e3d5a1' 
  AND name = 'Reconnaissance'
  LIMIT 1
)
INSERT INTO game_commands (phase_id, command, description, expected_output, hint, sequence_order) VALUES
(
  (SELECT id FROM recon_phase),
  'nmap -sV *************',
  'Scan target for open ports and services',
  E'Starting Nmap 7.94 ( https://nmap.org )\nScanning *************\nPORT     STATE  SERVICE        VERSION\n22/tcp   open   ssh           OpenSSH 8.2p1\n80/tcp   open   http          nginx 1.18.0\n443/tcp  open   https         nginx 1.18.0\n3306/tcp open   mysql         MySQL 8.0.28\n8080/tcp open   http-proxy    [FILTERED]\n\nService detection performed. Please wait...\n[!] Interesting: Non-standard SSH configuration detected\n[!] Port 8080 appears to be running custom software',
  E'Try these Nmap commands:\n• nmap -sV *************    (Service version detection)\n• nmap -p- *************    (All ports scan)\n• nmap -A *************     (Aggressive scan)\n\nThe SSH service on port 22 looks misconfigured - this could be our way in.\nThe custom software on port 8080 might be related to the AI system.',
  1
),
(
  (SELECT id FROM recon_phase),
  'nmap -p- *************',
  'Scan all ports on target',
  E'Starting Nmap 7.94 ( https://nmap.org )\nScanning *************\nPORT     STATE  SERVICE\n22/tcp   open   ssh\n80/tcp   open   http\n443/tcp  open   https\n3306/tcp open   mysql\n8080/tcp open   http-proxy\n9001/tcp open   unknown\n\n[!] Hidden service detected on port 9001',
  'The "-p-" flag scans all 65535 ports. Look for unusual or non-standard ports that might be running custom services.',
  2
),
(
  (SELECT id FROM recon_phase),
  'nmap -A *************',
  'Aggressive scan with OS detection',
  E'Starting Nmap 7.94 ( https://nmap.org )\nScanning *************\nOS detection performed. Please wait... \nOS details: Linux 5.4.0-generic\n\nHost script results:\n| ssh-auth-methods: \n|   Supported authentication methods: \n|     publickey\n|_    password\n| ssl-cert: Subject: commonName=cybercorp.local\n\n[!] Potential vulnerability: SSH misconfiguration (CVE-2020-14145)',
  'The "-A" flag enables OS detection, version detection, script scanning, and traceroute. Look for potential vulnerabilities in the detailed output.',
  3
);