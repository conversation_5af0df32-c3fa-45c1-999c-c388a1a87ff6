/*
  # Cybersecurity AI Updates
  
  1. Updates
    - Add language field to ai_responses table
    - Create index on keyword field for better search performance
    
  2. Security
    - Ensure RLS policies are in place
*/

-- Add language field to ai_responses table if it doesn't exist
ALTER TABLE ai_responses ADD COLUMN IF NOT EXISTS language text DEFAULT 'english';

-- Create index on keyword field for better search performance
CREATE INDEX IF NOT EXISTS ai_responses_keyword_idx ON ai_responses (keyword);

-- Create text search index if it doesn't exist
CREATE INDEX IF NOT EXISTS ai_responses_keyword_search_idx ON ai_responses USING gin(to_tsvector('english', keyword));

-- Ensure RLS is enabled
ALTER TABLE ai_responses ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "ai_responses_select_20250225_v2" ON ai_responses;
EXCEPTION
  WHEN undefined_object THEN null;
END $$;

-- Create policies with unique names
CREATE POLICY "ai_responses_select_20250228"
  ON ai_responses
  FOR SELECT
  TO public
  USING (true);