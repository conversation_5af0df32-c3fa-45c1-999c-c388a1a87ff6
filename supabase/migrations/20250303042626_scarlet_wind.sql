-- Drop existing function if it exists
DROP FUNCTION IF EXISTS get_user_subscription(UUID);

-- Create RPC function to get user subscription
CREATE OR REPLACE FUNCTION get_user_subscription(p_user_id UUID)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_subscription jsonb;
  v_free_plan_id UUID;
BEGIN
  -- Get subscription data
  SELECT jsonb_build_object(
    'id', us.id,
    'user_id', us.user_id,
    'plan_id', us.plan_id,
    'status', us.status,
    'current_period_start', us.current_period_start,
    'current_period_end', us.current_period_end,
    'subscription_plan', jsonb_build_object(
      'id', sp.id,
      'name', sp.name,
      'features', sp.features
    )
  )
  INTO v_subscription
  FROM user_subscriptions us
  JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE us.user_id = p_user_id;

  -- If no subscription found, create a free one
  IF v_subscription IS NULL THEN
    -- Get free plan ID
    SELECT id INTO v_free_plan_id
    FROM subscription_plans
    WHERE name = 'Free'
    LIMIT 1;

    IF v_free_plan_id IS NOT NULL THEN
      -- Create free subscription
      INSERT INTO user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        payment_method_id
      )
      VALUES (
        p_user_id,
        v_free_plan_id,
        'active',
        NOW(),
        NOW() + INTERVAL '30 days',
        'default'
      )
      ON CONFLICT (user_id) DO NOTHING
      RETURNING jsonb_build_object(
        'id', id,
        'user_id', user_id,
        'plan_id', plan_id,
        'status', status,
        'current_period_start', current_period_start,
        'current_period_end', current_period_end,
        'subscription_plan', (
          SELECT jsonb_build_object(
            'id', id,
            'name', name,
            'features', features
          )
          FROM subscription_plans
          WHERE id = v_free_plan_id
        )
      )
      INTO v_subscription;
    END IF;
  END IF;

  RETURN v_subscription;
END;
$$;