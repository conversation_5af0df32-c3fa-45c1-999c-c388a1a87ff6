/*
  # Initial Schema Setup for XCerberus Platform

  1. New Tables
    - users
      - Basic user information and authentication
    - challenges
      - Challenge details and configuration
    - challenge_submissions
      - User submissions and progress tracking
    - leaderboard
      - User rankings and points
    - user_coins
      - XCerberus coin balance and transactions

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Users Table
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT auth.uid(),
  email text UNIQUE NOT NULL,
  username text UNIQUE NOT NULL,
  full_name text,
  avatar_url text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own data"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own data"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Challenges Table
CREATE TABLE IF NOT EXISTS challenges (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text NOT NULL,
  difficulty text NOT NULL,
  points integer NOT NULL,
  category text NOT NULL,
  is_open boolean DEFAULT false,
  start_time timestamptz,
  end_time timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE challenges ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read challenges"
  ON challenges
  FOR SELECT
  TO public
  USING (true);

-- Challenge Submissions Table
CREATE TABLE IF NOT EXISTS challenge_submissions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  challenge_id uuid REFERENCES challenges(id) ON DELETE CASCADE,
  status text NOT NULL,
  points_earned integer DEFAULT 0,
  submission_time timestamptz DEFAULT now(),
  completion_time timestamptz,
  UNIQUE(user_id, challenge_id)
);

ALTER TABLE challenge_submissions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own submissions"
  ON challenge_submissions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create submissions"
  ON challenge_submissions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Leaderboard Table
CREATE TABLE IF NOT EXISTS leaderboard (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  total_points integer DEFAULT 0,
  challenges_completed integer DEFAULT 0,
  rank integer,
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id)
);

ALTER TABLE leaderboard ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read leaderboard"
  ON leaderboard
  FOR SELECT
  TO public
  USING (true);

-- User Coins Table
CREATE TABLE IF NOT EXISTS user_coins (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  balance integer DEFAULT 0,
  last_transaction_at timestamptz DEFAULT now(),
  UNIQUE(user_id)
);

ALTER TABLE user_coins ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own coin balance"
  ON user_coins
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Coin Transactions Table
CREATE TABLE IF NOT EXISTS coin_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  amount integer NOT NULL,
  transaction_type text NOT NULL,
  description text,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE coin_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own transactions"
  ON coin_transactions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Functions
CREATE OR REPLACE FUNCTION update_leaderboard()
RETURNS TRIGGER AS $$
BEGIN
  -- Update user's total points and challenges completed
  INSERT INTO leaderboard (user_id, total_points, challenges_completed)
  VALUES (NEW.user_id, NEW.points_earned, 1)
  ON CONFLICT (user_id)
  DO UPDATE SET
    total_points = leaderboard.total_points + NEW.points_earned,
    challenges_completed = leaderboard.challenges_completed + 1,
    updated_at = now();
    
  -- Update ranks
  WITH ranked_users AS (
    SELECT
      id,
      ROW_NUMBER() OVER (ORDER BY total_points DESC) as new_rank
    FROM leaderboard
  )
  UPDATE leaderboard l
  SET rank = r.new_rank
  FROM ranked_users r
  WHERE l.id = r.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_leaderboard_on_submission
  AFTER INSERT ON challenge_submissions
  FOR EACH ROW
  WHEN (NEW.status = 'completed')
  EXECUTE FUNCTION update_leaderboard();

-- Insert two open challenges
INSERT INTO challenges (title, description, difficulty, points, category, is_open, start_time, end_time)
VALUES 
  (
    'Web Security Championship',
    'Test your skills in web security with real-world scenarios. Find and exploit vulnerabilities in a controlled environment.',
    'Medium',
    500,
    'Web Security',
    true,
    now(),
    now() + interval '30 days'
  ),
  (
    'Cryptography Master Challenge',
    'Break complex encryption schemes and secure communication protocols. Put your cryptography skills to the test.',
    'Hard',
    750,
    'Cryptography',
    true,
    now(),
    now() + interval '30 days'
  );