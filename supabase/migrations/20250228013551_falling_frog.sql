/*
  # Fix AI Responses Table
  
  1. Updates
    - Add missing indexes for better search performance
    - Fix text search functionality
    - Add sample responses for common queries
    
  2. Security
    - Ensure RLS policies are in place
*/

-- Add language field to ai_responses table if it doesn't exist
ALTER TABLE ai_responses ADD COLUMN IF NOT EXISTS language text DEFAULT 'english';

-- Create index on keyword field for better search performance
CREATE INDEX IF NOT EXISTS ai_responses_keyword_idx ON ai_responses (keyword);

-- Create trigram index for better fuzzy search
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE INDEX IF NOT EXISTS ai_responses_keyword_trgm_idx ON ai_responses USING gin (keyword gin_trgm_ops);

-- Ensure RLS is enabled
ALTER TABLE ai_responses ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "ai_responses_select_20250225_v2" ON ai_responses;
  DROP POLICY IF EXISTS "ai_responses_select_20250228" ON ai_responses;
  DROP POLICY IF EXISTS "ai_responses_select_20250228_v2" ON ai_responses;
  DROP POLICY IF EXISTS "ai_responses_insert_20250228" ON ai_responses;
EXCEPTION
  WHEN undefined_object THEN null;
END $$;

-- Create policies with unique names
CREATE POLICY "ai_responses_select_20250228_v3"
  ON ai_responses
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "ai_responses_insert_20250228_v2"
  ON ai_responses
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Insert some basic responses for common queries
INSERT INTO ai_responses (keyword, content, category, language)
VALUES 
('hi', 'Hello! I''m your AI assistant. How can I help you with cybersecurity today?', 'general', 'english'),
('hello', 'Hi there! I''m ready to assist with your cybersecurity questions. What would you like to know?', 'general', 'english'),
('help', 'I can help with various cybersecurity topics including network security, web vulnerabilities, cryptography, malware analysis, and more. What specific area are you interested in?', 'general', 'english')
ON CONFLICT (keyword) DO NOTHING;