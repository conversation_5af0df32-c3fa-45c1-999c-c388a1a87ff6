-- Drop existing functions first to avoid conflicts
DROP FUNCTION IF EXISTS search_ai_responses(text);
DROP FUNCTION IF EXISTS search_ai_responses(text, text, text);
DROP FUNCTION IF EXISTS search_ai_responses_v2(text, text, text);

-- <PERSON><PERSON> improved search function with a unique name
CREATE OR REPLACE FUNCTION search_ai_responses_v3(
  search_term TEXT,
  search_domain TEXT DEFAULT NULL,
  search_language TEXT DEFAULT 'english'
)
RETURNS TABLE (
  id UUID,
  keyword TEXT,
  content TEXT,
  category TEXT,
  domain TEXT,
  language TEXT,
  similarity DOUBLE PRECISION
) AS $$
BEGIN
  -- First try exact match
  RETURN QUERY
  SELECT 
    ar.id,
    ar.keyword,
    ar.content,
    ar.category,
    ar.domain,
    ar.language,
    1.0::double precision AS similarity
  FROM 
    ai_responses ar
  WHERE 
    LOWER(ar.keyword) = LOWER(search_term)
    AND (search_domain IS NULL OR ar.domain = search_domain)
    AND ar.language = search_language
  LIMIT 1;

  -- If no exact match, try fuzzy search
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      ar.id,
      ar.keyword,
      ar.content,
      ar.category,
      ar.domain,
      ar.language,
      similarity(ar.keyword, search_term)::double precision AS similarity
    FROM 
      ai_responses ar
    WHERE 
      ar.keyword % search_term
      AND (search_domain IS NULL OR ar.domain = search_domain)
      AND ar.language = search_language
    ORDER BY 
      similarity DESC
    LIMIT 1;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Add SIEM-related responses
INSERT INTO ai_responses (keyword, content, category, domain, language)
VALUES 
('what is siem', 'SIEM (Security Information and Event Management) is a comprehensive security solution that combines:

1. Security Information Management (SIM):
   • Log storage and management
   • Data analysis and reporting
   • Compliance monitoring

2. Security Event Management (SEM):
   • Real-time monitoring
   • Event correlation
   • Security alerts
   • Incident response

Key Features:
• Log Collection and Aggregation
• Real-time Analysis
• Threat Detection
• Automated Alerts
• Compliance Reporting
• Incident Response
• Forensic Analysis

Common Use Cases:
• Security Monitoring
• Threat Detection
• Incident Response
• Compliance Reporting
• Forensic Investigation', 'technical', 'TECHNICAL', 'english'),

('siem tools', 'Popular SIEM Tools and Platforms:

1. Splunk Enterprise Security
   • Industry leader
   • Powerful search capabilities
   • Advanced analytics

2. IBM QRadar
   • AI-powered analytics
   • Threat intelligence
   • Risk management

3. Exabeam
   • User behavior analytics
   • Automated investigation
   • Cloud-native

4. LogRhythm
   • SOAR capabilities
   • Machine learning
   • Automated response

5. Microsoft Sentinel
   • Cloud-native SIEM
   • Azure integration
   • AI-powered', 'technical', 'TECHNICAL', 'english'),

('siem implementation', 'SIEM Implementation Best Practices:

1. Planning Phase
   • Define objectives
   • Identify data sources
   • Plan architecture
   • Set retention policies

2. Deployment Steps
   • Start small, scale gradually
   • Configure data collection
   • Set up correlation rules
   • Establish baselines

3. Optimization
   • Tune alert rules
   • Reduce false positives
   • Automate responses
   • Regular updates

4. Maintenance
   • Monitor performance
   • Update use cases
   • Train analysts
   • Review effectiveness', 'technical', 'TECHNICAL', 'english')

ON CONFLICT (keyword) DO UPDATE 
SET 
  content = EXCLUDED.content,
  category = EXCLUDED.category,
  domain = EXCLUDED.domain,
  language = EXCLUDED.language;