-- Function to update a user's subscription tier
CREATE OR R<PERSON>LACE FUNCTION update_user_subscription_tier(p_user_id UUID, p_tier TEXT)
RETURNS VOID AS $$
BEGIN
  -- Validate the tier
  IF p_tier NOT IN ('free', 'premium', 'business') THEN
    RAISE EXCEPTION 'Invalid subscription tier. Must be one of: free, premium, business';
  END IF;

  -- Update the user's subscription tier
  UPDATE profiles
  SET 
    subscription_tier = p_tier,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Record the change in subscription history
  INSERT INTO subscription_history (user_id, tier, changed_at)
  VALUES (p_user_id, p_tier, NOW());
  
  -- If upgrading to premium or business, update the user's role
  IF p_tier = 'premium' THEN
    UPDATE profiles
    SET role_id = (SELECT id FROM user_roles WHERE name = 'premium_user')
    WHERE id = p_user_id;
  ELSIF p_tier = 'business' THEN
    UPDATE profiles
    SET role_id = (SELECT id FROM user_roles WHERE name = 'business_user')
    WHERE id = p_user_id;
  ELSIF p_tier = 'free' THEN
    UPDATE profiles
    SET role_id = (SELECT id FROM user_roles WHERE name = 'free_user')
    WHERE id = p_user_id;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Example usage:
-- SELECT update_user_subscription_tier('user-uuid-here', 'premium');
