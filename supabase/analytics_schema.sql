-- XCerberus Analytics Schema
-- Designed for extreme scalability (100M+ users)

-- User Activity Logs Table
CREATE TABLE IF NOT EXISTS user_activity_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type TEXT NOT NULL,
  activity_data JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_user_activity_logs_user ON user_activity_logs(user_id);
CREATE INDEX idx_user_activity_logs_type ON user_activity_logs(activity_type);
CREATE INDEX idx_user_activity_logs_created ON user_activity_logs(created_at);

-- Daily User Stats Table
CREATE TABLE IF NOT EXISTS daily_user_stats (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  login_count INTEGER DEFAULT 0,
  challenge_attempts INTEGER DEFAULT 0,
  challenge_completions INTEGER DEFAULT 0,
  module_progress INTEGER DEFAULT 0,
  coins_earned INTEGER DEFAULT 0,
  coins_spent INTEGER DEFAULT 0,
  total_time_spent INTEGER DEFAULT 0, -- In seconds
  UNIQUE(user_id, date)
);

-- Create index for faster queries
CREATE INDEX idx_daily_user_stats_user_date ON daily_user_stats(user_id, date);

-- Daily Site Stats Table
CREATE TABLE IF NOT EXISTS daily_site_stats (
  date DATE PRIMARY KEY,
  total_users INTEGER DEFAULT 0,
  new_users INTEGER DEFAULT 0,
  active_users INTEGER DEFAULT 0,
  total_logins INTEGER DEFAULT 0,
  challenge_attempts INTEGER DEFAULT 0,
  challenge_completions INTEGER DEFAULT 0,
  module_completions INTEGER DEFAULT 0,
  coins_earned INTEGER DEFAULT 0,
  coins_spent INTEGER DEFAULT 0,
  premium_conversions INTEGER DEFAULT 0,
  business_conversions INTEGER DEFAULT 0
);

-- Error Logs Table
CREATE TABLE IF NOT EXISTS error_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  error_type TEXT NOT NULL,
  error_message TEXT NOT NULL,
  error_stack TEXT,
  component TEXT,
  page_url TEXT,
  additional_data JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_error_logs_user ON error_logs(user_id);
CREATE INDEX idx_error_logs_type ON error_logs(error_type);
CREATE INDEX idx_error_logs_created ON error_logs(created_at);

-- User Engagement Metrics Table
CREATE TABLE IF NOT EXISTS user_engagement_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  metric_type TEXT NOT NULL,
  metric_value FLOAT NOT NULL,
  calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_user_engagement_metrics_user ON user_engagement_metrics(user_id);
CREATE INDEX idx_user_engagement_metrics_type ON user_engagement_metrics(metric_type);

-- Functions

-- Function to log user activity
CREATE OR REPLACE FUNCTION log_user_activity(
  user_id UUID,
  activity_type TEXT,
  activity_data JSONB DEFAULT NULL,
  ip_address TEXT DEFAULT NULL,
  user_agent TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO user_activity_logs (
    user_id,
    activity_type,
    activity_data,
    ip_address,
    user_agent
  ) VALUES (
    log_user_activity.user_id,
    log_user_activity.activity_type,
    log_user_activity.activity_data,
    log_user_activity.ip_address,
    log_user_activity.user_agent
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Function to log errors
CREATE OR REPLACE FUNCTION log_error(
  user_id UUID,
  error_type TEXT,
  error_message TEXT,
  error_stack TEXT DEFAULT NULL,
  component TEXT DEFAULT NULL,
  page_url TEXT DEFAULT NULL,
  additional_data JSONB DEFAULT NULL,
  ip_address TEXT DEFAULT NULL,
  user_agent TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO error_logs (
    user_id,
    error_type,
    error_message,
    error_stack,
    component,
    page_url,
    additional_data,
    ip_address,
    user_agent
  ) VALUES (
    log_error.user_id,
    log_error.error_type,
    log_error.error_message,
    log_error.error_stack,
    log_error.component,
    log_error.page_url,
    log_error.additional_data,
    log_error.ip_address,
    log_error.user_agent
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update daily user stats
CREATE OR REPLACE FUNCTION update_daily_user_stats(
  user_id UUID,
  login_count INTEGER DEFAULT 0,
  challenge_attempts INTEGER DEFAULT 0,
  challenge_completions INTEGER DEFAULT 0,
  module_progress INTEGER DEFAULT 0,
  coins_earned INTEGER DEFAULT 0,
  coins_spent INTEGER DEFAULT 0,
  time_spent INTEGER DEFAULT 0
)
RETURNS VOID AS $$
DECLARE
  today DATE := CURRENT_DATE;
BEGIN
  INSERT INTO daily_user_stats (
    user_id,
    date,
    login_count,
    challenge_attempts,
    challenge_completions,
    module_progress,
    coins_earned,
    coins_spent,
    total_time_spent
  ) VALUES (
    update_daily_user_stats.user_id,
    today,
    update_daily_user_stats.login_count,
    update_daily_user_stats.challenge_attempts,
    update_daily_user_stats.challenge_completions,
    update_daily_user_stats.module_progress,
    update_daily_user_stats.coins_earned,
    update_daily_user_stats.coins_spent,
    update_daily_user_stats.time_spent
  )
  ON CONFLICT (user_id, date) DO UPDATE
  SET
    login_count = daily_user_stats.login_count + update_daily_user_stats.login_count,
    challenge_attempts = daily_user_stats.challenge_attempts + update_daily_user_stats.challenge_attempts,
    challenge_completions = daily_user_stats.challenge_completions + update_daily_user_stats.challenge_completions,
    module_progress = daily_user_stats.module_progress + update_daily_user_stats.module_progress,
    coins_earned = daily_user_stats.coins_earned + update_daily_user_stats.coins_earned,
    coins_spent = daily_user_stats.coins_spent + update_daily_user_stats.coins_spent,
    total_time_spent = daily_user_stats.total_time_spent + update_daily_user_stats.time_spent;
END;
$$ LANGUAGE plpgsql;

-- Function to get user analytics
CREATE OR REPLACE FUNCTION get_user_analytics(user_id UUID, days INTEGER DEFAULT 30)
RETURNS TABLE (
  date DATE,
  login_count INTEGER,
  challenge_attempts INTEGER,
  challenge_completions INTEGER,
  module_progress INTEGER,
  coins_earned INTEGER,
  coins_spent INTEGER,
  total_time_spent INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    s.date,
    s.login_count,
    s.challenge_attempts,
    s.challenge_completions,
    s.module_progress,
    s.coins_earned,
    s.coins_spent,
    s.total_time_spent
  FROM daily_user_stats s
  WHERE s.user_id = get_user_analytics.user_id
  AND s.date >= (CURRENT_DATE - (get_user_analytics.days || ' days')::INTERVAL)
  ORDER BY s.date DESC;
END;
$$ LANGUAGE plpgsql;

-- Row Level Security Policies

-- User Activity Logs: Users can only view their own logs
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own activity logs" 
ON user_activity_logs FOR SELECT 
USING (auth.uid() = user_id);

-- Daily User Stats: Users can only view their own stats
ALTER TABLE daily_user_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own daily stats" 
ON daily_user_stats FOR SELECT 
USING (auth.uid() = user_id);

-- Error Logs: Users can only view their own error logs
ALTER TABLE error_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own error logs" 
ON error_logs FOR SELECT 
USING (auth.uid() = user_id);

-- User Engagement Metrics: Users can only view their own metrics
ALTER TABLE user_engagement_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own engagement metrics" 
ON user_engagement_metrics FOR SELECT 
USING (auth.uid() = user_id);

-- Admin policies for super admins
CREATE POLICY "Super admins can view all analytics data" 
ON user_activity_logs FOR SELECT 
USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE role_id = (SELECT id FROM user_roles WHERE name = 'super_admin')
  )
);

CREATE POLICY "Super admins can view all daily user stats" 
ON daily_user_stats FOR SELECT 
USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE role_id = (SELECT id FROM user_roles WHERE name = 'super_admin')
  )
);

CREATE POLICY "Super admins can view all error logs" 
ON error_logs FOR SELECT 
USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE role_id = (SELECT id FROM user_roles WHERE name = 'super_admin')
  )
);

CREATE POLICY "Super admins can view all engagement metrics" 
ON user_engagement_metrics FOR SELECT 
USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE role_id = (SELECT id FROM user_roles WHERE name = 'super_admin')
  )
);

CREATE POLICY "Super admins can view all site stats" 
ON daily_site_stats FOR SELECT 
USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE role_id = (SELECT id FROM user_roles WHERE name = 'super_admin')
  )
);
