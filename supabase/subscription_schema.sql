-- XCerberus Subscription and User Tracking Schema

-- User Roles Table
CREATE TABLE IF NOT EXISTS user_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default roles
INSERT INTO user_roles (name, description) VALUES
  ('free_user', 'Free tier user with limited access'),
  ('premium_user', 'Premium tier user with enhanced access'),
  ('business_user', 'Business tier user with team capabilities'),
  ('admin', 'Administrator with content management capabilities'),
  ('super_admin', 'Super administrator with full system access')
ON CONFLICT (name) DO NOTHING;

-- Enhanced User Profiles Table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS role_id UUID REFERENCES user_roles(id);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_start_date TIMESTAMP WITH TIME ZONE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_end_date TIMESTAMP WITH TIME ZONE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS auto_renew BOOLEAN DEFAULT FALSE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS coins INTEGER DEFAULT 0;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS login_count INTEGER DEFAULT 0;

-- Subscription Plans Table
CREATE TABLE IF NOT EXISTS subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  price INTEGER,
  currency TEXT DEFAULT 'INR',
  duration_days INTEGER DEFAULT 30,
  max_challenges INTEGER,
  max_learning_modules INTEGER,
  max_groups INTEGER DEFAULT 0,
  max_group_members INTEGER DEFAULT 0,
  features JSONB,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default subscription plans
INSERT INTO subscription_plans (name, description, price, duration_days, max_challenges, max_learning_modules, max_groups, max_group_members, features) VALUES
  ('free', 'Free tier with limited access', 0, NULL, 5, 3, 0, 0, '{"startHack": 15, "communityAccess": false, "realLinuxBoxes": false}'),
  ('premium', 'Premium tier with enhanced access', 399, 30, 15, NULL, 15, 8, '{"startHack": null, "communityAccess": true, "realLinuxBoxes": true}'),
  ('business', 'Business tier with team capabilities', NULL, 30, NULL, NULL, NULL, 8, '{"startHack": null, "communityAccess": true, "realLinuxBoxes": true, "teamFormation": true, "teamMessaging": true, "advancedAnalytics": true}')
ON CONFLICT (name) DO NOTHING;

-- Subscription Tracking Table
CREATE TABLE IF NOT EXISTS subscription_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id UUID REFERENCES subscription_plans(id),
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_date TIMESTAMP WITH TIME ZONE,
  status TEXT CHECK (status IN ('active', 'expired', 'cancelled', 'pending')),
  auto_renew BOOLEAN DEFAULT FALSE,
  payment_id TEXT,
  payment_method TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Wallet Table
CREATE TABLE IF NOT EXISTS user_wallets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  coins INTEGER DEFAULT 0,
  total_purchased INTEGER DEFAULT 0,
  total_spent INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Coin Packages Table
CREATE TABLE IF NOT EXISTS coin_packages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  coins INTEGER NOT NULL,
  price INTEGER NOT NULL,
  currency TEXT DEFAULT 'INR',
  is_popular BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default coin packages
INSERT INTO coin_packages (name, description, coins, price, is_popular) VALUES
  ('Basic Pack', 'Get started with a small pack of coins', 100, 99, FALSE),
  ('Standard Pack', 'Most popular choice for regular users', 500, 399, TRUE),
  ('Premium Pack', 'Great value for active users', 1200, 799, FALSE),
  ('Ultimate Pack', 'Best value for serious users', 3000, 1499, FALSE)
ON CONFLICT DO NOTHING;

-- Coin Transactions Table
CREATE TABLE IF NOT EXISTS coin_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  transaction_type TEXT CHECK (transaction_type IN ('purchase', 'spend', 'reward', 'refund')),
  description TEXT,
  reference_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teams Table
CREATE TABLE IF NOT EXISTS teams (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  max_members INTEGER DEFAULT 8,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Members Table
CREATE TABLE IF NOT EXISTS team_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_id, user_id)
);

-- Team Invitations Table
CREATE TABLE IF NOT EXISTS team_invitations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  invited_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'expired')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  UNIQUE(team_id, email)
);

-- Team Messages Table
CREATE TABLE IF NOT EXISTS team_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Access Table
CREATE TABLE IF NOT EXISTS challenge_access (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  subscription_plan_id UUID REFERENCES subscription_plans(id),
  requires_coins BOOLEAN DEFAULT FALSE,
  coin_cost INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Access Table
CREATE TABLE IF NOT EXISTS module_access (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  subscription_plan_id UUID REFERENCES subscription_plans(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Activity Tracking Table
CREATE TABLE IF NOT EXISTS user_activity (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type TEXT NOT NULL,
  activity_data JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscription Expiry Notification Table
CREATE TABLE IF NOT EXISTS subscription_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES subscription_tracking(id),
  notification_type TEXT CHECK (notification_type IN ('expiry_warning', 'expired', 'renewal_success', 'renewal_failed')),
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_read BOOLEAN DEFAULT FALSE
);

-- Functions

-- Function to check if subscription is expiring soon
CREATE OR REPLACE FUNCTION check_subscription_expiry()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if subscription is expiring in 3 days
  IF NEW.end_date - NOW() <= INTERVAL '3 days' AND NEW.status = 'active' THEN
    INSERT INTO subscription_notifications (
      user_id,
      subscription_id,
      notification_type
    ) VALUES (
      NEW.user_id,
      NEW.id,
      'expiry_warning'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to handle subscription expiry
CREATE OR REPLACE FUNCTION handle_expired_subscriptions()
RETURNS VOID AS $$
DECLARE
  expired_sub RECORD;
BEGIN
  -- Find expired subscriptions
  FOR expired_sub IN
    SELECT * FROM subscription_tracking
    WHERE end_date < NOW()
    AND status = 'active'
  LOOP
    -- Update subscription status
    UPDATE subscription_tracking
    SET status = 'expired',
        updated_at = NOW()
    WHERE id = expired_sub.id;
    
    -- Send notification
    INSERT INTO subscription_notifications (
      user_id,
      subscription_id,
      notification_type
    ) VALUES (
      expired_sub.user_id,
      expired_sub.id,
      'expired'
    );
    
    -- Update user role to free
    UPDATE profiles
    SET role_id = (SELECT id FROM user_roles WHERE name = 'free_user'),
        subscription_tier = 'free',
        updated_at = NOW()
    WHERE id = expired_sub.user_id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to track user login
CREATE OR REPLACE FUNCTION track_user_login()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE profiles
  SET last_login = NOW(),
      login_count = login_count + 1
  WHERE id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers

-- Trigger for subscription expiry check
CREATE TRIGGER check_subscription_expiry_trigger
AFTER INSERT OR UPDATE ON subscription_tracking
FOR EACH ROW
EXECUTE FUNCTION check_subscription_expiry();

-- Trigger for user login tracking
CREATE TRIGGER track_user_login_trigger
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION track_user_login();

-- Row Level Security Policies

-- Teams: Only team members can view their teams
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view their teams" 
ON teams FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = id
  )
);

CREATE POLICY "Team owners can update their teams" 
ON teams FOR UPDATE 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = id AND role = 'owner'
  )
);

-- Team Messages: Only team members can view messages
ALTER TABLE team_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view messages" 
ON team_messages FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_messages.team_id
  )
);

CREATE POLICY "Team members can send messages" 
ON team_messages FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_messages.team_id
  )
);

-- User Wallets: Users can only view and update their own wallet
ALTER TABLE user_wallets ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own wallet" 
ON user_wallets FOR SELECT 
USING (auth.uid() = user_id);

-- Coin Transactions: Users can only view their own transactions
ALTER TABLE coin_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own transactions" 
ON coin_transactions FOR SELECT 
USING (auth.uid() = user_id);

-- Subscription Tracking: Users can only view their own subscriptions
ALTER TABLE subscription_tracking ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own subscriptions" 
ON subscription_tracking FOR SELECT 
USING (auth.uid() = user_id);

-- Admin policies for super admins
CREATE POLICY "Super admins can view all data" 
ON profiles FOR SELECT 
USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE role_id = (SELECT id FROM user_roles WHERE name = 'super_admin')
  )
);

-- Create a cron job to check for expired subscriptions daily
SELECT cron.schedule(
  'check-expired-subscriptions',
  '0 0 * * *', -- Run at midnight every day
  $$
  SELECT handle_expired_subscriptions();
  $$
);
