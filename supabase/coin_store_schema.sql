-- XCerberus Coin System and Store Schema
-- Designed for extreme scalability (100M+ users)

-- Store Categories Table
CREATE TABLE IF NOT EXISTS store_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Store Items Table
CREATE TABLE IF NOT EXISTS store_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  category_id UUID REFERENCES store_categories(id),
  image_url TEXT,
  price INTEGER NOT NULL,
  discount_price INTEGER,
  discount_start TIMESTAMP WITH TIME ZONE,
  discount_end TIMESTAMP WITH TIME ZONE,
  stock INTEGER DEFAULT NULL, -- NULL means unlimited
  is_featured BOOLEAN DEFAULT FALSE,
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  content JSONB, -- Flexible content based on item type
  item_type TEXT NOT NULL, -- 'challenge', 'module', 'avatar', 'badge', etc.
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_store_items_category ON store_items(category_id);
CREATE INDEX idx_store_items_price ON store_items(price);
CREATE INDEX idx_store_items_type ON store_items(item_type);
CREATE INDEX idx_store_items_active ON store_items(is_active);
CREATE INDEX idx_store_items_featured ON store_items(is_featured);

-- Coin Packages Table
CREATE TABLE IF NOT EXISTS coin_packages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  coins INTEGER NOT NULL,
  price INTEGER NOT NULL,
  currency TEXT DEFAULT 'INR',
  discount_percentage INTEGER DEFAULT 0,
  is_popular BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Inventory Table
CREATE TABLE IF NOT EXISTS user_inventory (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  item_id UUID REFERENCES store_items(id) ON DELETE CASCADE,
  quantity INTEGER DEFAULT 1,
  purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  is_consumed BOOLEAN DEFAULT FALSE,
  consumed_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, item_id)
);

-- Create index for faster queries
CREATE INDEX idx_user_inventory_user ON user_inventory(user_id);
CREATE INDEX idx_user_inventory_item ON user_inventory(item_id);
CREATE INDEX idx_user_inventory_consumed ON user_inventory(is_consumed);

-- Coin Transactions Table
CREATE TABLE IF NOT EXISTS coin_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  balance_after INTEGER NOT NULL,
  transaction_type TEXT CHECK (transaction_type IN ('purchase', 'spend', 'reward', 'refund', 'gift')),
  description TEXT,
  reference_id UUID,
  reference_type TEXT, -- 'store_item', 'challenge', 'hint', etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_coin_transactions_user ON coin_transactions(user_id);
CREATE INDEX idx_coin_transactions_type ON coin_transactions(transaction_type);
CREATE INDEX idx_coin_transactions_created ON coin_transactions(created_at);

-- Store Orders Table
CREATE TABLE IF NOT EXISTS store_orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  total_coins INTEGER NOT NULL,
  status TEXT CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Store Order Items Table
CREATE TABLE IF NOT EXISTS store_order_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES store_orders(id) ON DELETE CASCADE,
  item_id UUID REFERENCES store_items(id) ON DELETE CASCADE,
  quantity INTEGER DEFAULT 1,
  price_per_unit INTEGER NOT NULL,
  total_price INTEGER NOT NULL
);

-- Coin Purchase Transactions Table
CREATE TABLE IF NOT EXISTS coin_purchases (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  package_id UUID REFERENCES coin_packages(id),
  coins INTEGER NOT NULL,
  amount INTEGER NOT NULL,
  currency TEXT DEFAULT 'INR',
  payment_method TEXT,
  payment_id TEXT,
  status TEXT CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create index for faster queries
CREATE INDEX idx_coin_purchases_user ON coin_purchases(user_id);
CREATE INDEX idx_coin_purchases_status ON coin_purchases(status);
CREATE INDEX idx_coin_purchases_created ON coin_purchases(created_at);

-- User Wishlist Table
CREATE TABLE IF NOT EXISTS user_wishlist (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  item_id UUID REFERENCES store_items(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, item_id)
);

-- Store Item Reviews Table
CREATE TABLE IF NOT EXISTS store_item_reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  item_id UUID REFERENCES store_items(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  review TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(item_id, user_id)
);

-- Create index for faster queries
CREATE INDEX idx_store_item_reviews_item ON store_item_reviews(item_id);
CREATE INDEX idx_store_item_reviews_user ON store_item_reviews(user_id);
CREATE INDEX idx_store_item_reviews_rating ON store_item_reviews(rating);

-- Store Promotions Table
CREATE TABLE IF NOT EXISTS store_promotions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  discount_type TEXT CHECK (discount_type IN ('percentage', 'fixed')),
  discount_value INTEGER NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Store Promotion Items Table
CREATE TABLE IF NOT EXISTS store_promotion_items (
  promotion_id UUID REFERENCES store_promotions(id) ON DELETE CASCADE,
  item_id UUID REFERENCES store_items(id) ON DELETE CASCADE,
  PRIMARY KEY (promotion_id, item_id)
);

-- Functions

-- Function to add coins to user
CREATE OR REPLACE FUNCTION add_user_coins(user_id UUID, coins_to_add INTEGER, transaction_type TEXT, description TEXT DEFAULT NULL, reference_id UUID DEFAULT NULL, reference_type TEXT DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  current_coins INTEGER;
  new_balance INTEGER;
BEGIN
  -- Get current coins
  SELECT coins INTO current_coins
  FROM profiles
  WHERE id = add_user_coins.user_id;
  
  -- Calculate new balance
  new_balance := COALESCE(current_coins, 0) + coins_to_add;
  
  -- Update user's coins
  UPDATE profiles
  SET coins = new_balance,
      updated_at = NOW()
  WHERE id = add_user_coins.user_id;
  
  -- Record transaction
  INSERT INTO coin_transactions (
    user_id,
    amount,
    balance_after,
    transaction_type,
    description,
    reference_id,
    reference_type
  ) VALUES (
    add_user_coins.user_id,
    coins_to_add,
    new_balance,
    add_user_coins.transaction_type,
    add_user_coins.description,
    add_user_coins.reference_id,
    add_user_coins.reference_type
  );
  
  RETURN new_balance;
END;
$$ LANGUAGE plpgsql;

-- Function to purchase item
CREATE OR REPLACE FUNCTION purchase_store_item(user_id UUID, item_id UUID, quantity INTEGER DEFAULT 1)
RETURNS BOOLEAN AS $$
DECLARE
  item_price INTEGER;
  item_stock INTEGER;
  user_coins INTEGER;
  total_cost INTEGER;
  order_id UUID;
BEGIN
  -- Get item details
  SELECT price, stock INTO item_price, item_stock
  FROM store_items
  WHERE id = purchase_store_item.item_id
  AND is_active = TRUE;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Item not found or inactive';
  END IF;
  
  -- Check stock
  IF item_stock IS NOT NULL AND item_stock < purchase_store_item.quantity THEN
    RAISE EXCEPTION 'Not enough stock available';
  END IF;
  
  -- Get user coins
  SELECT coins INTO user_coins
  FROM profiles
  WHERE id = purchase_store_item.user_id;
  
  -- Calculate total cost
  total_cost := item_price * purchase_store_item.quantity;
  
  -- Check if user has enough coins
  IF user_coins < total_cost THEN
    RAISE EXCEPTION 'Not enough coins';
  END IF;
  
  -- Create order
  INSERT INTO store_orders (
    user_id,
    total_coins,
    status
  ) VALUES (
    purchase_store_item.user_id,
    total_cost,
    'pending'
  ) RETURNING id INTO order_id;
  
  -- Add order item
  INSERT INTO store_order_items (
    order_id,
    item_id,
    quantity,
    price_per_unit,
    total_price
  ) VALUES (
    order_id,
    purchase_store_item.item_id,
    purchase_store_item.quantity,
    item_price,
    total_cost
  );
  
  -- Deduct coins from user
  PERFORM add_user_coins(
    purchase_store_item.user_id,
    -total_cost,
    'spend',
    'Purchase of store item',
    purchase_store_item.item_id,
    'store_item'
  );
  
  -- Add item to user inventory
  INSERT INTO user_inventory (
    user_id,
    item_id,
    quantity
  ) VALUES (
    purchase_store_item.user_id,
    purchase_store_item.item_id,
    purchase_store_item.quantity
  )
  ON CONFLICT (user_id, item_id) DO UPDATE
  SET quantity = user_inventory.quantity + purchase_store_item.quantity;
  
  -- Update stock if needed
  IF item_stock IS NOT NULL THEN
    UPDATE store_items
    SET stock = stock - purchase_store_item.quantity
    WHERE id = purchase_store_item.item_id;
  END IF;
  
  -- Complete order
  UPDATE store_orders
  SET status = 'completed',
      completed_at = NOW()
  WHERE id = order_id;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    -- If any error occurs, mark order as failed if it was created
    IF order_id IS NOT NULL THEN
      UPDATE store_orders
      SET status = 'failed'
      WHERE id = order_id;
    END IF;
    
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's coin balance
CREATE OR REPLACE FUNCTION get_user_coin_balance(user_id UUID)
RETURNS INTEGER AS $$
DECLARE
  balance INTEGER;
BEGIN
  SELECT coins INTO balance
  FROM profiles
  WHERE id = get_user_coin_balance.user_id;
  
  RETURN COALESCE(balance, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to get user's transaction history
CREATE OR REPLACE FUNCTION get_user_transaction_history(user_id UUID, limit_count INTEGER DEFAULT 50)
RETURNS TABLE (
  id UUID,
  amount INTEGER,
  balance_after INTEGER,
  transaction_type TEXT,
  description TEXT,
  reference_id UUID,
  reference_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.amount,
    t.balance_after,
    t.transaction_type,
    t.description,
    t.reference_id,
    t.reference_type,
    t.created_at
  FROM coin_transactions t
  WHERE t.user_id = get_user_transaction_history.user_id
  ORDER BY t.created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's inventory
CREATE OR REPLACE FUNCTION get_user_inventory(user_id UUID)
RETURNS TABLE (
  id UUID,
  item_id UUID,
  name TEXT,
  description TEXT,
  image_url TEXT,
  item_type TEXT,
  content JSONB,
  quantity INTEGER,
  purchased_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_consumed BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id,
    i.item_id,
    s.name,
    s.description,
    s.image_url,
    s.item_type,
    s.content,
    i.quantity,
    i.purchased_at,
    i.expires_at,
    i.is_consumed
  FROM user_inventory i
  JOIN store_items s ON i.item_id = s.id
  WHERE i.user_id = get_user_inventory.user_id
  ORDER BY i.purchased_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Row Level Security Policies

-- Store Items: Everyone can view active items
ALTER TABLE store_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view active store items" 
ON store_items FOR SELECT 
USING (is_active = TRUE);

CREATE POLICY "Premium users can view premium items" 
ON store_items FOR SELECT 
USING (
  NOT is_premium OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business'))
);

CREATE POLICY "Business users can view business items" 
ON store_items FOR SELECT 
USING (
  NOT is_business OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business')
);

-- User Inventory: Users can only view their own inventory
ALTER TABLE user_inventory ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own inventory" 
ON user_inventory FOR SELECT 
USING (auth.uid() = user_id);

-- Coin Transactions: Users can only view their own transactions
ALTER TABLE coin_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own transactions" 
ON coin_transactions FOR SELECT 
USING (auth.uid() = user_id);

-- Store Orders: Users can only view their own orders
ALTER TABLE store_orders ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own orders" 
ON store_orders FOR SELECT 
USING (auth.uid() = user_id);

-- Store Order Items: Users can only view their own order items
ALTER TABLE store_order_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own order items" 
ON store_order_items FOR SELECT 
USING (
  order_id IN (
    SELECT id FROM store_orders WHERE user_id = auth.uid()
  )
);

-- Coin Purchases: Users can only view their own purchases
ALTER TABLE coin_purchases ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own coin purchases" 
ON coin_purchases FOR SELECT 
USING (auth.uid() = user_id);

-- User Wishlist: Users can only view their own wishlist
ALTER TABLE user_wishlist ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own wishlist" 
ON user_wishlist FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can add to their own wishlist" 
ON user_wishlist FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can remove from their own wishlist" 
ON user_wishlist FOR DELETE 
USING (auth.uid() = user_id);

-- Store Item Reviews: Everyone can view reviews
ALTER TABLE store_item_reviews ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view store item reviews" 
ON store_item_reviews FOR SELECT 
USING (TRUE);

CREATE POLICY "Users can add their own reviews" 
ON store_item_reviews FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reviews" 
ON store_item_reviews FOR UPDATE 
USING (auth.uid() = user_id);

-- Insert sample data

-- Insert store categories
INSERT INTO store_categories (name, description, icon, display_order) VALUES
  ('Challenges', 'Premium cybersecurity challenges', 'fa-flag', 1),
  ('Learning Modules', 'Premium learning content', 'fa-book', 2),
  ('Avatars', 'Custom avatars for your profile', 'fa-user', 3),
  ('Badges', 'Special badges to show off your skills', 'fa-certificate', 4),
  ('Boosts', 'Temporary boosts and power-ups', 'fa-bolt', 5)
ON CONFLICT (name) DO NOTHING;

-- Insert coin packages
INSERT INTO coin_packages (name, description, coins, price, is_popular) VALUES
  ('Starter Pack', 'Get started with a small pack of coins', 100, 99, FALSE),
  ('Popular Pack', 'Most popular choice for regular users', 500, 399, TRUE),
  ('Premium Pack', 'Great value for active users', 1200, 799, FALSE),
  ('Ultimate Pack', 'Best value for serious users', 3000, 1499, FALSE)
ON CONFLICT DO NOTHING;
