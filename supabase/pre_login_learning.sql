-- Pre-login Learning Modules for XCerberus
-- These modules will be available to users before they log in

-- Insert learning module categories if they don't exist
INSERT INTO learning_module_categories (name, description, icon, display_order) VALUES
  ('Web Security', 'Learn about web application security vulnerabilities and how to exploit and mitigate them.', 'globe', 1),
  ('Network Security', 'Understand network protocols, attacks, and defense mechanisms.', 'network-wired', 2),
  ('Cryptography', 'Learn about encryption, hashing, and cryptographic protocols.', 'key', 3),
  ('OSINT', 'Open Source Intelligence gathering and analysis techniques.', 'eye', 4),
  ('Forensics', 'Digital forensics techniques for investigating security incidents.', 'search', 5),
  ('Reverse Engineering', 'Techniques for analyzing and understanding compiled code.', 'microchip', 6)
ON CONFLICT (name) DO NOTHING;

-- Insert learning module difficulty levels if they don't exist
INSERT INTO learning_module_difficulty_levels (name, description, display_order) VALUES
  ('Beginner', 'Suitable for those new to cybersecurity.', 1),
  ('Intermediate', 'Requires some prior knowledge of cybersecurity concepts.', 2),
  ('Advanced', 'For experienced cybersecurity practitioners.', 3),
  ('Expert', 'Challenging content for security experts.', 4)
ON CONFLICT (name) DO NOTHING;

-- Module 1: Introduction to Cybersecurity
INSERT INTO learning_modules (
  title, 
  slug, 
  description, 
  category_id, 
  difficulty_id, 
  estimated_time, 
  is_premium, 
  is_business,
  is_active
) VALUES (
  'Introduction to Cybersecurity', 
  'intro-to-cybersecurity', 
  'Learn the fundamentals of cybersecurity, including key concepts, common threats, and basic security practices. This module provides a solid foundation for beginners.',
  (SELECT id FROM learning_module_categories WHERE name = 'Web Security'),
  (SELECT id FROM learning_module_difficulty_levels WHERE name = 'Beginner'),
  60, 
  FALSE, 
  FALSE,
  TRUE
)
ON CONFLICT (slug) DO UPDATE SET
  description = EXCLUDED.description,
  estimated_time = EXCLUDED.estimated_time,
  is_premium = EXCLUDED.is_premium,
  is_business = EXCLUDED.is_business,
  is_active = EXCLUDED.is_active;

-- Module 1 Content
INSERT INTO learning_module_content (
  module_id,
  content
) VALUES (
  (SELECT id FROM learning_modules WHERE slug = 'intro-to-cybersecurity'),
  '{
    "introduction": "Welcome to the Introduction to Cybersecurity module! In this module, you will learn the fundamental concepts of cybersecurity, understand common threats, and discover basic security practices that everyone should follow. By the end of this module, you will have a solid foundation in cybersecurity principles that will prepare you for more advanced topics.",
    "sections": [
      "cybersecurity-basics",
      "common-threats",
      "security-practices",
      "career-paths"
    ],
    "section_details": {
      "cybersecurity-basics": {
        "title": "Cybersecurity Basics",
        "content": "Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These attacks are usually aimed at accessing, changing, or destroying sensitive information; extorting money from users; or interrupting normal business processes. The field encompasses various disciplines, including network security, application security, information security, operational security, and more.",
        "key_points": [
          "The CIA Triad: Confidentiality, Integrity, and Availability",
          "Authentication vs. Authorization",
          "Defense in Depth Strategy",
          "Security by Design Principles"
        ]
      },
      "common-threats": {
        "title": "Common Cybersecurity Threats",
        "content": "Understanding the threat landscape is crucial for effective cybersecurity. This section covers the most common types of cyber threats and attack vectors that organizations and individuals face today.",
        "key_points": [
          "Malware: Viruses, Worms, Trojans, Ransomware",
          "Social Engineering: Phishing, Spear Phishing, Pretexting",
          "Man-in-the-Middle Attacks",
          "Denial of Service (DoS) and Distributed Denial of Service (DDoS)",
          "SQL Injection and Cross-Site Scripting (XSS)"
        ]
      },
      "security-practices": {
        "title": "Basic Security Practices",
        "content": "Implementing good security practices is essential for protecting yourself and your organization from cyber threats. This section covers fundamental security measures that everyone should follow.",
        "key_points": [
          "Strong Password Management",
          "Multi-Factor Authentication",
          "Regular Software Updates and Patching",
          "Data Backup and Recovery",
          "Security Awareness and Training"
        ]
      },
      "career-paths": {
        "title": "Cybersecurity Career Paths",
        "content": "The field of cybersecurity offers diverse career opportunities. This section provides an overview of various cybersecurity roles and the skills required for each.",
        "key_points": [
          "Security Analyst",
          "Penetration Tester",
          "Security Engineer",
          "Security Architect",
          "Chief Information Security Officer (CISO)"
        ]
      }
    },
    "quiz": {
      "questions": [
        {
          "question": "What does the 'I' in the CIA triad stand for?",
          "options": ["Intelligence", "Integrity", "Internet", "Interface"],
          "correct_answer": "Integrity"
        },
        {
          "question": "Which of the following is NOT a type of malware?",
          "options": ["Virus", "Worm", "Phishing", "Ransomware"],
          "correct_answer": "Phishing"
        },
        {
          "question": "What is the primary purpose of multi-factor authentication?",
          "options": [
            "To make login faster",
            "To eliminate the need for passwords",
            "To add additional layers of security beyond just a password",
            "To track user activity"
          ],
          "correct_answer": "To add additional layers of security beyond just a password"
        }
      ]
    },
    "resources": [
      {
        "title": "NIST Cybersecurity Framework",
        "url": "https://www.nist.gov/cyberframework"
      },
      {
        "title": "OWASP Top Ten",
        "url": "https://owasp.org/www-project-top-ten/"
      }
    ]
  }'
)
ON CONFLICT (module_id) DO UPDATE SET
  content = EXCLUDED.content;

-- Module 2: Web Security Fundamentals
INSERT INTO learning_modules (
  title, 
  slug, 
  description, 
  category_id, 
  difficulty_id, 
  estimated_time, 
  is_premium, 
  is_business,
  is_active
) VALUES (
  'Web Security Fundamentals', 
  'web-security-fundamentals', 
  'Learn about common web vulnerabilities, how they are exploited, and best practices for securing web applications. This module covers essential web security concepts.',
  (SELECT id FROM learning_module_categories WHERE name = 'Web Security'),
  (SELECT id FROM learning_module_difficulty_levels WHERE name = 'Beginner'),
  75, 
  FALSE, 
  FALSE,
  TRUE
)
ON CONFLICT (slug) DO UPDATE SET
  description = EXCLUDED.description,
  estimated_time = EXCLUDED.estimated_time,
  is_premium = EXCLUDED.is_premium,
  is_business = EXCLUDED.is_business,
  is_active = EXCLUDED.is_active;

-- Module 2 Content
INSERT INTO learning_module_content (
  module_id,
  content
) VALUES (
  (SELECT id FROM learning_modules WHERE slug = 'web-security-fundamentals'),
  '{
    "introduction": "Welcome to the Web Security Fundamentals module! In this module, you will learn about common web vulnerabilities, how they are exploited by attackers, and best practices for securing web applications. Web security is crucial as most modern businesses rely on web applications for their operations and customer interactions.",
    "sections": [
      "web-architecture",
      "common-vulnerabilities",
      "secure-coding",
      "security-testing"
    ],
    "section_details": {
      "web-architecture": {
        "title": "Web Application Architecture",
        "content": "Understanding how web applications work is essential for securing them. This section covers the basic components of web architecture and how they interact.",
        "key_points": [
          "Client-Server Model",
          "HTTP/HTTPS Protocols",
          "Frontend vs. Backend",
          "Databases and Data Storage",
          "Authentication and Session Management"
        ]
      },
      "common-vulnerabilities": {
        "title": "Common Web Vulnerabilities",
        "content": "Web applications are susceptible to various security vulnerabilities. This section covers the most common web vulnerabilities based on the OWASP Top Ten.",
        "key_points": [
          "Injection Flaws (SQL, NoSQL, OS, LDAP)",
          "Broken Authentication and Session Management",
          "Cross-Site Scripting (XSS)",
          "Cross-Site Request Forgery (CSRF)",
          "Security Misconfigurations"
        ]
      },
      "secure-coding": {
        "title": "Secure Coding Practices",
        "content": "Implementing secure coding practices is essential for developing secure web applications. This section covers fundamental principles and techniques for secure coding.",
        "key_points": [
          "Input Validation and Sanitization",
          "Parameterized Queries",
          "Output Encoding",
          "Secure Authentication Implementation",
          "Proper Error Handling"
        ]
      },
      "security-testing": {
        "title": "Web Application Security Testing",
        "content": "Regular security testing is crucial for identifying and addressing vulnerabilities in web applications. This section introduces various approaches to web application security testing.",
        "key_points": [
          "Static Application Security Testing (SAST)",
          "Dynamic Application Security Testing (DAST)",
          "Manual Penetration Testing",
          "Security Code Reviews",
          "Continuous Security Testing"
        ]
      }
    },
    "quiz": {
      "questions": [
        {
          "question": "Which of the following is an example of an injection attack?",
          "options": ["Cross-Site Scripting", "SQL Injection", "Session Hijacking", "Brute Force Attack"],
          "correct_answer": "SQL Injection"
        },
        {
          "question": "What is the primary defense against Cross-Site Scripting (XSS) attacks?",
          "options": [
            "Input validation",
            "Output encoding",
            "Using HTTPS",
            "Strong passwords"
          ],
          "correct_answer": "Output encoding"
        },
        {
          "question": "Which of the following is NOT a secure coding practice?",
          "options": [
            "Using parameterized queries",
            "Storing passwords in plain text",
            "Implementing proper error handling",
            "Input validation"
          ],
          "correct_answer": "Storing passwords in plain text"
        }
      ]
    },
    "resources": [
      {
        "title": "OWASP Web Security Testing Guide",
        "url": "https://owasp.org/www-project-web-security-testing-guide/"
      },
      {
        "title": "Mozilla Web Security Guidelines",
        "url": "https://infosec.mozilla.org/guidelines/web_security"
      }
    ]
  }'
)
ON CONFLICT (module_id) DO UPDATE SET
  content = EXCLUDED.content;

-- Module 3: Network Security Basics
INSERT INTO learning_modules (
  title, 
  slug, 
  description, 
  category_id, 
  difficulty_id, 
  estimated_time, 
  is_premium, 
  is_business,
  is_active
) VALUES (
  'Network Security Basics', 
  'network-security-basics', 
  'Learn the fundamentals of network security, including common network attacks, defense mechanisms, and monitoring techniques. This module provides essential knowledge for securing networks.',
  (SELECT id FROM learning_module_categories WHERE name = 'Network Security'),
  (SELECT id FROM learning_module_difficulty_levels WHERE name = 'Beginner'),
  70, 
  FALSE, 
  FALSE,
  TRUE
)
ON CONFLICT (slug) DO UPDATE SET
  description = EXCLUDED.description,
  estimated_time = EXCLUDED.estimated_time,
  is_premium = EXCLUDED.is_premium,
  is_business = EXCLUDED.is_business,
  is_active = EXCLUDED.is_active;

-- Module 3 Content
INSERT INTO learning_module_content (
  module_id,
  content
) VALUES (
  (SELECT id FROM learning_modules WHERE slug = 'network-security-basics'),
  '{
    "introduction": "Welcome to the Network Security Basics module! In this module, you will learn the fundamentals of network security, including network architecture, common attack vectors, defense mechanisms, and monitoring techniques. Network security is critical as it protects the underlying infrastructure that supports information systems and communications.",
    "sections": [
      "network-fundamentals",
      "network-threats",
      "defense-mechanisms",
      "monitoring-detection"
    ],
    "section_details": {
      "network-fundamentals": {
        "title": "Network Fundamentals",
        "content": "Understanding how networks function is essential for securing them. This section covers basic network concepts and components.",
        "key_points": [
          "OSI and TCP/IP Models",
          "Network Protocols (TCP, UDP, ICMP, etc.)",
          "Network Devices (Routers, Switches, Firewalls)",
          "IP Addressing and Subnetting",
          "Network Topologies"
        ]
      },
      "network-threats": {
        "title": "Common Network Threats",
        "content": "Networks face various security threats. This section covers common network attacks and vulnerabilities.",
        "key_points": [
          "Man-in-the-Middle Attacks",
          "Denial of Service (DoS) and Distributed Denial of Service (DDoS)",
          "ARP Poisoning and MAC Flooding",
          "DNS Attacks (Cache Poisoning, Tunneling)",
          "Wireless Network Attacks"
        ]
      },
      "defense-mechanisms": {
        "title": "Network Defense Mechanisms",
        "content": "Implementing proper defense mechanisms is crucial for network security. This section covers various tools and techniques for protecting networks.",
        "key_points": [
          "Firewalls and Access Control Lists",
          "Intrusion Detection and Prevention Systems",
          "Virtual Private Networks (VPNs)",
          "Network Segmentation",
          "Encryption Protocols (TLS/SSL, IPsec)"
        ]
      },
      "monitoring-detection": {
        "title": "Network Monitoring and Detection",
        "content": "Continuous monitoring is essential for detecting and responding to network security incidents. This section covers monitoring tools and techniques.",
        "key_points": [
          "Network Traffic Analysis",
          "Log Management and Analysis",
          "Security Information and Event Management (SIEM)",
          "Network Behavior Analysis",
          "Incident Response Procedures"
        ]
      }
    },
    "quiz": {
      "questions": [
        {
          "question": "Which layer of the OSI model is responsible for routing?",
          "options": ["Physical Layer", "Data Link Layer", "Network Layer", "Transport Layer"],
          "correct_answer": "Network Layer"
        },
        {
          "question": "What type of attack aims to exhaust a system\'s resources, making it unavailable to legitimate users?",
          "options": [
            "Man-in-the-Middle Attack",
            "SQL Injection",
            "Denial of Service Attack",
            "Cross-Site Scripting"
          ],
          "correct_answer": "Denial of Service Attack"
        },
        {
          "question": "Which of the following is NOT a function of a firewall?",
          "options": [
            "Filtering network traffic",
            "Blocking unauthorized access",
            "Encrypting data",
            "Monitoring network traffic"
          ],
          "correct_answer": "Encrypting data"
        }
      ]
    },
    "resources": [
      {
        "title": "SANS Network Security Resources",
        "url": "https://www.sans.org/network-security/"
      },
      {
        "title": "Wireshark Network Protocol Analyzer",
        "url": "https://www.wireshark.org/"
      }
    ]
  }'
)
ON CONFLICT (module_id) DO UPDATE SET
  content = EXCLUDED.content;
