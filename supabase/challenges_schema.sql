-- XCerberus Challenges Schema
-- Designed for extreme scalability (100M+ users)

-- Challenge Categories Table
CREATE TABLE IF NOT EXISTS challenge_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Difficulty Levels Table
CREATE TABLE IF NOT EXISTS challenge_difficulty_levels (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Types Table
CREATE TABLE IF NOT EXISTS challenge_types (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenges Table
CREATE TABLE IF NOT EXISTS challenges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE,
  description TEXT NOT NULL,
  category_id UUID REFERENCES challenge_categories(id),
  difficulty_id UUID REFERENCES challenge_difficulty_levels(id),
  type_id UUID REFERENCES challenge_types(id),
  points INTEGER DEFAULT 0,
  coin_reward INTEGER DEFAULT 0,
  estimated_time INTEGER, -- In minutes
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_challenges_category_difficulty ON challenges(category_id, difficulty_id);
CREATE INDEX idx_challenges_premium_business ON challenges(is_premium, is_business);
CREATE INDEX idx_challenges_active ON challenges(is_active);
CREATE INDEX idx_challenges_search ON challenges USING gin(title gin_trgm_ops, description gin_trgm_ops);

-- Challenge Content Table
CREATE TABLE IF NOT EXISTS challenge_content (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  content JSONB NOT NULL, -- Structured content for the challenge
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Hints Table
CREATE TABLE IF NOT EXISTS challenge_hints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  hint TEXT NOT NULL,
  coin_cost INTEGER DEFAULT 0,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Attempts Table
CREATE TABLE IF NOT EXISTS challenge_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  solution TEXT,
  is_correct BOOLEAN,
  points_earned INTEGER DEFAULT 0,
  coins_earned INTEGER DEFAULT 0,
  attempt_time INTEGER, -- In seconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, challenge_id, created_at)
);

-- Challenge Completions Table
CREATE TABLE IF NOT EXISTS challenge_completions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  points_earned INTEGER DEFAULT 0,
  coins_earned INTEGER DEFAULT 0,
  completion_time INTEGER, -- In seconds
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, challenge_id)
);

-- Challenge Ratings Table
CREATE TABLE IF NOT EXISTS challenge_ratings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  feedback TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(challenge_id, user_id)
);

-- Challenge Comments Table
CREATE TABLE IF NOT EXISTS challenge_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  parent_id UUID REFERENCES challenge_comments(id) ON DELETE CASCADE,
  comment TEXT NOT NULL,
  is_approved BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Hint Purchases Table
CREATE TABLE IF NOT EXISTS challenge_hint_purchases (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  hint_id UUID REFERENCES challenge_hints(id) ON DELETE CASCADE,
  coins_spent INTEGER NOT NULL,
  purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, hint_id)
);

-- Insert sample data for categories
INSERT INTO challenge_categories (name, description, icon, display_order) VALUES
  ('Web', 'Web application security challenges', 'globe', 1),
  ('Crypto', 'Cryptography challenges', 'key', 2),
  ('Forensics', 'Digital forensics challenges', 'search', 3),
  ('Reverse Engineering', 'Reverse engineering challenges', 'microchip', 4),
  ('Binary Exploitation', 'Binary exploitation challenges', 'bug', 5),
  ('OSINT', 'Open Source Intelligence challenges', 'eye', 6),
  ('Network', 'Network security challenges', 'network-wired', 7)
ON CONFLICT (name) DO NOTHING;

-- Insert sample data for difficulty levels
INSERT INTO challenge_difficulty_levels (name, description, display_order) VALUES
  ('Easy', 'Entry-level challenges for beginners', 1),
  ('Medium', 'Challenges for users with some experience', 2),
  ('Hard', 'Challenging problems for experienced users', 3),
  ('Expert', 'Very difficult challenges for experts', 4)
ON CONFLICT (name) DO NOTHING;

-- Insert sample data for challenge types
INSERT INTO challenge_types (name, description) VALUES
  ('CTF', 'Capture The Flag challenges'),
  ('Jeopardy', 'Jeopardy-style challenges'),
  ('Attack-Defense', 'Attack and defense challenges'),
  ('King of the Hill', 'King of the Hill challenges')
ON CONFLICT (name) DO NOTHING;

-- Insert sample challenges
INSERT INTO challenges (title, slug, description, category_id, difficulty_id, type_id, points, coin_reward, estimated_time, is_premium, is_business) VALUES
  ('SQL Injection Basics', 'sql-injection-basics', 'Learn the basics of SQL injection and exploit a vulnerable login form.',
   (SELECT id FROM challenge_categories WHERE name = 'Web'),
   (SELECT id FROM challenge_difficulty_levels WHERE name = 'Easy'),
   (SELECT id FROM challenge_types WHERE name = 'CTF'),
   100, 10, 30, FALSE, FALSE),
   
  ('Advanced XSS Attacks', 'advanced-xss-attacks', 'Explore advanced cross-site scripting techniques and bypass various filters.',
   (SELECT id FROM challenge_categories WHERE name = 'Web'),
   (SELECT id FROM challenge_difficulty_levels WHERE name = 'Hard'),
   (SELECT id FROM challenge_types WHERE name = 'CTF'),
   300, 30, 60, TRUE, FALSE),
   
  ('Cryptographic Puzzle', 'cryptographic-puzzle', 'Solve a series of cryptographic puzzles using various encryption techniques.',
   (SELECT id FROM challenge_categories WHERE name = 'Crypto'),
   (SELECT id FROM challenge_difficulty_levels WHERE name = 'Medium'),
   (SELECT id FROM challenge_types WHERE name = 'Jeopardy'),
   200, 20, 45, FALSE, FALSE),
   
  ('Network Traffic Analysis', 'network-traffic-analysis', 'Analyze network traffic to identify malicious activities and extract hidden information.',
   (SELECT id FROM challenge_categories WHERE name = 'Network'),
   (SELECT id FROM challenge_difficulty_levels WHERE name = 'Medium'),
   (SELECT id FROM challenge_types WHERE name = 'CTF'),
   200, 20, 45, FALSE, FALSE),
   
  ('Enterprise Security Assessment', 'enterprise-security-assessment', 'Conduct a comprehensive security assessment of an enterprise environment.',
   (SELECT id FROM challenge_categories WHERE name = 'Network'),
   (SELECT id FROM challenge_difficulty_levels WHERE name = 'Expert'),
   (SELECT id FROM challenge_types WHERE name = 'Attack-Defense'),
   500, 50, 120, TRUE, TRUE)
ON CONFLICT DO NOTHING;

-- Insert sample challenge content
INSERT INTO challenge_content (challenge_id, content) VALUES
  ((SELECT id FROM challenges WHERE slug = 'sql-injection-basics'), 
   '{"description": "In this challenge, you will learn about SQL injection vulnerabilities and how to exploit them...", "flag_format": "flag{...}", "hints_available": true}'),
   
  ((SELECT id FROM challenges WHERE slug = 'advanced-xss-attacks'), 
   '{"description": "Cross-site scripting (XSS) is a type of security vulnerability typically found in web applications...", "flag_format": "flag{...}", "hints_available": true}'),
   
  ((SELECT id FROM challenges WHERE slug = 'cryptographic-puzzle'), 
   '{"description": "This challenge consists of multiple cryptographic puzzles that you need to solve...", "flag_format": "flag{...}", "hints_available": true}'),
   
  ((SELECT id FROM challenges WHERE slug = 'network-traffic-analysis'), 
   '{"description": "You are provided with a packet capture file containing network traffic...", "flag_format": "flag{...}", "hints_available": true}'),
   
  ((SELECT id FROM challenges WHERE slug = 'enterprise-security-assessment'), 
   '{"description": "You are tasked with conducting a security assessment of an enterprise environment...", "flag_format": "flag{...}", "hints_available": true}')
ON CONFLICT DO NOTHING;

-- Insert sample challenge hints
INSERT INTO challenge_hints (challenge_id, hint, coin_cost, display_order) VALUES
  ((SELECT id FROM challenges WHERE slug = 'sql-injection-basics'), 
   'Try using single quotes in the input fields to see how the application responds.', 5, 1),
   
  ((SELECT id FROM challenges WHERE slug = 'sql-injection-basics'), 
   'Look for error messages that might reveal information about the database structure.', 10, 2),
   
  ((SELECT id FROM challenges WHERE slug = 'advanced-xss-attacks'), 
   'The application is filtering certain characters. Try using encoding techniques to bypass the filters.', 15, 1),
   
  ((SELECT id FROM challenges WHERE slug = 'cryptographic-puzzle'), 
   'The first part of the puzzle uses a Caesar cipher with a non-standard shift.', 10, 1),
   
  ((SELECT id FROM challenges WHERE slug = 'network-traffic-analysis'), 
   'Focus on HTTP traffic and look for suspicious requests.', 10, 1)
ON CONFLICT DO NOTHING;

-- Row Level Security Policies

-- Challenges: Access based on subscription tier
ALTER TABLE challenges ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view non-premium challenges" 
ON challenges FOR SELECT 
USING (
  NOT is_premium OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business'))
);

CREATE POLICY "Premium users can view premium challenges" 
ON challenges FOR SELECT 
USING (
  NOT is_business OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business')
);

-- Challenge Content: Access based on subscription tier
ALTER TABLE challenge_content ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view challenge content they have access to" 
ON challenge_content FOR SELECT 
USING (
  challenge_id IN (
    SELECT id FROM challenges WHERE 
      (NOT is_premium OR auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business')))
      AND
      (NOT is_business OR auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business'))
  )
);

-- Challenge Hints: Everyone can view hints
ALTER TABLE challenge_hints ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view challenge hints" 
ON challenge_hints FOR SELECT 
USING (true);

-- Challenge Attempts: Users can only view and insert their own attempts
ALTER TABLE challenge_attempts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own challenge attempts" 
ON challenge_attempts FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own challenge attempts" 
ON challenge_attempts FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Challenge Completions: Users can only view their own completions
ALTER TABLE challenge_completions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own challenge completions" 
ON challenge_completions FOR SELECT 
USING (auth.uid() = user_id);

-- Challenge Ratings: Everyone can view ratings, users can only add their own
ALTER TABLE challenge_ratings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view challenge ratings" 
ON challenge_ratings FOR SELECT 
USING (true);

CREATE POLICY "Users can insert their own challenge ratings" 
ON challenge_ratings FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Challenge Comments: Everyone can view approved comments
ALTER TABLE challenge_comments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view approved challenge comments" 
ON challenge_comments FOR SELECT 
USING (is_approved = TRUE);

CREATE POLICY "Users can view their own challenge comments" 
ON challenge_comments FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own challenge comments" 
ON challenge_comments FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Challenge Hint Purchases: Users can only view and insert their own purchases
ALTER TABLE challenge_hint_purchases ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own hint purchases" 
ON challenge_hint_purchases FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own hint purchases" 
ON challenge_hint_purchases FOR INSERT 
WITH CHECK (auth.uid() = user_id);
