-- Learning Module System Database Schema

-- Learning Module Categories
CREATE TABLE IF NOT EXISTS learning_module_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Difficulty Levels
CREATE TABLE IF NOT EXISTS learning_module_difficulty_levels (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Modules
CREATE TABLE IF NOT EXISTS learning_modules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE,
  description TEXT NOT NULL,
  category_id UUID REFERENCES learning_module_categories(id),
  difficulty_id UUID REFERENCES learning_module_difficulty_levels(id),
  estimated_time INTEGER, -- In minutes
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_learning_modules_category_difficulty ON learning_modules(category_id, difficulty_id);
CREATE INDEX idx_learning_modules_premium_business ON learning_modules(is_premium, is_business);
CREATE INDEX idx_learning_modules_active ON learning_modules(is_active);

-- Learning Module Content
CREATE TABLE IF NOT EXISTS learning_module_content (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  content JSONB NOT NULL, -- Structured content for the module
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Sections
CREATE TABLE IF NOT EXISTS learning_module_sections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  display_order INTEGER DEFAULT 0,
  estimated_time INTEGER, -- In minutes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Units
CREATE TABLE IF NOT EXISTS learning_module_units (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  section_id UUID REFERENCES learning_module_sections(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  type TEXT NOT NULL, -- 'text', 'video', 'quiz', 'exercise', etc.
  content JSONB NOT NULL,
  display_order INTEGER DEFAULT 0,
  estimated_time INTEGER, -- In minutes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Progress
CREATE TABLE IF NOT EXISTS learning_module_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  progress FLOAT DEFAULT 0, -- Percentage of completion
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed BOOLEAN DEFAULT FALSE,
  completed_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, module_id)
);

-- Learning Module Unit Progress
CREATE TABLE IF NOT EXISTS learning_module_unit_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  unit_id UUID REFERENCES learning_module_units(id) ON DELETE CASCADE,
  status TEXT NOT NULL, -- 'not_started', 'in_progress', 'completed'
  progress FLOAT DEFAULT 0, -- Percentage of completion
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed BOOLEAN DEFAULT FALSE,
  completed_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, unit_id)
);

-- Learning Module Ratings
CREATE TABLE IF NOT EXISTS learning_module_ratings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  feedback TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(module_id, user_id)
);

-- Learning Paths
CREATE TABLE IF NOT EXISTS learning_paths (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category_id UUID REFERENCES learning_module_categories(id),
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Path Modules
CREATE TABLE IF NOT EXISTS learning_path_modules (
  path_id UUID REFERENCES learning_paths(id) ON DELETE CASCADE,
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  display_order INTEGER DEFAULT 0,
  PRIMARY KEY (path_id, module_id)
);

-- Insert sample data for categories
INSERT INTO learning_module_categories (name, description, icon, display_order) VALUES
  ('Web Security', 'Learn about web application security vulnerabilities and how to exploit and mitigate them.', 'globe', 1),
  ('Network Security', 'Understand network protocols, attacks, and defense mechanisms.', 'network-wired', 2),
  ('Cryptography', 'Learn about encryption, hashing, and cryptographic protocols.', 'key', 3),
  ('Reverse Engineering', 'Techniques for analyzing and understanding compiled code.', 'microchip', 4),
  ('Forensics', 'Digital forensics techniques for investigating security incidents.', 'search', 5),
  ('OSINT', 'Open Source Intelligence gathering and analysis.', 'eye', 6),
  ('Mobile Security', 'Security aspects of mobile applications and devices.', 'mobile-alt', 7)
ON CONFLICT (name) DO NOTHING;

-- Insert sample data for difficulty levels
INSERT INTO learning_module_difficulty_levels (name, description, display_order) VALUES
  ('Beginner', 'Suitable for those new to cybersecurity.', 1),
  ('Intermediate', 'Requires some prior knowledge of cybersecurity concepts.', 2),
  ('Advanced', 'For experienced cybersecurity practitioners.', 3),
  ('Expert', 'Challenging content for security experts.', 4)
ON CONFLICT (name) DO NOTHING;

-- Insert sample learning modules
INSERT INTO learning_modules (title, slug, description, category_id, difficulty_id, estimated_time, is_premium, is_business) VALUES
  ('Introduction to Web Security', 'intro-web-security', 'Learn the basics of web security including common vulnerabilities and how to identify them.', 
   (SELECT id FROM learning_module_categories WHERE name = 'Web Security'),
   (SELECT id FROM learning_module_difficulty_levels WHERE name = 'Beginner'),
   60, FALSE, FALSE),
  
  ('Advanced SQL Injection Techniques', 'advanced-sql-injection', 'Master advanced SQL injection techniques and learn how to protect against them.',
   (SELECT id FROM learning_module_categories WHERE name = 'Web Security'),
   (SELECT id FROM learning_module_difficulty_levels WHERE name = 'Advanced'),
   120, TRUE, FALSE),
   
  ('Network Traffic Analysis', 'network-traffic-analysis', 'Learn how to analyze network traffic to identify security threats and anomalies.',
   (SELECT id FROM learning_module_categories WHERE name = 'Network Security'),
   (SELECT id FROM learning_module_difficulty_levels WHERE name = 'Intermediate'),
   90, FALSE, FALSE),
   
  ('Cryptography Fundamentals', 'crypto-fundamentals', 'Understand the basics of cryptography including symmetric and asymmetric encryption.',
   (SELECT id FROM learning_module_categories WHERE name = 'Cryptography'),
   (SELECT id FROM learning_module_difficulty_levels WHERE name = 'Beginner'),
   75, FALSE, FALSE),
   
  ('Enterprise Threat Hunting', 'enterprise-threat-hunting', 'Advanced techniques for hunting threats in enterprise environments.',
   (SELECT id FROM learning_module_categories WHERE name = 'Network Security'),
   (SELECT id FROM learning_module_difficulty_levels WHERE name = 'Expert'),
   180, TRUE, TRUE)
ON CONFLICT DO NOTHING;

-- Insert sample learning module content
INSERT INTO learning_module_content (module_id, content) VALUES
  ((SELECT id FROM learning_modules WHERE slug = 'intro-web-security'), 
   '{"introduction": "Web security is critical in today\'s interconnected world...", "sections": ["overview", "common-vulnerabilities", "defense-strategies"]}'),
  
  ((SELECT id FROM learning_modules WHERE slug = 'advanced-sql-injection'), 
   '{"introduction": "SQL injection remains one of the most dangerous web vulnerabilities...", "sections": ["advanced-techniques", "blind-injection", "prevention"]}'),
   
  ((SELECT id FROM learning_modules WHERE slug = 'network-traffic-analysis'), 
   '{"introduction": "Network traffic analysis is essential for identifying security threats...", "sections": ["packet-analysis", "anomaly-detection", "tools"]}'),
   
  ((SELECT id FROM learning_modules WHERE slug = 'crypto-fundamentals'), 
   '{"introduction": "Cryptography is the practice of secure communication...", "sections": ["symmetric-encryption", "asymmetric-encryption", "hashing"]}'),
   
  ((SELECT id FROM learning_modules WHERE slug = 'enterprise-threat-hunting'), 
   '{"introduction": "Threat hunting is a proactive approach to finding threats...", "sections": ["methodology", "tools", "case-studies"]}')
ON CONFLICT DO NOTHING;

-- Insert sample learning paths
INSERT INTO learning_paths (title, description, category_id, is_premium, is_business) VALUES
  ('Web Security Specialist', 'Become a web security specialist with this comprehensive learning path.',
   (SELECT id FROM learning_module_categories WHERE name = 'Web Security'),
   TRUE, FALSE),
   
  ('Network Defense Expert', 'Master network security and defense techniques.',
   (SELECT id FROM learning_module_categories WHERE name = 'Network Security'),
   TRUE, FALSE),
   
  ('Cryptography Essentials', 'Learn the essential cryptographic concepts and techniques.',
   (SELECT id FROM learning_module_categories WHERE name = 'Cryptography'),
   FALSE, FALSE)
ON CONFLICT DO NOTHING;

-- Insert sample learning path modules
INSERT INTO learning_path_modules (path_id, module_id, display_order) VALUES
  ((SELECT id FROM learning_paths WHERE title = 'Web Security Specialist'),
   (SELECT id FROM learning_modules WHERE slug = 'intro-web-security'),
   1),
   
  ((SELECT id FROM learning_paths WHERE title = 'Web Security Specialist'),
   (SELECT id FROM learning_modules WHERE slug = 'advanced-sql-injection'),
   2),
   
  ((SELECT id FROM learning_paths WHERE title = 'Network Defense Expert'),
   (SELECT id FROM learning_modules WHERE slug = 'network-traffic-analysis'),
   1),
   
  ((SELECT id FROM learning_paths WHERE title = 'Cryptography Essentials'),
   (SELECT id FROM learning_modules WHERE slug = 'crypto-fundamentals'),
   1)
ON CONFLICT DO NOTHING;

-- Row Level Security Policies

-- Learning Modules: Access based on subscription tier
ALTER TABLE learning_modules ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view non-premium learning modules" 
ON learning_modules FOR SELECT 
USING (
  NOT is_premium OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business'))
);

CREATE POLICY "Premium users can view premium learning modules" 
ON learning_modules FOR SELECT 
USING (
  NOT is_business OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business')
);

-- Learning Module Content: Access based on subscription tier
ALTER TABLE learning_module_content ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view learning module content they have access to" 
ON learning_module_content FOR SELECT 
USING (
  module_id IN (
    SELECT id FROM learning_modules WHERE 
      (NOT is_premium OR auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business')))
      AND
      (NOT is_business OR auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business'))
  )
);

-- Learning Module Progress: Users can only view and update their own progress
ALTER TABLE learning_module_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own module progress" 
ON learning_module_progress FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own module progress" 
ON learning_module_progress FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own module progress" 
ON learning_module_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Learning Module Unit Progress: Users can only view and update their own progress
ALTER TABLE learning_module_unit_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own unit progress" 
ON learning_module_unit_progress FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own unit progress" 
ON learning_module_unit_progress FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own unit progress" 
ON learning_module_unit_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Learning Module Ratings: Everyone can view ratings, users can only add their own
ALTER TABLE learning_module_ratings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view module ratings" 
ON learning_module_ratings FOR SELECT 
USING (true);

CREATE POLICY "Users can insert their own module ratings" 
ON learning_module_ratings FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Learning Paths: Access based on subscription tier
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view non-premium learning paths" 
ON learning_paths FOR SELECT 
USING (
  NOT is_premium OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business'))
);

CREATE POLICY "Premium users can view premium learning paths" 
ON learning_paths FOR SELECT 
USING (
  NOT is_business OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business')
);
