-- XCerberus Team Collaboration Schema
-- Designed for extreme scalability (100M+ users)

-- Enhanced Teams Table
CREATE TABLE IF NOT EXISTS enhanced_teams (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  logo_url TEXT,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  max_members INTEGER DEFAULT 8,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Members Table
CREATE TABLE IF NOT EXISTS team_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member' <PERSON>ECK (role IN ('owner', 'admin', 'member')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_id, user_id)
);

-- Team Invitations Table
CREATE TABLE IF NOT EXISTS team_invitations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  invited_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'expired')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  UNIQUE(team_id, email)
);

-- Team Messages Table
CREATE TABLE IF NOT EXISTS team_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  attachments JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Channels Table
CREATE TABLE IF NOT EXISTS team_channels (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  is_private BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_id, name)
);

-- Team Channel Members Table
CREATE TABLE IF NOT EXISTS team_channel_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  channel_id UUID REFERENCES team_channels(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(channel_id, user_id)
);

-- Team Channel Messages Table
CREATE TABLE IF NOT EXISTS team_channel_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  channel_id UUID REFERENCES team_channels(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  attachments JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Resources Table
CREATE TABLE IF NOT EXISTS team_resources (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  resource_type TEXT NOT NULL, -- 'file', 'link', 'note', etc.
  resource_url TEXT,
  content TEXT,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Challenges Table
CREATE TABLE IF NOT EXISTS team_challenges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  score INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_id, challenge_id)
);

-- Team Challenge Assignments Table
CREATE TABLE IF NOT EXISTS team_challenge_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_challenge_id UUID REFERENCES team_challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  role TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_challenge_id, user_id)
);

-- Team Challenge Notes Table
CREATE TABLE IF NOT EXISTS team_challenge_notes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_challenge_id UUID REFERENCES team_challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  note TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Learning Modules Table
CREATE TABLE IF NOT EXISTS team_learning_modules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_id, module_id)
);

-- Team Learning Module Assignments Table
CREATE TABLE IF NOT EXISTS team_learning_module_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_module_id UUID REFERENCES team_learning_modules(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_module_id, user_id)
);

-- Team Events Table
CREATE TABLE IF NOT EXISTS team_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  event_type TEXT NOT NULL, -- 'meeting', 'challenge', 'training', etc.
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  location TEXT,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Event Attendees Table
CREATE TABLE IF NOT EXISTS team_event_attendees (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_id UUID REFERENCES team_events(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(event_id, user_id)
);

-- Team Notifications Table
CREATE TABLE IF NOT EXISTS team_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  notification_type TEXT NOT NULL, -- 'message', 'invitation', 'assignment', etc.
  reference_id UUID,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Analytics Table
CREATE TABLE IF NOT EXISTS team_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES enhanced_teams(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  active_members INTEGER DEFAULT 0,
  messages_sent INTEGER DEFAULT 0,
  challenges_started INTEGER DEFAULT 0,
  challenges_completed INTEGER DEFAULT 0,
  modules_started INTEGER DEFAULT 0,
  modules_completed INTEGER DEFAULT 0,
  total_points INTEGER DEFAULT 0,
  UNIQUE(team_id, date)
);

-- Functions

-- Function to check if user is a member of a team
CREATE OR REPLACE FUNCTION is_team_member(team_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  is_member BOOLEAN;
BEGIN
  SELECT EXISTS(
    SELECT 1 FROM team_members
    WHERE team_id = is_team_member.team_id
    AND user_id = is_team_member.user_id
  ) INTO is_member;
  
  RETURN is_member;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user is a team admin or owner
CREATE OR REPLACE FUNCTION is_team_admin(team_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  SELECT EXISTS(
    SELECT 1 FROM team_members
    WHERE team_id = is_team_admin.team_id
    AND user_id = is_team_admin.user_id
    AND role IN ('admin', 'owner')
  ) INTO is_admin;
  
  RETURN is_admin;
END;
$$ LANGUAGE plpgsql;

-- Function to get team statistics
CREATE OR REPLACE FUNCTION get_team_statistics(team_id UUID)
RETURNS TABLE (
  total_members INTEGER,
  total_challenges INTEGER,
  completed_challenges INTEGER,
  total_modules INTEGER,
  completed_modules INTEGER,
  total_points INTEGER,
  active_days INTEGER
) AS $$
DECLARE
  members INTEGER;
  challenges INTEGER;
  completed_chals INTEGER;
  modules INTEGER;
  completed_mods INTEGER;
  points INTEGER;
  active INTEGER;
BEGIN
  -- Get total members
  SELECT COUNT(*) INTO members
  FROM team_members
  WHERE team_id = get_team_statistics.team_id;
  
  -- Get total challenges
  SELECT COUNT(*) INTO challenges
  FROM team_challenges
  WHERE team_id = get_team_statistics.team_id;
  
  -- Get completed challenges
  SELECT COUNT(*) INTO completed_chals
  FROM team_challenges
  WHERE team_id = get_team_statistics.team_id
  AND status = 'completed';
  
  -- Get total modules
  SELECT COUNT(*) INTO modules
  FROM team_learning_modules
  WHERE team_id = get_team_statistics.team_id;
  
  -- Get completed modules
  SELECT COUNT(*) INTO completed_mods
  FROM team_learning_modules
  WHERE team_id = get_team_statistics.team_id
  AND status = 'completed';
  
  -- Get total points
  SELECT COALESCE(SUM(score), 0) INTO points
  FROM team_challenges
  WHERE team_id = get_team_statistics.team_id
  AND status = 'completed';
  
  -- Get active days
  SELECT COUNT(DISTINCT date) INTO active
  FROM team_analytics
  WHERE team_id = get_team_statistics.team_id
  AND (messages_sent > 0 OR challenges_started > 0 OR modules_started > 0);
  
  RETURN QUERY SELECT members, challenges, completed_chals, modules, completed_mods, points, active;
END;
$$ LANGUAGE plpgsql;

-- Function to get team member activity
CREATE OR REPLACE FUNCTION get_team_member_activity(team_id UUID)
RETURNS TABLE (
  user_id UUID,
  username TEXT,
  messages_sent INTEGER,
  challenges_completed INTEGER,
  modules_completed INTEGER,
  total_points INTEGER,
  last_active TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    tm.user_id,
    p.username,
    COUNT(DISTINCT tm2.id) AS messages_sent,
    COUNT(DISTINCT tca.id) AS challenges_completed,
    COUNT(DISTINCT tlma.id) AS modules_completed,
    COALESCE(SUM(tc.score), 0) AS total_points,
    MAX(GREATEST(
      COALESCE(tm2.created_at, '1970-01-01'::TIMESTAMP WITH TIME ZONE),
      COALESCE(tca.created_at, '1970-01-01'::TIMESTAMP WITH TIME ZONE),
      COALESCE(tlma.created_at, '1970-01-01'::TIMESTAMP WITH TIME ZONE)
    )) AS last_active
  FROM team_members tm
  JOIN profiles p ON tm.user_id = p.id
  LEFT JOIN team_messages tm2 ON tm.user_id = tm2.user_id AND tm.team_id = tm2.team_id
  LEFT JOIN team_challenge_assignments tca ON tm.user_id = tca.user_id
  LEFT JOIN team_challenges tc ON tca.team_challenge_id = tc.id AND tc.status = 'completed'
  LEFT JOIN team_learning_module_assignments tlma ON tm.user_id = tlma.user_id
  LEFT JOIN team_learning_modules tlm ON tlma.team_module_id = tlm.id AND tlm.status = 'completed'
  WHERE tm.team_id = get_team_member_activity.team_id
  GROUP BY tm.user_id, p.username
  ORDER BY total_points DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to update team analytics
CREATE OR REPLACE FUNCTION update_team_analytics()
RETURNS TRIGGER AS $$
DECLARE
  team_id UUID;
  today DATE := CURRENT_DATE;
BEGIN
  -- Determine team_id based on the table and new/old record
  IF TG_TABLE_NAME = 'team_messages' THEN
    team_id := NEW.team_id;
  ELSIF TG_TABLE_NAME = 'team_challenges' THEN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
      team_id := NEW.team_id;
    ELSE
      team_id := OLD.team_id;
    END IF;
  ELSIF TG_TABLE_NAME = 'team_learning_modules' THEN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
      team_id := NEW.team_id;
    ELSE
      team_id := OLD.team_id;
    END IF;
  END IF;
  
  -- Update or insert analytics record
  IF team_id IS NOT NULL THEN
    INSERT INTO team_analytics (team_id, date)
    VALUES (team_id, today)
    ON CONFLICT (team_id, date) DO NOTHING;
    
    -- Update specific metrics based on the table
    IF TG_TABLE_NAME = 'team_messages' AND TG_OP = 'INSERT' THEN
      UPDATE team_analytics
      SET messages_sent = messages_sent + 1
      WHERE team_id = NEW.team_id AND date = today;
    ELSIF TG_TABLE_NAME = 'team_challenges' THEN
      IF TG_OP = 'INSERT' THEN
        UPDATE team_analytics
        SET challenges_started = challenges_started + 1
        WHERE team_id = NEW.team_id AND date = today;
      ELSIF TG_OP = 'UPDATE' AND NEW.status = 'completed' AND OLD.status != 'completed' THEN
        UPDATE team_analytics
        SET challenges_completed = challenges_completed + 1,
            total_points = total_points + NEW.score
        WHERE team_id = NEW.team_id AND date = today;
      END IF;
    ELSIF TG_TABLE_NAME = 'team_learning_modules' THEN
      IF TG_OP = 'INSERT' THEN
        UPDATE team_analytics
        SET modules_started = modules_started + 1
        WHERE team_id = NEW.team_id AND date = today;
      ELSIF TG_OP = 'UPDATE' AND NEW.status = 'completed' AND OLD.status != 'completed' THEN
        UPDATE team_analytics
        SET modules_completed = modules_completed + 1
        WHERE team_id = NEW.team_id AND date = today;
      END IF;
    END IF;
    
    -- Update active members count
    UPDATE team_analytics
    SET active_members = (
      SELECT COUNT(DISTINCT user_id) 
      FROM team_messages 
      WHERE team_id = team_id 
      AND DATE(created_at) = today
    )
    WHERE team_id = team_id AND date = today;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Triggers for team analytics
CREATE TRIGGER team_messages_analytics_trigger
AFTER INSERT ON team_messages
FOR EACH ROW
EXECUTE FUNCTION update_team_analytics();

CREATE TRIGGER team_challenges_analytics_trigger
AFTER INSERT OR UPDATE ON team_challenges
FOR EACH ROW
EXECUTE FUNCTION update_team_analytics();

CREATE TRIGGER team_learning_modules_analytics_trigger
AFTER INSERT OR UPDATE ON team_learning_modules
FOR EACH ROW
EXECUTE FUNCTION update_team_analytics();

-- Row Level Security Policies

-- Teams: Only team members can view their teams
ALTER TABLE enhanced_teams ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view their teams" 
ON enhanced_teams FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = id
  )
);

CREATE POLICY "Team owners can update their teams" 
ON enhanced_teams FOR UPDATE 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = id AND role = 'owner'
  )
);

CREATE POLICY "Users can create teams" 
ON enhanced_teams FOR INSERT 
WITH CHECK (auth.uid() = created_by);

-- Team Members: Team members can view other members
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view other members" 
ON team_members FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_members.team_id
  )
);

CREATE POLICY "Team admins can manage members" 
ON team_members FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_members.team_id AND role IN ('admin', 'owner')
  )
);

-- Team Invitations: Team admins can manage invitations
ALTER TABLE team_invitations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team admins can view invitations" 
ON team_invitations FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_invitations.team_id AND role IN ('admin', 'owner')
  )
);

CREATE POLICY "Users can view their own invitations" 
ON team_invitations FOR SELECT 
USING (
  auth.uid() IN (
    SELECT id FROM auth.users WHERE email = team_invitations.email
  )
);

CREATE POLICY "Team admins can create invitations" 
ON team_invitations FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_invitations.team_id AND role IN ('admin', 'owner')
  )
);

-- Team Messages: Only team members can view messages
ALTER TABLE team_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view messages" 
ON team_messages FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_messages.team_id
  )
);

CREATE POLICY "Team members can send messages" 
ON team_messages FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_messages.team_id
  )
);

-- Team Channels: Only team members can view channels
ALTER TABLE team_channels ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view channels" 
ON team_channels FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_channels.team_id
  )
);

CREATE POLICY "Team admins can create channels" 
ON team_channels FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_channels.team_id AND role IN ('admin', 'owner')
  )
);

-- Team Channel Members: Only team members can view channel members
ALTER TABLE team_channel_members ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view channel members" 
ON team_channel_members FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = (
      SELECT team_id FROM team_channels WHERE id = team_channel_members.channel_id
    )
  )
);

-- Team Channel Messages: Only channel members can view messages
ALTER TABLE team_channel_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Channel members can view messages" 
ON team_channel_messages FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_channel_members WHERE channel_id = team_channel_messages.channel_id
  )
);

CREATE POLICY "Channel members can send messages" 
ON team_channel_messages FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_channel_members WHERE channel_id = team_channel_messages.channel_id
  )
);

-- Team Resources: Only team members can view resources
ALTER TABLE team_resources ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view resources" 
ON team_resources FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_resources.team_id
  )
);

CREATE POLICY "Team members can create resources" 
ON team_resources FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_resources.team_id
  )
);

-- Team Challenges: Only team members can view team challenges
ALTER TABLE team_challenges ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view team challenges" 
ON team_challenges FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_challenges.team_id
  )
);

CREATE POLICY "Team admins can manage team challenges" 
ON team_challenges FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_challenges.team_id AND role IN ('admin', 'owner')
  )
);

-- Team Challenge Assignments: Only team members can view assignments
ALTER TABLE team_challenge_assignments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view challenge assignments" 
ON team_challenge_assignments FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = (
      SELECT team_id FROM team_challenges WHERE id = team_challenge_assignments.team_challenge_id
    )
  )
);

-- Team Challenge Notes: Only team members can view notes
ALTER TABLE team_challenge_notes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view challenge notes" 
ON team_challenge_notes FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = (
      SELECT team_id FROM team_challenges WHERE id = team_challenge_notes.team_challenge_id
    )
  )
);

CREATE POLICY "Team members can create challenge notes" 
ON team_challenge_notes FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = (
      SELECT team_id FROM team_challenges WHERE id = team_challenge_notes.team_challenge_id
    )
  )
);

-- Team Learning Modules: Only team members can view team modules
ALTER TABLE team_learning_modules ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view team modules" 
ON team_learning_modules FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_learning_modules.team_id
  )
);

-- Team Events: Only team members can view events
ALTER TABLE team_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view events" 
ON team_events FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_events.team_id
  )
);

CREATE POLICY "Team members can create events" 
ON team_events FOR INSERT 
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_events.team_id
  )
);

-- Team Notifications: Users can only view their own notifications
ALTER TABLE team_notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications" 
ON team_notifications FOR SELECT 
USING (auth.uid() = user_id);

-- Team Analytics: Only team admins can view analytics
ALTER TABLE team_analytics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team admins can view analytics" 
ON team_analytics FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = team_analytics.team_id AND role IN ('admin', 'owner')
  )
);
