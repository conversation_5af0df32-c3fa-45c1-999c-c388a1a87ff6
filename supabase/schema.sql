-- XCerberus Database Schema

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search

-- User Profiles Table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium', 'business')),
  points INTEGER DEFAULT 0,
  challenges_completed INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscription History Table
CREATE TABLE IF NOT EXISTS subscription_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tier TEXT NOT NULL CHECK (tier IN ('free', 'premium', 'business')),
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Skills Table
CREATE TABLE IF NOT EXISTS user_skills (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  category TEXT NOT NULL,
  skill_level INTEGER DEFAULT 1,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, category)
);

-- Challenges Table
CREATE TABLE IF NOT EXISTS challenges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  difficulty TEXT NOT NULL CHECK (difficulty IN ('beginner', 'intermediate', 'advanced', 'expert')),
  category TEXT NOT NULL,
  points INTEGER NOT NULL,
  content JSONB NOT NULL, -- Structured content for the challenge
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Resources Table
CREATE TABLE IF NOT EXISTS challenge_resources (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  resource_path TEXT NOT NULL,
  resource_name TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Attempts Table
CREATE TABLE IF NOT EXISTS challenge_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Completions Table
CREATE TABLE IF NOT EXISTS challenge_completions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  score INTEGER NOT NULL DEFAULT 0
);

-- Learning Modules Table
CREATE TABLE IF NOT EXISTS learning_modules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  difficulty TEXT NOT NULL CHECK (difficulty IN ('beginner', 'intermediate', 'advanced', 'expert')),
  category TEXT NOT NULL,
  content JSONB NOT NULL, -- Structured content for the module
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Module Progress Table
CREATE TABLE IF NOT EXISTS module_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  progress FLOAT DEFAULT 0, -- Percentage of completion
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed BOOLEAN DEFAULT FALSE,
  UNIQUE(module_id, user_id)
);

-- User Events Table (for analytics)
CREATE TABLE IF NOT EXISTS user_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  event_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teams Table
CREATE TABLE IF NOT EXISTS teams (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team Members Table
CREATE TABLE IF NOT EXISTS team_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_id, user_id)
);

-- Store Items Table
CREATE TABLE IF NOT EXISTS store_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  price INTEGER NOT NULL, -- Price in coins
  item_type TEXT NOT NULL CHECK (item_type IN ('hint', 'tool', 'content', 'cosmetic')),
  content JSONB, -- Item-specific content
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Inventory Table
CREATE TABLE IF NOT EXISTS user_inventory (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  item_id UUID REFERENCES store_items(id) ON DELETE CASCADE,
  quantity INTEGER DEFAULT 1,
  purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, item_id)
);

-- Purchase History Table
CREATE TABLE IF NOT EXISTS purchase_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  item_id UUID REFERENCES store_items(id) ON DELETE CASCADE,
  quantity INTEGER DEFAULT 1,
  coins_spent INTEGER NOT NULL,
  purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications Table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  notification_type TEXT NOT NULL,
  related_id UUID, -- Can reference various entities
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security Policies

-- Profiles: Users can read all profiles but only update their own
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public profiles are viewable by everyone" 
ON profiles FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile" 
ON profiles FOR UPDATE USING (auth.uid() = id);

-- Challenges: Access based on subscription tier
ALTER TABLE challenges ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view non-premium challenges" 
ON challenges FOR SELECT 
USING (
  NOT is_premium OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business'))
);

CREATE POLICY "Premium users can view premium challenges" 
ON challenges FOR SELECT 
USING (
  NOT is_business OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business')
);

-- Learning Modules: Access based on subscription tier
ALTER TABLE learning_modules ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view non-premium modules" 
ON learning_modules FOR SELECT 
USING (
  NOT is_premium OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business'))
);

CREATE POLICY "Premium users can view premium modules" 
ON learning_modules FOR SELECT 
USING (
  NOT is_business OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business')
);

-- Challenge Completions: Users can only view their own completions
ALTER TABLE challenge_completions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own completions" 
ON challenge_completions FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own completions" 
ON challenge_completions FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Module Progress: Users can only view and update their own progress
ALTER TABLE module_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own module progress" 
ON module_progress FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own module progress" 
ON module_progress FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own module progress" 
ON module_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- User Events: Users can only view their own events
ALTER TABLE user_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own events" 
ON user_events FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own events" 
ON user_events FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Teams: Team members can view their teams
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view their teams" 
ON teams FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = id
  )
);

CREATE POLICY "Users can create teams" 
ON teams FOR INSERT 
WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Team owners can update their teams" 
ON teams FOR UPDATE 
USING (
  auth.uid() IN (
    SELECT user_id FROM team_members WHERE team_id = id AND role = 'owner'
  )
);

-- Notifications: Users can only view their own notifications
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications" 
ON notifications FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" 
ON notifications FOR UPDATE 
USING (auth.uid() = user_id);

-- Functions

-- Function to add points to a user
CREATE OR REPLACE FUNCTION add_user_points(user_id UUID, points_to_add INTEGER)
RETURNS VOID AS $$
BEGIN
  UPDATE profiles
  SET 
    points = points + points_to_add,
    updated_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to increment completed challenges count
CREATE OR REPLACE FUNCTION increment_completed_challenges(user_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE profiles
  SET 
    challenges_completed = challenges_completed + 1,
    updated_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get personalized challenge recommendations
CREATE OR REPLACE FUNCTION get_recommended_challenges(user_id UUID)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  difficulty TEXT,
  category TEXT,
  points INTEGER,
  is_premium BOOLEAN,
  is_business BOOLEAN,
  relevance_score FLOAT
) AS $$
BEGIN
  RETURN QUERY
  WITH user_skill_levels AS (
    SELECT category, skill_level
    FROM user_skills
    WHERE user_id = get_recommended_challenges.user_id
  ),
  completed_challenges AS (
    SELECT challenge_id
    FROM challenge_completions
    WHERE user_id = get_recommended_challenges.user_id
  ),
  user_subscription AS (
    SELECT subscription_tier
    FROM profiles
    WHERE id = get_recommended_challenges.user_id
  )
  SELECT 
    c.id,
    c.title,
    c.description,
    c.difficulty,
    c.category,
    c.points,
    c.is_premium,
    c.is_business,
    -- Calculate relevance score based on user's skill level in the category
    CASE
      WHEN us.skill_level IS NULL THEN 0.5 -- Default for categories user hasn't tried
      ELSE (us.skill_level::float / 10) -- Normalize skill level to 0-1 range
    END AS relevance_score
  FROM challenges c
  LEFT JOIN user_skill_levels us ON c.category = us.category
  CROSS JOIN user_subscription sub
  WHERE 
    -- Exclude completed challenges
    c.id NOT IN (SELECT challenge_id FROM completed_challenges)
    -- Filter based on subscription tier
    AND (
      (c.is_premium = FALSE) 
      OR (c.is_premium = TRUE AND sub.subscription_tier IN ('premium', 'business'))
    )
    AND (
      (c.is_business = FALSE)
      OR (c.is_business = TRUE AND sub.subscription_tier = 'business')
    )
  ORDER BY relevance_score DESC, c.created_at DESC
  LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- Triggers

-- Trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_challenges_updated_at
BEFORE UPDATE ON challenges
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_modules_updated_at
BEFORE UPDATE ON learning_modules
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at
BEFORE UPDATE ON teams
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger to increment challenges_completed when a challenge is completed
CREATE OR REPLACE FUNCTION increment_challenges_completed()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM increment_completed_challenges(NEW.user_id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_challenge_completion
AFTER INSERT ON challenge_completions
FOR EACH ROW
EXECUTE FUNCTION increment_challenges_completed();

-- Trigger to create initial user profile after signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO profiles (id, username, full_name, avatar_url, subscription_tier)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'avatar_url', ''),
    'free'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION handle_new_user();
