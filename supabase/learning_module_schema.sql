-- XCerberus Learning Module Schema
-- Designed for extreme scalability (100M+ users)

-- Learning Module Categories Table
CREATE TABLE IF NOT EXISTS learning_module_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Difficulty Levels Table
CREATE TABLE IF NOT EXISTS learning_module_difficulty_levels (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Types Table
CREATE TABLE IF NOT EXISTS learning_module_types (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Modules Table
CREATE TABLE IF NOT EXISTS learning_modules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE,
  description TEXT NOT NULL,
  category_id UUID REFERENCES learning_module_categories(id),
  difficulty_id UUID REFERENCES learning_module_difficulty_levels(id),
  type_id UUID REFERENCES learning_module_types(id),
  estimated_time INTEGER, -- In minutes
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_learning_modules_category_difficulty ON learning_modules(category_id, difficulty_id);
CREATE INDEX idx_learning_modules_premium_business ON learning_modules(is_premium, is_business);
CREATE INDEX idx_learning_modules_active ON learning_modules(is_active);
CREATE INDEX idx_learning_modules_search ON learning_modules USING gin(title gin_trgm_ops, description gin_trgm_ops);

-- Learning Module Content Table
CREATE TABLE IF NOT EXISTS learning_module_content (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  content JSONB NOT NULL, -- Structured content for the module
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Sections Table
CREATE TABLE IF NOT EXISTS learning_module_sections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  display_order INTEGER DEFAULT 0,
  estimated_time INTEGER, -- In minutes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Units Table
CREATE TABLE IF NOT EXISTS learning_module_units (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  section_id UUID REFERENCES learning_module_sections(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  type TEXT NOT NULL, -- 'text', 'video', 'quiz', 'exercise', etc.
  content JSONB NOT NULL,
  display_order INTEGER DEFAULT 0,
  estimated_time INTEGER, -- In minutes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Resources Table
CREATE TABLE IF NOT EXISTS learning_module_resources (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  resource_type TEXT NOT NULL, -- 'pdf', 'video', 'link', etc.
  resource_url TEXT NOT NULL,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Progress Table
CREATE TABLE IF NOT EXISTS learning_module_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  progress FLOAT DEFAULT 0, -- Percentage of completion
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed BOOLEAN DEFAULT FALSE,
  completed_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, module_id)
);

-- Learning Module Unit Progress Table
CREATE TABLE IF NOT EXISTS learning_module_unit_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  unit_id UUID REFERENCES learning_module_units(id) ON DELETE CASCADE,
  status TEXT NOT NULL, -- 'not_started', 'in_progress', 'completed'
  progress FLOAT DEFAULT 0, -- Percentage of completion
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed BOOLEAN DEFAULT FALSE,
  completed_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, unit_id)
);

-- Learning Module Quiz Attempts Table
CREATE TABLE IF NOT EXISTS learning_module_quiz_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  unit_id UUID REFERENCES learning_module_units(id) ON DELETE CASCADE,
  score FLOAT DEFAULT 0,
  max_score FLOAT NOT NULL,
  answers JSONB,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Learning Module Exercise Submissions Table
CREATE TABLE IF NOT EXISTS learning_module_exercise_submissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  unit_id UUID REFERENCES learning_module_units(id) ON DELETE CASCADE,
  submission TEXT NOT NULL,
  is_correct BOOLEAN,
  feedback TEXT,
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Ratings Table
CREATE TABLE IF NOT EXISTS learning_module_ratings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  feedback TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(module_id, user_id)
);

-- Learning Module Comments Table
CREATE TABLE IF NOT EXISTS learning_module_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  parent_id UUID REFERENCES learning_module_comments(id) ON DELETE CASCADE,
  comment TEXT NOT NULL,
  is_approved BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Module Prerequisites Table
CREATE TABLE IF NOT EXISTS learning_module_prerequisites (
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  prerequisite_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  PRIMARY KEY (module_id, prerequisite_id)
);

-- Learning Paths Table
CREATE TABLE IF NOT EXISTS learning_paths (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category_id UUID REFERENCES learning_module_categories(id),
  is_premium BOOLEAN DEFAULT FALSE,
  is_business BOOLEAN DEFAULT FALSE,
  author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Path Modules Table
CREATE TABLE IF NOT EXISTS learning_path_modules (
  path_id UUID REFERENCES learning_paths(id) ON DELETE CASCADE,
  module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
  display_order INTEGER DEFAULT 0,
  PRIMARY KEY (path_id, module_id)
);

-- Learning Path Progress Table
CREATE TABLE IF NOT EXISTS learning_path_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  path_id UUID REFERENCES learning_paths(id) ON DELETE CASCADE,
  progress FLOAT DEFAULT 0, -- Percentage of completion
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed BOOLEAN DEFAULT FALSE,
  completed_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, path_id)
);

-- Functions

-- Function to get module completion rate
CREATE OR REPLACE FUNCTION get_module_completion_rate(module_id UUID)
RETURNS FLOAT AS $$
DECLARE
  total_users INTEGER;
  completed_users INTEGER;
  rate FLOAT;
BEGIN
  SELECT COUNT(DISTINCT user_id) INTO total_users
  FROM learning_module_progress
  WHERE module_id = get_module_completion_rate.module_id;
  
  SELECT COUNT(DISTINCT user_id) INTO completed_users
  FROM learning_module_progress
  WHERE module_id = get_module_completion_rate.module_id
  AND completed = TRUE;
  
  IF total_users = 0 THEN
    rate := 0;
  ELSE
    rate := (completed_users::FLOAT / total_users::FLOAT) * 100;
  END IF;
  
  RETURN rate;
END;
$$ LANGUAGE plpgsql;

-- Function to get module average rating
CREATE OR REPLACE FUNCTION get_module_average_rating(module_id UUID)
RETURNS FLOAT AS $$
DECLARE
  avg_rating FLOAT;
BEGIN
  SELECT AVG(rating) INTO avg_rating
  FROM learning_module_ratings
  WHERE module_id = get_module_average_rating.module_id;
  
  RETURN COALESCE(avg_rating, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to get user learning progress
CREATE OR REPLACE FUNCTION get_user_learning_progress(user_id UUID)
RETURNS TABLE (
  total_modules INTEGER,
  completed_modules INTEGER,
  in_progress_modules INTEGER,
  completion_percentage FLOAT
) AS $$
DECLARE
  total INTEGER;
  completed INTEGER;
  in_progress INTEGER;
  percentage FLOAT;
BEGIN
  -- Get total active modules
  SELECT COUNT(*) INTO total
  FROM learning_modules
  WHERE is_active = TRUE;
  
  -- Get completed modules
  SELECT COUNT(*) INTO completed
  FROM learning_module_progress
  WHERE user_id = get_user_learning_progress.user_id
  AND completed = TRUE;
  
  -- Get in-progress modules
  SELECT COUNT(*) INTO in_progress
  FROM learning_module_progress
  WHERE user_id = get_user_learning_progress.user_id
  AND completed = FALSE
  AND progress > 0;
  
  -- Calculate percentage
  IF total = 0 THEN
    percentage := 0;
  ELSE
    percentage := (completed::FLOAT / total::FLOAT) * 100;
  END IF;
  
  RETURN QUERY SELECT total, completed, in_progress, percentage;
END;
$$ LANGUAGE plpgsql;

-- Function to get recommended learning modules for a user
CREATE OR REPLACE FUNCTION get_recommended_learning_modules(user_id UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  category_name TEXT,
  difficulty_name TEXT,
  estimated_time INTEGER,
  relevance_score FLOAT
) AS $$
BEGIN
  RETURN QUERY
  WITH user_completed_modules AS (
    SELECT module_id
    FROM learning_module_progress
    WHERE user_id = get_recommended_learning_modules.user_id
    AND completed = TRUE
  ),
  user_categories AS (
    SELECT c.category_id, COUNT(*) as category_count
    FROM learning_module_progress p
    JOIN learning_modules c ON p.module_id = c.id
    WHERE p.user_id = get_recommended_learning_modules.user_id
    AND p.completed = TRUE
    GROUP BY c.category_id
  ),
  user_difficulties AS (
    SELECT c.difficulty_id, COUNT(*) as difficulty_count
    FROM learning_module_progress p
    JOIN learning_modules c ON p.module_id = c.id
    WHERE p.user_id = get_recommended_learning_modules.user_id
    AND p.completed = TRUE
    GROUP BY c.difficulty_id
  )
  SELECT 
    m.id,
    m.title,
    m.description,
    cat.name as category_name,
    diff.name as difficulty_name,
    m.estimated_time,
    -- Calculate relevance score based on user's history
    (
      CASE
        WHEN uc.category_count IS NOT NULL THEN 0.5
        ELSE 0.1
      END +
      CASE
        WHEN ud.difficulty_count IS NOT NULL THEN 0.3
        ELSE 0.1
      END +
      CASE
        WHEN m.is_premium = TRUE THEN 0.2
        ELSE 0.1
      END
    ) as relevance_score
  FROM learning_modules m
  JOIN learning_module_categories cat ON m.category_id = cat.id
  JOIN learning_module_difficulty_levels diff ON m.difficulty_id = diff.id
  LEFT JOIN user_categories uc ON m.category_id = uc.category_id
  LEFT JOIN user_difficulties ud ON m.difficulty_id = ud.difficulty_id
  WHERE 
    m.is_active = TRUE
    AND m.id NOT IN (SELECT module_id FROM user_completed_modules)
  ORDER BY relevance_score DESC, m.created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Row Level Security Policies

-- Learning Modules: Access based on subscription tier
ALTER TABLE learning_modules ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view non-premium learning modules" 
ON learning_modules FOR SELECT 
USING (
  NOT is_premium OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business'))
);

CREATE POLICY "Premium users can view premium learning modules" 
ON learning_modules FOR SELECT 
USING (
  NOT is_business OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business')
);

-- Learning Module Content: Access based on subscription tier
ALTER TABLE learning_module_content ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view learning module content they have access to" 
ON learning_module_content FOR SELECT 
USING (
  module_id IN (
    SELECT id FROM learning_modules WHERE 
      (NOT is_premium OR auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business')))
      AND
      (NOT is_business OR auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business'))
  )
);

-- Learning Module Progress: Users can only view and update their own progress
ALTER TABLE learning_module_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own module progress" 
ON learning_module_progress FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own module progress" 
ON learning_module_progress FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own module progress" 
ON learning_module_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Learning Module Unit Progress: Users can only view and update their own progress
ALTER TABLE learning_module_unit_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own unit progress" 
ON learning_module_unit_progress FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own unit progress" 
ON learning_module_unit_progress FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own unit progress" 
ON learning_module_unit_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Learning Module Quiz Attempts: Users can only view their own attempts
ALTER TABLE learning_module_quiz_attempts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own quiz attempts" 
ON learning_module_quiz_attempts FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own quiz attempts" 
ON learning_module_quiz_attempts FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Learning Module Exercise Submissions: Users can only view their own submissions
ALTER TABLE learning_module_exercise_submissions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own exercise submissions" 
ON learning_module_exercise_submissions FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own exercise submissions" 
ON learning_module_exercise_submissions FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Learning Module Ratings: Users can only view and insert their own ratings
ALTER TABLE learning_module_ratings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view module ratings" 
ON learning_module_ratings FOR SELECT 
USING (true);

CREATE POLICY "Users can insert their own module ratings" 
ON learning_module_ratings FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Learning Module Comments: Everyone can view approved comments
ALTER TABLE learning_module_comments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view approved module comments" 
ON learning_module_comments FOR SELECT 
USING (is_approved = TRUE);

CREATE POLICY "Users can view their own module comments" 
ON learning_module_comments FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own module comments" 
ON learning_module_comments FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Learning Paths: Access based on subscription tier
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view non-premium learning paths" 
ON learning_paths FOR SELECT 
USING (
  NOT is_premium OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier IN ('premium', 'business'))
);

CREATE POLICY "Premium users can view premium learning paths" 
ON learning_paths FOR SELECT 
USING (
  NOT is_business OR 
  auth.uid() IN (SELECT id FROM profiles WHERE subscription_tier = 'business')
);

-- Learning Path Progress: Users can only view and update their own progress
ALTER TABLE learning_path_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own path progress" 
ON learning_path_progress FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own path progress" 
ON learning_path_progress FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own path progress" 
ON learning_path_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Insert sample data for testing

-- Insert learning module categories
INSERT INTO learning_module_categories (name, description, icon, display_order) VALUES
  ('Web Security', 'Modules focused on web application security', 'fa-globe', 1),
  ('Network Security', 'Modules focused on network security', 'fa-network-wired', 2),
  ('Cryptography', 'Modules focused on cryptography', 'fa-key', 3),
  ('Reverse Engineering', 'Modules focused on reverse engineering', 'fa-microchip', 4),
  ('Forensics', 'Modules focused on digital forensics', 'fa-search', 5),
  ('Binary Exploitation', 'Modules focused on binary exploitation', 'fa-bug', 6),
  ('OSINT', 'Modules focused on open-source intelligence', 'fa-eye', 7),
  ('Mobile Security', 'Modules focused on mobile application security', 'fa-mobile-alt', 8)
ON CONFLICT (name) DO NOTHING;

-- Insert learning module difficulty levels
INSERT INTO learning_module_difficulty_levels (name, description, display_order) VALUES
  ('Beginner', 'Entry-level modules for beginners', 1),
  ('Intermediate', 'Modules for users with some experience', 2),
  ('Advanced', 'Challenging modules for experienced users', 3),
  ('Expert', 'Very difficult modules for experts', 4)
ON CONFLICT (name) DO NOTHING;

-- Insert learning module types
INSERT INTO learning_module_types (name, description) VALUES
  ('Text', 'Text-based learning modules'),
  ('Video', 'Video-based learning modules'),
  ('Interactive', 'Interactive learning modules with exercises'),
  ('Mixed', 'Mixed-media learning modules')
ON CONFLICT (name) DO NOTHING;
