#!/bin/bash

# Exit on error
set -e

# Update system packages
echo "Updating system packages..."
sudo apt-get update
sudo apt-get upgrade -y

# Install Node.js if not already installed
if ! command -v node &> /dev/null; then
    echo "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install PM2 globally if not already installed
if ! command -v pm2 &> /dev/null; then
    echo "Installing PM2..."
    sudo npm install -g pm2
fi

# Create app directory if it doesn't exist
echo "Setting up application directory..."
mkdir -p ~/cyberforce

# Copy files to app directory
echo "Copying application files..."
cp -r dist ~/cyberforce/
cp server.js ~/cyberforce/
cp server-package.json ~/cyberforce/package.json

# Install server dependencies
echo "Installing server dependencies..."
cd ~/cyberforce
npm install

# Start or restart the application with PM2
echo "Starting application with PM2..."
sudo env PATH=$PATH pm2 start server.js --name cyberforce

# Save PM2 process list and configure to start on system startup
echo "Configuring PM2 to start on system boot..."
pm2 save
sudo env PATH=$PATH:/usr/bin pm2 startup systemd -u $USER --hp $HOME

echo "Deployment completed successfully!"
