import fs from 'fs-extra';
import path from 'path';

const services = ['ai', 'auth', 'challenges', 'learning'];
const logDir = 'logs';

async function cleanLogs() {
  try {
    // Clean each service's logs
    for (const service of services) {
      const serviceLogDir = path.join(logDir, service);
      
      if (await fs.pathExists(serviceLogDir)) {
        await fs.emptyDir(serviceLogDir);
        console.log(`Cleaned logs for ${service} service`);
      }
    }

    console.log('All logs cleaned successfully');
  } catch (error) {
    console.error('Error cleaning logs:', error);
    process.exit(1);
  }
}

cleanLogs();