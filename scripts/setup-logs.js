import fs from 'fs-extra';
import path from 'path';

const services = ['ai', 'auth', 'challenges', 'learning'];
const logDir = 'logs';

async function setupLogs() {
  try {
    // Ensure main logs directory exists
    await fs.ensureDir(logDir);

    // Create service-specific log directories
    for (const service of services) {
      const serviceLogDir = path.join(logDir, service);
      await fs.ensureDir(serviceLogDir);
      
      // Set proper permissions
      await fs.chmod(serviceLogDir, 0o755);
    }

    console.log('Log directories created successfully');
  } catch (error) {
    console.error('Error setting up log directories:', error);
    process.exit(1);
  }
}

setupLogs();