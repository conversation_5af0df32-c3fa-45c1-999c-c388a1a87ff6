// Setup Pre-Login Content
// This script runs the SQL files to set up the pre-login content in the database

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to run SQL file
async function runSqlFile(filePath) {
  try {
    console.log(`Running SQL file: ${filePath}`);
    const sql = fs.readFileSync(path.resolve(__dirname, '..', filePath), 'utf8');
    
    // Split the SQL file into individual statements
    const statements = sql
      .replace(/--.*$/gm, '') // Remove comments
      .split(';')
      .filter(statement => statement.trim() !== '');
    
    // Execute each statement
    for (const statement of statements) {
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      if (error) {
        console.error(`Error executing SQL statement: ${error.message}`);
        console.error(`Statement: ${statement}`);
      }
    }
    
    console.log(`Successfully executed SQL file: ${filePath}`);
  } catch (error) {
    console.error(`Error running SQL file ${filePath}:`, error);
  }
}

// Main function
async function setupPreLoginContent() {
  try {
    console.log('Setting up pre-login content...');
    
    // Run the SQL files
    await runSqlFile('supabase/pre_login_learning.sql');
    await runSqlFile('supabase/pre_login_challenges.sql');
    
    console.log('Pre-login content setup complete!');
  } catch (error) {
    console.error('Error setting up pre-login content:', error);
  }
}

// Run the setup
setupPreLoginContent();
